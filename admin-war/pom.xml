<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>1.2.5.RELEASE</version>
	</parent>
	<groupId>org.itboys</groupId>
	<artifactId>admin-war</artifactId>
	<packaging>war</packaging>
	<version>1.0</version>
	<name>general platform</name>
	<properties>
		<junit.version>4.11</junit.version>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<java.version>1.8</java.version>
		<start-class>org.itboys.admin.AdminApplication</start-class>
	</properties>
	<!--
	<repositories>
		<repository>
			<id>nexus</id>
			<name>Team Nexus Repository</name>
			<url>http://*************:8081/nexus/content/repositories/thirdparty</url>
		</repository>
	</repositories>


	<pluginRepositories>
		<pluginRepository>
			<id>nexus</id>
			<name>Team Nexus Repository</name>
			<url>http://*************:8081/nexus/content/repositories/thirdparty</url>
			<snapshots>
				<enabled>false</enabled>
			</snapshots>
		</pluginRepository>
	</pluginRepositories>
-->
	<dependencies>
		<!--<dependency>
			<groupId>org.itboys</groupId>
			<artifactId>weixin</artifactId>
			<version>1.0</version>
		</dependency>-->
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>4.10</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-test</artifactId>
		</dependency>
		<!-- 		<dependency>
			<groupId>org.itboys</groupId>
			<artifactId>product</artifactId>
			<version>1.0</version>
		</dependency> -->
		<dependency>
			<groupId>org.itboys</groupId>
			<artifactId>framework</artifactId>
			<version>1.2.1</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-jdbc</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-velocity</artifactId>
		</dependency>
		<!--<dependency>
			<groupId>com.mangofactory</groupId>
			<artifactId>swagger-springmvc</artifactId>
			<version>1.0.2</version>
		</dependency>-->
		<dependency>
			<groupId>org.codehaus.jackson</groupId>
			<artifactId>jackson-mapper-asl</artifactId>
			<version>1.9.13</version>
		</dependency>
		<dependency>
			<groupId>org.codehaus.jackson</groupId>
			<artifactId>jackson-core-asl</artifactId>
			<version>1.9.13</version>
		</dependency>
		<dependency>
			<groupId>org.itboys</groupId>
			<artifactId>admin-template</artifactId>
			<version>1.0</version>
		</dependency>
		<dependency>
			<groupId>org.itboys</groupId>
			<artifactId>mobile</artifactId>
			<version>1.0</version>
		</dependency>
	</dependencies>
	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-war-plugin</artifactId>
				<version>3.3.1</version>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-eclipse-plugin</artifactId>
				<version>2.9</version>
				<configuration>
					<downloadSources>true</downloadSources>
					<downloadJavadocs>false</downloadJavadocs>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.1</version>
				<configuration>
					<source>${java.version}</source>
					<target>${java.version}</target>
					<showWarnings>true</showWarnings>
				</configuration>
			</plugin>
			<plugin>
				<artifactId>maven-jar-plugin</artifactId>
				<executions>
					<execution>
						<goals>
							<goal>test-jar</goal>
						</goals>
						<configuration>
							<includes>
								<include>org/itboys/**/*.class</include>
							</includes>
							<excludes>
								<exclude>org/itboys/**/*Test.class</exclude>
							</excludes>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<!-- 跳过测试 -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<configuration>
					<skip>true</skip>
				</configuration>
			</plugin>
		</plugins>
	</build>
	<!--
	<distributionManagement>
		<repository>
			<id>releases</id>
			<name>Internal Releases</name>
			<url>http://*************:8081/nexus/content/repositories/thirdparty</url>
		</repository>
		<snapshotRepository>
			<id>releases</id>
			<name>Internal Releases</name>
			<url>http://*************:8081/nexus/content/repositories/thirdparty</url>
		</snapshotRepository>
	</distributionManagement>
	-->
</project>
