<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>育华教育管理后台</title>
    <!-- <link rel="icon" href="img/logo.ico" type="image/x-icon">
    <link rel="shortcut icon" href="img/logo.ico" type="image/x-icon"> -->
    <link rel="stylesheet" type="text/css" href="$!{staticRoot}/yuhua/web/css/style.css">
    <link rel="icon" href="$!{staticRoot}/yuhua/admin/img/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="$!{staticRoot}/yuhua/admin/img/favicon.ico" type="image/x-icon">
</head>
<body>
<div class="container">
    <div class="login-box">
        <form action="" method="post">
            <div class="login-wrapper">
                <img src="$!staticRoot/yuhua/web/img/bear.png" alt="" class="bear">
                <div class="icon-right"></div>
                <div class="icon-left"></div>
                <div class="circular circular-right"></div>
                <div class="circular circular-left"></div>
                <!-- 以上为装饰部分 -->

                <img src="$!{staticRoot}/yuhua/web/img/<EMAIL>" alt="" class="logo">
                <input type="text" placeholder="用户名" title="用户名" class="input-item" id="userName" value="$!username">
                <input type="password" placeholder="密码" title="密码" class="input-item" id="password"  >

                <input type="button" value="登录" class="sub" onclick="doLogin()">
            </div>
        </form>
    </div>
</div>

<script type="text/javascript" src="$!{staticRoot}/plug/jquery-easyui/jquery-1.8.0.min.js"></script>
<script type="text/javascript">
    !function () {

    }()
    function doLogin(){
        var userName = $("#userName").val();
        var password = $("#password").val();
      //  var yzm = $("#yzm").val();
      //  var autoLogin = document.getElementById("autoLogin").checked;

        if(jQuery.trim(userName)=="" ||  jQuery.trim(password)==""){
            alert("用户名,密码都必填!");
            return;
        }
        jQuery.post("/admin/doLogin",{userName:userName,password:password,yzm:null,autoLogin:false},function(data){
            if(data=="1"){
                parent.window.location.href="$!{webRoot}";
            }else if(data=="0" || data=="-1"){
                alert("用户名或密码错误!");
            }else if(data=="-2"){
                alert("验证码不正确!");
            }else if(data=="-4"){
                alert("账户被冻结或被注销!");
            }
        });
    }

    document.onkeydown = function(e){
        if(!e) e = window.event;//火狐中是 window.event
        if((e.keyCode || e.which) == 13){
            doLogin();
        }
    }
    // 是否记住我


    //2018年9月29日，session过期，不要再内页返回登录页
    /*if(self!=top){
       // console.info("是")
        parent.window.location.replace(window.location.href);
    }else{
       // console.info("否")
    }*/
    if($("#sunday_web_content").length>0){
        window.location.reload();
    }
</script>
<script>
    var arr = [1,2,3,4,5,6,7,9,11,12,14,15,16,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,35,36,38,39,40,41,42,43,44,45,46,47,48,49,51,52,53,54,55,56,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,75,76,77,78,79,80,81,82,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,125,127,128,129,131,132,134,135,136,138,144,146,148,149,150,152,153,154,155,156,157,158,176,177,178,179,180,181,183,186,188,191,192,194,195,197,198,201,205,210,214,215,216,217,230,233,234,235,237,238,245,246,247,248,249,253,254,255,257,258,262,263,265,266,268,269,271,278,283,285,286,287,288,293,294,295,296,299,303,305,310,311,312,314,315,316,317,338,339,340,341,342,343,344,345,347,348,349,350,351,353,354,355,356,357,360,362,363,364,366,368,369,373,374,375,376,377,379,381,382,383,385,386,392,394,395,396,397,398,399,400,402,
        404,405,406,407,408,409,411,412,413,414,415,419,420,421,422,423,426,427,428,429,430,431,432,436,437,438,439,442,444,445,447,448,449,451,452,454,455,456,457,
        458,459,461,462,463,465,467,468,469,470,471,472,473,474,475,476,479,481,484,1005,1006,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041,1042,1043,1044,1045,1046,1047,1048,1049,1050,1051,1052,1053,1054,1055,1056,1057,1058,1059,1060,1061,1062,1063,1064,1065,1066,1067,1068,1069,1070,1071,1072,1073,1074,1075,1076,1077,1078,1079,1080,1081,1082,1083,1084,1085,1086,1087,1088,1089,1090,1091,1092,1093,1094,1095,1096,1097,1098,1099,1100,1101,1102,1103,1104,1105,1106,1107,1108,1109,1110,1111,1112,1113,1114,1115,1116,1117,1118,1119,1120,1121,1122,1123,1124,1125,1126,1127,1128,1129,1130,1131,1132,1133,1134,1135,1136,1137,1138,1139,1140,1141,1142,1143,1144,1145,1146,1147,1148,1149,1150,1151,1152,1153,1154,1155,1156,1157,1158,1159,1160,1161,1162,1163,1164,1165,1166,1167,
        1168,1169,1170,1171,1172,1173,1174,1176,1177,1178,1179,1180,1181,1182,1183,1184,1186,1187,1188,1189,1190,1191,1205,1206,1207,1208,1209,1212,1215,1217,1219,1220,1224,1227,1228,1232,1233,1234,1236,1238,1239,1240,1241,1243,1244,1245,1246,1247,1248,1252,1253,1255,1258,1259,1260,1261,1262,1263,1266,1269,1270,1271,1272,1275,1276,1278,1279,1280,1282,1283,1284,1285,1286,1287,1288,1289,1290,1291,1292,1294,1295,1296,1297,1298,1300,1301,1303,1304,1305,1306,1307,1308,1309,1310,1311,1312,1313,1314,1315,1317,1318,1319,1320,1321,1322,1323,1324,1325,1326,1327,1328,1329,1331,1332,1333,1334,1335,1336,1338,1339,1341,1342,1343,1345,1346,1347,1348,1349,1350,1351,1352,1353,1354,1355,1357,1358,1359,1360,1361,1362,1363,1364,1365,1366,1367,1368,1369,1370,1371,1374,1377,1378,1379,1380,1381,1382,1383,1384,1387,1388,1390,1391,1392,1394,1395,1396,1397,1398,1399,1401,1402,1403,1404,1405,1406,1407,1408,
        1412,1413,1414,1415,1416,1418,1419,1420,1421,1422,1423,1424,1425,1426,1429,1431,1432,1433,1435,1437,1438,1439,1440,1443,1444,1445,1446,1450,1451,1452,1454,1455,1456,1457,1458,1459,1461,1462,1463,1464,1465,1467,1468,1469,1470,1471,1472,1474,1475,1476,1477,1478,1479,1480,1481,1482,1483,1484,1485,1486,1487,1488,1489,1490,1492,1493,1496,1497,1499,1500,1501,1502,1505,1506,1508,1509,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1524,1525,1526,1528,1529,1530,1532,1533,1534,1535,1536,1537,1538,1543,1545,1546,1547,1548,1550,1551,1552,1553,1554,1555,1556,1557,1558,1559,1561,1564,1565,1567,1569,1570,1571,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1583,1584,1586,1587,1588,1589,1590,1591,1592,1593,1594,1597,1598,1599,1600,1601,1602,1603,1605,1606,1607,1608,1609,1610,1611,1612,1613,1614,1615,1616,1617,1618,1619,1622,1623,1625,1627,1628,1629,1630,1631,1632,1633,
        1634,1636,1638,1641,1642,1644,1645,1646,1647,1648,1650,1651,1652,1653,1654,1655,1656,1657,1661,1662,1663,1664,1665,1667,1668,1672,1673,1674,1675,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1693,1694,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1709,1710,1712,1713,1714,1715,1716,1717,1718,1719,1720,1721,1722,1723,1724,1725,1726,1727,1728,1729,1730,1731,1732,1735,1736,1737,1739,1740,1741,1742,1743,1744,1745,1746,1747,1748,1749,1750,1751,1752,1753,1756,1757,1758,1759,1760,1761,1762,1763,1766,1767,1769,1772,1773,1774,1775,1776,1777,1778,1779,1780,1781,1782,1783,1784,1785,1786,1787,1788,1789,1790,1791,1792,1793,1795,1796,1797,1798,1801,1804,1808,1812,1813,1814,1815,1816,1817,1818,1819,1820,1822,1823,1824,1825,1828,1829,1830,1831,1833,1834,1835,1836,1837,1838,1839,1840,1841,1842,1844,1845,1847,1849,1853,1854,1855,1856,1857,
        1859,1860,1861,1866,1869,1870,1871,1872,1873,1874,1883,1884,1886,1898,1899,1900,1901,1902,1903,1904,1906,1907,1908,1909,1951,1993,2066,2250];

    var arr2 = [1,2,3,5,6,7,9,12,15,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,35,36,38,39,40,41,42,43,44,45,46,47,48,49,51,52,53,54,55,56,58,59,61,62,63,64,65,66,67,68,69,70,72,73,75,76,77,78,81,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,127,129,131,135,136,138,144,146,148,152,153,155,156,157,158,177,178,181,195,201,216,230,233,234,235,237,238,246,249,253,255,257,258,262,263,266,268,269,271,283,285,288,293,294,295,296,305,310,314,315,316,338,339,340,341,342,343,345,347,348,350,351,353,355,356,357,360,362,363,364,366,368,369,374,375,376,377,381,383,386,392,394,396,397,398,399,400,402,404,405,406,407,408,409,411,412,413,414,415,419,423,426,428,430,431,432,436,437,438,442,444,445,447,451,452,454,455,457,458,459,461,468,470,472,473,474,1006,1008,1009,1010,1012,1017,1018,1019,1020,1022,1024,1025,1026,1028,1030,1031,1033,1035,1036,1037,1038,1039,1040,1041,1042,1043,1044,1047,1048,1049,1050,1052,1053,1054,1055,1056,1059,1060,1061,1062,1063,1064,1065,1066,1067,1068,1069,1070,1071,1073,1074,1075,1076,1077,1078,1079,1080,1081,1082,1083,1084,1085,1086,1088,1089,1090,1091,1093,1094,1095,1096,1097,1099,1100,1102,1103,1104,1105,1106,1107,1108,1109,1110,1111,1112,1113,1114,1115,1117,1118,1120,1121,1122,1123,1124,1125,1128,1133,1134,1135,1137,1138,1139,1140,1141,1142,1143,1144,1145,1146,1147,1148,1149,1150,1151,1153,1154,1155,1156,1157,1158,1159,1160,1161,1162,1167,1168,1169,1172,1173,1176,1177,1178,1180,1181,1182,1183,1184,1186,1187,1188,1191,1205,1206,1207,1208,1209,1212,1215,1217,1219,1224,1228,1232,1233,1234,1239,1240,1241,1243,1244,1246,1247,1248,1252,1255,1258,1260,1261,1262,1272,1275,1276,1280,1282,1283,1285,1286,1287,1288,1289,1290,1291,1300,1301,1303,1304,1305,1307,1308,1310,1311,1312,1313,1314,1315,1317,1318,1319,1321,1322,1324,1325,1326,1329,1332,1333,1334,1335,1336,1339,1342,1343,1345,1346,1348,1349,1350,1351,1352,1353,1354,1355,1357,1358,1359,1360,1361,1363,1364,1365,1366,1367,1368,1369,1374,1377,1378,1379,1380,1381,1382,1384,1387,1390,1391,1392,1394,1395,1396,1397,1398,1399,1401,1402,1403,1404,1405,1406,1407,1412,1413,1414,1415,1418,1419,1420,1421,1422,1423,1424,1426,1429,1432,1433,1435,1437,1439,1440,1445,1450,1451,1452,1454,1457,1458,1459,1461,1462,1463,1464,1465,1474,1476,1477,1478,1479,1480,1481,1482,1483,1484,1485,1486,1489,1490,1492,1496,1497,1499,1500,1501,1502,1508,1509,1510,1511,1512,1513,1514,1515,1516,1519,1520,1521,1524,1525,1526,1533,1534,1537,1538,1543,1546,1547,1548,1550,1552,1553,1554,1555,1556,1557,1558,1559,1561,1564,1565,1567,1569,1570,1571,1572,1573,1574,1575,1576,1577,1579,1580,1581,1583,1584,1586,1587,1588,1590,1591,1592,1593,1594,1597,1598,1599,1600,1601,1602,1603,1605,1606,1607,1608,1609,1610,1611,1612,1613,1614,1615,1616,1618,1619,1622,1623,1625,1627,1628,1629,1630,1631,1633,1634,1636,1638,1641,1642,1644,1645,1646,1647,1648,1650,1651,1652,1653,1655,1656,1657,1661,1663,1664,1665,1668,1672,1673,1674,1675,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1689,1690,1692,1693,1694,1696,1697,1698,1699,1701,1703,1704,1705,1706,1707,1710,1713,1714,1715,1716,1717,1718,1719,1720,1721,1722,1723,1724,1725,1726,1727,1728,1729,1730,1731,1732,1735,1736,1737,1739,1740,1741,1742,1743,1744,1745,1746,1747,1748,1749,1750,1752,1753,1756,1757,1758,1759,1760,1761,1762,1766,1772,1774,1775,1777,1778,1779,1780,1781,1782,1783,1784,1785,1786,1787,1788,1789,1790,1791,1792,1793,1795,1796,1797,1798,1801,1804,1808,1812,1813,1814,1815,1816,1817,1818,1819,1820,1822,1823,1824,1825,1828,1829,1830,1831,1833,1834,1835,1836,1837,1838,1839,1840,1841,1842,1844,1845,1847,1849,1853,1854,1855,1856,1857,1859,1860,1861,1866,1869,1870,1871,1872,1873,1874,1883,1884,1886,1898,1899,1900,1901,1902,1903,1904,1906,1907,1908,1909,1951,1993,2066,2250];

    Array.prototype.each = function(fn){
        fn = fn || Function.K;
        var a = [];
        var args = Array.prototype.slice.call(arguments, 1);
        for(var i = 0; i < this.length; i++){
            var res = fn.apply(this,[this[i],i].concat(args));
            if(res != null) a.push(res);
        }
        return a;
    };
    Array.prototype.uniquelize = function(){
        var ra = new Array();
        for(var i = 0; i < this.length; i ++){
            if(!ra.contains(this[i])){
                ra.push(this[i]);
            }
        }
        return ra;
    };

    Array.intersect = function(a, b){
        return a.uniquelize().each(function(o){return b.contains(o) ? o : null});
    };

    console.log(Array.intersect(arr,arr2));
</script>
</body>
</html>