#set( $layout = "/layout/easyui/easyui-layout.vm")
	<div style="margin: 10px 0;"></div>
<div id="wxrelFormDiv" style="padding: 5px; height: auto">
	<div style="margin-bottom:5px">
			
		<form id="queryUserForm2">
			用户名:<input class="easyui-validatebox" name="filter_S_userName" style="width:100px">
			姓名:<input class="easyui-validatebox" name="filter_S_name" style="width:100px">
			最后登入时间: <input class="easyui-datebox" name="filter_S_startTime" style="width:80px">
			To: <input class="easyui-datebox"  name="filter_S_endTime" style="width:80px">
			<a href="javascript:doSearchObject('wxrelTable','queryUserForm2')" class="easyui-linkbutton" iconCls="icon-search">搜索</a>
		</form>
	</div>
		
</div>

	<div class="easyui-layout" style="width:1100px;height:550px;">
		<div data-options="region:'east',split:true" title="选择微信号" style="width:200px;" id="wxrel2">
			##<ul id="weixinConfigTree" class="easyui-tree" data-options="url:'/weixin/sendmessage/getWeixinConfigTree',animate:true,checkbox:true"></ul>
			<ul id="weixinConfigTree2" class="easyui-tree" data-options="animate:true,checkbox:true"></ul>
			<br/>
			<a href="javascript:doRel2()" id="userrelwx" class="easyui-linkbutton"  style="display:none">确定</a> 
		</div>
		<div data-options="region:'center',title:'用户关联微信管理'">
		
		<table id="wxrelTable" ></table>
		
<script>				
	$('#wxrelTable$!weixinId').datagrid({
		url:'/admin/user/list',
		toolbar: '#wxrelFormDiv',
		pagination : true,
		singleSelect : true,
		rownumbers : true,
		columns : [[
			{field:'userName',title:'用户名',width:100},
					{field:'name',title:'姓名',width:90},
					{field:'mobile',title:'手机号',width:95},
					{field:'deptName',title:'部门',width:190,
						formatter: function(value,row,index){
							if(row && row.userOrgPost){
								return row.userOrgPost.deptName;
							}
						}
					},
					{field:'postName',title:'职务',width:115,
						formatter: function(value,row,index){
							if(row && row.userOrgPost){
								return row.userOrgPost.postName;
							}
						}	
					},
					{
						field:'type',title:'类型',width:50,
						formatter: function(value,row,index){
							if(row.type==1){
								return '超级';
							}else if(row.type=='0')
								return '普通';
						}
						
					},
			{field : 'operate',title : '操作',width:350,
				formatter : function(value, row, index) {
				   if(row.type==0){
					return '[<a href="javascript:dorel(' + row.id + ',\'' + row.name+ '\')">关联微信</a>]';
					}else{}
				}
			} 
		]]
	});
					

</script>
					
<script>
	//关联微信
		var _current_user_role_id = null;
	function dorel(_id,_name){
			_current_user_role_id=_id;
			$('#wxrel2').panel({"title":_name+"关联微信"});
			$("#weixinConfigTree2").show();
			$('#weixinConfigTree2').tree({
				url:'/weixin/rel/getWeixinConfigTree1?userId='+_id+'&type=1',
				cascadeCheck:false,
				onlyLeafCheck:false
				
			}); 
			$("#userrelwx").show();
			
	}
	
	//点击确定  doRel2
	function doRel2(){
	var nodes2 = $('#weixinConfigTree2').tree('getChecked');
	var u = '';
		for(var i=0; i<nodes2.length; i++){
			if (u != '') u += ',';
				u += nodes2[i].id;
		}
	jQuery.post("/weixin/rel/setUserRelwx",{relObjectId:_current_user_role_id,weixinId:u},function(data){
			if(data=='success'){
				jQuery.messager.alert('Info', "选择成功", 'info');
			}else{
				jQuery.messager.alert('Info', "选择失败", 'info');
			}
		});
	}


</script>
	</div>
</div>