<div class="weekly-plan">
        <div class="search-wrapper clearfix">
            <div class="com-title-wrapper">
                <div class="title">条件搜索</div>
            </div>

            <table class="search-table" style="margin-top: 20px">
                <tr>
                    <td><div class="search-titl">地区选择：</div></td>
                    <td>
                        <div class="search-selectpicker">
                            <select id="sunday_daily_search_form_provinceId"></select>
                        </div>
                    </td>
                    <td>
                        <div class="search-selectpicker">
                            <select  id="sunday_daily_search_form_cityId"></select>
                        </div>
                    </td>
                    <td>
                        <div class="search-selectpicker">
                            <select   id="sunday_daily_search_form_districtId"></select>
                        </div>
                    </td>
                    <td></td>
                    <td></td>
                </tr>
                <tr>
                    <td><div class="search-titl">园区选择：</div></td>
                    <td>
                        <div class="search-selectpicker">
                            <select id="sunday_daily_search_form_guardId"></select>
                        </div>
                    </td>
                    <td>
                        <div class="search-selectpicker">
                            <select   id="sunday_daily_search_form_bunchId"></select>
                        </div>
                    </td>
                    <td><button type="button" class="search-button" onclick="sunday_daily_search()">搜索</button>
                        <a href="javascript:sunday_daily_save(0);" class="search-add-button">新增一日作息</a>
                    </td>
                    <td></td>
                    <td></td>
                </tr>
            </table>
            <script>
                intAreaNoTown("sunday_daily_search_form_provinceId","sunday_daily_search_form_cityId","sunday_daily_search_form_districtId",null,null,null);
                initGuardAndBunch("sunday_daily_search_form_guardId","sunday_daily_search_form_bunchId",null,null,"sunday_daily_search_form_provinceId","sunday_daily_search_form_cityId","sunday_daily_search_form_districtId");
                //2018年10月22日，省市区控件，控制园区选项
                $("#sunday_daily_search_form_provinceId,#sunday_daily_search_form_cityId,#sunday_daily_search_form_districtId").change(function(){
                    initGuardAndBunch("sunday_daily_search_form_guardId","sunday_daily_search_form_bunchId",null,null,"sunday_daily_search_form_provinceId","sunday_daily_search_form_cityId","sunday_daily_search_form_districtId");
                })
            </script>
        </div>

    </div>
    <!-- 查询 -->

    <div class="list" id="sunday_daily_div">

    </div>
</div>
<script>
    var sunday_daily_pageNumber=1;
    var sunday_daily_pageSize=50;
    var sunday_daily_sort="id";
    var sunday_daily_order="desc";
    var sunday_daily_is_loading=false;
    var sunday_daily_is_end=false;
    var sunday_daily_guardId="";
    var sunday_daily_bunchId=""
    //输入订单号查询
    function sunday_daily_search(){

        sunday_daily_guardId = $("#sunday_daily_search_form_guardId option:selected").val();
        sunday_daily_bunchId = $("#sunday_daily_search_form_bunchId option:selected").val();
        sunday_daily_is_loading=false;
        sunday_daily_is_end=false;
        sunday_daily_pageNumber =1;
        // sunday_daily_pageSize =1;
        sunday_daily_getData(true);
    }
    function sunday_daily_search_page(pageNumber,pageSize){
        sunday_daily_pageNumber =pageNumber;
        sunday_daily_pageSize =pageSize==null?sunday_daily_pageSize:pageSize;
        sunday_daily_getData(true);
    }
    function sunday_daily_getData(isRemove){
        var t="#sunday_daily_div";

        if(sunday_daily_is_loading||sunday_daily_is_end)return;
        if(isRemove){
            $(t).children().remove();
        }
        sunday_daily_is_loading=true;
        //layer.load(1, {shade: [0.5, '#fff']});
        //layer.load(4, {shade: [0.8, '#393D49']})

        //同步请求
        $.ajax({
            type: "POST",
            url: "/sunday/web/daily/select",
            data: {
                "pageSize":sunday_daily_pageSize,
                "pageNumber":sunday_daily_pageNumber,
                "sort":sunday_daily_sort,
                "order":sunday_daily_order,
                "guardId":sunday_daily_guardId,
                "bunchIds":sunday_daily_bunchId
            },
            dataType: "text",
            async:true,
            success: function(result){
                //d layer.closeAll('loading');
                //返回的是html
                if(result==null||result==""||result.length<1){
                    sunday_daily_is_end=true;
                }else{
                    $(t).append(result);
                    sunday_daily_is_loading=false;
                }
            }
        });
    }
    sunday_daily_getData();

    function sunday_daily_save(id){
        newTab("/sunday/web/daily/input?id=" + id , "新增/编辑");
    }
    function sunday_daily_delete(id) {
        var msg = "确认要删除这条信息吗?";
        layer.confirm(msg, function (index) {
            layer.load(4, {shade: [0.8, '#393D49']})
            //不要异步操作
            $.ajax({
                type: "post",
                url: "/sunday/web/daily/delete",
                data: {"id": id},
                async: false,
                success: function (data) {
                    //关闭加载
                    layer.closeAll('loading');
                    if (data.code == 0) {
                        newTab('/sunday/web/daily/index',null)
                    } else {
                        layer.msg('删除失败', {
                            icon: 2
                        });
                    }
                }
            });
            //最后手动关闭
            layer.close(index);

        });
    }
</script>