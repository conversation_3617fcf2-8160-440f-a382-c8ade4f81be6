#*<div class="add-administrator add-role">*#
<div class="add-plan add-role">
<form id="sunday_user_form" class="form-horizontal" action="/sunday/web/user/save" method="post"  enctype="multipart/form-data">
    <!--隐藏参数-->
    <input type="hidden" name="id" value="$!{result.user.id}">
    <input type="hidden" name="isSuper" id="sunday_user_form_isSuper_input">
    <input type="hidden" name="roleName" id="sunday_user_form_roleName_input">
    <input type="hidden" name="status" id="sunday_user_form_status_input">
    <input type="hidden" name="guardIds" id="sunday_user_form_guardIds_input">
    <input type="hidden" name="bunchIds" id="sunday_user_form_bunchIds_input">

 #*   <div class="con">*#
    <div class="search-wrapper clearfix">
        <div class="list">
            <div class="com-title-wrapper">
                <div class="title">管理员信息</div>
            </div>
            <div class="item clearfix">
                <div class="com-titl"><i>*</i>是否超管：</div>
                <div class="com-radio-wrapper fl clearfix" id="sunday_user_form_isSuper_div">
                    <div class="com-radio-item" value="0">否</div>
                    <div class="com-radio-item" value="1">是</div>
                </div>
            </div>

            <div class="item clearfix">
                <div class="com-titl"><i>*</i>姓名：</div>
                <input type="text"  class="com-input-item fl" name="name"  value="$!{result.user.name}"  required="required">
            </div>
            <div class="item clearfix">
                <div class="com-titl"><i>*</i>电话：</div>
                <input type="text"  class="com-input-item fl" name="mobile"  value="$!{result.user.mobile}"  required="required">
            </div>
            <div class="item clearfix">
                <div class="com-titl"><i>*</i>角色：</div>
                <div class="selectpicker" style="margin: auto 0px">
                    <select  name="roleId"  required="required" id="sunday_user_form_roleId"></select>
                </div>
            </div>
            <div class="item clearfix">
                <div class="com-titl"><i>*</i>登录账号：</div>
                <input type="text"  class="com-input-item fl" name="userName"  value="$!{result.user.userName}"  required="required">
            </div>
            <div class="item clearfix">
                <div class="com-titl">登录密码：</div>
                <input type="password"  class="com-input-item fl" placeholder="默认初始化：123456" name="password">
            </div>
            <div class="item clearfix">
                <div class="com-titl"><i>*</i>操作状态：</div>
                <div class="com-radio-wrapper fl clearfix" id="sunday_user_form_status_div">
                    <div class="com-radio-item" value="1">正常</div>
                    <div class="com-radio-item" value="0">冻结</div>
                </div>
            </div>
            <div class="item clearfix">
                <div class="com-titl"><i>*</i>关联园区：</div>
                #* <div class="com-radio-wrapper fl clearfix" id="sunday_user_form_guard_div">
                     #foreach($!guard in $!guards)
                         <div class="com-radio-item" value="$!guard.id">$!guard.name</div>
                     #end
                </div>*#
                <table border="0" cellspacing="0" cellpadding="0" class="fl" id="sunday_user_guard_and_bunch_table">
                    <tr>
                        <th width="20%">园区</th>
                        <th>班级</th>
                    </tr>
                    #foreach($!guard in $!result.guards)
                        <tr>
                            <td><div class="com-checkbox-item2 firstMenu#if($!guard.isCheck == 1) on #end" value="$!guard.id">$!guard.name</div></td>
                            <td>
                                #foreach($!bunch in $!guard.bunches)
                                    <div class="com-checkbox-item2 secondMenu#if($!bunch.isCheck == 1) on #end" value="$!bunch.id"> $!bunch.name</div>
                                #end
                            </td>
                        </tr>
                    #end
                </table>
            </div>

            <div class="item clearfix">
                <div class="com-titl" style="height: 30px;"></div>
                <div class="button-wrapper fl">
                    <a href="javascript:yuhua_return('/sunday/web/user/index');" class="com-button return">返回</a>
                    <button type="submit" class="com-button">保存</button>
                </div>
            </div>
        </div>
    </div>
</form>
</div>
<script>
    //一级菜单勾选
    $(".firstMenu").click(function () {
        //勾选
        if(!$(this).hasClass("on")){
            //1,勾选一级菜单
            $(this).addClass("on");
            //2，勾选全部二级菜单
            $(this).parent().next().find(".secondMenu").addClass("on");
            //反选
        }else{
            //1,反选一级菜单
            $(this).removeClass("on");
            //2，反选全部二级菜单
            $(this).parent().next().find(".secondMenu").removeClass("on");
        }
    })
    $(".secondMenu").click(function () {
        //勾选
        if(!$(this).hasClass("on")){
            //1,勾选二级级菜单
            $(this).addClass("on");
            //2，勾选一级菜单
            $(this).parent().prev().find(".firstMenu").addClass("on");
            //反选
        }else{
            //1,反选二级级菜单
            $(this).removeClass("on");
            //2,判断是否还没有二级菜单被勾选
            var isAllNot=true;
            $(this).siblings().each(function () {
                if($(this).hasClass("on")){
                    isAllNot=false;
                    return false;
                }
            });
            if(isAllNot){
                //2，反选一级菜单
                $(this).parent().prev().find(".firstMenu").removeClass("on");
            }
        }
    })

</script>
<script type="text/javascript">
    //实例化角色
    var dictionary_url ="$!dictionaryRoot";
    iniSelectRole("$!result.user.roleId")
    //实例化超管，状态，园区 选择框
    //选择框点击
    $("#sunday_user_form_isSuper_div,#sunday_user_form_status_div").children().click(function(){
        $(this).addClass("on");
        $(this).siblings().removeClass("on")
    })
    $("#sunday_user_form_guard_div").children().click(function(){
        if($(this).hasClass("on")){
            $(this).removeClass("on")
        }else{
            $(this).addClass("on");
        }
    })
    
    
    
    var userId = "$!result.user.id";
    //新增
    if(userId==null || userId == 0){
        $("#sunday_user_form_isSuper_div").children(":first").click();
        $("#sunday_user_form_status_div").children(":first").click();
    }else{
        //编辑
        var isSuper  = "$!result.user.isSuper";
        var status ="$!result.user.status";
        $("#sunday_user_form_isSuper_div").children().each(function () {
            if(isSuper == $(this).attr("value")){
                $(this).click();
            }
        });
        $("#sunday_user_form_status_div").children().each(function () {
            if(status == $(this).attr("value")){
                $(this).click();
            }
        });
    }



    var Required = "*必填!";
    //2018年10月4日，验证手机号
    jQuery.validator.addMethod("isMobile", function(value, element) {
        var length = value.length;
        var mobile = /^1[123456789]\d{9}$/;/*/^1(3|4|5|7|8)\d{9}$/*/
        return this.optional(element) || (length == 11 && mobile.test(value));
    }, "请正确填写您的手机号码");

    //实例化表单组件
    var validatorMenu=$("#sunday_user_form").validate({
        rules: {
            mobile: {
                required: true,
                minlength: 11,
                maxlength:11,
                digits:true,
                number:true,
                isMobile : true
            }
        },
        messages: {
            // mobile: "请输入正确的联系方式",
            mobile:{
                required: "不能为空",
                minlength: "必须11位数字",
                maxlength:"必须11位数字",
                digits:"必须是数字"    ,
                number:"请输入有效数字",
                isMobile : "联系方式格式错误"
            },
        },
        submitHandler: function(form) {
            $("#sunday_user_form_isSuper_input").val($("#sunday_user_form_isSuper_div").children(".on").attr("value"));
            $("#sunday_user_form_roleName_input").val($("#sunday_user_form_roleId option:selected").text());
            $("#sunday_user_form_status_input").val($("#sunday_user_form_status_div").children(".on").attr("value"));

            //园区和班级信息
            var guardIds = "";
            var bunchIds = "";

            $("#sunday_user_guard_and_bunch_table").find(".firstMenu").each(function(){
                if($(this).hasClass("on")){

                    guardIds +=$(this).attr("value")+",";
                }
            });

            $("#sunday_user_guard_and_bunch_table").find(".secondMenu").each(function(){
                if($(this).hasClass("on")){
                    bunchIds +=$(this).attr("value")+",";
                }
            });
            if(guardIds!=""){
                guardIds =guardIds.substring(0,guardIds.length-1);
            }
            if(bunchIds!=""){
                bunchIds =bunchIds.substring(0,bunchIds.length-1);
            }
            $("#sunday_user_form_guardIds_input").val(guardIds);
            $("#sunday_user_form_bunchIds_input").val(bunchIds);
            //加遮罩层
            layer.load(4, {shade: [0.8, '#393D49']});

            $(form).ajaxSubmit({
                //你表单提交就要切换页面，ajax加载又是异步的。两边同时进行，
                //当ajax加载在切换页面之前完成，就会执行回调，反之不会执行
                //加同步提交数据
                async: false,
                dataType:"json",
                success:function(data){
                    //关闭加载
                    layer.closeAll('loading');
                    if(data.code == 0){
                        newTab('/sunday/web/user/index',null)
                    }else{
                        layer.msg(data.message, {
                            icon: 2
                        });
                    }
                }
            });
        }
    }) ;
    //实例化话角色
    function iniSelectRole(oldValue){
        var selector =$("#sunday_user_form_roleId");
        if(selector==null||selector==""||typeof(selector)=="undefined")return;
        //$("<option> "+"</option>").appendTo($(selector));
        $.post("/sunday/web/role/selectNoPage",function(data){
            var datas = data.result;
            $(selector).children().remove();
            $("<option value=''>请选择</option>").appendTo($(selector));
            for(var i=0;i<datas.length;i++){
                var data=datas[i];
                var option ="<option";
                if(oldValue!=null&&oldValue!=""&&oldValue==data.id){
                    option+=  " selected='selected'";
                }
                option +=" value="+data.id+">";
                option+=data.name;
                option+="</option>";

                $(option).appendTo($(selector));
            }
        })
    }
</script>