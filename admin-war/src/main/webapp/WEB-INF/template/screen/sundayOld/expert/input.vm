<div class="add-parent-curriculum">
    <form id="sunday_expert_form" class="form-horizontal" action="/sunday/web/expert/save" method="post"  enctype="multipart/form-data">
        <input type="hidden" name="id" value="$!{expert.id}">
        #*<input type="hidden" name="desc"  id="sunday_expert_form_desc_input"/>*#
        <div class="con">
            <div class="com-title-wrapper">
                <div class="title">新增专家</div>
            </div>
            <div class="item clearfix">
                <div class="com-titl"><i>*</i>姓名：</div>
                <input type="text"  class="com-input-item fl"  name="name"  value="$!{expert.name}"  required="required">

            </div>
            <div class="item clearfix">
                <div class="com-titl"><i>*</i>头像：</div>
                <div class="com-add-img xzBtn"
                     style="width:100px;height:100px;background-image: url(#if($expert.image)$!expert.image#else$!{adminRoot}/yuhua/admin/img/add.png#end);"
                     id="sunday_upload_image_div">
                    <input type="hidden" id="sunday_upload_image_input" name="image" value="$!expert.image" >
                </div>
                <div class="com-titl mw80">（300px*300px）</div>
            </div>
            <script>

                //实例化图片裁剪控件
                upImg(4,'sunday_upload_image_div','sunday_upload_image_input',100,100);
            </script>
            <div class="item clearfix">
                <div class="com-titl"><i>*</i>简介：</div>
                <textarea class="com-textarea w500"  id="sunday_expert_desc_form_texarea"  style="width: 400px;height: 200px" name="desc"> $!{expert.desc}</textarea>
            </div>
            <div class="item clearfix">
                <div class="com-titl" style="height: 30px"></div>
                <div class="button-wrapper clearfix fl">
                    <button type="submit" class="com-button fr">保存</button>
                    <a href="javascript:yuhua_return('/sunday/web/expert/index')" class="com-button return fr mr20">返回</a>
                </div>
            </div>
        </div>
    </form>
</div>
<script>
    //实例化富文本
    /*var desc_editor = KindEditor.create('textarea[id="sunday_expert_desc_form_texarea"]', {
        items:[
            'source', '|', 'undo', 'redo','|',"bold",'underline','strikethrough','|',
            'subscript', 'superscript', '|','forecolor', 'hilitecolor','|','removeformat','|',
            'insertorderedlist', 'insertunorderedlist','|','selectall','formatblock','fontname','fontsize','justifyleft',
            'fullscreen','justifycenter','justifyright','|','link','unlink','|','emoticons','image','media','|','baidumap','hr','print','preview'
        ],
        uploadJson:'$!{adminRoot}/sunday/web/upload/kindeditor/upload/single',
        fileManagerJson : '$!{adminRoot}/sunday/web/upload/kindeditor/upload/single',
        allowFileManager : true,
        width:"82.5%",
        height:"400px"
    });*/



    //实例化form
    var Required = "*必填!";
    var validatorMenu = $("#sunday_expert_form").validate({
        submitHandler: function (form) {

            var image_val=$("#sunday_upload_image_input").val();
            if(image_val==null || image_val ==""){
                layer.msg("请上传头像", {
                    icon: 2
                });
                return ;
            }
            //加遮罩层
            layer.load(4, {shade: [0.8, '#393D49']})
            $("#sunday_expert_form_desc_input").val($("#sunday_expert_desc_form_texarea").val());


            $(form).ajaxSubmit({
                //你表单提交就要切换页面，ajax加载又是异步的。两边同时进行，
                //当ajax加载在切换页面之前完成，就会执行回调，反之不会执行
                //加同步提交数据
                async: false,
                dataType: "json",
                success: function (data) {
                    //关闭加载
                    layer.closeAll('loading');
                    if (data.code == 0) {

                        //  newTab("");
                        newTab('/sunday/web/expert/index?zoneKey=$!result.expert.zoneKey',null)
                    } else {
                        layer.msg(data.message, {icon: 2});
                    }
                }
            });
        }
    });
</script>