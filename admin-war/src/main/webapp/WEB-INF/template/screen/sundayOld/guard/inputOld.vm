#set( $layout = "/layout/easyui/h-layout.vm")
<script type="text/javascript" src="$!{staticRoot}/diy/js/sunday_area.js"></script>

<!--如果单行有两列数据，宽度为70%。单行一列数据，就不填写宽度-->

<!--modald的header。存放标题什么的-->
<!-- 此处使用enctype="multipart/form-data"造成表单数据错位 -->
<form id="sunday_guard_form" class="form-horizontal" action="/sunday/web/guard/save" method="post"  enctype="multipart/form-data">
    <!--隐藏参数-->
    <input type="hidden" name="id" value="$!{guard.id}">
    <input type="hidden" name="desc"  id="descInput"/>
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal"></button>
        <h4 class="modal-viceName">基本信息</h4>
    </div>
    <div class="modal-body">
        <div class="row form-horizontal">
            <div class="col-sm-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label"><span style="color: red;">*</span>园区名称:</label>
                    <div class="col-sm-8">
                        <input class="form-control"  type="text" name="name"  value="$!{guard.name}"  required="required">
                    </div>
                </div>
            </div>
        </div>
        <div class="row form-horizontal">
            <div class="col-sm-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label"><span style="color: red;">*</span>园区类型:</label>
                    <div class="col-sm-8">
                        <select id="sunday_guard_form_type" class="form-control" name="type" required="required"></select>
                    </div>
                </div>
            </div>
        </div>
        <div class="row form-horizontal">
            <div class="col-sm-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label"><span style="color: red;">*</span>园长姓名:</label>
                    <div class="col-sm-8">
                        <input class="form-control"  type="text" name="guarderName"  value="$!{guard.guarderName}"  required="required">
                    </div>
                </div>
            </div>
        </div>
        <div class="row form-horizontal">
            <div class="col-sm-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label"><span style="color: red;">*</span>园长联系电话:</label>
                    <div class="col-sm-8">
                        <input class="form-control"  type="text" name="guarderMobile"  value="$!{guard.guarderMobile}"  required="required">
                    </div>
                </div>
            </div>
        </div>
        <div class="row form-horizontal">
            <div class="col-sm-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label"><span style="color: red;">*</span>登录账号（默认密码：123456）:</label>
                    <div class="col-sm-8">
                        <input class="form-control" id="sunday_course1_form_userName" type="text" name="userName"  value="$!{guard.userName}"  required="required">
                    </div>
                </div>
            </div>
        </div>
        <div class="row form-horizontal">
            <div class="col-sm-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">园区图片：</label>
                    <div class="col-sm-8">
                        <input class="form-control"  type="file" name="imageFile">
                        #if($!{guard.image})
                            <a href="$!{imgRoot}$!{guard.image}" target="_blank">[点击查看园区图片]</a>
                        #end
                    </div>
                </div>
            </div>
        </div>
        <div class="row form-horizontal">
            <div class=" col-sm-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label"><font color="#FF0000">*</font>省：</label>
                    <div class="col-sm-8">
                        <select id="provinceId" class="form-control canDis" name="provinceId"  required="required"></select>
                        <input id ="provinceName" name="provinceName" type="hidden" value="$!{guard.provinceName}"/>
                    </div>
                </div>
            </div>
        </div>
        <div class="row form-horizontal">
            <div class=" col-sm-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label"><font color="#FF0000">*</font>市：</label>
                    <div class="col-sm-8">
                        <select id="cityId" class="form-control canDis" name="cityId"  required="required"></select>
                        <input id ="cityName" name="cityName" type="hidden" value="$!{guard.cityName}">
                    </div>
                </div>
            </div>
        </div>
      <div class="row form-horizontal">
            <div class=" col-sm-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label"><font color="#FF0000">*</font>区/县：</label>
                    <div class="col-sm-8">
                        <select id="districtId" class="form-control canDis" name="districtId"  required="required"></select>
                        <input id ="districtName" name="districtName" type="hidden" value="$!{guard.districtName}"/>
                    </div>
                </div>
            </div>
      </div>
      <div class="row form-horizontal">
            <div class="col-sm-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label"><font color="#FF0000">*</font>详细地址：</label>
                    <div class="col-sm-8">
                        <input  class="form-control"  name="address" required="required" value="$!{guard.address}" />
                    </div>
                </div>
            </div>
        </div>

        <div class="row form-horizontal">
            <div class="col-sm-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">图文详情:</label>
                    <div class="col-sm-8">
                        <textarea class="summernote" id="sunday_course1_form_desc">$!{guard.desc}</textarea>
                    </div>
                </div>
            </div>
        </div>

    <div class="form-group text-center"  >
        <button type="button" class="btn btn-default" data-dismiss="modal" onclick="closeCurrentTab()">关闭</button>
            <button  class="btn btn-primary" type="submit">保存</button>
    </div>
</form>

<script type="text/javascript">
    //实例化省市区
    intAreaNoTown("provinceId","cityId","districtId","$!{guard.provinceId}","$!{guard.cityId}","$!{guard.districtId}");
    //实例化类型
    var dictionary_url ="$!dictionaryRoot";
    iniSelect("sunday_guard_form_type",dictionary_url+"guard_type","$!guard.type");
    //实例化editor
   /* var desc_editor = KindEditor.create('textarea[id="sunday_guard_desc"]', {
        uploadJson:'$!{adminRoot}/sunday/web/upload/kindeditor/upload/single',
        fileManagerJson : '$!{adminRoot}/sunday/web/upload/kindeditor/upload/single',
        allowFileManager : true,
        width:"100%",
        height:"400px"
    });*/
    //实例化园长列表
   // initGuarder("$!guard.guarderId");
    //编辑不允许修改登录账号
    var id = "$!guard.id";
    if(id >0){
        //console.info("log-------------------");
      $("#sunday_course1_form_userName").attr("disabled","disabled");
        $("#sunday_guard_form_type").attr("disabled","disabled");

    }else{
       // console.info("log---sssssssssss----------------");
    }

    $('.summernote').summernote({
        height: 400,
        minHeight: 300,
        maxHeight: 500,
        focus: true,
        lang:'zh-CN',
        // 重写图片上传
        onImageUpload: function(files, editor, $editable) {
            summernote_sendFile(this, files,editor,$editable);
        }
    });
    //summbernote富文本编辑图片上传
    function summernote_sendFile(t,files,editor,$editable) {
        console.log("summernote开始上传文件")
        layer.load(4, {shade: [0.8, '#393D49']})
        var data = new FormData();
        data.append("imgFile", files[0]);
        $.ajax({
            data: data,
            type: "POST",
            url: "$!{adminRoot}/sunday/web/upload/kindeditor/upload/single", //图片上传出来的url，返回的是图片上传后的路径，http格式
            cache: false,
            contentType: false,
            processData: false,
            async: false,
            dataType: "json",
            success: function (data) {//data是返回的hash,key之类的值，key是定义的文件名
                //editor.summernote('insertImage', data.url);
                // editor.insertImage($editable, data.url);
                $(t).summernote('editor.insertImage', data.url);

                layer.closeAll('loading');
            },
            error: function () {
                layer.closeAll('loading');
                alert("上传失败");
            }

        });
    }
    var Required = "*必填!";
    //实例化表单组件
    var validatorMenu=$("#sunday_guard_form").validate({
        submitHandler: function(form) {
            //加遮罩层
            layer.load(4, {shade: [0.8, '#393D49']});
            $("#descInput").val($("#sunday_course1_form_desc").val());
            $("#provinceName").val($("#provinceId option:selected").text());
            $("#cityName").val($("#cityId option:selected").text());
            $("#districtName").val($("#districtId option:selected").text());
            $("#sunday_guard_form_guarderName").val($("#sunday_guard_form_guarderId option:selected").text());
            $(form).ajaxSubmit({

                //你表单提交就要切换页面，ajax加载又是异步的。两边同时进行，
                //当ajax加载在切换页面之前完成，就会执行回调，反之不会执行
                //加同步提交数据
                async: false,
                dataType:"json",
                success:function(data){
                    //关闭加载
                    layer.closeAll('loading');
                    if(data.code == 0){
                        //先刷新，后关闭。顺序不要反了
                        //刷新列表      
                        refreashTableInTabs( "/sunday/web/guard/index","sunday_guard_search");
                        //关闭窗口
                        closeCurrentTab();
                    }else{
                        layer.msg(data.message, {
                            icon: 2
                        });
                    }
                }
            });
        }
    }) ;

</script>
