#set( $layout = "/layout/easyui/h-layout.vm")

<body>
<div class="wrapper wrapper-content animated fadeInRight">
    <!--搜索-新增区-->
    <div class="row">
        <div class="col-sm-12">
            <div class="ibox" style="margin-bottom:0px">
                <div class="ibox-title">
                    <h5>查询条件</h5>
                </div>
                <div class="ibox-content" style="display: block" >
                    <div class="row">
                        <form role="form" class="form-inline" id="sunday_advert_zone_search_form">
                            <div class="col-sm-3">
                                <div class="form-group form-form-group">
                                    <label>key值：</label>
                                    <input type="text" name="key" placeholder="请输入KEY" class="form-control input-sm">
                                </div>
                            </div>
                            <div class="col-sm-3">
                                <button class="btn btn-success btn-large" type="button"
                                        onclick="sunday_advert_zone_search()"><i class="fa fa-search"></i>&nbsp;查询
                                </button>
                                <button type="button" onclick="sunday_advert_zone_save(0)"
                                        class="btn btn-success btn-large"><i class="fa fa-plus"></i>&nbsp;新增
                                </button>
                           </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--table-->
    <div class="row">
        <div class="col-sm-12">
            <div class="ibox">
                <div class="ibox-content" style="display: block;">
                    <div class="table-responsive">
                        <table id="sunday_advert_zone_table" class="table table-striped"></table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div id="sunday_advert_zone_div" class="modal fade" role="dialog" style="display: none;" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <!--modald的header。存放标题什么的-->
            <form id="sunday_advert_zone_form" class="form-horizontal"
                  action="/sunday/web/advert/zone/save" method="post" enctype="multipart/form-data">
                <!--隐藏参数-->
                <input type="hidden" name="id">


                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal"></button>
                    <h4 class="modal-title">广告位管理</h4>
                </div>
                <!--modald的body。存放form里面的数据什么的-->

                <div class="modal-body">

                    <div class="row form-horizontal">
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="col-sm-4 control-label"><span style="color: red;">*</span>KEY</label>
                                <div class="col-sm-8">
                                    <input class="form-control" type="text" name="key" required="required">
                                </div>
                            </div>
                        </div>
                    </div>


                    <div class="row form-horizontal">
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="col-sm-4 control-label"><span style="color: red;">*</span>名称:</label>
                                <div class="col-sm-8">
                                    <input class="form-control" type="text" name="name" required="required">

                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row form-horizontal">
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">描述</label>
                                <div class="col-sm-8">
                                    <textarea class="form-control " name="desc" maxlength="100"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--modald的footer。存放按钮神马的-->
                    <div class="modal-footer">
                        <div class="row form-horizontal">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <div class="col-sm-12 text-center">
                                        <button type="button" class="btn btn-primary" data-dismiss="modal">关闭</button>
                                        <button class="btn btn-success" type="submit">保存</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
            </form>
        </div>
    </div>
</div>

</body>
<script>


    //查询。查询方法统一使用sunday_advert_zone_search，方便回调函数调用
    function sunday_advert_zone_search() {
        $("#sunday_advert_zone_table").bootstrapTable("refresh");
    }


    $("#sunday_advert_zone_table").bootstrapTable({
        columns: [
            {field: 'key', title: 'Key', width: 100, align: "center"},
            {field: 'name', title: '名称', width: 150, align: "center"},
            {field: 'desc', title: '描述', width: 200, align: "center"},
            {
                field: 'operate', title: '操作', width: 250, align: "center",
                formatter: function (value, row, index) {
                    var operate = "&nbsp;&nbsp;&nbsp;<button class='btn btn-xs btn-warning' type='button' onclick='sunday_advert_zone_save(" + row.id + ");'>编辑</button>";

                    operate += "&nbsp;&nbsp;&nbsp;<button class='btn btn-danger btn-xs' type='button' onclick='sunday_advert_zone_delete(" + row.id + ");'>删除</button>";
                    operate += "&nbsp;&nbsp;&nbsp;<button class='btn btn-primary btn-xs' type='button' onclick='sunday_advert_manager(\"" + row.key + "\");'>首焦图信息管理</button>";
                    return operate;
                }
            }
        ],
        url: '/sunday/web/advert/zone/select',
        cache: false,
        pagination: true,

        pageSize: 10,
        pageList: [10, 20],
        pageNumber: 1,
        sidePagination: 'server',
        queryParams: function (params) {
            $('#sunday_advert_zone_search_form').find('input[name]').each(function () {
                // //params
                var name = $(this).attr('name');
                var value = $(this).val();
                if (value == null || value == "") {
                    value = null;
                }
                params[name] = value;

            });
            //实例化组件-自带参数
            params['offset'] = params.offset;
            params['limit'] = params.limit;
            params['sort'] = params.sort;
            params['order'] = params.order;

            return params;
        },
        onClickRow: function (row, $element, field) {
                $element.addClass("row-select");
                $element.siblings('tr').removeClass("row-select");
        }
    });

    //新增或修改
    function sunday_advert_zone_save(id) {
        $("#sunday_advert_zone_form").form("clear");

        $("#sunday_advert_zone_form").form("load", {"id": 0});
        if (id != 0) {
            $.post('/sunday/web/advert/zone/findOne', {"id": id}, function (data) {
                $("#sunday_advert_zone_form").form("load", data.result);
            })

        }
        remoteModal("sunday_advert_zone_div", "");

    }

    //打开广告管理
    function sunday_advert_manager(zoneKey) {
        parent.newTab("/sunday/web/advert/index?zoneKey=" + zoneKey, '首焦图信息管理');
    }

    var validatorMenu = $("#sunday_advert_zone_form").validate({

        submitHandler: function (form) {
            //默认
            //layer.load();
            //加遮罩层
            layer.load(4, {shade: [0.8, '#393D49']})


            $(form).ajaxSubmit({

                //你表单提交就要切换页面，ajax加载又是异步的。两边同时进行，
                //当ajax加载在切换页面之前完成，就会执行回调，反之不会执行
                //加同步提交数据
                async: false,
                success: function (data) {
                    //关闭加载
                    layer.closeAll('loading');
                    if (data.code == 0) {
                        //刷新列表
                        //先刷新，后关闭。顺序不要反了
                        //刷新列表
                        //refreashTableInTabs( "/ace/sys/dictionary/list");
                        sunday_advert_zone_search();
                        //关闭窗口
                        $("#sunday_advert_zone_div").modal("toggle");

                    } else {
                        layer.msg('保存失败', {
                            icon: 6
                        });
                    }
                }
            });
        }
    });


    function sunday_advert_zone_delete(id) {
        var msg = "确认要删除这条信息?";
        layer.confirm(msg, function (index) {
            layer.load(4, {shade: [0.8, '#393D49']})
            //不要异步操作
            $.ajax({
                type: "post",
                url: "/sunday/web/advert/zone/delete?id=" + id,
                async: false,
                success: function (data) {
                    //关闭加载
                    layer.closeAll('loading');
                    if (data.code == 0) {
                        sunday_advert_zone_search();
                    } else {
                        layer.msg('删除失败', {
                            icon: 2
                        });
                    }


                }
            });
            //最后手动关闭
            layer.close(index);

        });
    }
</script>
