<div class="weekly-plan">
    <div class="search-wrapper clearfix">
        <div class="com-title-wrapper">
            <div class="title">条件搜索</div>
        </div>
        <table class="search-table" style="margin-top: 20px">
            <tr>
                <td><div class="search-titl">地区选择：</div></td>
                <td>
                    <div class="search-selectpicker">
                        <select id="sunday_history_search_form_provinceId"></select>
                    </div>
                </td>
                <td>
                    <div class="search-selectpicker">
                        <select  id="sunday_history_search_form_cityId"></select>
                    </div>
                </td>
                <td>
                    <div class="search-selectpicker">
                        <select   id="sunday_history_search_form_districtId"></select>
                    </div>
                </td>
                <td></td>
                <td></td>
            </tr>
            <tr>
                <td><div class="search-titl">园区选择：</div></td>
                <td>
                    <div class="search-selectpicker">
                        <select id="sunday_history_search_form_guardId"></select>
                    </div>
                </td>
                <td>
                    <div class="search-selectpicker">
                        <select   id="sunday_history_search_form_bunchId"></select>
                    </div>
                </td>
                <td>  <input type="text" placeholder="搜索学生姓名" class="search-input-item"  id="sunday_history_search_form_name"></td>
                <td><button type="button" class="search-button" onclick="sunday_history_search()">搜索</button>

                </td>

                <td></td>
            </tr>
        </table>
        <script>
            intAreaNoTown("sunday_history_search_form_provinceId","sunday_history_search_form_cityId","sunday_history_search_form_districtId",null,null,null);
            initGuardAndBunch("sunday_history_search_form_guardId","sunday_history_search_form_bunchId",null,null,"sunday_history_search_form_provinceId","sunday_history_search_form_cityId","sunday_history_search_form_districtId");
            //2018年10月22日，省市区控件，控制园区选项
            $("#sunday_history_search_form_provinceId,#sunday_history_search_form_cityId,#sunday_history_search_form_districtId").change(function(){
                initGuardAndBunch("sunday_history_search_form_guardId","sunday_history_search_form_bunchId",null,null,"sunday_history_search_form_provinceId","sunday_history_search_form_cityId","sunday_history_search_form_districtId");
            })
        </script>
    </div>
    <!-- 查询 -->

    <div class="list" id="sunday_history_div">


    </div>
</div>
<script>
    var sunday_history_pageNumber=1;
    var sunday_history_pageSize=50;
    var sunday_history_sort="id";
    var sunday_history_order="desc";
    var sunday_history_is_loading=false;
    var sunday_history_is_end=false;
    var sunday_history_guardId="";
    var sunday_history_bunchId=""
    var sunday_history_name=""
    //输入订单号查询
    function sunday_history_search(){

        sunday_history_guardId = $("#sunday_history_search_form_guardId option:selected").val();
        sunday_history_bunchId = $("#sunday_history_search_form_bunchId option:selected").val();
        sunday_history_name = $("#sunday_history_search_form_name").val();
        sunday_history_is_loading=false;
        sunday_history_is_end=false;
        sunday_history_pageNumber =1;
        // sunday_history_pageSize =1;
        sunday_history_getData(true);
    }
    function sunday_history_search_page(pageNumber,pageSize){
        sunday_history_pageNumber =pageNumber;
        sunday_history_pageSize =pageSize==null?sunday_history_pageSize:pageSize;
        sunday_history_getData(true);
    }
    function sunday_history_getData(isRemove){
        var t="#sunday_history_div";

        if(sunday_history_is_loading||sunday_history_is_end)return;
        if(isRemove){
            $(t).children().remove();
        }
        sunday_history_is_loading=true;
        //layer.load(1, {shade: [0.5, '#fff']});
        //layer.load(4, {shade: [0.8, '#393D49']})

        //同步请求
        $.ajax({
            type: "POST",
            url: "/sunday/web/history/select",
            data: {
                "pageSize":sunday_history_pageSize,
                "pageNumber":sunday_history_pageNumber,
                "sort":sunday_history_sort,
                "order":sunday_history_order,
                "guardId":sunday_history_guardId,
                "bunchId":sunday_history_bunchId,
                "memberName":sunday_history_name
            },
            dataType: "text",
            async:true,
            success: function(result){
                //d layer.closeAll('loading');
                //返回的是html
                if(result==null||result==""||result.length<1){
                    sunday_history_is_end=true;
                }else{
                    $(t).append(result);
                    sunday_history_is_loading=false;
                }
            }
        });
    }
    sunday_history_getData();

    function sunday_history_delete(id) {
        var msg = "确认要删除这条信息吗?";
        layer.confirm(msg, function (index) {
            layer.load(4, {shade: [0.8, '#393D49']})
            //不要异步操作
            $.ajax({
                type: "post",
                url: "/sunday/web/history/delete",
                data: {"id": id},
                async: false,
                success: function (data) {
                    //关闭加载
                    layer.closeAll('loading');
                    if (data.code == 0) {
                        newTab('/sunday/web/history/index',null)
                    } else {
                        layer.msg('删除失败', {
                            icon: 2
                        });
                    }
                }
            });
            //最后手动关闭
            layer.close(index);

        });
    }
    
</script>