<div class="kindergarten-survey">
    <div class="search-wrapper clearfix">
            <div class="com-title-wrapper">
                <div class="title">条件搜索</div>
            </div>
            <table class="search-table" style="margin-top: 20px">
                <tr>
                    <td><div class="search-titl">地区选择：</div></td>
                    <td>
                        <div class="search-selectpicker">
                            <select   id="sunday_guard_search_form_provinceId"></select>
                        </div>
                    </td>
                    <td>
                        <div class="search-selectpicker">
                            <select  id="sunday_guard_search_form_cityId"></select>
                        </div>
                    </td>
                    <td>
                        <div class="search-selectpicker">
                            <select id="sunday_guard_search_form_districtId"></select>
                        </div>
                    </td>
                    <td></td>
                    <td></td>

                </tr>
                <tr>
                    <td><div class="search-titl">园区名称：</div></td>
                    <td><input type="text" placeholder="请输入园区名称" class="search-input-item" id="sunday_guard_search_form_name"></td>
                    <td><button type="button" class="search-button" onclick="sunday_guard_search()">搜索</button>
                        #if($!changeType == 1)
                            <a href="javascript:sunday_guard_save(0);" class="search-add-button">新增园区</a>
                        #end
                    </td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
            </table>
            <script>
                intAreaNoTown("sunday_guard_search_form_provinceId","sunday_guard_search_form_cityId","sunday_guard_search_form_districtId",null,null,null);
               // initGuard("sunday_guard_search_form_guardId",null,"sunday_guard_search_form_provinceId","sunday_guard_search_form_cityId","sunday_guard_search_form_districtId");
            </script>


    </div>
        <!--数据-->
    <div class="list" id="sunday_guard_div">


    </div>
</div>
<script>
    var sunday_guard_pageNumber=1;
    var sunday_guard_pageSize=50;
    var sunday_guard_sort="id";
    var sunday_guard_order="desc";
    var sunday_guard_is_loading=false;
    var sunday_guard_is_end=false;
    var sunday_guard_provinceId="";
    var sunday_guard_cityId="";
    var sunday_guard_districtId="";
    var sunday_guard_name="";

    //输入订单号查询
    function sunday_guard_search(){
        sunday_guard_provinceId = $("#sunday_guard_search_form_provinceId option:selected").val();
        sunday_guard_cityId = $("#sunday_guard_search_form_cityId option:selected").val();
        sunday_guard_districtId = $("#sunday_guard_search_form_districtId option:selected").val();
        sunday_guard_name = $("#sunday_guard_search_form_name").val();
        sunday_guard_is_loading=false;
        sunday_guard_is_end=false;
        sunday_guard_pageNumber =1;
        sunday_guard_getData(true);
    }
    function sunday_guard_search_page(pageNumber,pageSize){
        sunday_guard_pageNumber = pageNumber;
        sunday_guard_pageSize = pageSize==null?sunday_guard_pageSize:pageSize;
        sunday_guard_getData(true);
    }
    function sunday_guard_getData(isRemove){
        var t="#sunday_guard_div";
        if(sunday_guard_is_loading||sunday_guard_is_end)return;
        if(isRemove){
            $(t).children().remove();
        }
        sunday_guard_is_loading=true;
        //同步请求
        $.ajax({
            type: "POST",
            url: "/sunday/web/guard/select",
            data: {
                "pageSize":sunday_guard_pageSize,
                "pageNumber":sunday_guard_pageNumber,
                "sort":sunday_guard_sort,
                "order":sunday_guard_order,
                "provinceId":sunday_guard_provinceId,
                "cityId":sunday_guard_cityId,
                "districtId":sunday_guard_districtId,
                "name":sunday_guard_name,
                "changeType":"$!changeType"
            },
            dataType: "text",
            async:true,
            success: function(result){
                //d layer.closeAll('loading');
                //返回的是html
                if(result==null||result==""||result.length<1){
                    sunday_guard_is_end=true;
                }else{
                    $(t).append(result);
                    sunday_guard_is_loading=false;
                }
            }
        });
    }
    sunday_guard_getData(true);

    //新增或编辑
    function sunday_guard_save(id){
        newTab('/sunday/web/guard/input?id='+id,'新增或编辑')
    }
    //上线或下线
    function sunday_guard_change(t,id){
        var status  = $(t).is(':checked')?1:0;
        //下线操作
        if(status == 0){
            $(t).parent().find('.switch-title').html('未上线');
            $(t).parent().find('.switch-title').removeClass('on');
        }
        //上线操作
        if(status == 1){
            $(t).parent().find('.switch-title').html('运营中');
            $(t).parent().find('.switch-title').addClass('on');
        }

        $.post("/sunday/web/guard/change",{"id":id,"status":status},function (dat) {

        })
    }
</script>