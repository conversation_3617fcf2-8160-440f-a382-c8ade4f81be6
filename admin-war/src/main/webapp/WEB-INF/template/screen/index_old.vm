<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>育华教育管理后台</title>
    <link rel="icon" href="$!adminRoot/yuhua/admin/img/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="$!adminRoot/yuhua/admin/img/favicon.ico" type="image/x-icon">
    <link rel="stylesheet" type="text/css" href="$!adminRoot/yuhua/admin/css/bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="$!adminRoot/plug/cropper/cropper.css">
    <link rel="stylesheet" type="text/css" href="$!adminRoot/yuhua/admin/css/style.css">
    <link rel="stylesheet" href="$!adminRoot/plug/cropper/cropperDiy.css" />
    <link rel="stylesheet" href="$!adminRoot/plug/css/style.min862f.css?v=4.1.0" rel="stylesheet">
    <script type="text/javascript" src="$!adminRoot/h_admin/js/jquery.min.js?v=2.1.4"></script>
    <script type="text/javascript" src="$!adminRoot/plug/jquery-easyui/ajaxfileupload.js"></script>
    <script type="text/javascript"  src="$!adminRoot/plug/jquery-easyui/jquery.easyui.min.js"></script>
    <script type="text/javascript" src="$!adminRoot/yuhua/admin/js/bootstrap.min.js"></script>
    <script type="text/javascript" src="$!adminRoot/yuhua/admin/js/bootstrap-select.js"></script>
    <script type="text/javascript" src="$!adminRoot/diy/js/sunday_area.js"></script>
#*  <script type="text/javascript" src="$!adminRoot/plug/js/plugins/layer/layer.js?v=2.4"></script>*#

    <script type="text/javascript" src="$!adminRoot/diy/js/yuhuaDiy.js?v=1.1"></script>
    <script type="text/javascript" src="$!adminRoot/plug/cropper/cropper.js"></script>
    <script type="text/javascript" src="$!adminRoot/plug/cropper/upImg2.js"></script>
    <script type="text/javascript" src="$!adminRoot/plug/js/plugins/validate/jquery.validate.min.js?v=1.15.0"></script>
    <script type="text/javascript" src="$!adminRoot/plug/js/plugins/validate/additional-methods.js"></script>
    <script type="text/javascript" src="$!adminRoot/plug/js/plugins/validate/jquery-form.js"></script>
    <script type="text/javascript" src="$!adminRoot/plug/js/plugins/validate/messages_zh.min.js"></script>

    <!--2018年10月30日 更新kindeditor-->
    <script charset="utf-8" src="$!adminRoot/plug/kindeditor/kindeditor-all-min.js"></script>
    <script charset="utf-8" src="$!adminRoot/plug/kindeditor/lang/zh-CN.js"></script>
#* <script src="$!adminRoot/plug/js/plugins/layer/layDate-v5.0.9/laydate/laydate.js"></script>*#

    <!--2018年10月29日，引入wangEditor-->
#* <link rel="stylesheet" href="$!adminRoot/plug/wangEditor/wangEditor.min.css" rel="stylesheet">

 <script src="http://unpkg.com/wangeditor/release/wangEditor.min.js"></script>*#
    <!--2018年10月30日，引入summernote-->
    <link type="text/css" rel="stylesheet" href="$!adminRoot/plug/summernote/summernote.css" />
    <script type="text/javascript" src="$!adminRoot/plug/summernote/summernote.js"></script>
    <script type="text/javascript" src="$!adminRoot/plug/summernote/lang/summernote-zh-CN.js"></script>
</head>
<body>
<div class="left-wrapper">
    <div class="content">
        <img src="$!adminRoot/yuhua/admin/img/<EMAIL>" alt="" class="logo">
        <div class="menu-wrapper">

            #foreach($menudto in $allowMenus)
                <div class="menu-item">
                    <!--一级菜单-->
                    #if($!menudto.url == '#')
                        <a href="javascript:;" class="con clearfix" >
                            <img src="$!adminRoot/yuhua/admin/img/$menudto.menuClass" alt="" class="icon">
                            <div class="text">$menudto.name</div>
                            <div class="next"></div>
                        </a>
                        <!--二级菜单-->
                        <div class="menu-li-wrapper">
                            #foreach($child in $!menudto.children)
                                <a href="javascript:;" class="menu-li" onclick="newTab('$!child.url',null)">$child.name</a>
                            #end
                        </div>
                    #else
                        <a href="javascript:;" class="con clearfix" onclick="newTab('$!menudto.url',null)">
                            <img src="$!adminRoot/yuhua/admin/img/$menudto.menuClass" alt="" class="icon">
                            <div class="text">$menudto.name</div>
                        </a>
                    #end


                </div>
            #end

        </div>
    </div>
</div>
<!-- left -->
<!--内容页-->
<div class="right-wrapper" >
    <div class="top clearfix">
        <div class="title" id="sunday_index_right_title"></div>
        <a href="/admin/logout" class="exit"></a>
        <i>|</i>
        <img src="$!adminRoot/yuhua/admin/img/avatar.png" alt="" class="avatar">
        <i>|</i>
        <div class="name">欢迎您：$!systemUserDto.name</div>
    </div>
    <div class="top-place"></div>
    <div class="content" id="sunday_web_content">


    </div>
</div>
<!-- right-wrapper -->
<!--2018年10月30日 退出确认-->
<div class="mask" style="display: none" id="yuhua_return_mask"></div>
<div class="com-popup" id="yuhua_return_div">
    <form action="" method="">
        <div class="title">返回</div>
        <div class="text" style="line-height: 1;text-align: center;font-size: 16px;color: #333;">返回将删除您之前编写的全部内容，确定要返回吗？</div>
        <div class="button-wrapper">
            <a href="javascript:;" class="com-button return" id="yuhua_return_cancel">取消</a>
            <button type="button" class="com-button" id="yuhua_return_quite">确认返回</button>
        </div>
    </form>
</div>

<script type="text/javascript" src="$!adminRoot/yuhua/admin/js/com.js"></script>
<script>

    function newTab(url,title){
        if(tabLoading){
            return false;
        }
        $("#sunday_web_content").children().remove();
        setRigthTitle(title);
        $.ajax({
            type: "POST",
            url: url,
            data: {},
            dataType: "text",
            async:false,
            success: function(result){
                $("#sunday_web_content").html(result);
            }
        });
    }
    function setRigthTitle(title){
        //  console.log("AAAAA");
        var oldTitle = $("#sunday_index_right_title").text();
        //1，获取一级标题
        // var first = $(".left-wrapper").find(".ac").find(".text").text();
        // console.info(first);

        //1，选中一级菜单
        var isFirst = false;
        var text ="";
        $(".menu-item").each(function(){
            if($(this).hasClass("on")){
                isFirst=true;
                text = $(this).find(".text").text();
            }
        });
        //2，选中二级菜单
        if(!isFirst){
            $(".menu-item").find(".menu-li").each(function () {
                //二级菜单
                if($(this).hasClass("on")){
                    text = $(this).parent().prev().find(".text").text();
                    text +=" > "+$(this).text();
                }
            })
        }
        if(title != null){
            $("#sunday_index_right_title").text(text+" > "+title);
        }else{
            $("#sunday_index_right_title").text(text);
        }

    }
    //默认打开第一个菜单
    // newTab('/sunday/web/advert/index?zoneKey=app_index_top_roll',null);
    $(".menu-li:eq(0)").click();

    //休眠方法毫秒
    function sleep(n) {
        var start = new Date().getTime();
        //  console.log('休眠前：' + start);
        while (true) {
            if (new Date().getTime() - start > n) {
                break;
            }
        }
        // console.log('休眠后：' + new Date().getTime());
    }


</script>

</body>
</html>