<!--前端权限公用模板-->
#parse("/screen/sunday/operate.vm")
<div class="teacher-list">
    <div class="search-wrapper clearfix">
        <form action="" method="">
            <div class="com-title-wrapper">
                <div class="title">条件搜索</div>
            </div>
            <table class="search-table" style="margin-top: 20px">
                <tr>
                    <td><div class="search-titl">教工姓名：</div></td>
                    <td><input type="text" placeholder="请输入教工姓名" class="search-input-item" id="sunday_teacher_search_form_name" value="$!param.name"></td>
                    <td><button type="button" class="search-button" onclick="sunday_teacher_search(true)">搜索</button>
                        <a href="javascript:sunday_teacher_save(0);" class="search-add-button sunday_teacher_add">新增教工</a>
                        <div style="cursor:pointer" class="search-add-button sunday_teacher_batchSave" onclick="sunday_teacher_batch_save()">批量导入</div>
                    </td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
            </table>
        </form>
    </div>
    <!-- 查询 -->
    <div class="list" id="sunday_teacher_div"></div>
</div>
<!--批量导入-->
<div class="mask" style="display: none"></div>
<div class="com-popup kindergarten-assessment-popup kindergarten-assessment-popup2" id="sunday_teacher_batch_save_div">
    <form id="sunday_teacher_batch_form" class="form-horizontal" action="/sunday/web/teacher/batchSave"  method="post"  enctype="multipart/form-data">
        <input type="hidden" name="guardId"/>
        <div class="title">教师批量导入</div>
        <div class="item clearfix">
            <div class="com-titl"><i>*</i>模板文件：</div>
            <input   name="excelFile" type="file" required="required"/>
        </div>
        <div class="item clearfix">
            <div class="com-titl">模板下载：</div>
            <button type="button" class="com-button" onclick="window.open('$!staticRoot/teacher.xls')">点击下载</button>
        </div>
        <div class="button-wrapper">
            <a href="javascript:sunday_teacher_batch_operate();" class="com-button return" >关闭</a>
            <button type="submit" class="com-button">保存</button>
        </div>
    </form>
</div>
<script>
    $.ajaxSetup({
        async: false
    });
    function sunday_teacher_batch_save(){
        $("#sunday_teacher_batch_form").form("clear");
        $("#sunday_teacher_batch_form").form("load",{"guardId":"$!guardId"})
        sunday_teacher_batch_operate();
    }
    function sunday_teacher_batch_operate() {
        if($("#sunday_teacher_batch_save_div").hasClass("on")){
            $("#sunday_teacher_batch_save_div").removeClass("on");
            $(".mask").hide();
        }else{
            $("#sunday_teacher_batch_save_div").addClass("on");
            $(".mask").show();
        }
    }
    var validatorMenu=$("#sunday_teacher_batch_form").validate({
        submitHandler: function(form) {
            //加遮罩层
            layer.load(4, {shade: [0.8, '#393D49']})
            $(form).ajaxSubmit({
                //你表单提交就要切换页面，ajax加载又是异步的。两边同时进行，
                //当ajax加载在切换页面之前完成，就会执行回调，反之不会执行
                //加同步提交数据
                async: false,
                success:function(data){
                    //关闭加载
                    layer.closeAll('loading');
                    if(data.code ==0){
                        sunday_teacher_search();
                        sunday_teacher_batch_operate();
                    }else{
                        layer.msg(data.message, {
                            icon: 2
                        });
                    }
                }
            });
        }
    }) ;
</script>

<script>
    var param_pageNumber = "$!param.pageNumber";
    var param_pageSize = "$!param.pageSize";
    if(param_pageNumber==null|| param_pageNumber==""){
        param_pageNumber=1
    }
    if(param_pageSize==null|| param_pageSize==""){
        param_pageSize=50
    }
    var sunday_teacher_pageNumber=param_pageNumber;
    var sunday_teacher_pageSize=param_pageSize;
    var sunday_teacher_sort="id";
    var sunday_teacher_order="desc";
    var sunday_teacher_is_loading=false;
    var sunday_teacher_is_end=false;
    var sunday_teacher_name="";

    //输入订单号查询
    function sunday_teacher_search(isPageNumber){
        sunday_teacher_name = $("#sunday_teacher_search_form_name").val();
        sunday_teacher_is_loading=false;
        sunday_teacher_is_end=false;
        if(isPageNumber){
            sunday_teacher_pageNumber =1;
        }
        sunday_teacher_getData(true);
    }

    function sunday_teacher_search_page(pageNumber,pageSize,maxPageNumber){
        if(maxPageNumber!=null&&maxPageNumber!=""&&typeof(maxPageNumber)!="undefined"){
            if(parseInt(pageNumber)>parseInt(maxPageNumber)){
                return;
            }
        }
        sunday_teacher_pageNumber =pageNumber;
        sunday_teacher_pageSize =pageSize==null?sunday_teacher_pageSize:pageSize;
        sunday_teacher_getData(true);
    }

    function sunday_teacher_getData(isRemove){
        var t="#sunday_teacher_div";
        if(sunday_teacher_is_loading||sunday_teacher_is_end)return;
        sunday_teacher_is_loading=true;
        //同步请求
        $.ajax({
            type: "POST",
            url: "/sunday/web/teacher/select",
            data: {
                "pageSize":sunday_teacher_pageSize,
                "pageNumber":sunday_teacher_pageNumber,
                "sort":sunday_teacher_sort,
                "order":sunday_teacher_order,
                "guardId":"$!guardId",
                "name":sunday_teacher_name,
            },
            dataType: "text",
            async:true,
            success: function(result){

                if(result==null||result==""||result.length<1){
                    sunday_teacher_is_end=true;
                }else{
                    if(isRemove){
                        $(t).children().remove();
                    }
                    $(t).append(result);
                    sunday_teacher_is_loading=false;
                }
            }
        });
    }
    sunday_teacher_search(false);

    function sunday_teacher_save(id){
        newTab("/sunday/web/teacher/input?id=" + id+"&guardId=$!guardId", "新增/编辑");
    }

    //上线或下线
    function sunday_teacher_change(t,id){
        var status  = $(t).is(':checked')?1:0;

        //下线操作
        if(status == 0){
            $(t).parent().find('.switch-title').html('离园');
            $(t).parent().find('.switch-title').removeClass('on');
        }
        //上线操作
        if(status == 1){
            $(t).parent().find('.switch-title').html('在园');
            $(t).parent().find('.switch-title').addClass('on');
        }

        $.post("/sunday/web/teacher/change",{"id":id,"status":status},function (dat) {

        })
    }
</script>