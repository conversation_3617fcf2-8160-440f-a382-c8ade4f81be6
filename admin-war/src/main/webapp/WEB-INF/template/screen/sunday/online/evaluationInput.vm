#parse("/screen/sunday/operate.vm")
<style>
    .deletedImg{
        width: 13px;
        height: 13px;
        margin-top: -19px;
        margin-left: -6px;
        cursor: pointer;
    }

.tag-item{
    float: left;

    padding: 0px 10px;
}
    .deleteDateImg{
        width: 20px;
        height: 20px;
        cursor: pointer;
    }
    .search-selectpicker{
        width: 350px;
    }
    .search-selectpicker select{
        width: 242px;
    }
</style>
<div class="add-student">
<form id="sunday_evaluation_form" class="form-horizontal" action="/sunday/web/online/evaluationSave" method="post"  enctype="multipart/form-data">
    <!--隐藏参数-->
    <input type="hidden" name="courseId" value="$!{courseId}">
    <input type="hidden" name="desc" value="" id="sunday_evaluation_form_desc">
    <input type="hidden" name="image" value="" id="sunday_evaluation_form_image">

    <div class="com-title-wrapper">
        <div class="title">

                新增评论

        </div>
    </div>
    <div class="con">
        <div class="item clearfix">
            <div class="com-titl"><i>*</i>课程名称：</div>
            <input type="text"  class="com-input-item fl" style="width: 351px;" placeholder="请输入课程名称" name="courseName" value="$!course.courseName" readonly>
        </div>



        <div class="item clearfix">
            <div class="com-titl" id="descName"><i>*</i>评论：</div>
            <textarea  id="sunday_course_desc_form_texarea"  style="width: 82.5%;float: left" placeholder = ""></textarea>
        </div>

        <div class="item clearfix">
            <div class="com-titl"><i>*</i>图片：</div>
            <div class="com-add-img xzBtn"
                 style="width:100px;height:100px;"
                 id="sunday_upload_image_div"
                 onclick="upImg2(4,'sunday_upload_image_div','sunday_upload_image_input',100,100)">
                <input type="hidden" id="sunday_upload_image_input" class="form_input_image" value="" >
            </div>
            <div class="">建议尺寸690*300px,支持.jpg.png格式,图片小于5M</div>
        </div>
        <div class="item clearfix">
            <div class="com-titl"><i>*</i>图片：</div>
            <div class="com-add-img xzBtn"
                 style="width:100px;height:100px;"
                 id="sunday_upload_image_div2"
                 onclick="upImg2(4,'sunday_upload_image_div2','sunday_upload_image_input2',100,100)">
                <input type="hidden" id="sunday_upload_image_input2" class="form_input_image" value="" >
            </div>
            <div class="">建议尺寸690*300px,支持.jpg.png格式,图片小于5M</div>
        </div>
        <script>

            //  upImg(4,'sunday_upload_image_div','sunday_upload_image_input',100,100);
            function upImg2(bili,sunday_upload_image_div,sunday_upload_image_input,width,height,callType) {
                //1,加载素材库
                layer.load(4, {shade: [0.8, '#393D49']})
                //不要异步操作
                $.ajax({
                    type: "post",
                    url: "/sunday/web/material/upload/index",
                    data: {"type": 3,"bili":bili,"imageDiv":sunday_upload_image_div,"imageInput":sunday_upload_image_input,"width":width,"height":height,"callType":callType},
                    async: false,
                    success: function (data) {
                        //关闭加载
                        layer.closeAll('loading');
                        $(".right-wrapper").after(data);
                    }
                });

            }

        </script>
        <div class="item clearfix">
            <div class="com-titl"><i>*</i>课程分数：</div>
            <div style="    float: left;">
                <div style="clear: both">

            <input type="text"  class="com-input-item fl" style="width: 200px;" placeholder="" name="score" value="" >整数

        </div>

        <div class="button-wrapper clearfix">
            <a href="javascript:yuhua_return('/sunday/web/online/recommendCourse?type=0');" class="com-button return">返回</a>
            <button type="submit" class="com-button sunday_column_save">保存</button>
        </div>
    </div>

</form>
<!-- 查询 -->
</div>



<script>
    //    上传
    var validatorMenu = $("#sunday_evaluation_form").validate({
        submitHandler: function (form) {


            //加遮罩层
            layer.load(4, {shade: [0.8, '#393D49']})
var image_input_value=""
$(".form_input_image").each(function () {
    image_input_value+=($(this).val()+"_,_")
})
$("#sunday_evaluation_form_image").val(image_input_value);
            $("#sunday_evaluation_form_desc").val($("#sunday_course_desc_form_texarea").val())


            $(form).ajaxSubmit({
                //你表单提交就要切换页面，ajax加载又是异步的。两边同时进行，
                //当ajax加载在切换页面之前完成，就会执行回调，反之不会执行
                //加同步提交数据
                async: false,
                dataType: "json",
                success: function (data) {
                    //关闭加载
                    layer.closeAll('loading');
                    if (data.code == 0) {
                        //  newTab("");
                        newTab('/sunday/web/online/recommendCourse?type=0',null)
                    } else {
                        layer.msg(data.message, {icon: 2});
                    }
                }
            });
        }
    });
</script>



