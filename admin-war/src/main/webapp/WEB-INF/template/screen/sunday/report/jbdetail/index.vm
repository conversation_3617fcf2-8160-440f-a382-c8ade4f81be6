#parse("/screen/sunday/operate.vm")
<div class="weekly-plan">
<div class="search-wrapper clearfix">
        <div class="com-title-wrapper">
            <div class="title">条件搜索</div>
        </div>
        <table class="search-table" style="margin-top: 20px">
            <tr>
                <td><div class="search-titl">地区选择：</div></td>
                <td>
                    <div class="search-selectpicker">
                        <select id="sunday_jbdetail_search_form_provinceId"></select>
                    </div>
                </td>
                <td>
                    <div class="search-selectpicker">
                        <select  id="sunday_jbdetail_search_form_cityId"></select>
                    </div>
                </td>
                <td>
                    <div class="search-selectpicker">
                        <select   id="sunday_jbdetail_search_form_districtId"></select>
                    </div>
                </td>
                <td></td>
                <td></td>
            </tr>
            <tr>
                <td><div class="search-titl">时间选择：</div></td>
                <td>
                    <input type="text" class="search-input-item" style="width:240px;" readonly placeholder="请选择开始日期"  style="background: #f2f2f2;width: 150px" id="sunday_student_body_search_startDate" value="">
                </td>
                <td>
                    <input type="text" class="search-input-item" style="width:240px;" readonly placeholder="请选择结束日期" style="background: #f2f2f2;width: 150px" id="sunday_student_body_course_search_endDate" value="">
                </td>
                <td></td>
                <td></td>
                <td></td>
            </tr>
            <tr>
                <td><div class="search-titl">园区选择：</div></td>
                <td>
                    <div class="search-selectpicker">
                        <select id="sunday_jbdetail_search_form_guardId"></select>
                    </div>
                </td>
                <td>
                    <div class="search-selectpicker">
                        <select   id="sunday_jbdetail_search_form_bunchId"></select>
                    </div>
                </td>
                <td>
                	<button type="button" class="search-button" onclick="sunday_jbdetail_search(true)">搜索</button>
                	<button type="button" class="search-button" onclick="evaluation_daochu()">导出</button>
                </td>
                <td></td>
                <td></td>
            </tr>
        </table>
        <script>
            $.ajaxSetup({
                async: false
            });
            intAreaNoTown("sunday_jbdetail_search_form_provinceId","sunday_jbdetail_search_form_cityId","sunday_jbdetail_search_form_districtId",null,null,null);
            initGuardAndBunch("sunday_jbdetail_search_form_guardId","sunday_jbdetail_search_form_bunchId","$!param.guardIds","$!param.bunchIds","sunday_jbdetail_search_form_provinceId","sunday_jbdetail_search_form_cityId","sunday_jbdetail_search_form_districtId");
            $("#sunday_jbdetail_search_form_provinceId,#sunday_jbdetail_search_form_cityId,#sunday_jbdetail_search_form_districtId").change(function(){
                initGuardAndBunch("sunday_jbdetail_search_form_guardId","sunday_jbdetail_search_form_bunchId",null,null,"sunday_jbdetail_search_form_provinceId","sunday_jbdetail_search_form_cityId","sunday_jbdetail_search_form_districtId");
            })
        </script>
   
</div>
<div class="list" id="sunday_jbdetail_div"></div>
</div>
<script>


    laydate.render({
        elem: '#sunday_student_body_search_startDate'
    });

    laydate.render({
        elem: '#sunday_student_body_course_search_endDate'
    });
    
    var param_pageNumber = "$!param.pageNumber";
    var param_pageSize = "$!param.pageSize";
    if(param_pageNumber==null|| param_pageNumber==""){
        param_pageNumber=1
    }
    if(param_pageSize==null|| param_pageSize==""){
        param_pageSize=50
    }
    var sunday_jbdetail_pageNumber=param_pageNumber;
    var sunday_jbdetail_pageSize=param_pageSize;
    var sunday_jbdetail_sort="ct";
    var sunday_jbdetail_order="desc";
    var sunday_jbdetail_is_loading=false;
    var sunday_jbdetail_is_end=false;
    var sunday_jbdetail_guardId="";
    var sunday_jbdetail_bunchId=""
    var startDate="";
    var endDate="";
    //输入订单号查询
    function sunday_jbdetail_search(isPageNumber){
        sunday_jbdetail_guardId = $("#sunday_jbdetail_search_form_guardId option:selected").val();
        sunday_jbdetail_bunchId = $("#sunday_jbdetail_search_form_bunchId option:selected").val();
        startDate = $("#sunday_student_body_search_startDate").val();
        endDate = $("#sunday_student_body_course_search_endDate").val();
        sunday_jbdetail_is_loading=false;
        sunday_jbdetail_is_end=false;
        if(isPageNumber){
            sunday_jbdetail_pageNumber =1;
        }
        sunday_jbdetail_getData(true);
    }

    function sunday_jbdetail_search_page(pageNumber,pageSize,maxPageNumber){
        if(maxPageNumber!=null&&maxPageNumber!=""&&typeof(maxPageNumber)!="undefined"){
            if(parseInt(pageNumber)>parseInt(maxPageNumber)){
                return;
            }
        }
        sunday_jbdetail_pageNumber =pageNumber;
        sunday_jbdetail_pageSize =pageSize==null?sunday_jbdetail_pageSize:pageSize;
        sunday_jbdetail_getData(true);
    }

    function sunday_jbdetail_getData(isRemove){
        var t="#sunday_jbdetail_div";

        if(sunday_jbdetail_is_loading||sunday_jbdetail_is_end)return;
        if(isRemove){
            $(t).children().remove();
        }
        sunday_jbdetail_is_loading=true;
        //同步请求
        $.ajax({
            type: "POST",
            url: "/sunday/web/jbdetail/select",
            data: {
                "pageSize":sunday_jbdetail_pageSize,
                "pageNumber":sunday_jbdetail_pageNumber,
                "sort":sunday_jbdetail_sort,
                "order":sunday_jbdetail_order,
                "guardId":sunday_jbdetail_guardId,
                "bunchId":sunday_jbdetail_bunchId,
                "startDate":startDate,
                "endDate":endDate
            },
            dataType: "text",
            async:true,
            success: function(result){
                if(result==null||result==""||result.length<1){
                    sunday_jbdetail_is_end=true;
                }else{
                    if(isRemove && $(t).children()!=null){
                        $(t).children().remove();
                    }
                    $(t).append(result);
                    sunday_jbdetail_is_loading=false;
                }
            }
        });
    }
    sunday_jbdetail_search(false);

    function sunday_jbdetail_detail(id){
        newTab("/sunday/web/jbdetail/detail?id=" + id , "详情查看");
    }
    
    function  evaluation_daochu() {
    
        sunday_jbdetail_guardId = $("#sunday_jbdetail_search_form_guardId option:selected").val();
        sunday_jbdetail_bunchId = $("#sunday_jbdetail_search_form_bunchId option:selected").val();
        startDate = $("#sunday_student_body_search_startDate").val();
        endDate = $("#sunday_student_body_course_search_endDate").val();
        
        $.ajax({
            type: "POST",
            url: "/sunday/web/jbdetail/export",
            data:   {
                "guardId":sunday_jbdetail_guardId,
                "bunchId":sunday_jbdetail_bunchId,
                "startDate":startDate,
                "endDate":endDate
            },
            dataType: "text",
            async: true,
            success: function (data) {
            	//alert(JSON.parse(data).code);
            	var obj = JSON.parse(data);
                layer.closeAll('loading');
                if (obj.code == 0) {
                    window.open(obj.result);
                } else {
                    layer.msg('导出失败', {
                        icon: 2
                    });
                }
            }
        });
    }
</script>