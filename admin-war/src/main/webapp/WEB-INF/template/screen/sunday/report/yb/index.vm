#parse("/screen/sunday/operate.vm")
<div class="weekly-plan">
    <div class="search-wrapper clearfix">
        <div class="com-title-wrapper">
            <div class="title">条件搜索</div>
        </div>
        <table class="search-table" style="margin-top: 20px">
        <tr>
            <td><div class="search-titl">月报：</div></td>
            <td>
            	<div class="search-selectpicker">
            	<select name="sunday_report_code" id="sunday_report_code">
                    <option value="">--请选择月报--</option>
                    <option value="YB_SPARK">SPARK儿童运动课程</option>
                    <option value="YB_SEL">SEL社会情感学习课程</option>
                    <option value="YB_XXL">学习力课程</option>
                </select>
                </div>
            </td>
            </td>
            <td><button type="button" class="search-button" onclick="sunday_report_search(true)">搜索</button></td>
            <td colspan=3>
            	<a class="search-add-button sunday_report_add" href="javascript:sunday_report_save('YB_SPARK', 0);" >SPARK儿童运动课程</a>
            	<a class="search-add-button sunday_report_add" href="javascript:sunday_report_save('YB_SEL', 0);" >SEL社会情感学习课程</a>
            	<a class="search-add-button sunday_report_add" href="javascript:sunday_report_save('YB_XXL', 0);" >学习力课程</a>
                <a class="search-add-button sunday_report_add" href="javascript:sunday_report_save('YB_ALL', 0);" >新增课程模版</a>
            </td>
        </tr>
    </table>
    </div>
    <div class="list" id="sunday_role_div"></div>
</div>
<script>
    $.ajaxSetup({
        async: false
    });
    var param_pageNumber = "$!param.pageNumber";
    var param_pageSize = "$!param.pageSize";
    console.log("param_pageNumber="+param_pageNumber+",param_pageSize="+param_pageSize)
    if(param_pageNumber==null|| param_pageNumber==""){
        param_pageNumber=1
    }
    if(param_pageSize==null|| param_pageSize==""){
        param_pageSize=50
    }
    var sunday_report_pageNumber=param_pageNumber;
    var sunday_report_pageSize=param_pageSize;
    var sunday_report_sort="id";
    var sunday_report_order="desc";
    var sunday_role_is_loading=false;
    var sunday_role_is_end=false;
    var sunday_report_code=""
    //输入订单号查询
    function sunday_report_search(isPageNumber){
        sunday_report_code = $("#sunday_report_code").val();
        sunday_role_is_loading=false;
        sunday_role_is_end=false;
        if(isPageNumber){
            sunday_report_pageNumber =1;
        }
        sunday_report_getData(true);
    }
    function sunday_report_search_page(pageNumber,pageSize,maxPageNumber){
        if(maxPageNumber!=null&&maxPageNumber!=""&&typeof(maxPageNumber)!="undefined"){
            if(parseInt(pageNumber)>parseInt(maxPageNumber)){
                return;
            }
        }
        sunday_report_pageNumber =pageNumber;
        sunday_report_pageSize =pageSize==null?sunday_report_pageSize:pageSize;
        sunday_report_getData(true);
    }
    function sunday_report_getData(isRemove){
        var t="#sunday_role_div";
        if(sunday_role_is_loading||sunday_role_is_end)return;
        if(isRemove){
            $(t).children().remove();
        }
        sunday_role_is_loading=true;
        //同步请求
        $.ajax({
            type: "POST",
            url: "/sunday/web/yb/select",
            data: {
                "pageSize":sunday_report_pageSize,
                "pageNumber":sunday_report_pageNumber,
                "sort":sunday_report_sort,
                "order":sunday_report_order,
                "code":sunday_report_code
            },
            dataType: "text",
            async:true,
            success: function(result){
                if(result==null||result==""||result.length<1){
                    sunday_role_is_end=true;
                }else{
                    if(isRemove && $(t).children()!=null){
                        $(t).children().remove();
                    }
                    $(t).append(result);
                    sunday_role_is_loading=false;
                }
            }
        });
    }
    sunday_report_search(false);

    function sunday_report_save(code, id){
        newTab("/sunday/web/yb/input?code="+code+"&&id="+id , "新增/编辑");
    }
    function sunday_report_delete(id) {
        var msg = "确认要删除这条信息吗?";
        layer.confirm(msg, function (index) {
            layer.load(4, {shade: [0.8, '#393D49']})
            //不要异步操作
            $.ajax({
                type: "post",
                url: "/sunday/web/yb/delete",
                data: {"id": id},
                async: false,
                success: function (data) {
                    //关闭加载
                    layer.closeAll('loading');
                    if (data.code == 0) {
                        sunday_report_search(false);
                    } else {
                        layer.msg(data.message, {
                            icon: 2
                        });
                    }
                }
            });
            //最后手动关闭
            layer.close(index);
        });
    }
    
    function sunday_report_publish(id) {
        var msg = "确认要发布这条月报吗?"
        layer.confirm(msg, function (index) {
            layer.load(4, {shade: [0.8, '#393D49']})
            //不要异步操作
            $.ajax({
                type: "post",
                url: "/sunday/web/yb/publish",
                data: {"id": id},
                async: false,
                success: function (data) {
                    //关闭加载
                    layer.closeAll('loading');
                    if (data.code == 0) {
                        sunday_report_search(false);
                    } else {
                        layer.msg(data.message, {
                            icon: 2
                        });
                    }
                }
            });
            //最后手动关闭
            layer.close(index);
        });
    }
</script>