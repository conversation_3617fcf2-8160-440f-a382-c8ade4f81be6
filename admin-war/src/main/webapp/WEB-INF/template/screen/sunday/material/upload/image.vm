<style>
    .menu-item.on{
        background:#f4f5f9
    }
</style>
<div class="mask" id="sunday_material_choose_mask" ></div>
<!-- 从素材库选择封面弹窗 -->
<div class="com-popup select-picture-popup select-picture-list-popup" id="sunday_material_choose_step1" style="z-index:99999">
    <div class="title">选择图片</div>
    <div class="title-des"><i>1.从素材库选择封面</i> 一 2.裁剪封面</div>
    <div class="con clearfix">
        <div class="select-picture-list-left" id="sunday_material_choose_step1_group_div">
            #foreach($!group in $!groups)
                <div class="menu-item #if($!group.isCheck == 1) on #end" value="$!group.id">
                    <div class="menu-item-titl">$!group.name<i>($!group.number)</i></div>
                </div>
            #end

        </div>
        <div class="select-picture-list-right" id="sunday_material_choose_step1_material_div">

        </div>
    </div>
    <div class="button-wrapper">
        <button type="submit" class="com-button" onclick="sunday_material_choose_operate(2)">下一步</button>
    </div>
    <i class="clos" onclick="sunday_material_choose_operate(3)"></i>
</div>
<!--分页-->
<script>
    var param_pageNumber = "$!param.pageNumber";
    var param_pageSize = "$!param.pageSize";
    console.log("param_pageNumber="+param_pageNumber+",param_pageSize="+param_pageSize)
    if(param_pageNumber==null|| param_pageNumber==""){
        param_pageNumber=1
    }
    if(param_pageSize==null|| param_pageSize==""){
        param_pageSize=20
    }
    var sunday_material_upload_pageNumber=param_pageNumber;
    var sunday_material_upload_pageSize=param_pageSize;
    var sunday_material_upload_sort="id";
    var sunday_material_upload_order="desc";
    var sunday_material_upload_is_loading=false;
    var sunday_material_upload_is_end=false;
    var sunday_material_upload_groupId="";

    //输入订单号查询
    function sunday_material_upload_search(isPageNumber){
        sunday_material_upload_groupId = $("#sunday_material_choose_step1_group_div").children(".on").attr("value");
        sunday_material_upload_is_loading=false;
        sunday_material_upload_is_end=false;
        if(isPageNumber){
            sunday_material_upload_pageNumber =1;
        }
        sunday_material_upload_getData(true);
    }
    function sunday_material_upload_search_page(pageNumber,pageSize,maxPageNumber){
        if(maxPageNumber!=null&&maxPageNumber!=""&&typeof(maxPageNumber)!="undefined"){
            if(parseInt(pageNumber)>parseInt(maxPageNumber)){
                return;
            }
        }
        sunday_material_upload_pageNumber =pageNumber;
        sunday_material_upload_pageSize =pageSize==null?sunday_material_upload_pageSize:pageSize;
        sunday_material_upload_getData(true);
    }
    function sunday_material_upload_getData(isRemove){
        var t="#sunday_material_choose_step1_material_div";

        if(sunday_material_upload_is_loading||sunday_material_upload_is_end)return;
        if(isRemove){
            $(t).children().remove();
        }
        sunday_material_upload_is_loading=true;
        //layer.load(1, {shade: [0.5, '#fff']});
        //layer.load(4, {shade: [0.8, '#393D49']})

        //同步请求
        $.ajax({
            type: "POST",
            url: "/sunday/web/material/upload/select",
            data: {
                "pageSize":sunday_material_upload_pageSize,
                "pageNumber":sunday_material_upload_pageNumber,
                "sort":sunday_material_upload_sort,
                "order":sunday_material_upload_order,
                "groupId":sunday_material_upload_groupId,
                "type":3
            },
            dataType: "text",
            async:true,
            success: function(result){
                //d layer.closeAll('loading');
                //返回的是html
                if(result==null||result==""||result.length<1){
                    sunday_material_upload_is_end=true;
                }else{
                    $(t).append(result);
                    sunday_material_upload_is_loading=false;
                }
            }
        });
    }
    sunday_material_upload_search(false);
    // 左侧菜单栏点击
    $('#sunday_material_choose_step1_group_div').children(".menu-item").click(function () {
        //1，选中自己
        $(this).addClass('on');
        //2,反选同胞
        $(this).siblings().removeClass("on");
        //3,显示对应的图片
        sunday_material_upload_search(true);
    })
</script>
<!-- 裁剪封面弹窗 -->
<div class="com-popup select-picture-popup" id="sunday_material_choose_step2" style="z-index:99999">
    <input type="hidden" id="sunday_material_choose_oldMaterialId">
    <div class="title">选择图片</div>
    <div class="title-des">1.从素材库选择封面 一 <i>2.裁剪封面</i></div>
    <div class="con clearfix">
        <div class="select-picture-left">
            <div class="titl">裁剪封面</div>
            <div class="pic-wrapper" >
                <img class="preview-img" id="sunday_materail_choose_old_image" style="height: 244px;background: red;">
            </div>
            <div class="proportion" id="sunday_materail_choose_bili_title1">裁剪比例  （16 : 9）</div>
        </div>
        <div class="select-picture-right">
            <div class="titl">预览封面</div>
            <div class="img-wrapper clearfix">
                <img  class="preview-img" id="sunday_materail_choose_new_image">
                <div class="name" id="sunday_materail_choose_bili_title2">裁剪比例  （16 : 9）</div>
            </div>
        </div>
    </div>
    <div class="button-wrapper">
        <a href="javascript:;" class="com-button return" onclick="sunday_material_choose_operate(1)">上一步</a>
        <button type="button" class="com-button" id="sunday_materail_choose_confirm">确定</button>
    </div>
    <i class="clos" onclick="sunday_material_choose_operate(3)"></i>
</div>
<script>
    function sunday_material_get_groupId(){
        return $(".select-picture-list-left").children(".on").attr("value");
    }


    // 右侧列表选中图片选中
    $('.select-picture-list-right').on('click', '.list-item', function () {
        $(this).addClass('on').siblings().removeClass('on');

       // $("#sunday_materail_choose_old_image").attr('src',choosedImageUrl);

    })

    //操作选择器
    function sunday_material_choose_operate(type){
        //第一步
        if(type == 1){
            $("#sunday_material_choose_step1").addClass("on");
            $("#sunday_material_choose_step2").removeClass("on");
            $("#sunday_material_choose_mask").show();
        //第二步
        }else if(type == 2){
            //1，获取选中的图片
           //var choosedImage =  $(".materialList:visible").find("img").attr("backgroud-image")
            var choosedMaterial = $(".materialList:visible").find(".list-item.on").find(".img")
            var choosedImageUrl = $(choosedMaterial).css("backgroundImage");
            //var choosedImageUrl = $(choosedMaterial).attr("image-url");
            //choosedImageUrl = choosedImageUrl.substr(choosedImageUrl.lastIndexOf("?"));
            var choosedMaterialId =$(choosedMaterial).attr("value");


            if(typeof (choosedImageUrl) == "undefined"){
                layer.msg('请选择图片', {
                    icon: 2
                });
                return;
            }

            choosedImageUrl = choosedImageUrl.split('("')[1].split('")')[0];


            var arr = choosedImageUrl.split("?");
            choosedImageUrl = arr[0]+"?"+arr[2];

          //  $("#sunday_materail_choose_old_image").attr("src",choosedImageUrl);
           sunday_material_choose_init_cropper(choosedImageUrl,choosedMaterialId)
            //setTimeout(test,2000);
            $("#sunday_material_choose_step1").removeClass("on");
            $("#sunday_material_choose_step2").addClass("on");
            $("#sunday_material_choose_mask").show();
            //关闭
        }else if(type == 3){
            $("#sunday_material_choose_step1").remove();
            $("#sunday_material_choose_step2").remove();
            $("#sunday_material_choose_mask").remove();
        }
    }
    sunday_material_choose_operate(1);

      //
    //上传图片
    function sunday_material_save(t) {
        var  name=$(t).val().replace(/^.+?\\([^\\]+?)(\.[^\.\\]*?)?$/gi,"$1")+"."+$(t).val().replace(/.+\./,"");
        var groupId =sunday_material_get_groupId();

        console.log({"name":name,"type":3,"groupId":groupId,});
        //layer.load(4, {shade: [0.8, '#393D49']})
        console.log(t);


        //
        $.ajaxFileUpload({
            //2019年4月26日，替换成oss上传
            url:'/sunday/web/material/insertByFile',
            secureuri:false,
            fileElementId:$(t).attr("id"),//file标签的id
            dataType: 'json',//返回数据的类型
            data:{"name":name,"type":3,"groupId":groupId,},//一同上传的数据
            async: true,
            success: function (data) {
                layer.closeAll('loading');
                if (data.code == 0) {
                    //静态追加
                    var _name = data.result.name;
                    var _source = data.result.source;
                    var div = '<div class="list-item">';
                    div += '<div class="img" style="height: 140px;background-image: url('+_source+'?x-oss-process=image/resize,p_50);">';
                    div += '<div class="icon" style="background-image: url($!adminRoot/yuhua/admin/img/gou.png);"></div>';
                    div += '</div>';
                    div += '<div class="img-name">'+_name+'</div>';
                    div += '</div>';
                    $(".materialList:visible").prepend(div);
                } else {
                    layer.msg('本地上传失败，请重试', {
                        icon: 2
                    });
                }
            }
        });
    }
    //初始化裁剪控件
    function sunday_material_choose_init_cropper(choosedImageUrl,choosedMaterialId) {
        //测试
       // choosedImageUrl="http://xiongwei.oss-cn-hangzhou.aliyuncs.com/0017bc65376cb57dda685ac3593665e45edc87ce.png";
        console.log(choosedImageUrl);
        //销毁控件
        $('#sunday_materail_choose_old_image').cropper('destroy');
        $('#sunday_material_choose_oldMaterialId').val(choosedMaterialId);
        //初始化裁剪参数
        var bili = "$!bili";
        var sunday_upload_image_div = "$!sunday_upload_image_div";
        var sunday_upload_image_input = "$!sunday_upload_image_input";
        var width = "$!width";
        var height = "$!height";
        var callType = "$!callType" == ""?1:"$!callType";
        //初始化比例标题
        var sunday_materail_choose_bili_title = "";
        console.log("bili="+bili)
        if(bili == 1){
            sunday_materail_choose_bili_title = "裁剪比例：16 / 9";
        }else if(bili == 2){
            sunday_materail_choose_bili_title = "裁剪比例：3.75 / 1";
        }else if(bili == 3){
            sunday_materail_choose_bili_title = "裁剪比例：4 / 3";
        }else if(bili == 4){
            sunday_materail_choose_bili_title = "裁剪比例：1 / 1";
        }else if(bili == 5){
            sunday_materail_choose_bili_title = "裁剪比例：2 / 1";
        }else if(bili == 6){
            sunday_materail_choose_bili_title = "裁剪比例：3 / 2";
        } else if(bili == 7){
            sunday_materail_choose_bili_title = "裁剪比例：23 / 10";
        } else if(bili == 8){
            sunday_materail_choose_bili_title = "裁剪比例：23 / 5";
        }
        $("#sunday_materail_choose_bili_title1").text(sunday_materail_choose_bili_title);
        $("#sunday_materail_choose_bili_title2").text(sunday_materail_choose_bili_title);


        //2019年5月9日，图片类型不同，导致转换缓慢。
        var imageType = checkImageType(choosedImageUrl);
        //图片转换成base64.。妈的，在线图片有BUG！！！！！！！！！！！！！！！！！！！！！！！！！！
        //花了1天解决了这个BUG！！！！！！！！！！！！！！！！！！
        //在线图片要转换成base64,不然没有默认的裁剪图
        //2019年5月9日，转换了base64谷歌加载又太慢，脑壳痛
        //2019年5月10日，增加一个时间搓，避免缓存，造成跨域
        choosedImageUrl = choosedImageUrl+"?"+new Date().getTime();
      convertImgToBase64(choosedImageUrl, function(base64Img) {
            //转化后的base64
        $("#sunday_materail_choose_old_image").attr("src",base64Img);

         var oldImage = $("#sunday_materail_choose_old_image");
       /* $("#sunday_materail_choose_old_image").attr("src",choosedImageUrl);*/

        console.log("type类型="+imageType);
            //判断比例
            if (bili == 1) {
                console.log("比例=16 / 9")
                oldImage.cropper({
                    viewMode:1,
                    aspectRatio: 16 / 9,        //1 / 1,  //图片比例,1:1
                    crop: function (data) {
                        var oldImageData = oldImage.cropper('getCroppedCanvas')
                        var dataurl = oldImageData.toDataURL(imageType);
                        $("#sunday_materail_choose_new_image").attr("src", dataurl)
                    },
                    built: function (e) {

                    },
                    //定义自动裁剪面积大小(百分比)和图片进行对比。
                    autoCropArea: 1,
                });
            } else if (bili == 2) {
                console.log("比例=3.75 / 1")
                oldImage.cropper({
                    viewMode:1,
                    aspectRatio: 3.75 / 1,        //1 / 1,  //图片比例,1:1
                    crop: function (data) {
                        var oldImageData = oldImage.cropper('getCroppedCanvas')
                        var dataurl = oldImageData.toDataURL(imageType);
                        $("#sunday_materail_choose_new_image").attr("src", dataurl)
                    },
                    built: function (e) {
                    },
                    //定义自动裁剪面积大小(百分比)和图片进行对比。
                    autoCropArea: 1,
                });
            } else if (bili == 3) {
                console.log("比例=4 / 3")
                oldImage.cropper({
                    viewMode:1,
                    aspectRatio: 4 / 3,        //1 / 1,  //图片比例,1:1
                    crop: function (data) {
                        var oldImageData = oldImage.cropper('getCroppedCanvas')
                        var dataurl = oldImageData.toDataURL(imageType);
                       
                        $("#sunday_materail_choose_new_image").attr("src", dataurl)
                    },
                    built: function (e) {
                    },
                    //定义自动裁剪面积大小(百分比)和图片进行对比。
                    autoCropArea: 1,
                });
            } else if (bili == 4) {
                console.log("比例=1 / 1")
                oldImage.cropper({
                    viewMode:1,
                    aspectRatio: 1 / 1,        //1 / 1,  //图片比例,1:1
                    crop: function (data) {
                        console.log("crop事件")
                        var oldImageData = oldImage.cropper('getCroppedCanvas')
                        var dataurl = oldImageData.toDataURL(imageType);
                        $("#sunday_materail_choose_new_image").attr("src", dataurl)
                    },
                    built: function (e) {
                        console.log("buid事件")
                    },
                    //定义自动裁剪面积大小(百分比)和图片进行对比。
                    autoCropArea: 1,
                });
            } else if (bili == 5) {
                console.log("比例=2 / 1")
                oldImage.cropper({
                    viewMode:1,
                    aspectRatio: 2 / 1,        //1 / 1,  //图片比例,1:1
                    crop: function (data) {
                        var oldImageData = oldImage.cropper('getCroppedCanvas')
                        var dataurl = oldImageData.toDataURL(imageType);
                        $("#sunday_materail_choose_new_image").attr("src", dataurl)
                    },
                    built: function (e) {
                    },
                    //定义自动裁剪面积大小(百分比)和图片进行对比。
                    autoCropArea: 1,
                });
            } else if (bili == 6) {
                console.log("比例=3 / 2")
                oldImage.cropper({
                    viewMode:1,
                    aspectRatio: 3 / 2,        //1 / 1,  //图片比例,1:1
                    crop: function (data) {
                        var oldImageData = oldImage.cropper('getCroppedCanvas')
                        var dataurl = oldImageData.toDataURL(imageType);
                        $("#sunday_materail_choose_new_image").attr("src", dataurl)
                    },
                    built: function (e) {
                    },
                    //定义自动裁剪面积大小(百分比)和图片进行对比。
                    autoCropArea: 1,
                });
            } else if (bili == 7) {
                console.log("比例=23 / 10")
                oldImage.cropper({
                    viewMode:1,
                    aspectRatio: 23 / 10,        //1 / 1,  //图片比例,1:1
                    crop: function (data) {
                        var oldImageData = oldImage.cropper('getCroppedCanvas')
                        var dataurl = oldImageData.toDataURL(imageType);
                        $("#sunday_materail_choose_new_image").attr("src", dataurl)
                    },
                    built: function (e) {
                    },
                    //定义自动裁剪面积大小(百分比)和图片进行对比。
                    autoCropArea: 1,
                });
            } else if(bili == 8){
                console.log("比例=23 / 5")
                oldImage.cropper({
                    viewMode:1,
                    aspectRatio: 23 / 5,        //1 / 1,  //图片比例,1:1
                    crop: function (data) {
                        var oldImageData = oldImage.cropper('getCroppedCanvas')
                        var dataurl = oldImageData.toDataURL(imageType);
                        $("#sunday_materail_choose_new_image").attr("src", dataurl)
                    },
                    built: function (e) {
                    },
                    //定义自动裁剪面积大小(百分比)和图片进行对比。
                    autoCropArea: 1,
                });
            }

            //setTimeout(test,1);
            //最后一步，确认裁剪图片。
            //确认选择图片
            $("#sunday_materail_choose_confirm").unbind();
            $("#sunday_materail_choose_confirm").click(function () {
                console.log("确认裁剪")

                //   var oldImageData=$("#sunday_materail_choose_new_image").attr("src");
                var dataurl = $("#sunday_materail_choose_new_image").attr("src");  //dataurl便是base64图片
                var formData = new FormData();   //这里连带form里的其他参数也一起提交了,如果不需要提交其他参数可以直接FormData无参数的构造函数
                //已当前时间作为文件名称
                // var nameImg = new Date().getTime()+".png";
                //convertBase64UrlToBlob函数是将base64编码转换为Blob
                // formData.append("file",convertBase64UrlToBlob(dataurl),nameImg);  //append函数的第一个参数是后台获取数据的参数名,和html标签的input的name属性功能相同
                formData.append("file", convertBase64UrlToBlob(dataurl,imageType));  //append函数的第一个参数是后台获取数据的参数名,和html标签的input的name属性功能相同
                //  formData.append("oldMaterialId",$("#sunday_material_choose_oldMaterialId").val());
               // formData.append("file", null);  //append函数的第一个参数是后台获取数据的参数名,和html标签的input的name属性功能相同

                //console.info(dataurl)
                // return ;
                //2019年1月29日，使用裁剪过的图片，吧原来的素材库中的替换掉
                layer.load(4, {shade: [0.8, '#393D49']})
                $.ajax({
                    //  url:'/sunday/web/upload/uploadSingle',
                    //2019年4月26日，替换成oss上传
                    url: '/sunday/web/upload/oss/upload/single',
                    type: "POST",
                    data: formData,
                    dataType: "json",
                    processData: false,         // 告诉jQuery不要去处理发送的数据
                    contentType: false,        // 告诉jQuery不要去设置Content-Type请求头
                    async: true,
                    success: function (data) {
                        layer.closeAll('loading');
                        if (data.code != 0) {
                            alert("上传错误，请重试！");
                        } else {
                            //关闭上传框
                            sunday_material_choose_operate(3);
                            // console.log("上传成功");
                            if (data.result.halfPath != null && data.result.halfPath != "") {
                                console.log("回掉方式=" + callType);
                                if (callType == 1) {
                                    var newStyle = "width:" + width + "px;height:" + height + "px;background-image: url(" + data.result.fullPath + ")";
                                    //console.log("上传成功,newStyle="+newStyle);
                                    // console.log($("#"+sunday_upload_image_div));
                                    //    console.log($("#"+sunday_upload_image_input));
                                    $("#" + sunday_upload_image_div).attr("style", newStyle);
                                    $("#" + sunday_upload_image_input).val(data.result.halfPath);
                                } else if (callType == 2) {
                                    var adminRoot = "$!adminRoot";
                                    // var adminRoot = "http://yuhua.pereal.cn";
                                    $("#sunday_opus_image_div").prepend('<div class="img-item">' +
                                            '<img src="' + data.result.fullPath + '" alt="" class="img-item">' +
                                            '<img src="' + adminRoot + '/yuhua/admin/img/del.png" alt="" class="delete" onclick="$(this).parent().remove()" />' +
                                            '<input type="hidden" value="' + data.result.halfPath + '" name="image">');
                                }
                            }
                        }
                    },
                    xhr: function () {            //在jquery函数中直接使用ajax的XMLHttpRequest对象
                        var xhr = new XMLHttpRequest();
                        xhr.upload.addEventListener("progress", function (evt) {
                            if (evt.lengthComputable) {
                                var percentComplete = Math.round(evt.loaded * 100 / evt.total);
                                console.log("正在提交." + percentComplete.toString() + '%');        //在控制台打印上传进度
                            }
                        }, false);
                        return xhr;
                    }
                });
            })
       });
    }
   // 延迟移动裁剪框。解决图片默认不显示被裁剪的
  /*  function test(){
        console.log($("#sunday_materail_choose_old_image").attr("src"));
       $("#sunday_materail_choose_new_image").attr("src",$("#sunday_materail_choose_old_image").attr("src"));
    }*/

    /**
     * 转换图片格式
     * @param urlData
     * @returns {*}
     */
   function convertBase64UrlToBlob(urlData,imageType){
        var bytes=window.atob(urlData.split(',')[1]);        //去掉url的头，并转换为byte
        //处理异常,将ascii码小于0的转换为大于0
        var ab = new ArrayBuffer(bytes.length);
        var ia = new Uint8Array(ab);
        for (var i = 0; i < bytes.length; i++) {
            ia[i] = bytes.charCodeAt(i);
        }
        return new Blob( [ab] , {type : imageType});
    }
    //将blob转换为file
  /*  function blobToFile(theBlob){
        theBlob.lastModifiedDate = new Date();
        return theBlob;
    }
    function  convertBase64UrlToFile(urlData,fileName) {
        return blobToFile(convertBase64UrlToBlob(urlData));
    }*/




</script>

<script>
   /* function(){
       // var url = "http://admin.sunday_yuhua.com/upload/2019/1/75ae1df3-7795-462f-b7fa-684ddf35ad30.png";//这是站内的一张图片资源，采用的相对路径

    }*/
  /*  convertImgToBase64("http://admin.sunday_yuhua.com/upload/2019/1/75ae1df3-7795-462f-b7fa-684ddf35ad30.png", function(base64Img){
        //转化后的base64
        console.log(base64Img);
    });*/

    function checkImageType(imageUrl){
        var imageType = "image/png";
        if(imageUrl.indexOf("jpg")!=-1||imageUrl.indexOf("JPG")!=-1){
            imageType = "image/jpg";
        }
        return imageType;
    }
    //实现将项目的图片转化成base64
    function convertImgToBase64(url, callback, outputFormat){
        console.log("----url----");
        console.log(url);
        var imageType = checkImageType(url)
        var canvas = document.createElement('CANVAS'),
                ctx = canvas.getContext('2d'),
                img = new Image;
        img.crossOrigin = 'Anonymous';
        img.onload = function(){
            canvas.height = img.height;
            canvas.width = img.width;
            ctx.drawImage(img,0,0);
            var dataURL = canvas.toDataURL(outputFormat || imageType);
            console.log('dataUrl');
            console.log(dataURL);
            callback.call(this, dataURL);
            canvas = null;
        };
        img.src = url;
    }


</script>