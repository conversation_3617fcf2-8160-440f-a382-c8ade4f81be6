#parse("/screen/sunday/operate.vm")
<div class="add-plan add-role">
    <form id="sunday_plan_form" class="form-horizontal" action="/sunday/web/plan/save" method="post" enctype="multipart/form-data">
        <input type="hidden" name="id" value="$!{plan.id}">
        <input type="type" name="type"  id="sunday_plan_form_type_input"/>
        <input type="hidden" name="weekTime"  id="sunday_plan_form_weekTime_input"/>
        <input type="hidden" name="weekTimeStr"  id="sunday_plan_form_weekTimeStr_input"/>
        <input type="hidden" name="weekTimeStart"  id="sunday_plan_form_weekTimeStart_input"/>
        <input type="hidden" name="weekTimeEnd"  id="sunday_plan_form_weekTimeEnd_input"/>
        <input type="hidden" name="season" id="sunday_plan_form_season_input">

        <input type="hidden" name="guardIds" id="sunday_plan_form_guardIds_input">
        <input type="hidden" name="bunchIds" id="sunday_plan_form_bunchIds_input">
        <input type="hidden" name="guardAndBunchJson" id="sunday_plan_form_guardAndBunchJson_input">


        <!-- 教学周计划 -->
        <div class="list">
            <div class="com-title-wrapper">
                <div class="title">教学计划</div>
                #if($!plan.id > 0)
                    <button type="button" class="com-button fr" style="margin: 12px 20px 0 0;" onclick="window.open('/sunday/web/plan/pdf?id=$!plan.id','_blank')">导出PDF</button>
                #end

            </div>
            <div class="item clearfix">
                <div class="com-titl"><i>*</i>主题：</div>
                <input type="text"  class="com-input-item fl"  name="name" value="$!{plan.name}" required="required" placeholder="(月主题)——(周主题)">
            </div>

            <div class="item clearfix">
                <div class="com-titl"><i>*</i>时间：</div>
                <div class="com-time-item fl">
                    <div class="selectpicker">
                        <select  id="sunday_plan_form_year" name="year"  required="required"></select>
                    </div>
                </div>
                <div class="com-time-item fl">
                    <div class="selectpicker">
                        <select   id="sunday_plan_form_month" name="month"  required="required"></select>
                    </div>
                </div>
                <div class="com-time-item fl">
                    <div class="selectpicker">
                        <select   id="sunday_plan_form_week" required="required"></select>
                    </div>
                </div>
            </div>
            <div class="item clearfix">
                <div class="com-titl"><i>*</i>季节：</div>
                <div class="com-radio-wrapper fl clearfix" id="sunday_plan_form_season_div">
                    <div class="com-radio-item on" value="春季">春季</div>
                    <div class="com-radio-item" value="夏季">夏季</div>
                    <div class="com-radio-item" value="秋季">秋季</div>
                    <div class="com-radio-item" value="冬季">冬季</div>
                </div>

            </div>
            <div class="item clearfix">
                <div class="com-titl"><i>*</i>填写模式：</div>
                <div class="com-radio-wrapper fl clearfix" id="sunday_plan_form_type_div">
                    <div class="com-radio-item" value="1">奇妙园</div>
                    <div class="com-radio-item" value="2">托育园</div>
                </div>
            </div>
            <div class="item clearfix">
                <div class="com-titl" style="height: 30px;"></div>
                <!--蒙氏【202308改成奇妙园】-->
                <table  border="0" cellspacing="0" cellpadding="0" id="sunday_plan_from_type_1_div" style="table-layout:fixed">
                    <tr>
                        <th colspan="2">一日流程</br>Daily Routine</th>
                        <th width="16%">星期一</br>Monday</th>
                        <th width="16%">星期二</br>Tuesday</th>
                        <th width="16%">星期三</br>Wednesday</th>
                        <th width="16%">星期四</br>Thursday</th>
                        <th width="16%">星期五</br>Friday</th>
                    </tr>
                     #*入园*#
                    <tr #if($!plan.isMry == 0) style="display:none" #end>
                        <td colspan="2">
                        	<!-- <input type="text"  class="com-input-item fl" style="text-align: center"  name="mryName" value="$!plan.mryName"> -->
                            <textarea style="text-align:center;" name="mryName">$!plan.mryName</textarea>
                            <img src="$!adminRoot/yuhua/admin/img/del.png" class="del-btn" >
                            <input type="hidden" name="isMry" value="$!plan.isMry">
                        </td>
                        <td colspan="5"><textarea placeholder="（根据时令及班级情况，对一日环节中各生活活动有计划有重点的实施，可参考《生活活动》-【入园】的活动目标）" maxlength="150" name ="mry" >$!plan.mry</textarea>
                        </td>
                    </tr>
                     #*团讨[启动仪式]*#
                    <tr #if($!plan.isMmsgztt == 0) style="display:none" #end>
                        <td colspan="2">
                            <!-- <input type="text"  class="com-input-item fl" style="text-align: center"  name="mmsgzttName" value="$!plan.mmsgzttName"> -->
                            <textarea style="text-align:center;" name="mmsgzttName">$!plan.mmsgzttName</textarea>
                            <img src="$!adminRoot/yuhua/admin/img/del.png" class="del-btn" >
                            <input type="hidden" name="isMtt" value="$!plan.isMtt">
                        </td>
                        <td colspan="5"><textarea placeholder="（可参考SEL课程教材单元卡中仪式内容）" maxlength="150" name ="mmsgztt" >$!plan.mmsgztt</textarea>
                        </td>
                    </tr>
                     #*体育游戏[SPARK专项运动课程]*#
                    <tr #if($!plan.isMtyyx == 0) style="display:none" #end>
                        <td colspan="2">
                            <!-- <input type="text"  class="com-input-item fl" style="text-align: center"  name="mtyyxName" value="$!plan.mtyyxName"> -->
                            <textarea style="text-align:center;" name="mtyyxName">$!plan.mtyyxName</textarea>
                            <img src="$!adminRoot/yuhua/admin/img/del.png" class="del-btn" >
                            <input type="hidden" name="isMtyyx" value="$!plan.isMtyyx">
                        </td>
                        <td colspan="2"><textarea placeholder="" maxlength="150" name ="mtyyx1" >$!plan.mtyyx1</textarea></td>
                        <td colspan="2"><textarea placeholder="" maxlength="150" name ="mtyyx2" >$!plan.mtyyx2</textarea></td>
                        <td colspan="1"><textarea placeholder="" maxlength="150" name ="mtyyx3" >$!plan.mtyyx3</textarea></td>
                        #*
                        <td colspan="1"><textarea placeholder="" maxlength="150" name ="mtyyx4" >$!plan.mtyyx4</textarea></td>
                        <td colspan="1"><textarea placeholder="" maxlength="150" name ="mtyyx5" >$!plan.mtyyx5</textarea></td>
                        *#
                    </tr>
                     #*新蒙氏工作[蒙氏+课程Montessori+]*#
                    <tr #if($!plan.isMmsgz == 0) style="display:none" #end>
                        <td colspan="2">
                            <!-- <input type="text"  class="com-input-item fl" style="text-align: center"  name="mmsgzName" value="$!plan.mmsgzName"> -->
                            <textarea style="text-align:center;" name="mmsgzName">$!plan.mmsgzName</textarea>
                            <img src="$!adminRoot/yuhua/admin/img/del.png" class="del-btn" >
                            <input type="hidden" name="isMmsgz" value="$!plan.isMmsgz">
                        </td>
                        <td colspan="5"><textarea placeholder="（蒙氏重点工作安排或学习力课程主题册中游戏活动）" maxlength="150" name ="mmsgz" >$!plan.mmsgz</textarea>
                        </td>
                    </tr>
                     #*英语[学习力&SEL课程]*#
                    <tr #if($!plan.isMyy == 0) style="display:none" #end>
                        <td colspan="2">
                            <!-- <input type="text"  class="com-input-item fl" style="text-align: center" name="myyName" value="$!plan.myyName"> -->
                            <textarea style="text-align:center;" name="myyName">$!plan.myyName</textarea>
                            <img src="$!adminRoot/yuhua/admin/img/del.png" class="del-btn" >
                            <input type="hidden" name="isMyy" value="$!plan.isMyy">
                        </td>
                        <td><textarea placeholder="" maxlength="150" name ="myy1" >$!plan.myy1</textarea>
                        <td><textarea placeholder="" maxlength="150" name ="myy2" >$!plan.myy2</textarea>
                        <td><textarea placeholder="" maxlength="150" name ="myy3" >$!plan.myy3</textarea>
                        <td><textarea placeholder="" maxlength="150" name ="myy4" >$!plan.myy4</textarea>
                        <td><textarea placeholder="" maxlength="150" name ="myy5" >$!plan.myy5</textarea>
                        </td>
                    </tr>
                     #*进餐饮水[进餐&饮水Lunchtime]*#
                    <tr #if($!plan.isMjcys == 0) style="display:none" #end>
                        <td colspan="2">
                            <!-- <input type="text"  class="com-input-item fl" style="text-align: center"  name="mjcysName" value="$!plan.mjcysName"> -->
                            <textarea style="text-align:center;" name="mjcysName">$!plan.mjcysName</textarea>
                            <img src="$!adminRoot/yuhua/admin/img/del.png" class="del-btn" >
                            <input type="hidden" name="isMjcys" value="$!plan.isMjcys">
                        </td>
                        <td colspan="5"><textarea placeholder="（根据时令及班级情况，对一日环节中各生活活动有计划有重点的实施，可参考《生活活动》-【进餐饮水】的活动目标）" maxlength="150" name ="mjcys" >$!plan.mjcys</textarea>
                        </td>
                    </tr>
                    #*午睡[午睡&午睡仪式Nap time]*#
                    <tr #if($!plan.isMws == 0) style="display:none" #end>
                        <td colspan="2">
                            <!-- <input type="text"  class="com-input-item fl" style="text-align: center"  name="mwsName" value="$!plan.mwsName"> -->
                            <textarea style="text-align:center;" name="mwsName">$!plan.mwsName</textarea>
                            <img src="$!adminRoot/yuhua/admin/img/del.png" class="del-btn" >
                            <input type="hidden" name="isMws" value="$!plan.isMws">
                        </td>
                        <td colspan="5"><textarea placeholder="（根据时令及班级情况，对一日环节中各生活活动有计划有重点的实施，可参考《生活活动》-【午睡】的活动目标）" maxlength="150"  name ="mws">$!plan.mws</textarea></td>
                    </tr>
                    #*家园共育[双语课程English Program]*#
                    <tr #if($!plan.isMjygy == 0) style="display:none" #end>
                        <td colspan="2">
                            <!-- <input type="text"  class="com-input-item fl" style="text-align: center"  name="mjygyName" value="$!plan.mjygyName"> -->
                            <textarea style="text-align:center;" name="mjygyName">$!plan.mjygyName</textarea>
                            <img src="$!adminRoot/yuhua/admin/img/del.png" class="del-btn" >
                            <input type="hidden" name="isMjygy" value="$!plan.isMjygy"></td>
                        <td colspan="5">
                        <!-- <textarea placeholder="（可参考《主题册》相应主题中的【家园共育】，请标明活区活动名称及目标）" maxlength="150"  name ="mjygy">$!plan.mjygy</textarea> -->
                        <textarea maxlength="150" name ="mjygy">$!plan.mjygy</textarea>
                        </td>
                    </tr>
                     #*学习活动[SPARK拓展运动]*#
                    <tr #if($!plan.isMxxhd == 0) style="display:none" #end>
                        <td colspan="2" rowspan="1">
                            <!-- <input type="text"  class="com-input-item fl" style="text-align: center"  name="mxxhdName" value="$!plan.mxxhdName"> -->
                            <textarea style="text-align:center;" name="mxxhdName">$!plan.mxxhdName</textarea>
                            <img src="$!adminRoot/yuhua/admin/img/del.png" class="del-btn" >
                            <input type="hidden" name="isMxxhd" value="$!plan.isMxxhd">
                        </td>
                        <td colspan="2"><textarea placeholder="" maxlength="150" name ="mxxhd1" >$!plan.mxxhd1</textarea>
                        <td colspan="2"><textarea placeholder="" maxlength="150" name ="mxxhd2" >$!plan.mxxhd2</textarea>
                        <td colspan="1"><textarea placeholder="" maxlength="150" name ="mxxhd3" >$!plan.mxxhd3</textarea>
                        #*
                        <td colspan="1"><textarea placeholder="" maxlength="150" name ="mxxhd4" >$!plan.mxxhd4</textarea>
                        <td colspan="1"><textarea placeholder="" maxlength="150" name ="mxxhd5" >$!plan.mxxhd5</textarea>
                        *#
                        </td>
                    </tr>

                #*大肌肉运动*#
                #*     <tr #if($!plan.isMdjryd1 == 0) style="display:none" #end>
                        <td rowspan="2" colspan="2">
                            <!-- <input type="text"  class="com-input-item fl" style="text-align: center"  name="mdjrydName1" value="$!plan.mdjrydName1"> -->
                            <textarea style="text-align:center;" name="mdjrydName1">$!plan.mdjrydName1</textarea>
                            <img src="$!adminRoot/yuhua/admin/img/del.png" class="del-btn next" >
                            <input type="hidden" name="isMdjryd1" value="$!plan.isMdjryd1">
                        </td>
                        <td colspan="5"><textarea placeholder="最多150字符" maxlength="150"  name ="mdjryd1">$!plan.mdjryd1</textarea></td>
                    </tr>
                    <tr #if($!plan.isMdjryd1 == 0) style="display:none" #end>
                        <td colspan="5" rowspan="1" ><textarea placeholder="最多150字符" maxlength="150" name ="mdjryd2">$!plan.mdjryd2</textarea></td>
                    </tr>
                    
                *#
                #*旧蒙氏工作*#
                #*
                    <tr #if($!plan.isMmsgz == 0) style="display:none" #end>
                        <td rowspan="4"  >
                            <!-- <input type="text"  class="com-input-item fl" style="text-align: center;width: 120px"  name="mmsgzName" value="$!plan.mmsgzName"> -->
                            <textarea style="text-align:center;" name="mmsgzName">$!plan.mmsgzName</textarea>
                            <img src="$!adminRoot/yuhua/admin/img/del.png" class="del-btn three" >
                            <input type="hidden" name="isMmsgz" value="$!plan.isMmsgz">
                        </td>
                        <td >
                            <input type="text"  class="com-input-item fl" name="mmsgzzdName" value="$!plan.mmsgzzdName"style="text-align: center;width: 120px" ></td>
                        </td>
                        <td colspan="5" ><textarea placeholder="猛士-早点" maxlength="150"  name ="mmsgzzd">$!plan.mmsgzzd</textarea></td>
                    </tr>
                    <tr #if($!plan.isMmsgz == 0) style="display:none" #end>
                        <td>
                            <!-- <input type="text"  class="com-input-item fl" name="mmsgzttName" value="$!plan.mmsgzttName" style="text-align: center;width: 120px" > -->
                            <textarea style="text-align:center;" name="mmsgzttName">$!plan.mmsgzttName</textarea>
                       	</td>
                        <td colspan="5"><textarea placeholder="猛士-团套" maxlength="150"  name ="mmsgztt">$!plan.mmsgztt</textarea></td>
                    </tr>
                    <tr #if($!plan.isMmsgz == 0) style="display:none" #end>
                        <td>
                            <!-- <input type="text"  class="com-input-item fl" name="mmsgzgzName" value="$!plan.mmsgzgzName" style="text-align: center;width: 120px" > -->
                            <textarea style="text-align:center;" name="mmsgzgzName">$!plan.mmsgzgzName</textarea>
                        </td>
                        </td>
                        <td colspan="5"><textarea placeholder="猛士-工作" maxlength="150"  name ="mmsgzgz">$!plan.mmsgzgz</textarea></td>
                    </tr>
                    <tr #if($!plan.isMmsgz == 0) style="display:none" #end>
                        <td colspan="6"><textarea placeholder="最多150个字符" maxlength="150"  name ="mmsgz">$!plan.mmsgz</textarea></td>
                    </tr>
                    *#
                    #*外教*#
                    #*
                    <tr  #if($!plan.isMwj == 0) style="display:none" #end>
                        <td colspan="2">
                            <!-- <input type="text"  class="com-input-item fl" style="text-align: center"  name="mwjName" value="$!plan.mwjName"> -->
                            <textarea style="text-align:center;" name="mwjName">$!plan.mwjName</textarea>
                            <img src="$!adminRoot/yuhua/admin/img/del.png" class="del-btn" >
                            <input type="hidden" name="isMwj" value="$!plan.isMwj">
                        </td>
                        <td><textarea placeholder="最多150个字符" maxlength="150"  name ="mwj1">$!plan.mwj1</textarea></td>
                        <td><textarea placeholder="最多150个字符" maxlength="150"  name ="mwj2">$!plan.mwj2</textarea></td>
                        <td><textarea placeholder="最多150个字符" maxlength="150"  name ="mwj3">$!plan.mwj3</textarea></td>
                        <td><textarea placeholder="最多150个字符" maxlength="150"  name ="mwj4">$!plan.mwj4</textarea></td>
                        <td><textarea placeholder="最多150个字符" maxlength="150"  name ="mwj5">$!plan.mwj5</textarea></td>
                    </tr>
                    *#
                    #*午餐*#
                   #*
                    <tr #if($!plan.isMwc == 0) style="display:none" #end>
                        <td colspan="2">
                            <!-- <input type="text"  class="com-input-item fl" style="text-align: center"  name="mwcName" value="$!plan.mwcName"> -->
                            <textarea style="text-align:center;" name="mwcName">$!plan.mwcName</textarea>
                            <img src="$!adminRoot/yuhua/admin/img/del.png" class="del-btn" >
                            <input type="hidden" name="isMwc" value="$!plan.isMwc">
                        </td>
                        <td colspan="5"><textarea placeholder="最多150个字符" maxlength="150"  name ="mwc">$!plan.mwc</textarea></td>
                    </tr>
                    *#
                    #*睡前准备*#
                    #*
                    <tr #if($!plan.isMsqzb == 0) style="display:none" #end>
                        <td colspan="2">
                            <!-- <input type="text"  class="com-input-item fl" style="text-align: center"  name="msqzbName" value="$!plan.msqzbName"> -->
                            <textarea style="text-align:center;" name="msqzbName">$!plan.msqzbName</textarea>
                            <img src="$!adminRoot/yuhua/admin/img/del.png" class="del-btn" >
                            <input type="hidden" name="isMsqzb" value="$!plan.isMsqzb">
                        </td>
                        <td colspan="5"><textarea placeholder="最多150个字符" maxlength="150"  name ="msqzb">$!plan.msqzb</textarea></td>
                    </tr>
                    *#
                 
                    #*起床午点*#
                    #*
                    <tr #if($!plan.isMqcwd == 0) style="display:none" #end>
                        <td colspan="2">
                            <!-- <input type="text"  class="com-input-item fl" style="text-align: center"  name="mqcwdName" value="$!plan.mqcwdName"> -->
                            <textarea style="text-align:center;" name="mqcwdName">$!plan.mqcwdName</textarea>
                            <img src="$!adminRoot/yuhua/admin/img/del.png" class="del-btn" >
                            <input type="hidden" name="isMqcwd" value="$!plan.isMqcwd">
                        </td>
                        <td colspan="5"><textarea placeholder="最多150个字符" maxlength="150"  name ="mqcwd">$!plan.mqcwd</textarea></td>
                    </tr>
                    *#
                    #*大肌肉运动*#
                    #*
                    <tr #if($!plan.isMdjryd2 == 0) style="display:none" #end>
                        <td colspan="2" rowspan="2" >
                            <!-- <input type="text"  class="com-input-item fl" style="text-align: center"  name="mdjrydName2" value="$!plan.mdjrydName2"> -->
                            <textarea style="text-align:center;" name="mdjrydName2">$!plan.mdjrydName2</textarea>
                            <img src="$!adminRoot/yuhua/admin/img/del.png" class="del-btn next" >
                            <input type="hidden" name="isMdjryd2" value="$!plan.isMdjryd2">
                        </td>
                        <td colspan="5"><textarea placeholder="最多150个字符" maxlength="150"  name ="mdjryd3">$!plan.mdjryd3</textarea></td>
                    </tr>
                    <tr #if($!plan.isMdjryd2 == 0) style="display:none" #end>
                        <td colspan="5"><textarea placeholder="最多150个字符" maxlength="150"  name ="mdjryd4">$!plan.mdjryd4</textarea></td>
                    </tr>
                    *#
                    #*领域强化活动*#
                    #*
                    <tr #if($!plan.isMqhlyhd == 0) style="display:none" #end>
                        <td colspan="2">
                            <!-- <input type="text"  class="com-input-item fl" style="text-align: center"  name="mqhlyhdName" value="$!plan.mqhlyhdName"> -->
                            <textarea style="text-align:center;" name="mqhlyhdName">$!plan.mqhlyhdName</textarea>
                            <img src="$!adminRoot/yuhua/admin/img/del.png" class="del-btn" >
                            <input type="hidden" name="isMqhlyhd" value="$!plan.isMqhlyhd">
                        </td>
                        <td><textarea placeholder="最多150个字符" maxlength="150"  name ="mqhlyhd1">$!plan.mqhlyhd1</textarea></td>
                        <td><textarea placeholder="最多150个字符" maxlength="150"  name ="mqhlyhd2">$!plan.mqhlyhd2</textarea></td>
                        <td><textarea placeholder="最多150个字符" maxlength="150"  name ="mqhlyhd3">$!plan.mqhlyhd3</textarea></td>
                        <td><textarea placeholder="最多150个字符" maxlength="150"  name ="mqhlyhd4">$!plan.mqhlyhd4</textarea></td>
                        <td><textarea placeholder="最多150个字符" maxlength="150"  name ="mqhlyhd5">$!plan.mqhlyhd5</textarea></td>
                    </tr>
                    *#
                    #*离园*#
                    <tr #if($!plan.isMly == 0) style="display:none" #end>
                        <td colspan="2">
                            <!-- <input type="text"  class="com-input-item fl" style="text-align: center"  name="mlyName" value="$!plan.mlyName"> -->
                            <textarea style="text-align:center;" name="mlyName">$!plan.mlyName</textarea>
                            <img src="$!adminRoot/yuhua/admin/img/del.png" class="del-btn" >
                            <input type="hidden" name="isMly" value="$!plan.isMly"></td>
                        <td colspan="5"><textarea placeholder="根据时令及班级情况，对一日环节中各生活活动有计划有重点的实施，可参考《生活活动》-【离园】的活动目标）" maxlength="150"  name ="mly">$!plan.mly</textarea></td>
                    </tr>

                </table>
                <!-- 非蒙氏 【202308改成托育园】-->
                <table border="0" cellspacing="0" cellpadding="0" class="fl"  id="sunday_plan_from_type_2_div" style="table-layout:fixed">
                    <tr>
                        <th>一日流程</th>
                        <th width="16.7%">星期一</th>
                        <th width="16.7%">星期二</th>
                        <th width="16.7%">星期三</th>
                        <th width="16.7%">星期四</th>
                        <th width="16.7%">星期五</th>
                    </tr>
                    #*入园*#
                    <tr #if($!plan.isRy == 0) style="display:none" #end>
                        <td>
                            <!-- <input type="text"  class="com-input-item fl" style="text-align: center"  name="ryName" value="$!plan.ryName"> -->
                            <textarea style="text-align:center;" name="ryName">$!plan.ryName</textarea>
                            <img src="$!adminRoot/yuhua/admin/img/del.png" class="del-btn" >
                            <input type="hidden" name="isRy" value="$!plan.isRy"></td>
                        <td colspan="5"><textarea placeholder="（根据时令及班级情况，对一日环节中各生活活动有计划有重点的实施，可参考《生活活动》-【入园】的活动目标）" maxlength="150" name ="ry" >$!plan.ry</textarea></td>
                    </tr>
                    #*体育游戏[SPARK专项运动]*#
                    <tr #if($!plan.isTyyx == 0) style="display:none" #end>
                        <td >
                            <!-- <input type="text"  class="com-input-item fl" style="text-align: center"  name="tyyxName" value="$!plan.tyyxName"> -->
                            <textarea style="text-align:center;" name="tyyxName">$!plan.tyyxName</textarea>
                            <img src="$!adminRoot/yuhua/admin/img/del.png" class="del-btn" >
                            <input type="hidden" name="isTyyx" value="$!plan.isTyyx">
                        </td>
                        <td colspan="2"><textarea placeholder="" maxlength="150" name ="tyyx1" >$!plan.tyyx1</textarea></td>
                        <td colspan="2"><textarea placeholder="" maxlength="150" name ="tyyx2" >$!plan.tyyx2</textarea></td>
                        <td colspan="1"><textarea placeholder="" maxlength="150" name ="tyyx3" >$!plan.tyyx3</textarea></td>
                        #*
                        <td colspan="1"><textarea placeholder="" maxlength="150" name ="tyyx4" >$!plan.tyyx4</textarea></td>
                        <td colspan="1"><textarea placeholder="" maxlength="150" name ="tyyx5" >$!plan.tyyx5</textarea></td>
                    	*#
                    </tr>
                     #*学习活动*#
                    <tr #if($!plan.isXxhd == 0) style="display:none" #end>
                        <td>
                            <!-- <input type="text"  class="com-input-item fl" style="text-align: center"  name="xxhdName" value="$!plan.xxhdName"> -->
                            <textarea style="text-align:center;" name="xxhdName">$!plan.xxhdName</textarea>
                            <img src="$!adminRoot/yuhua/admin/img/del.png" class="del-btn" >
                            <input type="hidden" name="isXxhd" value="$!plan.isXxhd">
                        </td>
                        <td><textarea placeholder="" maxlength="150" name ="xxhd1" >$!plan.xxhd1</textarea>
                        <td><textarea placeholder="" maxlength="150" name ="xxhd2" >$!plan.xxhd2</textarea>
                        <td><textarea placeholder="" maxlength="150" name ="xxhd3" >$!plan.xxhd3</textarea>
                        <td><textarea placeholder="" maxlength="150" name ="xxhd4" >$!plan.xxhd4</textarea>
                        <td><textarea placeholder="" maxlength="150" name ="xxhd5" >$!plan.xxhd5</textarea>
                        </td>
                    </tr>
                    #*大肌肉运动*#
                    #*
                    <tr #if($!plan.isDjryd == 0) style="display:none" #end>
                        <td rowspan="2">
                            <!-- <input type="text"  class="com-input-item fl"   name="djrydName" value="$!plan.djrydName" style="text-align: center"> -->
                            <textarea style="text-align:center;" name="djrydName">$!plan.djrydName</textarea>
                            <img src="$!adminRoot/yuhua/admin/img/del.png" class="del-btn next" >
                            <input type="hidden" name="isDjryd" value="$!plan.isDjryd"></td>
                        <td colspan="5"><textarea placeholder="最多150个字符" maxlength="150" name ="djryd1">$!plan.djryd1</textarea></td>
                    </tr>
                    <tr #if($!plan.isDjryd == 0) style="display:none" #end>
                        <td colspan="5" rowspan="1"><textarea placeholder="最多150个字符" maxlength="150"  name ="djryd2">$!plan.djryd2</textarea></td>
                    </tr>
                    *#
                    #*早点*#
                    #*
                    <tr  #if($!plan.isZd == 0) style="display:none" #end>
                        <td>
                            <!-- <input type="text"  class="com-input-item fl"   name="zdName" value="$!plan.zdName" style="text-align: center"> -->
                            <textarea style="text-align:center;" name="zdName">$!plan.zdName</textarea>
                            <img src="$!adminRoot/yuhua/admin/img/del.png" class="del-btn" >
                            <input type="hidden" name="isZd" value="$!plan.isZd">
                        </td>
                        <td colspan="5" ><textarea placeholder="最多150个字符" maxlength="150" name ="zd">$!plan.zd</textarea></td>
                    </tr>
                    *#
                    #*启动仪式*#
                    <tr #if($!plan.isQdys == 0) style="display:none" #end>
                        <td>
                            <!-- <input type="text"  class="com-input-item fl"   name="qdysName" value="$!plan.qdysName" style="text-align: center"> -->
                            <textarea style="text-align:center;" name="qdysName">$!plan.qdysName</textarea>
                            <img src="$!adminRoot/yuhua/admin/img/del.png" class="del-btn" >
                            <input type="hidden" name="isQdys" value="$!plan.isQdys"></td>
                        <td colspan="5"><textarea placeholder="（可选用《生活活动》-【启动仪式】中的活动）" maxlength="150"  name ="qdys">$!plan.qdys</textarea></td>
                    </tr>
                    #*外教*#
                    #*
                    <tr #if($!plan.isWj == 0) style="display:none" #end>
                        <td rowspan="2" >
                            <!-- <input type="text"  class="com-input-item fl"   name="wjName" value="$!plan.wjName" style="text-align: center"> -->
                            <textarea style="text-align:center;" name="wjName">$!plan.wjName</textarea>
                            <img src="$!adminRoot/yuhua/admin/img/del.png" class="del-btn next" >
                            <input type="hidden" name="isWj" value="$!plan.isWj"></td>
                        <td><textarea placeholder="最多150个字符" maxlength="150" name ="wj1">$!plan.wj1</textarea></td>
                        <td><textarea placeholder="最多150个字符" maxlength="150" name ="wj2">$!plan.wj2</textarea></td>
                        <td><textarea placeholder="最多150个字符" maxlength="150" name ="wj3">$!plan.wj3</textarea></td>
                        <td><textarea placeholder="最多150个字符" maxlength="150" name ="wj4">$!plan.wj4</textarea></td>
                        <td><textarea placeholder="最多150个字符" maxlength="150" name ="wj5">$!plan.wj5</textarea></td>
                    </tr>
                    <tr #if($!plan.isWj == 0) style="display:none" #end>
                        <td colspan="5"><textarea placeholder="最多150个字符" maxlength="150" name ="wj">$!plan.wj</textarea></td>
                    </tr>
                    *#
                    #*学习中心*#
                    #*
                    <tr #if($!plan.isXxzx == 0) style="display:none" #end>
                        <td>
                            <!-- <input type="text"  class="com-input-item fl"   name="xxzxName" value="$!plan.xxzxName" style="text-align: center"> -->
                            <textarea style="text-align:center;" name="xxzxName">$!plan.xxzxName</textarea>
                            <img src="$!adminRoot/yuhua/admin/img/del.png" class="del-btn" >
                            <input type="hidden" name="isXxzx" value="$!plan.isXxzx"></td>
                        <td colspan="5"><textarea placeholder="最多150个字符" maxlength="150" name ="xxzx">$!plan.xxzx</textarea></td>
                    </tr>
                    *#
                    #*午餐及餐后活动*#
                    #*
                    <tr #if($!plan.isWcjchzyhd == 0) style="display:none" #end>
                        <td>
                            <!-- <input type="text"  class="com-input-item fl"   name="wcjchzyhdName" value="$!plan.wcjchzyhdName" style="text-align: center"> -->
                            <textarea style="text-align:center;" name="wcjchzyhdName">$!plan.wcjchzyhdName</textarea>
                            <img src="$!adminRoot/yuhua/admin/img/del.png" class="del-btn" >
                            <input type="hidden" name="isWcjchzyhd" value="$!plan.isWcjchzyhd"></td>
                        <td colspan="5"><textarea placeholder="最多150个字符" maxlength="150" name ="wcjchzyhd">$!plan.wcjchzyhd</textarea></td>
                    </tr>
                    *#
                    #*游戏活动*#
                    <tr #if($!plan.isYxhd == 0) style="display:none" #end>
                        <td>
                            <!-- <input type="text"  class="com-input-item fl" style="text-align: center"  name="yxhdName" value="$!plan.yxhdName"> -->
                            <textarea style="text-align:center;" name="yxhdName">$!plan.yxhdName</textarea>
                            <img src="$!adminRoot/yuhua/admin/img/del.png" class="del-btn" >
                            <input type="hidden" name="isYxhd" value="$!plan.isYxhd">
                        </td>
                        <td colspan="5"><textarea placeholder="（可参考《主题册》相应主题中的【游戏活动】，请标明区域名称及活动名称）" maxlength="150" name ="yxhd">$!plan.yxhd</textarea>
                        </td>
                    </tr>
                    #*进餐饮水*#
                    <tr #if($!plan.isJcys == 0) style="display:none" #end>
                        <td>
                            <!-- <input type="text"  class="com-input-item fl" style="text-align: center"  name="jcysName" value="$!plan.jcysName"> -->
                            <textarea style="text-align:center;" name="jcysName">$!plan.jcysName</textarea>
                            <img src="$!adminRoot/yuhua/admin/img/del.png" class="del-btn" >
                            <input type="hidden" name="isJcys" value="$!plan.isJcys">
                        </td>
                        <td colspan="5"><textarea placeholder="（根据时令及班级情况，对一日环节中各生活活动有计划有重点的实施，可参考《生活活动》-【进餐饮水】的活动目标）" maxlength="150" name ="jcys" >$!plan.jcys</textarea>
                        </td>
                    </tr>
                    #*午睡*#
                    <tr  #if($!plan.isWs == 0) style="display:none" #end>
                        <td>
                            <!-- <input type="text"  class="com-input-item fl" name="wsName" value="$!plan.wsName" style="text-align: center"> -->
                            <textarea style="text-align:center;" name="wsName">$!plan.wsName</textarea>
                            <img src="$!adminRoot/yuhua/admin/img/del.png" class="del-btn" >
                            <input type="hidden" name="isWs" value="$!plan.isWs"></td>
                        <td colspan="5"><textarea placeholder="（根据时令及班级情况，对一日环节中各生活活动有计划有重点的实施，可参考《生活活动》-【午睡】的活动目标）" maxlength="150" name ="ws">$!plan.ws</textarea></td>
                    </tr>
                    #*午点*#
                    #*
                    <tr #if($!plan.isWd == 0) style="display:none" #end>
                        <td>
                            <!-- <input type="text"  class="com-input-item fl" name="wdName" value="$!plan.wdName" style="text-align: center"> -->
                            <textarea style="text-align:center;" name="wdName">$!plan.wdName</textarea>
                            <img src="$!adminRoot/yuhua/admin/img/del.png" class="del-btn" >
                            <input type="hidden" name="isWd" value="$!plan.isWd"></td>
                        <td colspan="5"><textarea placeholder="最多150个字符" maxlength="150" name ="wd">$!plan.wd</textarea></td>
                    </tr>
                    *#
                    #*领域强化活动*#
                    #*
                    <tr #if($!plan.isLyqhhd == 0) style="display:none" #end>
                        <td rowspan="2">
                            <!-- <input type="text"  class="com-input-item fl" name="lyqhhdName" value="$!plan.lyqhhdName" style="text-align: center"> -->
                            <textarea style="text-align:center;" name="lyqhhdName">$!plan.lyqhhdName</textarea>
                            <img src="$!adminRoot/yuhua/admin/img/del.png" class="del-btn next" >
                            <input type="hidden" name="isLyqhhd" value="$!plan.isLyqhhd"></td>
                        <td><textarea placeholder="最多150个字符" maxlength="150" name ="lyqhhd1">$!plan.lyqhhd1</textarea></td>
                        <td><textarea placeholder="最多150个字符" maxlength="150" name ="lyqhhd2">$!plan.lyqhhd2</textarea></td>
                        <td><textarea placeholder="最多150个字符" maxlength="150" name ="lyqhhd3">$!plan.lyqhhd3</textarea></td>
                        <td><textarea placeholder="最多150个字符" maxlength="150" name ="lyqhhd4">$!plan.lyqhhd4</textarea></td>
                        <td><textarea placeholder="最多150个字符" maxlength="150" name ="lyqhhd5">$!plan.lyqhhd5</textarea></td>
                    </tr>
                    <tr #if($!plan.isLyqhhd == 0) style="display:none" #end>
                        <td colspan="5"><textarea placeholder="最多150个字符" maxlength="150"  name ="lyqhhd">$!plan.lyqhhd</textarea></td>
                    </tr>
                    *#
                     #*英语*#
                    <tr #if($!plan.isYy == 0) style="display:none" #end>
                        <td >
                            <!-- <input type="text"  class="com-input-item fl" style="text-align: center"  name="yyName" value="$!plan.yyName"> -->
                            <textarea style="text-align:center;" name="yyName">$!plan.yyName</textarea>
                            <img src="$!adminRoot/yuhua/admin/img/del.png" class="del-btn" >
                            <input type="hidden" name="isYy" value="$!plan.isYy">
                        </td>
                        <td><textarea placeholder="" maxlength="150" name ="yy1" >$!plan.yy1</textarea>
                        <td><textarea placeholder="" maxlength="150" name ="yy2" >$!plan.yy2</textarea>
                        <td><textarea placeholder="" maxlength="150" name ="yy3" >$!plan.yy3</textarea>
                        <td><textarea placeholder="" maxlength="150" name ="yy4" >$!plan.yy4</textarea>
                        <td><textarea placeholder="" maxlength="150" name ="yy5" >$!plan.yy5</textarea>
                        </td>
                    </tr>
                     #*家园共育[SPARK拓展运动]*#
                    <tr #if($!plan.isJygy == 0) style="display:none" #end>
                        <td>
                            <!-- <input type="text"  class="com-input-item fl" style="text-align: center"  name="jygyName" value="$!plan.jygyName"> -->
                            <textarea style="text-align:center;" name="jygyName">$!plan.jygyName</textarea>
                            <img src="$!adminRoot/yuhua/admin/img/del.png" class="del-btn" >
                            <input type="hidden" name="isJygy" value="$!plan.isJygy"></td>
                        <td colspan="2"><textarea placeholder="" maxlength="150" name ="jygy1" >$!plan.jygy1</textarea></td>
                        <td colspan="2"><textarea placeholder="" maxlength="150" name ="jygy2" >$!plan.jygy2</textarea></td>
                        <td colspan="1"><textarea placeholder="" maxlength="150" name ="jygy3" >$!plan.jygy3</textarea></td>
                        #*
                        <td colspan="5">
                        <textarea maxlength="150"  name ="jygy">$!plan.jygy</textarea>
                        </td>
                        *#
                    </tr>
                    #*离园*#
                    <tr #if($!plan.isLy == 0) style="display:none" #end>
                        <td>
                            <!-- <input type="text"  class="com-input-item fl" name="lyName" value="$!plan.lyName" style="text-align: center"> -->
                            <textarea style="text-align:center;" name="lyName">$!plan.lyName</textarea>
                            <img src="$!adminRoot/yuhua/admin/img/del.png" class="del-btn" >
                            <input type="hidden" name="isLy" value="$!plan.isLy"></td></td>
                        <td colspan="5" ><textarea placeholder="根据时令及班级情况，对一日环节中各生活活动有计划有重点的实施，可参考《生活活动》-【离园】的活动目标）" maxlength="150" name ="ly">$!plan.ly</textarea></td>
                    </tr>
                </table>
            </div>

        </div>

        <!--园区信息-->
        <div class="search-wrapper clearfix" style="margin-top: 20px">
            <div class="list">
                <div class="com-title-wrapper">
                    <div class="title">关联园区</div>
                </div>
                <div class="item clearfix">

                    <table border="0" cellspacing="0" cellpadding="0" class="fl" id="sunday_plan_guard_and_bunch_table" style="margin-left: 100px">
                        <tr>
                            <th width="20%">园区</th>
                            <th>班级</th>
                        </tr>
                        #foreach($!guard in $!plan.guards)
                            <tr>
                                <td><div class="com-checkbox-item2 firstMenu#if($!guard.isCheck == 1) on #end" value="$!guard.id">$!guard.name</div></td>
                                <td>
                                    #foreach($!bunch in $!guard.bunches)
                                        <div class="com-checkbox-item2 secondMenu#if($!bunch.isCheck == 1) on #end" value="$!bunch.id"> $!bunch.name</div>
                                    #end
                                </td>
                            </tr>
                        #end
                    </table>
                </div>
                <div class="item clearfix">
                    <div class="com-titl" style="height: 30px;"></div>
                    <div class="button-wrapper fl">
                        <a href="javascript:yuhua_return('/sunday/web/plan/index?isClean=0');" class="com-button return">返回</a>
                        <button type="submit" class="com-button sunday_plan_add sunday_plan_edit">保存</button>
                    </div>
                </div>
            </div>
            <script>
                //一级菜单勾选
                $(".firstMenu").click(function () {
                    //勾选
                    if(!$(this).hasClass("on")){
                        //1,勾选一级菜单
                        $(this).addClass("on");
                        //2，勾选全部二级菜单
                        $(this).parent().next().find(".secondMenu").addClass("on");
                        //反选
                    }else{
                        //1,反选一级菜单
                        $(this).removeClass("on");
                        //2，反选全部二级菜单
                        $(this).parent().next().find(".secondMenu").removeClass("on");
                    }
                })
                $(".secondMenu").click(function () {
                    //勾选
                    if(!$(this).hasClass("on")){
                        //1,勾选二级级菜单
                        $(this).addClass("on");
                        //2，勾选一级菜单
                        $(this).parent().prev().find(".firstMenu").addClass("on");
                        //反选
                    }else{
                        //1,反选二级级菜单
                        $(this).removeClass("on");
                        //2,判断是否还没有二级菜单被勾选
                        var isAllNot=true;
                        $(this).siblings().each(function () {
                            if($(this).hasClass("on")){
                                isAllNot=false;
                                return false;
                            }
                        });
                        if(isAllNot){
                            //2，反选一级菜单
                            $(this).parent().prev().find(".firstMenu").removeClass("on");
                        }
                    }
                })

            </script>
        </div>
    </form>
</div>


<script>
    //2019年2月20日。texarea输入超限制提示
    //1,增加字符数量
     $("textarea").after('<i class="word-num">0/150</i>');
     //2,计算字符
    $('textarea').bind('input propertychange', function(){
        var value = $(this).val();
        // 将换行符不计算为单词数
       // value = value.replace(/\n|\r/gi,"");
        var length = value.length;
        $(this).next(".word-num").text(length+"/"+150)
    });
    $('textarea').bind('input change', function(){
        var value = $(this).val();
        // 将换行符不计算为单词数
        // value = value.replace(/\n|\r/gi,"");
        var length = value.length;
        $(this).next(".word-num").text(length+"/"+150)
    });
    $('textarea').change();

    $(".del-btn").click(function () {
        var thisObject = $(this);
        var msg = "确认删除该栏目?";
        layer.confirm(msg, function (index) {
            var isRemoveNext = $(thisObject).hasClass("next");
            var isThreeNext = $(thisObject).hasClass("three");
            $(thisObject).parents("tr").hide();
            if(isRemoveNext){
                $(thisObject).parents("tr").next().hide();
            }
            if(isThreeNext){
                $(thisObject).parents("tr").next().hide();
                $(thisObject).parents("tr").next().next().hide();
                $(thisObject).parents("tr").next().next().next().hide();
            }
            $(thisObject).parent("td").find("input").val(0);
            //最后手动关闭
            layer.close(index);

        });

    })



    var dictionary_url ="$!dictionaryRoot";

   // intAreaNoTown("sunday_plan_form_provinceId","sunday_plan_form_cityId","sunday_plan_form_districtId",null,null,null);
    //initGuardAndBunchCheckbox("sunday_plan_form_guardId","sunday_plan_form_bunchId_div","$!plan.guardId","$!plan.bunchIds","sunday_plan_form_provinceId","sunday_plan_form_cityId","sunday_plan_form_districtId");
    //2018年10月22日，省市区控件，控制园区选项
   // $("#sunday_plan_form_provinceId,#sunday_plan_form_cityId,#sunday_plan_form_districtId").change(function(){
     //   initGuardAndBunchCheckbox("sunday_plan_form_guardId","sunday_plan_form_bunchId_div","$!plan.guardId","$!plan.bunchIds","sunday_plan_form_provinceId","sunday_plan_form_cityId","sunday_plan_form_districtId");
    //})
    iniSelect("sunday_plan_form_season",dictionary_url+"season","$!plan.season");
    initYearMonthWeek("sunday_plan_form_year","sunday_plan_form_month","sunday_plan_form_week","$!plan.year","$!plan.month","$!plan.weekTime")

    //转跳类型判断
    $("#sunday_plan_form_type_div").children().click(function(){
        var type = $(this).attr("value");
        if (type == 1) {
            $("#sunday_plan_from_type_1_div").show();
            $("#sunday_plan_from_type_2_div").hide();
        }else if (type == 2) {
            $("#sunday_plan_from_type_1_div").hide();
            $("#sunday_plan_from_type_2_div").show();
        }
        $(this).addClass("on");
        $(this).siblings().removeClass("on")
    })
    //判断季节
    $("#sunday_plan_form_season_div").children().click(function(){
        $(this).addClass("on");
        $(this).siblings().removeClass("on")
    })
    //默认点击第一个
    var planId = "$!plan.id";
    //新增
    if(planId==null || planId == 0){
        $("#sunday_plan_form_type_div").children(":eq(0)").click();
    }else{
        //编辑
        var type  = "$!plan.type";
        var season  = "$!plan.season";
        $("#sunday_plan_form_type_div").children().each(function () {
            if(type == $(this).attr("value")){
                $(this).click();
            }
        });
        $("#sunday_plan_form_season_div").children().each(function () {
            if(season == $(this).attr("value")){
                $(this).click();
            }
        });
    }
    //实例化form
    var validatorMenu = $("#sunday_plan_form").validate({

        submitHandler: function (form) {


            $("#sunday_plan_form_guardName_input").val($("#sunday_plan_form_guardId option:selected").text());
            //类型
            $("#sunday_plan_form_type_input").val($("#sunday_plan_form_type_div").children(".on").attr("value"));
            //园区和班级信息
            var guardIds = "";
            var bunchIds = "";

            $("#sunday_plan_guard_and_bunch_table").find(".firstMenu").each(function(){
                if($(this).hasClass("on")){
                    guardIds +=$(this).attr("value")+",";
                }
            });

            $("#sunday_plan_guard_and_bunch_table").find(".secondMenu").each(function(){
                if($(this).hasClass("on")){
                    bunchIds +=$(this).attr("value")+",";
                }
            });

            //2019年4月28日，增加园区和班级JSON，用于按园区生成教学计划
            var guardAndBunchJson = new Array();
            $("#sunday_plan_guard_and_bunch_table").find(".firstMenu.on").each(function(){
                var guardJson =  JSON.parse("{}");
                guardJson["guardIds"]=","+$(this).attr("value")+",";
                var bunchArray = ",";
                $(this).parent().next().find(".secondMenu.on").each(function(){
                    bunchArray +=$(this).attr("value")+",";
                });
                guardJson["bunchIds"]=bunchArray;
                guardAndBunchJson.push(guardJson);
            });


            if(guardIds!=""){
                guardIds=","+guardIds;
                // guardIds =guardIds.substring(0,guardIds.length-1);
            }
            if(bunchIds!=""){
                bunchIds=","+bunchIds;
                // bunchIds =bunchIds.substring(0,bunchIds.length-1);
            }
            $("#sunday_plan_form_guardIds_input").val(guardIds);
            $("#sunday_plan_form_bunchIds_input").val(bunchIds);
            //2019年4月30日，新增
            $("#sunday_plan_form_guardAndBunchJson_input").val(JSON.stringify(guardAndBunchJson));



            var weekTime = $("#sunday_plan_form_week option:selected").val();
            var weekTimeStr = $("#sunday_plan_form_week option:selected").text();
            var weekTimeStart = weekTime.split(",")[0];
            var weekTimeEnd = weekTime.split(",")[1];
            $("#sunday_plan_form_weekTime_input").val(weekTime);
            $("#sunday_plan_form_weekTimeStr_input").val(weekTimeStr);
            $("#sunday_plan_form_weekTimeStart_input").val(weekTimeStart);
            $("#sunday_plan_form_weekTimeEnd_input").val(weekTimeEnd);
            $("#sunday_plan_form_season_input").val($("#sunday_plan_form_season_div").children(".on").attr("value"))
            //加遮罩层
            layer.load(4, {shade: [0.8, '#393D49']})
            $(form).ajaxSubmit({
                //你表单提交就要切换页面，ajax加载又是异步的。两边同时进行，
                //当ajax加载在切换页面之前完成，就会执行回调，反之不会执行
                //加同步提交数据
                async: false,
                dataType: "json",
                success: function (data) {
                    //关闭加载
                    layer.closeAll('loading');
                    if (data.code == 0) {
                        newTab('/sunday/web/plan/index?isClean=0',null)
                    } else {
                        layer.msg(data.message, {icon: 2});
                    }
                }
            });
        }
    });

</script>
