<style>
    .com-titl {
        width: 10%;
    }
</style>
<div class="release-notice">
    <form id="sunday_notice_form" class="form-horizontal" action="/sunday/web/notice/save" method="post"  enctype="multipart/form-data">
    <input type="hidden" name="id" value="$!{notice.id}">
    #*<input type="hidden" name="desc"  id="sunday_notice_form_desc_input"/>*#

    <input  type="hidden" name="status"  id ="sunday_notice_form_status_input"/>
    <input  type="hidden" name="type"  id ="sunday_notice_form_type_input"/>

    <div class="con">
        <div class="clearfix top-button-wrapper" >
            <button type="button" class="com-button item" onclick="sunday_notice_material_choose_open()">从素材库选择</button>
           <a href="javascript:newTab('/sunday/web/material/input?id=0&type=4&noticeId=$!notice.id',null)" class="com-add-button item">新建图文素材</a>
        </div>
        <!-- 未选择 -->
        <div class="notice-con clearfix">
            <div class="notice-item" id="sunday_notice_material_div">
                #if($notice.name)
                    <input type="hidden" name="name" value="$!notice.name">
                    <input type="hidden" name="shortName" value="$!notice.shortName">
                    <input type="hidden" name="image" value="$!notice.image">
                   #* <input type="hidden" name="desc" value='$!notice.desc'>*#
                    <textarea type="hidden" name="desc"  style="display: none">$!notice.desc</textarea>
                    <div class="notice-item-con">
                        <div class="titl">$!notice.name</div>
                        <img src="$!imgRoot$!notice.image" alt="" class="img">
                        <div class="des">
                            $!notice.shortName
                        </div>
                        <div class="time">更新于 $!notice.updateTime</div>
                    </div>
                    <button type="button" class="com-button return" onclick="$(this).parent().children().remove()">删除</button>
                #end
            </div>
        </div>


        <!-- 选择的 -->

        <!--需要发布的老师或学生-->
        <div class="filter-wrapper">
            <div class="titl">选择发布的园区活班级</div>
            <div class="item clearfix">
                <div class="com-titl" style="text-align: left"><i>*</i>发布时间：</div>
                <div class="com-time-item fl">
                    <input type="text" name="onLineTimeStr" value="$!notice.onLineTimeStr" required="required" id="sunday_notice_form_onlimeStr" readonly=""/>
                </div>
            </div>
            <div class="student-list-wrapper clearfix">
                <div class="student-list-left" id="sunday_notice_member_choose_div">
                    <div class="student-list-titl">请选择成员</div>
                    <div class="student-con">
                        <!--单个园区-->
                        <div class="yuanqu yuanqu1">
                            <div class="yuanqu-top clearfix">
                                <div class="up-btn"></div>
                                <div class="com-checkbox-wrapper clearfix">
                                    <div class="com-checkbox-item hook1" >杭州测试园幼教</div>
                                </div>
                            </div>
                            <div class="yuanqu-drop-down">
                                <!-- 二级菜单 -->
                                <div class="yuanqu yuanqu2 ">
                                    <div class="yuanqu-top clearfix">
                                        <div class="up-btn"></div>
                                        <div class="com-checkbox-wrapper clearfix">
                                            <div class="com-checkbox-item hook2" >班级</div>
                                        </div>
                                    </div>
                                    <div class="yuanqu-drop-down">
                                        <div class="yuanqu yuanqu3">
                                            <div class="yuanqu-top clearfix">
                                                <div class="up-btn"></div>
                                                <div class="com-checkbox-wrapper clearfix">
                                                    <div class="com-checkbox-item hook3" >教工</div>
                                                </div>
                                            </div>
                                            <div class="yuanqu-drop-down">
                                                <div class="yuanqu  yuanqu4 ">
                                                    <div class="yuanqu-top clearfix">
                                                        <div class="com-checkbox-wrapper clearfix">
                                                            <div class="com-checkbox-item hook4" >王老师</div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="yuanqu  yuanqu4 ">
                                                    <div class="yuanqu-top clearfix">
                                                        <div class="com-checkbox-wrapper clearfix">
                                                            <div class="com-checkbox-item hook4" >李老师</div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="yuanqu-top clearfix">
                                                <div class="up-btn"></div>
                                                <div class="com-checkbox-wrapper clearfix">
                                                    <div class="com-checkbox-item hook3"  >学生</div>
                                                </div>
                                            </div>
                                            <div class="yuanqu-drop-down">
                                                <div class="yuanqu  yuanqu4 ">
                                                    <div class="yuanqu-top clearfix">
                                                        <div class="com-checkbox-wrapper clearfix">
                                                            <div class="com-checkbox-item hook4" >小明</div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="yuanqu  yuanqu4 ">
                                                    <div class="yuanqu-top clearfix">
                                                        <div class="com-checkbox-wrapper clearfix">
                                                            <div class="com-checkbox-item hook4" >小美</div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                        <!--单个园区-->

                    </div>
                </div>
                <img src="$!adminRoot/yuhua/admin/img/youjiantou.png" width="19" alt="" class="icon">
                <!--已经选中的成员-->
                <div class="student-list-right" id="sunday_notice_member_choosed_div">
                    <div class="student-list-titl">即将发送的成员</div>
                    <div class="student-con">
                        <!--已选择的老师或学生-->
                        <div class="yuanqu-item">
                            <div class="yuanqu-name">杭州测试园幼教</div>
                            <div class="ban-list">
                                <!--班级--老师-->
                                <div class="ban-item clearfix">
                                    <div class="ban-name" style="width: 150px">云朵班（老师）：</div>
                                    <div class="ban-con clearfix" style="width: 550px">
                                        <div class="ban-member">毛老师</div>
                                        <div class="ban-member">毛老师</div>
                                    </div>
                                </div>
                                <div class="ban-item clearfix">
                                    <div class="ban-name">云朵班（学生）：</div>
                                    <div class="ban-con clearfix">
                                        <div class="ban-member">毛老师</div>
                                        <div class="ban-member">毛老师</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--  -->
                        <div class="yuanqu-item">
                            <div class="yuanqu-name">杭州测试园幼教</div>
                            <div class="ban-list">
                                <div class="ban-item clearfix">
                                    <div class="ban-name">云朵班：</div>
                                    <div class="ban-con clearfix">
                                        <div class="ban-member">毛老师</div>
                                        <div class="ban-member">毛老师</div>
                                    </div>
                                </div>
                                <div class="ban-item clearfix">
                                    <div class="ban-name">云朵班：</div>
                                    <div class="ban-con clearfix">
                                        <div class="ban-member">毛老师</div>
                                        <div class="ban-member">毛老师</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--  -->
                    </div>
                </div>
            </div>
        </div>
        <!-- 发布筛选条件 -->
        <div class="button-wrapper">
            <a href="javascript:yuhua_return('/sunday/web/notice/index');" class="com-button return">返回</a>
            <button type="submit" class="com-button">发布</button>
        </div>
    </div>
</form>

</div>

<script>
    $('.release-notice-popup .clos').click(function () {
        $('.release-notice-popup').removeClass('on');
        $('.mask').hide();
    })
    $('.release-notice-popup .return').click(function () {
        $('.add-plan-popup').removeClass('on');
        $('.mask').hide();
    })
    // 关闭弹窗

    $('.student-list-wrapper .yuanqu-top').on('click', '.up-btn', function () {
        if ($(this).hasClass('on')) {
            $(this).removeClass('on');
            $(this).parent().parent().children('.yuanqu-drop-down').stop().slideUp(300);
        } else {
            $(this).addClass('on');

            $(this).parent().parent().children('.yuanqu-drop-down').stop().slideDown(300);
        }
    })
    // 园区学生名单

    $('#preview').click(function () {
        $('.mask').show();
        $('.preview-popup').addClass('on');
    })

    $('.mask').click(function () {
        $('.mask').hide();
        $('.preview-popup').removeClass('on');
    })
    //园区树形结构勾选
    $("#sunday_notice_member_choose_div").find(".com-checkbox-item").click(function () {
      //  var level = 0;
        var isOn = false;
        if($(this).hasClass("on")){
            $(this).removeClass("on");
            isOn=false;
        }else{
            $(this).addClass("on");
            isOn=true;
        }
        console.log($(this).attr("class"));
        //园区
        if($(this).hasClass("hook1")){
            console.log("level=1")
            if(isOn){
                $(this).parents(".yuanqu1").find(".com-checkbox-item").addClass("on");
            }else{
                $(this).parents(".yuanqu1").find(".com-checkbox-item").removeClass("on");
            }
        //班级
        }else if($(this).hasClass("hook2")){
            console.log("level=2")
            if(isOn){
                //向上
                $(this).parents(".yuanqu1").addClass("on");
                //向下
                $(this).find(".com-checkbox-item").addClass("on");
            }else{
                //向上
                $(this).parents(".yuanqu1").removeClass("on");
                //向下
                $(this).find(".com-checkbox-item").removeClass("on");
            }
         //类型
        }else if($(this).hasClass("hook3")){
            console.log("level=3")
         //教师或学生
        }else if($(this).hasClass("hook4")){
            console.log("level=4")
        }

    })
</script>
<script>

    laydate.render({
        elem: '#sunday_notice_form_onlimeStr' //指定元素
        ,type:'datetime'
    });
    //打开选择器
    function sunday_notice_material_choose_open(){
        $("#sunday_material_choose_store_div").remove();
        $(".mask").remove();
        $.post("/sunday/web/material/choose?type=4&callBack=sunday_notice_material_choose_confirm",function (data) {
            $(".right-wrapper").append(data);
        })
    }
    //回掉选择器
    function sunday_notice_material_choose_confirm(data){
        console.log("------------执行选择毁掉------");
        $("#sunday_notice_material_div").children().remove();
        $("#sunday_notice_material_div").append(data);
    }
</script>
<script>
   //实例化图片裁剪控件
   // upImg(4/3,'sunday_upload_image_div','sunday_upload_image_input',400,300);
    //实例化富文本
   /* var desc_editor = KindEditor.create('textarea[id="sunday_notice_desc_form_texarea"]', {
        items:[
            'source', '|', 'undo', 'redo','|',"bold",'underline','strikethrough','|',
            'subscript', 'superscript', '|','forecolor', 'hilitecolor','|','removeformat','|',
            'insertorderedlist', 'insertunorderedlist','|','selectall','formatblock','fontname','fontsize','justifyleft',
            'fullscreen','justifycenter','justifyright','|','link','unlink','|','emoticons','image','media','|','baidumap','hr','print','preview'
        ],
        uploadJson:'$!{adminRoot}/sunday/web/upload/kindeditor/upload/single',
        fileManagerJson : '$!{adminRoot}/sunday/web/upload/kindeditor/upload/single',
        allowFileManager : true,
        width:"60.5%",
        height:"400px"
    });*/
  //实例化园区班级
   initGuardAndBunch("sunday_notice_form_guardId","sunday_notice_form_bunchId","$!notice.guardId","$!notice.bunchId");
    //实例化类型和状态选择

   $("#sunday_notice_form_status_div").children().click(function(){
       $(this).addClass("on");
       $(this).siblings().removeClass("on")
   })
   $("#sunday_notice_form_type_div").children().click(function(){
       $(this).addClass("on");
       $(this).siblings().removeClass("on");
       var type = $(this).attr("value");
       if(type == 1){
           $("#sunday_notice_form_guard_div").show();
           $("#sunday_notice_form_bunch_div").show();
       }else if(type == 2){
           $("#sunday_notice_form_guard_div").show();
           $("#sunday_notice_form_bunch_div").hide();
       }
   })
   var noticeId = "$!notice.id";
   //新增
   if(noticeId==null || noticeId == 0){
       $("#sunday_notice_form_status_div").children(":first").click();
       $("#sunday_notice_form_type_div").children(":first").click();
   }else{
       //编辑
       var status ="$!notice.status";
       var type  = "$!notice.type";
       $("#sunday_notice_form_status_div").children().each(function () {
           if(status == $(this).attr("value")){
               $(this).click();
           }
       });
       $("#sunday_notice_form_type_div").children().each(function () {
           if(type == $(this).attr("value")){
               $(this).click();
           }
       });
   }

   var validatorMenu = $("#sunday_notice_form").validate({

       submitHandler: function (form) {

           var name=$("#sunday_notice_form input[name='name']").val();
           if(name==null || name ==""){
               layer.msg("请选择素材", {
                   icon: 2
               });
               return ;
           }
           //加遮罩层
           layer.load(4, {shade: [0.8, '#393D49']})
           //$("#sunday_notice_form_desc_input").val($("#sunday_guard_desc_form_texarea").val());
           //状态和类型
           $("#sunday_notice_form_status_input").val($("#sunday_notice_form_status_div").children(".on").attr("value"));
           $("#sunday_notice_form_type_input").val($("#sunday_notice_form_type_div").children(".on").attr("value"));
            //园区和班级
           $("#sunday_notice_form_guardName_input").val($("#sunday_notice_form_guardId option:selected").text());
           $("#sunday_notice_form_bunchName_input").val($("#sunday_notice_form_bunchId option:selected").text());



           $(form).ajaxSubmit({
               //你表单提交就要切换页面，ajax加载又是异步的。两边同时进行，
               //当ajax加载在切换页面之前完成，就会执行回调，反之不会执行
               //加同步提交数据
               async: false,
               dataType: "json",
               success: function (data) {
                   //关闭加载
                   layer.closeAll('loading');
                   if (data.code == 0) {

                       //  newTab("");
                       newTab('/sunday/web/notice/index',null)
                   } else {
                       layer.msg(data.message, {icon: 2});
                   }
               }
           });
       }
   });
</script>