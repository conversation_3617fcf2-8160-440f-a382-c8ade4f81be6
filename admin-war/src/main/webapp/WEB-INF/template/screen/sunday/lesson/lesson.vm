#parse("/screen/sunday/operate.vm")
<div class="text-img imgs">
    <div class="top-wrapper clearfix">
        <script>
            $.post("/sunday/web/lesson/top",
                    {"type":1},
                    function (data) {
                        $(".top-wrapper").append(data);
                    })
        </script>
    </div>
    <div class="search-wrapper clearfix">
        <table class="search-table" style="margin-top: 20px;width: 100%">
            <tr>
                <td><div class="search-titl">课程名称：</div></td>
                <td>
                    <input type="text" placeholder="请输入课程名称" class="search-input-item" id="sunday_role_search_form_name" value="$!param.lessonName">
                </td>
                <td>
                    <button type="button" class="search-button" onclick="clearSearch()">清空</button>
                    <button type="button" class="search-button" onclick="sunday_role_search(true)">搜索</button>
                </td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td><a class="search-add-button sunday_lesson_add" href="javascript:sunday_lesson_save(0);" >新增课程</a></td>
            </tr>
        </table>
    </div>
    <div class="list" id="sunday_lesson_div" style="padding-top: 20px"></div>

    <div id="sunday_updatebook_div" class="modal fade" role="dialog" style="display: none;" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <form id="sunday_updatebook_form" autocomplete="off" class="form-horizontal" action="/sunday/web/lesson/save" method="post" enctype="multipart/form-data">
                    <input type="hidden" name="id"/>
                    <input type="hidden" name="guardIds" id="sunday_lesson_form_guardIds_input">
                    <input type="hidden" name="guardType" id="sunday_lesson_form_guardType_input">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal"></button>
                        <h4 class="modal-title">课程信息</h4>
                    </div>
                    <!--modald的body。存放form里面的数据什么的-->
                    <div class="modal-body">
                        <div class="row form-horizontal">
                        </div>
                        <div class="row form-horizontal">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label"><font color="#FF0000">*</font>课程名称：</label>
                                    <div class="col-sm-8">
                                        <input maxlength="100" class="form-control" type="text" name="lessonName"  required="required">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row form-horizontal">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label"><font color="#FF0000">*</font>课程图标：</label>
                                    <div class="col-sm-8">
                                        <div class="item clearfix" style="">
                                            <div class="com-add-img xzBtn" style="width:100px;height:100px;background-image: url($!{adminRoot}/yuhua/admin/img/add.png);" id="sunday_upload_image_div" onclick="upImg2(4,'sunday_upload_image_div','sunday_upload_image_input',100,100)">
                                                <input type="hidden" id="sunday_upload_image_input" name="categoryIcon" value="" >
                                            </div>
                                            <div class="com-titl mw80">(100*100px)</div>

                                            <script>
                                                function upImg2(bili,sunday_upload_image_div,sunday_upload_image_input,width,height,callType) {
                                                    //1,加载素材库
                                                    layer.load(4, {shade: [0.8, '#393D49']})
                                                    //不要异步操作
                                                    $.ajax({
                                                        type: "post",
                                                        url: "/sunday/web/material/upload/index",
                                                        data: {"type": 3,"bili":bili,"imageDiv":sunday_upload_image_div,"imageInput":sunday_upload_image_input,"width":width,"height":height,"callType":callType},
                                                        async: false,
                                                        success: function (data) {
                                                            //关闭加载
                                                            layer.closeAll('loading');
                                                            $(".right-wrapper").after(data);
                                                        }
                                                    });
                                                }
                                            </script>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row form-horizontal">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label"><font color="#FF0000">*</font>适用月龄：</label>
                                    <div class="col-sm-8">
                                        <input class="form-control" type="text" name="categoryName"  required="required">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row form-horizontal">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label"><font color="#FF0000">*</font>园区选择：</label>
                                    <div class="col-sm-8">
                                        <div class="com-radio-wrapper fl clearfix" id="sunday_lesson_form_guard_type_div" style="width: 82.5%">
                                            #if($!isSuper == 1)
                                                <div class="com-radio-item"  id="allGuard" style="margin-left: 0px;margin-right: 20px">全部校区</div>
                                                <div class="com-radio-item"  id="someGuard" style="margin-left: 0px;margin-right: 20px">部分校区</div>
                                            #else
                                                <div class="com-radio-item"  id="someGuard" style="margin-left: 0px;margin-right: 20px">部分校区</div>
                                            #end
                                        </div>
                                        <div class="com-radio-wrapper fl clearfix" id="sunday_lesson_form_guard_div" style="width: 82.5%">
                                            #foreach($!guard in $!guards)
                                                <div class="com-radio-item guard-radio" value="$!guard.id" style="display: none;margin-left: 0px;margin-right: 20px">$!guard.name</div>
                                            #end
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-group text-center">
                        <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                        <button class="btn btn-primary" type="submit">保存</button>
                    </div>
                </form>

            </div>
        </div>
    </div>
</div>
<script>
    $.ajaxSetup({
        async: false
    });
    $("#sunday_lesson_form_guard_type_div").children().click(function(){
        var id = $(this).attr("id");
        if(id =='allGuard'){
            $("#someGuard").removeClass("on");
            $(".guard-radio").hide();
            $(".guard-radio").removeClass("on");
        }else if(id == 'someGuard'){
            $("#allGuard").removeClass("on");
            $(".guard-radio").show();
            $(".guard-radio").removeClass("on");
        }
        if($(this).hasClass("on")){
            $(this).removeClass("on")
        }else{
            $(this).addClass("on");
        }
    })
    var isSuper = '$!isSuper';
    if(isSuper != 1){
        $("#someGuard").click();
    }
    //实例化多园区
    $("#sunday_lesson_form_guard_div").children().click(function(){
        if($(this).hasClass("on")){
            $(this).removeClass("on")
        }else{
            $(this).addClass("on");
        }
    })

    var param_pageNumber = "$!param.pageNumber";
    var param_pageSize = "$!param.pageSize";
    if(param_pageNumber==null|| param_pageNumber==""){
        param_pageNumber=1
    }
    if(param_pageSize==null|| param_pageSize==""){
        param_pageSize=10
    }
    var sunday_role_pageNumber=param_pageNumber;
    var sunday_role_pageSize=param_pageSize;
    var sunday_role_sort="id";
    var sunday_role_order="desc";
    var sunday_role_is_loading=false;
    var sunday_role_is_end=false;
    var sunday_role_guardId="";
    var sunday_role_name=""
    //输入订单号查询

    function sunday_role_search(isPageNumber){
        sunday_role_guardId = $("#sunday_bunch_search_form_guardId").val();
        sunday_role_is_loading=false;
        sunday_role_is_end=false;
        if(isPageNumber){
            sunday_role_pageNumber =1;
        }
        sunday_role_getData(true);
    }

    function sunday_role_search_page(pageNumber,pageSize,maxPageNumber){
        if(maxPageNumber!=null&&maxPageNumber!=""&&typeof(maxPageNumber)!="undefined"){
            if(parseInt(pageNumber)>parseInt(maxPageNumber)){
                return;
            }
        }
        sunday_role_pageNumber =pageNumber;
        sunday_role_pageSize =pageSize==null?sunday_role_pageSize:pageSize;
        sunday_role_getData(true);
    }

    function sunday_role_getData(isRemove){
        var t="#sunday_lesson_div";

        if(sunday_role_is_loading||sunday_role_is_end)return;
        if(isRemove){
            $(t).children().remove();
        }
        sunday_role_is_loading=true;
        sunday_role_name = $("#sunday_role_search_form_name").val();
        //同步请求
        $.ajax({
            type: "POST",
            url: "/sunday/web/lesson/select",
            data: {
                "pageSize":sunday_role_pageSize,
                "pageNumber":sunday_role_pageNumber,
                "sort":sunday_role_sort,
                "order":sunday_role_order,
                "lessonName":sunday_role_name
            },
            dataType: "text",
            async:true,
            success: function(result){
                if(result==null||result==""||result.length<1){
                    sunday_role_is_end=true;
                }else{
                    $(t).append(result);
                    sunday_role_is_loading=false;
                }
            }
        });
    }
    sunday_role_search(false);

    function sunday_lesson_save(id,lessonName,guardType,guardIds,categoryName,categoryIcon){
        if(isSuper != 1 && guardType == 0){
            layer.msg("权限不足,无法编辑", {
                icon: 2
            });
            return ;
        }
        $("#sunday_updatebook_form").form("clear");
        if(id != '' && id != 0  && id !=null){
            $("#sunday_updatebook_form").form("load", {"id":id,"lessonName":lessonName,'guardType':guardType,'guardIds':guardIds,'categoryName':categoryName,'categoryIcon':categoryIcon});
            $("#sunday_upload_image_div").css("background-image","url("+categoryIcon+")");
            if(guardType == 0){
                if(!$("#allGuard").hasClass("on")){
                    $("#allGuard").click();
                }

            }else{
                if(!$("#someGuard").hasClass("on")){
                    $("#someGuard").click();
                }
                $("#sunday_lesson_form_guard_div").children().removeClass("on");
                $("#sunday_lesson_form_guard_div").children().each(function () {
                    var guardIdArr = guardIds.split(",")

                    for(var i = 0 ;i <guardIdArr.length;i++){
                        if($(this).attr("value") == guardIdArr[i]){
                            if(!$(this).hasClass("on")){
                                $(this).click();
                            }

                        }
                    }
                });
            }
        }else{
            $("#sunday_updatebook_form").form("load", {"id": 0,'guardType':0});
            $("#sunday_upload_image_div").css("background-image","url($!{adminRoot}/yuhua/admin/img/add.png)");

            if(!$("#allGuard").hasClass("on")){
                $("#allGuard").click();
            }
        }
        remoteModal("sunday_updatebook_div", "")
    }
    var validatorMenu = $("#sunday_updatebook_form").validate({
        submitHandler: function (form) {
            var guardIds = "";
            var guardNames = "";
            var guardType = "";
            $("#sunday_lesson_form_guard_type_div").children().each(function(){
                if($(this).hasClass("on")){
                    var id = $(this).attr("id");
                    if(id =='allGuard'){
                        guardType = 0;
                    }else{
                        guardType = 1;
                    }
                }
            });
            if(guardType === ""){
                layer.msg("请选择关联园区", {
                    icon: 2
                });
                return ;
            }
            if(guardType == 1){
                $("#sunday_lesson_form_guard_div").children().each(function(){
                    if($(this).hasClass("on")){
                        guardIds +=$(this).attr("value")+",";
                    }
                });
                if(guardIds != ""){
                    guardIds=","+guardIds;
                }else{
                    layer.msg("请选择关联园区", {
                        icon: 2
                    });
                    return ;
                }
                $("#sunday_lesson_form_guardIds_input").val(guardIds);
            }

            var icon = $("#sunday_upload_image_input").val();

            if(!icon || icon == ''){
                layer.msg("请选择ICON", {
                    icon: 2
                });
                return ;
            }

            $("#sunday_lesson_form_guardType_input").val(guardType);
            //加遮罩层
            layer.load(4, {shade: [0.8, '#393D49']})
            $(form).ajaxSubmit({
                async: false,
                dataType: "json",
                success: function (data) {
                    //关闭加载
                    layer.closeAll('loading');
                    if (data.code == 0) {
                        sunday_role_getData(true);
                        closeModal("sunday_updatebook_div");
                    } else {
                        layer.msg(data.message, {
                            icon: 6
                        });
                    }
                }
            });
        }
    });

    function sunday_lesson_delete(id) {
        var msg = "确认要删除该课程吗?";
        layer.confirm(msg, function (index) {
            layer.load(4, {shade: [0.8, '#393D49']})
            //不要异步操作
            $.ajax({
                type: "post",
                url: "/sunday/web/lesson/deleteLesson",
                data: {"lessonId": id},
                async: false,
                success: function (data) {
                    //关闭加载
                    layer.closeAll('loading');
                    if (data.code == 0) {
                        sunday_role_search(false);
                    } else {
                        layer.msg(data.message, {
                            icon: 2
                        });
                    }
                }
            });
            //最后手动关闭
            layer.close(index);

        });
    }

    function clearSearch() {
        $("#sunday_role_search_form_name").val("");
        sunday_role_pageNumber = 1;
        sunday_role_getData(true);
    }


</script>