#parse("/screen/sunday/operate.vm")
<style>
    .layui-laydate-content>.laydate-time-list {
        padding-bottom: 0px;
        overflow: hidden;
    }
    .layui-laydate-content>.laydate-time-list>li{
        width:50%
    }
    .merge-box .scrollbox .merge-list {
        padding-bottom: 5px;
    }
    .add_t{
        padding: 0 9px;
        margin: 2px 5px;
        line-height: 30px;
        font-size: 14px;
        background: #27cdd7;
        color: #fff;
        border-radius: 4px;
        border: none;
        outline: none;
    }
    .select_t{
        width: 300px!important;
    }
</style>
<div class="add-student">
    <form id="sunday_bunch_form" autocomplete="off" class="form-horizontal" action="/sunday/web/bunch/saveQinzi" method="post" enctype="multipart/form-data">
        <input type="hidden" name="id" value="$!{bunch.id}">
        <input type="hidden" id="roomName" name="roomName" value="$!{bunch.bunchQinzi.roomName}">
        <input type="hidden" id="teacherIds" name="teacherIds" value="$!{bunch.bunchQinzi.teacherIds}">
        <input type="hidden" id="scheduleData" name="scheduleData">
        <div class="com-title-wrapper">
            <div class="title">#if($!type==1)编辑课表#else 编辑班级 #end</div>
        </div>
        #if($!type==2)
        <div class="con">
            <div class="item clearfix">
                <div class="com-titl"><i>*</i>班级图片：</div>
                <div class="com-add-img xzBtn" style="width:300px;height:200px;background-image: url(#if($bunch.image)$!bunch.image#else$!{adminRoot}/yuhua/admin/img/add.png#end);" id="sunday_upload_image_div" onclick="upImg2(6,'sunday_upload_image_div','sunday_upload_image_input',300,200)">
                    <input type="hidden" id="sunday_upload_image_input" name="image" value="$!bunch.image" >
                </div>
                <div class="com-titl mw80">&nbsp;(300*200px))</div>
                <script>
                    function upImg2(bili,sunday_upload_image_div,sunday_upload_image_input,width,height,callType) {
                        layer.load(4, {shade: [0.8, '#393D49']})
                        $.ajax({
                            type: "post",
                            url: "/sunday/web/material/upload/index",
                            data: {"type": 3,"bili":bili,"imageDiv":sunday_upload_image_div,"imageInput":sunday_upload_image_input,"width":width,"height":height,"callType":callType},
                            async: false,
                            success: function (data) {
                                layer.closeAll('loading');
                                $(".right-wrapper").after(data);
                            }
                        });
                    }
                </script>
            </div>
            <div class="item clearfix">
                <div class="com-titl">地区选择：</div>
                <div class="selectpicker">
                    <select id="sunday_bunch_form_provinceId"></select>
                </div>
                <div class="selectpicker">
                    <select id="sunday_bunch_form_cityId"></select>
                </div>
                <div class="selectpicker">
                    <select id="sunday_bunch_form_districtId"></select>
                </div>
            </div>
            <div class="item clearfix">
                <div class="com-titl"><i>*</i>园区信息：</div>
                <div class="selectpicker">
                    <select id="sunday_bunch_form_guardId" name="guardId" required="required"></select>
                </div>
                <div class="com-titl">教室信息：</div>
                <div class="selectpicker">
                    <select id="sunday_bunch_form_roomId" name="roomId"></select>
                </div>
                <div class="com-titl"><i>*</i>课程名称：</div>
                <div class="selectpicker">
                    <select id="lessonId" name="lessonId"></select>
                </div>
            </div>

            <div class="item clearfix">
                <div class="com-titl">开课日期：</div>
                <div class="com-time-item fl">
                    <input type="text"  name="openDate" value="$!bunch.bunchQinzi.openTimeStr" id="sunday_bunch_form_openDate">
                </div>
            </div>
            <div class="item clearfix">
                <div class="com-titl"><i>*</i>课类：</div>
                <input type="text" class="com-input-item fl" name="classCategory" readonly  value="亲子班" required="required">
            </div>
            <div class="item clearfix">
                <div class="com-titl"><i>*</i>班级名：</div>
                <input type="text" maxlength="100" class="com-input-item fl" name="name" value="$!bunch.name" required="required">
            </div>
            <div class="item clearfix">
                <div class="com-titl"><i>*</i>满班人数：</div>
                <input type="text" class="com-input-item fl" name="fullNumber" id="form_fullNumber" value="$!bunch.bunchQinzi.fullNumber" required="required" #*onmouseenter="getTotalNumber(this,'$!bunch.bunchQinzi.totalNumber')"*#>
            </div>
            <div class="item clearfix">
                <div class="com-titl"><i>*</i>消耗课时：</div>
                <input type="text" class="com-input-item fl" name="consumptionHour" readonly id="form_consumptionHour" value="$!bunch.bunchQinzi.consumptionHour" required="required">
            </div>
            <div class="item clearfix teacher_show">
                <div class="com-titl">教师：</div>
                <div class="selectpicker" style="width: 300px">
                    <select id="sunday_bunch_form_teacherId"></select>
                </div>
                #*<div style="float: left">
                    <button type="button" onclick="addTeacher()" class="add_t">添加教师</button>
                    <button type="button" onclick="deleteTeacher()" class="add_t">减少教师</button>
                </div>*#
            </div>
            <div class="item clearfix" id="teacher_div">
                <div class="com-titl">  备注：</div>
                <textarea type="text" class="com-input-item fl" name="remark" style="width: 360px;height: 160px">$!bunch.remark</textarea>
            </div>
            #end
            #if($!type==1)
            <div class="con" style="display:none;">
                <div class="item clearfix">
                    <div class="com-titl"><i>*</i>班级图片：</div>
                    <div class="com-add-img xzBtn" style="width:200px;height:150px;background-image: url(#if($bunch.image)$!bunch.image#else$!{adminRoot}/yuhua/admin/img/add.png#end);" id="sunday_upload_image_div" onclick="upImg2(3,'sunday_upload_image_div','sunday_upload_image_input',200,150)">
                        <input type="hidden" id="sunday_upload_image_input" name="image" value="$!bunch.image" >
                    </div>
                    <div class="com-titl mw80">&nbsp;(200*150px))</div>
                    <script>
                        function upImg2(bili,sunday_upload_image_div,sunday_upload_image_input,width,height,callType) {
                            layer.load(4, {shade: [0.8, '#393D49']})
                            $.ajax({
                                type: "post",
                                url: "/sunday/web/material/upload/index",
                                data: {"type": 3,"bili":bili,"imageDiv":sunday_upload_image_div,"imageInput":sunday_upload_image_input,"width":width,"height":height,"callType":callType},
                                async: false,
                                success: function (data) {
                                    layer.closeAll('loading');
                                    $(".right-wrapper").after(data);
                                }
                            });
                        }
                    </script>
                </div>
                <div class="item clearfix">
                    <div class="com-titl">地区选择：</div>
                    <div class="selectpicker">
                        <select id="sunday_bunch_form_provinceId"></select>
                    </div>
                    <div class="selectpicker">
                        <select id="sunday_bunch_form_cityId"></select>
                    </div>
                    <div class="selectpicker">
                        <select id="sunday_bunch_form_districtId"></select>
                    </div>
                </div>
                <div class="item clearfix">
                    <div class="com-titl"><i>*</i>园区信息：</div>
                    <div class="selectpicker">
                        <select id="sunday_bunch_form_guardId" name="guardId" required="required"></select>
                    </div>
                    <div class="com-titl"><i>*</i>教室信息：</div>
                    <div class="selectpicker">
                        <select id="sunday_bunch_form_roomId" name="roomId" required="required"></select>
                    </div>
                    <div class="com-titl"><i>*</i>课程名称：</div>
                    <div class="selectpicker">
                        <select id="lessonId" name="lessonId"></select>
                    </div>
                </div>

                <div class="item clearfix">
                    <div class="com-titl"><i>*</i>开课日期：</div>
                    <div class="com-time-item fl">
                        <input type="text"  name="openDate" value="$!bunch.bunchQinzi.openTimeStr" required="required" id="sunday_bunch_form_openDate">
                    </div>
                </div>
                <div class="item clearfix">
                    <div class="com-titl"><i>*</i>课类：</div>
                    <input type="text" class="com-input-item fl" name="classCategory" value="亲子班" required="required">
                </div>
                <div class="item clearfix">
                    <div class="com-titl"><i>*</i>班级名：</div>
                    <input type="text" maxlength="100" class="com-input-item fl" name="name" value="$!bunch.name" required="required">
                </div>
                <div class="item clearfix">
                    <div class="com-titl"><i>*</i>满班人数：</div>
                    <input type="text" class="com-input-item fl" name="fullNumber" id="form_fullNumber" value="$!bunch.bunchQinzi.fullNumber" required="required" >
                </div>
                <div class="item clearfix">
                    <div class="com-titl"><i>*</i>消耗课时：</div>
                    <input type="text" class="com-input-item fl" name="consumptionHour" id="form_consumptionHour" value="$!bunch.bunchQinzi.consumptionHour" required="required">
                </div>
                <div class="item clearfix teacher_show">
                    <div class="com-titl"><i>*</i>教师：</div>
                    <div class="selectpicker" style="width: 300px">
                        <select id="sunday_bunch_form_teacherId"></select>
                    </div>
        #*            <div style="float: left">
                        <button type="button" onclick="addTeacher()" class="add_t">添加教师</button>
                        <button type="button" onclick="deleteTeacher()" class="add_t">减少教师</button>
                    </div>*#
                </div>
                <div class="item clearfix" id="teacher_div">
                    <div class="com-titl"><i>*</i>备注：</div>
                    <textarea type="text" class="com-input-item fl" name="remark" style="width: 360px;height: 160px">$!bunch.remark</textarea>
                </div>
            </div>
            <div class="item clearfix" id="teacher_div">
                <div style="float:left;margin-left:72px;">
                    <button type="button" onclick="addSchedule()" class="add_t">修改上课时间</button>
                </div>
                <div>
                    <table width="100%"  border="0" cellspacing="0" cellpadding="0" id="sunday_schedule_div">
                        <tr>
                            <th width="20%">重复方式</th>
                            <th width="20%">上课时间</th>
                            <th width="20%">上课时段</th>
                            <th width="20%">上课主题</th>
                            <th>操作</th>
                        </tr>
                    </table>
                </div>
            </div>
            #end
            <div class="item clearfix">
                <div class="com-titl" style="height: 30px"></div>
                <div class="button-wrapper fl">
                    <button type="submit" class="com-button fr sunday_bunch_edit" style="margin-left: 20px">保存</button>
                    #if($!goType == 1)
                        <a href="javascript:yuhua_return('/sunday/web/schedule/index2?type=2');" class="com-button return fr">返回</a>
                    #else
                        <a href="javascript:yuhua_return('/sunday/web/bunch/qinziIndex?isClean=0');" class="com-button return fr">返回</a>
                    #end
                </div>
            </div>
        </div>
    </form>
</div>
<div id="sunday_updateSchedule_div" class="modal fade" role="dialog" style="display: none;" data-backdrop="static" data-keyboard="false">
    <div class="modal-dialog" style="width: 720px">
        <div class="modal-content">
            <form id="sunday_updateSchedule_form" class="form-horizontal" method="post" enctype="multipart/form-data">
                <input type="hidden" name="id" id="schedule_id" value="0"/>
                <input type="hidden" name="repetitionData" id="student_schedule_repetitionData"/>
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal"></button>
                    <h4 class="modal-title" id="schedule-title">排课计划</h4>
                </div>
                <div class="modal-body">
                    <div class="row form-horizontal"></div>
                    <div class="row form-horizontal">
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="col-sm-4 control-label"><font color="#FF0000">*</font>上课时间：</label>
                                <div class="col-sm-4">
                                    <div class="search-time-item" style="margin-left: 0px">
                                        <input type="text" placeholder="开始时间" readonly name="beginTime" id="sunday_schedule_save_form_beginTime"  required="required">
                                    </div>
                                </div>
                                <div class="col-sm-4 endTime" style="display: none">
                                    <div class="search-time-item" style="margin-left: 0px">
                                        <input type="text"  placeholder="结束时间" readonly name="endTime" id="sunday_schedule_save_form_endTime" required="required">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row form-horizontal">
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="col-sm-4 control-label"><font color="#FF0000">*</font>上课时段：</label>
                                <div class="col-sm-4">
                                    <div class="search-time-item" style="margin-left: 0px">
                                        <input type="text"  placeholder="请选择时段" readonly name="startInterval" id="sunday_schedule_save_form_startInterval_2" required="required" value="">
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="search-time-item" style="margin-left: 0px">
                                        <input type="text"  placeholder="请选择时段" readonly name="endInterval" id="sunday_schedule_form_endInterval_2" required="required" value="">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row form-horizontal">
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="col-sm-4 control-label"><font color="#FF0000">*</font>重复方式：</label>
                                <div class="col-sm-8">
                                    <div class="selectpicker" style="width: 100%">
                                        <select id="sunday_schedule_form_repetitionWay"  name="repetitionWay" required="required" >
                                            <option value="0">不重复</option>
                                            <option value="1">每周重复</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row form-horizontal repetitionWay" style="display: none">
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="col-sm-4 control-label"><font color="#FF0000">*</font>重复设置：</label>
                                <div class="col-sm-8">
                                    <label class="checkbox-inline">
                                        <input type="checkbox" class="inlineCheckbox" value="all">全部
                                    </label>
                                    #set($weeks = ['周一','周二','周三','周四','周五','周六','周日'])
                                    #foreach($!checkday in[0..6])
                                        <label class="checkbox-inline">
                                            <input type="checkbox" class="inlineCheckbox" value="$!checkday">$!{weeks.get($!{checkday})}
                                        </label>
                                    #end

                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row form-horizontal">
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">上课主题：</label>
                                <div class="col-sm-8">
                                    <textarea maxlength="200" class="form-control" type="text" name="scheduleTheme" id="sunday_schedule_form_theme">
                                        </textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-group text-center">
                    <button type="button" class="btn btn-default" id="closeButton" data-dismiss="modal">关闭</button>
                    <button class="btn btn-primary" type="button" onclick="saveSchedule()">保存</button>
                </div>
            </form>
        </div>
    </div>
</div>
<script>
    laydate.render({
        elem: '#sunday_schedule_save_form_beginTime' //指定元素
        ,format: 'yyyy-MM-dd'
    });
    laydate.render({
        elem: '#sunday_schedule_save_form_endTime' //指定元素
        ,format: 'yyyy-MM-dd'
    });
    laydate.render({
        elem: '#sunday_schedule_save_form_startInterval_2' //指定元素
        ,type: 'time'
        ,min: '07:00:00'
        ,max: '21:00:00'
        ,format: 'HH:mm'
        ,btns: ['clear', 'confirm']
    });

    laydate.render({
        elem: '#sunday_schedule_form_endInterval_2' //指定元素
        ,type: 'time'
        ,min: '07:00:00'
        ,max: '21:00:00'
        ,format: 'HH:mm'
        ,btns: ['clear', 'confirm']
    });

    laydate.render({
        elem: '#sunday_bunch_form_openDate' //指定元素
        ,min:minDate()
    });

    function minDate(){
        var now = new Date();
        return now.getFullYear()+"-" + (now.getMonth()+1) + "-" + now.getDate();
    }

    //重复选择
    $("#sunday_schedule_form_repetitionWay").change(function () {
        var value = $(this).val();
        if(value == 0){
            $(".repetitionWay").hide();
            $(".endTime").hide();
        }else{
            $(".repetitionWay").show();
            $(".endTime").show();
            $(".inlineCheckbox").first().click(function () {
                var value = $(this).prop("checked");
                if(value){
                    $(".inlineCheckbox").not(":first").prop("checked",true);
                }else{
                    $(".inlineCheckbox").not(":first").prop("checked",false);
                }
            })
        }
    })

    var goType="$!goType";
    var guardId = "$!bunch.guardId";
    var lessonId ="$!bunch.bunchQinzi.lessonId";
    var roomId = "$!bunch.bunchQinzi.roomId";
    intAreaNoTown("sunday_bunch_form_provinceId","sunday_bunch_form_cityId","sunday_bunch_form_districtId",null,null,null);
    initGuardAndLesson("sunday_bunch_form_guardId","lessonId","sunday_bunch_form_roomId",guardId,lessonId,roomId,"sunday_bunch_form_provinceId","sunday_bunch_form_cityId","sunday_bunch_form_districtId");
    //2018年10月22日，省市区控件，控制园区选项
    $("#sunday_bunch_form_provinceId,#sunday_bunch_form_cityId,#sunday_bunch_form_districtId").change(function(){
        initGuardAndLesson("sunday_bunch_form_guardId","lessonId","sunday_bunch_form_roomId",null,null,null,"sunday_bunch_form_provinceId","sunday_bunch_form_cityId","sunday_bunch_form_districtId");
    })
    var dictionary_url ="$!dictionaryRoot";
    var bunchId = "$!bunch.id";
    if(bunchId > 0){
        initGuardAndLesson("sunday_bunch_form_guardId","lessonId","sunday_bunch_form_roomId",guardId,lessonId,roomId,"sunday_bunch_form_provinceId","sunday_bunch_form_cityId","sunday_bunch_form_districtId");
        $("#sunday_bunch_form_guardId").attr("disabled",true);
        $("#lessonId").attr("disabled",true);
        $("#form_consumptionHour").attr("disabled",true);
        $("#sunday_bunch_form_provinceId").attr("disabled",true);
        $("#sunday_bunch_form_cityId").attr("disabled",true);
        $("#sunday_bunch_form_districtId").attr("disabled",true);
    }

    var teacherIds = '$!bunch.bunchQinzi.teacherIds';
    !function () {
        var tIds = teacherIds.split(",");
        tIds = tIds.filter(item => item != '' && item !=null );
        if(tIds.length ==0){
            $(".teacher_show").hide();
            //initTeacher("sunday_bunch_form_teacherId",null,"sunday_bunch_form_guardId");

        }else if(tIds.length == 1){
            initTeacher("sunday_bunch_form_teacherId",tIds[0],"sunday_bunch_form_guardId");
            $("#sunday_bunch_form_teacherId").attr("disabled",true);
        }else if(tIds.length == 2){
            initTeacher("sunday_bunch_form_teacherId",tIds[0],"sunday_bunch_form_guardId");
            addTeacher();
            initTeacher2("teacher_select_1",tIds[1]);
            $("#teacher_select_1").attr("disabled",true);
        }else if(tIds.length == 3){
            initTeacher("sunday_bunch_form_teacherId",tIds[0],"sunday_bunch_form_guardId");
            addTeacher();
            initTeacher2("teacher_select_1",tIds[1]);
            addTeacher();
            initTeacher2("teacher_select_2",tIds[2]);
            $("#teacher_select_1").attr("disabled",true);
            $("#teacher_select_2").attr("disabled",true);
        }
    }();

    //实例化表单组件
    var validatorMenu=$("#sunday_bunch_form").validate({
        submitHandler: function(form) {

            var consumptionHour = $('#form_consumptionHour').val();

            if(!isNaN(consumptionHour)){
                var dot = consumptionHour.indexOf(".");
                if(dot != -1){
                    var dotCnt = consumptionHour.substring(dot+1,consumptionHour.length);
                    if(dotCnt.length >= 2){
                        layer.msg("请输入正确的消耗课时(最多精确到小数点后1位)", {
                            icon: 2
                        });
                        return;
                    }
                }
            }else{
                layer.msg("请输入正确的消耗课时(最多精确到小数点后1位)", {
                    icon: 2
                });
                return;
            }
            $("#sunday_bunch_form_guardId").attr("disabled",false);
            $("#sunday_bunch_form_provinceId").attr("disabled",false);
            $("#sunday_bunch_form_cityId").attr("disabled",false);
            $("#sunday_bunch_form_districtId").attr("disabled",false);
            $("#roomName").val($("#sunday_bunch_form_roomId option:selected").text());
            var image_val=$("#sunday_upload_image_input").val();
            if(image_val==null || image_val ==""){
                layer.msg("请上传班级图片", {
                    icon: 2
                });
                return ;
            }
            var num = $("#form_fullNumber").val();

            var oldNum = '$!bunch.bunchQinzi.fullNumber';

            if(parseInt(num)<0){
                layer.msg("满班人数不正确!", {
                    icon: 2
                });
                return;
            }
            $("#form_fullNumber").val(parseInt(num));

            /*var teacherIds = [];
            var t1 = $("#sunday_bunch_form_teacherId option:selected").val();
            if(t1 == '' || t1 == 0 || t1 ==null){
                layer.msg("请选择教师", {
                    icon: 2
                });
                return;
            }*/
            //teacherIds.push(parseInt(t1));
            /*$(".teacher_select").each(function () {
                var value = $(this).val();
                if(value != '' && value != 0 && value !=null){
                    var index = $.inArray(parseInt(value), teacherIds);
                    if (index >= 0) {

                    }else{
                        teacherIds.push(parseInt(value));
                    }
                }
            })

            $("#teacherIds").val(","+teacherIds.join(",")+",");*/
            layer.load(4, {shade: [0.8, '#393D49']});
            $(form).ajaxSubmit({
                async: false,
                dataType:"json",
                success:function(data){
                    layer.closeAll('loading');
                    if(data.code == 0){
                        if(goType=="1"){
                            newTab('/sunday/web/schedule/index2?type=2',null);
                        } else{
                            newTab('/sunday/web/bunch/qinziIndex?isClean=0',null);
                        }
                    }else{
                        layer.msg(data.message, {icon: 2});
                    }
                }
            });
        }
    }) ;

    function initTeacher(C, B,L) {
        var A = "/sunday/web/teacher/selectNoPage";
        $("#" + C).unbind("change");
        $("#" + C).children().remove();
        $("#" + C).append("<option value=''>请选择教师</option>");
        $.post(A,{"guardId":$("#"+L).val()
        }, function(I) {
            var G = I.result;
            for (var H = 0; H < G.length; H++) {
                var guardName = G[H].guardName;
                if(guardName == '' || guardName == null){
                    guardName = "无";
                }
                var degree = "  职位:"+G[H].degree;
                if (typeof(B) != "undefined" && B != null && B != "" && B == G[H].id) {
                    $("#" + C).append("<option selected value='" + G[H].id + "'>" + G[H].name +"("+guardName+"---"+degree+")" + "</option>")
                } else {
                    $("#" + C).append("<option  value='" + G[H].id + "'>" + G[H].name+"("+guardName+"---"+degree+")" + "</option>")
                }
            }
            $("#" + C).change()
        })
    }

    function initTeacher2(C, B) {
        var A = "/sunday/web/teacher/selectNoPage";
        $("#" + C).unbind("change");
        $("#" + C).children().remove();
        $("#" + C).append("<option value=''>请选择教师</option>");
        $.post(A,{
        }, function(I) {
            var G = I.result;
            for (var H = 0; H < G.length; H++) {
                var guardName = G[H].guardName;
                if(guardName == '' || guardName == null){
                    guardName = "无";
                }
                var degree = "  职位:"+G[H].degree;
                if (typeof(B) != "undefined" && B != null && B != "" && B == G[H].id) {
                    $("#" + C).append("<option selected value='" + G[H].id + "'>" + G[H].name +"("+guardName+"---"+degree+")" + "</option>")
                } else {
                    $("#" + C).append("<option  value='" + G[H].id + "'>" + G[H].name+"("+guardName+"---"+degree+")" + "</option>")
                }
            }
            $("#" + C).change()
        })
    }


    function getTotalNumber(obj,number) {
        layer.tips("满班人数不可低于当前班级人数,当前:"+number+"人",obj,{
            tips: [2, '#3595CC'],time:2000});
    }


    function addTeacher() {
        var num = $(".teacher_select").length;
        if(num >=2){
            layer.msg("一个班级最多3名教师", {
                icon: 2
            });
            return ;
        }
        var html =  "<div class='item clearfix t_d'>" +
                "    <div class='com-titl'>教师"+(num+1)+": </div>" +
                "    <div class='selectpicker select_t'>" +
                "    <select id='teacher_select_"+(num+1)+"' class='teacher_select'></select>" +
                "    </div>"+
                "    </div>";
        $("#teacher_div").before(html);
        initTeacher2("teacher_select_"+(num+1));
    }
    function deleteTeacher() {
        $(".t_d").last().remove();
    }
    function initGuardAndLesson(H, A, R, B, E, Q, D, F, C) {
        var I = "/sunday/web/guard/selectNoPage";
        var G = "/sunday/web/lesson/selectNoPage";
        var W = "/sunday/web/room/selectNoPage";
        $("#" + H).unbind("change");
        $("#" + H).children().remove();
        $("#" + A).children().remove();
        $("#" + R).children().remove();
        $("#" + H).append("<option value=''>请选择园区</option>");
        $.post(I, {
                    "provinceId": $("#" + D).val(),
                    "cityId": $("#" + F).val(),
                    "districtId": $("#" + C).val()
                },
                function(L) {
                    var J = L.result;
                    for (var K = 0; K < J.length; K++) {
                        if (typeof(B) != "undefined" && B != null && B != "" && B == J[K].id) {
                            $("#" + H).append("<option selected value='" + J[K].id + "'>" + J[K].name + "</option>")
                        } else {
                            $("#" + H).append("<option  value='" + J[K].id + "'>" + J[K].name + "</option>")
                        }
                    }
                    $("#" + H).change(function() {
                        $("#" + A).children().remove();
                        $("#" + R).children().remove();
                        $("#" + A).append("<option value=''>请选择课程</option>");
                        if ($(this).val() != "") {
                            $.post(G, {"guardId": $(this).val()},
                                    function(N) {
                                        var O = N.result;
                                        for (var M = 0; M < O.length; M++) {
                                            if (typeof(E) != "undefined" && E != null && E != "" && E == O[M].id) {
                                                $("#" + A).append("<option selected value='" + O[M].id +"'>" + O[M].lessonName + "</option>")
                                            } else {
                                                $("#" + A).append("<option value='" + O[M].id + "' >" + O[M].lessonName + "</option>")
                                            }
                                        }
                                    })
                        }
                        $("#" + R).append("<option value=''>请选择教室</option>");
                        if ($(this).val() != "") {
                            $.post(W, {"guardId": $(this).val()},
                                    function(N) {
                                        var O = N.result;
                                        for (var M = 0; M < O.length; M++) {
                                            if (typeof(Q) != "undefined" && Q != null && Q != "" && Q == O[M].id) {
                                                $("#" + R).append("<option selected value='" + O[M].id +"'>" + O[M].name + "</option>")
                                            } else {
                                                $("#" + R).append("<option value='" + O[M].id + "' >" + O[M].name + "</option>")
                                            }
                                        }
                                    })
                        }
                    });
                    $("#" + H).change()
                })
    }

    laydate.render({
        elem: '#sunday_schedule_save_form_beginTime' //指定元素
        ,format: 'yyyy-MM-dd'
    });
    laydate.render({
        elem: '#sunday_schedule_save_form_endTime' //指定元素
        ,format: 'yyyy-MM-dd'
    });

    function addSchedule(id) {
        if(id && id !='' && id != 0){
            var scheduleData = $("#scheduleData").val();
            if(scheduleData && scheduleData != ''){
                var schedule_arr =  [];
                schedule_arr= JSON.parse(scheduleData);
                schedule_arr = schedule_arr.filter(item => item.id ==id);
                if(schedule_arr.length>0){
                    var schedule = schedule_arr[0];
                    $("#sunday_updateSchedule_form").form("clear");
                    remoteModal("sunday_updateSchedule_div", "");
                    $("#sunday_updateSchedule_form").form("load", schedule);
                    if(schedule.repetitionWay == 1){
                        var week = schedule.repetitionData;
                        for(var i =0;i<week.length;i++){
                            if(week[i] == 1){
                                $(".inlineCheckbox").eq(i+1).click();
                            }
                        }

                    }else{
                    }
                    $("#sunday_updateSchedule_div").modal({show:true,backdrop:'static'})
                }else{
                    addSchedule(0);
                }
            }
        }else{
            $("#sunday_updateSchedule_form").form("clear");
            remoteModal("sunday_updateSchedule_div", "");
            $("#sunday_updateSchedule_form").form("load", {"id":0,"startInterval":"","endInterval":"","repetitionWay":0});
            $(".repetitionWay").hide();
            $("#sunday_updateSchedule_div").modal({show:true,backdrop:'static'})
        }
    }

    function saveSchedule() {
        var scheduleData = $("#scheduleData").val();
        if(scheduleData && scheduleData != '') {
            var schedule_arr = [];
            schedule_arr = JSON.parse(scheduleData);
        }
        var scheduleId = $("#schedule_id").val();

        var openTime = $("#sunday_bunch_form_openDate").val();

        var way = $("#sunday_schedule_form_repetitionWay").val();
        var beginTime = $("#sunday_schedule_save_form_beginTime").val();

        if(beginTime == '' || beginTime == null){
            layer.msg("开始日期不可为空!", {
                icon: 2
            });
            return ;
        }



        var endTime = $("#sunday_schedule_save_form_endTime").val();




        var start = $("#sunday_schedule_save_form_startInterval_2").val();
        var end = $("#sunday_schedule_form_endInterval_2").val();
        var scheduleTheme = $("#sunday_schedule_form_theme").val();
        if(end<=start){
            layer.msg("上课时段不正常!", {
                icon: 2
            });
            return ;
        }



        var data = [0,0,0,0,0,0,0];
        if(way == 0){
            endTime = beginTime;
            $("#sunday_schedule_save_form_endTime").val(endTime);
        }else{
            $(".inlineCheckbox").not(":first").each(function () {
                var flag = $(this).prop("checked");
                if(flag){
                    var value = $(this).val();
                    data[parseInt(value)] = 1;
                }
            })
            if($.inArray(1, data) == -1){
                layer.msg("请勾选重复日期!", {
                    icon: 2
                });
                return ;
            }
            if(endTime == '' || endTime == null){
                layer.msg("结束日期不可为空!", {
                    icon: 2
                });
                return ;
            }
        }

        if(new Date(beginTime)<new Date()){
            layer.msg("开始日期需大于今天!", {
                icon: 2
            });
            return ;
        }
        if(new Date(endTime)<new Date()){
            layer.msg("结束日期需大于今天!", {
                icon: 2
            });
            return ;
        }
        if(new Date(endTime)<new Date(beginTime)){
            layer.msg("结束日期需大于开始日期", {
                icon: 2
            });
            return ;
        }

        var schedule = {
            'id':scheduleId,
            'repetitionWay':way,
            'beginTime':beginTime,
            'endTime':endTime,
            'startInterval':start,
            'endInterval':end,
            'repetitionData':data,
            'scheduleTheme':scheduleTheme,
        };
        if(!scheduleId || scheduleId =='' || scheduleId == 0){
            schedule.id = new Date().getTime();
        }
        var td1 = "";
        if(way == 0){
            td1 = "不重复"
        }else{
            var arr = ['每周一','每周二','每周三','每周四','每周五','每周六','每周日']
            for(var i=0;i<data.length;i++){
                if(data[i] == 1){
                    if(td1 == ''){
                        td1 = arr[i];
                    }else{
                        td1+=","+arr[i];
                    }

                }
            }
        }
        var table_html = "" +
                "        <td>"+td1+"</td>" +
                "        <td>"+beginTime+"~"+endTime+"</td>" +
                "        <td>"+start+"-"+end+"</td>" +
                "        <td>"+scheduleTheme+"</td>" +
                "        <td id='"+schedule.id+"'><button type='button' class='schedule_edit'>编辑</button><button type='button' class='button-delete schedule_delete'>删除</button></td>" +
                "";
        if(scheduleId && scheduleId !='' && scheduleId != 0){
            if(scheduleData && scheduleData != ''){
                var schedule_arr =  [];
                schedule_arr= JSON.parse(scheduleData);
                for(var i in schedule_arr){
                    if(schedule_arr[i].id == scheduleId){
                        schedule_arr[i] = schedule;
                        break;
                    }
                }
            }
            $("#"+scheduleId).parent().html(table_html);
        }else{
            $("#sunday_schedule_div").append("<tr>"+table_html+"</tr>");
        }
        if(scheduleData && scheduleData != ''){
            schedule_arr.push(schedule);
            $("#scheduleData").val(JSON.stringify(schedule_arr));
        }else{
            var item = [];
            item.push(schedule);
            $("#scheduleData").val(JSON.stringify(item));
        }
        closeModal("sunday_updateSchedule_div");
    }

    $("body").on("click", ".schedule_edit", function() {
        var id = $(this).parent().attr('id');
        addSchedule(id);
    });

    function remoteModal(B) {
        $("#" + B).modal({
            show: true,
            backdrop: "static",//点击空白处不关闭对话框
        });
        $("#" + B).show()
    }
    //重复选择
    $("#sunday_schedule_form_repetitionWay").change(function () {
        var value = $(this).val();
        if(value == 0){
            $(".repetitionWay").hide();
            $(".endTime").hide();
        }else{
            $(".repetitionWay").show();
            $(".endTime").show();
            $(".inlineCheckbox").first().click(function () {
                var value = $(this).prop("checked");
                if(value){
                    $(".inlineCheckbox").not(":first").prop("checked",true);
                }else{
                    $(".inlineCheckbox").not(":first").prop("checked",false);
                }
            })
        }
    })

</script>