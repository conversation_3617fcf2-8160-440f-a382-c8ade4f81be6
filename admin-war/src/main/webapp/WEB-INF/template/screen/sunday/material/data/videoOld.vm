<div class="list clearfix">
    <div class="list-top clearfix" id="sunday_material_data_group" >
        #if($!groupId != 0)
            <div class="bt sunday_material_save" onclick="sunday_material_group_save(null,null)">
                <img src="$adminRoot/yuhua/admin/img/chong.png" width="21" alt="">
                <i>重命名</i>
            </div>
            <div class="bt sunday_material_delete" onclick="sunday_material_group_delete()">
                <img src="$adminRoot/yuhua/admin/img/shan.png" width="22" alt="" >
                <i>删除</i>
            </div>
        #end
    </div>
    <!--数据列表-->
    #parse("/screen/sunday/operate.vm")
    <table width="100%"  border="0" cellspacing="0" cellpadding="0" id="sunday_material_data_table" >
        <tr>
            <th width="10%">封面</th>
            <th width="20%">标题</th>
            <th width="10%">大小</th>
            <th width="15%">时长</th>
            <th width="20%">创建时间</th>
            <th>操作</th>
        </tr>
        #foreach($!material in $!materials)
            <tr>
                <td><img src="$!material.image" height="80%" alt=""></td>
                <td>$!material.name</td>
                <td>$!material.sourceSize KB</td>
                <td>$!material.sourceTime 秒</td>
                <td>$!material.createTime</td>
                <td>
                    <button type="button" onclick="sunday_material_delete($!material.id)" class="button-delete sunday_material_delete">删除</button>
                    <button type="button" onclick="sunday_material_save($!material.id)" class="sunday_material_edit">编辑</button>
                    <button type="button" onclick="sunday_material_down('$!material.source')" class="sunday_material_down">下载</button>
                </td>
            </tr>
        #end
    </table>

    <div class="com-page-wrapper" id="sunday_material_data_page">
        <span class="page-num">共$!total条，$!currentPage/$!totalPage页</span>
        <!--判断是否能够转跳页数-->
            <span class="next pre #if($!prevPage >= $!minPage) on" onclick=sunday_material_search_page($!prevPage,null) #end"></span>

            <span class="next #if($!maxPage >= $!nextPage) on" onclick=sunday_material_search_page($!nextPage,null) #end"></span>
        <span class="text">跳转到：</span>
        <input type="text" value="$!currentPage" min="$!minPage" max="$!maxPage">
        <span class="go" onclick="sunday_material_search_page($(this).prev().val(),null,$!maxPage)">GO</span>
    </div>
</div>
<script>
    $("#sunday_material_search_form_msg").text("音频共 $!total 条");
</script>