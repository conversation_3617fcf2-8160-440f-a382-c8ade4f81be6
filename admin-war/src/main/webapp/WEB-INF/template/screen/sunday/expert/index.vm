#parse("/screen/sunday/operate.vm")
<div class="weekly-plan">
    <div class="search-wrapper clearfix">
        <form action="" method="">
            <div class="com-title-wrapper">
                <div class="title">查询条件</div>
            </div>
            <div class="clearfix">
                <div class="com-titl item">姓名</div>
                <input type="text" placeholder="" class="com-input-item fl item" id="sunday_expert_search_form_name">
                <button type="button" class="com-button item" onclick="sunday_expert_search(true)">搜索</button>
                <a href="javascript:sunday_expert_save(0)" class="com-add-button item sunday_expert_add">新增专家</a>

            </div>
        </form>
    </div>
    <!-- 查询 -->

    <div class="list" id="sunday_expert_div">

        <!-- 页码 -->

    </div>
</div>
<script>
    var param_pageNumber = "$!param.pageNumber";
    var param_pageSize = "$!param.pageSize";
    console.log("param_pageNumber="+param_pageNumber+",param_pageSize="+param_pageSize)
    if(param_pageNumber==null|| param_pageNumber==""){
        param_pageNumber=1
    }
    if(param_pageSize==null|| param_pageSize==""){
        param_pageSize=50
    }
    var sunday_expert_pageNumber=param_pageNumber;
    var sunday_expert_pageSize=param_pageSize;
    var sunday_expert_sort="id";
    var sunday_expert_order="desc";
    var sunday_expert_is_loading=false;
    var sunday_expert_is_end=false;
    var sunday_expert_name="";
    //输入订单号查询
    function sunday_expert_search(isPageNumber){
        sunday_expert_name = $("#sunday_expert_search_form_name").val();
        sunday_expert_is_loading=false;
        sunday_expert_is_end=false;
        if(isPageNumber){
            sunday_expert_pageNumber =1;
        }
        sunday_expert_getData(true);
    }
    function sunday_expert_search_page(pageNumber,pageSize,maxPageNumber){
        if(maxPageNumber!=null&&maxPageNumber!=""&&typeof(maxPageNumber)!="undefined"){
            if(parseInt(pageNumber)>parseInt(maxPageNumber)){
                return;
            }
        }
        sunday_expert_pageNumber =pageNumber;
        sunday_expert_pageSize =pageSize==null?sunday_expert_pageSize:pageSize;
        sunday_expert_getData(true);
    }
    function sunday_expert_getData(isRemove){
        var t="#sunday_expert_div";

        if(sunday_expert_is_loading||sunday_expert_is_end)return;
        if(isRemove){
            $(t).children().remove();
        }
        sunday_expert_is_loading=true;
        //layer.load(1, {shade: [0.5, '#fff']});
        //layer.load(4, {shade: [0.8, '#393D49']})

        //同步请求
        $.ajax({
            type: "POST",
            url: "/sunday/web/expert/select",
            data: {
                "pageSize":sunday_expert_pageSize,
                "pageNumber":sunday_expert_pageNumber,
                "sort":sunday_expert_sort,
                "order":sunday_expert_order,
                "name":sunday_expert_name
            },
            dataType: "text",
            async:true,
            success: function(result){
                //d layer.closeAll('loading');
                //返回的是html
                if(result==null||result==""||result.length<1){
                    sunday_expert_is_end=true;
                }else{
                    if(isRemove && $(t).children()!=null){
                        $(t).children().remove();
                    }
                    $(t).append(result);
                    sunday_expert_is_loading=false;
                }
            }
        });
    }
    sunday_expert_getData(false);

    function sunday_expert_save(id){
        newTab("/sunday/web/expert/input?id=" + id , "新增/编辑");
    }
    function sunday_expert_delete(id) {
        var msg = "确认要删除这条信息吗?";
        layer.confirm(msg, function (index) {
            layer.load(4, {shade: [0.8, '#393D49']})
            //不要异步操作
            $.ajax({
                type: "post",
                url: "/sunday/web/expert/delete",
                data: {"id": id},
                async: false,
                success: function (data) {
                    //关闭加载
                    layer.closeAll('loading');
                    if (data.code == 0) {
                        sunday_expert_search(false);
                    } else {
                        layer.msg('删除失败', {
                            icon: 2
                        });
                    }
                }
            });
            //最后手动关闭
            layer.close(index);

        });
    }
</script>