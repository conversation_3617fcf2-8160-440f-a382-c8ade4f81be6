#parse("/screen/sunday/operate.vm")
<div class="weekly-plan kindergarten-notice">
<div class="search-wrapper clearfix">
        <div class="com-title-wrapper">
            <div class="title">条件筛选</div>
        </div>
        <table class="search-table" style="margin-top: 20px">
            <tr>
                <td><div class="search-titl">地区选择：</div></td>
                <td>
                    <div class="search-selectpicker">
                        <select id="sunday_notice_search_form_provinceId"></select>
                    </div>
                </td>
                <td>
                    <div class="search-selectpicker">
                        <select  id="sunday_notice_search_form_cityId"></select>
                    </div>
                </td>
                <td>
                    <div class="search-selectpicker">
                        <select   id="sunday_notice_search_form_districtId"></select>
                    </div>
                </td>
                <td></td>
                <td></td>
            </tr>
            <tr>
                <td><div class="search-titl">园区选择：</div></td>
                <td>
                    <div class="search-selectpicker">
                        <select id="sunday_notice_search_form_guardId"></select>
                    </div>
                </td>
                <td>
                    <div class="search-selectpicker">
                        <select   id="sunday_notice_search_form_bunchId"></select>
                    </div>
                </td>
                <td><input type="text" placeholder="请输入标题" class="search-input-item" id="sunday_notice_search_form_name" value="$!param.name"></td>
                <td><button type="button" class="search-button" onclick="sunday_notice_search(true)">搜索</button>
                        <a href="javascript:sunday_notice_save(0);" class="search-add-button sunday_notice_add">发布通知</a>
                    </td>
                <td></td>
            </tr>
        </table>
        <script>
            $.ajaxSetup({
                async: false
            });
            intAreaNoTown("sunday_notice_search_form_provinceId","sunday_notice_search_form_cityId","sunday_notice_search_form_districtId",null,null,null);
            initGuardAndBunch("sunday_notice_search_form_guardId","sunday_notice_search_form_bunchId","$!param.guardIds","$!param.bunchIds","sunday_notice_search_form_provinceId","sunday_notice_search_form_cityId","sunday_notice_search_form_districtId");
            //2018年10月22日，省市区控件，控制园区选项
            $("#sunday_notice_search_form_provinceId,#sunday_notice_search_form_cityId,#sunday_notice_search_form_districtId").change(function(){
                initGuardAndBunch("sunday_notice_search_form_guardId","sunday_notice_search_form_bunchId",null,null,"sunday_notice_search_form_provinceId","sunday_notice_search_form_cityId","sunday_notice_search_form_districtId");
            })
        </script>
</div>
<!-- 查询 -->

    <div class="list">
        <div class="list-top clearfix" id="sunday_notice_div">
           <div class="com-menu-wrapper clearfix fl" id="sunday_notice_search_form_status_div">
                <div class="com-menu-item #if($!status == 1) on #end" value="1" >已发布</div>
                <div class="com-menu-item #if($!status == 0) on #end" value="0" >未发布</div>
            </div>

        </div>

        <!-- 页码 -->

    </div>
</div>
<!--图文素材选择器-->

<script>

    $("#sunday_notice_search_form_status_div").children().click(function(){
            $(this).addClass("on");
            $(this).siblings().removeClass("on");
             sunday_notice_search(false);
    })
    var param_pageNumber = "$!param.pageNumber";
    var param_pageSize = "$!param.pageSize";
    console.log("param_pageNumber="+param_pageNumber+",param_pageSize="+param_pageSize)
    if(param_pageNumber==null|| param_pageNumber==""){
        param_pageNumber=1
    }
    if(param_pageSize==null|| param_pageSize==""){
        param_pageSize=50
    }
    $("#sunday_notice_search_form_status_div").children(".on").click();
    var sunday_notice_pageNumber=param_pageNumber;
    var sunday_notice_pageSize=param_pageSize;
    var sunday_notice_sort="id";
    var sunday_notice_order="desc";
    var sunday_notice_is_loading=false;
    var sunday_notice_is_end=false;
    var sunday_notice_guardId="";
    var sunday_notice_bunchId="";
    var sunday_notice_status="";
    var sunday_notice_name="";
    //输入订单号查询
    function sunday_notice_search(isPageNumber){
        sunday_notice_guardId = $("#sunday_notice_search_form_guardId option:selected").val();
        sunday_notice_bunchId = $("#sunday_notice_search_form_bunchId option:selected").val();
        sunday_notice_status = $("#sunday_notice_search_form_status_div").children(".on").attr("value");
        sunday_notice_name = $("#sunday_notice_search_form_name").val();
        sunday_notice_is_loading=false;
        sunday_notice_is_end=false;
        if(isPageNumber){
            sunday_notice_pageNumber =1;
        }
        sunday_notice_getData(true);
    }
    function sunday_notice_search_page(pageNumber,pageSize,maxPageNumber){
        if(maxPageNumber!=null&&maxPageNumber!=""&&typeof(maxPageNumber)!="undefined"){
            if(parseInt(pageNumber)>parseInt(maxPageNumber)){
                return;
            }
        }
        sunday_notice_pageNumber =pageNumber;
        sunday_notice_pageSize =pageSize==null?sunday_notice_pageSize:pageSize;
        sunday_notice_getData(true);
    }
    function sunday_notice_getData(isRemove){
        var t="#sunday_notice_div";

        if(sunday_notice_is_loading||sunday_notice_is_end)return;
        if(isRemove){
            $("#sunday_notice_table").remove();
            $(".com-page-wrapper").remove();
        }
        sunday_notice_is_loading=true;
        //layer.load(1, {shade: [0.5, '#fff']});
        //layer.load(4, {shade: [0.8, '#393D49']})

        //同步请求
        $.ajax({
            type: "POST",
            url: "/sunday/web/notice/select",
            data: {
                "pageSize":sunday_notice_pageSize,
                "pageNumber":sunday_notice_pageNumber,
                "sort":sunday_notice_sort,
                "order":sunday_notice_order,
                "guardIds":sunday_notice_guardId,
                "bunchIds":sunday_notice_bunchId,
                "status":sunday_notice_status,
                "name":sunday_notice_name


            },
            dataType: "text",
            async:true,
            success: function(result){
                //d layer.closeAll('loading');
                //返回的是html
                if(result==null||result==""||result.length<1){
                    sunday_notice_is_end=true;
                }else{
                    $(t).after(result);
                    sunday_notice_is_loading=false;
                }
            }
        });
    }
    function sunday_notice_save(id){
        newTab("/sunday/web/notice/input?id=" + id , "新增/编辑");
    }

    function sunday_notice_delete(id) {
         var msg = "确认要删除这条信息吗?";
         layer.confirm(msg, function (index) {
             layer.load(4, {shade: [0.8, '#393D49']})
             //不要异步操作
             $.ajax({
                 type: "post",
                 url: "/sunday/web/notice/delete",
                 data: {"id": id},
                 async: false,
                 success: function (data) {
                     //关闭加载
                     layer.closeAll('loading');
                     if (data.code == 0) {
                         sunday_notice_search(false);
                     } else {
                         layer.msg('删除失败', {
                             icon: 2
                         });
                     }
                 }
             });
             //最后手动关闭
             layer.close(index);

         });
     }

     function sunday_notice_record_index(id) {
         newTab("/sunday/web/notice/record/index?noticeId=" + id , "查看浏览历史");
     }
</script>