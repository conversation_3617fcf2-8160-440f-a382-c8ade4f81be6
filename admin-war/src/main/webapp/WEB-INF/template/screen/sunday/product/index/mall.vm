#set( $layout = "/layout/easyui/h-layout.vm")
<body>
<div class="wrapper wrapper-content animated fadeInRight">
    <div class="row">
        <div class="col-sm-12" id="tableDiv">
            <div class="ibox float-e-margins">
                <div class="ibox-content">
                    <div class="col-sm-12 no-padding">
                        <button type="button" onclick="sunday_product_save(0)" class="btn btn-success btn-sm" ><i class="fa fa-plus"></i>&nbsp;新增商品</button>
                         <button type="button" onclick="sunday_product_import()" class="btn btn-primary btn-sm" ><i class="fa fa-plus"></i>&nbsp;导入(只支持.xls)</button>
                       
                       

                    </div>

                    <form role="form" class="form-inline" id="sunday_product_search_form">
                       
                            <input type="hidden"  name="type"  value="1" >
                             <input type="hidden"  name="memberId"  value="$!{memberId}" >
                        <div class="form-group form-form-group" > 
                            <label>商品名称：</label>
                            <input type="text" placeholder="请输入名称" name="name" class="form-control input-sm">
                        </div>
                        <div class="form-group form-form-group">
                           <label >SKU：</label>
                           <input type="text" placeholder="请输入商品SKU" name="sku" class="form-control input-sm">
                        </div>
                        <div class="form-group form-form-group">
                            <label >商品类目：</label>
                            <input class="form-control"   readonly="readonly" type="text" name="categoryName" required="required"
                            onclick="sunday_product_category_choose_open('sunday_product_category_choose_callBack')"">
                            <input type="hidden" name="categoryId" value="$!{product.categoryId}">
                        </div>
                            <button class="btn btn-success btn-sm" type="button" onclick="sunday_product_search()"><i class="fa fa-search"></i>&nbsp;搜索</button>
                       

                    </form>
                    <!--商品table-->
                    <table id="sunday_product_table" class="table table-striped"></table>
                </div>
            </div>
        </div>
    </div>
</div>
<!--分类选择器-->
<div id="sunday_product_category_store_div" class="modal fade" role="dialog" style="display: none;" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
        </div>
    </div>
</div>

<!-- <div id="sunday_product_view_div" class="modal fade" role="dialog" style="display: none;" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
        </div>
    </div>
</div> -->
<!--商品导入-->
<div id="sunday_product_import_div" class="modal fade" role="dialog" style="display: none;" aria-hidden="true">      
    <div class="modal-dialog" >
        <div class="modal-content">
                 <!--modald的header。存放标题什么的-->
                  <form id="sunday_product_import_form" class="form-horizontal" action="/sunday/web/product/product_import"  method="post"  enctype="multipart/form-data">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal">×</button>
                        <h4 class="modal-title">商品导入管理</h4>
                    </div>
                      <!--modald的body。存放form里面的数据什么的-->
                    <div class="modal-body">
                       <div class="row form-horizontal">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">excel文件：</label>
                                    <div class="col-sm-8">
                                       <input  class="form-control "  name="excelFile" type="file"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                         
                    </div>
                
                    <!--modald的footer。存放按钮神马的-->
                    <div class="form-group text-center">
                            <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                            <button  class="btn btn-primary" type="submit">保存</button>
                             
                    </div>
                </form>
        </div>
    </div>    
</div>
</body>
<!--分类选择器-->
<script type="text/javascript">
//打开分类 选择器
    function sunday_product_category_choose_open(sunday_product_category_choose_callBack) {
        remoteModal("sunday_product_category_store_div","/sunday/web/product/category/choose?isSingle=1&callBack="+sunday_product_category_choose_callBack)
    }

    //分类选择器回掉    
    function sunday_product_category_choose_callBack(selectedRow) {
        //2017年11月21日。必须选择二级分类
        if(selectedRow.parentId==0){
            layer.msg("必须选择二级分类", { icon: 6});
            return
        }
        $("#sunday_product_search_form").form("load",
                {"categoryId":selectedRow.id,
                    "categoryName":selectedRow.name,
                });
        closeModal('sunday_product_category_store_div');
    }
</script>>
<script>
    $("#sunday_product_table").bootstrapTable({
        columns: [
            { checkbox: true,},
            {field : 'number',title : '商品条码',width:100,align:"center"},
            {field : 'name',title : '商品名称',width:200,align:"center"},
            {field : 'sku',title : 'SKU',width:150,align:"center"},
            {field : 'categoryName',title : '所属类目',width:100,align:"center"},
            {field : 'marketPrice',title : '市场价',width:100,align:"center"},
            {field : 'price',title : '销售价',width:100,align:"center"},

            {field:'ewm',title:'二维码',width:'100', align: "center",
                    formatter : function(value, row, index) {
                    //if(value!=null&&value!=""){
                        var src=" http://qr.liantu.com/api.php?bg=FFFFFF&fg=ff0000&gc=222222&el=l&w=200&m=10&text=$!{mobileRoot}/DrBike/site/product/detail?productId="+row.id;
                        //	return "<a target='_blank' href='"+value"'><img src='"+value+"' style='width:100px;height:100px'/></a>";
                        return "<a target='_blank'  href='"+src+"'><img src='"+src+"' style='width:50px;height:50px' /></a>";
                    //}
               // return "";
                }
            },

            {field : 'operate',title : '操作',width:250,align:'center',
                formatter : function(value, row, index) {
                    var operate="&nbsp;&nbsp;&nbsp<button class='btn btn-xs btn-warning' type='button' onclick='sunday_product_save("+row.id+")'>编辑</button>";
                    /*operate+="&nbsp;&nbsp;&nbsp<button class='btn btn-success btn-xs' type='button' onclick='sunday_product_cookbook_manage("+row.id+")'>关联菜谱</button>";*/

                    operate+="&nbsp;&nbsp;&nbsp<button class='btn btn-danger btn-xs' type='button' onclick='sunday_product_delete("+row.id+")'>删除</button>";

                    //设置推荐
                    /*if(row.isIndexRecommend==0){
                         operate+="&nbsp;&nbsp;&nbsp<button class='btn btn-primary btn-xs' type='button' onclick='sunday_product_set_recommend("+row.id+",1,1)'>设置首页推荐</button>";    
                    }
                    if(row.isIndexRecommend==1){
                         operate+="&nbsp;&nbsp;&nbsp<button class='btn btn-primary btn-xs' type='button' onclick='sunday_product_set_recommend("+row.id+",0,1)'>取消首页推荐</button>";    
                    }*/
                    //上下架
                    if(row.status==0){
                         operate+="&nbsp;&nbsp;&nbsp<button class='btn btn-success btn-xs' type='button' onclick='sunday_product_change("+row.id+",1)'>上架</button>";    
                    }
                    if(row.status==1){
                         operate+="&nbsp;&nbsp;&nbsp<button class='btn btn-danger btn-xs' type='button' onclick='sunday_product_change("+row.id+",0)'>下架</button>";    
                    }
                    return operate;
                }
            }],
        url:'/sunday/web/product/select',
        cache:false,
        pagination: true,

        pageSize: 50,
        pageList: [50, 100,150],
        pageNumber:1,
        sidePagination: 'server',
        queryParams: function (params) {
            $('#sunday_product_search_form').find('input[name]').each(function () {
                // //params
                var name=$(this).attr('name');
                var value=$(this).val();
                if(value==null||value==""){
                    value=null;
                }
                params[name]=value;

            });
            //实例化组件-自带参数
            params['offset']=params.offset;
            params['limit']=params.limit;
            params['sort']=params.sort;
            params['order']=params.order;

            return params;
        },
        onClickRow:function (row, $element, field){
                $element.addClass("row-select");
                $element.siblings('tr').removeClass("row-select");
        }
    });
  
    function sunday_product_search() {
        $("#sunday_product_table").bootstrapTable("refresh");
    }

    //新增
    function sunday_product_save(id){
        parent.newTab('/sunday/web/product/input?memberId=$!{memberId}&type=1&id='+id,'商品管理');
        //remoteModal("sunday_product_div","/sunday/web/product/input?id=0")
    }

    function sunday_product_import(){
        $("#sunday_product_import_form").form("clear");
        remoteModal("sunday_product_import_div",null);
    }
   var validatorMenu=$("#sunday_product_import_form").validate({
        submitHandler: function(form) {
            //默认
            //layer.load();
            //加遮罩层
            layer.load(4, {shade: [0.8, '#393D49']})
           
           
        $(form).ajaxSubmit({
           
            //你表单提交就要切换页面，ajax加载又是异步的。两边同时进行，
            //当ajax加载在切换页面之前完成，就会执行回调，反之不会执行
            //加同步提交数据
                async: false,
                success:function(data){
                    //关闭加载
                       layer.closeAll('loading');
                    if(data.code ==0){
                        
                        //刷新列表
                         sunday_product_search();
                         //关闭窗口
                         $("#sunday_product_import_div").modal("toggle");

                    }else{
                        layer.msg('保存失败', {
                            icon: 6
                        });
                    }
                }
            });
        }
    }) ;
    function sunday_product_delete(productId) {
        var msg="确认要删除这条商品吗?";
        layer.confirm(msg, function(index){
            layer.load(4, {shade: [0.8, '#393D49']})
            //不要异步操作
            $.ajax({
                type : "post",
                url : "/sunday/web/product/delete",
                data : {"id":productId},
                async : false,
                success : function(data){
                    //关闭加载
                    layer.closeAll('loading');
                    if(data.code == 0){
                        sunday_product_search()

                    }else{
                        layer.msg('删除失败', {
                            icon: 2
                        });
                    }


                }
            });
            //最后手动关闭
            layer.close(index);

        });
    }
    //上下架
    function sunday_product_change(id,status){
        layer.load(4, {shade: [0.8, '#393D49']})
            //不要异步操作
            $.ajax({
                type : "post",
                url : "/sunday/web/product/change",
                data : {"id":id,"status":status},
                async : false,
                success : function(data){
                    //关闭加载
                    layer.closeAll('loading');
                    if(data.code == 0){
                        sunday_product_search()

                    }else{
                        layer.msg('上/下架商品失败', {
                            icon: 2
                        });
                    }


                }
            });
    }
    function sunday_product_process() {
        var msg="同步pos和APP的商品:名称，规格，itemCode。此操作非常消耗系统资源，请耐心等待";
        layer.confirm(msg, function(index){
            layer.load(4, {shade: [0.8, '#393D49']})
            //不要异步操作
            $.ajax({
                type : "post",
                url : "/sunday/web/pos/processProductToApp",
                data : {},
                async : false,
                success : function(data){
                    //关闭加载
                    layer.closeAll('loading');
                    if(data.code == 0){
                        sunday_product_search()
                    }else{
                        layer.msg('同步失败', {
                            icon: 2
                        });
                    }


                }
            });
            //最后手动关闭
            layer.close(index);

        });
    }
</script>

