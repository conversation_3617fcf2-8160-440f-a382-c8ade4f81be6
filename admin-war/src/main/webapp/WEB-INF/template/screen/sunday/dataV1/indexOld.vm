#parse("/screen/sunday/operate.vm")

<!--自定义控件颜色-->
<style>
    .laydate-theme-xxx .layui-this{
        background-color: #477cff
    };
    .laydate-theme-xxx .laydate-selected{
        background-color: #d9e3ff
    }
</style>

<div class="data-report">
    <!--数据概况-->
    <div class="search-wrapper clearfix" id="sunday_data0_count_div">
        <div class="com-title-wrapper">
            <div class="title">数据概况</div>
        </div>

    </div>
    <script>
        $.ajaxSetup({
            async: false
        });
        function sunday_data0_search() {

            $("#sunday_data0_count_result_div").remove();
            $.ajax({
                type: "POST",
                url: "/sunday/web/data/count0",
                data: {},
                dataType: "text",
                async: false,
                success: function (data) {
                    $("#sunday_data0_count_div").append(data);
                }
            });
        }
        sunday_data0_search();
    </script>
    <!--应用概况-->
    <!--今日，昨天，30日的数据，3条数据-->
    <div class="search-wrapper clearfix" id="sunday_data1_count_div">
        <div class="com-title-wrapper">
            <div class="title">应用概况</div>
        </div>
    </div>
    <script>
        function sunday_data1_search() {

            $("#sunday_data1_count_result_div").remove();
            $.post("/sunday/web/data/count1",
                    {},
                    function (data) {
                        $("#sunday_data1_count_div").append(data);
                    })
        }
      sunday_data1_search();
    </script>

    <!--时段分析-->
<div class="search-wrapper clearfix">
    <div class="com-title-wrapper">
        <div class="title">时段分析</div>
    </div>

    <div class="charts-wrapper">
        <div class="charts-button-wrapper clearfix" id="sunday_data2_search_type_div">
            <div class="charts-button-item on" value="1">新用户</div>
            <div class="charts-button-item" value="2">活跃用户</div>
            <div class="charts-button-item" value="3">启动次数</div>
        </div>
        <div class="charts-content">
            <div id="sunday_data2_count_result_chart" style="min-width:400px;height:400px"></div>
        </div>
    </div>
</div>
    <script>
        $("#sunday_data2_search_type_div").children().click(function () {
            $(this).addClass("on");
            $(this).siblings().removeClass("on");
            sunday_data2_search()
        })

        function sunday_data2_search() {

            var queryType =  $("#sunday_data2_search_type_div").children(".on").attr("value");
            $.ajax({
                type: "POST",
                url: "/sunday/web/data/count2",
                data: {"queryType":queryType},
                dataType: "json",
                async: false,
                success: function (data) {
                    var categories = data.result.categories;
                    categories=eval(categories);
                    var series = data.result.series;
                    series=eval(series);
                    var chart = Highcharts.chart('sunday_data2_count_result_chart', {
                        /*chart: {
                            type: 'areaspline'
                        },*/
                        title: {
                            text: ''
                        },
                        xAxis: {
                            categories: categories
                        },
                        yAxis: {
                            title: {
                                text: ''
                            },
                            allowDecimals:false

                        },
                        series: series
                    });
                    $(".highcharts-credits").remove();
                    $(".highcharts-exporting-group").remove();
                }
            });
        }
       sunday_data2_search();
    </script>





    <!--总体数据-->
    <div class="search-wrapper clearfix">
        <div class="com-title-wrapper">
            <div class="title">总体数据</div>
        </div>
        <div class="clearfix" id="sunday_data100_count_div">
            <div class="item clearfix fl">
                <div class="com-titl">开始时间：</div>
                <div class="com-time-item fl">
                    <input type="text"  value="$!ct_start" id="sunday_data100_search_ct_start">
                </div>
            </div>
            <div class="item clearfix fl">
                <div class="com-titl">结束时间：</div>
                <div class="com-time-item fl">
                    <input type="text"  value="$!ct_end"  id="sunday_data100_search_ct_end">
                </div>
            </div>

            <button type="button" class="com-button item fl" onclick="sunday_data100_search()">搜索</button>
            <a href="javascript:sunday_data100_daochu();" class="com-button return item fl sunday_data_daochu" >导出</a>
        </div>
    </div>
    <script>
        laydate.render({
            elem: '#sunday_data100_search_ct_start' //指定元素
            ,format: 'yyyy-MM-dd'
            ,theme: 'xxx'
        });
        laydate.render({
            elem: '#sunday_data100_search_ct_end' //指定元素
            ,format: 'yyyy-MM-dd'
            ,theme: 'xxx'
        });
        function sunday_data100_search() {
            var ct_start = $("#sunday_data100_search_ct_start").val();
            var ct_end = $("#sunday_data100_search_ct_end").val();
            if(ct_start==null || ct_start == "" || ct_end == null ||  ct_end == "" ){
                layer.msg("请选择开始和结束日期", {
                    icon: 2
                });
                return ;
            }
            $("#sunday_data100_count_result_div").remove();
            $.ajax({
                type: "POST",
                url: "/sunday/web/data/count100",
                data:   {"ct_start":ct_start,"ct_end":ct_end},
                dataType: "text",
                async: false,
                success: function (data) {
                    $("#sunday_data100_count_div").after(data);
                }
            });
        }
        sunday_data100_search();

        function  sunday_data100_daochu() {
            var ct_start = $("#sunday_data100_search_ct_start").val();
            var ct_end = $("#sunday_data100_search_ct_end").val();
            if(ct_start==null || ct_start == "" || ct_end == null ||  ct_end == "" ){
                layer.msg("请选择开始和结束日期", {
                    icon: 2
                });
                return ;
            }
            $.ajax({
                type: "POST",
                url: "/sunday/web/data/daochu100",
                data:   {"ct_start":ct_start,"ct_end":ct_end},
                dataType: "json",
                async: false,
                success: function (data) {
                    if (data.code == 0) {
                        window.open(data.result);
                    } else {
                        layer.msg('导出失败', {
                            icon: 2
                        });
                    }
                }
            });
        }
    </script>




    <!--园区总体数据-->
    <div class="search-wrapper clearfix">
        <div class="com-title-wrapper">
            <div class="title">园区总体数据</div>
        </div>
        <div class="clearfix" id="sunday_data101_count_div">

            <div class="item clearfix fl">
                <div class="com-titl">开始时间：</div>
                <div class="com-time-item fl">
                    <input type="text"  value="$!ct_start" id="sunday_data101_search_ct_start">
                </div>
            </div>
            <div class="item clearfix fl">
                <div class="com-titl">结束时间：</div>
                <div class="com-time-item fl">
                    <input type="text"  value="$!ct_end"  id="sunday_data101_search_ct_end">
                </div>
            </div>

            <button type="button" class="com-button item fl" onclick="sunday_data101_search()">搜索</button>
            <a href="javascript:sunday_data101_daochu();" class="com-button return item fl sunday_data_daochu" >导出</a>
        </div>


    </div>
    <script>
        laydate.render({
            elem: '#sunday_data101_search_ct_start' //指定元素
            ,format: 'yyyy-MM-dd'
            ,theme: 'xxx'
        });
        laydate.render({
            elem: '#sunday_data101_search_ct_end' //指定元素
            ,format: 'yyyy-MM-dd'
            ,theme: 'xxx'
        });
        function sunday_data101_search() {
            var ct_start = $("#sunday_data101_search_ct_start").val();
            var ct_end = $("#sunday_data101_search_ct_end").val();
            if(ct_start==null || ct_start == "" || ct_end == null ||  ct_end == "" ){
                layer.msg("请选择开始和结束日期", {
                    icon: 2
                });
                return ;
            }
            $("#sunday_data101_count_result_div").remove();
            $.ajax({
                type: "POST",
                url: "/sunday/web/data/count101",
                data:   {"ct_start":ct_start,"ct_end":ct_end},
                dataType: "text",
                async: false,
                success: function (data) {
                    $("#sunday_data101_count_div").after(data);
                }
            });
        }
        sunday_data101_search();

        function  sunday_data101_daochu() {
            var ct_start = $("#sunday_data101_search_ct_start").val();
            var ct_end = $("#sunday_data101_search_ct_end").val();
            if(ct_start==null || ct_start == "" || ct_end == null ||  ct_end == "" ){
                layer.msg("请选择开始和结束日期", {
                    icon: 2
                });
                return ;
            }
            $.ajax({
                type: "POST",
                url: "/sunday/web/data/daochu101",
                data:   {"ct_start":ct_start,"ct_end":ct_end},
                dataType: "json",
                async: false,
                success: function (data) {
                    if (data.code == 0) {
                        window.open(data.result);
                    } else {
                        layer.msg('导出失败', {
                            icon: 2
                        });
                    }
                }
            });
        }
    </script>

    <!--日均访问数据-->
    <div class="search-wrapper clearfix">
        <div class="com-title-wrapper">
            <div class="title">日均访问数据（访问人数UV  访问次数PV）</div>
        </div>
        <div class="clearfix">

            <div class="item clearfix fl">
                <div class="com-titl">省市区：</div>
                <div class="selectpicker" >
                    <select id="sunday_data102_search_provinceId"></select>
                </div>
                <div class="selectpicker" style="margin-left: 10px">
                    <select id="sunday_data102_search_cityId"></select>
                </div>
                <div class="selectpicker" style="margin-left: 10px">
                    <select id="sunday_data102_search_districtId"></select>
                </div>
            </div>
            <div class="item clearfix fl">
                <div class="com-titl">园区选择：</div>
                <div class="selectpicker">
                    <select id="sunday_data102_search_guardId"></select>
                </div>
            </div>
            <div class="item clearfix fl">
                <div class="com-titl">开始时间：</div>
                <div class="com-time-item fl">
                    <input type="text"  value="$!ct_start" id="sunday_data102_search_ct_start">
                </div>
            </div>
            <div class="item clearfix fl">
                <div class="com-titl">结束时间：</div>
                <div class="com-time-item fl">
                    <input type="text"  value="$!ct_end"  id="sunday_data102_search_ct_end">
                </div>
            </div>

            <button type="button" class="com-button item fl" onclick="sunday_data102_search()">搜索</button>
            <a href="javascript:sunday_data102_daochu();" class="com-button return item fl sunday_data_daochu" >导出</a>
        </div>
        <div class="charts-wrapper">
            <div class="charts-button-wrapper clearfix" id="sunday_data102_search_type_div">
                <div class="charts-button-item on" value="0">登录页</div>
                <div class="charts-button-item" value="1">首页</div>
                <div class="charts-button-item" value="3">教学计划</div>
                <div class="charts-button-item" value="4">一周英语</div>
                <div class="charts-button-item" value="5">一周食谱</div>
                <div class="charts-button-item" value="6">宝贝作品集</div>
                <div class="charts-button-item" value="7">一日作息</div>
                <div class="charts-button-item" value="8">园区简介</div>
                <div class="charts-button-item" value="9">宝贝画面</div>
                <div class="charts-button-item" value="10">通知</div>
                <div class="charts-button-item" value="11">班级圈</div>
                <div class="charts-button-item" value="12">我的</div>
            </div>
            <div class="charts-content">
                <div id="sunday_data102_count_result_chart" style="min-width:400px;height:400px"></div>
            </div>
        </div>
    </div>
    <script>
        laydate.render({
            elem: '#sunday_data102_search_ct_start' //指定元素
            ,format: 'yyyy-MM-dd'
            ,theme: 'xxx'
        });
        laydate.render({
            elem: '#sunday_data102_search_ct_end' //指定元素
            ,format: 'yyyy-MM-dd'
            ,theme: 'xxx'
        });
        intAreaNoTown("sunday_data102_search_provinceId","sunday_data102_search_cityId","sunday_data102_search_districtId",null,null,null);
        initGuard("sunday_data102_search_guardId",null,"sunday_data102_search_provinceId","sunday_data102_search_cityId","sunday_data102_search_districtId");
        //2018年10月22日，省市区控件，控制园区选项
        $("#sunday_data102_search_provinceId,#sunday_data102_search_cityId,#sunday_data102_search_districtId").change(function(){
            initGuard("sunday_data102_search_guardId",null,"sunday_data102_search_provinceId","sunday_data102_search_cityId","sunday_data102_search_districtId");
        })
        $("#sunday_data102_search_type_div").children().click(function () {
            $(this).addClass("on");
            $(this).siblings().removeClass("on");
            sunday_data102_search()
        })
        function sunday_data102_search() {
            var ct_start = $("#sunday_data102_search_ct_start").val();
            var ct_end = $("#sunday_data102_search_ct_end").val();
            var guardId = $("#sunday_data102_search_guardId option:selected").val();
            var queryType =  $("#sunday_data102_search_type_div").children(".on").attr("value");
            if(ct_start==null || ct_start == "" || ct_end == null ||  ct_end == "" ){
                layer.msg("请选择开始和结束日期", {
                    icon: 2
                });
                return ;
            }
            $.ajax({
                type: "POST",
                url: "/sunday/web/data/count102",
                data:     {"ct_start":ct_start,"ct_end":ct_end,"guardId":guardId,"queryType":queryType},
                dataType: "json",
                async: false,
                success: function (data) {
                    var categories = data.result.dates;
                    categories=eval(categories)
                    var series = data.result.series;
                    series=eval(series)


                    var chart = Highcharts.chart('sunday_data102_count_result_chart', {
                        /*chart: {
                            type: 'areaspline'
                        },*/
                        title: {
                            text: '$!result.title'
                        },
                        xAxis: {
                            categories: categories
                        },
                        yAxis: {
                            title: {
                                text: ''
                            },allowDecimals:false
                        },
                        /*tooltip: {
                            shared: true,
                            valueSuffix: ' 单位'
                        },*/

                        series: series
                    });

                    $(".highcharts-credits").remove();
                    $(".highcharts-exporting-group").remove();
                }
            })

        }
        sunday_data102_search();

        function  sunday_data102_daochu() {
            var ct_start = $("#sunday_data102_search_ct_start").val();
            var ct_end = $("#sunday_data102_search_ct_end").val();
            var guardId = $("#sunday_data102_search_guardId option:selected").val();
            if(ct_start==null || ct_start == "" || ct_end == null ||  ct_end == "" ){
                layer.msg("请选择开始和结束日期", {
                    icon: 2
                });
                return ;
            }
            $.ajax({
                type: "POST",
                url: "/sunday/web/data/daochu102",
                data:   {"ct_start":ct_start,"ct_end":ct_end,"guardId":guardId},
                dataType: "json",
                async: false,
                success: function (data) {
                    if (data.code == 0) {
                        window.open(data.result);
                    } else {
                        layer.msg('导出失败', {
                            icon: 2
                        });
                    }
                }
            });
        }
    </script>
    <!--周活，月活-->
    <div class="search-wrapper clearfix">
        <div class="com-title-wrapper">
            <div class="title">周/月活跃数据</div>
        </div>
        <div class="clearfix">

            <div class="item clearfix fl">
                <div class="com-titl">省市区：</div>
                <div class="selectpicker" >
                    <select id="sunday_data103_search_provinceId"></select>
                </div>
                <div class="selectpicker" style="margin-left: 10px">
                    <select id="sunday_data103_search_cityId"></select>
                </div>
                <div class="selectpicker" style="margin-left: 10px">
                    <select id="sunday_data103_search_districtId"></select>
                </div>
            </div>
            <div class="item clearfix fl">
                <div class="com-titl">园区选择：</div>
                <div class="selectpicker">
                    <select id="sunday_data103_search_guardId"></select>
                </div>
            </div>
            <div class="item clearfix fl">
                <div class="com-titl">年：</div>
                <div class="selectpicker">
                    <select id="sunday_data103_search_year"></select>
                </div>
            </div>
            <div class="item clearfix fl">
                <div class="com-titl">月：</div>
                <div class="selectpicker">
                    <select id="sunday_data103_search_month"></select>
                </div>

            </div>
            <button type="button" class="com-button item fl" onclick="sunday_data103_search()">搜索</button>
            <a href="javascript:sunday_data103_daochu();" class="com-button return item fl sunday_data_daochu" >导出</a>
        </div>
        <div class="charts-wrapper">
            <div class="charts-content">
                <div id="sunday_data103_count_result_chart" style="min-width:400px;height:400px"></div>
            </div>
        </div>
    </div>
    <script>

        intAreaNoTown("sunday_data103_search_provinceId","sunday_data103_search_cityId","sunday_data103_search_districtId",null,null,null);
        initGuard("sunday_data103_search_guardId",null,"sunday_data103_search_provinceId","sunday_data103_search_cityId","sunday_data103_search_districtId");
        //2018年10月22日，省市区控件，控制园区选项
        $("#sunday_data103_search_provinceId,#sunday_data103_search_cityId,#sunday_data103_search_districtId").change(function(){
            initGuard("sunday_data103_search_guardId",null,"sunday_data103_search_provinceId","sunday_data103_search_cityId","sunday_data103_search_districtId");
        })

        //实例化年、月
        var dictionary_url ="$!dictionaryRoot";
        iniSelect("sunday_data103_search_year",dictionary_url+"year","$!year");
        iniSelect("sunday_data103_search_month",dictionary_url+"month","$!month");


        function sunday_data103_search() {
            var year = $("#sunday_data103_search_year option:selected").val();
            var month = $("#sunday_data103_search_month option:selected").val();
            var guardId = $("#sunday_data103_search_guardId option:selected").val();
            if(year==null || year == "" || month == null ||  month == "" ){
                layer.msg("请选择年月", {
                    icon: 2
                });
                return ;
            }
            $.ajax({
                type: "POST",
                url: "/sunday/web/data/count103",
                data:     {"year":year,"month":month,"guardId":guardId},
                dataType: "json",
                async: false,
                success: function (data) {
                    var categories = data.result.categories;
                    categories=eval(categories)
                    //console.info(categories);
                    var series = data.result.series;
                    series=eval(series)


                    var chart = Highcharts.chart('sunday_data103_count_result_chart', {
                        /*chart: {
                            type: 'areaspline'
                        },*/
                        title: {
                            text: '$!result.title'
                        },
                        xAxis: {
                            categories: categories
                        },
                        yAxis: {
                            title: {
                                text: ''
                            },
                            allowDecimals:false
                        },

                        series: series
                    });

                    $(".highcharts-credits").remove();
                    $(".highcharts-exporting-group").remove();
                }
            })

        }
        sunday_data103_search();

        function  sunday_data103_daochu() {
            var year = $("#sunday_data103_search_year option:selected").val();
            var month = $("#sunday_data103_search_month option:selected").val();
            var guardId = $("#sunday_data103_search_guardId option:selected").val();
            if(year==null || year == "" || month == null ||  month == "" ){
                layer.msg("请选择年月", {
                    icon: 2
                });
                return ;
            }

            $.ajax({
                type: "POST",
                url: "/sunday/web/data/daochu103",
                data:     {"year":year,"month":month,"guardId":guardId},
                dataType: "json",
                async: false,
                success: function (data) {
                    if (data.code == 0) {
                        window.open(data.result);
                    } else {
                        layer.msg('导出失败', {
                            icon: 2
                        });
                    }
                }
            });
        }
    </script>
</div>