#parse("/screen/sunday/operate.vm")
<table width="100%"  border="0" cellspacing="0" cellpadding="0" id="sunday_qinzi_bunch_table">
    <tr>
        <th width="6%">
            <div class="com-checkbox-wrapper" style="margin-left: 12px"><div class="com-checkbox-item bunch-select-all-checkbox" >全选</div></div>
        </th>
        <th width="10%">班级名称</th>
        <th width="5%">班级人数</th>
        <th width="10%">教师</th>
        <th width="10%">所在园区</th>
        <th width="6%">所属课程</th>
        <th width="10%">开班日期</th>
        <th width="15%">上课时间</th>
        <th>操作</th>
    </tr>
    #foreach($!bunch in $!bunches)

        <tr>
            <td>
                <div class="com-checkbox-wrapper" style="margin-left: 12px"><div class="com-checkbox-item bunch-select-checkbox" value="$!bunch.id">&nbsp;</div></div>
            </td>
            <td>$!bunch.name</td>
            <td>
                #if($!bunch.bunchQinzi.totalNumber < $!bunch.bunchQinzi.fullNumber)
                    <font color="#FF9800">
                        $!bunch.bunchQinzi.totalNumber/$!bunch.bunchQinzi.fullNumber
                    </font>
                #else
                    <font color="#E51C23">
                        $!bunch.bunchQinzi.totalNumber/$!bunch.bunchQinzi.fullNumber
                    </font>
                #end
            </td>
            <td>
                #if(!$!bunch.teacherNames || $!bunch.teacherNames == '')
                    <font style="color: red">未填写</font>
                #else
                    $!bunch.teacherNames
                #end
            </td>
            <td>$!bunch.guardName</td>
            <td>$!bunch.lessonName</td>
            #if(!$!bunch.bunchQinzi.openDateLong)
                <td style="color: red">未填写</td>
            #else
                <td class="timeToString">$!bunch.bunchQinzi.openDateLong</td>
            #end
            <td>
                <div>
                    #if($!bunch.status ==1)
                        #if($!bunch.bunchQinzi.scheduleIntervals.size()<=0)
                            <div title="点击编辑课时" style="cursor: pointer;color: #27cdd7" onclick="sunday_schedule_edit($!bunch.id,1)">点击编辑</div>
                           #* <div title="点击编辑课时" style="cursor: pointer;color: #27cdd7" onclick="sunday_schedule_edit($!bunch.id)">点击编辑</div>*#
                        #else
                            <div title="点击编辑课时" onclick="sunday_schedule_edit($!bunch.id,1)">
                                #foreach($!interval in $!bunch.bunchQinzi.scheduleIntervals)
                                    <p class="bunch-info-p" style="margin-top: 10px;cursor:pointer;">$!{interval}</p>
                                #end
                            </div>
                        #end

                        #else
                            #foreach($!interval in $!bunch.bunchQinzi.scheduleIntervals)
                                <p class="bunch-info-p" style="margin-top: 10px;cursor:pointer;">$!{interval}</p>
                            #end
                    #end
                </div>
            </td>
            <td>
                #if($!bunch.status ==1)
                    #if($!bunch.bunchQinzi.totalNumber < $!bunch.bunchQinzi.fullNumber)
                        <button type="button" onclick="addStudent(true,'$!bunch.id','$!bunch.lessonId','$!bunch.bunchQinzi.fullNumber','$!bunch.bunchQinzi.totalNumber','$!bunch.guardId','$!bunch.teacherIds')" class="sunday_qinzi_bunch_add_student">新增学生</button>
                    #else
                        <button disabled style="background-color: #CCCCCC" type="button" onclick="addStudent(true,'$!bunch.id','$!bunch.lessonId','$!bunch.bunchQinzi.fullNumber','$!bunch.bunchQinzi.totalNumber','$!bunch.guardId','$!bunch.teacherIds')" class="sunday_qinzi_bunch_add_student">新增学生</button>
                    #end
                    <button type="button" onclick="sunday_bunch_detail($!bunch.id)" class="sunday_qinzi_bunch_detail">详情</button>
                    <button type="button" onclick="sunday_student_batch_daochu($!bunch.guardId,'$!bunch.id')" class="sunday_qinzi_bunch_daochu">导出花名册</button>
                    <button type="button" onclick="sunday_bunch_edit($!bunch.id,2)" class="sunday_qinzi_bunch_edit">编辑</button>
                    <button type="button" onclick="sunday_bunch_delete($!bunch.id)" class="sunday_qinzi_bunch_delete">下线</button>
                #else
                    <button type="button" onclick="sunday_bunch_detail($!bunch.id)" class="sunday_qinzi_bunch_detail">详情</button>
                    <button type="button" onclick="sunday_bunch_online($!bunch.id)" class="sunday_qinzi_bunch_delete">上线</button>
                #end
            </td>
        </tr>
    #end
</table>
<div class="com-page-wrapper">
    <span class="page-num">共$!total条，$!currentPage/$!totalPage页</span>
    <!--判断是否能够转跳页数-->
        <span class="next pre #if($!prevPage >= $!minPage) on" onclick=sunday_bunch_search_page($!prevPage,null) #end"></span>
        <span class="next #if($!maxPage >= $!nextPage) on" onclick=sunday_bunch_search_page($!nextPage,null) #end"></span>
    <span class="text">跳转到：</span>
    <input type="text" value="$!currentPage" min="$!minPage" max="$!maxPage">
    <span class="go" onclick="sunday_bunch_search_page($(this).prev().val(),null,$!maxPage)">GO</span>
</div>
<script>
    $(".timeToString").each(function () {
        var timestamp  = $(this).text();
        var time = new Date(parseInt(timestamp));
        var year = time.getFullYear();
        var month = time.getMonth()+1;
        var date = time.getDate();
        $(this).text(year+'-'+add0(month)+'-'+add0(date));
    })

    function add0(text) {
        if(parseInt(text)<10){
            text = "0" + text;
        }
        return text;
    }

    $(".bunch-select-all-checkbox").click(function () {
        if($(this).hasClass("on")){
            $(this).removeClass("on");
            $(".bunch-select-checkbox").removeClass("on");
        }else{
            $(this).addClass("on");
            $(".bunch-select-checkbox").addClass("on");
        }
    })

    $(".bunch-select-checkbox").click(function () {
        if($(this).hasClass("on")){
            $(this).removeClass("on");
        }else {
            $(this).addClass("on");
            $(this).siblings().removeClass("on");
        }
    })
</script>