#parse("/screen/sunday/operate.vm")
<style>
    /*不显示秒*/
    .layui-laydate-content>.laydate-time-list {
        padding-bottom: 0px;
        overflow: hidden;
    }
    .layui-laydate-content>.laydate-time-list>li{
        width:50%
    }

    .merge-box .scrollbox .merge-list {
        padding-bottom: 5px;
    }
</style>
<div class="student-management">
    <div class="search-wrapper clearfix" style="margin-top: 20px">
        <form action="" method="">
            <div class="com-title-wrapper">
                <div class="title">编辑课表</div>
            </div>
            #*<div>
                <div class="search-time-item">
                    <input type="text" style="width:240px;margin-left:-15px;" placeholder="开始日期" name="beginTime" id="sunday_schedule_search_beginTime" value="$!param.beginTime">
                </div>
            </div>
            <div >
                <div class="search-time-item">
                    <input type="text" style="width:240px;margin-left:-15px;" placeholder="截止日期" name="endTime" id="sunday_schedule_search_endTime" value="$!param.endTime">
                </div>
            </div>*#

            <button type="button" class="com-button item fl mr16" style="margin-left: 16px" onclick="newTab('/sunday/web/bunch/qinziIndex',null)">返回班级</button>
            <a href="javascript:addSchedule(0)" class="com-add-button item fl sunday_schedule_add" >新增课表</a>
        </form>
    </div>
    <!-- 查询 -->

#*    <button class="updateTypeButton online-button batch_delete">批量删除</button>*#
    <button class="updateTypeButton online-button batch_stop" onclick="batchStop()">批量停课</button>
    <div class="list" id="sunday_bunch_schedule_div">
        <div class="com-menu-wrapper clearfix fl" id="sunday_schedule_search_form_status_div">
            <div class="com-menu-item  #if($!status == 1) on #end" value="1">重复</div>
            <div class="com-menu-item  #if($!status == 0) on #end" value="0" >单次</div>
        </div>
        #*<a href="javascript:sunday_category_save(0)" class="com-add-button item fl sunday_schedule_add" >新增课表</a>*#
    </div>

    <div id="sunday_updateSchedule_div" class="modal fade" role="dialog" style="display: none;" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog" style="width: 720px">
            <div class="modal-content">
                <form id="sunday_updateSchedule_form" autocomplete="off" class="form-horizontal" action="" method="post" enctype="multipart/form-data">
                    <input type="hidden" name="id" id="schedule_id" value="0"/>
                    <input type="hidden" name="repetitionData" id="student_schedule_repetitionData"/>
                    <input type="hidden" name="state"/>
                    <input type="hidden" name="fatherId" id="form_fatherId">
                    <input type="hidden" name="fatherType" id="form_fatherType">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal"></button>
                        <h4 class="modal-title" id="schedule-title">课表</h4>
                    </div>
                    <!--modald的body。存放form里面的数据什么的-->
                    <div class="modal-body">
                        <div class="row form-horizontal"></div>
                        <div class="row form-horizontal">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label"><font color="#FF0000">*</font>选择班级：</label>
                                    <div class="col-sm-4">
                                        <div class="selectpicker" style="width: 100%">
                                            <select id="sunday_schedule_form_guardId"  name="guardId" disabled required="required" ></select>
                                        </div>
                                    </div>
                                    <div class="col-sm-4">
                                        <div class="selectpicker" style="width: 100%">
                                            <select id="sunday_schedule_form_bunchId"   name="bunchId"  disabled required="required" ></select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row form-horizontal">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label"><font color="#FF0000">*</font>上课时间：</label>
                                    <div class="col-sm-4">
                                        <div class="search-time-item" style="margin-left: 0px">
                                            <input type="text" placeholder="开始时间" readonly name="beginTime" id="sunday_schedule_save_form_beginTime" required="required">
                                        </div>
                                    </div>
                                    <div class="col-sm-4 endTime" style="display: none">
                                        <div class="search-time-item" style="margin-left: 0px">
                                            <input type="text"  placeholder="结束时间" readonly name="endTime" id="sunday_schedule_save_form_endTime" required="required">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row form-horizontal">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label"><font color="#FF0000">*</font>上课时段：</label>
                                    <div class="col-sm-4">
                                        <div class="search-time-item" style="margin-left: 0px">
                                            <input type="text"  placeholder="请选择时段" readonly name="startInterval" id="sunday_schedule_save_form_startInterval_2" required="required" value="">
                                        </div>
                                    </div>
                                    <div class="col-sm-4">
                                        <div class="search-time-item" style="margin-left: 0px">
                                            <input type="text"  placeholder="请选择时段" readonly name="endInterval" id="sunday_schedule_form_endInterval_2" required="required" value="">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row form-horizontal">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label"><font color="#FF0000">*</font>重复方式：</label>
                                    <div class="col-sm-8">
                                        <div class="selectpicker" style="width: 100%">
                                            <select id="sunday_schedule_form_repetitionWay" name="repetitionWay" required="required" >
                                                <option value="0">不重复</option>
                                                <option value="1">每周重复</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row form-horizontal repetitionWay" style="display: none">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label"><font color="#FF0000">*</font>重复设置：</label>
                                    <div class="col-sm-8">
                                        <label class="checkbox-inline">
                                            <input type="checkbox" class="inlineCheckbox" value="all">全部
                                        </label>
                                        #set($weeks = ['周一','周二','周三','周四','周五','周六','周日'])
                                        #foreach($!checkday in[0..6])
                                            <label class="checkbox-inline">
                                                <input type="checkbox" class="inlineCheckbox" value="$!checkday">$!{weeks.get($!{checkday})}
                                            </label>
                                        #end

                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row form-horizontal">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">上课主题：</label>
                                    <div class="col-sm-8">
                                        <input class="form-control" type="text" name="scheduleTheme" id="sunday_schedule_form_theme">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-group text-center">
                        <button type="button" class="btn btn-default" id="closeButton" data-dismiss="modal">关闭</button>
                        <button class="btn btn-primary" type="button" onclick="saveSchedule()">保存</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<style>
/*    .sunday_schedule_add{
        float: right;
        margin-top: 11px;
        margin-right: 10px;
    }*/
    .updateTypeButton{
        padding: 0 9px;
        margin: 0 0 0 16px;
        line-height: 30px;
        font-size: 14px;
        background: #27cdd7;
        color: #fff;
        border-radius: 4px;
        border: none;
        outline: none;
        margin-top: 20px;
        margin-left: 0px;
    }
</style>
<script>

    function getDate() {
        var time = new Date();
        var year = time.getFullYear();
        var month = time.getMonth()+1;
        var date = time.getDate();
        return year+'-'+add0(month)+'-'+add0(date);
    }


    laydate.render({
        elem: '#sunday_attend_search_beginTime' //指定元素
        ,format: 'yyyy-MM-dd'
        ,min: 0
    });
    laydate.render({
        elem: '#sunday_attend_search_endTime' //指定元素
        ,format: 'yyyy-MM-dd'
    });


    laydate.render({
        elem: '#sunday_schedule_save_form_beginTime' //指定元素
        ,format: 'yyyy-MM-dd'
    });

    laydate.render({
        elem: '#sunday_schedule_save_form_endTime' //指定元素
        ,format: 'yyyy-MM-dd'
    });

    laydate.render({
        elem: '#sunday_schedule_save_form_startInterval_2' //指定元素
        ,type: 'time'
        ,min: '07:00:00'
        ,max: '21:00:00'
        ,format: 'HH:mm'
        ,btns: ['clear', 'confirm']
    });

    laydate.render({
        elem: '#sunday_schedule_form_endInterval_2' //指定元素
        ,type: 'time'
        ,min: '07:00:00'
        ,max: '21:00:00'
        ,format: 'HH:mm'
        ,btns: ['clear', 'confirm']
    });


    $("#sunday_schedule_search_form_status_div").children().click(function(){
        $(this).addClass("on");
        $(this).siblings().removeClass("on");
        sunday_bunch_schedule_status = $(this).attr("value");
        if(sunday_bunch_schedule_status == 1){
            $(".batch_stop").hide();
        }else{
            $(".batch_stop").show();
        }

        sunday_category_search(true);
    })


    var param_pageNumber = "$!param.pageNumber";
    var param_pageSize = "$!param.pageSize";
    console.log("param_pageNumber="+param_pageNumber+",param_pageSize="+param_pageSize)
    if(param_pageNumber==null|| param_pageNumber==""){
        param_pageNumber=1
    }
    if(param_pageSize==null|| param_pageSize==""){
        param_pageSize=10;
    }


    var sunday_bunch_schedule_pageNumber=param_pageNumber;
    var sunday_bunch_schedule_pageSize=param_pageSize;
    var sunday_bunch_schedule_sort="id";
    var sunday_bunch_schedule_order="desc";
    var sunday_bunch_schedule_bunchId="$!bunch.id";
    var sunday_bunch_schedule_status="$!status";


    if(sunday_bunch_schedule_status == 1){
        $(".batch_stop").hide();
    }

    var sunday_bunch_schedule_is_loading=false;
    var sunday_bunch_schedule_is_end=false;
    //输入订单号查询
    function sunday_category_search(isPageNumber){
        sunday_bunch_schedule_is_loading=false;
        sunday_bunch_schedule_is_end=false;
        if(isPageNumber){
            sunday_bunch_schedule_pageNumber =1;
        }
        sunday_category_getData(true);
    }
    function sunday_category_search_page(pageNumber,pageSize){
        sunday_bunch_schedule_pageNumber =pageNumber;
        sunday_bunch_schedule_pageSize =pageSize==null?sunday_bunch_schedule_pageSize:pageSize;
        sunday_category_getData(true);
    }

    function sunday_category_getData(isRemove){
        var t="#sunday_bunch_schedule_div";

        if(sunday_bunch_schedule_is_loading||sunday_bunch_schedule_is_end)return;
        if(isRemove){
            $("#sunday_schedule_table").remove();
            $(".com-page-wrapper").remove();
        }
        sunday_bunch_schedule_is_loading=true;
        //layer.load(1, {shade: [0.5, '#fff']});
        //layer.load(4, {shade: [0.8, '#393D49']})

        //同步请求
        $.ajax({
            type: "GET",
            url: "/sunday/web/schedule/selectScheduleByBunchId",
            data: {
                "pageSize":sunday_bunch_schedule_pageSize,
                "pageNumber":sunday_bunch_schedule_pageNumber,
                "sort":sunday_bunch_schedule_order,
                "bunchId":sunday_bunch_schedule_bunchId,
                "status":sunday_bunch_schedule_status,
            },
            dataType: "text",
            async:true,
            success: function(result){
                //d layer.closeAll('loading');
                //返回的是html
                if(result==null||result==""||result.length<1){
                    sunday_bunch_schedule_is_end=true;
                }else{
                    if(isRemove){
                        $("#sunday_schedule_table").remove();
                        $(".com-page-wrapper").remove();
                    }
                    $(t).append(result);
                    sunday_bunch_schedule_is_loading=false;
                }
            }
        });
    }

    sunday_category_search(false);


    var validatorMenu = $("#sunday_category_form").validate({

        submitHandler: function (form) {
            //加遮罩层
            layer.load(4, {shade: [0.8, '#393D49']})
            $(form).ajaxSubmit({

                //你表单提交就要切换页面，ajax加载又是异步的。两边同时进行，
                //当ajax加载在切换页面之前完成，就会执行回调，反之不会执行
                //加同步提交数据
                async: false,
                dataType: "json",
                success: function (data) {
                    //关闭加载
                    layer.closeAll('loading');
                    if (data.code == 0) {
                        sunday_category_search(true);
                        closeModal("sunday_category_div");
                    } else {
                        layer.msg(data.message, {
                            icon: 6
                        });
                    }
                }
            });
        }
    });

    function sunday_category_delete(id) {
        var msg = "确认要删除该课类吗?";
        layer.confirm(msg, function (index) {
            layer.load(4, {shade: [0.8, '#393D49']})
            //不要异步操作
            $.ajax({
                type: "post",
                url: "/sunday/web/schedule/deleteCategory",
                data: {"categoryIds": id},
                async: false,
                success: function (data) {
                    //关闭加载
                    layer.closeAll('loading');
                    if (data.code == 0) {
                        sunday_category_search(false);
                    } else {
                        layer.msg(data.message, {
                            icon: 2
                        });
                    }
                }
            });
            //最后手动关闭
            layer.close(index);

        });
    }
    
    
    //编辑单次课程
    function editSchedule(id,dayStatus,state) {
        if(dayStatus == 0 || dayStatus == '0' || !dayStatus){
            layer.msg("该课表日期已过,无法编辑!", {
                icon: 2
            });
            return ;
        }
        if(state == 1){
            layer.msg("停课状态下无法编辑!", {
                icon: 2
            });
            return ;
        }
        var schedule = getByScheduleId(id);
        console.log(schedule);
        addSchedule(id,schedule);
    }
    
    //编辑重复课程
    function editReSchedule(id,endTimeLong) {
        if(new Date(parseInt(endTimeLong))<new Date()){
            layer.msg("课程截止日期已过,无法编辑!", {
                icon: 2
            });
            return ;
        }
        var msg = "编辑重复课表,将会改变今天以后的课表计划,确定编辑?";
        layer.confirm(msg, function (index) {
            var schedule = getByScheduleId(id);
            console.log(schedule);
            addSchedule(id,schedule);
            //最后手动关闭
            layer.close(index);
        });
    }
    
    

    //停课
    function stopSchedule(id,status) {
        if(status == 0 || status == '0' || !status){
            layer.msg("该课表日期已过,无法停用!", {
                icon: 2
            });
            return ;
        }
        var msg = "确认将该课表停课吗?";
        layer.confirm(msg, function (index) {
            layer.load(4, {shade: [0.8, '#393D49']})
            //不要异步操作
            $.ajax({
                type: "post",
                url: "/sunday/web/schedule/stopSchedule",
                data: {"scheduleId": id},
                async: false,
                success: function (data) {
                    //关闭加载
                    layer.closeAll('loading');
                    if (data.code == 0) {
                        sunday_category_getData(true);
                    } else {
                        layer.msg(data.message, {
                            icon: 2
                        });
                    }
                }
            });
            //最后手动关闭
            layer.close(index);
        });
    }

    //批量停课
    function batchStop() {
        var scheduleIds = [];
        $(".schedule-select-checkbox").each(function () {
            if($(this).hasClass("on")){
                var id = $(this).attr("value");
                scheduleIds.push(id);
            }
        })

        scheduleIds = scheduleIds.join(",");
        if(!scheduleIds || scheduleIds == ''){
            layer.msg("请勾选所要停用的课表!", {
                icon: 2
            });
            return ;
        }


        var msg = "确认将这些课表停课?";
        layer.confirm(msg, function (index) {
            layer.load(4, {shade: [0.8, '#393D49']})
            //不要异步操作
            $.ajax({
                type: "post",
                url: "/sunday/web/schedule/batchStopSchedule",
                data: {"scheduleIds": scheduleIds},
                async: false,
                success: function (data) {
                    //关闭加载
                    layer.closeAll('loading');
                    if (data.code == 0) {
                        sunday_category_getData(true);
                    } else {
                        layer.msg(data.message, {
                            icon: 2
                        });
                    }
                }
            });
            //最后手动关闭
            layer.close(index);
        });
    }


    //恢复
    function recoverSchedule(id,status) {
        if(status == 0 || status == '0' || !status){
            layer.msg("该课表日期已过,无法恢复!", {
                icon: 2
            });
            return ;
        }
        var msg = "确认将该课表恢复?";
        layer.confirm(msg, function (index) {
            layer.load(4, {shade: [0.8, '#393D49']})
            //不要异步操作
            $.ajax({
                type: "post",
                url: "/sunday/web/schedule/recoverSchedule",
                data: {"scheduleId": id},
                async: false,
                success: function (data) {
                    //关闭加载
                    layer.closeAll('loading');
                    if (data.code == 0) {
                        sunday_category_getData(true);
                    } else {
                        layer.msg(data.message, {
                            icon: 2
                        });
                    }
                }
            });
            //最后手动关闭
            layer.close(index);
        });
    }

    function deleteSchedule(id,endTime) {
        var datetime = endTime+" 00:00:00";
        var timestamp1 = new Date(datetime), timestamp2 = new Date();
        var d = timestamp1.getTime() - timestamp2.getTime();
        if(d<=0){
            layer.msg("无法删除当天或当天之前的课表!", {
                icon: 2
            });
            return;
        }
        var msg = "确认将该课表删除?";
        layer.confirm(msg, function (index) {
            layer.load(4, {shade: [0.8, '#393D49']})
            //不要异步操作
            $.ajax({
                type: "post",
                url: "/sunday/web/schedule/deleteSchedule",
                data: {"scheduleId": id},
                async: false,
                success: function (data) {
                    //关闭加载
                    layer.closeAll('loading');
                    if (data.code == 0) {
                        sunday_category_getData(true);
                    } else {
                        layer.msg(data.message, {
                            icon: 2
                        });
                    }
                }
            });
            //最后手动关闭
            layer.close(index);
        });

    }

    function deleteFatherSchedule(id,endTime) {

        var datetime = endTime+" 00:00:00";
        var timestamp1 = new Date(datetime), timestamp2 = new Date();
        var d = timestamp1.getTime() - timestamp2.getTime();
        if(d<=0){
            layer.msg("无法删除当天或当天之前的课表!", {
                icon: 2
            });
            return;
        }

        var msg = "确定删除该课表,删除该课表只会删除今天以后的课表?";
        layer.confirm(msg, function (index) {
            layer.load(4, {shade: [0.8, '#393D49']})
            //不要异步操作
            $.ajax({
                type: "post",
                url: "/sunday/web/schedule/deleteFatherSchedule",
                data: {"scheduleId": id},
                async: false,
                success: function (data) {
                    //关闭加载
                    layer.closeAll('loading');
                    if (data.code == 0) {
                        sunday_category_getData(true);
                    } else {
                        layer.msg(data.message, {
                            icon: 2
                        });
                    }
                }
            });
            //最后手动关闭
            layer.close(index);
        });
    }

    function addSchedule(id,schedule) {
        $("#sunday_updateSchedule_form").form("clear");
        $("#sunday_schedule_form_repetitionWay").attr("disabled",false);
        $(".repetitionWay").hide();
        $(".endTime").hide();
        remoteModal("sunday_updateSchedule_div", "");
        if(id == 0){
            $("#sunday_updateSchedule_form").form("load", {"id":0,"startInterval":"","endInterval":"","repetitionWay":0});
            $("#sunday_schedule_save_form_beginTime").attr("disabled",false);
            $("#sunday_schedule_save_form_startInterval_2").attr("disabled",false);
            $("#sunday_schedule_form_endInterval_2").attr("disabled",false);
        }else{

            schedule.beginTime = getTime(schedule.beginTimeLong);
            schedule.endTime = getTime(schedule.endTimeLong);
            if(schedule.fatherType == 1){

                $("#sunday_updateSchedule_form").form("load", schedule);
                $("#sunday_schedule_form_repetitionWay").attr("disabled",true);
                $(".repetitionWay").show();
                $(".endTime").show();

                var repetitionData = JSON.parse(schedule.repetitionData);
                for(var i=0;i<repetitionData.length;i++){
                    if(repetitionData[i] == 1){
                        $(".inlineCheckbox").not(":first").eq(i).prop("checked",true);
                    }
                }
            }else{

                $("#sunday_updateSchedule_form").form("load", schedule);
                console.log(schedule.beginTime);
                console.log(1212121);
                if(schedule.beginTime == getDate(new Date().getTime())){
                    $("#sunday_schedule_save_form_beginTime").attr("disabled",true);
                    $("#sunday_schedule_save_form_startInterval_2").attr("disabled",true);
                    $("#sunday_schedule_form_endInterval_2").attr("disabled",true);
                }else{
                    $("#sunday_schedule_save_form_beginTime").attr("disabled",false);
                    $("#sunday_schedule_save_form_startInterval_2").attr("disabled",false);
                    $("#sunday_schedule_form_endInterval_2").attr("disabled",false);
                }
                $("#sunday_schedule_form_repetitionWay").attr("disabled",true);
            }

        }

        $("#sunday_updateSchedule_div").modal({show:true,backdrop:'static'})
        initGuardAndqinZiBunch("sunday_schedule_form_guardId","sunday_schedule_form_bunchId",'$!bunch.guardId','$!bunch.id');
        $("#sunday_schedule_form_guardId").attr("disabled",true);
        $("#sunday_schedule_form_bunchId").attr("disabled",true);
    }




    function initGuardAndqinZiBunch(H, A, B, E, D, F, C) {
        var I = "/sunday/web/guard/selectNoPage";
        var G = "/sunday/web/bunch/selectQinziBunchNoPage";
        $("#" + H).unbind("change");
        $("#" + H).children().remove();
        $("#" + A).children().remove();
        $("#" + H).append("<option value=''>请选择园区</option>");
        $.post(I, {
                    "provinceId": $("#" + D).val(),
                    "cityId": $("#" + F).val(),
                    "districtId": $("#" + C).val()
                },
                function(L) {
                    var J = L.result;
                    for (var K = 0; K < J.length; K++) {
                        if (typeof(B) != "undefined" && B != null && B != "" && B == J[K].id) {
                            $("#" + H).append("<option selected value='" + J[K].id + "'>" + J[K].name + "</option>")
                        } else {
                            $("#" + H).append("<option  value='" + J[K].id + "'>" + J[K].name + "</option>")
                        }
                    }
                    $("#" + H).change(function() {
                        $("#" + A).children().remove();
                        $("#" + A).append("<option value=''>请选择班级</option>");
                        if ($(this).val() != "") {
                            $.post(G, {
                                        "guardId": $(this).val()
                                    },
                                    function(N) {
                                        var O = N.result;
                                        for (var M = 0; M < O.length; M++) {
                                            if (typeof(E) != "undefined" && E != null && E != "" && E == O[M].id) {
                                                $("#" + A).append("<option selected value='" + O[M].id + "' valueType='" + O[M].type + "' valueCategory='" + O[M].category + "'>" + O[M].name + "</option>")
                                            } else {
                                                $("#" + A).append("<option value='" + O[M].id + "' valueType='" + O[M].type + "' valueCategory='" + O[M].category + "' >" + O[M].name + "</option>")
                                            }
                                        }
                                    })
                        }
                    });
                    $("#" + H).change()
                })
    }

    function add0(text) {
        if(parseInt(text)<10){
            text = "0"+text;
        }
        return text;
    }

    //重复选择
    $("#sunday_schedule_form_repetitionWay").change(function () {
        var value = $(this).val();
        if(value == 0){
            $(".repetitionWay").hide();
            $(".endTime").hide();
        }else{
            $(".repetitionWay").show();
            $(".endTime").show();
            $(".inlineCheckbox").first().click(function () {
                var value = $(this).prop("checked");
                if(value){
                    $(".inlineCheckbox").not(":first").prop("checked",true);
                }else{
                    $(".inlineCheckbox").not(":first").prop("checked",false);
                }
            })
        }
    })

    function getTime(timeLong) {
        var time = new Date(parseInt(timeLong));
        var year = time.getFullYear();
        var month = time.getMonth()+1;
        var date = time.getDate();
        var t = year+'-'+add0(month)+'-'+add0(date);

        return t;
    }

    function getByScheduleId(id) {
        var param = {};
        $.ajax({
            type: "GET",
            url: "/sunday/web/schedule/getScheduleById",
            data: {"scheduleId": id},
            async: false,
            success: function (data) {
                //关闭加载
                layer.closeAll('loading');
                if (data.code == 0) {
                    param = data.result;
                } else {
                    layer.msg(data.message, {
                        icon: 2
                    });
                }
            }
        });
        return param;
    }
    
    
    function saveSchedule() {
        $("#sunday_schedule_form_repetitionWay").attr("disabled",false);
        $("#sunday_schedule_form_guardId").attr("disabled",false);
        $("#sunday_schedule_form_bunchId").attr("disabled",false);
        $("#sunday_schedule_save_form_startInterval_2").attr("disabled",false);
        $("#sunday_schedule_form_endInterval_2").attr("disabled",false);
        var schedule = $("#sunday_updateSchedule_form").serializeArray();

        var param = {};
        $.each(schedule, function() {
            param[this.name] = this.value;
        });


        var disabled = $("#sunday_schedule_save_form_beginTime").attr("disabled");

        var now_flag = false;
        if(disabled == "disabled"){
            now_flag = true;
            $("#sunday_schedule_save_form_beginTime").attr("disabled",false);
        }
        if(!now_flag){
            if(!param.beginTime || new Date(param.beginTime)<new Date()){
                layer.tips("请输入大于今天的日期!",'#sunday_schedule_save_form_beginTime',{
                    tips: [2, '#3595CC'],time:2000});
                return ;
            }
        }

        if(param.id == 0){
            $("#sunday_updateSchedule_form").attr("action",'/sunday/web/schedule/saveSch');

            if(!param.startInterval || !param.endInterval ||param.endInterval<=param.startInterval){
                layer.msg("上课时段不正常!", {
                    icon: 2
                });
                return ;
            }

            if(param.repetitionWay == 0){
                $("#sunday_schedule_save_form_endTime").val(param.beginTime);
                $("#student_schedule_repetitionData").val("");
                $("#form_fatherType").val(0);
                $("#form_fatherId").val(0);
            }else{
                if(new Date(param.endTime)<new Date()){
                    layer.tips("请输入大于今天的日期!",'#sunday_schedule_save_form_endTime',{
                        tips: [2, '#3595CC'],time:2000});
                    return ;
                }


                if(!param.endTime || new Date(param.endTime)<new Date(param.beginTime)){
                    layer.tips("日期不正确!",'#sunday_schedule_save_form_endTime',{
                        tips: [2, '#3595CC'],time:2000});
                    return;
                }

                var data = [0,0,0,0,0,0,0];
                $(".inlineCheckbox").not(":first").each(function () {
                    var flag = $(this).prop("checked");
                    if(flag){
                        var value = $(this).val();
                        data[parseInt(value)] = 1;
                    }
                })
                if($.inArray(1, data) == -1){
                    layer.msg("请勾选日期!", {
                        icon: 2
                    });
                    return ;
                }
                $("#student_schedule_repetitionData").val(JSON.stringify(data));

            }
        }
        else{
            if(param.repetitionWay == 0){
                $("#sunday_updateSchedule_form").attr("action",'/sunday/web/schedule/editScheduleByOnce');
            }else{
                $("#sunday_updateSchedule_form").attr("action",'/sunday/web/schedule/editSchedule');
                if(new Date(param.endTime)<new Date()){
                    layer.tips("请输入大于今天的日期!",'#sunday_schedule_save_form_endTime',{
                        tips: [2, '#3595CC'],time:2000});
                    return ;
                }
                if(new Date(param.endTime)<new Date(param.beginTime)){
                    layer.tips("日期不正确!",'#sunday_schedule_save_form_endTime',{
                        tips: [2, '#3595CC'],time:2000});
                    return;
                }

                var data = [0,0,0,0,0,0,0];
                $(".inlineCheckbox").not(":first").each(function () {
                    var flag = $(this).prop("checked");
                    if(flag){
                        var value = $(this).val();
                        data[parseInt(value)] = 1;
                    }
                })
                if($.inArray(1, data) == -1){
                    layer.msg("请勾选日期!", {
                        icon: 2
                    });
                    return ;
                }
                $("#student_schedule_repetitionData").val(JSON.stringify(data));
            }
        }
        $("#sunday_updateSchedule_form").ajaxSubmit({
            dataType: "json",
            success: function (data) {
                //关闭加载
                layer.closeAll('loading');
                if (data.code == 0) {
                    closeModal("sunday_updateSchedule_div");
                    $(".modal-backdrop").remove();
                } else {
                    layer.msg(data.message, {
                        icon: 6
                    });
                }
            }
        });
        console.log(param);
    }


    $('#sunday_updateSchedule_div').on('hide.bs.modal', function () {
        sunday_category_getData(true);
    });
</script>