#parse("/screen/sunday/operate.vm")
<div class="text-img audio-frequency video-management">
    <div class="top-wrapper clearfix">
        <script>
            $.post("/sunday/web/material/top",
                    {"type":"$!type"},
                    function (data) {
                        $(".top-wrapper").append(data);
                    })
        </script>
    </div>

    <div class="con">

        <div class="num">
            <span id="sunday_material_search_form_msg">音频共 2 条</span>
            <a href="javascript:sunday_material_save(0);" class="com-add-button item fr sunday_material_add">新建音频</a>
        </div>


        <div class="list" id="sunday_material_div">

        </div>


    </div>
    <script>
        //  layer.load(2, {shade: [0.8, '#393D49'],content:$('#shangchuan')})
        var param_pageNumber = "$!param.pageNumber";
        var param_pageSize = "$!param.pageSize";
        console.log("param_pageNumber="+param_pageNumber+",param_pageSize="+param_pageSize)
        if(param_pageNumber==null|| param_pageNumber==""){
            param_pageNumber=1
        }
        if(param_pageSize==null|| param_pageSize==""){
            param_pageSize=50
        }
        var sunday_material_pageNumber=param_pageNumber;
        var sunday_material_pageSize=param_pageSize;
        var sunday_material_sort="id";
        var sunday_material_order="desc";
        var sunday_material_is_loading=false;
        var sunday_material_is_end=false;
        var sunday_material_name="";
        //输入订单号查询
        function sunday_material_search(isPageNumber){
            sunday_material_name = $("#sunday_material_search_form_name").val();
            sunday_material_is_loading=false;
            sunday_material_is_end=false;
            if(isPageNumber){
                sunday_material_pageNumber =1;
            }
            sunday_material_getData(true);
        }
        function sunday_material_search_page(pageNumber,pageSize,maxPageNumber){
            if(maxPageNumber!=null&&maxPageNumber!=""&&typeof(maxPageNumber)!="undefined"){
                if(parseInt(pageNumber)>parseInt(maxPageNumber)){
                    return;
                }
            }
            sunday_material_pageNumber =pageNumber;
            sunday_material_pageSize =pageSize==null?sunday_material_pageSize:pageSize;
            sunday_material_getData(true);
        }
        function sunday_material_getData(isRemove){
            var t="#sunday_material_div";
            if(sunday_material_is_loading||sunday_material_is_end)return;
            if(isRemove){
                $(t).children().remove();
            }
            sunday_material_is_loading=true;
            //同步请求
            $.ajax({
                type: "POST",
                url: "/sunday/web/material/select",
                data: {
                    "pageSize":sunday_material_pageSize,
                    "pageNumber":sunday_material_pageNumber,
                    "sort":sunday_material_sort,
                    "order":sunday_material_order,
                    "type":"$!type",
                    "name":sunday_material_name,
                },
                dataType: "text",
                async:true,
                success: function(result){
                    //返回的是html
                    if(result==null||result==""||result.length<1){
                        sunday_material_is_end=true;
                    }else{
                        $(t).append(result);
                        sunday_material_is_loading=false;

                    }
                }
            });
        }
        sunday_material_search(false);
        function sunday_material_save(id){
            newTab("/sunday/web/material/input?id=" + id+"&type=$!type", "新增/编辑");
        }


        function sunday_material_delete(id) {
            var msg = "确认要删除选中的素材吗?";
            layer.confirm(msg, function (index) {
                layer.load(4, {shade: [0.8, '#393D49']})
                //不要异步操作
                $.ajax({
                    type: "post",
                    url: "/sunday/web/material/delete",
                    data: {"id": id},
                    async: false,
                    success: function (data) {
                        //关闭加载
                        layer.closeAll('loading');
                        if (data.code == 0) {
                          //  newTab('/sunday/web/material/index?type=$!type"',null)
                            sunday_material_search(false);
                        } else {
                            layer.msg('删除失败', {
                                icon: 2
                            });
                        }
                    }
                });
                //最后手动关闭
                layer.close(index);

            });
        }
        function sunday_material_down(source) {
            window.open(source)
        }
    </script>

