<div class="student-management">
    <div class="search-wrapper clearfix">
        <form action="" method="">
            <div class="com-title-wrapper">
                <div class="title">条件筛选</div>
            </div>
                <button type="button" class="com-button item fl mr16" style="margin-left: 10px" onclick="newTab('/sunday/web/notice/index?isClean=0')">返回园区通知</button>
        </form>
    </div>
    <!-- 查询 -->

    <div class="list" id="sunday_notice_record_div">

    </div>

</div>
<script>


    var param_pageNumber = "$!param.pageNumber";
    var param_pageSize = "$!param.pageSize";
    console.log("param_pageNumber="+param_pageNumber+",param_pageSize="+param_pageSize)
    if(param_pageNumber==null|| param_pageNumber==""){
        param_pageNumber=1
    }
    if(param_pageSize==null|| param_pageSize==""){
        param_pageSize=50
    }

    var sunday_notice_record_pageNumber=param_pageNumber;
    var sunday_notice_record_pageSize=param_pageSize;
    var sunday_notice_record_sort="id";
    var sunday_notice_record_order="desc";
    var sunday_notice_record_is_loading=false;
    var sunday_notice_record_is_end=false;
    //输入订单号查询
    function sunday_notice_record_search(){
        sunday_notice_record_is_loading=false;
        sunday_notice_record_is_end=false;
        //sunday_notice_record_pageNumber =1;
        // sunday_notice_record_pageSize =1;
        sunday_notice_record_getData(true);
    }
    function sunday_notice_record_search_page(pageNumber,pageSize){
        sunday_notice_record_pageNumber =pageNumber;
        sunday_notice_record_pageSize =pageSize==null?sunday_notice_record_pageSize:pageSize;
        sunday_notice_record_getData(true);
    }
    function sunday_notice_record_getData(isRemove){
        var t="#sunday_notice_record_div";

        if(sunday_notice_record_is_loading||sunday_notice_record_is_end)return;
        if(isRemove){
           $(t).children().remove();
        }
        sunday_notice_record_is_loading=true;
        //layer.load(1, {shade: [0.5, '#fff']});
        //layer.load(4, {shade: [0.8, '#393D49']})

        //同步请求
        $.ajax({
            type: "POST",
            url: "/sunday/web/notice/record/select",
            data: {
                "pageSize":sunday_notice_record_pageSize,
                "pageNumber":sunday_notice_record_pageNumber,
                "sort":sunday_notice_record_sort,
                "order":sunday_notice_record_order,
                "noticeId":"$!noticeId"
            },
            dataType: "text",
            async:true,
            success: function(result){
                //d layer.closeAll('loading');
                //返回的是html
                if(result==null||result==""||result.length<1){
                    sunday_notice_record_is_end=true;
                }else{
                    $(t).append(result);
                    sunday_notice_record_is_loading=false;
                }
            }
        });
    }

    sunday_notice_record_search();
    function sunday_notice_record_delete(id) {
        var msg = "是否确认删除这条记录";
        layer.confirm(msg, function (index) {
            layer.load(4, {shade: [0.8, '#393D49']})
            //不要异步操作
            $.ajax({
                type: "post",
                url: "/sunday/web/notice/record/delete",
                data: {"id": id},
                async: false,
                success: function (data) {
                    //关闭加载
                    layer.closeAll('loading');
                    if (data.code == 0) {
                        sunday_notice_record_search()
                    } else {
                        layer.msg('删除失败', {
                            icon: 2
                        });
                    }
                }
            });
            //最后手动关闭
            layer.close(index);

        });
    }
</script>