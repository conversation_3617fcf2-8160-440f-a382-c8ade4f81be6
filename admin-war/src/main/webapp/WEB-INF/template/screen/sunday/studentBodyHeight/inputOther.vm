#parse("/screen/sunday/operate.vm")
<style>
    .student-body-addspecial{
        background: #fff;
    }
    .form-modal-body-k .item {
        margin-top: 20px;
    }
    .form-modal-body-k .com-titl{
        width: 120px;
    }
    .form-modal-body-k textarea{
        width: 500px;
        padding: 6px 12px;
    }
    .bb1{
        border-bottom: 1px solid #eee;
    }
    .space20{

        padding: 20px;
    }
    .student-body-addspecial .com-input-item{
        min-width: 500px;
    }
    .student-body-addspecial .com-item{
        line-height: 30px;
    }
    .student-body-addspecial .button-wrapper {
        min-width: 900px;
        width: 70%;
        text-align: center;
        font-size: 0;
        padding: 20px 0;
    }.student-body-addspecial  .button-wrapper .com-button {
         display: inline-block;
         vertical-align: top;
         margin: 0 10px;
     }
    .search-selectpicker {
        float: left;
        margin-left:0;
        width: 240px;
    }
    .item label{
        font-weight: normal;
        margin-bottom: 0;
        display: block;float: left;
        line-height: 30px;
        cursor: pointer;
    }
    .item label+label{
        margin-left: 15px;
    }
    .item label input{
        display: block;float: left;
        margin-top: 8px;
        line-height: 30px;
        margin-right: 5px ;
    }
    .item label var{
        font-style: normal;
        line-height: 30px;
        display: block;float: left;;
    }
    .form-modal-body-k textarea{
        width: 240px;
    }
   .modal .com-item{
        line-height: 30px;
    }
    .error-tip{
        color:red;
    }
</style>
<div class="student-body-addspecial">

    <div class="space20">
        <div class="head">晨午检特别情况</div>
    </div>
    <div class="item clearfix" >
        <div class="button-wrapper fl" style="width: 96.5%;text-align: right;padding:0; ">
            <a href="javascript:newTab('/sunday/web/studentBody/index?isClean=1',null);" class="com-button return">返回</a>
        </div>
    </div>
    <div class="space20">
        <form id="sunday_material_form" class="form-horizontal form-modal-body-k" action="/sunday/web/material/save" method="post"  enctype="multipart/form-data">
            <div class="item clearfix">
                <div class="com-titl">学生姓名：</div>
                <div class="com-item fl">$!bodyDetail.studentName ($!bodyDetail.bunchName)</div>
            </div>
            <div class="item clearfix">
                <div class="com-titl">过敏源：</div>
                <input onblur="save()" type="text" maxlength="30" class="com-input-item fl" id="addspecial_anaphylactogen"  name="shortName" value="$!bodyDetail.anaphylactogen" required="required">
            </div>
            <div class="item clearfix">
                <div class="com-titl">备注：</div>
                <textarea onblur="save()" name="shortName" maxlength="200"  id="addspecial_anaphylactogenRemarks" rows="4">$!bodyDetail.anaphylactogenRemarks</textarea>
            </div>
        </form>


    </div>

    <div class="head bb1">
        <div class="space20">传染病记录
            <div class="fr">
                <button type="submit" class="com-button sunday_plan_add sunday_plan_edit" onclick="add_illness_showmodal()">添加传染病记录</button>
            </div>
        </div>
    </div>
    <div class="q" style="height: 400px;overflow: scroll;">
        <table width="100%"  border="0" cellspacing="0" cellpadding="0" id="sunday_course_div">
            <tr>
                <th width="14%">传染病名称</th>
                <th width="14%">就诊医院</th>
                <th width="14%">诊断日期</th>
                <th width="14%">处置</th>
                <th width="14%">是否痊愈</th>
                <th >操作</th>
            </tr>
                #foreach($!item in $!diseases)
                <tr>
                    <td>$!item.diseaseName</td>
                    <td>$!item.hospital</td>
                    <td>$!item.recordTime</td>
                    <td>$!item.handle</td>
                    <td>
                        #if($!item.cured==1)是#elseif($!item.cured==0)否#end
                    </td>
                    <td>
                        <button type="button" onclick="add_illness_showmodal('$!item.diseaseName','$!item.hospital',
                            '$!item.recordTime','$!item.handle','$!item.cured','$!item.id')" class="">编辑</button>
                        <button type="button" onclick="show_illness_log_del($!item.id)" class="">删除</button>
                    </td>
                </tr>
            #end
        </table>
    </div>

    <div class="item clearfix" style="display: none">
        <div class="com-titl" style="height: 30px;"></div>
        <div class="button-wrapper fl">
            <a href="javascript:yuhua_return('/sunday/web/studentBody/index?isClean=1');" class="com-button return">返回</a>
            <button type="button" onclick="sub()"  class="com-button sunday_plan_add sunday_plan_edit">保存</button>
        </div>
    </div>
</div>





<!--显示-->
<div id="sunday_evaluation_div_ill" class="modal fade" role="dialog" style="display: none;" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">

            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal"></button>
                <h4 class="modal-title">传染病记录</h4>
            </div>
            <!--modald的body。存放form里面的数据什么的-->
            <div class="modal-body" id="sunday_body_ill_text">
                <form id="sunday_material_form" class="form-horizontal form-modal-body-k"
                     method="post"  enctype="multipart/form-data">
                    <input type="hidden" id="studentbody_id">
                    <div class="item clearfix">
                        <div class="com-titl">学生姓名：</div>
                        <div class="com-item fl">$!bodyDetail.studentName ($!bodyDetail.bunchName)</div>
                    </div>
                    <div class="item clearfix">
                        <div class="com-titl"><i>*</i>传染病名称：</div>
                        <div class="search-selectpicker">
                            <select   id="studentbody_diseaseName">
                                <option value="">请选择传染病名称</option>
                                <option value="手足口病">手足口病</option>
                                <option value="流行性腮腺炎">流行性腮腺炎</option>
                                <option value="猩红热">猩红热</option>
                                <option value="急性出血性结膜炎">急性出血性结膜炎</option>
                                <option value="麻疹">痢疾</option>
                                <option value="麻疹">麻疹</option>
                                <option value="风疹">风疹</option>
                                <option value="传染性肝炎">传染性肝炎</option>
                                <option value="病毒性肝炎（甲型）">病毒性肝炎（甲型）</option>
                                <option value="病毒性肝炎（乙型）">病毒性肝炎（乙型）</option>
                                <option value="脊髓灰质炎">脊髓灰质炎</option>
                                <option value="细菌性痢疾">细菌性痢疾</option>
                                <option value="流行性感冒">流行性感冒</option>
                                <option value="流行性脑脊髓膜炎">流行性脑脊髓膜炎</option>
                                <option value="学生急诊">学生急诊</option>
                                <option value="百日咳">百日咳</option>
                                <option value="流行性乙型脑炎">流行性乙型脑炎</option>
                                <option value="甲型H1N流感">甲型H1N流感</option>
                                <option value="传染性非典型肺炎">传染性非典型肺炎</option>
                                <option value="人禽流感">人禽流感</option>
                                <option value="其他">其他</option>
                            </select>

                        </div>
                        <label class="error-tip" id="diseaseNameTip"></label>
                    </div>
                    <div class="item clearfix">
                        <div class="com-titl"><i>*</i>就医医院：</div>
                        <input type="text" id="studentbody_hospital" class="com-input-item fl" maxlength="30" name="hospital"  required="required">
                        <label class="error-tip" id="hospitalTip"></label>
                    </div>
                    <div class="item clearfix">
                        <div class="com-titl"><i>*</i>诊断日期：</div>
                        <input type="text" maxlength="12" readonly placeholder="请选择日期" style="background: #f2f2f2;" id="studentbody_recordTime" class="com-input-item fl"  name="recordTime"  required="required">
                        <label class="error-tip" id="recordDateTip"></label>
                    </div>
                    <div class="item clearfix">
                        <div class="com-titl"><i>*</i>是否已痊愈：</div>
                        <label><input type="radio" name="cured" value="1"/><var>是</var></label>
                        <label><input type="radio" name="cured" value="0"/><var>否</var></label>

                    </div>
                    <div class="item clearfix">
                        <div class="com-titl">处置：</div>
                        <textarea name="shortName" maxlength="200"  id="studentbody_handle" rows="4">$!diseasesDetail.handle</textarea>
                    </div>
                    <script>
                        laydate.render({
                            elem: '#sunday_body_ill_time' //指定元素
                            ,format: 'yyyy-MM-dd'
                            ,theme: 'xxx'
                            ,max: 0
                        });
                    </script>
                </form>
            </div>
            <div class="form-group text-center">
                <button id="modalCancelBtn" type="button" class="btn btn-default" data-dismiss="modal">取消</button>

                <button class="btn btn-primary" type="button" onclick="illness_submit()" >保存</button>

            </div>

        </div>
    </div>
</div>

<script>
    var diseasesDetail="$!diseasesDetail";
    var isEdit=0;
    var returnData;
    laydate.render({
        elem: '#studentbody_recordTime' //指定元素
        ,format: 'yyyy-MM-dd'
        ,theme: 'xxx'
        ,max:0
    });
    function validateEdit() {
            $("input[name=cured][value='0']").prop('checked', true);
    }
    function illness_submit() {
        $(".error-tip").html("");
        var data = {
            // 取值
            diseaseName: $("#studentbody_diseaseName option:selected").val(),
            recordTime: $("#studentbody_recordTime").val(),
            handle: $("#studentbody_handle").val(),
            cured: $("input[type=radio][name=cured]:checked").val(),
            hospital: $("#studentbody_hospital").val(),
            id:$("#studentbody_id").val(),
            temperatureId:'$!temperatureId'
        }
        // if(isEdit==1) data.id=diseasesDetail.id;
        if(data.diseaseName==null||data.diseaseName=="") {
            $("#diseaseNameTip").html("请选择传染病名称");
           return;
        }
        if(data.hospital==null||data.hospital=="") {
            $("#hospitalTip").html("请选择就诊医院");
            return;}
        if(data.recordTime==null||data.recordTime=="") {
            $("#recordDateTip").html("请选择诊断日期");
            return;}

        returnData = data;
        $.ajax({
            type: "post",
            url: isEdit==1?"/sunday/web/studentBody/editDisease":"/sunday/web/studentBody/addDisease",
            data: data,
            async: false,
            success: function (data) {
                //关闭加载
//                layer.closeAll('loading');
                if (data.code == 0) {
//                    layer.alert("保存成功");
                   $("#modalCancelBtn").trigger("click");
//                    $(".modal-backdrop").remove();
                    //在最上方加一行
//                    var html = "<tr><td>"+returnData.diseaseName+"</td>" +
//                            "<td>"+returnData.hospital+"</td>"+
//                            "<td>"+returnData.recordTime+"</td>"+
//                    "<td>"+returnData.handle+"</td>"+
//                    "<td>"+(returnData.cured==1?"是":"否")+"</td>"+
//                    "<td>"+
//                    "<button type='button' onclick=\"add_illness_showmodal('"+returnData.diseaseName+
//                    "','"+returnData.hospital+"','"+returnData.recordTime+"','"+returnData.handle+"','"+returnData.cured+"','"+returnData.id+"')\ class=''>编辑</button>"+
//                    "<button type='button' onclick='show_illness_log_del("+returnData.id+")' class=''>删除</button>"+
//                    "</td>"+
//                    "</tr>";
//                    console.log(html);
//                    $("#sunday_course_div tr").eq(0).after(html);
                    setTimeout(function(){
                        newTab("/sunday/web/studentBody/inputOther?temperatureId="+ "$!temperatureId");
                    },200);

//                     layer.closeAll();
                } else {
                    //如果重复，需要确认交互
                    layer.alert("保存失败");
                }
            }
        });
    }

    // 保存过敏源和备注
    function save() {
        var data = {
            recordTime: "$!bodyDetail.recordTime",
            temperature: "$!bodyDetail.temperature",
            symptom: "$!bodyDetail.symptom",
            state: "$!bodyDetail.state",
            images:"$!bodyDetail.images",
            checkType:"$!bodyDetail.checkType",
            handleType: "$!bodyDetail.handleType",
            remarks: "$!bodyDetail.remarks",
            id: "$!bodyDetail.id",
            creatorType:1,
            memberId:0,
            anaphylactogen:$("#addspecial_anaphylactogen").val(),
            anaphylactogenRemarks:$("#addspecial_anaphylactogenRemarks").val()
        };
        $.ajax({
            type: "post",
            url: "/sunday/web/studentBody/edit",
            data: data,
            async: false,
            success: function (data) {
                //关闭加载
                if (data.code == 0) {

                } else {
                    //如果重复，需要确认交互
                }
            }
        });
    };
    function sub() {
        var data = {
            recordTime: "$!bodyDetail.recordTime",
            temperature: "$!bodyDetail.temperature",
            symptom: "$!bodyDetail.symptom",
            state: "$!bodyDetail.state",
            images:"$!bodyDetail.images",
            checkType:"$!bodyDetail.checkType",
            handleType: "$!bodyDetail.handleType",
            remarks: "$!bodyDetail.remarks",
            id: "$!bodyDetail.id",
            creatorType:1,
            memberId:0,
            anaphylactogen:$("#addspecial_anaphylactogen").val(),
            anaphylactogenRemarks:$("#addspecial_anaphylactogenRemarks").val()
        };
        $.ajax({
            type: "post",
            url: "/sunday/web/studentBody/edit",
            data: data,
            async: false,
            success: function (data) {
                //关闭加载
                layer.closeAll('loading');
                if (data.code == 0) {
                    layer.alert("保存成功");
                    setTimeout(function () {
                        newTab("/sunday/web/studentBody/index?isClean=1");
                    },1500);
                } else {
                    //如果重复，需要确认交互
                    layer.alert("保存失败");
                }
            }
        });
    };
    function add_illness_showmodal(diseaseName,hospital,recordTime,handle,cured,id) {

       var item = {
            diseaseName:diseaseName,
            hospital:hospital,
            recordTime:recordTime,
            handle:handle,
            cured:cured,
           id:id
        };
        if(item.id){
            isEdit=1;
            $("#studentbody_id").val(item.id);
            $("#studentbody_diseaseName option[value='"+item.diseaseName+"']").prop("selected",true);
            $("#studentbody_recordTime").val(item.recordTime);
            $("#studentbody_handle").val(item.handle);
            if(item.cured==1) {
                $("input[name=cured][value='1']").prop('checked', true);
            } else {
                $("input[name=cured][value='0']").prop('checked', true);
            }
            $("#studentbody_hospital").val(item.hospital);
            remoteModal("sunday_evaluation_div_ill");
            $(".error-tip").html("");
        }else{
            isEdit=0;
            // 清空输入内容
            $("#studentbody_id").val("");
            $("#studentbody_diseaseName").val("");
            $("#studentbody_recordTime").val("");
            $("#studentbody_handle").val("");
            $("input[name=cured][value='0']").prop('checked', true);
            $("#studentbody_hospital").val("");
            remoteModal("sunday_evaluation_div_ill");
            $(".error-tip").html("");
            validateEdit();
        }
}
    function show_illness_log_del(id) {
        var msg="确认删除?";

        layer.confirm(msg, function(index){
            //do something
            layer.load(4, {shade: [0.8, '#393D49']})
            // 不要异步操作
            $.ajax({
                type : "get",
                url : "/sunday/web/studentBody/deleteDisease?id="+ id,
                data : {},
                async : false,
                success : function(data){
                    //关闭加载
                    layer.closeAll('loading');
                    if(data.code == 0){
                        layer.alert("删除成功");
                        newTab("/sunday/web/studentBody/inputOther?temperatureId="+$!temperatureId,null);
                    }else{
                        layer.msg('删除失败', {
                            icon: 2
                        });
                    }


                }
            });
            //最后手动关闭
            layer.close(index);





        });
    }
</script>