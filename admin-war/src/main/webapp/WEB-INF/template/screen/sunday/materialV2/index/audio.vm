#parse("/screen/sunday/operate.vm")
<div class="text-img imgs">
    <div class="top-wrapper clearfix">
        <script>
            $.post("/sunday/web/material/top",
                    {"type":2},
                    function (data) {
                        $(".top-wrapper").append(data);
                    })
        </script>
    </div>

    <div class="con">
        <div class="num"><span id="sunday_material_search_form_msg" ></span>
            <button type="button" class="com-button sunday_material_add" onclick="sunday_material_save(0);">新建音频
            </button>
        </div>
        <div class="group-wrapper clearfix" id="sunday_material_div" >
            <div class="titl">分组</div>
            <div class="com-menu-wrapper clearfix fl" id="sunday_material_group_choose_div" >
                #foreach($!group in $!groups)
                    <a href="javascript:;" class="com-menu-item #if($!group.isCheck == 1) on #end" value="$!group.id" valueName="$!group.name">$!group.name (<span>$!group.number</span>)
                    </a>
                #end
            </div>
            <a href="javascript:sunday_material_group_save(0,null)" class="com-add-button sunday_material_add">新建分组</a>
        </div>
        <!-- 页码 -->
    </div>
</div>
<!--新增修改分组DIV-->
<div class="mask" style="display: none" id="sunday_material_group_div_mask"></div>
<div class="com-popup add-group-popup" id="sunday_material_group_div">
    <form id="sunday_material_group_form" class="form-horizontal" action="/sunday/web/material/group/save" method="post"  enctype="multipart/form-data">
        <input type="hidden" name="id">
        <input type="hidden" name="type">
        <div class="title">新建/编辑分组</div>
        <input type="text" maxlength="20" placeholder="请输入分组名称" class="com-input-item" name="name">
        <div class="button-wrapper">
            <a href="javascript:sunday_material_group_div_open_and_close();" class="com-button return">关闭</a>
            <button type="submit" class="com-button" >保存</button>
        </div>
    </form>
</div>
<script>

    // 打开或关闭分组div
    function sunday_material_group_div_open_and_close(){
        if($("#sunday_material_group_div").hasClass("on")){
            $("#sunday_material_group_div").removeClass("on");
            $(".mask").hide();
        }else{
            $("#sunday_material_group_div").addClass("on");
            $(".mask").show();
        }
    }
    //新增或保存
    function sunday_material_group_save(id,name){
        if(id==null){
            id = $("#sunday_material_group_choose_div").children(".on").attr("value");
            name = $("#sunday_material_group_choose_div").children(".on").attr("valueName");
        }
        $("#sunday_material_group_form").form("clear");
        $("#sunday_material_group_form").form("load",{"id":id,"name":name,"type":"$!type"});
        sunday_material_group_div_open_and_close();
    }
    var validatorMenu = $("#sunday_material_group_form").validate({

        submitHandler: function (form) {

            //加遮罩层
            layer.load(4, {shade: [0.8, '#393D49']})
            //状态和类型
            $(form).ajaxSubmit({
                //你表单提交就要切换页面，ajax加载又是异步的。两边同时进行，
                //当ajax加载在切换页面之前完成，就会执行回调，反之不会执行
                //加同步提交数据
                async: false,
                dataType: "json",
                success: function (data) {
                    //关闭加载
                    layer.closeAll('loading');
                    if (data.code == 0) {
                        sunday_material_group_div_open_and_close();
                        newTab('/sunday/web/material/index?type=2&groupId='+data.result.id,null);

                    } else {
                        layer.msg(data.message, {icon: 2});
                    }
                }
            });
        }
    });

    //删除
    function sunday_material_group_delete(){
        var id  = $("#sunday_material_group_choose_div").children(".on").attr("value");
        var msg = "确认要删除当前分组吗?";
        layer.confirm(msg, function (index) {
            layer.load(4, {shade: [0.8, '#393D49']})
            //不要异步操作
            $.ajax({
                type: "post",
                url: "/sunday/web/material/group/delete",
                data: {"id": id},
                async: false,
                success: function (data) {
                    //关闭加载
                    layer.closeAll('loading');
                    if (data.code == 0) {
                        var groupId= getSelectedGroupId();
                        newTab('/sunday/web/material/index?type=2&groupId='+groupId,null);
                    } else {
                        layer.msg('删除失败', {
                            icon: 2
                        });
                    }
                }
            });
            //最后手动关闭
            layer.close(index);

        });
    }
</script>
<script>
    //新增素材
    function sunday_material_save(id){
        var groupId = getSelectedGroupId();
        newTab("/sunday/web/material/input?type=2&id=" + id+"&groupId="+groupId , "新增/编辑");
    }
    //删除素材
    function sunday_material_delete(id) {
        var msg = "确认要删除选中的素材吗?";
        layer.confirm(msg, function (index) {
            layer.load(4, {shade: [0.8, '#393D49']})
            //不要异步操作
            $.ajax({
                type: "post",
                url: "/sunday/web/material/batchDelete",
                data: {"ids": id},
                async: false,
                success: function (data) {
                    //关闭加载
                    layer.closeAll('loading');
                    if (data.code == 0) {
                        sunday_material_search(false);
                    } else {
                        layer.msg('删除失败', {
                            icon: 2
                        });
                    }
                }
            });
            //最后手动关闭
            layer.close(index);

        });
    }
</script>




<script>
    //选择分组-
    $("#sunday_material_group_choose_div").children().click(function () {
        $(this).addClass("on");
        $(this).siblings().removeClass("on");
        sunday_material_search(false);
    })
    function getSelectedGroupId(){
        return $("#sunday_material_group_choose_div").children(".on").attr("value");
    }
    var param_pageNumber = "$!param.pageNumber";
    var param_pageSize = "$!param.pageSize";
    console.log("param_pageNumber="+param_pageNumber+",param_pageSize="+param_pageSize)
    if(param_pageNumber==null|| param_pageNumber==""){
        param_pageNumber=1
    }
    if(param_pageSize==null|| param_pageSize==""){
        param_pageSize=50
    }
    var sunday_material_pageNumber=param_pageNumber;
    var sunday_material_pageSize=param_pageSize;
    var sunday_material_sort="id";
    var sunday_material_order="desc";
    var sunday_material_is_loading=false;
    var sunday_material_is_end=false;
    var sunday_material_groupId=""
    //输入订单号查询
    function sunday_material_search(isPageNumber){

        sunday_material_name = $("#sunday_material_search_form_name").val();

        sunday_material_is_loading=false;
        sunday_material_is_end=false;
        sunday_material_groupId =$("#sunday_material_group_choose_div").children(".on").attr("value");
        if(isPageNumber){
            sunday_material_pageNumber =1;
        }
        sunday_material_getData(true);
    }
    function sunday_material_search_page(pageNumber,pageSize,maxPageNumber){
        if(maxPageNumber!=null&&maxPageNumber!=""&&typeof(maxPageNumber)!="undefined"){
            if(parseInt(pageNumber)>parseInt(maxPageNumber)){
                return;
            }
        }
        sunday_material_pageNumber =pageNumber;
        sunday_material_pageSize =pageSize==null?sunday_material_pageSize:pageSize;
        unday_material_groupId =$("#sunday_material_group_choose_div").children(".on").attr("value");
        sunday_material_getData(true);
    }
    function sunday_material_getData(isRemove){
        var t="#sunday_material_div";

        if(sunday_material_is_loading||sunday_material_is_end)return;
        if(isRemove){
            $("#sunday_material_data_group").remove();
            $("#sunday_material_data_table").remove();
            $("#sunday_material_data_page").remove();

        }
        sunday_material_is_loading=true;


        //同步请求
        $.ajax({
            type: "POST",
            url: "/sunday/web/material/select",
            data: {
                "pageSize":sunday_material_pageSize,
                "pageNumber":sunday_material_pageNumber,
                "sort":sunday_material_sort,
                "order":sunday_material_order,
                "groupId":sunday_material_groupId,
                "type":"$!type"
            },
            dataType: "text",
            async:true,
            success: function(result){
                //d layer.closeAll('loading');
                //返回的是html
                if(result==null||result==""||result.length<1){
                    sunday_material_is_end=true;
                }else{
                    $(t).after(result);
                    sunday_material_is_loading=false;

                }
            }
        });
    }
    $("#sunday_material_group_choose_div").children(".on").click();



</script>
<script>
    function  sunday_material_down(url) {
        window.open(url);
    }
</script>