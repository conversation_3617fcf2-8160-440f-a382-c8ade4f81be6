#parse("/screen/sunday/operate.vm")
<div class="add-plan add-role">
    <form id="sunday_zb_form" class="form-horizontal" action="/sunday/web/yb/save" method="post"  enctype="multipart/form-data">
        <input type="hidden" name="id" id="id" value="$!{result.id}">
        <input type="hidden" name="code" id="code" value="$!{result.code}" >
        <input type="hidden" name="course" id="course" value="$!{result.course}" >
        <input type="hidden" name="year" id="year" value="$!{result.year}" >
        <input type="hidden" name="month" id="month" value="$!{result.month}" >

        <div class="search-wrapper clearfix">
            <div class="list">
                <div class="com-title-wrapper">
                    <div class="title">新增课程模版</div>
                </div>
                <div class="item clearfix">
                    <div class="com-titl"><i>*</i>模板类型：</div>
                    <div class="com-radio-wrapper fl clearfix" id="sunday_report_template_type_div">
                        <div class="com-radio-item #if($!result.templateType == 1 || !$!result.templateType) on #end" value="1">早教</div>
                        <div class="com-radio-item #if($!result.templateType == 2) on #end" value="2">幼教</div>
                    </div>
                    <input type="hidden" name="templateType" id="templateType" value="#if($!result.templateType)$!{result.templateType}#else 1#end">
                </div>
                <div class="item clearfix">
                    <div class="com-titl"><i>*</i>月份：</div>
                	<input type="text" placeholder="请选择月份" class="search-input-item" style="width:100px;" #if($!result.status ==1) readOnly #else id="sunday_report_zb_month" #end   #if($!result.id != "0") value=" $!{result.year}-$!{result.month}" #end>
                </div>
                
                <div class="item clearfix" id="div_themeArray" style="width:1000px;">
                    <div class="com-titl"><i>*</i>本月主题：</div>
                	#if($!{result.theme})
                    	#foreach($!st in $!{result.themeArray})
                    		#if($foreach.index == 0)
                    			<input type="text" id="input_themeArray$!{result.id}$foreach.index" #if($!result.status ==1) readOnly #end class="com-input-item fl" style="width:500px;" name="themeArray"  value="$!st"  required="required">#if($!result.status !=1) <a class="easyui-linkbutton" id="button_themeArray$!{result.id}$foreach.index" style="margin-left:15px;margin-top:12px;padding: 5px 9px 5px 9px;color:#fff;border:none;background:#63B8FF;border-radius:3px;" href="javascript:addRow('themeArray');" >+</a>  #end
                    		#else
                    			<input type="text" id="input_themeArray$!{result.id}$foreach.index" #if($!result.status ==1) readOnly #end class="com-input-item" style="width:500px;margin-left:100px;margin-top:2px;" name="themeArray" value="$!st" required="required">#if($!result.status !=1)<a class="easyui-linkbutton" id="button_themeArray$!{result.id}$foreach.index" style="margin-left:15px;margin-top:2px;padding: 5px 10px 5px 10px;color:#fff;border:none;background:#63B8FF;border-radius:3px;" href="javascript:deleteRow('themeArray$!{result.id}$foreach.index');" >-</a>  #end
                    		#end
                    	#end
                    #else
                    	 <input type="text" class="com-input-item fl" style="width:500px;" name="themeArray"  #if($!result.status ==1) readOnly #end value="$!{result.themeArray}"  required="required"> #if($!result.status !=1) <a class="easyui-linkbutton" style="margin-left:15px;margin-top:12px;padding: 5px 9px 5px 9px;color:#fff;border:none;background:#63B8FF;border-radius:3px;" href="javascript:addRow('themeArray');" >+</a> #end
                	#end
                </div>
                <div class="item clearfix" id="div_growthRecordArray" style="width:1000px;">
                    <div class="com-titl"><i>*</i>成长记录：</div>
                	#if($!{result.growthRecord}) 
                    	#foreach($!st in $!{result.growthRecordArray})
                    		#if($foreach.index == 0)
                    			<input type="text" id="input_growthRecordArray$!{result.id}$foreach.index" #if($!result.status ==1) readOnly #end class="com-input-item fl" style="width:500px;" name="growthRecordArray"  value="$!st"  required="required">#if($!result.status !=1) <a class="easyui-linkbutton" id="button_teacherCommentArray$!{result.id}$foreach.index" style="margin-left:15px;margin-top:12px;padding: 5px 9px 5px 9px;color:#fff;border:none;background:#63B8FF;border-radius:3px;" href="javascript:addRow('growthRecordArray');" >+</a>  #end
                    		#else 
                    			<input type="text" id="input_growthRecordArray$!{result.id}$foreach.index" #if($!result.status ==1) readOnly #end class="com-input-item" style="width:500px;margin-left:100px;margin-top:2px;" name="growthRecordArray" value="$!st" required="required">#if($!result.status !=1)<a class="easyui-linkbutton" id="button_teacherCommentArray$!{result.id}$foreach.index" style="margin-left:15px;padding: 5px 10px 5px 10px;color:#fff;border:none;background:#63B8FF;border-radius:3px;" href="javascript:deleteRow('growthRecordArray$!{result.id}$foreach.index');" >-</a>  #end
                    		#end
                    	#end
                    #else
                    	 <input type="text" class="com-input-item fl" style="width:500px;" name="growthRecordArray"  #if($!result.status ==1) readOnly #end value="$!{result.growthRecordArray}"  required="required"> #if($!result.status !=1) <a class="easyui-linkbutton" style="margin-left:15px;margin-top:12px;padding: 5px 9px 5px 9px;color:#fff;border:none;background:#63B8FF;border-radius:3px;" href="javascript:addRow('growthRecordArray');" >+</a> #end
                	#end
                </div>
                <div class="item clearfix" id="div_teacherCommentArray" style="width:1000px;">
                    <div class="com-titl"><i>*</i>教师评语：</div>
                    #if($!{result.teacherComment}) 
                    	#foreach($!st in $!{result.teacherCommentArray})
                    		#if($foreach.index == 0)
                    			<input type="text" id="input_teacherCommentArray$!{result.id}$foreach.index" #if($!result.status ==1) readOnly #end class="com-input-item fl" style="width:500px;" name="teacherCommentArray"  value="$!st"  required="required">#if($!result.status !=1) <a class="easyui-linkbutton" id="button_teacherCommentArray$!{result.id}$foreach.index" style="margin-left:15px;margin-top:12px;padding: 5px 9px 5px 9px;color:#fff;border:none;background:#63B8FF;border-radius:3px;" href="javascript:addRow('teacherCommentArray');" >+</a>  #end
                    		#else 
                    			<input type="text" id="input_teacherCommentArray$!{result.id}$foreach.index" #if($!result.status ==1) readOnly #end class="com-input-item" style="width:500px;margin-left:100px;margin-top:2px;" name="teacherCommentArray" value="$!st" required="required"> #if($!result.status !=1) <a class="easyui-linkbutton" id="button_teacherCommentArray$!{result.id}$foreach.index" style="margin-left:15px;padding: 5px 10px 5px 10px;color:#fff;border:none;background:#63B8FF;border-radius:3px;" href="javascript:deleteRow('teacherCommentArray$!{result.id}$foreach.index');" >-</a> #end 
                    		#end
                    	#end
                    #else
                    	 <input type="text" class="com-input-item fl" style="width:500px;" name="teacherCommentArray" #if($!result.status ==1) readOnly #end  value="$!{result.teacherCommentArray}"  required="required"> #if($!result.status !=1) <a class="easyui-linkbutton" style="margin-left:15px;margin-top:12px;padding: 5px 9px 5px 9px;color:#fff;border:none;background:#63B8FF;border-radius:3px;" href="javascript:addRow('teacherCommentArray');" >+</a> #end
                	#end
                </div>
                
                <div class="item clearfix">
                    <div class="com-titl" style="height: 30px;"></div>
                    <div class="button-wrapper fl">
                        <a href="javascript:newTab('/sunday/web/yb/index?isClean=0', null);" class="com-button return">返回</a>
                        #if($!result.status !=1 && $!result.status !=4)
                        <button type="submit" class="com-button sunday_role_add sunday_role_edit">保存</button>
                        #end
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
<script>
   
   laydate.render({
        elem: '#sunday_report_zb_month', 
        type: 'month'
    });
    
    // 模板类型radio button切换功能
    $("#sunday_report_template_type_div").find(".com-radio-item").click(function(){
        // 移除所有选中状态
        $("#sunday_report_template_type_div").find(".com-radio-item").removeClass("on");
        // 添加当前选中状态
        $(this).addClass("on");
        // 更新隐藏字段的值
        $("#templateType").val($(this).attr("value"));
    });

    //实例化表单组件
    var validatorMenu=$("#sunday_zb_form").validate({

        submitHandler: function(form) {
           
           var sunday_report_zb_month = $("#sunday_report_zb_month").val();
           $("#year").val(parseInt(sunday_report_zb_month.split("-")[0]));
           $("#month").val(parseInt(sunday_report_zb_month.split("-")[1]));
           
            //加遮罩层
            layer.load(4, {shade: [0.8, '#393D49']});
           // return;
            $(form).ajaxSubmit({

                //你表单提交就要切换页面，ajax加载又是异步的。两边同时进行，
                //当ajax加载在切换页面之前完成，就会执行回调，反之不会执行
                //加同步提交数据
                async: false,
                dataType:"json",
                success:function(data){
                    //关闭加载
                    layer.closeAll('loading');
                    if(data.code == 0){
                        newTab('/sunday/web/yb/index?isClean=0',null)
                    }else{
                        layer.msg(data.message, {
                            icon: 2
                        });
                    }
                }
            });
        }
    }) ;

	var sort = 1;
	
	function addRow(r){
		var i = sort++;
		$("#div_"+r).append('<input type="text" id="input_'+r+i+'" class="com-input-item" style="width:500px;margin-left:100px;margin-top:2px;" name="'+r+'" required="required"><a class="easyui-linkbutton" id="button_'+r+i+'" style="margin-left:15px;padding: 5px 10px 5px 10px;color:#fff;border:none;background:#63B8FF;border-radius:3px;" href="javascript:deleteRow(\''+r+i+'\');" >-</a>');
	}
	
	function addSyRow(r){
		var i = sort++;
		$("#div_"+r).append('<input type="text" id="input_'+r+i+'" class="com-input-item" style="width:500px;margin-left:140px;margin-top:2px;" name="'+r+'" required="required"><a class="easyui-linkbutton" id="button_'+r+i+'" style="margin-left:15px;padding: 5px 10px 5px 10px;color:#fff;border:none;background:#63B8FF;border-radius:3px;" href="javascript:deleteRow(\''+r+i+'\');" >-</a>');
	}
	
	function deleteRow(r){
		
		$("#input_"+r).remove();
		$("#button_"+r).remove();
	}
</script>