#parse("/screen/sunday/operate.vm")
<style>
    .add-student .item .com-titl{
        width: 153px;
    }
    #ablum_view_mask{
        display:none;
    }
    #ablum_view_div{
        display:none;
    }
    #albumFrame{
        width: 100%;
        height: 98%;
        border: none;
    }
</style>
<link rel="stylesheet" href="$!{adminRoot}/yuhua/admin/css/swiper.min.css">
<script src="$!{adminRoot}/yuhua/admin/js/swiper4.js"></script>
<div class="add-student">
<form id="sunday_student_form" autocomplete="off" class="form-horizontal" action="/sunday/web/student/save" method="post"  enctype="multipart/form-data">
    <!--隐藏参数-->
    <input type="hidden" name="id" value="$!{student.id}">
    <input type="hidden" name="sex" id="sunday_student_form_sex_input">
    <input type="hidden" name="status" id="sunday_student_form_status_input">
    <input type="hidden" name="stuType" id="sunday_student_form_stuType_input">
    <input type="hidden" name="qinziType" id="sunday_student_form_qinziType_input">
    <input type="hidden" name="parentChildType" id="sunday_student_form_parentChildType_input">
    <input id="sunday_student_form_guardName_input" name="guardName" type="hidden"/>
    <input  id="sunday_student_form_bunchName_input" name="bunchName" type="hidden"/>
    <input  id="sunday_student_form_bunchType_input" name="bunchType"  type="hidden"/>
    <input  id="sunday_student_form_bunchCategory_input" name="bunchCategory"  type="hidden"/>
    <div class="com-title-wrapper">
        <div class="title">
            #if($!student.id == 0)
                新增学生
            #else
                编辑学生
            #end
        </div>
    </div>
    <div class="con" style="float:left;max-width: 800px">
        <div class="item clearfix">
            <div class="com-titl">宝贝登记照：</div>
            <div class="com-add-img xzBtn" style="width:150px;height:150px;background-image: url(#if($student.image)$!student.image#else$!{adminRoot}/yuhua/admin/img/add.png#end);" id="sunday_upload_image_div" onclick="upImg2(4,'sunday_upload_image_div','sunday_upload_image_input',150,150)">
                <input type="hidden" id="sunday_upload_image_input" name="image" value="$!student.image" >
            </div>
            <div class="com-titl mw80">(150*150px)</div>
        </div>
        <script>
          function upImg2(bili,sunday_upload_image_div,sunday_upload_image_input,width,height,callType) {
              //1,加载素材库
              layer.load(4, {shade: [0.8, '#393D49']})
              //不要异步操作
              $.ajax({
                  type: "post",
                  url: "/sunday/web/material/upload/index",
                  data: {"type": 3,"bili":bili,"imageDiv":sunday_upload_image_div,"imageInput":sunday_upload_image_input,"width":width,"height":height,"callType":callType},
                  async: false,
                  success: function (data) {
                      //关闭加载
                      layer.closeAll('loading');
                      $(".right-wrapper").after(data);
                  }
              });
          }

        </script>
        <div class="item clearfix">
            <div class="com-titl"><i>*</i>姓名：</div>
            <input type="text"  class="com-input-item fl" name="name" value="$!student.name" required="required">
        </div>
        <div class="item clearfix">
            <div class="com-titl"><i>*</i>性别：</div >
            <div class="com-radio-wrapper fl clearfix" id="sunday_student_form_sex_div">
                <div class="com-radio-item" value="1">男</div>
                <div class="com-radio-item" value="0">女</div>
            </div>
        </div>
        <div class="item clearfix">
            <div class="com-titl"><i>*</i>出生年月：</div>
            <div class="com-time-item fl">
                <input type="text"  name="birthDate" value="$!student.birthDate" required="required" id="sunday_student_form_birthDate"   readonly>
            </div>
        </div>
        <div class="item clearfix">
            <div class="com-titl">家庭住址：</div>
            <input type="text"  class="com-input-item fl" style="width: 351px;" name="address" value="$!student.address" >
        </div>
        <div class="item clearfix">
            <div class="com-titl"><i>*</i>入学时间：</div>
            <div class="com-time-item fl">
                <input type="text"  name="joinDate" value="$!student.joinDate" required="required" id="sunday_student_form_joinDate"   readonly>
            </div>
        </div>
        <div class="item clearfix">
            <div class="com-titl">地区选择：</div>
            <div class="selectpicker">
                <select id="sunday_student_form_provinceId"></select>
            </div>
            <div class="selectpicker">
                <select id="sunday_student_form_cityId"></select>
            </div>
            <div class="selectpicker">
                <select id="sunday_student_form_districtId"></select>
            </div>
        </div>
        <div class="item clearfix">
            <div class="com-titl"><i>*</i>学生类型：</div >
            <div class="com-radio-wrapper fl clearfix" id="sunday_student_form_stuType_div">
                <div class="com-radio-item #if($!student.stuType==1) on #end"  value="1">托班</div>
            </div>
            <div class="com-radio-wrapper fl clearfix" id="sunday_student_form_qinziType_div" style="margin-left: 15px">
                <div class="com-radio-item #if($!student.qinziType==1) on #end" value="1">亲子</div>
            </div>
        </div>
        <div class="item clearfix stuType">
            <div class="com-titl"><i>*</i>托班班级名：</div>
            <div class="selectpicker">
                <select id="sunday_student_form_guardId"  name="guardId" required="required" ></select>
            </div>
            <div class="selectpicker">
                <select id="sunday_student_form_bunchId"   name="bunchId"  required="required" ></select>
            </div>
        </div>
        <div class="item clearfix qinziType">
            <div class="com-titl"><i>*</i>亲子课类型：</div >
            <div class="com-radio-wrapper fl clearfix" id="sunday_student_form_parentChildType_div">
                <div class="com-radio-item on" value="0">小时制</div>
            </div>
        </div>
        <div class="item clearfix qinziType">
            <div class="com-titl"><i>*</i>课时：</div>
            #if($!student.qinziType == 1 && $tag == 1)
                <input type="text"  class="com-input-item fl" name="classHour"  id="classHour_all" value="$!student.classHour" readonly onclick="updateClassHour('$!student.classHour')" required="required">
            #else
                <input type="text"  class="com-input-item fl" name="classHour" id="classHour_all" value=""  required="required">
            #end

        </div>
        <div class="item clearfix qinziType">
            <div class="com-titl"><i></i>意向上课时段：</div>
            <input type="text"  class="com-input-item fl" name="intentionClassHour" value="$!student.intentionClassHour">
        </div>

        #if($!student.qinziType == 1 && $!tag==1)
            #foreach($qinzi in $!student.qinziBunches)

                <div class="item clearfix qinziType qinziBunchNum">
                    <div class="com-titl"><i>*</i>亲子班级名：</div>
                    <input id="qinz_guardId_$!velocityCount" value="$!qinzi.guardId" type="hidden">
                    <div class="selectpicker">
                        <select id="sunday_student_form_qinzi_guardId_$!velocityCount" disabled  name="qinziGuardId" required="required" ></select>
                    </div>
                    <input id="qinz_bunchId_$!velocityCount" value="$!qinzi.bunchId" type="hidden">
                    <div class="selectpicker">

                        <select id="sunday_student_form_qinzi_bunchId_$!velocityCount" disabled  name="qinziBunchId"  required="required" ></select>
                    </div>
                </div>
            #end
        #else
            <div class="item clearfix qinziType">
                <div class="com-titl"><i>*</i>亲子班级名：</div>
                <div class="selectpicker">
                    <select id="sunday_student_form_qinzi_guardId"  name="qinziGuardId" required="required" ></select>
                </div>
                <div class="selectpicker">
                    <select id="sunday_student_form_qinzi_bunchId"   name="qinziBunchId"  required="required" ></select>
                </div>
            </div>
        #end


        <div class="item clearfix">
            <div class="com-titl"><i>*</i>入园状态：</div >
            <div class="com-radio-wrapper fl clearfix" id="sunday_student_form_status_div">
                <div class="com-radio-item" value="1">在园</div>
                <div class="com-radio-item" value="0">离园</div>
            </div>
        </div>
        <div class="item clearfix">
            <div class="com-titl">备注：</div>
            <textarea placeholder="请输入宝贝的其他信息.." class="com-textarea" name="desc"  >$!student.desc</textarea>
        </div>



        <!--新增时,同时新增显示家长信息，编辑时也显示家长-->
        #if($!student.id == 0)
        <div class="com-title-wrapper">
            <div class="title">家长信息</div>
        </div>
        <div class="item clearfix">
            <div class="com-titl"><i>*</i>家长姓名：</div>
            <input type="text"  class="com-input-item fl" name="parentName"  required="required">
        </div>
            <div class="item clearfix">
                <div class="com-titl"><i>*</i>家长称谓：</div>
                <input type="text"  class="com-input-item fl" name="parentTile"  required="required">
            </div>
        <div class="item clearfix">
            <div class="com-titl"><i>*</i>家长手机号：</div>
            <input type="text"  class="com-input-item fl" maxlength="11" id="parentMobile" name="parentMobile" required="required">
        </div>
        #end
        <div class="item clearfix">
            <div class="com-titl" style="height: 30px"></div>
            <div class="button-wrapper fl">
                <button type="button" class="com-button fr sunday_student_add sunday_student_edit" style="margin-left: 20px" onclick="sunday_student_check_checkStudentIsExist()">保存</button>

                <!--  // 托班学生的新增、修改 一定传递stuType=1  -->
                #if($!stuType == 1)
                    <a href="javascript:stopProcess();yuhua_return(from==0?'/sunday/web/student/index?isClean=0':'/sunday/web/bunch/index');" class="com-button return fr">返回</a>
                #else
                    <a href="javascript:stopProcess();yuhua_return(from==0?'/sunday/web/student/index2?isClean=0':'/sunday/web/bunch/index');" class="com-button return fr">返回</a>
                #end

            </div>
        </div>
    </div>

    <div class="book" style="    float: left;max-width: 40%; margin-left: 40px;margin-top: 20px;padding-right: 20px;">
        <style>

            .add-student
            .swiper-container{
                width: 500px;
                text-align: center;
                height: 140px;
                box-sizing: content-box;
                margin: 0 auto;
            }
            .add-student .swiper-container+ .btnbox{
                margin-top: 33px;
                width: 140px;
                z-index: 2019;
                margin-left: 30px;
                padding: 10px 2px;
                border: 1px dashed #47C28D;
                border-radius: 15px;
            }
            .add-student .swiper-container+ .btnbox .com-button {

                line-height: 30px;
                border-radius: 30px;
                width: 100px;
                margin: 0 auto;
            }
            .add-student .swiper-container+ .btnbox .com-button+.com-button{
                margin-top: 10px;
            }
            .add-student .swiper-button-next,
            .add-student .swiper-button-prev{
                width: 10px;
                background-size: 100%;
            }
            .add-student .swiper-slide{
                margin-top: 20px;
            }
            .add-student .swiper-slide img{
                display: block;
                width: 65px;
                height:  88px;
                background: #5298ff;
                margin:0 auto;
            }
            .add-student .swiper-slide:hover{
                transform: scale(1.3);
                transition: transform ease 0.3s;

            }
            .add-student .swiper-slide-active{
                transform: scale(1.3);
                transition: transform ease 0.3s;
            }
            .swiper-button-prev{
                left: 0!important;;
            }
            .swiper-button-next{
                right: 0!important;;
            }
            .nowbook{
                display: block;
                width: 500px;
                margin: 0 auto 20px;
                box-sizing: content-box;
            }
            .nowbook img{
                width: 130px;
                height:  176px;
                float: left;
                background: #5298ff;
                margin-right: 15px;
            }
            .nowbook .msg{
                float: left;
            }
            .nowbook .msg span{
                color: black;
            }
            .nowbook .msg p{
                color: #333;
            }
            .nowbook  .com-button{
                margin-top: 10px;
                line-height: 30px;
                border-radius: 30px;
                width: 100px;
            }
            .btn-icon{
                height: 15px!important;
                background: none!important;
                width: auto!important;
                margin: 0!important;
                margin-top: 7px!important;
                margin-right: 10px!important;
            }
            #processBar{
                background-color:#C8EDDD;
                height:100%;
                width:0%;
                position:absolute;
                left:0px;
                top:0px;
                z-index: -1;
            }
            #curDownloadBtn{
                position: relative;
                overflow: hidden;
                z-index: 2;
                text-align: center;
                float: left; margin-left: 10px;background: white;cursor:pointer;color: #49c28d;border: 1px solid #49c28d;
            }
            .preview-popup{
                background: url($!{adminRoot}/yuhua/admin/img/phone2.png) no-repeat;
                display: block;
                width: 370px;
                background-size: cover;
                height: 750px;
            }
            .preview-popup.on{
                top:40%;
            }
        </style>

        <div class="item clearfix" #if($!student.id == 0 || !$!curAlbum.coverUrl) style="display:none" #end>
            <div class="nowbook">
                <img id="mainAlbumCover" src="$!curAlbum.coverUrl" alt="" class="">
                <div class="msg" id="mainAlbum"><h3>#if($!student.stuType==1)奇妙园托班 #else 奇妙园亲子班 #end</h3>
                    <span>相册日期：<span id="mainAlbumMonth">$!curAlbum.chineseMonth</span>月</span>
                    <p>页数：<span  id="mainAlbumPages">$!curAlbum.pageSize</span>页</p>
                    <p>记录人：<span  id="mainAlbumRecorders">$!curAlbum.recorders</span></p>
                    <div class="btnbox" style="margin-top: 40px">
                        <div id="curPreviewBtn" class="com-button" style="float: left;background:#49C28D;cursor:pointer;" onclick="previewImg('$!curAlbum.id')"><img class="btn-icon" src="$!{adminRoot}/yuhua/admin/img/liulan.png">预览</div>
                        <div id="curDownloadBtn" class="com-button return" onclick="downloadPdf('$!curAlbum.id')"><div id="processBar"></div><img class="btn-icon" src="$!{adminRoot}/yuhua/admin/img/download.png">下载</div>
                    </div>
                </div>
                <div class="clearfix"></div>
            </div>
            <!-- 按照年份划分 -->
            #foreach($year in $!years)
                <div class="swiper-container fl" style="margin-top: 20px;height: 200px;">
                    <label>$year 年</label>
                    <div class="swiper-wrapper">
                        #foreach($album in $!albums.data)
                            #if($album.year==$year)
                                <div class="swiper-slide albums-element" staticUrl="$!album.staticUrl" albumId="$!album.id"
                                     cover ="$!album.coverUrl"
                                     month =  "$!album.month"
                                     chineseMonth =  "$!album.chineseMonth"
                                     pageSize = "$!album.pageSize"
                                     recorders =   "$!album.recorders">
                                    <p>$!album.chineseMonth 月</p>
                                    <img src="$!album.coverUrl" alt="">
                                </div>
                            #end

                        #end
                    </div>
                    <!-- 如果需要导航按钮 -->
                    <div class="swiper-button-prev" id="prevBtn$year"></div>
                    <div class="swiper-button-next" id="nextBtn$year"></div>


                </div>
            #end

##            <div class="btnbox fl" style="position: fixed;right: 100px;">
##                <button type="button" class="com-button" onclick="previewImg2()">预览</button>
##                <button type="button" class="com-button return"  onclick="downloadPdf2()">下载</button>
##            </div>
        </div>

        <script>
            var curStaticUrl = '$!curAlbum.staticUrl';
            var curAlbumId = '$!curAlbum.id';
            var curCover = '$!curAlbum.coverUrl';
            var curMonth = '$!curAlbum.month';
            var curChineseMonth = '$!curAlbum.chineseMonth';
            var curPageSize = '$!curAlbum.pageSize';
            var curRecorders = '$!curAlbum.recorders';
            var mySwiper;

            var ticksPerSencond = 0.4;

            var processingHtml = false;
            var processingPdf = false;


            // 加载预览
            var intervalFlag2;

            function previewImg(id) {
                if(processingHtml){
                    return false;
                }
                processingHtml = true;
                // 先请求后台判断是否需要生成html
                layer.load(4, {shade: [0.8, '#393D49']})
                $.ajax({
                    type: "post",
                    url: "/sunday/web/student/getAlbumHtml",
                    data: {"id": id},
                    success: function (data) {
                        //关闭加载
                        if(data.result.ready==1){
                            layer.closeAll('loading');
                            processingHtml = false;
                            album_preview(data.result.url);
                        }else{
//                            beginProcess();
                            intervalFlag2 = setInterval(function(){
//                                layer.load(4, {shade: [0.8, '#393D49']})

                                $.ajax({
                                    type: "post",
                                    url: "/sunday/web/student/getAlbumHtml",
                                    data: {"id": id},
                                    success: function (data2) {
                                        //关闭加载
                                        if(data2.result.ready==1){
                                            album_preview(data2.result.url);
                                            window.clearInterval(intervalFlag2);
//                                            stopProcess();
                                            processingHtml = false;
                                            layer.closeAll('loading');
                                        }else{
                                            console.log("not ready");
                                        }
                                    },
                                    complete:function(){
//                                        layer.closeAll('loading');
                                    }
                                });
                            },5000);
                        }
                    },
                    complete:function(){
//                        layer.closeAll('loading');
                    }
                });

            }

            var tick = 0;
            var intervalFlag1;
            function beginProcess(){
                intervalFlag1 = setInterval(function(){
                    // 每页所占百分比=100/curPageSize
                    tick = tick + (100/curPageSize);
                    if(tick>=100){
                        tick = 99.99;
                        $("#curDownloadBtn").html('<div id="processBar"></div>'+tick+"%");
                        $("#processBar").css("width",tick+"%");
                        window.clearInterval(intervalFlag1);
                    }else{
                        $("#curDownloadBtn").html('<div id="processBar"></div>'+tick.toFixed(2)+"%");
                        $("#processBar").css("width",tick.toFixed(2)+"%");
                    }


                },ticksPerSencond*1000);
            }

            function stopProcess(){
                tick = 100;
                $("#curDownloadBtn").html('<div id="processBar"></div>'+tick+"%");
                $("#processBar").css("width",tick+"%");
                window.clearInterval(intervalFlag1);
                window.clearInterval(intervalFlag);
                window.clearInterval(intervalFlag2);
                processingPdf = false;
                tick = 0;

                $("#curDownloadBtn").html('<div id="processBar"></div><img class="btn-icon" src="$!{adminRoot}/yuhua/admin/img/download.png">下载');
                $("#processBar").css("width","0%");
            }
            // 下载pdf
            var intervalFlag;
            var step;
            function downloadPdf(id){
                if(processingPdf){
                    layer.tips('相册生成中，请耐心等待~', $("#curDownloadBtn"),{tips: [1, '#333'],time:3000});
                    return false;
                }
                processingPdf = true;
                // 先请求后台判断是否需要生成pdf
//                layer.load(4, {shade: [0.8, '#393D49']})
                // 对循环周期进行上下限处理
                step = curPageSize*ticksPerSencond*1000;
                if(step<2000){
                    step=2000;
                }
                if(step>30000){
                    step=30000;
                }
                $.ajax({
                    type: "post",
                    url: "/sunday/web/student/getAlbumPdf",
                    data: {"id": id},
                    success: function (data) {
                        //关闭加载
                        if(data.result.ready==1){
//                            layer.closeAll('loading');
                            processingPdf = false;
                            window.open(data.result.url, "_blank");
                        }else{
                            beginProcess();
                            intervalFlag = setInterval(function(){
//                                layer.load(4, {shade: [0.8, '#393D49']})

                                $.ajax({
                                    type: "post",
                                    url: "/sunday/web/student/getAlbumPdf",
                                    data: {"id": id},
                                    success: function (data2) {
                                        //关闭加载
                                        if(data2.result.ready==1){
                                            window.open(data2.result.url, "_blank");
                                            window.clearInterval(intervalFlag);
                                            stopProcess();
                                            processingPdf = false;
//                                            layer.closeAll('loading');
                                        }else{
                                            console.log("not ready");
                                        }
                                    },
                                    complete:function(){
//                                        layer.closeAll('loading');
                                    }
                                });
                            },step);
                        }
                    },
                    complete:function(){
//                        layer.closeAll('loading');
                    }
                });
            }

            // 动态
            function previewImg2() {
                album_preview(curStaticUrl);
            }

            function downloadPdf2(){
//                window.open(curPdfUrl, "_blank");
            }

            // 预览弹窗
            //素材预览
            function album_preview(url) {
                $("#ablum_view_div").show();
                $("#ablum_view_mask").show();
                $("#albumFrame").attr("src",url);
            }

            function changeMainAlbum(){
                $("#mainAlbumCover").attr("src",curCover);
                $("#mainAlbumMonth").html(curChineseMonth);
                $("#mainAlbumPages").html(curPageSize);
                $("#mainAlbumRecorders").html(curRecorders);
                $("#curPreviewBtn").attr("onclick","previewImg('"+curAlbumId+"')");
                $("#curDownloadBtn").attr("onclick","downloadPdf('"+curAlbumId+"')");
            }


        </script>
    </div>
    <div class="mask"  id="ablum_view_mask" onclick="$('#albumFrame').attr('src','');$(this).hide();$('#ablum_view_div').hide();"></div>
    <div class="preview-popup on" id="ablum_view_div">
        <div class="preview-popup-con" style="    height: 520px; margin-top: 50px;padding: 0px;width: 320px; ">
            <iframe id="albumFrame">

            </iframe>
        </div>
    </div>
    <div class="clearfix"></div>
</form>
<!-- 查询 -->
</div>
<script>

    $(document).ready(function(){
        $(".albums-element").click(function(obj){
            curStaticUrl = $(this).attr("staticUrl");
            curAlbumId =  $(this).attr("albumId");
            curCover = $(this).attr("cover");
            curMonth =  $(this).attr("month");
            curChineseMonth =  $(this).attr("chineseMonth");
            curPageSize =  $(this).attr("pageSize");
            curRecorders =  $(this).attr("recorders");
            changeMainAlbum();
        });

        var intevalFlag = setInterval(function(){
            if(Swiper != undefined&&Swiper!=null){
                clearInterval(intevalFlag);
                mySwiper = new Swiper('.swiper-container', {
                    // autoplay:true,//等同于以下设置
                    initialSlide :$!albums.data.size()-1,
                    slidesPerView : 5,
                    centeredSlides : true,
                    slideToClickedSlide: true,
                    loop:false,
                    navigation: {
                        nextEl: '.swiper-button-next',
                        prevEl: '.swiper-button-prev',
                    },
                    on: {
                        slideChangeTransitionEnd: function(){
                            $(this.slides[this.realIndex]).trigger("click");
                        },
                    },
                });
            }
        }, 500);

        stopProcess();
    });

    //第一步，校验学生-家长信息是否为唯一
    function sunday_student_check_checkStudentIsExist(){
        stopProcess();
        var name =  $("#sunday_student_form input[name='name']").val();
        var mobile =null;
        var id = "$!student.id";
        if(id == "0"){
            if(name == null || name == ""){
                layer.alert("请填写宝贝姓名");
                return ;
            }
            parentName = $("#sunday_student_form input[name='parentName']").val();
            if(parentName == null || parentName == ""){
                layer.alert("请填写家长姓名");
                return ;
            }
            parentTile = $("#sunday_student_form input[name='parentTile']").val();
            if(parentTile == null || parentTile == ""){
                layer.alert("请填写家长称谓");
                return ;
            }
            parentMobile = $("#sunday_student_form input[name='parentMobile']").val();
            if(parentMobile == null || parentMobile == ""){
                layer.alert("请填写家长手机号");
                return ;
            }
             mobile =$("#sunday_student_form input[name='parentMobile']").val();
        }
        sunday_student_check_checkStudentExistMsg();
    }
    //第二步，新增，编辑学生时，获取重名信息
   function  sunday_student_check_checkStudentExistMsg(){
       var id = "$!student.id";
       var name =  $("#sunday_student_form input[name='name']").val();

       var guardId = '';

       if(stuType == 1){
           guardId = $("#sunday_student_form_guardId").val();
       }

       if(qinziType == 1 && stuType == 0){
           guardId = $("#sunday_student_form_qinzi_guardId").val();
       }

       layer.load(4, {shade: [0.8, '#393D49']});
       //不要异步操作
       $.ajax({
           type: "post",
           url: "/sunday/web/student/checkStudentExistMsg",
           data: {"id": id,"name":name,'guardId':guardId},
           async: false,
           success: function (data) {
               //关闭加载
               layer.closeAll('loading');
               if (data.code == 0) {
                   sunday_student_status_check();
               } else {
                   //如果重复，需要确认交互
                   layer.confirm(data.message, function (index) {
                       sunday_student_status_check();
                       //最后手动关闭
                       layer.close(index);
                   });
               }
           }
       });
   }
    //第三步，校验学生离园或在园状态
    function sunday_student_status_check(){
        var oldStatus = "$!student.status";
        var status =   $("#sunday_student_form_status_div").children(".on").attr("value");
        var name = $("#sunday_student_form input[name='name']").val();
        if(oldStatus!=status){
            var msg = "请确认"+name+"已经入园?";
            if(status == 0){
                msg ="请确认"+name+"已经离园，设置离园后所有园区的信息将不会对家长更新，部分功能不可使用?";
            }
            layer.confirm(msg, function (index) {
                $("#sunday_student_form").submit();
                //最后手动关闭
                layer.close(index);
            });
        }else{
            $("#sunday_student_form").submit();
        }
    }
</script>
<script>


    function updateClassHour(hour) {

        var msg = "修改学生课时可能导致 学生剩余课时为负数,请确定修改?";
        layer.confirm(msg, function (index) {
            layer.prompt({title: '修改课时',value:hour, formType: 0}, function(text, index){

                if(!isNaN(text)){
                    var dot = text.indexOf(".");
                    if(dot != -1){
                        var dotCnt = text.substring(dot+1,text.length);
                        if(dotCnt.length >= 2){
                            layer.tips("请输入正确的数值(最多一位小数)!",'.layui-layer-input',{
                                tips: [2, '#3595CC'],time:2000});
                        }else{
                            $("#classHour_all").val(text);
                            layer.close(index);
                        }
                    }else{
                        $("#classHour_all").val(text);
                        layer.close(index);
                    }
                }else{
                    layer.tips("请输入正确的数值(最多一位小数)!",'.layui-layer-input',{
                        tips: [2, '#3595CC'],time:2000});
                }


            });
        });

    }

    var guardId = "$!student.guardId";
    var bunchId ="$!student.bunchId";

    var stuType = '$!student.stuType';
    var qinziType = '$!student.qinziType';
    var from ='$!from'

    if(qinziType == 1){
        $(".qinziType").show();
        initGuardAndqinZiBunch("sunday_student_form_qinzi_guardId","sunday_student_form_qinzi_bunchId",qinzi_guardId,qinzi_bunchId,"sunday_student_form_provinceId","sunday_student_form_cityId","sunday_student_form_districtId");

    }else{
        $(".qinziType").hide();
    }
    if(stuType == 1){
        $(".stuType").show();
    }else{
        $(".stuType").hide();
    }


    //实例化图片裁剪控件
    //upImg(1/1,'sunday_upload_image_div','sunday_upload_image_input',300,300);
    //实例化省市区园区班级(托班)
    intAreaNoTown("sunday_student_form_provinceId","sunday_student_form_cityId","sunday_student_form_districtId",null,null,null);
    initGuardAndBunch("sunday_student_form_guardId","sunday_student_form_bunchId",guardId,bunchId,"sunday_student_form_provinceId","sunday_student_form_cityId","sunday_student_form_districtId");
    if(guardId > 0 || bunchId > 0){
        $("#sunday_student_form_guardId").attr("disabled",true);
        $("#sunday_student_form_bunchId").attr("disabled",true);
    }else{
        $("#sunday_student_form_provinceId,#sunday_student_form_cityId,#sunday_student_form_districtId").change(function(){
            initGuardAndBunch("sunday_student_form_guardId","sunday_student_form_bunchId",null,null,"sunday_student_form_provinceId","sunday_student_form_cityId","sunday_student_form_districtId");
        })
    }
    var qinzi_guardId = "$!qinzi_guardId";
    var qinzi_bunchId = "$!qinzi_bunchId";
    laydate.render({
        elem: '#sunday_student_form_birthDate' //指定元素

    });
    laydate.render({
        elem: '#sunday_student_form_joinDate' //指定元素
    });
    $("#sunday_student_form_sex_div,#sunday_student_form_status_div,#sunday_student_form_parentChildType_div").children().click(function(){
        $(this).addClass("on");
        $(this).siblings().removeClass("on")
    })

    if(parseInt(qinziType) == 1){
        $("#sunday_student_form_qinziType_div").children(":first").click();
        var num = $(".qinziBunchNum").length;

        if(num >0){
            for(var i = 1;i<=num;i++){
                var guardId = $("#qinz_guardId_"+i).val();
                if(guardId && guardId != ''){
                    var bunchId1 = $("#qinz_bunchId_"+i).val();
                    initGuardAndqinZiBunch("sunday_student_form_qinzi_guardId_"+i,"sunday_student_form_qinzi_bunchId_"+i,guardId,bunchId1,"sunday_student_form_provinceId","sunday_student_form_cityId","sunday_student_form_districtId");
                }
            }
        }
    }

    $("#sunday_student_form_stuType_div").children().click(function(){
        if('$!student.id' == 0 && qinziType == 1){
            layer.alert("新增亲子班学生时无法勾选托班!");
            return;
        }else{
            if(parseInt(stuType) != 1){
                if($(this).hasClass("on")){
                    $(".stuType").hide();
                    $(this).removeClass("on")
                }else{
                    $(".stuType").show();
                    $(this).addClass("on");
//                    console.log("====1:",guardId);
//                    console.log(bunchId);
//                    $("#sunday_student_form_provinceId").val(guardId);
//                    $("#sunday_student_form_bunchId").val(bunchId);
//                    initGuardAndBunch("sunday_student_form_guardId","sunday_student_form_bunchId",guardId,bunchId,"sunday_student_form_provinceId","sunday_student_form_cityId","sunday_student_form_districtId");
                }
            }
        }


    });
    $("#sunday_student_form_qinziType_div").children().click(function(){
        if('$!student.id' == 0 && stuType == 1){
            layer.alert("新增托班学生时无法勾选亲子班!");
            return;
        }else{
            if(parseInt(qinziType) != 1){
                if($(this).hasClass("on")){
                    $(".qinziType").hide();
                    $(this).removeClass("on")
                }else{
                    $(".qinziType").show();
                    $(this).addClass("on");
                    $("#sunday_student_form_parentChildType_div").children(":first").click();
                    initGuardAndqinZiBunch("sunday_student_form_qinzi_guardId","sunday_student_form_qinzi_bunchId",qinzi_guardId,qinzi_bunchId,"sunday_student_form_provinceId","sunday_student_form_cityId","sunday_student_form_districtId");
                }
            }
        }


    });
    if(stuType == 1){
        $("#sunday_student_form_stuType_div").children(":first").click();
    }else{
        $(".stuType").hide();
    }
    if(qinziType == 1){
        $("#sunday_student_form_qinziType_div").children(":first").click();
    }

    if(qinzi_guardId > 0 || qinzi_bunchId > 0){
        $("#sunday_student_form_qinzi_guardId").attr("disabled",true);
        $("#sunday_student_form_qinzi_bunchId").attr("disabled",true);
    }else{
        $("#sunday_student_form_provinceId,#sunday_student_form_cityId,#sunday_student_form_districtId").change(function(){
            initGuardAndqinZiBunch("sunday_student_form_qinzi_guardId","sunday_student_form_qinzi_bunchId",null,null,"sunday_student_form_provinceId","sunday_student_form_cityId","sunday_student_form_districtId");
        })
    }
    //默认点击第一个
    var studentId = "$!student.id";
    //新增
    if(studentId==null || studentId == 0){
        $("#sunday_student_form_sex_div").children(":first").click();
        $("#sunday_student_form_status_div").children(":first").click();
        if(stuType == 1){
            $("#sunday_student_form_stuType_div").children(":first").click();
        }
        $("#sunday_student_form_parentChildType_div").children(":first").click();
        //2019年5月14日，新增的用户状态必须是在园
        $("#sunday_student_form_status_div").children(":last").remove();
    }else{
        //编辑
        var sex  = "$!student.sex";
        var status ="$!student.status";
        $("#sunday_student_form_sex_div").children().each(function () {
            if(sex == $(this).attr("value")){
                $(this).click();
            }
        });

        $("#sunday_student_form_status_div").children().each(function () {
            if(status == $(this).attr("value")){
                $(this).click();
            }
        });
    }

    // 验证手机号
    function isPhoneNo(phone) {
        var pattern = /^1[0-9]\d{9}$/;
        return pattern.test(phone);
    }

    //实例化表单
    var Required = "*必填!";
    //实例化表单组件
    var validatorMenu=$("#sunday_student_form").validate({
        submitHandler: function(form) {

            var classHour = $("#classHour_all").val();
            if(!isNaN(classHour)){
                var dot = classHour.indexOf(".");
                if(dot != -1){
                    var dotCnt = classHour.substring(dot+1,classHour.length);
                    if(dotCnt.length >= 2){
                        layer.alert("请输入正确的数值(最多一位小数)!");
                        return;
                    }
                }
            }else{
                layer.alert("请输入正确的数值(最多一位小数)!");
                return;
            }


            var studentId = '$!student.id';

            if(studentId == 0){
                var  parentMobile = $("#sunday_student_form input[name='parentMobile']").val();

                if(parentMobile==null||parentMobile==""){
                    layer.alert("请输入正确的手机号!");
                    return ;
                }

                if ($.trim(parentMobile).length == 0) {
                    layer.alert("请输入正确的手机号!");
                    return ;
                } else {
                    if (isPhoneNo(parentMobile) == false) {
                        layer.alert("请输入正确的手机号!");
                        return ;
                    }
                }
            }
            // 学生类型必须选择
            if($("#sunday_student_form_stuType_div").children(".on").size()<=0&&$("#sunday_student_form_qinziType_div").children(".on").size()<=0){

                layer.alert("请选择学生类型!");
                return ;
            }




            //加遮罩层
            layer.load(4, {shade: [0.8, '#393D49']});
            $("#sunday_student_form_guardId").attr("disabled",false);
            $("#sunday_student_form_bunchId").attr("disabled",false);
            $("#sunday_student_form_sex_input").val($("#sunday_student_form_sex_div").children(".on").attr("value"));
            $("#sunday_student_form_status_input").val($("#sunday_student_form_status_div").children(".on").attr("value"));
            if($("#sunday_student_form_stuType_div").children(".on").size()>0){
                $("#sunday_student_form_stuType_input").val($("#sunday_student_form_stuType_div").children(".on").attr("value"));
            }else{
                $("#sunday_student_form_stuType_input").val(0);
            }
            if($("#sunday_student_form_qinziType_div").children(".on").size()>0){
                $("#sunday_student_form_qinziType_input").val($("#sunday_student_form_qinziType_div").children(".on").attr("value"));
            }else{
                $("#sunday_student_form_qinziType_input").val(0);
            }

            $("#sunday_student_form_parentChildType_input").val($("#sunday_student_form_parentChildType_div").children(".on").attr("value"));
            $("#sunday_student_form_guardName_input").val($("#sunday_student_form_guardId option:selected").text());
            $("#sunday_student_form_bunchName_input").val($("#sunday_student_form_bunchId option:selected").text());
            $("#sunday_student_form_bunchType_input").val($("#sunday_student_form_bunchId option:selected").attr("valueType"));
            $("#sunday_student_form_bunchCategory_input").val($("#sunday_student_form_bunchId option:selected").attr("valueCategory"));



            $(form).ajaxSubmit({
                async: false,

                dataType:"json",
                success:function(data){
                    //关闭加载
                    layer.closeAll('loading');
                    if(data.code == 0){
                        if(qinziType == 1){
                            newTab('/sunday/web/student/index2?qinziType=1&isClean=0',null);
                        }else{
                            newTab('/sunday/web/student/index?isClean=0',null);
                        }
                    }else{
                        layer.msg(data.message, {
                            icon: 2
                        });
                    }
                }
            });
        }
    }) ;

    // 托班初始化
    function initGuardAndBunch(H, A, B, E, D, F, C) {
        var I = "/sunday/web/guard/selectNoPage";
        var G = "/sunday/web/bunch/selectTuobanBunchNoPage";
        $("#" + H).unbind("change");
        $("#" + H).children().remove();
        $("#" + A).children().remove();
        $("#" + H).append("<option value=''>请选择园区</option>");
        $.post(I, {
                    "provinceId": $("#" + D).val(),
                    "cityId": $("#" + F).val(),
                    "districtId": $("#" + C).val()
                },
                function(L) {
                    var J = L.result;
                    for (var K = 0; K < J.length; K++) {
                        if (typeof(B) != "undefined" && B != null && B != "" && B == J[K].id) {
                            $("#" + H).append("<option selected value='" + J[K].id + "'>" + J[K].name + "</option>")
                        } else {
                            $("#" + H).append("<option  value='" + J[K].id + "'>" + J[K].name + "</option>")
                        }
                    }
                    $("#" + H).change(function() {
                        $("#" + A).children().remove();
                        $("#" + A).append("<option value=''>请选择班级</option>");
                        if ($(this).val() != "") {
                            $.post(G, {
                                        "guardId": $(this).val()
                                    },
                                    function(N) {
                                        var O = N.result;
                                        for (var M = 0; M < O.length; M++) {
                                            if (typeof(E) != "undefined" && E != null && E != "" && E == O[M].id) {
                                                $("#" + A).append("<option selected value='" + O[M].id + "' valueType='" + O[M].type + "' valueCategory='" + O[M].category + "'>" + O[M].name + "</option>")
                                            } else {
                                                if(O[M].status == 0){
                                                    //$("#" + A).append("<option value='" + O[M].id + "' valueType='" + O[M].type + "' valueCategory='" + O[M].category + "' disabled >" + O[M].name + "</option>")
                                                }else{
                                                    $("#" + A).append("<option value='" + O[M].id + "' valueType='" + O[M].type + "' valueCategory='" + O[M].category + "' >" + O[M].name + "</option>")
                                                }

                                            }
                                        }
                                    })
                        }
                    });
                    $("#" + H).change()
                })
    }
    // 亲子班初始化
    function initGuardAndqinZiBunch(H, A, B, E, D, F, C) {
        var I = "/sunday/web/guard/selectNoPage";
        var G = "/sunday/web/bunch/selectQinziBunchNoPage";
        $("#" + H).unbind("change");
        $("#" + H).children().remove();
        $("#" + A).children().remove();
        $("#" + H).append("<option value=''>请选择园区</option>");
        $.post(I, {
                    "provinceId": $("#" + D).val(),
                    "cityId": $("#" + F).val(),
                    "districtId": $("#" + C).val()
                },
                function(L) {
                    var J = L.result;
                    for (var K = 0; K < J.length; K++) {
                        if (typeof(B) != "undefined" && B != null && B != "" && B == J[K].id) {
                            $("#" + H).append("<option selected value='" + J[K].id + "'>" + J[K].name + "</option>")
                        } else {
                            $("#" + H).append("<option  value='" + J[K].id + "'>" + J[K].name + "</option>")
                        }
                    }
                    $("#" + H).change(function() {
                        $("#" + A).children().remove();
                        $("#" + A).append("<option value=''>请选择班级</option>");
                        if ($(this).val() != "") {
                            $.post(G, {
                                        "guardId": $(this).val()
                                    },
                                    function(N) {
                                        var O = N.result;
                                        for (var M = 0; M < O.length; M++) {
                                            if (typeof(E) != "undefined" && E != null && E != "" && E == O[M].id) {
                                                $("#" + A).append("<option selected value='" + O[M].id + "' valueType='" + O[M].type + "' valueCategory='" + O[M].category + "'>" + O[M].name + "</option>")
                                            } else {
                                                if(O[M].status == 0){
                                                    //$("#" + A).append("<option value='" + O[M].id + "' valueType='" + O[M].type + "' valueCategory='" + O[M].category + "' disabled >" + O[M].name + "</option>")
                                                }else{
                                                    $("#" + A).append("<option value='" + O[M].id + "' valueType='" + O[M].type + "' valueCategory='" + O[M].category + "' >" + O[M].name + "</option>")
                                                }

                                            }
                                        }
                                    })
                        }
                    });
                    $("#" + H).change()
                })
    }
</script>