#foreach($!guard in $!guards)
<div class="col-sm-4">
    <div class="contact-box">
        <a href="javscript:sunday_guard_open()">
            <div class="col-sm-6">
                <div class="text-center">
                    <img alt="image" class=" m-t-xs img-responsive" src="$!guard.image">
                    <div class="m-t-xs font-bold">$!guard.typeStr</div>

                </div>
            </div>
        </a>
        <div class="col-sm-6">
            <a href="#">
                <h3><strong>$!guard.name</strong></h3>
            #* <h3><strong>主键ID:$!guard.id</strong></h3>*#
                <p><i class="fa fa-map-marker"></i> $!guard.provinceName·$!guard.cityName·$!guard.address</p>
            </a>
            <address>
                <a href="">$!guard.guarderName</a><br>
                <abbr title="Phone">Tel:</abbr>$!guard.guarderMobile
            </address>
        </div>
        <div class="clearfix">
            <button  class="btn btn-success" type="button" onclick="sunday_guard_save($!guard.id)">编辑</button>
            <button  class="btn btn-success" type="button" onclick="sunday_bunch_index($!guard.id,$!guard.type)">班级管理</button>
        </div>

    </div>
</div>
#end