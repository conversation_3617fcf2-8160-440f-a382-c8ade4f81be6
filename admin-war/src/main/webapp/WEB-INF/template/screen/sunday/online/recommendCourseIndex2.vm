<!--前端权限公用模板-->
#parse("/screen/sunday/operate.vm")
<style>
    .search-selectpicker select {
        width: 100%;
    }
    .search-selectpicker {
        width:150px;
    }
</style>

<div class="kindergarten-survey">
    <div class="top-wrapper clearfix" style="width: 100%">
        <script>
            $.post("/sunday/web/online/recommendTop",
                    {"type":1,"clientType":"$!clientType"},
                    function (data) {
                        $(".top-wrapper").append(data);
                    })
        </script>
    </div>
    <div class="list" id="sunday_course_2_div" style="    padding: 5px;"></div>
</div>
<script>
    var param_pageNumber = "$!param.pageNumber";
    var param_pageSize = "$!param.pageSize";
    if(param_pageNumber==null|| param_pageNumber==""){
        param_pageNumber=1
    }
    if(param_pageSize==null|| param_pageSize==""){
        param_pageSize=10
    }
    var sunday_course_pageNumber=param_pageNumber;
    var sunday_course_pageSize=param_pageSize;
    var sunday_course_sort="id";
    var sunday_course_order="desc";
    var sunday_course_is_loading=false;
    var sunday_course_is_end=false;
        sunday_course_getData(true);
    function sunday_course_getData(isRemove){
        var t="#sunday_course_2_div";
        if(sunday_course_is_loading||sunday_course_is_end)return;
        if(isRemove){
            $(t).children().remove();
        }
        sunday_course_is_loading=true;
        //同步请求
        $.ajax({
            type: "POST",
            url: "/sunday/web/online/recommendCourseSelectByVideo",
            data: {
                "clientType":"$!clientType"
            },
            dataType: "text",
            async:true,
            success: function(result){
                if(result==null||result==""||result.length<1){
                    sunday_course_is_end=true;
                }else{
                    $(t).append(result);
                    sunday_course_is_loading=false;
                }
            }
        });
    }

    function sunday_course_save(id){
        var html="";
        if(id==0){
            html="新建";
        }else{
            html="编辑";
            newTab("/sunday/web/online/courseInput?id=" + id+"&columnId=$!column.id" , "编辑");
        }
        newTab("/sunday/web/online/courseInput?id=" + id+"&columnId=$!column.id" , html);
    }

    function sunday_course_delete(id){
        var msg = "确认撤销该课程的推荐吗？";
        layer.confirm(msg, function (index) {
            layer.load(4, {shade: [0.8, '#393D49']})
            $.ajax({
                type: "post",
                url: "/sunday/web/online/deleteRecommendCourse",
                data: {"id": id},
                async: false,
                success: function (data) {
                    //关闭加载
                    layer.closeAll('loading');
                    if (data.code == 0) {
                        newTab("/sunday/web/online/recommendCourse?type=1"+"&clientType=$!clientType",null)
                    } else {
                        layer.msg(data.message, {
                            icon: 2
                        });
                    }
                }
            });
            //最后手动关闭
            layer.close(index);
        });
    }
</script>