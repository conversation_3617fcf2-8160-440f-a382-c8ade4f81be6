#set( $layout = "/layout/easyui/h-layout.vm")
<body>
<div class="wrapper wrapper-content animated fadeInRight">
    <div class="row">
        <div class="col-sm-12" id="tableDiv">
            <div class="ibox float-e-margins">
                <div class="ibox-content">
                    <div class="col-sm-12 no-padding">
                        <button type="button" onclick="sunday_product_category_first_standard_save(0,null,$!{categoryId})" class="btn btn-success btn-sm" ><i class="fa fa-plus"></i>&nbsp;新增一级规格（最大4组）</button>
                    </div>

                    <form role="form" class="form-inline" id="sunday_product_category_first_standard_search_form">
                            <input type="hidden"  name="categoryId"  value="${categoryId}" >
                    </form>
                    <!--套餐table-->
                    <table id="sunday_product_category_first_standard_table" class="table table-striped"></table>
                </div>
            </div>
        </div>
    </div>
</div>

<!--新增/修改一级规格-->
 <div id="sunday_product_category_first_standard_save_div" class="modal fade" role="dialog" style="display: none;" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
        <!--modald的header。存放标题什么的-->
                  <form id="sunday_product_category_first_standard_save_form" class="form-horizontal" action="/sunday/web/product/category/saveFirst"  method="post"  enctype="multipart/form-data">
                  <!--隐藏参数-->
                 <input name="id" type="hidden" /> 
				 <input name="categoryId" type="hidden"/>



                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal">×</button>
                        <h4 class="modal-title">一级规格管理</h4>
                    </div>
                      <!--modald的body。存放form里面的数据什么的-->
                    <div class="modal-body">
                                               <div class="row form-horizontal">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label"><span style="color: red;">*</span>一级规格名称：</label>
                                    <div class="col-sm-8">
                                       <input  class="form-control "  name="name" required="required"/>
                                    </div>
                                </div>
                            </div>
                        </div>	 
                    </div>
                
                    <!--modald的footer。存放按钮神马的-->
                    <div class="form-group text-center">
                            <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                            <button  class="btn btn-primary" type="submit">保存</button>
                             
                    </div>
                </form>
        </div>
    </div>
</div> 
<!--二级规格table-->
 <div id="sunday_product_category_second_standard_table_div" class="modal fade" role="dialog" style="display: none;" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
	        <div class="wrapper wrapper-content animated fadeInRight">
			    <div class="row">
			        <div class="col-sm-12" id="tableDiv">
			            <div class="ibox float-e-margins">
			                <div class="ibox-content">
			                    <div class="col-sm-12 no-padding">
			                        <button type="button" id="sunday_product_category_second_standard_save" class="btn btn-success btn-sm" ><i class="fa fa-plus"></i>&nbsp;新增二级规格）</button>
			                    </div>

			                    <form role="form" class="form-inline" id="sunday_product_category_second_standard_search_form">
			                     		<input type="hidden"  name="first">
			                            <input type="hidden"  name="categoryId">

			                    </form>
			                    <!--套餐table-->
			                    <table id="sunday_product_category_second_standard_table" class="table table-striped"></table>
			                </div>
			            </div>
			        </div>
			    </div>
			</div>
        </div>
    </div>
</div> 
<!--新增/修改二级级规格-->
 <div id="sunday_product_category_second_standard_save_div" class="modal fade" role="dialog" style="display: none;" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
        <!--modald的header。存放标题什么的-->
                  <form id="sunday_product_category_second_standard_save_form" class="form-horizontal" action="/sunday/web/product/category/saveSecond"  method="post"  enctype="multipart/form-data">
                  <!--隐藏参数-->
                 <input name="id" type="hidden" /> 
                 <input name="firstId" type="firstId"/>
				 <input name="categoryId" type="hidden"/>



                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal">×</button>
                        <h4 class="modal-title">二级级规格管理</h4>
                    </div>
                      <!--modald的body。存放form里面的数据什么的-->
                    <div class="modal-body">
                                               <div class="row form-horizontal">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label"><span style="color: red;">*</span>二级规格名称：</label>
                                    <div class="col-sm-8">
                                       <input  class="form-control "  name="name" required="required"/>
                                    </div>
                                </div>
                            </div>
                        </div>	 
                    </div>
                
                    <!--modald的footer。存放按钮神马的-->
                    <div class="form-group text-center">
                            <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                            <button  class="btn btn-primary" type="submit">保存</button>
                             
                    </div>
                </form>
        </div>
    </div>
</div> 
</body>
<script>
    $("#sunday_product_category_first_standard_table").bootstrapTable({
        columns: [
        	{field : 'categoryName',title : '分类名称',width:120,align:'center'},
            {field : 'name',title : '一级规格名称',width:200,align:'center'},
            {field : 'operate',title : '操作',width:100,align:'center',
                formatter : function(value, row, index) {
                    var operate="&nbsp;&nbsp;&nbsp<button class='btn btn-xs btn-warning' type='button' onclick='sunday_product_category_first_standard_save("+row.id+"\""+row.name+"\""+row.categoryId+")'>编辑</button>";
                    operate+="&nbsp;&nbsp;&nbsp<button class='btn btn-danger btn-xs' type='button' onclick='sunday_product_category_first_standard_delete("+row.id+")'>删除</button>";
                    operate+="&nbsp;&nbsp;&nbsp;&nbsp;<button class='btn btn-sm btn-primary' type='button' onclick='sunday_product_category_second_standard_open("+row.id+","+row.categoryId+");'>配置分类二级规格</button>";   
                    return operate;
                }
            }],
        url:'/sunday/web/product/category/selectFirst',
        cache:false,
        pagination: true,

        pageSize: 50,
        pageList: [50, 100,150],
        pageNumber:1,
        sidePagination: 'server',
        queryParams: function (params) {
            $('#sunday_product_category_first_standard_search_form').find('input[name]').each(function () {
                // //params
                var name=$(this).attr('name');
                var value=$(this).val();
                if(value==null||value==""){
                    value=null;
                }
                params[name]=value;

            });
            //实例化组件-自带参数
            params['offset']=params.offset;
            params['limit']=params.limit;
            params['sort']=params.sort;
            params['order']=params.order;

            return params;
        },
        onClickRow:function (row, $element, field){
                $element.addClass("row-select");
                $element.siblings('tr').removeClass("row-select");
        }
    });
  	//搜索一级分类
    function sunday_product_category_first_standard_search() {
        $("#sunday_product_category_first_standard_table").bootstrapTable("refresh");
    }

    //新增or修改一级分类
    function sunday_product_category_first_standard_save(id,name,categoryId){
         $("#sunday_product_category_first_standard_save_form").form("clear");
        $("#sunday_product_category_first_standard_save_form").form("load",{"id":id,"categoryId":categoryId});
        //新增
        if(id!=null&&id!=0&&name!=null){
           $("#sunday_product_category_first_standard_save_form").form("load",{"name":name});
        }
           remoteModal("sunday_product_category_first_standard_save_div",null)
        
    }
    //一级分类form实例化
    var validatorMenu=$("#sunday_product_category_first_standard_save_form").validate({

        submitHandler: function(form) {
            //默认
            //layer.load();
            //加遮罩层
            layer.load(4, {shade: [0.8, '#393D49']})
           
           
        $(form).ajaxSubmit({
           
            //你表单提交就要切换页面，ajax加载又是异步的。两边同时进行，
            //当ajax加载在切换页面之前完成，就会执行回调，反之不会执行
            //加同步提交数据
                async: false,
                success:function(data){
                    //关闭加载
                       layer.closeAll('loading');
                    if(data.code ==0){
                        
                        //刷新列表
                         sunday_product_category_first_standard_search();
                         //关闭窗口
                         $("#sunday_product_category_first_standard_save_div").modal("toggle");

                    }else{
                        layer.msg('保存失败', {
                            icon: 6
                        });
                    }
                }
            });
        }
    }) ;



    
    //删除一级分类	
    function sunday_product_category_first_standard_delete(firstId) {
        var msg="确认要删除这条一级规格吗?";
        layer.confirm(msg, function(index){
            layer.load(4, {shade: [0.8, '#393D49']})
            //不要异步操作
            $.ajax({
                type : "post",
                url : "/sunday/web/product/deleteFirst",
                data : {"firstId":firstId},
                async : false,
                success : function(data){
                    //关闭加载
                    layer.closeAll('loading');
                    if(data.code == 0){
                        sunday_product_category_first_standard_search()

                    }else{
                        layer.msg('删除失败', {
                            icon: 2
                        });
                    }


                }
            });
            //最后手动关闭
            layer.close(index);

        });
    }
</script>
<script type="text/javascript">
//打开二级规格
function sunday_product_category_second_standard_open(firstId,categoryId){
	//实例化新增/修改按钮
	$("#sunday_product_category_second_standard_save").click(function(){
  		$(this).unbind();
  		sunday_product_category_second_standard_save(0,firstId,categoryId,null);
	});
	//实例化table的查询参数
	$("#sunday_product_category_second_standard_search_form").form("clear");
     $("#sunday_product_category_second_standard_search_form").form("load",{"firstId":firstId,"categoryId":categoryId});





	//示例化二级规格table
	 $("#sunday_product_category_second_standard_table").bootstrapTable({
        columns: [
        	{field : 'categoryName',title : '分类名称',width:120,align:'center'},
        	 {field : 'firstName',title : '一级规格名称',width:200,align:'center'},
            {field : 'name',title : '二级规格名称',width:200,align:'center'},
            {field : 'operate',title : '操作',width:100,align:'center',
                formatter : function(value, row, index) {
                    var operate="&nbsp;&nbsp;&nbsp<button class='btn btn-xs btn-warning' type='button' onclick='sunday_product_category_second_standard_save("+row.id+","+row.firstId+","+row.categoryId+"\""+row.name+"\")'>编辑</button>";
                    operate+="&nbsp;&nbsp;&nbsp<button class='btn btn-danger btn-xs' type='button' onclick='sunday_product_category_second_standard_delete("+row.id+")'>删除</button>";
                    return operate;
                }
            }],
        url:'/sunday/web/product/category/selectSecond',
        cache:false,
        pagination: true,

        pageSize: 50,
        pageList: [50, 100,150],
        pageNumber:1,
        sidePagination: 'server',
        queryParams: function (params) {
            $('#sunday_product_category_second_standard_search_form').find('input[name]').each(function () {
                // //params
                var name=$(this).attr('name');
                var value=$(this).val();
                if(value==null||value==""){
                    value=null;
                }
                params[name]=value;

            });
            //实例化组件-自带参数
            params['offset']=params.offset;
            params['limit']=params.limit;
            params['sort']=params.sort;
            params['order']=params.order;

            return params;
        },
        onClickRow:function (row, $element, field){
                $element.addClass("row-select");
                $element.siblings('tr').removeClass("row-select");
        }
    });
  remoteModal("sunday_product_category_second_standard_table_div",null)

}
//搜索二级分类
function sunday_product_category_second_standard_search() {
    $("#sunday_product_category_second_standard_table").bootstrapTable("refresh");
}

//新增or修改二级规格
function sunday_product_category_second_standard_save(id,firstId,categoryId,name){
	 $("#sunday_product_category_second_standard_save_form").form("clear");
        $("#sunday_product_category_second_standard_save_form").form("load",{"id":id,"firstId":firstId,"categoryId":categoryId});
        //新增
        if(id!=null&&id!=0&&name!=null){
           $("#sunday_product_category_second_standard_save_form").form("load",{"name":name});
        }
           remoteModal("sunday_product_category_first_standard_save_div",null)
}
//删除二级分类	
    function sunday_product_category_second_standard_delete(secondId) {
        var msg="确认要删除这条二级级规格吗?";
        layer.confirm(msg, function(index){
            layer.load(4, {shade: [0.8, '#393D49']})
            //不要异步操作
            $.ajax({
                type : "post",
                url : "/sunday/web/product/category/deleteSecond",
                data : {"secondId":secondId},
                async : false,
                success : function(data){
                    //关闭加载
                    layer.closeAll('loading');
                    if(data.code == 0){
                        sunday_product_categorys_second_standard_search()
                    }else{
                        layer.msg('删除失败', {
                            icon: 2
                        });
                    }


                }
            });
            //最后手动关闭
            layer.close(index);

        });
    }
</script>

