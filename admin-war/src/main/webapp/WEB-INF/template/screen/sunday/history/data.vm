#parse("/screen/sunday/operate.vm")
<table width="100%"  border="0" cellspacing="0" cellpadding="0" >
    <tr>
        <th width="6%">序号</th>
        <th width="8%">姓名</th>
        <th width="12.5%">所在园区</th>
        <th width="12.5%">所在班级</th>
        <th width="20%">成长记录内容</th>
        <th width="7%">点赞数量</th>
        <th width="7%">评论数量</th>
        <th width="7%">发布时间</th>
        <th>操作</th>
    </tr>
    #foreach($!history in $!historys)
        <tr>
            <td>$!velocityCount</td>
            <td>$!history.memberName</td>
            <td>$!history.guardName</td>
            <td>$!history.bunchName</td>
            <td>$!history.content</td>
            <td>$!history.zanNum</td>
            <td>$!history.commentNum</td>
            <td>$!history.createTime</td>
            <td>
                <button type="button" onclick="newTab('/sunday/web/history/input?id=$!history.id')" class="sunday_history_view">查看详情</button>
                <button type="button" onclick="sunday_history_delete($!history.id)" class="sunday_history_delete">删除</button>
            </td>
        </tr>
    #end
</table>

<div class="com-page-wrapper">
    <span class="page-num">共$!total条，$!currentPage/$!totalPage页</span>
    <!--判断是否能够转跳页数-->
        <span class="next pre #if($!prevPage >= $!minPage) on" onclick=sunday_history_search_page($!prevPage,null) #end"></span>

        <span class="next #if($!maxPage >= $!nextPage) on" onclick=sunday_history_search_page($!nextPage,null) #end"></span>
    <span class="text">跳转到：</span>
    <input type="text" value="$!currentPage" min="$!minPage" max="$!maxPage">
    <span class="go" onclick="sunday_history_search_page($(this).prev().val(),null,$!maxPage)">GO</span>
</div>