#parse("/screen/sunday/operate.vm")
<table width="100%"  border="0" cellspacing="0" cellpadding="0" >
    <tr>
        <th width="5%">序号</th>
        <th width="5%">姓名</th>
        <th width="10%">角色</th>
        <th width="10%">账号</th>
        <th width="10%">电话</th>
        <th width="15%">关联园区</th>
        <th width="15%">关联班级</th>
        <th width="10%">状态</th>
        <th width="10%">是否超级管理员</th>
        <th>操作</th>
    </tr>
    #foreach($!user in $!users)
        <tr>
            <td>$!velocityCount</td>
            <td>$!user.name</td>
            <td>$!user.roleName</td>
            <td>$!user.userName</td>
            <td>$!user.mobile</td>
            <td>$!user.guardNames</td>
            <td>$!user.bunchNames</td>

            <td>#if($!user.status == 1)正常#else 冻结#end</td>
            <td>#if($!user.isSuper == 1)是#else 否#end</td>
            <td>
                <button type="button" onclick="sunday_user_save($!user.id)" class="sunday_user_edit">编辑</button>
                <button type="button" onclick="sunday_user_delete($!user.id)" class="button-delete sunday_user_delete">删除</button>
            </td>
        </tr>
    #end
</table>
<div class="com-page-wrapper">
    <span class="page-num">共$!total条，$!currentPage/$!totalPage页</span>
    <!--判断是否能够转跳页数-->
        <span class="next pre #if($!prevPage >= $!minPage) on" onclick=sunday_user_search_page($!prevPage,null) #end"></span>

        <span class="next #if($!maxPage >= $!nextPage) on" onclick=sunday_user_search_page($!nextPage,null) #end"></span>
    <span class="text">跳转到：</span>
    <input type="text" value="$!currentPage" min="$!minPage" max="$!maxPage">
    <span class="go" onclick="sunday_user_search_page($(this).prev().val(),null,$!maxPage)">GO</span>
</div>