#parse("/screen/sunday/operate.vm")
<style>
    .backButton{
        padding: 0 9px;
        margin: 0 0 0 16px;
        line-height: 30px;
        font-size: 14px;
        background: #27cdd7;
        color: #fff;
        border-radius: 4px;
        border: none;
        outline: none;
        margin-top: 20px;
        margin-left: 0px;
    }
</style>
<div class="weekly-plan">
    <div class="search-wrapper clearfix">
        <div class="com-title-wrapper">
            <div class="title">课程更新记录</div>
        </div>

        <table class="search-table" style="margin-top: 20px">
        <tr>
            <td>
                <div class='search-selectpicker'>
                    <select class='' id='column_select'>
                        <option value="">栏目名称</option>
                        #foreach($!column in $!columns)
                            <option value="$!column.id">$!column.name</option>
                        #end
                    </select>
                </div>
            </td>
            <td>
                <div class='search-selectpicker'>
                    <select class='' id='pushType_select'>
                        <option value="">更新类型</option>
                        <option value="0">自动更新</option>
                        <option value="1">手动更新</option>
                    </select>
                </div>
            </td>
            <td>
                <div class='search-selectpicker'>
                    <select class='' id='updateStatus_select'>
                        <option value="">更新状态</option>
                        <option value="0">已更新</option>
                        <option value="1">未更新</option>
                    </select>
                </div>
            </td>
            <td>
                <button type="button" class="search-button" onclick="sunday_plan_search(true)">搜索</button>
            </td>

        </tr>
    </table>
    </div>
    <a class="search-add-button" style="margin-top: 20px" href="javascript:sunday_plan_push_save(0);" >手动更新(栏目)</a>
    <a class="search-add-button" style="margin-top: 20px" href="javascript:sunday_plan_push_save(1);" >手动更新(课程)</a>
    <button class="backButton" style="margin-top: 20px;margin-left: 10px" onclick="back()" >返回</button>
    <!-- 查询 -->

    <div class="list" id="sunday_push_plan_div">


    </div>

    <!--手动更新(栏目)-->
    <div id="sunday_updatebook_div" class="modal fade" role="dialog" style="display: none;" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <form id="sunday_updatebook_form" class="form-horizontal" action="/sunday/web/online/manualPush" method="post"
                      enctype="multipart/form-data">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal"></button>
                        <h4 class="modal-title">手动更新(栏目)</h4>
                    </div>
                    <!--modald的body。存放form里面的数据什么的-->
                    <div class="modal-body">
                        <div class="row form-horizontal">
                        </div>
                        <div class="row form-horizontal">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label"><font color="#FF0000">*</font>课程栏目：</label>
                                    <div class="col-sm-8">
                                        <div class='search-selectpicker' style="width: 100%;margin-left: 0px">
                                            <select id='column_form_select' name="columnId" required="required">
                                                #foreach($!column in $!columns)
                                                    <option value="$!column.id">$!column.name</option>
                                                #end
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row form-horizontal">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label"><font color="#FF0000">*</font>更新节数：</label>
                                    <div class="col-sm-8">
                                        <input class="form-control" type="text" id="pushNum"  name="num" required="required">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--第三行-->
                    </div>
                    <div class="form-group text-center">
                        <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                        <button class="btn btn-primary" type="submit">更新</button>

                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<script>
    $.ajaxSetup({
        async: false
    });
    var param_pageNumber = "$!param.pageNumber";
    var param_pageSize = "$!param.pageSize";
    console.log("param_pageNumber="+param_pageNumber+",param_pageSize="+param_pageSize)
    if(param_pageNumber==null|| param_pageNumber==""){
        param_pageNumber=1
    }
    if(param_pageSize==null|| param_pageSize==""){
        param_pageSize=10
    }
    var sunday_role_pageNumber=param_pageNumber;
    var sunday_role_pageSize=param_pageSize;
    var sunday_role_sort="id";
    var sunday_role_order="desc";
    var sunday_role_is_loading=false;
    var sunday_role_is_end=false;
    var sunday_column_id="";
    var sunday_push_type= '';
    var sunday_update_status= '';
    //输入订单号查询
    function sunday_plan_search(isPageNumber){

        sunday_column_id = $("#column_select").val();
        sunday_push_type = $("#pushType_select").val();
        sunday_update_status = $("#updateStatus_select").val();
        sunday_role_is_loading=false;
        sunday_role_is_end=false;
        if(isPageNumber){
            sunday_role_pageNumber =1;
        }
        sunday_role_getData(true);
    }
    function sunday_role_search_page(pageNumber,pageSize,maxPageNumber){
        if(maxPageNumber!=null&&maxPageNumber!=""&&typeof(maxPageNumber)!="undefined"){
            if(parseInt(pageNumber)>parseInt(maxPageNumber)){
                return;
            }
        }
        sunday_role_pageNumber =pageNumber;
        sunday_role_pageSize =pageSize==null?sunday_role_pageSize:pageSize;
        sunday_role_getData(true);
    }
    function sunday_role_getData(isRemove){
        var t="#sunday_push_plan_div";

        if(sunday_role_is_loading||sunday_role_is_end)return;
        if(isRemove){
            $(t).children().remove();
        }
        sunday_role_is_loading=true;

        //同步请求
        $.ajax({
            type: "GET",
            url: "/sunday/web/online/pushPlanList",
            data: {
                "pageSize":sunday_role_pageSize,
                "pageNumber":sunday_role_pageNumber,
                "sort":sunday_role_sort,
                "order":sunday_role_order,
                "columnId":sunday_column_id,
                "updateStatus":sunday_update_status,
                "pushType":sunday_push_type
            },
            dataType: "text",
            async:true,
            success: function(result){
                //d layer.closeAll('loading');
                //返回的是html
                if(result==null||result==""||result.length<1){
                    sunday_role_is_end=true;
                }else{
                    $(t).append(result);
                    sunday_role_is_loading=false;
                }
            }
        });
    }
    sunday_plan_search(false);


    function back(){
        newTab("/sunday/web/online/index" , null);
    }
    
    function sunday_plan_push_save(status) {
        if(status == 0){
            $("#sunday_updatebook_form").form("clear");
            remoteModal("sunday_updatebook_div", "")
        }else{
            newTab("/sunday/web/online/manualPushCourse?columnId=1" , null);
        }

    }

    var validatorMenu = $("#sunday_updatebook_form").validate({

        submitHandler: function (form) {


            var num = $("#pushNum").val();

            if(isNaN(num) || num<=0){
                layer.msg("请输入正确的更新节数", {icon: 2});
                return ;
            }

            //加遮罩层
            layer.load(4, {shade: [0.8, '#393D49']})
            $(form).ajaxSubmit({

                //你表单提交就要切换页面，ajax加载又是异步的。两边同时进行，
                //当ajax加载在切换页面之前完成，就会执行回调，反之不会执行
                //加同步提交数据
                async: false,
                dataType: "json",
                success: function (data) {
                    //关闭加载
                    layer.closeAll('loading');
                    if (data.code == 0) {
                        sunday_role_getData(true);
                        closeModal("sunday_updatebook_div");
                    } else {
                        layer.msg(data.message, {
                            icon: 6
                        });
                    }
                }
            });
        }
    });
</script>