
<script src="$!adminRoot/plug/aliyun-upload-sdk-1.4.0/lib/es6-promise.min.js" defer="true"></script>
<script src="$!adminRoot/plug/aliyun-upload-sdk-1.4.0/lib/aliyun-oss-sdk-5.2.0.min.js" defer="true"></script>
<script src="$!adminRoot/plug/aliyun-upload-sdk-1.4.0/aliyun-upload-sdk-1.4.0.min.js" defer="true"></script>

#parse("/screen/sunday/operate.vm")
<div class="add-imagetext">
    <div class="com-title-wrapper">
        <div class="title">新建音频素材</div>
    </div>
    <div style="position: relative;" class="clearfix">
    <div class="con">
        <form id="sunday_material_form" class="form-horizontal" action="/sunday/web/material/save" method="post"  enctype="multipart/form-data">
            <input type="hidden" name="id" value="$!{material.id}">
            <input type="hidden" name="type" value="2"/>
            <input type="hidden" name="groupId" value="$!material.groupId"/>
            <div class="item clearfix">
                <div class="com-titl"><i>*</i>标题：</div>
                <input type="text"  maxlength="100" class="com-input-item fl"  name="name" value="$!{material.name}" required="required" id="sunday_material_form_name">
            </div>

            <div class="item clearfix">
                <div class="com-titl"><i>*</i>封面：</div>
                <div class="com-add-img xzBtn"
                     style="width:200px;height:150px;background-image: url(#if($material.image)$imgRoot$!material.image#else$!{adminRoot}/yuhua/admin/img/add.png#end);"
                     id="sunday_upload_image_div" onclick="upImg2(3,'sunday_upload_image_div','sunday_upload_image_input',200,150)">
                    <input type="hidden" id="sunday_upload_image_input" name="image" value="$!material.image" >
                </div>
                <div class="com-titl mw80">（800px*600px）</div>
            </div>
            <script>
                //实例化图片裁剪控件
               // upImg(3,'sunday_upload_image_div','sunday_upload_image_input',200,150);
                function upImg2(bili,sunday_upload_image_div,sunday_upload_image_input,width,height,callType) {
                    //1,加载素材库
                    layer.load(4, {shade: [0.8, '#393D49']})
                    //不要异步操作
                    $.ajax({
                        type: "post",
                        url: "/sunday/web/material/upload/index",
                        data: {"type": 3,"bili":bili,"imageDiv":sunday_upload_image_div,"imageInput":sunday_upload_image_input,"width":width,"height":height,"callType":callType},
                        async: false,
                        success: function (data) {
                            //关闭加载
                            layer.closeAll('loading');
                            $(".right-wrapper").after(data);
                        }
                    });

                }
            </script>
            <div class="item clearfix">
                <div class="com-titl"><i>*</i>上传音频：</div>
                <div class="text">音频不能超过1G，音频时长不少于1秒，不多于10小时</div>
            </div>
            <div class="item clearfix">
                <div class="com-titl" style="height: 30px;">MP3格式：</div>

                <input type="file"  id="file" accept="audio/*"></button>
            </div>
            <div class="item clearfix">
                <div class="com-titl">点击操作：</div>
                <button type="button" class="com-button return sunday_material_save" style="position: relative;overflow: hidden;" onclick="start()">开始上传</button>
            </div>
            <div class="item clearfix">
                <div class="com-titl"><i>*</i>文件名称：</div>
                <input type="text" placeholder="选择文件后自动获取" class="com-input-item fl"  name="sourceName" value="$!{material.sourceName}" required="required" readonly id="sourceName">
            </div>
            <div class="item clearfix" id="shangchuan" style="display: none">
                <div class="com-titl"><i>*</i>上传进度：</div>
                <div class="progress">
                    <div id="shangchuanProcess" style="width: 0%" aria-valuemax="100" aria-valuemin="0" aria-valuenow="35" role="progressbar" class="progress-bar progress-bar-success">

                    </div>
                </div>
            </div>
            <div class="item clearfix">
                <div class="com-titl"><i>*</i>音频大小(KB)：</div>
                <input type="text" placeholder="选择文件后自动获取" class="com-input-item fl"  name="sourceSize" value="$!{material.sourceSize}" required="required" readonly id="sourceSize">
            </div>
            <div class="item clearfix">
                <div class="com-titl"><i>*</i>音频大小(秒)：</div>
                <input type="text"  placeholder="选择文件后自动获取" class="com-input-item fl"  name="sourceTime" value="$!{material.sourceTime}" required="required" readonly id="sourceTime">
            </div>
            <div class="item clearfix">
                <div class="com-titl"><i>*</i>音频播放地址：</div>
                <input type="text" placeholder="上传完成后自动获取"  class="com-input-item fl"  name="source" value="$!{material.source}" required="required" readonly id="source">
            </div>
            <div class="item clearfix">
                <div class="com-titl" style="height: 30px"></div>
                <div class="button-wrapper clearfix fl">
                    <a href="javascript:yuhua_return('/sunday/web/material/index?type=2&groupId=$!material.groupId');" class="com-button return sunday_material_save">返回</a>
                    <button type="submit" class="com-button sunday_material_add sunday_material_edit">保存</button>
                </div>
            </div>
        </form>
        <!--asc上传凭证-->
        <input type="hidden" id="uploadAuth" >
        <input type="hidden" id="uploadAddress" >
        <input type="hidden" id="videoId" >

        <!--利用一个隐藏的video标签获取音频时常-->
        <audio  style="display: none" controls="controls" id="virtualVideo" oncanplaythrough="getVideoTime(this)">

        </audio>
    </div>
</div>
</div>


<script>
    /*   var accessKeyId = document.getElementById("accessKeyId").value;
       var accessKeySecret = document.getElementById("accessKeySecret").value;
       var secretToken = document.getElementById("secretToken").value;*/

    var uploader = new AliyunUpload.Vod({
        // 文件上传失败
        'onUploadFailed': function (uploadInfo, code, message) {
            // alert("上传失败")
            console.log("onUploadFailed: file:" + uploadInfo.file.name + ",code:" + code + ", message:" + message);
            layer.closeAll('loading')
        },
        // 文件上传完成
        'onUploadSucceed': function (uploadInfo) {
            // alert("上传成功")
            //  console.info(uploadInfo)
            //  layer.closeAll('loading')
            $("#shangchuan").hide();
            $("#shangchuanProcess").attr("style","width:0%");
            $("#source").val("http://eduvideo.wonderlandnet.cn/"+uploadInfo.object);
        },
        // 文件上传进度
        'onUploadProgress': function (uploadInfo, totalSize, loadedPercent) {
            // alert("上传中")
            console.log("onUploadProgress:file:" + uploadInfo.file.name + ", fileSize:" + totalSize + ", percent:" + (loadedPercent * 100.00).toFixed(2) + "%");
            //显示正在上传效果
            $("#shangchuan").show();
            var process = (loadedPercent * 100.00).toFixed(2);
            process=process+"%";
            $("#shangchuanProcess").attr("style","width:"+process);

        },
        // 开始上传
        'onUploadstarted': function (uploadInfo) {
            //alert("准备上传")
            //获取凭证
            var uploadAuth =$("#uploadAuth").val();
            var uploadAddress = $("#uploadAddress").val();
            var videoId = $("#videoId").val();
            //处理凭证
            uploader.setUploadAuthAndAddress(uploadInfo, uploadAuth, uploadAddress,videoId);
            console.log("onUploadStarted:" + uploadInfo.file.name + ", endpoint:" + uploadInfo.endpoint + ", bucket:" + uploadInfo.bucket + ", object:" + uploadInfo.object);
        },
        'onUploadEnd':function(uploadInfo){
            //alert("上传结束")
            console.log("onUploadEnd: uploaded all the files");
            $("#shangchuan").hide();
            $("#shangchuanProcess").attr("style","width:0%");
        }
    });

    //$("file").addEventListener('change', addFile);

    function getVideoTime(ele) {
        var second = Math.ceil(ele.duration);
        $("#sourceTime").val(second)
    }
    //将文件加入到阿里云ASC队列中
    var addFile = function (event) {
        //先清空
        uploader.cleanList();
        var userData= '{"Vod":{"StorageLocation":"","UserData":{"IsShowWaterMark":"false","Priority":"7"}}}';
        //for(var i=0; i<event.target.files.length; i++) {
        console.log("add file: " + event.target.files[0].name);
        // 点播上传。每次上传都是独立的OSS object，所以添加文件时，不需要设置OSS的属性
        uploader.addFile(event.target.files[0], null, null, null, userData);
        $("#sourceName").val(event.target.files[0].name);
        $("#sourceSize").val(event.target.files[0].size)
        $("#sourceTime").val(event.target.files[0].duration);
        $("#virtualVideo").attr("src",URL.createObjectURL(event.target.files[0]));
        console.info(URL.createObjectURL(event.target.files[0]))
        // $("#virtualVideo").attr("src"," http://video.pereal.cn/customerTrans/ead00d9597b0f55e431f69021d147838/4063bfe4-166af9d09c1-0004-de36-2ef-460d0.mp4");

        //console.info(event.target.files[0].duration)
        //$("#virtualVideo").play();


        // }
    };
    document.getElementById("file")
            .addEventListener('change', addFile);

    /* function getToken() {

     }*/
    //开始上传
    function start() {

        console.log("start upload.");
        //获取需要上传的文件
        // getToken();
        //获取上传的凭证
        var title = $("#sunday_material_form_name").val();
        var sourceName =$("#sourceName").val();

        if(title == null || title == ""){
            layer.msg('请填写标题', {
                icon: 2
            });
            return ;
        }
        if(sourceName == null || sourceName == ""){
            layer.msg('请选择上传文件', {
                icon: 2
            });
            return;
        }
        $.ajax({
            type: "post",
            url: "/sunday/web/upload/asc/getToken",
            data: {"title": title,"fileName":sourceName},
            async: false,
            success: function (data) {
                //关闭加载
                layer.closeAll('loading');
                if (data.code == 0) {
                    $("#uploadAuth").val(data.result.UploadAuth);
                    $("#uploadAddress").val(data.result.UploadAddress);
                    $("#videoId").val(data.result.VideoId);
                    uploader.startUpload();
                } else {
                    layer.msg('获取上传凭证失败，请稍后重试', {
                        icon: 2
                    });
                }
            }
        });
    }


    //获取上文件列表
    function getList() {
        console.log("get upload list.");
        var list = uploader.listFiles();
        for (var i=0; i<list.length; i++) {
            console.log("file:" + list[i].file.name + ", status:" + list[i].state + ", endpoint:" + list[i].endpoint + ", bucket:" + list[i].bucket + ", object:" + list[i].object);
        }
    }




















</script>



<script>
    //实例化图片裁剪控件
   // upImg(4/3,'sunday_upload_image_div','sunday_upload_image_input');
    //实例化form
    var validatorMenu = $("#sunday_material_form").validate({
        submitHandler: function (form) {
            var image_val=$("#sunday_upload_image_input").val();
            if(image_val==null || image_val ==""){
                layer.msg("请上传封面", {
                    icon: 2
                });
                return ;
            }
            //加遮罩层
            layer.load(4, {shade: [0.8, '#393D49']})
            $("#sunday_material_form_desc_input").val($("#sunday_material_desc_form_texarea").val());
            //状态和类型
            $(form).ajaxSubmit({
                //你表单提交就要切换页面，ajax加载又是异步的。两边同时进行，
                //当ajax加载在切换页面之前完成，就会执行回调，反之不会执行
                //加同步提交数据
                async: false,
                dataType: "json",
                success: function (data) {
                    //关闭加载
                    layer.closeAll('loading');
                    if (data.code == 0) {
                        newTab('/sunday/web/material/index?type=2&groupId=$!material.groupId',null)
                    } else {
                        layer.msg(data.message, {icon: 2});
                    }
                }
            });
        }
    });
</script>