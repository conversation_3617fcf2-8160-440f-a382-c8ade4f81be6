#parse("/screen/sunday/operate.vm")
<style>
    .com-checkbox-wrapper{
        margin-left: 28px;
    }
    .updateTypeButton{
        padding: 0 9px;
        margin: 0 0 0 16px;
        line-height: 30px;
        font-size: 14px;
        color: #fff;
        background: #27cdd7;
        border-radius: 4px;
        border: none;
        outline: none;
        margin-top: 20px;
        margin-left: 0px;
        margin-bottom: 20px;
    }
</style>
#*<table>
    <tr>
        <td>
            <button type="button" class="search-button sunday_room_batchDelete" onclick="room_deleted_batch(0)">批量删除
            </button>

        </td>
    </tr>
</table>*#
<button class="updateTypeButton"  onclick="room_deleted_batch(0)">批量删除</button>
<table width="100%"  border="0" cellspacing="0" cellpadding="0" id="sunday_theme_div">
    <tr>
        <th width="15%"> <div class="com-checkbox-wrapper"><div class="com-checkbox-item room-select-all-checkbox" >全选</div></div></th>
        <th width="20%">序号</th>
        <th width="20%">教室名称</th>
        <th width="25%">园区</th>
        <th>操作</th>
    </tr>
    #foreach($!room in $!rooms)
        <tr>
            <td><div class="com-checkbox-wrapper"><div class="com-checkbox-item room-select-checkbox" value="$!room.id">&nbsp;</div></div> </td>
            <td>$!velocityCount</td>
            <td>$!room.name</td>
            <td>$!room.guardName</td>
            <td>
                <button type="button" onclick="sunday_room_save($!room.id,'$!room.name','$!room.guardId')" class="sunday_room_edit">编辑</button>
                <button type="button" onclick="sunday_room_delete($!room.id)" class="button-delete sunday_room_delete">删除</button>
            </td>
        </tr>
    #end
</table>
<div class="com-page-wrapper">
    <span class="page-num">共$!total条，$!currentPage/$!totalPage页</span>
    <!--判断是否能够转跳页数-->
        <span class="next pre #if($!prevPage >= $!minPage) on" onclick=sunday_role_search_page($!prevPage,null) #end"></span>

        <span class="next #if($!maxPage >= $!nextPage) on" onclick=sunday_role_search_page($!nextPage,null) #end"></span>
    <span class="text">跳转到：</span>
    <input type="text" value="$!currentPage" min="$!minPage" max="$!maxPage">
    <span class="go" onclick="sunday_role_search_page($(this).prev().val(),null,$!maxPage)">GO</span>
</div>



<script>
    $(".room-select-all-checkbox").click(function () {
        if($(this).hasClass("on")){
            $(this).removeClass("on")
            $(".room-select-checkbox").each(function () {
                $(this).removeClass("on")
            })
        }else {
            $(this).addClass("on")
            $(".room-select-checkbox").each(function () {
                $(this).addClass("on")
            })
        }

    })

    $(".room-select-checkbox").click(function () {
        if($(this).hasClass("on")){
            $(this).removeClass("on")
            $(".room-select-all-checkbox").removeClass("on")
        }else {
            $(this).addClass("on")
        }
    })

    function room_deleted_batch(status) {
        var html=""
        $(".room-select-checkbox").each(function () {
            if($(this).hasClass("on")){
                html+=($(this).attr("value")+",")
            }
        })
        if(html==""){
            layer.msg("请至少选中一个进行操作")
            return
        }

        var msg = "确认要批量删除这些教室吗?";
        layer.confirm(msg, function (index) {
            layer.load(4, {shade: [0.8, '#393D49']})
            //不要异步操作
            $.ajax({
                type: "post",
                url: "/sunday/web/room/batchDeleteByIds",
                data: {"ids": html},
                async: false,
                success: function (data) {
                    //关闭加载
                    layer.closeAll('loading');
                    if (data.code == 0) {
                        sunday_role_search(false);


                    } else {
                        layer.msg(data.message, {
                            icon: 2
                        });
                    }
                }
            });
            //最后手动关闭
            layer.close(index);

        });

    }
</script>