<!--前端权限公用模板-->
#parse("/screen/sunday/operate.vm")



<div class="add-kindergarten">
    <form id="sunday_guard_form" class="form-horizontal" action="/sunday/web/guard/save" method="post"  enctype="multipart/form-data">
        <input type="hidden" name="id" value="$!{result.guard.id}">
        <input type="hidden" name="desc"  id="sunday_guard_form_desc_input"/>
        <input type="hidden" name="type" id="sunday_guard_form_type_input">
        <input type="hidden" name="status" id="sunday_guard_form_status_input">

        <input type="hidden" name="provinceName" id="sunday_guard_form_provinceName_input">
        <input type="hidden" name="cityName" id="sunday_guard_form_cityName_input">
        <input type="hidden" name="districtName" id="sunday_guard_form_districtName_input">


        <div class="kindergarten-introduction">
            <div class="com-title-wrapper">
                <div class="title">园区介绍</div>
            </div>
            <div class="con">
                <div class="item clearfix">
                    <div class="com-titl"><i>*</i>园区名称：</div>
                    <input type="text"  class="com-input-item fl"  name="name"  value="$!{result.guard.name}"  required="required">
                </div>
                <div class="item clearfix">
                    <div class="com-titl"><i>*</i>园区类型：</div>
                    <div class="com-radio-wrapper fl clearfix" id="sunday_guard_form_type_div">
                        <div class="com-radio-item" value="1">早教</div>
                        <div class="com-radio-item" value="2">幼教</div>
                    </div>
                </div>
                <div class="item clearfix">
                    <div class="com-titl"><i>*</i>地区选择：</div>
                    <div class="selectpicker mr20">
                        <select  class="selectpicker" id="sunday_guard_form_provinceId" name="provinceId" required="required"></select>
                    </div>
                    <div class="selectpicker mr20">
                        <select class="selectpicker" id="sunday_guard_form_cityId" name="cityId" required="required"></select>
                    </div>
                    <div class="selectpicker mr20">
                        <select class="selectpicker" id="sunday_guard_form_districtId"  name="districtId" required="required"></select>
                    </div>
                </div>
                <div class="item clearfix">
                    <div class="com-titl"><i>*</i>详细地址：</div>
                    <input type="text"  class="com-input-item fl w500" name="address"  value="$!{result.guard.address}" required="required">
                </div>
                <div class="item clearfix">
                    <div class="com-titl"><i>*</i>园区图：</div>
                    <div class="com-add-img xzBtn"
                         style="width:690px;height:300px;background-image: url(#if($result.guard.image)$!result.guard.image#else$!{adminRoot}/yuhua/admin/img/add.png#end);"
                         id="sunday_upload_image_div" onclick="upImg2(7,'sunday_upload_image_div','sunday_upload_image_input',690,300)">
                        <input type="hidden" id="sunday_upload_image_input" name="image" value="$!result.guard.image" >
                    </div>
                    <div class="com-titl mw80">（690px*300px）</div>
                    <script>
                        //  upImg(3,'sunday_upload_image_div','sunday_upload_image_input',250,150);
                        //重写图片选择器
                        function upImg2(bili,sunday_upload_image_div,sunday_upload_image_input,width,height,callType) {
                            //1,加载素材库
                            layer.load(4, {shade: [0.8, '#393D49']})
                            //不要异步操作
                            $.ajax({
                                type: "post",
                                url: "/sunday/web/material/upload/index",
                                data: {"type": 3,"bili":bili,"imageDiv":sunday_upload_image_div,"imageInput":sunday_upload_image_input,"width":width,"height":height,"callType":callType},
                                async: false,
                                success: function (data) {
                                    //关闭加载
                                    layer.closeAll('loading');
                                    $(".right-wrapper").after(data);
                                }
                            });

                        }
                    </script>
                </div>
                <div class="item clearfix">
                    <div class="com-titl"><i>*</i>联系方式：</div>
                    <input type="text"  class="com-input-item fl" name="mobile"  value="$!{result.guard.mobile}"  value" required="required">
                </div>
                <div class="item clearfix">
                    <div class="com-titl">园区简介：</div>
                     <div  id="sunday_guard_desc_form_texarea"  style="width: 82.5%;float: left" maxlength="300" placeholder = "最多300个字符">
                        $!result.guard.desc
                     </div>
                </div>

                <div class="item clearfix">
                    <div class="com-titl"><i>*</i>操作状态：</div>
                    <div class="com-radio-wrapper fl clearfix" id="sunday_guard_form_status_div">
                        <div class="com-radio-item" value="1">运营中</div>
                        <div class="com-radio-item" value="0">未上线</div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 园区介绍 -->
        <div class="class-introduction">
            <div class="com-title-wrapper">
                <div class="title">班级介绍</div>
            </div>
            <div class="con">
                #foreach($bunch in $!result.bunches)
                <div class="class-item clearfix">
                    <img src="$!{adminRoot}/yuhua/admin/img/class.png" alt="" class="img">
                    <div class="input-wrapper clearfix">
                        <div class="item clearfix">
                            <div class="com-titl">班级名称：</div>
                            <input type="text" disabled class="com-input-item fl" value="$!bunch.name">
                        </div>
                        <div class="item clearfix">
                            <div class="com-titl">班级类型：</div>
                            <input type="text" disabled class="com-input-item fl" value="$!bunch.category">
                        </div>
                        <div class="item clearfix">
                            <div class="com-titl">总人数：</div>
                            <input type="text" disabled class="com-input-item fl" value="$!bunch.total">
                        </div>
                        <div class="item clearfix">
                            <div class="com-titl">男女比例：</div>
                            <input type="text" disabled class="com-input-item fl" value="$!bunch.male / $!bunch.female ">
                        </div>
                    </div>
                </div>
                 #end
                <!-- class-item -->
            </div>
        </div>
        <!-- 班级介绍 -->
        <div class="teacher-introduction">
            <div class="com-title-wrapper">
                <div class="title">师资介绍</div>
            </div>
            <div class="con">
                <div class="list clearfix">
                    #foreach($!teacher in $!result.teachers)
                    <div class="item">
                        <img src="$!teacher.image" alt="" height="140" class="avatar">
                        <div class="name">$!teacher.name</div>
                    </div>
                    #end
                </div>

                <div class="button-wrapper clearfix">
                    <a href="javascript:yuhua_return('/sunday/web/guard/v2/index?isClean=0');" class="com-button return">返回</a>
                    <button type="submit" class="com-button sunday_guard_save">保存</button>
                </div>
            </div>
        </div>
        
    </form>
</div>

<script>


    //实例化富文本
    var E = window.wangEditor
    var descEditor = new E('#sunday_guard_desc_form_texarea');
    // 或者 var editor = new E( document.getElementById('editor') )
    descEditor.customConfig.showLinkImg = false;
    // 图片上传路径
    descEditor.customConfig.uploadImgServer = '/sunday/web/upload/wangEditor/upload';
   /* descEditor.customConfig.onchange = function (html) {
        // 监控变化，同步更新到 textarea
        var oldDesc = descEditor.txt.html();

        if(oldDesc.length > 300){
            oldDesc = oldDesc.substring(0,300);
        }
        descEditor.txt.html(oldDesc);
    }*/
    function GetChinese(strValue) {
        if(strValue!= null && strValue!= ""){
            var reg = /[\u4e00-\u9fa5]/g;
            return strValue.match(reg).join("");
        }
        else
            return "";
    }

    descEditor.create();

    //实例化省市区
    intAreaNoTown("sunday_guard_form_provinceId","sunday_guard_form_cityId","sunday_guard_form_districtId","$!{result.guard.provinceId}","$!{result.guard.cityId}","$!{result.guard.districtId}");



    //实例化字典
    initGuard("sunday_guard_form_guardId","$!result.guard.guardId")

    //实例化图片裁剪控件

    //转跳类型判断
    $("#sunday_guard_form_type_div,#sunday_guard_form_status_div").children().click(function(){
        $(this).addClass("on");
        $(this).siblings().removeClass("on")
    })
    //默认点击第一个
    var guardId = "$!result.guard.id";
    //新增
    if(guardId==null || guardId == 0){
        $("#sunday_guard_form_type_div").children(":eq(0)").click();
        $("#sunday_guard_form_status_div").children(":eq(0)").click();
    }else{
        //编辑
        var type  = "$!result.guard.type";
        var status ="$!result.guard.status";
        $("#sunday_guard_form_type_div").children().each(function () {
            if(type == $(this).attr("value")){
                $(this).click();
            }
            $(this).unbind("click");
        });
        $("#sunday_guard_form_status_div").children().each(function () {
            if(status == $(this).attr("value")){
                $(this).click();
            }
        });
    }
    jQuery.validator.addMethod("isMobile", function(value, element) {
        var length = value.length;
        var mobile = /^1[123456789]\d{9}$/;/*/^1(3|4|5|7|8)\d{9}$/*/
        return this.optional(element) || (length == 11 && mobile.test(value));
    }, "请正确填写您的手机号码");

    //实例化form
    var Required = "*必填!";
    var validatorMenu = $("#sunday_guard_form").validate({
       /* rules: {
            mobile: {
                required: true,
                minlength: 11,
                maxlength:11,
                digits:true,
                number:true,
                isMobile : true
            }
        },
        messages: {
            // mobile: "请输入正确的联系方式",
            mobile:{
                required: "不能为空",
                minlength: "必须11位数字",
                maxlength:"必须11位数字",
                digits:"必须是数字"    ,
                number:"请输入有效数字",
                isMobile : "联系方式格式错误"
            },
        },*/
        submitHandler: function (form) {

            var image_val=$("#sunday_upload_image_input").val();
            if(image_val==null || image_val ==""){
                layer.msg("请上传园区图", {
                    icon: 2
                });
                return ;
            }

            //加遮罩层
            layer.load(4, {shade: [0.8, '#393D49']})
            //2018年11月7日配合wangeEditor
            $("#sunday_guard_form_desc_input").val(descEditor.txt.html());

          //  return ;
            //状态和类型
            $("#sunday_guard_form_status_input").val($("#sunday_guard_form_status_div").children(".on").attr("value"));
            $("#sunday_guard_form_type_input").val($("#sunday_guard_form_type_div").children(".on").attr("value"));
            //省市区
            $("#sunday_guard_form_provinceName_input").val($("#sunday_guard_form_provinceId option:selected").text())
            $("#sunday_guard_form_cityName_input").val($("#sunday_guard_form_cityId option:selected").text())
            $("#sunday_guard_form_districtName_input").val($("#sunday_guard_form_districtId option:selected").text())




            $(form).ajaxSubmit({
                //你表单提交就要切换页面，ajax加载又是异步的。两边同时进行，
                //当ajax加载在切换页面之前完成，就会执行回调，反之不会执行
                //加同步提交数据
                async: false,
                dataType: "json",
                success: function (data) {
                    //关闭加载
                    layer.closeAll('loading');
                    if (data.code == 0) {
                        //  newTab("");
                        newTab('/sunday/web/guard/v2/index?isClean=0',null)
                    } else {
                        layer.msg(data.message, {icon: 2});
                    }
                }
            });
        }
    });
</script>