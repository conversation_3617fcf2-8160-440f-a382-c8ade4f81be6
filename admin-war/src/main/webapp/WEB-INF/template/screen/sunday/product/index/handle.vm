#set( $layout = "/layout/easyui/h-layout.vm")
<body>
<div class="wrapper wrapper-content animated fadeInRight">
    <div class="row">
        <div class="col-sm-12" id="tableDiv">
            <div class="ibox float-e-margins">
                <div class="ibox-content">
                    <div class="col-sm-12 no-padding">


                    </div>

                    <form role="form" class="form-inline" id="sunday_product_handle_search_form">

                        <input type="hidden"  name="type"  value="1" >
                        <input type="hidden"  name="memberId"  value="$!{memberId}" >
                        <div class="form-group form-form-group" >
                            <label>套餐名称：</label>
                            <input type="text" placeholder="请输入名称" name="name" class="form-control input-sm">
                        </div>
                        <div class="form-group form-form-group" >
                            <label>套餐状态：</label>
                            <select name="status" class="form-control input-sm">
                                <option value="2">待审核</option>
                                <option value="3">已驳回</option>
                            </select>
                        </div>

                        <button class="btn btn-success btn-sm" type="button" onclick="sunday_product_handle_search()"><i class="fa fa-search"></i>&nbsp;搜索</button>


                    </form>
                    <!--套餐table-->
                    <table id="sunday_product_handle_table" class="table table-striped"></table>
                </div>
            </div>
        </div>
    </div>
</div>


<!-- <div id="sunday_product_handle_view_div" class="modal fade" role="dialog" style="display: none;" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
        </div>
    </div>
</div> -->
</body>
<script>
    $("#sunday_product_handle_table").bootstrapTable({
        columns: [

            {field : 'name',title : '套餐名称',width:200,align:"center"},
            /*{field : 'number',title : '套餐编码',width:150,align:"center"},*/

            {field : 'firstCatName',title : '一级类目',width:100,align:"center"},
            {field : 'secCatName',title : '二级类目',width:100,align:"center"},
            {field : 'marketPrice',title : '市场价',width:100,align:"center"},
            {field : 'price',title : '销售价',width:100,align:"center"},
            {field : 'image',title : '商品图片',width:250,align:"center",
                formatter : function(value, row, index) {
                     //var images=row.images;  
                     var url="$!{imgRoot}"+value;         
                    if(value!=null&&value!=""){
                       return "<a href='"+url+"' target='_blank' ><img style='width:100px;height:100px'src='"+url+"'/></a>";
                   
                    }
                    return "";
                }
            },
            {field : 'statusStr',title : '状态',width:250,align:"center",
                formatter : function(value, row, index) {
                     
                    return " <h5><span class='label label-primary'>"+value+"</span></h5>";
                }
            },
            
           
            {field : 'operate',title : '操作',width:250,align:'center',
                formatter : function(value, row, index) {
                     var operate=""
                    var handle="&nbsp;&nbsp;&nbsp<button class='btn btn-xs btn-warning' type='button' onclick='sunday_product_handle_handle("+row.id+")'>审核</button>";
                    if(row.status==2){
                        operate+=handle;
                    }
                   
                    return operate;
                }
            }],
        url:'/sunday/web/product/handle/select',
        cache:false,
        pagination: true,

        pageSize: 50,
        pageList: [50, 100,150],
        pageNumber:1,
        sidePagination: 'server',
        queryParams: function (params) {
            $('#sunday_product_handle_search_form').find('input[name]').each(function () {
                // //params
                var name=$(this).attr('name');
                var value=$(this).val();
                if(value==null||value==""){
                    value=null;
                }
                params[name]=value;

            });
            //select参数
            $('#sunday_product_handle_search_form').find('select[name]').each(function () {
                // //params
                var name=$(this).attr('name');
                var value=$(this).val();
                if(value==null||value==""){
                    value=null;
                }
                params[name]=value;

            });
            //实例化组件-自带参数
            params['offset']=params.offset;
            params['limit']=params.limit;
            params['sort']=params.sort;
            params['order']=params.order;

            return params;
        },
        onClickRow:function (row, $element, field){
                $element.addClass("row-select");
                $element.siblings('tr').removeClass("row-select");
        }
    });

    function sunday_product_handle_search() {
        $("#sunday_product_handle_table").bootstrapTable("refresh");
    }

    //新增
    function sunday_product_handle_handle(id){
        var isAgree=false;
        layer.confirm('您是如何看待前端开发？', {
              btn: ['同意','拒绝','取消'] //按钮
            }, function(){
                layer.load(4, {shade: [0.8, '#393D49']})
                $.ajax({
                        type : "post",
                        url : "/sunday/web/product/handle",
                        data : {"id":id,"isAgree":1},
                        async : false,
                        success : function(data){
                            //关闭加载
                            layer.closeAll();
                            if(data.code == 0){
                                sunday_product_handle_search()

                            }else{
                                layer.msg('删除失败', {
                                    icon: 2
                                });
                            }


                        }
                });
             
            }, function(){
              layer.load(4, {shade: [0.8, '#393D49']})
                $.ajax({
                        type : "post",
                        url : "/sunday/web/product/handle",
                        data : {"id":id,"isAgree":0},
                        async : false,
                        success : function(data){
                            //关闭加载
                            layer.closeAll();
                            if(data.code == 0){
                                sunday_product_handle_search()

                            }else{
                                layer.msg('删除失败', {
                                    icon: 2
                                });
                            }


                        }
                });
            }, function(){
                layer.closeAll();
                //return;
            });
       // parent.newTab('/sunday/web/product/input?memberId=$!{memberId}&type=1&id='+id,'套餐管理');
        //remoteModal("sunday_product_handle_div","/sunday/web/product/input?id=0")
    }


    function sunday_product_handle_delete(productId) {
        var msg="确认要删除这条套餐吗?";
        layer.confirm(msg, function(index){
            layer.load(4, {shade: [0.8, '#393D49']})
            //不要异步操作
            $.ajax({
                type : "post",
                url : "/sunday/web/product/delete",
                data : {"id":productId},
                async : false,
                success : function(data){
                    //关闭加载
                    layer.closeAll('loading');
                    if(data.code == 0){
                        sunday_product_handle_search()

                    }else{
                        layer.msg('删除失败', {
                            icon: 2
                        });
                    }


                }
            });
            //最后手动关闭
            layer.close(index);

        });
    }
    //上下架
    function sunday_product_handle_change(id,status){
        layer.load(4, {shade: [0.8, '#393D49']})
            //不要异步操作
            $.ajax({
                type : "post",
                url : "/sunday/web/product/change",
                data : {"id":id,"status":status},
                async : false,
                success : function(data){
                    //关闭加载
                    layer.closeAll('loading');
                    if(data.code == 0){
                        sunday_product_handle_search()

                    }else{
                        layer.msg('上/下架套餐失败', {
                            icon: 2
                        });
                    }


                }
            });
    }
    
</script>

