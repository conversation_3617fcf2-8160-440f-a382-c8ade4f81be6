#parse("/screen/sunday/operate.vm")
<table width="100%"  border="0" cellspacing="0" cellpadding="0" id="sunday_theme_div">
    <tr>
        <th width="10%">序号</th>
        <th width="10%">ICON</th>
        <th width="20%">课程名称</th>
        <th width="20%">适用月龄</th>
        <th width="20%">开课园区</th>
        <th>操作</th>
    </tr>
    #foreach($!lesson in $!lessons)
        <tr>
            <td>$!velocityCount</td>
            <td>
                #if(!$!lesson.categoryIcon)
                    无
                #else
                    <img src="$!lesson.categoryIcon?x-oss-process=image/resize,p_50" style="width: 30px;height: 30px;">
                #end
            </td>
            <td>$!lesson.lessonName</td>
            <td>$!lesson.categoryName</td>
            <td>#if($!lesson.guardType == 0)全部园区#else$!lesson.guardNames #end
            </td>
            <td>
                <button type="button" onclick="sunday_lesson_save($!lesson.id,'$!lesson.lessonName','$!lesson.guardType','$!lesson.guardIds','$!lesson.categoryName','$!lesson.categoryIcon')" class="sunday_lesson_edit">编辑</button>
                <button type="button" onclick="sunday_lesson_delete($!lesson.id)" class="button-delete sunday_lesson_delete">删除</button>
            </td>
        </tr>
    #end
</table>
<div class="com-page-wrapper">
    <span class="page-num">共$!total条，$!currentPage/$!totalPage页</span>
    <!--判断是否能够转跳页数-->
        <span class="next pre #if($!prevPage >= $!minPage) on" onclick=sunday_role_search_page($!prevPage,null) #end"></span>

        <span class="next #if($!maxPage >= $!nextPage) on" onclick=sunday_role_search_page($!nextPage,null) #end"></span>
    <span class="text">跳转到：</span>
    <input type="text" value="$!currentPage" min="$!minPage" max="$!maxPage">
    <span class="go" onclick="sunday_role_search_page($(this).prev().val(),null,$!maxPage)">GO</span>
</div>