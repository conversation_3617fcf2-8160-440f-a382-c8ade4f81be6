#parse("/screen/sunday/operate.vm")
<style>
    .search-table td{min-width:80px !important;}
</style>
<div class="text-img imgs">
    <div class="top-wrapper clearfix">
        <script>
            $.post("/sunday/web/attendance/attendTop",
                    {"type":1},
                    function (data) {
                        $(".top-wrapper").append(data);
                    })
        </script>
    </div>
    <div class="search-wrapper clearfix" style="margin-top:20px;width:100%">
        <div style="background-color:#fff;padding-top:8px;">
            <font style="font-size:18px;margin-left:38px;">$!{now}&nbsp;$!{day}</font></div>
        <table class="search-table" style="width:100%;">
            <tr>
                <td><div class="search-titl">园区：</div></td>
                <td>
                    <div class="selectpicker">
                        <select id="sunday_student_form_qinzi_guardId" name="guardId" required="required" ></select>
                    </div>
                </td>
                <td><div class="search-titl">班级：</div></td>
                <td>
                    <div class="selectpicker">
                        <select id="sunday_student_form_qinzi_bunchId" name="bunchId" required="required"></select>
                    </div>
                </td>
                <td><div class="search-titl">课程：</div></td>
                <td>
                    <div class="selectpicker">
                        <select id="sunday_student_form_qinzi_lessonId" name="lessonId"required="required"></select>
                    </div>
                </td>
                <td>
                    <button type="button" class="search-button" onclick="clearSearch()">清空</button>
                    <button type="button" class="search-button sunday_attend_today_search" onclick="sunday_role_search(true)">搜索</button>
                </td>
            </tr>
            <script>
                $.ajaxSetup({
                    async: false
                });
                var dictionary_url ="$!dictionaryRoot";
                var guardId = "$!param.guardId";
                var bunchId = "$!param.bunchId";
                var lessonId = "$!param.lessonId";
                function initGuardAndqinZiBunch(H, A, R, B, E, Q) {
                    var I = "/sunday/web/guard/selectNoPage";
                    var G = "/sunday/web/bunch/selectQinziBunchNoPage";
                    var W = "/sunday/web/lesson/selectNoPage";
                    $("#" + H).unbind("change");
                    $("#" + H).children().remove();
                    $("#" + A).children().remove();
                    $("#" + R).children().remove();
                    $("#" + H).append("<option value=''>请选择园区</option>");
                    $.post(I, {
                            },
                            function(L) {
                                var J = L.result;
                                for (var K = 0; K < J.length; K++) {
                                    if (typeof(B) != "undefined" && B != null && B != "" && B == J[K].id) {
                                        $("#" + H).append("<option selected value='" + J[K].id + "'>" + J[K].name + "</option>")
                                    } else {
                                        $("#" + H).append("<option  value='" + J[K].id + "'>" + J[K].name + "</option>")
                                    }
                                }
                                $("#" + H).change(function() {
                                    $("#" + A).children().remove();
                                    $("#" + A).append("<option value=''>请选择班级</option>");
                                    if ($(this).val() != "") {
                                        $.post(G, {
                                                    "guardId": $(this).val()
                                                },
                                                function(N) {
                                                    var O = N.result;
                                                    for (var M = 0; M < O.length; M++) {
                                                        if (typeof(E) != "undefined" && E != null && E != "" && E == O[M].id) {
                                                            $("#" + A).append("<option selected value='" + O[M].id + "' valueType='" + O[M].type + "' valueCategory='" + O[M].category + "'>" + O[M].name + "</option>")
                                                        } else {
                                                            $("#" + A).append("<option value='" + O[M].id + "' valueType='" + O[M].type + "' valueCategory='" + O[M].category + "' >" + O[M].name + "</option>")
                                                        }
                                                    }
                                                })
                                    }
                                    $("#" + R).children().remove();
                                    $("#" + R).append("<option value=''>请选择课程</option>");
                                    if ($(this).val() != "") {
                                        $.post(W, {
                                                    "guardId":$(this).val()
                                                },
                                                function(N) {
                                                    var O = N.result;
                                                    for (var M = 0; M < O.length; M++) {
                                                        if (typeof(Q) != "undefined" && Q != null && Q != "" && Q == O[M].id) {
                                                            $("#" + R).append("<option selected value='" + O[M].id +"'>" + O[M].lessonName + "</option>")
                                                        } else {
                                                            $("#" + R).append("<option value='" + O[M].id + "' >" + O[M].lessonName + "</option>")
                                                        }
                                                    }
                                                })
                                    }
                                });
                                $("#" + H).change()
                            })
                }
                initGuardAndqinZiBunch("sunday_student_form_qinzi_guardId","sunday_student_form_qinzi_bunchId","sunday_student_form_qinzi_lessonId", guardId,bunchId,lessonId)
            </script>
        </table>
    </div>
    <div class="list" id="sunday_today_div" style="padding-top: 20px"></div>
</div>
<script>
    $.ajaxSetup({
        async: false
    });
    var param_pageNumber = "$!param.pageNumber";
    var param_pageSize = "$!param.pageSize";
    if(param_pageNumber==null|| param_pageNumber==""){
        param_pageNumber=1
    }
    if(param_pageSize==null|| param_pageSize==""){
        param_pageSize=10
    }
    var sunday_role_pageNumber=param_pageNumber;
    var sunday_role_pageSize=param_pageSize;
    var sunday_role_sort="id";
    var sunday_role_order="desc";
    var sunday_role_is_loading=false;
    var sunday_role_is_end=false;
    var sunday_role_guardId="";
    var sunday_role_bunchId="";
    var sunday_role_lessonId="";

    function clearSearch() {
        $("#sunday_student_form_qinzi_guardId").val("");
        $("#sunday_student_form_qinzi_bunchId").val("");
        $("#sunday_student_form_qinzi_lessonId").val("");
        sunday_role_pageNumber = 1;
        sunday_role_getData(true);
    }

    //输入订单号查询
    function sunday_role_search(isPageNumber){
        sunday_role_is_loading=false;
        sunday_role_is_end=false;
        if(isPageNumber){
            sunday_role_pageNumber =1;
        }
        // sunday_role_pageSize =1;
        sunday_role_getData(true);
    }
    function sunday_role_search_page(pageNumber,pageSize,maxPageNumber){
        if(maxPageNumber!=null&&maxPageNumber!=""&&typeof(maxPageNumber)!="undefined"){
            if(parseInt(pageNumber)>parseInt(maxPageNumber)){
                return;
            }
        }
        sunday_role_pageNumber =pageNumber;
        sunday_role_pageSize =pageSize==null?sunday_role_pageSize:pageSize;
        sunday_role_getData(true);
    }
    function sunday_role_getData(isRemove){
        var t="#sunday_today_div";

        if(sunday_role_is_loading||sunday_role_is_end)return;
        if(isRemove){
            $(t).children().remove();
        }
        sunday_role_is_loading=true;
        sunday_role_guardId = $("#sunday_student_form_qinzi_guardId").val();
        sunday_role_bunchId = $("#sunday_student_form_qinzi_bunchId").val();
        sunday_role_lessonId = $("#sunday_student_form_qinzi_lessonId").val();
        //同步请求
        $.ajax({
            type: "POST",
            url: "/sunday/web/attendance/selectAttendanceByToday",
            data: {
                "pageSize":sunday_role_pageSize,
                "pageNumber":sunday_role_pageNumber,
                "sort":sunday_role_sort,
                "order":sunday_role_order,
                "guardId":sunday_role_guardId,
                "bunchId":sunday_role_bunchId,
                "lessonId":sunday_role_lessonId,
            },
            dataType: "text",
            async:true,
            success: function(result){
                if(result==null||result==""||result.length<1){
                    sunday_role_is_end=true;
                }else{
                    if(isRemove && $(t).children()!=null){
                        $(t).children().remove();
                    }
                    $(t).append(result);
                    sunday_role_is_loading=false;
                }
            }
        });
    }
    sunday_role_search(false);

    function sunday_room_save(id,name,guardId){
        $("#sunday_updatebook_form").form("clear");
        if(id != '' && id != 0  && id !=null){
            $("#sunday_updatebook_form").form("load", {"id":id,"name":name,'guardId':guardId});
        }else{
            $("#sunday_updatebook_form").form("load", {"id": 0});
        }
        remoteModal("sunday_updatebook_div", "")
    }

    var validatorMenu = $("#sunday_updatebook_form").validate({
        submitHandler: function (form) {
            //加遮罩层
            layer.load(4, {shade: [0.8, '#393D49']})
            $(form).ajaxSubmit({
                //你表单提交就要切换页面，ajax加载又是异步的。两边同时进行，
                //当ajax加载在切换页面之前完成，就会执行回调，反之不会执行
                //加同步提交数据
                async: false,
                dataType: "json",
                success: function (data) {
                    //关闭加载
                    layer.closeAll('loading');
                    if (data.code == 0) {
                        sunday_role_getData(true);
                        closeModal("sunday_updatebook_div");
                    } else {
                        layer.msg(data.message, {
                            icon: 6
                        });
                    }
                }
            });
        }
    });

   function sunday_edit_history(historyId,editStatus) {
       newTab("/sunday/web/attendance/editAttendanceById?editStatus="+editStatus+"&editType=1&historyId=" + historyId ,editStatus=='1'?"查看考勤":"编辑考勤");
   }

    function batchAttend() {
        var arr = [];

        $(".yes-check").each(function () {
            if($(this).hasClass("on")){
                var historyId = $(this).attr("value");
                arr.push(parseInt(historyId));
            }
        })
        if(arr.length<=0){
            layer.msg("请勾选考勤", {
                icon: 2
            });
            return;
        }
        var historyIds = arr.join(",");
        var msg = "确认批量全勤?";
        layer.confirm(msg, function (index) {
            layer.load(4, {shade: [0.8, '#393D49']})
            //不要异步操作
            $.ajax({
                type: "post",
                url: "/sunday/web/attendance/updateAllAttendByScheduleId",
                data: {"historyIds": historyIds},
                async: false,
                success: function (data) {
                    //关闭加载
                    layer.closeAll('loading');
                    if (data.code == 0) {
                        sunday_role_search(false);
                    } else {
                        layer.msg(data.message, {
                            icon: 2
                        });
                    }
                }
            });
            //最后手动关闭
            layer.close(index);

        });
    }
</script>