#parse("/screen/sunday/operate.vm")
<style>
    .data-report .search-wrapper{
        padding-bottom:10px !important;
    }
    .data-report .search-wrapper{
        margin-top:10px;
    }
    .laydate-theme-xxx .layui-this{
        background-color: #477cff
    };
    .laydate-theme-xxx .laydate-selected{
        background-color: #d9e3ff
    }
    .data-report .charts-wrapper .charts-button-item{
        padding:0 14px;
    }
</style>
<script>
    $.ajaxSetup({
        async: true
    });
</script>
<div class="data-report" onscroll="scroll()">
    <div class="top-wrapper clearfix">
        <script>
            $.post("/sunday/web/data/top",
                    {"type":3},
                    function (data) {
                        $(".top-wrapper").html("");
                        $(".top-wrapper").append(data);
                    })
        </script>
    </div>
    <div class="search-wrapper clearfix" id="sunday_data0_count_div">
        <div class="com-title-wrapper">
            <div class="title">数据概况</div>
        </div>
    </div>
    <script>
        function sunday_data0_search() {
            $("#sunday_data0_count_result_div").remove();
            $.ajax({
                type: "POST",
                url: "/sunday/web/data/qin_count0",
                data: {},
                dataType: "text",
                async: true,
                success: function (data) {
                    layer.closeAll('loading');
                    $("#sunday_data0_count_result_div").remove();
                    $("#sunday_data0_count_div").append(data);
                }
            });
        }
    </script>
    <div class="search-wrapper clearfix" id="sunday_data1_count_div">
        <div class="com-title-wrapper">
            <div class="title">应用概况</div>
        </div>
    </div>
    <script>
        function sunday_data1_search() {
            $("#sunday_data1_count_result_div").remove();
            $.ajax({
                type: "POST",
                url: "/sunday/web/data/qin_count1",
                data: {},
                dataType: "text",
                async: true,
                success: function (data) {
                    $("#sunday_data1_count_result_div").remove();
                    $("#sunday_data1_count_div").append(data);
                    layer.closeAll('loading');
                }
            });
        }
    </script>
    <div class="search-wrapper clearfix">
        <div class="com-title-wrapper">
            <div class="title">时段分析</div>
        </div>
        <div class="charts-wrapper">
            <div class="charts-button-wrapper clearfix" id="sunday_data2_search_type_div">
                <div class="charts-button-item on" value="1">新用户</div>
                <div class="charts-button-item" value="2">活跃用户</div>
                <div class="charts-button-item" value="3">启动次数</div>
            </div>
            <div class="charts-content">
                <div id="sunday_data2_count_result_chart" style="min-width:400px;height:400px"></div>
            </div>
        </div>
    </div>
    <script>
        $("#sunday_data2_search_type_div").children().click(function () {
            $(this).addClass("on");
            $(this).siblings().removeClass("on");
            sunday_data2_search()
        })

        function sunday_data2_search() {
            var queryType =  $("#sunday_data2_search_type_div").children(".on").attr("value");
            $.ajax({
                type: "POST",
                url: "/sunday/web/data/qin_count2",
                data: {"queryType":queryType},
                dataType: "json",
                async: true,
                success: function (data) {
                    //关闭加载
                    layer.closeAll('loading');
                    var categories = data.result.categories;
                    categories=eval(categories);
                    var series = data.result.series;
                    series=eval(series);
                    var chart = Highcharts.chart('sunday_data2_count_result_chart', {
                        title: {
                            text: ''
                        },
                        xAxis: {
                            categories: categories
                        },
                        yAxis: {
                            title: {
                                text: ''
                            },
                            allowDecimals:false

                        },
                        series: series
                    });
                    $(".highcharts-credits").remove();
                    $(".highcharts-exporting-group").remove();
                }
            });
        }
    </script>
    <!--总体数据100-->
    <div class="search-wrapper clearfix">
        <div class="com-title-wrapper">
            <div class="title">总体数据</div>
        </div>
        <div class="clearfix" id="sunday_data100_count_div">
            <div class="item clearfix fl">
                <div class="com-titl">开始时间：</div>
                <div class="com-time-item fl">
                    <input type="text"  value="$!yesterday_start" id="sunday_data100_search_ct_start" class="search_date" readonly>
                </div>
            </div>
            <div class="item clearfix fl">
                <div class="com-titl">结束时间：</div>
                <div class="com-time-item fl">
                    <input type="text"  value="$!yesterday_end"  id="sunday_data100_search_ct_end" class="search_date" readonly>
                </div>
            </div>

            <button type="button" class="com-button item fl" onclick="sunday_data100_search()">搜索</button>
            <a href="javascript:sunday_data100_daochu();" class="com-button return item fl sunday_data_daochu" >导出</a>
        </div>
    </div>
    <script>
        /*laydate.render({
            elem: '#sunday_data100_search_ct_start' //指定元素
            ,format: 'yyyy-MM-dd'
            ,theme: 'xxx'
        });
        laydate.render({
            elem: '#sunday_data100_search_ct_end' //指定元素
            ,format: 'yyyy-MM-dd'
            ,theme: 'xxx'
        });*/
        function sunday_data100_search() {
            var ct_start = $("#sunday_data100_search_ct_start").val();
            var ct_end = $("#sunday_data100_search_ct_end").val();
            if(ct_start==null || ct_start == "" || ct_end == null ||  ct_end == "" ){
                layer.msg("请选择开始和结束日期", {
                    icon: 2
                });
                return ;
            }
            $("#sunday_data100_count_result_div").remove();
            $.ajax({
                type: "POST",
                url: "/sunday/web/data/qin_count100",
                data:   {"ct_start":ct_start,"ct_end":ct_end},
                dataType: "text",
                async: true,
                success: function (data) {
                    //关闭加载
                    layer.closeAll('loading');
                    $("#sunday_data100_count_result_div").remove();
                    $("#sunday_data100_count_div").after(data);
                }
            });
        }
        function  sunday_data100_daochu() {
            var ct_start = $("#sunday_data100_search_ct_start").val();
            var ct_end = $("#sunday_data100_search_ct_end").val();
            if(ct_start==null || ct_start == "" || ct_end == null ||  ct_end == "" ){
                layer.msg("请选择开始和结束日期", {
                    icon: 2
                });
                return ;
            }
            $.ajax({
                type: "POST",
                url: "/sunday/web/data/qin_daochu100",
                data:   {"ct_start":ct_start,"ct_end":ct_end},
                dataType: "json",
                async: true,
                success: function (data) {
                    layer.closeAll('loading');
                    if (data.code == 0) {
                        window.open(data.result);
                    } else {
                        layer.msg('导出失败', {
                            icon: 2
                        });
                    }
                }
            });
        }
    </script>
    <!--园区总体数据101-->
    <div class="search-wrapper clearfix">
        <div class="com-title-wrapper">
            <div class="title">园区总体数据</div>
        </div>
        <div class="clearfix" id="sunday_data101_count_div">
            <div class="item clearfix fl">
                <div class="com-titl">开始时间：</div>
                <div class="com-time-item fl">
                    <input type="text"  value="$!yesterday_start" readonly id="sunday_data101_search_ct_start" class="sunday_qin_data101_search_ct_start search_date">
                </div>
            </div>
            <div class="item clearfix fl">
                <div class="com-titl">结束时间：</div>
                <div class="com-time-item fl">
                    <input type="text"  value="$!yesterday_end"  readonly id="sunday_data101_search_ct_end" class="sunday_qin_data101_search_ct_end search_date">
                </div>
            </div>
            <button type="button" class="com-button item fl" onclick="sunday_data101_search()">搜索</button>
            <a href="javascript:sunday_data101_daochu();" class="com-button return item fl sunday_data_daochu" >导出</a>
        </div>
    </div>
    <script>
        /*laydate.render({
            elem: '.sunday_qin_data101_search_ct_start' //指定元素
            ,format: 'yyyy-MM-dd'
            ,theme: 'xxx'
        });
        laydate.render({
            elem: '#sunday_qin_data101_search_ct_end' //指定元素
            ,format: 'yyyy-MM-dd'
            ,theme: 'xxx'
        });*/
        function sunday_data101_search() {
            var ct_start = $("#sunday_data101_search_ct_start").val();
            var ct_end = $("#sunday_data101_search_ct_end").val();
            if(ct_start==null || ct_start == "" || ct_end == null ||  ct_end == "" ){
                layer.msg("请选择开始和结束日期", {
                    icon: 2
                });
                return ;
            }
            $("#sunday_data101_count_result_div").remove();
            $.ajax({
                type: "POST",
                url: "/sunday/web/data/qin_count101",
                data:   {"ct_start":ct_start,"ct_end":ct_end},
                dataType: "text",
                async: true,
                success: function (data) {
                    //关闭加载
                    layer.closeAll('loading');
                    $("#sunday_data101_count_result_div").remove();
                    $("#sunday_data101_count_div").after(data);
                }
            });
        }
        function  sunday_data101_daochu() {
            var ct_start = $("#sunday_data101_search_ct_start").val();
            var ct_end = $("#sunday_data101_search_ct_end").val();
            if(ct_start==null || ct_start == "" || ct_end == null ||  ct_end == "" ){
                layer.msg("请选择开始和结束日期", {
                    icon: 2
                });
                return ;
            }
            $.ajax({
                type: "POST",
                url: "/sunday/web/data/qin_daochu101",
                data:   {"ct_start":ct_start,"ct_end":ct_end},
                dataType: "json",
                async: true,
                success: function (data) {
                    //关闭加载
                    layer.closeAll('loading');
                    if (data.code == 0) {
                        window.open(data.result);
                    } else {
                        layer.msg('导出失败', {
                            icon: 2
                        });
                    }
                }
            });
        }
    </script>

</div>
<script>
   sunday_data0_search();
   sunday_data1_search();
    $("#sunday_data2_search_type_div").children(":first").click();
    sunday_data100_search();
    sunday_data101_search();
    $("#sunday_data102_search_type_div").children(":first").click();

   lay('.search_date').each(function(){
       laydate.render({
           elem: this
           ,trigger: 'click'
           ,format: 'yyyy-MM-dd'
           ,theme: 'xxx'
       });
   });
</script>