<!--前端权限公用模板-->
#parse("/screen/sunday/operate.vm")
<style>
    .space20{
        padding: 20px;
    }
    .add-special{
        background: #fff;
    }
    .form-modal-body-k .item {
        margin-top: 20px;
    }
    .form-modal-body-k .com-titl{
        width: 120px;
    }
    .form-modal-body-k textarea{
        width: 240px;
    }
    .search-selectpicker {
        float: left;
        margin-left:0;
        width: 240px;
    }
    .isapp{
        line-height: 30px;
        margin-left: 20px;
        color: #5298ff;
    }
    .item label{
        font-weight: normal;
        margin-bottom: 0;
        display: block;float: left;
        line-height: 30px;
        cursor: pointer;
    }
    .item label+label{
        margin-left: 15px;
    }
    .item label input{
        display: block;float: left;
        margin-top: 8px;
        line-height: 30px;
        margin-right: 5px ;
    }
    .item label var{
        font-style: normal;
        line-height: 30px;
        display: block;float: left;;
    }
    .add-special .button-wrapper {
        min-width: 900px;
        width: 70%;
        text-align: center;
        font-size: 0;
        padding: 20px 0;
    }.add-special  .button-wrapper .com-button {
         display: inline-block;
         vertical-align: top;
         margin: 0 10px;
     }
    .error-tip{
        color:red;
        margin-left:4px;
    }
</style>

<div class="add-special space20">
    #if($!editType == 1)
        <div class="head">编辑检查结果</div>
    #else
        <div class="head">新增检查结果</div>
    #end
    <form id="sunday_material_form" class="form-horizontal form-modal-body-k" action="/sunday/web/studentBody/add" method="post"  enctype="multipart/form-data">
        <div class="item clearfix">
            <div class="com-titl"><i>*</i>选择园区：</div>
            <div class="search-selectpicker">

                <select #if($!editType == 1) disabled #end
                                             id="sunday_student_body_search_guard">
                    #if ($!editType == 1)<option value="$!bodyDetail.guardId" selected>$!bodyDetail.guardName</option>
                    #else <option value="">请选择园区</option>
                    #end
                </select>

            </div>

            <label class="error-tip" id="guardTip" style="float: left;"></label>
        </div>
        <div class="item clearfix">
            <div class="com-titl"><i>*</i>选择班级：</div>
            <div class="search-selectpicker">
                <select
                    #if($!editType == 1) disabled #end
                                         id="sunday_student_body_search_bunch">
                    #if ($!editType == 1)<option value="$!bodyDetail.bunchId" selected>$!bodyDetail.bunchName</option>
                    #else <option value="">请选择班级</option>
                    #end
                </select>

            </div>

            <label class="error-tip" id="bunchTip" style="float: left;"></label>
        </div>
        <div class="item clearfix">
            <div class="com-titl"><i>*</i>选择学生：</div>
            <div class="search-selectpicker">
                <select
                    #if($!editType == 1) disabled #end
                                         id="sunday_student_body_search_stu">
                    #if ($!editType == 1)<option value="$!bodyDetail.studentId" selected>$!bodyDetail.studentName</option>
                    #else <option value="">请选择学生</option>
                    #end
                </select>
            </div>

            <label class="error-tip" id="stuTip" style="float: left;"></label>
        </div>
        <script>
            var tempGuardId = 0;
            var tempBunchId = 0;
            $.ajaxSetup({
                async: false
            });
            // intAreaNoTown("sunday_opus_search_form_provinceId","sunday_opus_search_form_cityId","sunday_opus_search_form_districtId",null,null,null);
                #if($!editType != 1)
            initGuardAndBunchV2("sunday_student_body_search_guard","sunday_student_body_search_bunch","sunday_student_body_search_stu","$!param.guardId","$!param.bunchId","$!param.studentId","sunday_opus_search_form_provinceId","sunday_opus_search_form_cityId","sunday_opus_search_form_districtId");
                  #end

            function initGuardAndBunchV2(H, A, Z, B, E, R, D, F, C) {
                var I = "/sunday/web/guard/selectNoPage";
                var G = "/sunday/web/bunch/selectTuobanNoPage";
                var qinziurl = "/sunday/web/bunch/selectQinziBunchNoPage"
                var Q = "/sunday/web/student/selectNoPage";
                var qinziStudentUrl = "/sunday/web/student/selectQinziNoPage";
                $("#" + H).unbind("change");
                $("#" + H).children().remove();
                $("#" + A).children().remove();
                $("#" + H).append("<option value=''>请选择园区</option>");
                $.post(I, {
                            "provinceId": $("#" + D).val(),
                            "cityId": $("#" + F).val(),
                            "districtId": $("#" + C).val()
                        },
                        function(L) {
                            var J = L.result;
                            for (var K = 0; K < J.length; K++) {
                                if (typeof(B) != "undefined" && B != null && B != "" && B == J[K].id) {
                                    $("#" + H).append("<option selected value='" + J[K].id + "'>" + J[K].name + "</option>")
                                } else {
                                    $("#" + H).append("<option  value='" + J[K].id + "'>" + J[K].name + "</option>")
                                }
                            }
                            $("#" + H).change(function() {
                                $("#" + A).children().remove();
                                $("#" + A).append("<option value=''>请选择班级</option>");
                                if ($(this).val() != "") {
                                    tempGuardId = $(this).val();
                                    $.post(G, {
                                                "guardId": $(this).val()
                                            },
                                            function(N) {

                                                var O = N.result;
                                                for (var M = 0; M < O.length; M++) {
                                                    if (typeof(E) != "undefined" && E != null && E != "" && E == O[M].id) {
                                                        $("#" + A).append("<option selected value='" + O[M].id + "' valueType='" + O[M].type + "' valueCategory='" + O[M].category + "'>" + O[M].name + "</option>")
                                                    } else {
                                                        $("#" + A).append("<option value='" + O[M].id + "' valueType='" + O[M].type + "' valueCategory='" + O[M].category + "' >" + O[M].name + "</option>")
                                                    }
                                                }
                                                $.post(qinziurl, {
                                                            "guardId": tempGuardId
                                                        },
                                                        function(rs) {
                                                            var O = rs.result;
                                                            for (var M = 0; M < O.length; M++) {
                                                                if (typeof(E) != "undefined" && E != null && E != "" && E == O[M].id) {
                                                                    $("#" + A).append("<option selected value='" + O[M].id + "' valueType='" + O[M].type + "' valueCategory='" + O[M].category + "'>" + O[M].name + "</option>")
                                                                } else {
                                                                    $("#" + A).append("<option value='" + O[M].id + "' valueType='" + O[M].type + "' valueCategory='" + O[M].category + "' >" + O[M].name + "</option>")
                                                                }
                                                            }

                                                            $("#" + A).change(function() {
                                                                $("#" + Z).children().remove();
                                                                $("#" + Z).append("<option value=''>请选择学员</option>");
                                                                if ($(this).val() != "") {
                                                                    tempBunchId  = $(this).val();
                                                                    $.post(Q, {
                                                                                "guardId": $("#" + H).val(),
                                                                                "bunchId": $(this).val()
                                                                            },
                                                                            function(N) {
                                                                                var O = N.result;
                                                                                // 如果为空再查询亲子班

                                                                                if(O.length>0){
                                                                                    for (var M = 0; M < O.length; M++) {
                                                                                        if (typeof(R) != "undefined" && R != null && R != "" && R == O[M].id) {
                                                                                            $("#" + Z).append("<option selected value='" + O[M].id + "' valueType='" + O[M].type + "' valueCategory='" + O[M].category + "'>" + O[M].name + "</option>")
                                                                                        } else {
                                                                                            $("#" + Z).append("<option value='" + O[M].id + "' valueType='" + O[M].type + "' valueCategory='" + O[M].category + "' >" + O[M].name + "</option>")
                                                                                        }
                                                                                    }
                                                                                }else{
                                                                                    $.post(qinziStudentUrl, {
                                                                                                "guardId": tempGuardId,
                                                                                                "bunchId": tempBunchId
                                                                                            },
                                                                                            function(rs) {

                                                                                                var R = rs.result;

                                                                                                if(R.length>0) {
                                                                                                    for (var M = 0; M < R.length; M++) {

                                                                                                         $("#" + Z).append("<option value='" + R[M].id + "'>" + R[M].name + "</option>")
                                                                                                    }
                                                                                                }
                                                                                            }
                                                                                    );
                                                                                }


                                                                            })
                                                                }
                                                            });
                                                        })

                                            })
                                }
                            });
                            $("#" + H).change()
                        })
            }
        </script>
        <div class="item clearfix">
            <div class="com-titl"><i>*</i>晨、午检时间：</div>
        ##            取值直接设置value yyyy-MM-dd
            <input
                #if($!editType == 1) disabled  #end
                    style="background: #f2f2f2;" readonly type="text" id="studentbody_time" placeholder="请选择日期" maxlength="12" class="com-input-item fl"  name="recordTime"  #if($!editType == 1) value="$!bodyDetail.recordTime" #end  required="required">


            <label class="error-tip" id="recordTimeTip"></label>
        </div>
        <div class="item clearfix">
            <div class="com-titl"><i>*</i>类别：</div>
            <label><input type="radio"
                #if($!editType == 1) disabled #end
                          name="checkType" value="1"/><var>晨检</var></label>
            <label><input type="radio"
                #if($!editType == 1) disabled #end
                          name="checkType" value="2"/><var>午检</var></label>


        </div>
        <div class="item clearfix">
            <div class="com-titl"><i>*</i>体温：</div>
            <input type="text" onkeyup="temperatureCheck(event,this)" placeholder="请输入宝宝测量的体温"
                #if($!editType == 1) disabled #end
                   id="studentbody_temperature"
                   class="com-input-item fl"  name="temperature" maxlength="4"  #if($!editType == 1) value="$!bodyDetail.temperature" #end  required="required"><span style="float: left;line-height: 30px;">℃</span>

            <label class="error-tip" id="temperatureTip"></label>
        </div>
        <div class="item clearfix">
            <div class="com-titl"><i>*</i>处理方式：</div>
            <label><input type="radio" name="handleType" value="1"/><var>入园</var></label>
            <label><input type="radio" name="handleType" value="2"/><var>离园返家</var></label>
            <label class="error-tip" id="handleTypeTip"></label>

        </div>
        <div class="item clearfix">
            <div class="com-titl"><i>*</i>晨检嘱托：</div>
            <label><input type="radio"
                #if($!editType == 1) disabled #end
                          name="state" value="1"/><var>正常</var></label>
            <label><input type="radio"
                #if($!editType == 1) disabled #end
                          name="state" value="2"/><var>需要观察</var></label>
            <label><input type="radio"
                #if($!editType == 1) disabled #end
                          name="state" value="3"/><var>委托吃药</var></label>
            <label><input type="radio"
                #if($!editType == 1) disabled #end
                          name="state" value="4"/><var>传染病预警</var></label>

        </div>
        <div class="item clearfix">
            <div class="com-titl"><i>*</i>症状：</div>
            <div class="maxline fl">
                <label><input type="checkbox" name="symptom" value="1"/><var>发热</var></label>
                <label><input type="checkbox" name="symptom" value="2"/><var>咳嗽</var></label>
                <label><input type="checkbox" name="symptom" value="3"/><var>出疹</var></label>
                <label><input type="checkbox" name="symptom" value="4"/><var>腹泻</var></label>
                <label><input type="checkbox" name="symptom" value="5"/><var>呕吐</var></label>
                <label><input type="checkbox" name="symptom" value="6"/><var>黄疸</var></label>
                <label><input type="checkbox" name="symptom" value="7"/><var>红眼</var></label>
                <label><input type="checkbox" name="symptom" value="8"/><var>腮腺肿大</var></label>
                <label><input type="checkbox" name="symptom" value="9"/><var>其他</var></label>
            </div>
            <label class="error-tip" id="symptomTip"></label>
        </div>
        <div class="item clearfix">
            <div class="com-titl">备注：</div>
            <textarea id="studentbody_remarks"  style="float: left;" maxlength="200" name="remarks"  rows="4">#if($!editType == 1)$!bodyDetail.remarks#end</textarea>
            <label class="error-tip" id="remarksTip" style="float: left;"></label>
        </div>

        <div class="add-sample-reels">
            <div class="item clearfix">
                <div class="com-titl">
##                    <i>*</i>
                    添加图片：</div>
                <div class="img-wrapper fl" id="sunday_opus_image_div" style="width:auto">
##                    $!bodyDetail.imgaes
                </div>
                <div class="com-add-img xzBtn fl" style="width:144px;height:144px;background-image: url(#if($bunch.image)$!bunch.image#else$!{adminRoot}/yuhua/admin/img/add.png#end);"
                     id="sunday_upload_image_div"
                     onclick="upImg2(4,'sunday_upload_image_div','sunday_upload_image_input',144,144,2)">
                    <input type="hidden" id="sunday_upload_image_input" name="images" #if($!editType == 1) value="$!bodyDetail.images"  #end>

                </div>
                <div class="com-titl mw80 xzBtns">&nbsp;(600*600px)</div>
                <script>
                   var imgStr= $("#sunday_upload_image_input").val();
                   if (imgStr){
                       // console.log(imgStr)
                       var imgsArr=imgStr.split(",");
                       // console.log(imgStr)
                       imgsArr.forEach(function(it){
                           // console.log()
                           $("#sunday_opus_image_div").append('<div class="img-item"><img src="'+it+'" alt="" class="img-item"><img src="http://yhqmy.china95059.com:88/yuhua/admin/img/del.png" alt="" class="delete" onclick="$(this).parent().remove()">' +
                                   '<input type="hidden" value="'+it+'" name="image"></div>')
                       });
                        if ($("#sunday_opus_image_div img.img-item").length>=3){

                        }
                   }
                    function upImg2(bili,sunday_upload_image_div,sunday_upload_image_input,width,height,callType) {
                        if ($("#sunday_opus_image_div img.img-item").length>=3){
                            layer.alert("最多添加三张图片")
                           return;
                        }
                        layer.load(4, {shade: [0.8, '#393D49']})
                        $.ajax({
                            type: "post",
                            url: "/sunday/web/material/upload/index",
                            data: {"type": 3,"bili":bili,"imageDiv":sunday_upload_image_div,"imageInput":sunday_upload_image_input,"width":width,"height":height,"callType":callType},
                            async: false,
                            success: function (data) {
                                //关闭加载
                                layer.closeAll('loading');
                                $(".right-wrapper").after(data);

                            ## $(".right-wrapper").after('<div class="com-add-img xzBtn" style="width:300px;height:200px;background-image: url(#if($bunch.image)$!bunch.image#else$!{adminRoot}/yuhua/admin/img/add.png#end);"\n' +
                            ##         '                             id="sunday_upload_image_div"\n' +
                            ##         '                             onclick="upImg2(6,\'sunday_upload_image_div\',\'sunday_upload_image_input\',300,200)">\n' +
                            ##         '                            <input type="hidden" id="sunday_upload_image_input" name="image" value="$!bunch.image" >\n' +
                            ##         '                        </div>')

                            }
                        });
                    }
                </script>
            </div>
        </div>

        <div class="item clearfix">
            <div class="com-titl" style="height: 30px;"></div>
            <div class="button-wrapper fl">
                <a href="javascript:yuhua_return('/sunday/web/studentBody/index?isClean=0');" class="com-button return">返回</a>
                <button type="button" onclick="sunday_student_check()"  class="com-button sunday_plan_add sunday_plan_edit">保存</button>
            </div>
            <script>

                if("$!editType"==1) {
                    var arr="$!bodyDetail.symptom";
                    arr=arr.split(',')
                    // console.log(arr);
                    for(var i=0;i<arr.length;i++){
                        var a = arr[i];
                        if(a==1) $("input[name=symptom][value='1']").prop('checked', true);
                        if(a==2) $("input[name=symptom][value='2']").prop('checked', true);
                        if(a==3) $("input[name=symptom][value='3']").prop('checked', true);
                        if(a==4) $("input[name=symptom][value='4']").prop('checked', true);
                        if(a==5) $("input[name=symptom][value='5']").prop('checked', true);
                        if(a==6) $("input[name=symptom][value='6']").prop('checked', true);
                        if(a==7) $("input[name=symptom][value='7']").prop('checked', true);
                        if(a==8) $("input[name=symptom][value='8']").prop('checked', true);
                        if(a==9) $("input[name=symptom][value='9']").prop('checked', true);
                    }
                    var state="$!bodyDetail.state";
                    if(state==1) {
                        $("input[name=state][value='1']").prop('checked', true);
                    } else {
                        $("input[name=state][value='2']").prop('checked', true);
                    }
                    var checkType="$!bodyDetail.checkType";
                    if(checkType==1) {
                        $("input[name=checkType][value='1']").prop('checked', true);
                    } else {
                        $("input[name=checkType][value='2']").prop('checked', true);
                    }
                    // console.log(studentInfo.handleType)
                    var handleType="$!bodyDetail.handleType";
                    if(handleType==1) {
                        $("input[name=handleType][value='1']").prop('checked', true);
                    } else if(handleType==2) {
                        $("input[name=handleType][value='2']").prop('checked', true);
                    }
                } else {
                    $("input[name=state][value='1']").prop('checked', true);
                    $("input[name=checkType][value='1']").prop('checked', true);
                    $("input[name=handleType][value='1']").prop('checked', true);
                }
                var oldTemperatureValue = "";
                function temperatureCheck(event,obj){


//                    var curString = $(obj).val();
                    var rs = false;
                    switch(event.target.value.length){
                        case 0:
                            rs =  true;
                            break;
                        case 1:
                                rs =  ((/^[1-9]$/).test(event.target.value));
                            break;
                        case 2:
                            rs =  ((/^[1-9][0-9]$/).test(event.target.value));
                            break;
                        case 3:
                            rs =  ((/^[1-9][0-9][.]$/).test(event.target.value));
                            break;
                        case 4:
                            rs =  ((/^[1-9][0-9][.][0-9]$/).test(event.target.value));
                            break;
                        default:
                            break;
                    }

                    if(rs){
                        oldTemperatureValue = event.target.value;
                    }else{
                        $(obj).val(oldTemperatureValue);
                    }
                }

                function sunday_student_check(){
                    var recordTime = $("#studentbody_time").val();
                    var temperature = $("#studentbody_temperature").val();
                    var remarks = $("#studentbody_remarks").val();
                    var symptom= $('input[type=checkbox][name=symptom]:checked').map(function () {
                        return $(this).val();
                    }).get().join(",");
                    var nowHandleType=$("input[type=radio][name=handleType]:checked").val()
                    // 校验提示
                    $(".error-tip").html("");

                    if("$!editType"!=1) {
                        if ($("#sunday_student_body_search_guard").val() == "") {
                            $("#guardTip").html("请选择园区");
                            return;
                        }
                        if ($("#sunday_student_body_search_bunch").val() == "") {
                            $("#bunchTip").html("请选择班级");
                            return;
                        }
                        if ($("#sunday_student_body_search_stu").val() == "") {
                            $("#stuTip").html("请选择学生");
                            return;
                        }
                    }

                    if(!recordTime.length) {
                        $("#recordTimeTip").html("请选择晨检、午检时间");
                        return;
                    };
                    if(!recordTime.length) {
                        $("#recordTimeTip").html("请选择晨检、午检时间");
                        return;
                    };
                    if(!temperature.length){

                        $("#temperatureTip").html("请输入宝宝测量的体温");
                        return;
                    }
                    if(!(/^([1-9]\d{0,2}|0)([.]?|(\.\d{0,1})?)$/.test(temperature))) {
                        $("#temperatureTip").html("您的数据有误，请确认后输入");
                        return;
                    };
                    if(nowHandleType==null||!nowHandleType.length) {
                        $("#handleTypeTip").html("请选择处理方式");
                        return;
                    };
                    if(!symptom.length) {
                        $("#symptomTip").html("请选择症状");
                        return;
                    };

                    sub();
                }
                function sub() {
                    var imgArr=[],symptomArr=[];
                    $('#sunday_opus_image_div img.img-item').each(function(){imgArr.push($(this).attr("src"))})
                    // console.log($("input[type=radio][name=handleType]:checked").val())
                    var nowHandleType=$("input[type=radio][name=handleType]:checked").val()
                    // return;
                    $('input[type=checkbox][name=symptom]:checked').each(function(){symptomArr.push($(this).val())})

                    var newData = {
                        // 取值
                        recordTime: $("#studentbody_time").val(),
                        temperature: $("#studentbody_temperature").val(),
                        symptom: symptomArr.join(","),
                        state: $("input[type=radio][name=state]:checked").val(),
                        images:imgArr.join(","),
                        checkType: $("input[type=radio][name=checkType]:checked").val(),
                        handleType:nowHandleType,
                        remarks: $("#studentbody_remarks").val(),
                        creatorType:1,
                        memberId:0,
                        studentId:$("#sunday_student_body_search_stu option:selected").val()
                    }
                    if("$!editType"!=1) {
                        newData.studentId= $("#sunday_student_body_search_stu option:selected").val();
                    }
                    if("$!editType"==1) {
                        newData.id= "$!bodyDetail.id"
                    }
                    $.ajax({
                        type: "post",
                        url: "$!editType"==1?"/sunday/web/studentBody/edit":"/sunday/web/studentBody/add",
                        data: newData,
                        async: false,
                        success: function (data) {
                            //关闭加载
                            layer.closeAll('loading');
                            if (data.code == 0) {
//                                layer.alert("保存成功");
                                setTimeout(function () {
                                    newTab("/sunday/web/studentBody/index?isClean=1");
                                },100);
                            } else {
                                //如果重复，需要确认交互
                                layer.alert("保存失败");
                            }
                        }
                    });
                };
            </script>
        </div>
    </form>
</div>
<script>
    laydate.render({
        elem: '#studentbody_time' //指定元素
        ,format: 'yyyy-MM-dd'
        ,theme: 'xxx'
        ,max: 0
    });
</script>