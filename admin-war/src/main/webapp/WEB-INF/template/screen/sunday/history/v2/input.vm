#parse("/screen/sunday/operate.vm")
<div class="class-community-details">
    <div class="con">
        <div class="com-title-wrapper">
            <div class="title">成长记录信息</div>
        </div>
        <div class="item clearfix">
            <div class="com-titl">姓名：</div>
            <input type="text" placeholder="" class="com-input-item fl" value="$!{result.history.memberName}" disabled="disabled">
        </div>
        <div class="item clearfix">
            <div class="com-titl">园区名称：</div>
            <input type="text" placeholder="" class="com-input-item fl" value="$!{result.history.guardName}" disabled="disabled">
        </div>
        <div class="item clearfix">
            <div class="com-titl">班级名称：</div>
            <input type="text" placeholder="" class="com-input-item fl" value="$!{result.history.bunchName}" disabled="disabled">
        </div>
        <div class="item clearfix">
            <div class="com-titl">成长记录标签：</div>
            #foreach($!tag in $!result.tags)
            <button type="button" class="com-button mr20 fl">$!tag</button>
             #end
        </div>
        <div class="item clearfix">
            <div class="com-titl">成长记录图片：</div>
            <div class="img-wrapper">
                #foreach($!image in $!result.images)
                    <img src="$!imgRoot$!image" alt="" class="img-item">
                #end
            </div>
        </div>
        <div class="item clearfix">
            <div class="com-titl">成长记录详情：</div>
            <textarea class="com-textarea" disabled="disabled">$!{result.history.content}</textarea>
        </div>
        <div class="item clearfix">
            <div class="button-wrapper fl" style="width: 100%;margin-top: 20px">
                <a href="javascript:newTab('/sunday/web/history/index?isClean=0');" class="com-button return">返回</a>
            </div>
        </div>
    </div>
</div>