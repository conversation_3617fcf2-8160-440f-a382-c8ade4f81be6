#parse("/screen/sunday/operate.vm")
<div class="add-plan add-rest add-role">
    <form id="sunday_daily_form" class="form-horizontal" action="/sunday/web/daily/save" method="post" enctype="multipart/form-data">
        <input type="hidden" name="id" value="$!{daily.id}">
        <input type="hidden" name="ids" id="sunday_daily_form_ids_input">
        <input type="hidden" name="startTimes" id="sunday_daily_form_startTimes_input">
        <input type="hidden" name="endTimes" id="sunday_daily_form_endTimes_input">
        <input type="hidden" name="contents" id="sunday_daily_form_contents_input">
        
        <input type="hidden" name="guardIds" id="sunday_daily_form_guardIds_input">
        <input type="hidden" name="bunchIds" id="sunday_daily_form_bunchIds_input">
        <input type="hidden" name="guardAndBunchJson" id="sunday_daily_form_guardAndBunchJson_input">

        <!-- 一周作息 -->
        <div class="list">
            <div class="com-title-wrapper">
                <div class="title">一周作息明细：</div>
                #if($!daily.id > 0)
               <button type="button" class="com-button fr" style="margin: 12px 20px 0 0;" onclick="window.open('/sunday/web/daily/pdf?id=$!daily.id','_blank')">导出PDF</button>
                #end
                <button type="button" class="com-button fr" style="margin: 12px 20px 0 0;" onclick="sunday_daily_detail_add()">添加作息时间</button>

            </div>

            <div class="item clearfix">
                <div class="com-titl" style="height: 30px;"></div>
                <div class="time-wrapper fl" id="sunday_daily_form_detai_div">
                    <!--一日作息单条-->
                    #foreach($detail in ${daily.details})
                        <div class="time-item clearfix">
                            <input type="hidden" value="$!{detail.id}" class="idInput">
                            <div class="com-select-input fl mr20">
                                <input type="text" class="startTimeInput" >
                                <select class="editable-select timeDiy1"  value="$!{detail.startTime}"   required="required"></select>
                            </div>
                            <div class="com-select-input fl mr20">
                                <input type="text" class="endTimeInput">
                                <select class="editable-select timeDiy2"  value="$!{detail.endTime}"  required="required"></select>
                            </div>
                            <div class="com-select-input fl mr20">
                                <input type="text" class="contentInput" value="$!{detail.content}">
                                <select class="editable-select contentDiy" value="$!{detail.content}"  required="required"></select>
                            </div>
                            <button type="button" class="com-button fl" onclick="sunday_daily_detail_delete(this,${detail.id})">删除</button>
                        </div>
                    #end
                </div>
            </div>

        </div>
        <!--园区信息-->
        <div class="search-wrapper clearfix" style="margin-top: 20px">
            <div class="list">
                <div class="com-title-wrapper">
                    <div class="title">关联园区</div>
                </div>
                <div class="item clearfix">

                    <table border="0" cellspacing="0" cellpadding="0" class="fl" id="sunday_daily_guard_and_bunch_table" style="margin-left: 100px">
                        <tr>
                            <th width="20%">园区</th>
                            <th>班级</th>
                        </tr>
                        #foreach($!guard in $!daily.guards)
                            <tr>
                                <td><div class="com-checkbox-item2 firstMenu#if($!guard.isCheck == 1) on #end" value="$!guard.id">$!guard.name</div></td>
                                <td>
                                    #foreach($!bunch in $!guard.bunches)
                                        <div class="com-checkbox-item2 secondMenu#if($!bunch.isCheck == 1) on #end" value="$!bunch.id"> $!bunch.name</div>
                                    #end
                                </td>
                            </tr>
                        #end
                    </table>
                </div>
                <!--按钮功能-->
                <div class="list">
                    <div class="item clearfix">
                        <div class="button-wrapper fl" style="width: 100%;margin-top: 20px">
                            <a href="javascript:yuhua_return('/sunday/web/daily/index?isClean=0');" class="com-button return">返回</a>
                            <button type="submit" class="com-button sunday_daily_add sunday_daily_edit">保存</button>
                        </div>
                    </div>
                </div>
            </div>
            <script>
                //一级菜单勾选
                $(".firstMenu").click(function () {
                    //勾选
                    if(!$(this).hasClass("on")){
                        //1,勾选一级菜单
                        $(this).addClass("on");
                        //2，勾选全部二级菜单
                        $(this).parent().next().find(".secondMenu").addClass("on");
                        //反选
                    }else{
                        //1,反选一级菜单
                        $(this).removeClass("on");
                        //2，反选全部二级菜单
                        $(this).parent().next().find(".secondMenu").removeClass("on");
                    }
                })
                $(".secondMenu").click(function () {
                    //勾选
                    if(!$(this).hasClass("on")){
                        //1,勾选二级级菜单
                        $(this).addClass("on");
                        //2，勾选一级菜单
                        $(this).parent().prev().find(".firstMenu").addClass("on");
                        //反选
                    }else{
                        //1,反选二级级菜单
                        $(this).removeClass("on");
                        //2,判断是否还没有二级菜单被勾选
                        var isAllNot=true;
                        $(this).siblings().each(function () {
                            if($(this).hasClass("on")){
                                isAllNot=false;
                                return false;
                            }
                        });
                        if(isAllNot){
                            //2，反选一级菜单
                            $(this).parent().prev().find(".firstMenu").removeClass("on");
                        }
                    }
                })

            </script>
        </div>

    </form>
</div>
<script>

    var dictionary_url ="$!dictionaryRoot";
   // intAreaNoTown("sunday_daily_form_provinceId","sunday_daily_form_cityId","sunday_daily_form_districtId",null,null,null);
   // initGuardAndBunchCheckbox("sunday_daily_form_guardId","sunday_daily_form_bunchId_div","$!daily.guardId","$!daily.bunchIds","sunday_daily_form_provinceId","sunday_daily_form_cityId","sunday_daily_form_districtId");
    //2018年10月22日，省市区控件，控制园区选项
   // $("#sunday_daily_form_provinceId,#sunday_daily_form_cityId,#sunday_daily_form_districtId").change(function(){
   //     initGuardAndBunchCheckbox("sunday_daily_form_guardId","sunday_daily_form_bunchId_div","$!daily.guardId","$!daily.bunchIds","sunday_daily_form_provinceId","sunday_daily_form_cityId","sunday_daily_form_districtId");
   // })

</script>
<script>
    //实例化
    var timeArr=new Array()//带：的时间
    var minute = 0;
    var hour = 7;

    while (true) {
        var hourStr = hour < 10 ? "0"+hour:hour;
        var minuteStr  = minute < 10 ? "0"+minute:minute;
        timeArr.push(hourStr+":"+minuteStr);
        minute += 15;
        if(minute > 45){
            minute = 0
            hour += 1;
        }
        if(hour == 20){
            break;
        }
    }
    function initTimeSelect(){
         for(var i = 0;i<timeArr.length;i++){
             var time = timeArr[i];
             $(".timeDiy1").children().remove();
             $(".timeDiy2").children().remove();
             $(".timeDiy1").each(function(){
                 var oldValue =$(this).attr("value");
                 if(oldValue == time){
                     $(this).append("<option  selected>"+time+"</option>");
                 }else{
                     $(this).append("<option >"+time+"</option>");
                 }
                 //填入input
                 $(this).parent().find(".startTimeInput").val(oldValue);
             })
             $(".timeDiy2").each(function(){
                 var oldValue =$(this).attr("value");
                 if(oldValue == time){
                     $(this).append("<option selected>"+time+"</option>");
                 }else{
                     $(this).append("<option>"+time+"</option>");
                 }
                 //填入input
                 $(this).parent().find(".endTimeInput").val(oldValue);
             });
         }

     }
    var contentArr=new Array()
    contentArr.push("入园");
    contentArr.push("晨间体操");
    contentArr.push("盥洗、早点");
    contentArr.push("蒙氏工作");
    contentArr.push("晨间谈话");
    contentArr.push("盥洗、喝水");
    contentArr.push("游戏活动");
    contentArr.push("户外活动");
    contentArr.push("团讨活动");
    contentArr.push("盥洗、水果");
    contentArr.push("主题活动");
    contentArr.push("外教活动");
    contentArr.push("餐前准备");
    contentArr.push("午餐");
    contentArr.push("餐后活动");
    contentArr.push("午睡");
    contentArr.push("起床");
    contentArr.push("区域活动");
    contentArr.push("离园准备");

    function intitContentSelect(){
        $(".contentDiy").children().remove();
        for(var i = 0;i<contentArr.length;i++){
            var content = contentArr[i];
            $(".contentDiy").each(function(index){
                var hasOption = false;
                var oldValue =$(this).parent().find(".contentInput").val();
                if(oldValue == content){
                    $(this).append("<option selected>"+content+"</option>");
                }else{
                    $(this).append("<option >"+content+"</option>");
                }
                $(this).parent().find(".contentInput").val(oldValue || contentArr[0])
            })
        }
    }
    function initSelect(){
        initTimeSelect();
        intitContentSelect();

        //监听select选择时间
        $(".timeDiy1,.timeDiy2,.contentDiy").change(function () {
            time_select_change(this);

            //将选择的值放到输入框中
            $(this).parent().find('input').val($(this).val());
            //将选择的值放到select的value属性上
            $(this).attr("value",$(this).val())
        })

        // 下拉输入检测。使用正则
        var pattern = /^[0-1][0-9]:[0-5][0-9]$/;
        $(".startTimeInput,.endTimeInput").change(function (){
            console.log("开始输入="+$(this).val());
            //人工替换大写冒号
            var thisValue = $(this).val();
            thisValue=thisValue.replace("：",":");
            if(!pattern.test(thisValue)){
                $(this).val("");
                $(this).next().attr("value","");
            }else{
                $(this).next().attr("value",thisValue);
                var t = $(this).parent().find("select");
                time_select_change(t);
            }

        });
    }

    initSelect();

    //关联两个时间select
    function time_select_change(t){
        var thisClass = $(t).attr("class");
        if(thisClass.indexOf("timeDiy1")!=-1){
            console.log("时间监听-开始时间"+thisClass)
            // $(".timeDiy1").change(function(){
            var thisValDate = $(t).val();
            var thisValInt = parseInt(thisValDate.replace(":",""));
            var nextSelect = $(t).parent().parent().find(".timeDiy2");
            //    console.log(nextSelect)
            var options = $(nextSelect).children("option");
            $(nextSelect).children("option").each(function(){
                var nextValDate = $(this).val();
                var nextValInt = parseInt(nextValDate.replace(":",""));
                if(nextValInt<=thisValInt){
                    $(this).hide();
                }else{
                    $(this).show();
                }
            });
            //  })
        }
        if(thisClass.indexOf("timeDiy2")!=-1){
            console.log("时间监听-结束时间"+thisClass)
            var thisValDate = $(t).val();
            var thisValInt = parseInt(thisValDate.replace(":",""));
            var prevSelect = $(t).parent().prev().children("select");
            var options = $(prevSelect).children("option");
            $(prevSelect).children("option").each(function(){
                var prevValDate = $(this).val();
                var prevValInt = parseInt(prevValDate.replace(":",""));
                if(prevValInt>=thisValInt){
                    $(this).hide();
                }else{
                    $(this).show();
                }
            });
        }
    }
</script>

<script type="text/javascript">


    //作息明细新增
    function sunday_daily_detail_add() {


        var div='<div class="time-item clearfix">';
                    div+='<input type="hidden" value="0" class="idInput">';
                    div+='<div class="com-select-input fl mr20">';
                        div+='<input type="text" class="startTimeInput" >';
                        div+='<select class="editable-select timeDiy1"  value="07:00"  required="required"></select>';
                    div+='</div>';
                    div+='<div class="com-select-input fl mr20">';
                        div+='<input type="text" class="endTimeInput">';
                        div+='<select class="editable-select timeDiy2"  value="07:00"  required="required"></select>';
                    div+='</div>';
                    div+='<div class="com-select-input fl mr20">';
                        div+='<input type="text" class="contentInput">';
                        div+='<select class="editable-select contentDiy" value="123"  required="required"></select>';
                    div+='</div>';
                    div+='<button type="button" class="com-button fl" onclick="sunday_daily_detail_delete(this,0)">删除</button>';
            div+='</div>';

        $("#sunday_daily_form_detai_div").append(div);

        initSelect();
    }


    //作息明细删除
    function sunday_daily_detail_delete(t, detailId) {
        if (detailId != 0) {
            $.post("/sunday/web/daily/detail/delete?id=" + detailId);
        }
        $(t).parent().remove();

    }

    //实例化表单组件

    var validatorMenu = $("#sunday_daily_form").validate({
        submitHandler: function(form) {

            //加遮罩层

            //  $("#sunday_daily_form_bunchName").val($("#sunday_daily_form_bunchId option:selected").text());
            //获取作息明细
            var ids = "";
            var startTimes = "";
            var endTimes = "";
            var contents = "";
            var isFinish = true;
            $("#sunday_daily_form_detai_div").find(".time-item").each(function(){
                // console.log($(this));
                ids+=$(this).find(".idInput").val()+",";
                startTimes+=$(this).find(".startTimeInput").val()+",";
                if($(this).find(".startTimeInput").val()==null||$(this).find(".startTimeInput").val()==""){
                    isFinish = false;
                    return false;
                }
                endTimes+=$(this).find(".endTimeInput").val()+",";
                if($(this).find(".endTimeInput").val()==null||$(this).find(".endTimeInput").val()==""){
                    isFinish = false;
                    return false;
                }

                contents+=$(this).find(".contentInput").val()+",";
                if($(this).find(".contentInput").val()==null||$(this).find(".contentInput").val()==""){
                    isFinish = false;
                    return false;
                }
            })
            if(!isFinish){
                layer.msg("请完整填写作息信息", {
                    icon: 2
                });
                return ;
            }
            if(ids != ""){
                ids = ids.substring(0,ids.length-1);
                $("#sunday_daily_form_ids_input").val(ids);
            }
            if(startTimes != ""){
                startTimes = startTimes.substring(0,startTimes.length-1);
                $("#sunday_daily_form_startTimes_input").val(startTimes);
            }
            
            if(endTimes != ""){
                endTimes = endTimes.substring(0,endTimes.length-1);
                $("#sunday_daily_form_endTimes_input").val(endTimes);
            }

            if(contents != ""){
                contents = contents.substring(0,contents.length-1);
                $("#sunday_daily_form_contents_input").val(contents);
            }

            //园区和班级信息
            var guardIds = "";
            var bunchIds = "";

            $("#sunday_daily_guard_and_bunch_table").find(".firstMenu").each(function(){
                if($(this).hasClass("on")){
                    guardIds +=$(this).attr("value")+",";
                }
            });

            $("#sunday_daily_guard_and_bunch_table").find(".secondMenu").each(function(){
                if($(this).hasClass("on")){
                    bunchIds +=$(this).attr("value")+",";
                }
            });
            //2019年4月28日，增加园区和班级JSON，用于按园区生成教学计划
            var guardAndBunchJson = new Array();
            $("#sunday_daily_guard_and_bunch_table").find(".firstMenu.on").each(function(){
                var guardJson =  JSON.parse("{}");
                guardJson["guardIds"]=","+$(this).attr("value")+",";
                var bunchArray = ",";
                $(this).parent().next().find(".secondMenu.on").each(function(){
                    bunchArray +=$(this).attr("value")+",";
                });
                guardJson["bunchIds"]=bunchArray;
                guardAndBunchJson.push(guardJson);
            });
            if(guardIds!=""){
                guardIds=","+guardIds;
                // guardIds =guardIds.substring(0,guardIds.length-1);
            }
            if(bunchIds!=""){
                bunchIds=","+bunchIds;
                // bunchIds =bunchIds.substring(0,bunchIds.length-1);
            }
            $("#sunday_daily_form_guardIds_input").val(guardIds);
            $("#sunday_daily_form_bunchIds_input").val(bunchIds);
            //2019年4月30日，新增
            $("#sunday_daily_form_guardAndBunchJson_input").val(JSON.stringify(guardAndBunchJson));
            layer.load(4, {shade: [0.8, '#393D49']})

            $(form).ajaxSubmit({
                //你表单提交就要切换页面，ajax加载又是异步的。两边同时进行，
                //当ajax加载在切换页面之前完成，就会执行回调，反之不会执行
                //加同步提交数据
                async: false,
                dataType: "json",
                success: function (data) {
                    //关闭加载
                    layer.closeAll('loading');
                    if (data.code == 0) {
                        //先刷新，后关闭。顺序不要反了
                        //刷新列表
                        newTab('/sunday/web/daily/index?isClean=0',null);
                    } else {
                        layer.msg(data.message, {
                            icon: 2
                        });
                    }
                }
            });
        }
    });

</script>
