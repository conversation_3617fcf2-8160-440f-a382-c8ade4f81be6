
<!--列表页-->
<div class="weekly-plan">
    <div class="search-wrapper clearfix">
        <form action="" method="">
            <div class="clearfix">
                <input type="text" placeholder="评测名称" class="com-input-item item fl" id="sunday_assessment_search_form_name">
                <button type="button" class="com-button item" onclick="sunday_assessment_search()">搜索</button>
                <a href="javascript:;" class="com-add-button item" onclick="sunday_assessment_save(0)">新增</a>
                <a href="javascript:;" class="com-add-button item" onclick="sunday_assessment_batch_save()">导入评测</a>
            </div>
        </form>
    </div>
<!-- 查询 -->
    <div class="list" id="sunday_assessment_div">

    </div>
</div>

<div class="mask" style="display: none"></div>

<!-- 新增评测 -->
<div class="com-popup kindergarten-assessment-popup" id="sunday_assessment_save_div">
    <form id="sunday_assessment_save_form"  action="/sunday/web/assessment/save"  method="post"  enctype="multipart/form-data">
        <input type="hidden" name="id">
        <input type="hidden" name="type">
        <input type="hidden"  name="expertName" id="sunday_assessment_save_form_expertName">
        <div class="title">新增评测</div>
        <div class="item clearfix">
            <div class="com-titl"><i>*</i>评测名称：</div>
            <input type="text" class="com-input-item fl" name="name"   required="required">
        </div>
        <div class="item clearfix">
            <div class="com-titl"><i>*</i>评测类型：</div>
            <div class="selectpicker">
                <select id="sunday_assessment_save_form_type" disabled="disabled"></select>
            </div>
        </div>
        <div class="item clearfix">
            <div class="com-titl"><i>*</i>专家信息：</div>
            <div class="selectpicker">
                <select id="sunday_assessment_save_form_expertId" name="exportId"   required="required"></select>
            </div>
        </div>
        <div class="button-wrapper">
            <a href="javascript:sunday_assessment_save_operate();" class="com-button return">关闭</a>
            <button type="submit" class="com-button">保存</button>
        </div>

    </form>
</div>
<!--导入评测-->
<div class="com-popup kindergarten-assessment-popup kindergarten-assessment-popup2" id="sunday_assessment_batch_save_div">
    <form id="sunday_assessment_batch_save_form" action="/sunday/web/assessment/batchSave"  method="post"  enctype="multipart/form-data">
        <input type="hidden" name="type">
        <div class="title">评测导入管理</div>
        <div class="item clearfix">
            <div class="com-titl"><i>*</i>评测名称：</div>
            <div class="selectpicker">
                <input type="text" class="com-input-item fl" name="name"   required="required">
            </div>
        </div>
        <div class="item clearfix">
            <div class="com-titl"><i>*</i>类型：</div>
            <div class="selectpicker">
                <input  class="form-control "  name="typeStr" disabled="disabled" />
            </div>
        </div>
        <div class="item clearfix">
            <div class="com-titl"><i>*</i>excel文件：<br>(仅支持.xls文件)</div>
            <input   name="excelFile" type="file" required="required"/>
        </div>
        <div class="item clearfix">
            <div class="com-titl">模板下载：</div>
            #if($!type == 1)
                <button type="button" class="com-button" onclick="window.open('$!staticRoot/assessment1.xls')">点击下载</button>
                #elseif($!type == 2)
                  <button type="button" class="com-button" onclick="window.open('$!staticRoot/assessment2.xls')">点击下载</button>
            #end

        </div>
        <div class="button-wrapper">
            <a href="javascript:sunday_assessment_batch_save_operate()" class="com-button return">关闭</a>
            <button type="submit" class="com-button">保存</button>
        </div>

    </form>
</div>

<script>
    var dictionary_url ="$!dictionaryRoot"
    function sunday_assessment_batch_save(type) {
        $("#sunday_assessment_batch_save_form").form("clear");
        var type= "$!type";
        var typeStr = type == 1?"入园评测":"注意力评测";
        $("#sunday_assessment_batch_save_form").form("load",{"type":type,"typeStr":typeStr});

        sunday_assessment_batch_save_operate()
    }
    function sunday_assessment_batch_save_operate() {
        if($("#sunday_assessment_batch_save_div").hasClass("on")){
            $("#sunday_assessment_batch_save_div").removeClass("on");
            $(".mask").hide();
        }else{
            $("#sunday_assessment_batch_save_div").addClass("on");
            $(".mask").show();
        }
    }
    var validatorMenu=$("#sunday_assessment_batch_save_form").validate({
        submitHandler: function(form) {
            layer.load(4, {shade: [0.8, '#393D49']})
            $(form).ajaxSubmit({
                //你表单提交就要切换页面，ajax加载又是异步的。两边同时进行，
                //当ajax加载在切换页面之前完成，就会执行回调，反之不会执行
                //加同步提交数据
                async: false,
                success:function(data){
                    //关闭加载
                    layer.closeAll('loading');
                    if(data.code ==0){
                        //刷新列表
                        sunday_assessment_search();
                        //关闭窗口
                        sunday_assessment_batch_save_operate()
                    }else{
                        layer.msg('导入失败', {
                            icon: 2
                        });
                    }
                }
            });
        }
    }) ;


</script>
<!--新增-->
<script>
    function sunday_assessment_save(id){
        $("#sunday_assessment_save_form").form("clear");
        $("#sunday_assessment_save_form").form("load", {"id":id,"type":"$!type"});
        //$("#sunday_assessment_save_form_type").attr("disabled",false);
        var oldType = "$!type";
        var oldExpertId= null
        if(id!=null && id !=0){
            $.ajax({
                type: "post",
                url: "/sunday/web/assessment/findOne",
                data: {"id": id,"grade":1},
                async: false,
                success: function (data) {
                    $("#sunday_assessment_save_form").form("load",data.result);
                    oldType=data.result.type;
                    oldExpertId=data.result.expertId;
                    //  $("#sunday_assessment_save_form_type").attr("disabled",true);
                }
            });
        }
        // console.log(oldType);
        var dictionary_url ="$!dictionaryRoot";

        iniSelect("sunday_assessment_save_form_type",dictionary_url+"assessment_type",oldType);
        initExpert("sunday_assessment_save_form_expertId",oldExpertId);

        sunday_assessment_save_operate();
    }
    function sunday_assessment_save_operate() {
        if($("#sunday_assessment_save_div").hasClass("on")){
            $("#sunday_assessment_save_div").removeClass("on");
            $(".mask").hide();
        }else{
            $("#sunday_assessment_save_div").addClass("on");
            $(".mask").show();
        }
    }
    var validatorMenu=$("#sunday_assessment_save_form").validate({
        submitHandler: function(form) {
            //加遮罩层
            layer.load(4, {shade: [0.8, '#393D49']})
            $("#sunday_assessment_save_form_expertName").val( $("#sunday_assessment_save_form_expertId option:selected").text())
            $(form).ajaxSubmit({
                //你表单提交就要切换页面，ajax加载又是异步的。两边同时进行，
                //当ajax加载在切换页面之前完成，就会执行回调，反之不会执行
                //加同步提交数据
                async: false,
                success:function(data){
                    //关闭加载
                    layer.closeAll('loading');
                    if(data.code ==0){
                        //关闭窗口
                        sunday_assessment_save_operate();
                        //刷新列表
                        sunday_assessment_search();
                    }else{
                        layer.msg(data.message, {
                            icon: 2
                        });
                    }
                }
            });
        }
    }) ;


</script>
<script>

    var sunday_assessment_pageNumber=1;
    var sunday_assessment_pageSize=50;
    var sunday_assessment_sort="id";
    var sunday_assessment_order="desc";
    var sunday_assessment_is_loading=false;
    var sunday_assessment_is_end=false;
    var sunday_assessment_name="";
    //输入订单号查询
    function sunday_assessment_search(){
        sunday_assessment_name=$("#sunday_assessment_search_form_name").val();
        sunday_assessment_is_loading=false;
        sunday_assessment_is_end=false;
        sunday_assessment_pageNumber =1;
        // sunday_assessment_pageSize =1;
        sunday_assessment_getData(true);
    }
    function sunday_assessment_search_page(pageNumber,pageSize){
        sunday_assessment_pageNumber =pageNumber;
        sunday_assessment_pageSize =pageSize==null?sunday_assessment_pageSize:pageSize;
        sunday_assessment_getData(true);
    }
    function sunday_assessment_getData(isRemove){
        var t="#sunday_assessment_div";

        if(sunday_assessment_is_loading||sunday_assessment_is_end)return;
        if(isRemove){
           $(t).children().remove();
        }
        sunday_assessment_is_loading=true;
        //同步请求
        $.ajax({
            type: "POST",
            url: "/sunday/web/assessment/select",
            data: {
                "pageSize":sunday_assessment_pageSize,
                "pageNumber":sunday_assessment_pageNumber,
                "sort":sunday_assessment_sort,
                "order":sunday_assessment_order,
                "name":sunday_assessment_name,
                "type":"$!type"

            },
            dataType: "text",
            async:true,
            success: function(result){
                //d layer.closeAll('loading');
                //返回的是html
                if(result==null||result==""||result.length<1){
                    sunday_assessment_is_end=true;
                }else{
                    if(isRemove && $(t).children()!=null){
                        $(t).children().remove();
                    }
                    $(t).append(result);
                    sunday_assessment_is_loading=false;
                }
            }
        });
    }
    sunday_assessment_search();


    function sunday_assessment_quetion_index(id,type,name){
        newTab('/sunday/web/assessment/question/index?assessmentId='+id+'&type='+type+'&assessmentName='+name , "试题管理");
    }
    /* function sunday_assessment_delete(id) {
         var msg = "确认要删除这条信息吗?";
         layer.confirm(msg, function (index) {
             layer.load(4, {shade: [0.8, '#393D49']})
             //不要异步操作
             $.ajax({
                 type: "post",
                 url: "/sunday/web/assessment/delete",
                 data: {"id": id},
                 async: false,
                 success: function (data) {
                     //关闭加载
                     layer.closeAll('loading');
                     if (data.code == 0) {
                         newTab('/sunday/web/assessment/index',null)
                     } else {
                         layer.msg('删除失败', {
                             icon: 2
                         });
                     }
                 }
             });
             //最后手动关闭
             layer.close(index);

         });
     }*/
</script>

