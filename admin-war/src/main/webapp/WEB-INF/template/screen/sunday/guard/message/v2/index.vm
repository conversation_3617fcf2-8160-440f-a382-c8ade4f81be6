#parse("/screen/sunday/operate.vm")
<div class="weekly-plan">
    <div class="search-wrapper clearfix">
            <div class="com-title-wrapper">
                <div class="title">条件搜索</div>
            </div>
            <table class="search-table" style="margin-top: 20px">
                <tr>
                    <td><div class="search-titl">地区选择：</div></td>
                    <td>
                        <div class="search-selectpicker">
                            <select id="sunday_guard_message_search_form_provinceId"></select>
                        </div>
                    </td>
                    <td>
                        <div class="search-selectpicker">
                            <select  id="sunday_guard_message_search_form_cityId"></select>
                        </div>
                    </td>
                    <td>
                        <div class="search-selectpicker">
                            <select   id="sunday_guard_message_search_form_districtId"></select>
                        </div>
                    </td>
                    <td></td>
                    <td></td>
                </tr>
                <tr>
                    <td><div class="search-titl">园区选择：</div></td>
                    <td>
                        <div class="search-selectpicker">
                            <select id="sunday_guard_message_search_form_guardId"></select>
                        </div>
                    </td>
                    <td><button type="button" class="search-button" onclick="sunday_guard_message_search(true)">搜索</button>
                    </td>
                    <td>
                    </td>
                    <td></td>
                    <td></td>
                </tr>
            </table>
            <script>
                $.ajaxSetup({
                    async: false
                });
                intAreaNoTown("sunday_guard_message_search_form_provinceId","sunday_guard_message_search_form_cityId","sunday_guard_message_search_form_districtId",null,null,null);
                initGuard("sunday_guard_message_search_form_guardId","$!param.guardId","sunday_guard_message_search_form_provinceId","sunday_guard_message_search_form_cityId","sunday_guard_message_search_form_districtId");
                //2018年10月22日，省市区控件，控制园区选项
                $("#sunday_guard_message_search_form_provinceId,#sunday_guard_message_search_form_cityId,#sunday_guard_message_search_form_districtId").change(function(){
                    initGuard("sunday_guard_message_search_form_guardId",null,"sunday_guard_message_search_form_provinceId","sunday_guard_message_search_form_cityId","sunday_guard_message_search_form_districtId");
                })
            </script>
        </form>
    </div>
    <!-- 查询 -->
    <div class="list" id="sunday_guard_message_div">

    </div>
</div>
<!--回复-->
<div class="mask" id="sunday_guard_message_mask_div" style="display: none"></div>
<!-- 00000000000000000000000000000000000000000000000000 园长回复弹窗开始（12.28） 0000000000000000000000000000000000000000-->
<div class="com-popup mailbox-chat-popup" id="sunday_guard_message_response_div">
    <div class="title">回复</div>
    <i class="clos" onclick="sunday_guard_message_save()"></i>
    <div class="cat-list" id="sunday_guard_message_response_message_div">

    </div>
    <div class="msg-wrapper">
        <form  id="sunday_guard_message_form" action="/sunday/web/guard/message/save" method="post" enctype="multipart/form-data">
            <input type="hidden" name="oldMessageId" id="sunday_guard_message_form_oldMessageId"/>
            <input type="hidden" name="groupId" id="sunday_guard_message_form_groupId"/>

            <textarea  name="content" placeholder="请输入回复内容"  id="sunday_guard_message_form_content"> </textarea>
        </form>
    </div>
    <div class="button-wrapper">
        <a href="javascript:sunday_guard_message_save();" class="com-button return">关闭</a>
        <button type="button" class="com-button" onclick="sunday_guard_message_submit()">发送</button>
    </div>
</div>
<!-- 00000000000000000000000000000000000000000000000000 园长回复弹窗结束（12.28） 0000000000000000000000000000000000000000-->
<script>
    function sunday_guard_message_save(oldMessageId,groupId){
        $("#sunday_guard_message_form").form("clear");
        if($("#sunday_guard_message_response_div").hasClass("on")){
            $("#sunday_guard_message_response_div").removeClass("on");
            $("#sunday_guard_message_mask_div").hide();
        }else{
            $("#sunday_guard_message_response_div").addClass("on");
            $("#sunday_guard_message_mask_div").show();
            $("#sunday_guard_message_form").form("load",{"oldMessageId":oldMessageId,"groupId":groupId});
            sunday_guard_message_getMessage(groupId);
        }

    }

    //2018年12月28日
    function sunday_guard_message_getMessage(groupId){
        $("#sunday_guard_message_response_message_div").children().remove();
        $.post("/sunday/web/guard/message/v2/getMessage",{"groupId":groupId},function (data) {
            $("#sunday_guard_message_response_message_div").append(data);
        })
        //自动滚动到地步
     //   var now = new Date();
        var div = document.getElementById('sunday_guard_message_response_message_div');
      //  div.innerHTML = div.innerHTML + 'time_' + now.getTime() + '<br />';
        div.scrollTop = div.scrollHeight;
    }
    function sunday_guard_message_submit() {
        var oldMessageId = $("#sunday_guard_message_form_oldMessageId").val();
        var groupId = $("#sunday_guard_message_form_groupId").val();
        var content = $("#sunday_guard_message_form_content").val();
        if(content==null||content==""){
            return;
        }
        //layer.load(4, {shade: [0.8, '#393D49']})
        //不要异步操作
        $.ajax({
            type: "post",
            url: "/sunday/web/guard/message/v2/save",
            data: {"oldMessageId": oldMessageId,"groupId":groupId,"content":content},
            async: false,
            success: function (data) {
                //关闭加载
              //  layer.closeAll('loading');
                if (data.code == 0) {
                     $("#sunday_guard_message_form_content").val("");
                    sunday_guard_message_getMessage(groupId);
                    sunday_guard_message_search(true);
                } else {
                    layer.msg('发送失败', {
                        icon: 2
                    });
                }
            }
        });
    }


</script>
<script>













    var param_pageNumber = "$!param.pageNumber";
    var param_pageSize = "$!param.pageSize";
    console.log("param_pageNumber="+param_pageNumber+",param_pageSize="+param_pageSize)
    if(param_pageNumber==null|| param_pageNumber==""){
        param_pageNumber=1
    }
    if(param_pageSize==null|| param_pageSize==""){
        param_pageSize=50
    }
    var sunday_guard_message_pageNumber=param_pageNumber;
    var sunday_guard_message_pageSize=param_pageSize;
    var sunday_guard_message_sort="ct";
    var sunday_guard_message_order="DESC";
    var sunday_guard_message_is_loading=false;
    var sunday_guard_message_is_end=false;
    var sunday_guard_message_guardId="";
    //var sunday_guard_message_bunchId=""
    //输入订单号查询
    function sunday_guard_message_search(isPageNumber){

        sunday_guard_message_guardId = $("#sunday_guard_message_search_form_guardId option:selected").val();
      //  sunday_guard_message_bunchId = $("#sunday_guard_message_search_form_bunchId option:selected").val();
        sunday_guard_message_is_loading=false;
        sunday_guard_message_is_end=false;
        sunday_guard_message_pageNumber =1;
        if(isPageNumber){
            sunday_guard_message_pageNumber =1;
        }
        // sunday_guard_message_pageSize =1;
        sunday_guard_message_getData(true);
    }
    function sunday_guard_message_search_page(pageNumber,pageSize,maxPageNumber){
        if(maxPageNumber!=null&&maxPageNumber!=""&&typeof(maxPageNumber)!="undefined"){
            if(parseInt(pageNumber)>parseInt(maxPageNumber)){
                return;
            }
        }
        sunday_guard_message_pageNumber =pageNumber;
        sunday_guard_message_pageSize =pageSize==null?sunday_guard_message_pageSize:pageSize;
        sunday_guard_message_getData(true);
    }
    function sunday_guard_message_getData(isRemove){

        var t="#sunday_guard_message_div";

        if(sunday_guard_message_is_loading||sunday_guard_message_is_end)return;
        if(isRemove){
            $(t).children().remove();
        }
        sunday_guard_message_is_loading=true;
        //同步请求
        $.ajax({
            type: "POST",
            url: "/sunday/web/guard/message/v2/select",
            data: {
                "pageSize":sunday_guard_message_pageSize,
                "pageNumber":sunday_guard_message_pageNumber,
                "sort":sunday_guard_message_sort,
                "order":sunday_guard_message_order,
                "guardId":sunday_guard_message_guardId,
                //"bunchIds":sunday_guard_message_bunchId
            },
            dataType: "text",
            async:true,
            success: function(result){
                //d layer.closeAll('loading');
                //返回的是html
                if(result==null||result==""||result.length<1){
                    sunday_guard_message_is_end=true;
                }else{
                    if(isRemove && $(t).children()!=null){
                        $(t).children().remove();
                    }
                    $(t).append(result);
                    sunday_guard_message_is_loading=false;
                }
            }
        });
    }
    sunday_guard_message_search(false);


    function sunday_guard_message_delete(id) {
        var msg = "确认要删除这条信息吗?";
        layer.confirm(msg, function (index) {
            layer.load(4, {shade: [0.8, '#393D49']})
            //不要异步操作
            $.ajax({
                type: "post",
                url: "/sunday/web/guard/message/v2/delete",
                data: {"id": id},
                async: false,
                success: function (data) {
                    //关闭加载
                    layer.closeAll('loading');
                    if (data.code == 0) {
                        sunday_guard_message_search(false);
                    } else {
                        layer.msg('删除失败', {
                            icon: 2
                        });
                    }
                }
            });
            //最后手动关闭
            layer.close(index);

        });
    }
</script>