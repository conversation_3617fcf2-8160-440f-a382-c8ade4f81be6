<div class="weekly-plan">
    <div class="search-wrapper clearfix">
        <form action="" method="">
            <div class="com-title-wrapper">
                <div class="title">查询条件</div>
            </div>
            <div class="clearfix">
                <div class="com-titl item">章节名称</div>
                <input type="text" placeholder="" class="com-input-item fl item" id="sunday_course_chapter_search_form_name">
                <button type="button" class="com-button item" onclick="sunday_course_chapter_search(true)">搜索</button>
                <a href="javascript:sunday_course_chapter_save(0,$!courseId);" class="com-add-button item">新增章节</a>
                   
            </div>
        </form>
    </div>
    <!-- 查询 -->

    <div class="list" id="sunday_course_chapter_div">

    </div>
</div>
<script>
    var sunday_course_chapter_pageNumber=1;
    var sunday_course_chapter_pageSize=50;
    var sunday_course_chapter_sort="id";
    var sunday_course_chapter_order="desc";
    var sunday_course_chapter_is_loading=false;
    var sunday_course_chapter_is_end=false;
    // });

    //输入订单号查询
    function sunday_course_chapter_search(isPageNumber){
        sunday_course_chapter_name = $("#sunday_course_chapter_search_form_name").val();
        sunday_course_chapter_is_loading=false;
        sunday_course_chapter_is_end=false;
        sunday_course_chapter_pageNumber =1;
        if(isPageNumber){
            sunday_course_chapter_pageNumber =1;
        }
        // sunday_course_chapter_pageSize =1;
        sunday_course_chapter_getData(true);
    }
    function sunday_course_chapter_search_page(pageNumber,pageSize){
        sunday_course_chapter_pageNumber =pageNumber;
        sunday_course_chapter_pageSize =pageSize==null?sunday_course_chapter_pageSize:pageSize;
        sunday_course_chapter_getData(true);
    }
    function sunday_course_chapter_getData(isRemove){
        var t="#sunday_course_chapter_div";

        if(sunday_course_chapter_is_loading||sunday_course_chapter_is_end)return;
        if(isRemove){
            $(t).children().remove();
        }
        sunday_course_chapter_is_loading=true;
        //layer.load(1, {shade: [0.5, '#fff']});
        //layer.load(4, {shade: [0.8, '#393D49']})

        //同步请求
        $.ajax({
            type: "POST",
            url: "/sunday/web/course/chapter/select",
            data: {
                "pageSize":sunday_course_chapter_pageSize,
                "pageNumber":sunday_course_chapter_pageNumber,
                "sort":sunday_course_chapter_sort,
                "order":sunday_course_chapter_order,
                "name":sunday_course_chapter_name,
                "courseId":"$!courseId"
            },
            dataType: "text",
            async:true,
            success: function(result){
                //返回的是html
                if(result==null||result==""||result.length<1){
                    sunday_course_chapter_is_end=true;
                }else{
                    if(isRemove && $(t).children()!=null){
                        $(t).children().remove();
                    }
                    $(t).append(result);
                    sunday_course_chapter_is_loading=false;
                }
            }
        });
    }
    sunday_course_chapter_search(false);

    function sunday_course_chapter_save(id,courseId) {
        newTab('/sunday/web/course/chapter/input?id='+id+'&courseId='+courseId, '章节新增/编辑');
    }


    function sunday_course_chapter_delete(id){
        var msg="确认要删除这条信息吗?";
        layer.confirm(msg, function(index){
            layer.load(4, {shade: [0.8, '#393D49']})
            //不要异步操作
            $.ajax({
                type : "post",
                url : "/sunday/web/course/chapter/delete",
                data : {"id":id},
                async : false,
                success : function(data){
                    //关闭加载
                    layer.closeAll('loading');
                    if(data.code == 0){
                        sunday_course_chapter_search(false);
                    }else{
                        layer.msg('删除失败', {
                            icon: 2
                        });
                    }
                }
            });
            //最后手动关闭
            layer.close(index);

        });
    }

</script>