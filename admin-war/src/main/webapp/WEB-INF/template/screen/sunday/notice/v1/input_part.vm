
<script type="text/javascript" src="$!{staticRoot}/plug/zTree/js/jquery.ztree.core-3.5.js"></script>
<script type="text/javascript" src="$!{staticRoot}/plug/zTree/js/jquery.ztree.excheck-3.5.js"></script>
<div class="student-list-left" id="sunday_notice_member_choose_div">
    <div class="student-list-titl">请选择成员</div>
    <div class="student-con">


    </div>
</div>
<img src="$!adminRoot/yuhua/admin/img/youjiantou.png" width="19" alt="" class="icon">
<!--已经选中的成员-->
<div class="student-list-right" id="sunday_notice_member_choosed_div">
    <div class="student-list-titl">即将发送的成员</div>
    <div class="student-con">
        <!--已选择的老师或学生-->
        <div class="yuanqu-item">
            <div class="yuanqu-name">杭州测试园幼教</div>
            <div class="ban-list">
                <!--班级--老师-->
                <div class="ban-item clearfix">
                    <div class="ban-name" style="width: 150px">云朵班（老师）：</div>
                    <div class="ban-con clearfix" style="width: 550px">
                        <div class="ban-member">毛老师</div>
                        <div class="ban-member">毛老师</div>
                    </div>
                </div>
                <div class="ban-item clearfix">
                    <div class="ban-name">云朵班（学生）：</div>
                    <div class="ban-con clearfix">
                        <div class="ban-member">毛老师</div>
                        <div class="ban-member">毛老师</div>
                    </div>
                </div>
            </div>
        </div>
        <!--  -->
        <div class="yuanqu-item">
            <div class="yuanqu-name">杭州测试园幼教</div>
            <div class="ban-list">
                <div class="ban-item clearfix">
                    <div class="ban-name">云朵班：</div>
                    <div class="ban-con clearfix">
                        <div class="ban-member">毛老师</div>
                        <div class="ban-member">毛老师</div>
                    </div>
                </div>
                <div class="ban-item clearfix">
                    <div class="ban-name">云朵班：</div>
                    <div class="ban-con clearfix">
                        <div class="ban-member">毛老师</div>
                        <div class="ban-member">毛老师</div>
                    </div>
                </div>
            </div>
        </div>
        <!--  -->
    </div>
</div>

<script>
     $('.release-notice-popup .clos').click(function () {
            $('.release-notice-popup').removeClass('on');
            $('.mask').hide();
        })
        $('.release-notice-popup .return').click(function () {
            $('.add-plan-popup').removeClass('on');
            $('.mask').hide();
        })
        // 关闭弹窗

        $('.student-list-wrapper .yuanqu-top').on('click', '.up-btn', function () {
            if ($(this).hasClass('on')) {
                $(this).removeClass('on');
                $(this).parent().parent().children('.yuanqu-drop-down').stop().slideUp(300);
            } else {
                $(this).addClass('on');

                $(this).parent().parent().children('.yuanqu-drop-down').stop().slideDown(300);
            }
        })
        // 园区学生名单

        $('#preview').click(function () {
            $('.mask').show();
            $('.preview-popup').addClass('on');
        })

        $('.mask').click(function () {
            $('.mask').hide();
            $('.preview-popup').removeClass('on');
        })
        //园区树形结构勾选
        $("#sunday_notice_member_choose_div").find(".com-checkbox-item").click(function () {
          //  var level = 0;
            var isOn = false;
            if($(this).hasClass("on")){
                $(this).removeClass("on");
                isOn=false;
            }else{
                $(this).addClass("on");
                isOn=true;
            }
            console.log($(this).attr("class"));
            //园区
            if($(this).hasClass("hook1")){
                console.log("level=1")
                if(isOn){
                    $(this).parents(".yuanqu1").find(".com-checkbox-item").addClass("on");
                }else{
                    $(this).parents(".yuanqu1").find(".com-checkbox-item").removeClass("on");
                }
            //班级
            }else if($(this).hasClass("hook2")){
                console.log("level=2")
                if(isOn){
                    //向上
                    $(this).parents(".yuanqu1").addClass("on");
                    //向下
                    $(this).find(".com-checkbox-item").addClass("on");
                }else{
                    //向上
                    $(this).parents(".yuanqu1").removeClass("on");
                    //向下
                    $(this).find(".com-checkbox-item").removeClass("on");
                }
             //类型
            }else if($(this).hasClass("hook3")){
                console.log("level=3")
             //教师或学生
            }else if($(this).hasClass("hook4")){
                console.log("level=4")
            }

        })
</script>