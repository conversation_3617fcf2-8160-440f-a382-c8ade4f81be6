#parse("/screen/sunday/operate.vm")
<div class="release-notice">
    <form id="sunday_notice_form" class="form-horizontal" action="/sunday/web/online/manualPushByCourseId" method="post"  enctype="multipart/form-data">
            <input type="hidden" name="courseId" value="" id="sunday_plan_form_courseId_input">
            <input  type="hidden" name="columnId" value="$!columnId"  id ="sunday_plan_form_columnId_input"/>
            <input  type="hidden" name="studentIds"  id ="sunday_plan_form_studentIds_input"/>
            <div class="con">

                <div class="filter-wrapper">

                    <div class="student-list-wrapper clearfix">
                        <div class="student-list-right" style="float: none;margin: auto" id="sunday_notice_teacher_and_student_div">
                            <div class="student-list-titl-wrapper clearfix">
                                <div class="com-titl"><i>*</i>课堂栏目：</div>
                                <div class="com-radio-wrapper fl clearfix" style="display: flex" id="sunday_column_form_div">
                                    #foreach($!column in $!columns)
                                        #if($!column.provideAllStatus==0)
                                            <div class="com-radio-item #if($!columnId == $!column.id) on #end" value="$!column.id">$!column.name</div>
                                        #end

                                    #end
                                </div>
                            </div>
                            <div class="student-list-titl-wrapper clearfix">
                                <div class="com-titl"><i>*</i>更新课程：</div>
                                <div class="com-radio-wrapper fl clearfix" style="display: flex" id="sunday_course_form_div">
                                    <div class='search-selectpicker'>
                                        <select class='' id='course_select'>
                                            #foreach($!course in $!courses)
                                                #if($!course.experienceType == 0 && $!course.sort)
                                                    <option value="$!course.id">$!{course.courseName}(排序:$!{course.sort})</option>
                                                #end

                                            #end
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="student-list-titl-wrapper clearfix">
                                <div class="student-list-titl-addres">即将发送的成员</div>
                                <div class="student-list-titl-num" >已选成员   <i id="sunday_notice_choosed_member">0</i>/<i style="color: #999999" id="sunday_notice_total_member">0</i></div>

                            </div>
                            <div class="student-con">
                                <!--园区-->
                                <div class="yuanqu-item guardDiv">
                                    <div class="ban-list">
                                        <div class="ban-item clearfix bunchDiv"">

                                            <div class="com-checkbox-wrapper ban-con  clearfix" style="float: none;margin: auto;width: 568px">

                                                #foreach($!student in $!students)
                                                    <div class="com-checkbox-item ban-member studentDiv"  style="width: 120px" studentId="$!student.id">$!student.name</div>
                                                #end

                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 发布筛选条件 -->

            <div class="button-wrapper button-wrapper2" style="text-align: center">
                <a href="javascript:yuhua_return('/sunday/web/online/push_index');" class="com-button return">返回</a>
                <button type="submit" class="com-button sunday_notice_add sunday_notice_edit">保存</button>
            </div>
            </div>
    </form>
</div>
    <script>

        //教工、学生勾选
        $("#sunday_notice_teacher_and_student_div").find(".teacherDiv,.studentDiv").click(function () {
                //反选
                if($(this).hasClass("on")){
                    $(this).removeClass("on");
                }else{
                    //勾选
                    $(this).addClass("on");
                }
            sunday_notice_count_choosed();
        })

        $("#sunday_notice_teacher_and_student_div").find(".com-radio-item").click(function () {
            var columnId = $(this).attr("value");
            newTab("/sunday/web/online/manualPushCourse?columnId="+columnId , null);
        })


        /**
         * 勾选对应的教工和学生
         * @param isOverWrite 是否全选教师和学生（true在页面编辑操作的时候，false在编辑进入的时候）
         */
        function  sunday_notice_choose_teacher_and_student(isAllCheck) {



            //获取选中的园区和班级ID
          //  var guardIds  = new Array();
            var guardIds  ="|";
           // var bunchIds =  new Array();
            var bunchIds =  "|";

            $("#sunday_notice_guard_and_bunch_div").find(".guardDiv.on").each(function () {
               // guardIds.push($(this).attr("guardId"));
                guardIds+=$(this).attr("guardId")+"|";
            })
            $("#sunday_notice_guard_and_bunch_div").find(".bunchDiv.on").each(function () {
              //  bunchIds.push($(this).attr("bunchId"));
                bunchIds+=$(this).attr("bunchId")+"|";
            })
         
            if(guardIds==""&&bunchIds==""){
                return;
            }

            //匹配选中的教工和学生
            //1,先隐藏园区和班级
            $("#sunday_notice_teacher_and_student_div").find(".guardDiv").hide();
            $("#sunday_notice_teacher_and_student_div").find(".bunchDiv").hide();
            $("#sunday_notice_teacher_and_student_div").find(".com-checkbox-item").removeClass("on");
            //2,再匹配显示园区和班级
            $("#sunday_notice_teacher_and_student_div").find(".guardDiv").each(function () {

                var guardId = "|"+$(this).attr("guardId")+"|";
              // console.log("guardId="+guardId+"           guardIds="+guardIds+"       是否匹配=   "+guardIds.indexOf(guardId));
                if(guardIds.indexOf(guardId)!=-1){

                    $(this).show();
                }
            });
            $("#sunday_notice_teacher_and_student_div").find(".bunchDiv").each(function () {
                var bunchId = "|"+$(this).attr("bunchId")+"|";
                if(bunchIds.indexOf(bunchId)!=-1){
                    $(this).show();
                    //勾选对应的老师和学生
                    $(this).find(".teacherDiv,.studentDiv").each(function () {
                        if(isAllCheck){
                            $(this).addClass("on");
                        }
                    })
                }
            });

            //统计勾选数量
            sunday_notice_count_choosed();
        }
        //统计已选班级，所有班级、已选成员、所有成员
        function sunday_notice_count_choosed() {
           var total_bunch = $("#sunday_notice_guard_and_bunch_div").find(".bunchDiv").length;
           var choosed_bunch = $("#sunday_notice_guard_and_bunch_div").find(".bunchDiv.on").length;
           var total_member = $("#sunday_notice_teacher_and_student_div").find(".com-checkbox-item").length;
           var choosed_member = $("#sunday_notice_teacher_and_student_div").find(".com-checkbox-item.on").length;
            $("#sunday_notice_total_bunch").text(total_bunch) ;
            $("#sunday_notice_choosed_bunch").text(choosed_bunch) ;
            $("#sunday_notice_total_member").text(total_member) ;
            $("#sunday_notice_choosed_member").text(choosed_member) ;
        }
        //初始化选中数量
       sunday_notice_count_choosed();


        //控制勾选园区
        $("#sunday_notice_form_provinceId,#sunday_notice_form_cityId,#sunday_notice_form_districtId,#sunday_notice_form_guardId").change(function(){
            //匹配园区
            var provinceId=$("#sunday_notice_form_provinceId").val();
            var cityId=$("#sunday_notice_form_cityId").val();
            var districtId=$("#sunday_notice_form_districtId").val();
            var guardId=$("#sunday_notice_form_guardId").val();
          //  console.info("guardId="+guardId)
            /*   console.log("provinceId="+provinceId+"是否选中="+(provinceId==null||provinceId==""));
               console.log("cityId="+cityId+"是否选中="+(cityId==null||cityId==""));
               console.log("districtId="+districtId+"是否选中="+(districtId==null||districtId==""));*/
            var isProvince = false;
            if(provinceId!=null&&provinceId!=""){
                isProvince=true;
            }
            var isCity = false;
            if(cityId!=null&&cityId!=""){
                isCity=true;
            }
            var isDistrict = false;
            if(districtId!=null&&districtId!=""){
                isDistrict=true;
            }
            var isGuard = false;
            if(guardId!=null&&guardId!=""){
                isGuard=true;
            }
          //  console.log("isProvince="+isProvince+","+"isCity="+isCity+","+"isDistrict="+isDistrict+","+"isGuard="+isGuard+",");
            if(!isProvince&&!isCity&&!isDistrict&&!isGuard){
                $("#sunday_notice_guard_and_bunch_div").find(".yuanqu1").show();
            }else{
                $("#sunday_notice_guard_and_bunch_div").find(".yuanqu1").hide();
                $("#sunday_notice_teacher_and_student_div").find(".guardDiv").hide();
                $("#sunday_notice_guard_and_bunch_div").find(".com-checkbox-item").removeClass("on");
                $("#sunday_notice_teacher_and_student_div").find(".com-checkbox-item").removeClass("on");



                $("#sunday_notice_guard_and_bunch_div").find(".yuanqu1").each(function () {
                    var thisProvinceId=$(this).attr("provinceId");
                    var thisCityId=$(this).attr("cityId");
                    var thisDistrictId=$(this).attr("districtId");
                    var thisGuardId=$(this).attr("guardId");
                    // console.log("thisProvinceId="+thisProvinceId+","+"thisCityId="+thisCityId+","+"thisDistrictId="+thisDistrictId+",");
                    //单独选择了园区
                    if(isGuard){
                       // console.info("--thisGuardId="+thisGuardId+",guardId="+guardId+",")
                        if(thisGuardId==guardId){
                            $(this).show();
                        }
                    }else{
                        //选择了省
                        if(isProvince&&!isCity&&!isDistrict){
                            //console.info("--thisProvinceId="+thisProvinceId+",provinceId="+provinceId)
                            if(thisProvinceId==provinceId){

                                $(this).show();
                            }
                        }
                        //选择了省市
                        if(isProvince&&isCity&&!isDistrict){
                            if(thisProvinceId==provinceId&&thisCityId==cityId){
                                $(this).show();
                            }
                        }
                        //选择了省市区
                        if(isProvince&&isCity&&isDistrict){
                            if(thisProvinceId==provinceId&&thisCityId==cityId&&thisDistrictId==districtId){
                                $(this).show();
                            }
                        }
                    }
                });
            }
            //重新统计选中数量
            sunday_notice_count_choosed();
        })

    </script>



<script>

    var validatorMenu = $("#sunday_notice_form").validate({

        submitHandler: function (form) {

            var columnId=$("#sunday_plan_form_columnId_input").val();
            var courseId=$("#course_select").val();


            if(columnId==null || columnId =="" ||courseId==null || courseId ==""){
                layer.msg("请选择栏目或课程", {
                    icon: 2
                });
                return ;
            }

            $("#sunday_plan_form_courseId_input").val(courseId);


            var studentIds = "";

            $("#sunday_notice_teacher_and_student_div").find(".com-checkbox-item.on").each(function () {
                var thisClass = $(this).attr("class");
                 if(thisClass.indexOf("teacherDiv")!=-1){
                    //teacherIds +=$(this).attr("teacherId")+","
                }else if(thisClass.indexOf("studentDiv")!=-1){
                    studentIds +=$(this).attr("studentId")+","
                }
            });
            //console.info("guardIds="+guardIds+",bunchIds="+bunchIds+",teacherIds="+teacherIds+",studentIds="+studentIds)
            if(studentIds == ""){
                layer.msg("请选择要发布的成员", {
                    icon: 2
                });
                return;
            }
            studentIds=","+studentIds;
            $("#sunday_plan_form_studentIds_input").val(studentIds);
            //  return;


            //加遮罩层
            layer.load(4, {shade: [0.8, '#393D49']})


            $(form).ajaxSubmit({
                //你表单提交就要切换页面，ajax加载又是异步的。两边同时进行，
                //当ajax加载在切换页面之前完成，就会执行回调，反之不会执行
                //加同步提交数据
                async: false,
                dataType: "json",
                success: function (data) {
                    //关闭加载
                    layer.closeAll('loading');
                    if (data.code == 0) {
                        newTab('/sunday/web/online/push_index',null)
                    } else {
                        layer.msg(data.message, {icon: 2});
                    }
                }
            });
        }
    });
</script>
<script>
    //字符串转日期
    function stringToDate (dateStr,separator){
        dateStr = dateStr.replace(/-/g,"/");
        var date = new Date(dateStr);
        /*if(!separator){
            separator="-";
        }
        }
        var dateArr = dateStr.split(separator);
        var year = parseInt(dateArr[0]);
        var month;
        //处理月份为04这样的情况
        if(dateArr[1].indexOf("0") == 0){
            month = parseInt(dateArr[1].substring(1));
        }else{
            month = parseInt(dateArr[1]);
        }
        var day = parseInt(dateArr[2]);
        var date = new Date(year,month -1,day);*/
        return date;
    }


</script>