<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">

    <meta name="apple-mobile-web-app-capable" content="yes">
    <title>教学周计划</title>
##    <link rel="stylesheet" type="text/css" href="$!adminRoot/yuhua/mobile/css/style2.css">
    <style>
        @charset "utf-8";
        td{
            font-size: 0.10rem;
            text-align: left;
        }
        tr>td:first-child{
            text-align: center;
        }
        .plan_text{

        }
        * {
            margin: 0;
            padding: 0;
        }

        body {
            font-family: -apple-system, Helvetica, sans-serif;
            background: #fbfbfb;
        }

        li {
            list-style: none;
        }

        h1,
        h2,
        h3,
        h4,
        h5,
        h6,
        strong,
        b {
            font-size: 12px;
            font-weight: normal;
        }

        em,
        i {
            font-style: normal;
        }

        pre {
            white-space: pre-wrap;
            white-space: -moz-pre-wrap;
            white-space: -pre-wrap;
            white-space: -o-pre-wrap;
            word-wrap: break-word;
        }

        a {
            text-decoration: none;
        }

        a:focus {
            outline: none;
        }

        .clearfix:after {
            content: "";
            display: block;
            clear: both;
        }

        .clearfix {
            zoom: 1;
        }

        .fl {
            float: left;
        }

        .fr {
            float: right;
        }

        input {
            border: none;
            background: none;
            outline: none;
        }

        select {
            border: none;
            outline: none;
            appearance: none;
            -moz-appearance: none;
            -webkit-appearance: none;
        }

        select::-ms-expand {
            display: none;
        }

        textarea {
            resize: none;
            border: none;
            outline: none;
            background: none;
        }

        input[type=button],
        input[type=submit],
        input[type=file],
        button {
            cursor: pointer;
            -webkit-appearance: none;
            border-radius: 0;
        }

        button {
            border: none;
            outline: none;
            border-radius: 0;
        }

        img {
            border: none;
        }

        .border-bottom:after {
            content: " ";
            position: absolute;
            left: 0;
            bottom: 0;
            right: 0;
            border-top: 1px solid #f3f3f3;
            -webkit-transform: scaleY(0.5);
            transform: scaleY(0.5);
        }

        .evaluation-results .evaluation-img {
            padding-top: .29rem;
            width: 100%;
            height: 7.5rem;
            box-sizing: border-box;
            background: url(../img/evaluation_bg.png) no-repeat center center;
            background-size: 100% 7.5rem;
        }

        .evaluation-results .evaluation-img .evaluation-item {
            display: -webkit-flex;
            display: flex;
            padding: .31rem 0 .3rem .48rem;
        }

        .evaluation-results .evaluation-item .title {
            -webkit-flex: 0 0 1.4rem;
            flex: 0 0 1.4rem;
            margin: .72rem .36rem .02rem 0;
            width: 1.4rem;
            line-height: .36rem;
            text-align: center;
            font-size: .28rem;
            color: #fff;
        }

        .evaluation-results .evaluation-item .con {
            position: relative;
            display: -webkit-flex;
            display: flex;
            -webkit-flex: 0 0 4.58rem;
            flex: 0 0 4.58rem;
            margin-top: .79rem;
            width: 4.58rem;
            height: .17rem;
            border-radius: .09rem;
        }

        .evaluation-results .evaluation-item .grade {
            position: relative;
            height: .17rem;
        }

        .evaluation-results .evaluation-item .grade .num {
            position: absolute;
            right: 0;
            top: -0.38rem;
            margin-right: -25%;
            line-height: .28rem;
            font-size: .26rem;
            color: #fff;
        }

        .evaluation-results .evaluation-item .grade .titl {
            position: absolute;
            top: .36rem;
            left: 0;
            margin-right: -25%;
            width: 100%;
            line-height: .32rem;
            font-size: .3rem;
            color: #9abef6;
        }

        .evaluation-results .evaluation-item .low {
            border-top-left-radius: .09rem;
            border-bottom-left-radius: .09rem;
            background: #ff543d;
        }

        .evaluation-results .evaluation-item .middle {
            background: #ff8c40;
        }

        .evaluation-results .evaluation-item .middle .titl {
            text-align: center;
        }

        .evaluation-results .evaluation-item .high {
            border-top-right-radius: .09rem;
            border-bottom-right-radius: .09rem;
            background: #93d68a;
        }

        .evaluation-results .evaluation-item .high .titl {
            text-align: right;
        }

        .evaluation-results .evaluation-item .con .progress {
            position: absolute;
            top: 0;
            z-index: 1;
            width: .17rem;
            height: .17rem;
            box-sizing: border-box;
            border: 2px solid #4679e8;
            border-radius: 50%;
            background: #fff;
        }

        .evaluation-results .evaluation-item .con .progress-num {
            position: absolute;
            left: 50%;
            -webkit-transform: translateX(-50%);
            transform: translateX(-50%);
            top: -0.79rem;
            width: .79rem;
            height: .52rem;
            line-height: .52rem;
            text-align: center;
            font-size: .26rem;
            border-radius: .1rem;
            color: #3c71e7;
            background: #fff;
        }

        .evaluation-results .evaluation-item .con .progress-num:before {
            position: absolute;
            left: 0;
            right: 0;
            bottom: -0.08rem;
            margin: auto;
            content: " ";
            width: .16rem;
            height: .16rem;
            -webkit-transform: rotate(45deg);
            transform: rotate(45deg);
            background: #fff;
        }

        .evaluation-results .comment {
            padding: .26rem .1rem .35rem .16rem;
            margin: .25rem .28rem 0;
            border-radius: .08rem;
            -webkit-box-shadow: 0 0 .18rem .12rem #f4f4f4;
            box-shadow: 0 0 .18rem .12rem #f4f4f4;
            overflow: hidden;
            background: #fff;
        }

        .evaluation-results .comment .title {
            line-height: .4rem;
            font-size: .32rem;
            font-weight: bold;
            color: #222;
        }

        .evaluation-results .comment .con {
            margin-top: .2rem;
            line-height: .5rem;
            font-size: .28rem;
            color: #666;
        }

        .evaluation-results .text {
            padding: .41rem .1rem .35rem .16rem;
            margin: .25rem .28rem;
            border-radius: .08rem;
            -webkit-box-shadow: 0 0 .18rem .12rem #f4f4f4;
            box-shadow: 0 0 .18rem .12rem #f4f4f4;
            overflow: hidden;
            background: #fff;
        }

        .evaluation-results .text .title {
            line-height: .4rem;
            font-size: .32rem;
            font-weight: bold;
            color: #222;
        }

        .evaluation-results .text .con {
            margin-top: .27rem;
            line-height: .5rem;
            font-size: .28rem;
            color: #666;
        }

        .evaluation-results .text .text-item {
            margin-top: .36rem;
        }

        .evaluation-results .text .text-item:first-child {
            margin-top: 0;
        }

        .evaluation-report .report-img-wrapper {
            position: relative;
            margin: 1.3rem auto 1.2rem;
            width: 2.77rem;
            height: 2.4rem;
        }

        #reportImg {
            position: absolute;
            left: 50%;
            top: 50%;
            margin-left: -1.85rem;
            margin-top: -1.9rem;
            -webkit-transform: rotate(-30deg);
            transform: rotate(-30deg);
        }

        .evaluation-report .report-img-wrapper .name-item {
            position: absolute;
            line-height: .32rem;
            font-size: .28rem;
            color: #666;
        }

        .evaluation-report .evaluator {
            display: -webkit-flex;
            display: flex;
            padding: 0 0 0 .45rem;
        }

        .evaluation-report .evaluator .avatar {
            -webkit-flex: 0 0 .9rem;
            flex: 0 0 .9rem;
            display: block;
            width: .9rem;
            height: .9rem;
            border-radius: 50%;
            background: #5ccfff;
        }

        .evaluation-report .evaluator .text {
            -webkit-flex: 1;
            flex: 1;
            margin-left: .19rem;
        }

        .evaluation-report .evaluator .name {
            line-height: .4rem;
            font-size: .28rem;
            font-weight: bold;
            color: #222;
        }

        .evaluation-report .evaluator .name i {
            margin-left: .4rem;
        }

        .evaluation-report .evaluator .con {
            display: -webkit-flex;
            display: flex;
            margin-top: .19rem;
        }

        .evaluation-report .evaluator .titl {
            line-height: .3rem;
            font-size: .28rem;
            color: #7c7c7c;
        }

        .evaluation-report .evaluator .line {
            margin-top: .04rem;
            width: 2.61rem;
            height: .21rem;
            border-radius: .11rem;
            background: #fccb37;
        }

        .evaluation-report .evaluator .grade {
            margin-left: .24rem;
            line-height: .3rem;
            font-size: .26rem;
            color: #7c7c7c;
        }

        .evaluation-report .evaluator .grade img {
            display: block;
            margin-top: .03rem;
            height: .23rem;
        }

        .evaluation-report .ratting {
            padding: 0 .26rem 0 .35rem;
            margin-top: .66rem;
        }

        .evaluation-report .ratting .title {
            line-height: .42rem;
            font-size: .36rem;
            font-weight: bold;
            color: #222;
        }

        .evaluation-report .ratting .con {
            margin-top: .22rem;
            line-height: .48rem;
            font-size: .28rem;
            color: #5b5b5b;
        }

        .evaluation .evaluation-item {
            display: -webkit-flex;
            display: flex;
            padding: .58rem 0 .52rem .23rem;
            margin: 0 .26rem .22rem;
            border-radius: .08rem;
            overflow: hidden;
            background: #f6f6f6;
        }

        .evaluation .evaluation-item:first-child {
            margin: .43rem .26rem .22rem;
        }

        .evaluation .evaluation-item .icon {
            -webkit-flex: 0 0 1.3rem;
            flex: 0 0 1.3rem;
            display: block;
            width: 1.3rem;
            height: 1.3rem;
        }

        .evaluation .evaluation-item .text {
            -webkit-flex: 1;
            flex: 1;
            display: table;
            margin-left: .42rem;
            height: 1.3rem;
        }

        .evaluation .evaluation-item .text span {
            display: table-cell;
            vertical-align: middle;
            line-height: .42rem;
            font-size: .36rem;
            color: #696969;
        }

        .parent-curriculum .introduce {
            background: #fff;
        }

        .parent-curriculum .introduce .title {
            padding: .38rem .26rem 0;
            line-height: .93rem;
            font-size: .36rem;
            border-bottom: 1px dashed #e5e5e5;
            color: #222;
        }

        .parent-curriculum .introduce .content {
            display: none;
            line-height: .42rem;
            font-size: .28rem;
            color: #666;
        }

        .parent-curriculum .introduce .content.on {
            display: block;
        }

        .parent-curriculum .introduce .content img {
            display: block;
            width: 100%;
        }

        .parent-curriculum .introduce .con {
            padding: 0 .26rem;
            margin-top: .26rem;
            line-height: .42rem;
            font-size: .28rem;
            color: #666;
        }

        .parent-curriculum .titl {
            padding: 0 .26rem .14rem;
            margin-top: .44rem;
            line-height: .38rem;
            font-size: .3rem;
            border-bottom: 1px dashed #e5e5e5;
            color: #11c5e4;
        }

        .parent-curriculum .teacher .con {
            display: -webkit-flex;
            display: flex;
            padding: 0 .26rem;
            margin-top: .4rem;
        }

        .parent-curriculum .introduce .teacher .avatar {
            -webkit-flex: 0 0 2rem;
            flex: 0 0 2rem;
            display: block;
            width: 2rem;
            height: 2rem;
            border-radius: .09rem;
            background: #f6bd47;
        }

        .parent-curriculum .teacher .text {
            -webkit-flex: 1;
            flex: 1;
            margin-left: .2rem;
        }

        .parent-curriculum .teacher .name {
            margin-top: .1rem;
            line-height: .36rem;
            font-size: .3rem;
            color: #222;
        }

        .parent-curriculum .teacher .des {
            margin-top: .23rem;
            line-height: .4rem;
            font-size: .28rem;
            color: #666;
        }

        .parent-curriculum .introduce .close {
            padding: .21rem .36rem .15rem 0;
            line-height: .32rem;
            text-align: right;
            font-size: .26rem;
            color: #666;
        }

        .parent-curriculum .chapter {
            margin-top: .16rem;
            background: #fff;
        }

        .parent-curriculum .chapter .title {
            padding: .38rem .26rem 0;
            line-height: .93rem;
            font-size: .36rem;
            border-bottom: 1px dashed #e5e5e5;
            color: #222;
        }

        .parent-curriculum .chapter .content {
            padding: .28rem 0 .24rem .26rem;
            overflow: hidden;
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
            font-size: 0;
            white-space: nowrap;
        }

        .parent-curriculum .chapter .chapter-item {
            display: inline-block;
            vertical-align: top;
            margin-right: .15rem;
            width: 3.12rem;
        }

        .parent-curriculum .chapter .chapter-item .img {
            display: block;
            width: 100%;
        }

        .parent-curriculum .chapter .chapter-item .name {
            margin-top: .1rem;
            max-height: .8rem;
            line-height: .4rem;
            font-size: .26rem;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            color: #222;
            white-space: normal;
        }

        .parent-offspring-details {
            padding-bottom: .48rem;
            background: #fff;
        }

        .banner img {
            display: block;
            width: 100%;
        }

        .banner .swiper-pagination {
            bottom: .1rem;
            font-size: 0;
        }

        .banner .swiper-pagination span {
            width: .12rem;
            height: .12rem;
            border-radius: 50%;
            background: rgba(0, 0, 0, 0.5);
        }

        .banner .swiper-pagination .swiper-pagination-bullet-active {
            background: #fff;
        }

        .swiper-container-horizontal>.swiper-pagination-bullets .swiper-pagination-bullet {
            margin: 0 .05rem;
        }

        .parent-offspring-details img {
            display: block;
            width: 100%;
        }

        .parent-offspring-details .title {
            padding: .51rem .26rem 0;
            line-height: .42rem;
            font-size: .36rem;
            color: #0f0f0f;
        }

        .parent-offspring-details .content {
            padding: 0 .26rem;
            margin-top: .37rem;
            line-height: .46rem;
            font-size: .26rem;
            color: #5b5b5b;
        }

        .parent-offspring-details .curriculum-para {
            padding: 0 .26rem;
            margin-top: .49rem;
        }

        .parent-offspring-details .curriculum-para .para-item {
            display: -webkit-flex;
            display: flex;
        }

        .parent-offspring-details .curriculum-para .para-item .titl {
            -webkit-flex: 0 0 2.66rem;
            flex: 0 0 2.66rem;
            width: 2.66rem;
            line-height: .58rem;
            font-size: .3rem;
            color: #0f0f0f;
        }

        .parent-offspring-details .curriculum-para .para-item .con {
            -webkit-flex: 1;
            flex: 1;
            padding: .13rem 0;
            line-height: .32rem;
            font-size: .26rem;
            color: #5c5c5c;
        }

        .parent-offspring-details .curriculum-para .para-item .star {
            display: -webkit-flex;
            display: flex;
        }

        .parent-offspring-details .curriculum-para .para-item .star-item {
            margin-right: .1rem;
            width: .24rem;
            height: .58rem;
            background: url(../img/star_g.png) no-repeat center center;
            background-size: .23rem;
        }

        .parent-offspring-details .curriculum-para .para-item .star-item.on {
            background: url(../img/star_y.png) no-repeat center center;
            background-size: .23rem;
        }

        .parent-offspring-details .section {
            padding: 0 .26rem;
            margin-top: .19rem;
        }

        .parent-offspring-details .section .titl {
            padding-left: .21rem;
            line-height: .32rem;
            font-size: .3rem;
            border-left: .1rem solid #11c5e4;
            color: #0f0f0f;
        }

        .parent-offspring-details .section .con {
            padding-left: .31rem;
            margin-top: .18rem;
            line-height: .46rem;
            font-size: .26rem;
            color: #5c5c5c;
        }

        .baby-recipe .data-wrapper {
            text-align: center;
            font-size: 0;
            background: #fddc59;
        }

        .baby-recipe .data-wrapper .btn {
            display: inline-block;
            vertical-align: top;
            padding: .43rem 0 .34rem;
            width: 1.1rem;
            text-align: center;
            font-size: 0;
        }

        .baby-recipe .data-wrapper .btn img {
            display: inline-block;
            vertical-align: middle;
            width: .12rem;
            height: .22rem;
        }

        .baby-recipe .data-wrapper .con {
            display: inline-block;
            vertical-align: middle;
            padding: .37rem 0 .28rem;
            margin: 0 .44rem;
            line-height: .34rem;
            font-size: .3rem;
            color: #666;
        }

        .baby-recipe .content {
            padding: .4rem 0 .3rem .25rem;
        }

        .baby-recipe .recipe-item {
            position: relative;
        }

        .baby-recipe .recipe-item:before {
            position: absolute;
            left: .62rem;
            top: 0;
            bottom: 0;
            z-index: -1;
            content: " ";
            border-left: 1px solid #e5e5e5;
        }

        .baby-recipe .recipe-item:last-child:before {
            display: none;
        }

        .baby-recipe .recipe-item .title-wrapper {
            display: -webkit-flex;
            display: flex;
        }

        .baby-recipe .recipe-item .icon-wrapper {
            -webkit-flex: 0 0 1.24rem;
            flex: 0 0 1.24rem;
        }

        .baby-recipe .recipe-item .icon-wrapper .icon {
            display: block;
            width: 1.24rem;
            height: 1.24rem;
            border-radius: 50%;
            -webkit-box-shadow: 0 0 .1rem .06rem #f7f7f7;
            box-shadow: 0 0 .1rem .06rem #f7f7f7;
        }

        .baby-recipe .recipe-item .title {
            -webkit-flex: 1;
            flex: 1;
            margin-left: .23rem;
            line-height: 1.24rem;
            font-size: .36rem;
            font-weight: bold;
            color: #222;
        }

        .baby-recipe .recipe-item .con {
            display: -webkit-flex;
            display: flex;
            padding: 0 0 .24rem 1.54rem;
        }

        .baby-recipe .recipe-item .food-item {
            margin-right: .34rem;
            width: 1.92rem;
        }

        .baby-recipe .recipe-item .food-item .name {
            margin-bottom: .2rem;
            line-height: .32rem;
            font-size: .3rem;
            color: #666;
        }

        .baby-recipe .recipe-item .food-item img {
            display: block;
            width: 100%;
        }

        .week-english .title-item {
            position: relative;
            display: -webkit-flex;
            display: flex;
            padding: .33rem .26rem .28rem;
            line-height: .38rem;
            background: #fff;
        }

        .week-english .title-item .titl {
            -webkit-flex: 0 0 1.5rem;
            flex: 0 0 1.5rem;
            width: 1.5rem;
            font-size: .3rem;
            color: #666;
        }

        .week-english .title-item .con {
            -webkit-flex: 1;
            flex: 1;
            font-size: .3rem;
            color: #222;
        }

        .week-english .content {
            padding: .53rem .26rem;
            margin-top: .17rem;
            background: #fff;
        }

        .week-english .english-item {
            padding: .2rem .09rem .17rem;
            margin-bottom: .42rem;
            border-radius: .1rem;
            -webkit-box-shadow: 0 .04rem .2rem .1rem #eee;
            box-shadow: 0 .04rem .2rem .1rem #eee;
            overflow: hidden;
            background: #fff;
        }

        .week-english .english-item:last-child {
            margin-bottom: 0;
        }

        .week-english .english-item .title {
            padding-left: .19rem;
            line-height: .39rem;
            font-size: .34rem;
            border-left: .18rem solid #0ec5e6;
            color: #0ec5e6;
        }

        .week-english .english-item .con {
            padding-left: .37rem;
            margin-top: .19rem;
            line-height: .48rem;
            font-size: .3rem;
            color: #222;
        }

        .daily-plan {
            padding: .53rem .26rem 0 .72rem;
        }

        .daily-plan .plan-item {
            position: relative;
            padding: 0 0 .5rem .6rem;
        }

        .daily-plan .plan-item:before {
            position: absolute;
            left: -0.115rem;
            top: .05rem;
            content: "";
            width: .23rem;
            height: .23rem;
            border-radius: 50%;
            background: #0ec5e6;
        }

        .daily-plan .plan-item:after {
            position: absolute;
            left: 0;
            top: .05rem;
            bottom: -0.05rem;
            content: "";
            border-left: 1px solid #0ec5e6;
        }

        .daily-plan .plan-item .time {
            line-height: .34rem;
            font-size: .34rem;
            color: #222;
        }

        .daily-plan .plan-item .con {
            margin-top: .12rem;
            line-height: .38rem;
            font-size: .3rem;
            color: #727272;
        }

        .daily-plan .plan-item:last-child:after {
            display: none;
        }

        .week-plan .title-item:last-child .titl {
            -webkit-flex: 0 0 1.6rem;
            flex: 0 0 1.6rem;
            width: 1.6rem;
        }

        .week-plan .title-item:last-child .con {
            text-align: center;
        }

        .week-plan .content {
            padding: .26rem .26rem 0;
        }

        .week-plan .content img {
            display: block;
            width: 100%;
        }

        .school-introduction .introduc {
            position: relative;
            padding: .38rem .26rem 0;
            background: #fff;
        }

        .school-introduction .introduc:after {
            left: .26rem;
            right: .26rem;
        }

        .school-introduction .introduc .title {
            line-height: .44rem;
            font-size: .36rem;
            color: #222;
        }

        .school-introduction .introduc .con {
            margin-top: .28rem;
            line-height: .45rem;
            font-size: .26rem;
            color: #666;
        }

        .school-introduction .introduc .con.on {
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 4;
            -webkit-box-orient: vertical;
        }

        .school-introduction .introduc .all {
            padding: .15rem 0;
            line-height: .3rem;
            text-align: right;
            font-size: .24rem;
            text-decoration: underline;
            color: #999;
        }

        .school-introduction .teacher-introduc {
            padding: .32rem 0 0 .06rem;
        }

        .school-introduction .teacher-introduc .title {
            padding-left: .2rem;
            line-height: .44rem;
            font-size: .36rem;
            color: #222;
        }

        .school-introduction .teacher-introduc .con {
            display: -webkit-flex;
            display: flex;
            -webkit-flex-wrap: wrap;
            flex-wrap: wrap;
            margin-top: .36rem;
        }

        .school-introduction .teacher-introduc .teacher-item {
            margin: 0 0 .4rem .2rem;
            width: 2.19rem;
        }

        .school-introduction .teacher-introduc .teacher-item img {
            display: block;
            width: 100%;
        }

        .school-introduction .teacher-introduc .teacher-item .name {
            margin-top: .11rem;
            line-height: .36rem;
            font-size: .28rem;
            color: #222;
        }

        .school-introduction .teacher-introduc .teacher-item .lecture {
            margin-top: .03rem;
        }

        .baby-works .work-item {
            padding: .34rem .6rem .23rem;
            margin-top: .16rem;
            background: #fff;
        }

        .baby-works .work-item:first-child {
            margin-top: 0;
        }

        .baby-works .work-item .name {
            line-height: .4rem;
            font-size: .32rem;
            color: #222;
        }

        .baby-works .work-item .content {
            margin-top: .14rem;
        }

        .baby-works .work-item .work-img {
            display: block;
            width: 100%;
        }

        .baby-works .work-item .curriculum-time {
            display: -webkit-flex;
            display: flex;
            -webkit-justify-content: space-between;
            justify-content: space-between;
            margin-top: .24rem;
        }

        .baby-works .work-item .curriculum {
            padding: 0 .27rem;
            box-sizing: border-box;
            line-height: .4rem;
            font-size: .22rem;
            border: 1px solid #4e7893;
            border-radius: .2rem;
            color: #4e7893;
        }

        .baby-works .work-item .time {
            line-height: .4rem;
            font-size: .26rem;
            color: #222;
        }

        .baby-works .work-item .ratting {
            margin-top: .21rem;
        }

        .baby-works .work-item .ratting .titl {
            line-height: .38rem;
            font-size: .3rem;
            font-weight: bold;
            color: #222;
        }

        .baby-works .work-item .ratting .con {
            margin-top: .09rem;
            line-height: .52rem;
            font-size: .3rem;
            color: #666;
        }

        .notice .notice-item {
            display: block;
            padding: .32rem .26rem .19rem;
            margin-bottom: .16rem;
            background: #fff;
        }

        .notice .notice-item img {
            width: 100%;
            border-radius: .06rem;
            box-shadow: 0 .07rem .14rem .07rem #ececec;
        }

        .notice .notice-item .titl-wrapper {
            display: -webkit-flex;
            display: flex;
            margin-top: .23rem;
            line-height: .4rem;
        }

        .notice .notice-item .titl-wrapper .titl {
            -webkit-flex: 1;
            flex: 1;
            font-size: .32rem;
            color: #666;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .notice .notice-item .titl-wrapper .more {
            -webkit-flex: 0 0 1.7rem;
            flex: 0 0 1.7rem;
            padding-right: .36rem;
            width: 1.7rem;
            box-sizing: border-box;
            text-align: right;
            font-size: .3rem;
            background: url(../img/next02.png) no-repeat right center;
            background-size: .12rem .22rem;
            color: #666;
        }

        .notice .notice-item .time {
            padding-left: .08rem;
            margin-top: .26rem;
            line-height: .32rem;
            font-size: .26rem;
            color: #bbb;
        }

        .notice-details {
            padding: .19rem .26rem;
            border-top: .16rem solid #f3f3f3;
            background: #fff;
        }

        .notice-details .title-wrapper {
            position: relative;
            padding-bottom: .31rem;
        }

        .notice-details .title-wrapper .title {
            line-height: .5rem;
            font-size: .34rem;
            font-weight: bold;
            color: #222;
        }

        .notice-details .title-wrapper .time {
            margin-top: .2rem;
            line-height: .28rem;
            font-size: .22rem;
            color: #666;
        }

        .notice-details .title-wrapper i {
            margin-left: .16rem;
        }

        .notice-details .content {
            margin-top: .29rem;
            line-height: .49rem;
            font-size: .28rem;
            color: #666;
        }

        .notice-details .content img {
            display: block;
            width: 100%;
        }

        .week-plan2 {
            position: relative;
            margin-top: .23rem;
        }

        .week-plan2:after {
            bottom: auto;
            top: 0;
        }

        .week-plan2 .plan-item {
            position: relative;
            display: -webkit-flex;
            display: flex;
            -webkit-justify-content: center;
            justify-content: center;
            padding: .33rem 0 .32rem .26rem;
            background: #fff;
        }

        .week-plan2 .plan-item .text {
            line-height: .38rem;
            font-size: .3rem;
            color: #222;
        }

        .week-plan2 .plan-item .next {
            -webkit-flex: 0 0 .74rem;
            flex: 0 0 .74rem;
            width: .74rem;
            background: url(../img/next02.png) no-repeat center center;
            background-size: .12rem .22rem;
        }

        .baby-roster .class {
            position: relative;
            display: -webkit-flex;
            display: flex;
            padding: .15rem 0 .18rem .34rem;
            background: #fff;
        }

        .baby-roster .class .titl {
            -webkit-flex: 0 0 1rem;
            flex: 0 0 1rem;
            width: 1rem;
            line-height: .7rem;
            font-size: .3rem;
            color: #222;
        }

        .baby-roster .class .con {
            -webkit-flex: 1;
            flex: 1;
        }

        .baby-roster .class select {
            display: block;
            padding-left: .21rem;
            margin: 0 auto;
            width: 4.49rem;
            height: .7rem;
            line-height: .7rem;
            font-size: .3rem;
            box-sizing: border-box;
            border: 1px solid #bfbfbf;
            border-radius: .08rem;
            background: url(../img/up.png) no-repeat 4.11rem center;
            background-size: .14rem .1rem;
            color: #222;
        }

        .baby-roster .plan-item {
            padding: .33rem 0 .32rem .34rem;
        }

        .plan-con {
            position: relative;
            background: #ffffff;
        }

        .plan-con .title-wrapper {
            font-size: 0;
            text-align: center;
        }

        .plan-con .title-wrapper .title {
            display: inline-block;
            vertical-align: top;
            line-height: .72rem;
            font-size: .48rem;
            letter-spacing: .03rem;
            color: #07b2e8;
        }

        .plan-con .title-wrapper .icon {
            display: inline-block;
            vertical-align: top;
            padding: 0 .08rem;
            margin-left: .2rem;
            width: 1.2rem;
            box-sizing: border-box;
            border-left: 1px solid #07b2e8;
            border-right: 1px solid #07b2e8;
        }

        .plan-con .text {
            margin-top: .39rem;
            line-height: .26rem;
            font-size: .2rem;
            color: #07b2e8;
        }

        .plan-con .text .ml {
            margin-left: .05rem;
        }

        .plan-con .theme {
            margin-top: .14rem;
            line-height: .26rem;
            font-size: .18rem;
            color: #07b2e8;
        }

        .plan-con table {
            white-space: pre-line;
            margin-top: .08rem;
            border-collapse: collapse;
            font-size: .12rem;
            text-align: center;
            word-break: break-all;
            border: 2px solid #488c3c;
            color: #713c2f;
        }

        .plan-con table tr:last-child td:first-child:after {
            content: "";
            position: absolute;
            left: -3px;
            bottom: -3px;
            width: 4px;
            height: 4px;
            border-radius: 50%;
        }

        .plan-con table tr:last-child td:last-child:before {
            content: "";
            position: absolute;
            right: -3px;
            bottom: -3px;
            width: 4px;
            height: 4px;
            border-radius: 50%;
        }

        .plan-con table th:first-child:after {
            content: "";
            position: absolute;
            left: -3px;
            top: -3px;
            width: 4px;
            height: 4px;
            border-radius: 50%;
        }

        .plan-con2 table tbody tr th:first-child {
            background: #cae7b9b3;
        }

        .plan-con2 table tbody tr td:first-child {
            background: #cae7b9b3;
        }

        .plan-con table th:last-child:before {
            content: "";
            position: absolute;
            right: -3px;
            top: -3px;
            width: 4px;
            height: 4px;
            border-radius: 50%;
        }

        .plan-con table th,
        td {
            position: relative;
            padding: .08rem 0;
            height: .24rem;
            border: 1px solid #659442;
            background: #fff;
        }

        .plan-con table th {
            height: .4rem;
            font-weight: normal;
        }

        .plan-con .bottom-text {
            display: -webkit-flex;
            display: flex;
            -webkit-justify-content: space-between;
            justify-content: space-between;
            margin-top: .14rem;
            line-height: .18rem;
            font-size: .12rem;
            color: #333333;
            height: 0.2rem;
            background: #66ab46;
        }

        .plan-con .bottom-text .left {
            letter-spacing: .15rem;
        }

        .plan-con .bottom-text i {
            margin: 0 .2rem;
        }

        .plan-con .bear {
            position: absolute;
            right: .25rem;
            bottom: 0;
            display: block;
            width: 75%!important;
        }

        .plan-con2 .title-wrapper2 {
            display: -webkit-flex;
            display: flex;
            -webkit-justify-content: center;
            justify-content: center;
            padding-top: .24rem;
        }

        .plan-con2 .title-wrapper2 .title-en {
            line-height: .4rem;
            font-size: .38rem;
            letter-spacing: .05rem;
            color: #07b2e8;
        }

        .plan-con2 .title-wrapper2 .title {
            line-height: .5rem;
            font-size: .34rem;
            letter-spacing: .07rem;
            color: #07b2e8;
        }

        .plan-con2 .title-wrapper2 .title i {
            font-size: .26rem;
            letter-spacing: none;
        }

        .plan-con2 .title-wrapper2 .des {
            margin-top: .08rem;
            line-height: .24rem;
            font-size: .25rem;
            font-weight: 400;
            text-align: center;
            color: #333333;
        }

        .plan-con2 .title-wrapper2 .icon-wrapper {
            margin-top: .06rem;
            width: 2.15rem;
            height: 1.04rem;
            border-left: 1px solid #07b2e8;
            border-right: 1px solid #07b2e8;
        }

        .plan-con2 .title-wrapper2 .icon-wrapper .icon {
            margin: 0 auto;
            width: 1.3rem;
        }

        .plan-con2 .text-wrapper {
            display: -webkit-flex;
            display: flex;
            -webkit-justify-content: space-between;
            justify-content: space-between;
            margin-top: .28rem;
            line-height: .2rem;
            font-size: .16rem;
            color: #333333;
        }

        .plan-con2 table {
            color: #1f1f1f;
        }

        .plan-con2 table th {
            font-size: .14rem;
        }

        .plan-con2 .bottom-text {
            letter-spacing: .21rem;
        }

        .plan-con3 .des span {
            padding: 0 .1rem;
            text-decoration: underline;
        }

        .plan-con3 .title-wrapper2 .icon-wrapper {
            border-right: none;
        }

        .baby-recipe .banner .swiper-slide {
            border-radius: .05rem;
            overflow: hidden;
        }

        .baby-recipe .banner .swiper-pagination {
            bottom: .11rem;
        }

        .baby-recipe .banner .swiper-pagination .swiper-pagination-bullet {
            width: 8px;
            height: 8px;
            border: 1px solid #fff;
            box-sizing: border-box;
            background: none;
        }

        .baby-recipe .banner .swiper-pagination .swiper-pagination-bullet-active {
            background: #fff;
        }

        .baby-recipe .banner .time {
            position: absolute;
            left: 0;
            right: 0;
            top: 0;
            line-height: .8rem;
            text-align: center;
            font-size: .28rem;
            color: #fff;
            background: rgba(129, 129, 129, 0.6);
        }

        .baby-recipe .banner .btn {
            position: absolute;
            top: 0;
            width: .9rem;
            height: .8rem;
            z-index: 10;
        }

        .baby-recipe .banner .btn:after {
            content: "";
            position: absolute;
            left: 0;
            top: 0;
            right: 0;
            bottom: 0;
            margin: auto;
            width: 0;
            height: 0;
            border-top: .09rem solid transparent;
            border-bottom: .09rem solid transparent;
        }

        .baby-recipe .banner .btn.pre {
            left: 0;
        }

        .baby-recipe .banner .btn.pre:after {
            border-right: .09rem solid #fff;
        }

        .baby-recipe .banner .btn.next {
            right: 0;
        }

        .baby-recipe .banner .btn.next:after {
            border-left: .09rem solid #fff;
        }

        .baby-recipe .recipe-item .icon-wrapper .icon {
            width: .7rem;
            height: .7rem;
        }

        .baby-recipe .content {
            padding: .38rem 0 .3rem .57rem;
        }

        .baby-recipe .recipe-item:before {
            position: absolute;
            left: .35rem;
            top: 0;
            bottom: 0;
            z-index: -1;
            content: " ";
            border-left: 1px solid #6fcbdb;
        }

        .baby-recipe .recipe-item {
            display: -webkit-flex;
            display: flex;
            padding-bottom: .4rem;
        }

        .baby-recipe .recipe-item .icon-wrapper {
            -webkit-flex: 0 0 .7rem;
            flex: 0 0 .7rem;
        }

        .baby-recipe .recipe-item .recipe-item-right {
            -webkit-flex: 1;
            flex: 1;
            margin: .08rem 0 0 .39rem;
        }

        .baby-recipe .recipe-item .title {
            margin-left: 0;
            line-height: .34rem;
            font-size: .28rem;
            font-weight: bold;
            color: #222;
        }

        .baby-recipe .recipe-item .food-item {
            margin-top: .17rem;
            margin-right: .28rem;
            width: auto;
            line-height: .34rem;
            font-size: .24rem;
            color: #666;
        }

        .baby-recipe .recipe-item .food-item:last-child {
            margin-right: 0;
        }

        .baby-recipe .recipe-item .con {
            display: block;
            padding: 0;
        }

        .baby-recipe .recipe-item .con .food-wrapper {
            display: -webkit-flex;
            display: flex;
        }

        .notice-details {
            padding: .25rem .20rem;
            border-top: none;
            background: #fff;
        }

        .notice-details .title-wrapper .title {
            line-height: .6rem;
            font-size: .4rem;
            font-weight: bold;
            color: #222;
        }

        .notice-details .title-wrapper .teacher-infor-wrapper {
            display: -webkit-flex;
            display: flex;
            -webkit-justify-content: space-between;
            justify-content: space-between;
            margin-top: .32rem;
        }

        .notice-details .title-wrapper .teacher-infor-wrapper .teacher-infor {
            display: -webkit-flex;
            display: flex;
        }

        .notice-details .title-wrapper .teacher-infor-wrapper .avatar {
            display: block;
            width: .7rem;
            height: .7rem;
            border-radius: 50%;
        }

        .notice-details .title-wrapper .teacher-infor-wrapper .infor-text {
            -webkit-flex: 1;
            flex: 1;
            margin-left: .2rem;
            padding-top: .05rem;
        }

        .notice-details .title-wrapper .teacher-infor-wrapper .infor-text .name {
            line-height: .32rem;
            font-size: .24rem;
            color: #222;
        }

        .notice-details .title-wrapper .teacher-infor-wrapper .infor-text .time {
            margin-top: 0;
            line-height: .3rem;
            font-size: .2rem;
            color: #999;
        }

        .notice-details .title-wrapper .teacher-infor-wrapper .read-num {
            padding: 0 .18rem;
            margin-top: .12rem;
            height: .42rem;
            line-height: .42rem;
            font-size: .24rem;
            border-radius: .21rem;
            background-color: #f66666;
            color: #fff;
        }

        .notice-details .title-wrapper {
            padding-bottom: 0;
        }

        .notice-details .content {
            margin-top: .33rem;
            line-height: .42rem;
            font-size: .3rem;
            color: #222;
        }

        .notice-details .content p {
            text-indent: 2em;
        }

        .mask {
            position: fixed;
            left: 0;
            top: 0;
            z-index: 100;
            width: 100%;
            height: 100%;
            background-color: #000;
            opacity: .3;
        }

        .read-popup {
            position: fixed;
            left: 50%;
            top: 50%;
            z-index: 120;
            padding-top: .34rem;
            width: 5.6rem;
            height: 7.9rem;
            box-sizing: border-box;
            border-radius: .08rem;
            -webkit-transform: translate(-50%, -50%);
            transform: translate(-50%, -50%);
            background-color: #fff;
        }

        .read-popup .titl {
            line-height: .44rem;
            text-align: center;
            font-size: .34rem;
            font-weight: bold;
            color: #222;
        }

        .read-popup .read-tab {
            position: relative;
            display: -webkit-flex;
            display: flex;
            height: .87rem;
        }

        .read-popup .read-tab:after {
            z-index: 2;
            border-color: #ebebeb;
        }

        .read-popup .read-tab .tab-item {
            position: relative;
            -webkit-flex: 1;
            flex: 1;
            line-height: .85rem;
            height: .85rem;
            text-align: center;
            font-size: .3rem;
            color: #999;
        }

        .read-popup .read-tab .tab-item.on {
            color: #49c28d;
        }

        .read-popup .read-tab .tab-item.on:after {
            content: " ";
            position: absolute;
            left: 1.11rem;
            right: 1.11rem;
            bottom: 0;
            z-index: 3;
            height: 2px;
            border-radius: 1px;
            background-color: #49c28d;
        }

        .read-popup .con {
            max-height: 6.09rem;
            overflow: hidden;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
        }

        .read-popup .read-item {
            position: relative;
            padding: .3rem .54rem .21rem .52rem;
            overflow: hidden;
        }

        .read-popup .read-item .num {
            float: left;
            width: .66rem;
            line-height: .8rem;
            font-size: .3rem;
            color: #222;
        }

        .read-popup .read-item .avatar {
            float: left;
            display: block;
            width: .8rem;
            height: .8rem;
            border-radius: 50%;
        }

        .read-popup .read-item .name {
            float: right;
            line-height: .8rem;
            font-size: .3rem;
            color: #222;
        }

        .notice-details .img-team {
            display: flex;
            display: -webkit-flex;
            -webkit-flex-wrap: wrap;
            flex-wrap: wrap;
            -webkit-justify-content: space-between;
            justify-content: space-between;
        }

        .notice-details .img-team img {
            display: block;
            margin-bottom: .15rem;
            width: 2.2rem;
            height: 2.2rem;
            border-radius: .08rem;
        }
    </style>
</head>
#*生成pdf的时候出现的英文重叠的问题原因:需要一个 全角 的符号,如果是全是 半角 则会出现问题*#
<body id="pdfBody" >
<div class="week-english week-plan" style="height:auto;">
    <div class="content" style="font-size: 150px;margin: 0px; padding: 0px;">
        <!--蒙氏-->
        #if($!plan.type == 1)
            <div class="plan-con plan-con2 plan-con3" id="pdf_div"  style="padding-top: 0px;">
                 <img src="$!adminRoot/yuhua/mobile/img/new-logo.png">
            <div style="padding:0.2rem">
                <div class="title-wrapper2" style="padding-top:10px">
                    <div class="title-wrapper2-left">
                        <div class="des" style="margin-top:5px">$!plan.guardNames$!plan.bunchNames周计划</div>
                    </div>
                </div>
                <div class="text-wrapper" style="margin-top:15px">
                    <div class="zhuti">主题:$!plan.name</div>
                    <div class="time" style="text-align:right">$!plan.season $!plan.weekTimeStr</div>
                </div>
                <table width="100%"  border="0" cellspacing="0" cellpadding="0">
                    <tr>
                        <th colspan="2">一日流程</th>
                        <th width="16%">周一</th>
                        <th width="16%">周二</th>
                        <th width="16%">周三</th>
                        <th width="16%">周四</th>
                        <th width="16%">周五</th>
                    </tr>
                    #*入园*#
                    <tr #if($!plan.isMry == 0) style="display:none" #end>
                        <td colspan="2">$!plan.mryName</td>
                        <td colspan="5"><p class="plan_text">$!plan.mry</p></td>
                    </tr>
                     #*$!plan.tyyx*#
                    <tr #if($!plan.isMtyyx == 0) style="display:none" #end>
                        <td colspan="2">$!plan.mtyyxName</td>
                        <td colspan="1"><p placeholder="" maxlength="150" name ="mtyyx1" >$!plan.mtyyx1</p></td>
                        <td colspan="1"><p placeholder="" maxlength="150" name ="mtyyx2" >$!plan.mtyyx2</p></td>
                        <td colspan="1"><p placeholder="" maxlength="150" name ="mtyyx3" >$!plan.mtyyx3</p></td>
                        <td colspan="1"><p placeholder="" maxlength="150" name ="mtyyx4" >$!plan.mtyyx4</p></td>
                        <td colspan="1"><p placeholder="" maxlength="150" name ="mtyyx5" >$!plan.mtyyx5</p></td>
                    </tr>
                    #*团讨*#
                    <tr #if($!plan.isMmsgztt == 0) style="display:none" #end>
                        <td colspan="2">$!plan.mmsgzttName</td>
                        <td colspan="5"><p class="plan_text">$!plan.mmsgztt</p></td>
                    </tr>
                    #*蒙氏工作*#
                     <tr #if($!plan.isMmsgz == 0) style="display:none" #end>
                        <td colspan="2">$!plan.mmsgzName</td>
                        <td colspan="5"><p class="plan_text">$!plan.mmsgz</p></td>
                    </tr>
                    
                    #*进餐饮水*#
                    <tr #if($!plan.isMjcys == 0) style="display:none" #end>
                        <td colspan="2">$!plan.mjcysName</td>
                        <td colspan="5"><p class="plan_text">$!plan.mjcys</p></td>
                    </tr>
                    #*午睡*#
                    <tr #if($!plan.isMws == 0) style="display:none" #end>
                        <td colspan="2">$!plan.mwsName</td>
                        <td colspan="5"><p class="plan_text">$!plan.mws</p></td>
                    </tr>
                    #*英语*#
                    <tr #if($!plan.isMyy == 0) style="display:none" #end>
                        <td colspan="2">$!plan.myyName</td>
                        <td colspan="1"><p class="plan_text">$!plan.myy1</p></td>
                        <td colspan="1"><p class="plan_text">$!plan.myy2</p></td>
                        <td colspan="1"><p class="plan_text">$!plan.myy3</p></td>
                        <td colspan="1"><p class="plan_text">$!plan.myy4</p></td>
                        <td colspan="1"><p class="plan_text">$!plan.myy5</p></td>
                    </tr>
                    #*$!plan.xxhd*#
                    <tr #if($!plan.isMxxhd == 0) style="display:none" #end>
                        <td colspan="2">$!plan.mxxhdName</td>
                        <td colspan="1"><p class="plan_text">$!plan.mxxhd1</p></td>
                        <td colspan="1"><p class="plan_text">$!plan.mxxhd2</p></td>
                        <td colspan="1"><p class="plan_text">$!plan.mxxhd3</p></td>
                        <td colspan="1"><p class="plan_text">$!plan.mxxhd4</p></td>
                        <td colspan="1"><p class="plan_text">$!plan.mxxhd5</p></td>
                    </tr>
                     #*离园*#
                    <tr #if($!plan.isMly == 0) style="display:none" #end>
                        <td colspan="2">$!plan.mlyName</td>
                        <td colspan="5"><p class="plan_text">$!plan.mly</p></td>
                    </tr>
                     #*家园共育*#
                    <tr #if($!plan.isMjygy == 0) style="display:none" #end>
                        <td colspan="2">$!plan.mjygyName</td>
                        <td colspan="5"><p class="plan_text">$!plan.mjygy</p></td>
                    </tr>

                </table>
                </div>
               <div class="bottom-text"></div>
            </div>
            <!--非蒙氏-->
        #elseif($!plan.type == 2)
            <div class="plan-con plan-con2" id="pdf_div" style="padding-bottom: 10px;">
                 <img src="$!adminRoot/yuhua/mobile/img/new-logo.png">
                 <div style="padding:0.2rem">
                <div class="title-wrapper2" style="padding-top:10px">
                    <div class="title-wrapper2-left">
                        <div class="des" style="margin-top:5px">$!plan.guardNames $!plan.bunchNames周计划</div>
                    </div>
                </div>
                <div class="text-wrapper" style="margin-top:15px">
                    <div class="zhuti">主题:$!plan.name</div>
                    <div class="time" style="text-align:right">$!plan.season $!plan.weekTimeStr</div>
                </div>
                <table width="100%"  border="0" cellspacing="0" cellpadding="0">
                    <tr>
                        <th width="20%">一日流程</th>
                        <th width="16%">周一</th>
                        <th width="16%">周二</th>
                        <th width="16%">周三</th>
                        <th width="16%">周四</th>
                        <th width="16%">周五</th>
                    </tr>
                    #*入园*#
                    <tr #if($!plan.isRy == 0) style="display:none" #end>
                        <td colspan="1">$!plan.ryName</td>
                        <td colspan="5"><p class="plan_text">$!plan.ry</p></td>
                    </tr>
                      #*$!plan.tyyx*#
                    <tr #if($!plan.isTyyx == 0) style="display:none" #end>
                        <td colspan="1">$!plan.tyyxName</td>
                        <td colspan="1"><p placeholder="" maxlength="150" name ="tyyx1" >$!plan.tyyx1</p></td>
                        <td colspan="1"><p placeholder="" maxlength="150" name ="tyyx2" >$!plan.tyyx2</p></td>
                        <td colspan="1"><p placeholder="" maxlength="150" name ="tyyx3" >$!plan.tyyx3</p></td>
                        <td colspan="1"><p placeholder="" maxlength="150" name ="tyyx4" >$!plan.tyyx4</p></td>
                        <td colspan="1"><p placeholder="" maxlength="150" name ="tyyx5" >$!plan.tyyx5</p></td>
                    </tr>
                    #*启动仪式*#
                    <tr #if($!plan.isQdys == 0) style="display:none" #end>
                        <td colspan="1">$!plan.qdysName</td>
                        <td colspan="5"><p class="plan_text">$!plan.qdys</p></td>
                    </tr>
                    #*游戏活动*#
                    <tr #if($!plan.isYxhd == 0) style="display:none" #end>
                        <td colspan="1">$!plan.yxhdName</td>
                        <td colspan="5"><p class="plan_text">$!plan.yxhd</p></td>
                    </tr>
                    #*进餐饮水*#
                    <tr #if($!plan.isJcys == 0) style="display:none" #end>
                        <td colspan="1">$!plan.jcysName</td>
                        <td colspan="5"><p class="plan_text">$!plan.jcys</p></td>
                    </tr>
                    #*午睡*#
                    <tr #if($!plan.isWs == 0) style="display:none" #end>
                        <td colspan="1">$!plan.wsName</td>
                        <td colspan="5"><p class="plan_text">$!plan.ws</p></td>
                    </tr>
                    #*英语*#
                    <tr #if($!plan.isYy == 0) style="display:none" #end>
                        <td colspan="1">$!plan.yyName</td>
                        <td colspan="1"><p class="plan_text">$!plan.yy1</p></td>
                        <td colspan="1"><p class="plan_text">$!plan.yy2</p></td>
                        <td colspan="1"><p class="plan_text">$!plan.yy3</p></td>
                        <td colspan="1"><p class="plan_text">$!plan.yy4</p></td>
                        <td colspan="1"><p class="plan_text">$!plan.yy5</p></td>
                    </tr>
                    #*$!plan.xxhd*#
                    <tr #if($!plan.isXxhd == 0) style="display:none" #end>
                        <td colspan="1">$!plan.xxhdName</td>
                        <td colspan="1"><p class="plan_text"></p>$!plan.xxhd1</td>
                        <td colspan="1"><p class="plan_text">$!plan.xxhd2</p></td>
                        <td colspan="1"><p class="plan_text">$!plan.xxhd3</p></td>
                        <td colspan="1"><p class="plan_text">$!plan.xxhd4</p></td>
                        <td colspan="1"><p class="plan_text">$!plan.xxhd5</p></td>
                    </tr>
                    #*离园*#
                    <tr #if($!plan.isLy == 0) style="display:none" #end>
                        <td colspan="1">$!plan.lyName</td>
                        <td colspan="5"><p class="plan_text">$!plan.ly</p></td>
                    </tr>
                    
                    #*家园共育*#
                    <tr #if($!plan.isJygy == 0) style="display:none" #end>
                        <td colspan="1">$!plan.jygyName</td>
                        <td colspan="5"><p class="plan_text">$!plan.jygy</p></td>
                    </tr>

                </table>
                </div>
                <div class="bottom-text"></div>
            </div>
        #else
            <h5>本周暂无教学计划</h5>
        #end
    </div>
</div>

<script type="text/javascript" src="$!adminRoot/yuhua/mobile/js/rem.js"></script>
<!-- 单位换算（*不可以删除 *）-->
<script type="text/javascript" src="$!{adminRoot}/h_admin/js/jquery.min.js?v=2.1.4"></script>
<script type="text/javascript" src="$!{adminRoot}/plug/jquery-easyui/ajaxfileupload.js"></script>
<script type="text/javascript" src="$!adminRoot/plug/canvas/html2canvas.js"></script>
<script type="text/javascript" src="$!adminRoot/plug/canvas/jsPdf.debug.js"></script>
<script>
    //内容默认居左
/*    $("tr td").each(function () {
        var index = $(this).index();
        if(index > 0){
            $(this).attr("style","text-align:left");
        }
    })*/
    #*生成pdf的时候出现的英文重叠的问题原因:需要一个 全角 的符号,如果是全是 半角 则会出现问题*#
    var content = "　";//<------------这里是全角的空格 符号
    $(".plan_text").each(function () {
        var text = $(this).html();
        text += content;
        $(this).html(text);
    })



    function exportPdf(){
        // 自动将页面高度取整页面
       
       var fixedHeight = $("#pdf_div").height();
        $("body").css("overflow-y","hidden");
        var pHeight = $("body").width()/595.28*841.89;

        $("body").css("overflow-y","scroll");
        if(fixedHeight%pHeight>0){
            fixedHeight = (parseInt(fixedHeight/pHeight)+1)*pHeight;
            $("#pdf_div").css("height",fixedHeight+"px");
        }
        
        html2canvas($('#pdf_div'), {  //选择要导出的页面id样式
            useCORS:true,
            height: $("#pdf_div").height(),
            onrendered:function(canvas) {

                var contentWidth = canvas.width;
                var contentHeight = canvas.height;

                //一页pdf显示html页面生成的canvas高度;
                var pageHeight = contentWidth / 595.28 * 841.89;

                //未生成pdf的html页面高度
                var leftHeight = contentHeight;
                //页面偏移
                var position = 0;
                //a4纸的尺寸[595.28,841.89]，html页面生成的canvas在pdf中图片的宽高
                var imgWidth = 595.28;
                var imgHeight = 595.28/contentWidth * contentHeight;
                var pageData = canvas.toDataURL('image/jpeg', 1.0);

                var pdf = new jsPDF('', 'pt', 'a4');

                // pdf.addImage(pageData, 'JPEG', 0, 0, 595.28, 595.28/canvas.width * canvas.height );
                //有两个高度需要区分，一个是html页面的实际高度，和生成pdf的页面高度(841.89)
                //当内容未超过pdf一页显示的范围，无需分页

                if (leftHeight < pageHeight) {
                    pdf.addImage(pageData, 'JPEG', 0, 0, imgWidth, imgHeight);
                } else {
                    while (leftHeight > 0) {
                        pdf.addImage(pageData, 'JPEG', 0, position, imgWidth, imgHeight)
                        leftHeight -= pageHeight;
                        //避免添加空白页
                        if (leftHeight > 0) {
                            pdf.addPage();
                        }
                        position -= 841.89;
                    }
                }
                pdf.save('教学周计划.pdf');
            }
        });
    }
    if (/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i.test(navigator.userAgent)) { //移动端
     //啥也不干
    }else{
        exportPdf();
    }
 </script>
</body>

</html>