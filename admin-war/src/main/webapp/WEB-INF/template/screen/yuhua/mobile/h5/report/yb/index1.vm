<!DOCTYPE html>
<html style="font-size: 20px;">
<head>
	  <meta charset="UTF-8" />
      <meta http-equiv="X-UA-Compatible" content="IE=edge">
	  <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0" />
	  <meta name="apple-mobile-web-app-status-bar-style" content="blank-translucent" /> 
      <title>月报列表</title>
	  <!-- 引入 WeUI CDN 链接 -->
    	<link rel="stylesheet" type="text/css" href="https://qimiaoyuan-app.oss-cn-hangzhou.aliyuncs.com/backgroud/css/weui.min.css">
    	<link rel="stylesheet" type="text/css" href="https://qimiaoyuan-app.oss-cn-hangzhou.aliyuncs.com/backgroud/css/index.css">
</head>
<body>
<form id="sunday_yb_form" class="form-horizontal" action="/sunday/mobile/h5/yb/select" method="post"  enctype="multipart/form-data">
    <input name="pageNumber" id="pageNumber" type="hidden" #if($!param.pageNumber) value="$!param.pageNumber" #else value="1" #end />
    <input name="pageSize" id="pageSize" type="hidden" #if($!param.pageSize) value="$!param.pageSize" #else value="50" #end />
	<input name="code" id="code" type="hidden" #if($!param.code) value="$!param.code" #end />
    <input name="status" id="status" type="hidden" #if($!param.status && $!param.status != "") value="$!param.status" #end />
    <input name="bunchId" id="bunchId" type="hidden" #if($!param.bunchId) value="$!param.bunchId" #end />
    <input name="studentId" id="studentId" type="hidden" #if($!param.studentId) value="$!param.studentId" #end />
    <input name="guardId" id="guardId" type="hidden" #if($!param.guardId) value="$!param.guardId" #end />
    <input name="isQmy" id="isQmy" type="hidden" #if($!param.isQmy) value="$!param.isQmy" #end />
</form>
<div class="page">
    <div class="page__bd page__bd_spacing">
      <div class="weui-flex">
        <div class="weui-flex__item">
          <a href="javascript:" role="button" class="weui-btn weui-btn_primary weui-btn_medium" id="showDialog">新增月报</a>
          <a href="javascript:sunday_yb_batch_submit()" role="button" class="weui-btn weui-btn_primary weui-btn_medium">批量提交</a>
        </div>
      </div>
      <div class="weui-flex" style="margin-top: 8px">
        <div style="width:20px;margin-top:3px;margin-left:10px;z-index:10000;position:absolute;">
        <input type="checkbox" id="checkAll" />
        </div>
        <div class="weui-flex__item" style="overflow:hidden;white-space:nowrap;text-overflow:ellipsis;">
           <a href="javascript:" role="button" class="weui-btn weui-btn_default weui-btn_medium"
            id="doubleLinePicker" style="font-size:12px;">#if($!param.codeName) $!param.codeName #else 请选择课程 #end</a>
        </div>
        <div class="weui-flex__item" style="overflow:hidden;white-space:nowrap;text-overflow:ellipsis;">
          <a href="javascript:" role="button" class="weui-btn weui-btn_default weui-btn_medium"
            id="singleLinePicker" style="font-size:12px;">#if($!param.status && $!param.status != "") #if($!param.status==0) 待提交 #end #if($!param.status==1) 已提交 #end #if($!param.status==2) 未通过 #end #if($!param.status==3) 审核通过 #end #if($!param.status==4) 已发布 #end #else 请选择状态 #end</a>
        </div>
      </div>
	  <form id="sunday_yb_form_batch_submit" class="form-horizontal" action="/sunday/mobile/h5/yb/batch_submit" method="post"  enctype="multipart/form-data">
      <div class="weui-cells">
        <!-- 月报列表 -->
		#foreach($!detail in $!ybs)
        <div class="weui-cell weui-cell_swiped">
          <div role="option" class="weui-cell__bd">
            <div class="weui-cell">
              <div style="width:50px;margin-left:-5px;">
                #if($!detail.status == 0 || $!detail.status == 2)
		        <input type="checkbox" id="id$!detail.id" name="ids" value="$!detail.id" >
		        #end
              </div>
              <div class="weui-cell__bd">
                <p>$!detail.year年$!detail.month月</p>
                <div class="weui-cell__desc">#if($!detail.isQmy==0 && $!detail.code=="YB_XXL")三心课程#else$!detail.course#end</div>
                #if($!detail.studentName)
                <div class="weui-cell__desc">$!detail.studentName</div>
                #end
                #if($!detail.approveResult)
                <div class="weui-cell__desc">审批意见：$!detail.approveResult</div>
                #end
              </div>
              <div class="" id="weui-cell__ft_$!detail.id">
                #if($!detail.status == 0) 
                <a class="easyui-linkbutton" id="button_sports_$!{detail.id}$foreach.index" style="margin-left:15px;margin-top:12px;padding: 5px 9px 5px 9px;color:#fff;border:none;background:#63B8FF;border-radius:3px;" href="javascript:preview('$!detail.pdfUrl');">预览</a>
        		<a class="easyui-linkbutton" id="button_sportsTeachingArray$!{detail.id}$foreach.index" style="margin-left:15px;margin-top:12px;padding: 5px 9px 5px 9px;color:#fff;border:none;background:#63B8FF;border-radius:3px;" href="javascript:add_modify_yb2('$!detail.ybId', '$!detail.studentId');" >编辑</a>
	        	#end
	        	#if($!detail.status == 1) 已提交 #end 
	        	#if($!detail.status == 2) <a class="easyui-linkbutton" id="button_sportsTeachingArray$!{detail.id}$foreach.index" style="margin-left:15px;margin-top:12px;padding: 5px 9px 5px 9px;color:#fff;border:none;background:#63B8FF;border-radius:3px;" href="javascript:add_modify_yb2('$!detail.ybId', '$!detail.studentId');" >编辑</a> #end 
	        	#if($!detail.status == 3) 审核通过 #end
	        	#if($!detail.status == 4) 已发布 #end
              </div>
            </div>
          </div>
        </div>
        #end
      </div>
      </form>
    </div>
  </div>
  <!-- 弹窗 -->
  <div id="dialogWrap" class="js_dialog_wrap" ref="showDialog" aria-label="弹窗标题" role="dialog" aria-modal="false"
    aria-hidden="true" style="display: none">
    <div aria-label="关闭" role="button" class="js_close weui-mask"></div>
    <div id="js_dialog" class="js_dialog weui-half-screen-dialog weui-half-screen-dialog_large">
      <div class="weui-half-screen-dialog__hd">
        <div class="weui-half-screen-dialog__hd__main">
          <h4 class="weui-flex" style="align-items: center; font-size: 14px">
            新增/修改月报
          </h4>
        </div>
      </div>
      <div class="weui-half-screen-dialog__bd">
        <div class="weui-cells__group weui-cells__group_form">
          <div class="weui-cells" id="weui-cells">
          		
            <div role="button" aria-haspopup="listbox"
              class="weui-cell weui-cell_active weui-cell_select weui-cell_select-after">
              <div class="weui-cell__hd">
                <label class="weui-label">课程</label>
              </div>
              <div class="weui-cell__bd">
              	<select class="weui-select" name="select2" required id="community">
                    <option value="">请选择</option>
                </select>
              </div>
            </div>
            
            <label for="js_input1" class="weui-cell weui-cell_active weui-cell_readonly">
              <div class="weui-cell__hd">
                <span class="weui-label">月报日期</span>
              </div>
              <div class="weui-cell__bd">
                <input id="js_input1" class="weui-input" placeholder="请输入月报日期" value="2023年8月" readonly />
              </div>
            </label>
            <label for="js_input2" class="weui-cell weui-cell_active weui-cell_disabled">
              <div class="weui-cell__hd">
                <span class="weui-label">所选课程</span>
              </div>
              <div class="weui-cell__bd">
                <input id="js_input2" class="weui-input" placeholder="请输入所选课程" value="SPARK儿童运动课程" disabled />
              </div>
            </label>
            
          </div>
        </div>
      </div>
      <div class="weui-half-screen-dialog__ft">
        <div class="weui-half-screen-dialog__btn-area" id="weui-half-screen-dialog__btn-area" style="display:block;">
          <a href="javascript:" class="js_close weui-btn weui-btn_default">取消</a>
          <a href="javascript:sunday_yb_save()" class="weui-btn weui-btn_primary">保存</a>
        </div>
        <div class="weui-half-screen-dialog__btn-area" id="weui-half-screen-dialog__btn-area2" style="display:block;">
          <a href="javascript:" class="js_close weui-btn weui-btn_default">取消</a>
        </div>
      </div>
    </div>
  </div>
  <!-- 表单弹窗 -->
  <div class="weui-gallery" id="gallery" style="z-index:9999;">
    <img class="weui-gallery__img" id="galleryImg" style="position: absolute;left: 50%;top: 50%;transform: translate(-50%, -50%);"></img>
  </div>


<script type="text/javascript" src="https://qimiaoyuan-app.oss-cn-hangzhou.aliyuncs.com/test/202306/911f4867895a88278321932b2a99feb12179e6d3.js"></script>
<script type="text/javascript" src="https://qimiaoyuan-app.oss-cn-hangzhou.aliyuncs.com/test/202306/244ee6ccc4479ca878e3b258493bbbb0f92114e0.js"></script>
<script type="text/javascript" src="https://cdn.bootcdn.net/ajax/libs/lodash.js/4.17.21/lodash.min.js"></script>
<script type="text/javascript" src="$!mobileRoot/plug/jquery-easyui/ajaxfileupload.js"></script>
<script type="text/javascript">
	
	
$(function () {
    // 筛选框doubleLinePicker
	$("#doubleLinePicker").on("click", function () {
      // 单列picker
      weui.picker(
        [
          {
            label: "请选择课程",
            value: "",
          },
          #if($!param.isQmy == 0)
          {
            label: "SPARK儿童运动课程",
            value: "YB_SPARK",
          },
          {
            label: "三心课程",
            value: "YB_XXL",
          }
          #else
          {
            label: "SPARK儿童运动课程",
            value: "YB_SPARK",
          },
          {
            label: "SEL社会情感学习课程",
            value: "YB_SEL",
          },
          {
            label: "学习力课程",
            value: "YB_XXL",
          }
          #end
        ],
        {
          className: "custom-classname",
          container: "body",
          defaultValue: [""],
          onChange: function (result) {
            console.log(result);
          },
          onConfirm: function (result) {
            console.log(result);
            $("#code").val(result);
            $("#pageNumber").val(1);
            $("#pageSize").val(50);
    		$("#sunday_yb_form").submit();
          },
          id: "doubleLinePicker",
        }
      );
    });
    $("#singleLinePicker").on("click", function () {
      // 单列picker
      weui.picker(
        [
          {
            label: "请选择状态",
            value: "",
          },
          {
            label: "待提交",
            value: "0",
            // disabled: true // 不可用
          },
          {
            label: "已提交",
            value: "1",
          },
          {
            label: "未通过",
            value: "2",
          },
          {
            label: "审核通过",
            value: "3",
          },
          {
            label: "已发布",
            value: "4",
          },
        ],
        {
          className: "custom-classname",
          container: "body",
          defaultValue: ["0"],
          onChange: function (result) {
            console.log(result);
          },
          onConfirm: function (result) {
            console.log(result);
            $("#status").val(result);
            $("#pageNumber").val(1);
            $("#pageSize").val(50);
    		$("#sunday_yb_form").submit();
          },
          id: "singleLinePicker",
        }
      );
    });
    
});

    // 弹窗
    const $dialog = $("#js_dialog");
    const $dialogWrap = $("#dialogWrap");

    function closeDialog (o) {
      const $jsDialogWrap = o.parents(".js_dialog_wrap");
      $jsDialogWrap
        .attr("aria-hidden", "true")
        .attr("aria-modal", "false")
        .removeAttr("tabindex");
      $jsDialogWrap.fadeOut(300);
      $jsDialogWrap
        .find(".js_dialog")
        .removeClass("weui-half-screen-dialog_show");
      setTimeout(function () {
        $("#" + $jsDialogWrap.attr("ref")).trigger("focus");
      }, 300);
    }

    // 阻止弹窗内部滚动冒泡
    $(".js_dialog_wrap").on("touchmove", function (e) {
      if ($.contains(document.getElementById("js_wrap_content"), e.target)) {
      } else {
        e.preventDefault();
      }
    });

    $(".js_close").on("click", function () {
      closeDialog($(this));
    });

    $("#showDialog").on("click", function () {
    
	  	var bunchId = $("#bunchId").val();
	  	var guardId = $("#guardId").val();
	  	var id = $("#id").val();
	  	var isQmy = $("#isQmy").val();
	    $.ajax({
			type:'get',
			url:'/sunday/mobile/h5/yb/input?bunchId='+bunchId+"&guardId="+guardId+"&isQmy="+isQmy+"&id="+id,
			cache:false,
			success:function(data){
				//alert(data);
				document.getElementById('weui-cells').innerHTML = data;
				$dialogWrap.attr("aria-hidden", "false");
			    $dialogWrap.attr("aria-modal", "true");
			    $dialogWrap.attr("tabindex", "0");
			    $dialogWrap.fadeIn(200);
			    $dialog.addClass("weui-half-screen-dialog_show");
	      		document.getElementById("weui-half-screen-dialog__btn-area").style.display = "block";
	      		document.getElementById("weui-half-screen-dialog__btn-area2").style.display = "none";
			    setTimeout(function () {
			        $dialogWrap.trigger("focus");
			    }, 200);
			}
		});
    });

  function add_modify_yb(ybId){
  	var bunchId = $("#bunchId").val();
  	var guardId = $("#guardId").val();
  	var id = $("#id").val();
  	var isQmy = $("#isQmy").val();
  	$.ajax({
		type:'get',
		url:'/sunday/mobile/h5/yb/input?ybId='+ybId+"&bunchId="+bunchId+"&guardId="+guardId+"&isQmy="+isQmy+"&id="+id,
		cache:false,
		success:function(data){
			console.log("data="+data);
			document.getElementById('weui-cells').innerHTML = data;
      		document.getElementById("weui-half-screen-dialog__btn-area").style.display = "block";
      		document.getElementById("weui-half-screen-dialog__btn-area2").style.display = "none";
		}
	});
  }

  function add_modify_yb2(ybId, studentId){
  	var bunchId = $("#bunchId").val();
  	var guardId = $("#guardId").val();
  	var isQmy = $("#isQmy").val();
  	$.ajax({
		type:'get',
		url:'/sunday/mobile/h5/yb/input?ybId='+ybId+"&bunchId="+bunchId+"&guardId="+guardId+"&studentId="+studentId+"&isQmy="+isQmy,
		cache:false,
		success:function(data){
			console.log("data="+data);
			document.getElementById('weui-cells').innerHTML = data;
			$dialogWrap.attr("aria-hidden", "false");
		    $dialogWrap.attr("aria-modal", "true");
		    $dialogWrap.attr("tabindex", "0");
		    $dialogWrap.fadeIn(200);
		    $dialog.addClass("weui-half-screen-dialog_show");
	        document.getElementById("weui-half-screen-dialog__btn-area").style.display = "block";
	        document.getElementById("weui-half-screen-dialog__btn-area2").style.display = "none";
		    setTimeout(function () {
		        $dialogWrap.trigger("focus");
		    }, 200);
		}
	});
  }
//图片上传
function sunday_yb_upload_imgage(t){
    
    var file_imgae = $(t).prev();
    var file_input = $(t).next()
    
    $.ajaxFileUpload({
        url:'/sunday/mobile/h5/yb/uploadSingle',
        secureuri:false,
        fileElementId:$(t).attr("id"),//file标签的id
        dataType: 'json',//返回数据的类型
        data:{},//一同上传的数据
        success: function (data) {
            if(data.code!=0){
                weui.alert("上传错误，请重试！");
            } else{
                if(data.result.halfPath!=null&&data.result.halfPath!=""){
                    $(file_imgae).attr("style","width:192px;height:144px;background-image:url("+data.result.fullPath+"); background-size:100% 100%;");
                    $(file_input).val(data.result.halfPath);
                }
            }
        }
    });
}

var click=0;//防止重复提交

//保存
function sunday_yb_save(){

	if($("#studentName").val() == "" || $("#studentName").val() == null){
		weui.alert("请选择学生");
		return;
	}
	var pass = false;
	$("input[name=growthRecordArray]").each(function () {
		if($(this).is(':checked')){
			pass = true;
		}
	});
	if(!pass){
		weui.alert("请选择至少一项成长记录");
		return;
	}
	
	$("input[name=teacherCommentDetailArray]").each(function () {
		if($(this).val() == "" || $(this).val() == null){
			weui.alert("请填写教师评语！");
			return ;
		}
	});
	if(($('#imgUrls_0').val() == null || $('#imgUrls_0').val() == "") || ($('#imgUrls_1').val() == null || $('#imgUrls_1').val() == "")
			|| ($('#imgUrls_2').val() == null || $('#imgUrls_2').val() == "") || ($('#imgUrls_3').val() == null || $('#imgUrls_3').val() == "") 
			 || ($('#imgUrls_4').val() == null || $('#imgUrls_4').val() == "")){
		
		weui.alert("上传图片数量不足，请全部上传");
		return ;
	}
	if(click == 0){
		click = 1;
		var msg = "确认是否保存?";
		weui.confirm(msg, function(){ 
	        //alert('yes') 
	        $.ajax({
				url: "/sunday/mobile/h5/yb/save",
				type: "post",
				dataType: "json",
				data: $('#sunday_yb_form_save').serialize(),
				success: function (result) {
					click = 0;
					if (result.code == 0) {
			            $("#pageNumber").val(1);
			            $("#pageSize").val(50);
			    		$("#sunday_yb_form").submit();
		            }
				}
			});
	    }, function(){ 
	        //alert('no') 
	        click = 0;
		});
	}
}

//批量提交
function sunday_yb_batch_submit(){
	if(!$('#sunday_yb_form_batch_submit').serialize()){
		weui.alert("请选择需要提交的月报")
	}else{
    	$.ajax({
			url: "/sunday/mobile/h5/yb/batch_submit",
			type: "post",
			dataType: "json",
			data: $('#sunday_yb_form_batch_submit').serialize(),
			success: function (result) {
				if (result.code == 0) {
			            $("#pageNumber").val(1);
			            $("#pageSize").val(50);
			    		$("#sunday_yb_form").submit();
                    }else{
                    	weui.alert(result.message);
                    }
			}
		})
	}
}
function preview(ele){
	if(ele == null || ele == ""){
        $("#pageNumber").val(1);
        $("#pageSize").val(50);
		$("#sunday_yb_form").submit();
	}else{
		
	  	let u = navigator.userAgent
	    let isAndroid = u.indexOf('Android') > -1 || u.indexOf('Linux') > -1; //g
	    let isIOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); //ios终端
	    if (isAndroid) {
	  		$dialogWrap.attr("aria-hidden", "false");
		    $dialogWrap.attr("aria-modal", "true");
		    $dialogWrap.attr("tabindex", "0");
		    $dialogWrap.fadeIn(200);
		    $dialog.addClass("weui-half-screen-dialog_show");
		    $("#weui-cells").html('<img class="demo-image" id="preview" src = "'+ele+'" width="100%" onclick="zoomIn(this)"/>');
		    document.getElementById("weui-half-screen-dialog__btn-area").style.display = "none";
		    document.getElementById("weui-half-screen-dialog__btn-area2").style.display = "block";
		    setTimeout(function () {
		        $dialogWrap.trigger("focus");
		    }, 200);  
	    }
	    if (isIOS) {
	        window.webkit.messageHandlers.operImageClick.postMessage(ele);
	    }
  	}
}
function zoomIn(ele) {
	//$("#galleryImg").attr("src", ele.getAttribute("src")); // 获取需要全屏展示的图片
  	//$("#gallery").fadeIn(100); // 放大图片
  	
  	window.android.show(ele.getAttribute("src"));
}
</script>
<script type="text/javascript">
  $(function () {
    /* 图片预览 */
    $("#gallery").on("click", function() {
      $("#gallery").fadeOut(100); // 退出大图
    });
  });
</script>
<script>
   $(function() {  
   		//全选,或取消全选;
		$("#checkAll").click( 
		  function(){ 
		    if(this.checked){ 
		        $("input[name='ids']").prop('checked', true)
		    }else{ 
		        $("input[name='ids']").prop('checked', false)
		    } 
		  } 
		);
 		//这里除了全选框之外的复选框统一name;
		$('input[name="ids"]').click(function () {
			//得到下面复选框的总数;
		   var l1= $('input[name="ids"]').length;
		   //得到选中复选框的总数;
		   var l2= $('input[name="ids"]:checked').length;
		   //将比较结果赋值给全选框的checked属性;
		   $('#checkAll').prop("checked", l1==l2);
		});
	});
</script>
</body>
</html>