<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,minimum-scale=1.0,user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <title>家长课程</title>
    <link rel="stylesheet" type="text/css" href="$!mobileRoot/yuhua/mobile/css/style.css">

</head>
<body>
<div class="parent-curriculum" style="margin-bottom: 0.5rem">
    <div class="introduce">
        <div class="title">课程介绍</div>
        <div class="content on">
            <div class="con">$!course.desc</div>
            <div class="section">
                <div class="titl">一句话经典</div>
                <div class="con">$!course.oneWord</div>
            </div>
            <!--  -->
            <div class="section">
                <div class="titl">你将听到</div>
                <div class="con">
                    $!course.willListen

                </div>
            </div>
            <!--  -->
            <div class="teacher">
                <div class="titl">讲师介绍</div>
                <div class="con">
                    <img src="$!course.expertImage" alt="" class="avatar">
                    <div class="text">
                        <div class="name">$!course.expertName</div>
                        <div class="des">$!course.expertDesc</div>
                    </div>
                </div>
            </div>
            <!-- 讲师介绍 -->
        </div>
        <!--<div class="close" id="close">收起</div>-->
    </div>
    <!-- 课程介绍 -->


    <!-- 章节 -->
</div>

<script type="text/javascript" src="$!mobileRoot/yuhua/mobile/js/rem.js"></script>
<!-- 单位换算（*不可以删除 *）-->
<script type="text/javascript" src="$!mobileRoot/yuhua/mobile/js/jquery.min.js"></script>
<script type="text/javascript">
    $('#close').click(function () {
        if ($('.introduce .content').hasClass('on')) {
            $('.introduce .content').stop().slideUp(300);
            $('.introduce .content').removeClass('on');
            $(this).text('展开');
        } else {

            $('.introduce .content').stop().slideDown(300);
            $('.introduce .content').addClass('on');
            $(this).text('收起');
        }
    })
    // 课程介绍 展开收起
</script>
</body>
</html>