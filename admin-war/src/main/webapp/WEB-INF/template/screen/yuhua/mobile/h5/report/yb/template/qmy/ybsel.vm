<!DOCTYPE html>
<html lang="en">
	
<style>
	
</style>
<head>
    <meta charset="UTF-8"/>
    <title>Title</title>
    <style>
        *{
            font-family: MicrosoftYaHei;
            margin: auto;
        }
        .square {
			width: 26px;
			heigth: 26px;
			border:1px solid #207583;
		}
    </style>
</head>
<body>
	<div style="width:2482px; height:3509px;position:relative;background-image: url(http://qimiaoyuan-app.oss-cn-hangzhou.aliyuncs.com/image/backgroud/YB_SEL_2.jpeg); background-size: 100% ;">
			<div style="padding-top:620px;padding-left:480px;width:100%">
				<div style="float:left;padding-left:140px;font-size:44px;font-family:MicrosoftYaHei;color:white;width:380px;">${name}</div>
				<div style="float:left;padding-left:710px;margin-top:10px;font-size:44px;font-family:MicrosoftYaHei;color:white;">${year}</div>
				<div style="float:left;padding-left:110px;margin-top:10px;font-size:44px;font-family:MicrosoftYaHei;color:white;width:20px;text-align:right;">${month}</div>
				<div style="float:left;padding-left:110px;margin-top:10px;font-size:44px;font-family:MicrosoftYaHei;color:white;width:20px;text-align:right;">${date}</div>
			</div>
			
			<div style="margin-top:50px;margin-left:680px;width:60%;height:220px;float:left;">
				<table width="100%" style="border:0px solid red;height:100%;">
				<tr>
					<td>
						<div style="float:left;margin-left:10px;width:100%;font-size:44px;font-family:MicrosoftYaHei;">${theme}</div>
					</td>	
				</tr>
				</table>
			</div>
			<div style="margin-top:20px;margin-left:580px;width:60%;height:900px;float:left;position:relative;valign:middle;background:#FFF;">
			<table width="100%" style="border:0px solid red;height:100%;">
				<tr>
					<td>
						#foreach($g in $growths)
						<div style="float:left;margin-top:25px;margin-left:110px;width:97%;font-size:40px;line-height:75px;vertical-algin:middle;font-family:MicrosoftYaHei;">
							<div class="square" style="float:left;margin-top:20px;padding-right:10px;padding-top:10px;padding-left:10px;#if(!$g.checked)padding-bottom:35px; #else padding-bottom:20px;#end line-height:13px;color:#207583;">#if($g.checked)√#else #end</div>
							<div style="float:left;width:92%;margin-left:20px;">$g.value</div>
						</div>
						<!--
						<div style="float:left;margin-top:25px;margin-left:110px;width:97%;font-size:40px;font-family:MicrosoftYaHei;line-height:50px;vertical-algin:middle;text-algin:left;">
							<table width="100%" style="border:0px solid red;height:100%;">
								<tr>
									<td style="text-algin:left;width:75px;">
										<div class="square" style="padding-right:10px;padding-top:10px;padding-left:10px;#if(!$g.checked)padding-bottom:35px; #else padding-bottom:20px;#end line-height:13px;color:#207583;">#if($g.checked)√#else #end</div>
									</td>
									<td style="text-algin:left;padding-left:20px;">$g.value</td>
								</tr>
							</table>
						</div>
						-->
						#end
					</td>	
				</tr>
			</table>
			</div>
			
			<div style="margin-top:50px;margin-left:680px;width:60%;height:320px;float:left;position:relative;valign:middle;">
				<table width="100%" style="border:0px solid red;height:100%;">
				<tr>
					<td>
						#foreach($g in $comments)
						#set($size = $foreach.index+1)	
						<div style="float:left;padding-left:15px;width:97%;font-size:42px;font-family:Microsoft YaHei;line-height:75px;vertical-algin:middle;color:#207583;">$size、$g</div>
						#end
					</td>	
				</tr>
			</table>
			</div>
			
			<div style="position: absolute; top: 2270px; left: 580px; width: 1760px; height: 1038px;">
				#foreach($i in $urllist)
				#if($foreach.index == 0)
				<div style="width:544px;height:408px;margin-left:10px;float:left;background-image: url($i?x-oss-process=image/resize,m_fill,w_544,h_408/quality,q_50); background-size: 100% 100%;"></div>
				#end
				#if($foreach.index == 1)
				<div style="width:544px;height:408px;margin-left:50px;float:left;background-image: url($i?x-oss-process=image/resize,m_fill,w_544,h_408/quality,q_50); background-size: 100% 100%;"></div>
				#end
				#if($foreach.index == 2)
				<div style="width:544px;height:408px;margin-right:20px;float:right;background-image: url($i?x-oss-process=image/resize,m_fill,w_544,h_408/quality,q_50); background-size: 100% 100%;"></div>
				#end
				#if($foreach.index == 3)
				<div style="  margin-top:25px;width:800px;height:600px;margin-left:30px;  float:left;background-image: url($i?x-oss-process=image/resize,m_fill,w_800,h_600/quality,q_50); background-size: 100% 100%;"></div>
				#end
				#if($foreach.index == 4)
				<div style="  margin-top:25px;width:800px;height:600px;margin-left:90px;  float:left;background-image: url($i?x-oss-process=image/resize,m_fill,w_800,h_600/quality,q_50); background-size: 100% 100%;"></div>
				#end
				#end
			</div>
	</div>
</body>
</html>