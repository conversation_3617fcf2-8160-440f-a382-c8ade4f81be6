
<div class="daily-plan">
	<table width="100%">
    <tr>
        <td colspan=3 style="height:45px; align:right;vertical-align:middle;">
    		<select name="list_code" id="list_code" class="form-control info-select" style="display:inline-block;vertical-align:middle;">
                <option value="">--请选择季报--</option>
                <option #if($!param.code == "JB_YY") selected #end value="JB_YY">语言课程</option>
            </select>
        </td>
    </tr>
	<tr>
        <td colspan=3 style="height:50px; align:right;">
        	<a class="easyui-linkbutton" id="button_search" style="margin-left:15px;margin-top:12px;padding: 5px 9px 5px 9px;color:#fff;border:none;background:#63B8FF;border-radius:3px;" href="javascript:openSubWin();" >查询</a>
        </td>
    </tr>
	<tr>
        <td align="center">报告日期</td>
        <td align="center">课程</td>
        <td align="center">操作</td>
    </tr>
    #foreach($!detail in $!zblist)
	<tr>
        <td>$!detail.date</td>
        <td>$!detail.course</td>
        <td style="height:45px; align:right;">
        	<a class="easyui-linkbutton" id="button_sportsTeachingArray$!{result.id}$foreach.index" style="margin-left:15px;margin-top:12px;padding: 5px 9px 5px 9px;color:#fff;border:none;background:#63B8FF;border-radius:3px;" href="javascript:add_jb_imgs('$!detail.id', 0);" >新增</a>
        </td>
    </tr>
    #end
    </table>

</div>