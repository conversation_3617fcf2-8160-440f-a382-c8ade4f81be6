<!DOCTYPE html>
<html lang="en">
	
<style>
	
</style>
<head>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width; initial-scale=0.38; maximum-scale=1.5; user-scalable=1"/>
    <title>日报详情</title>
    <style>
        *{
            font-family: MicrosoftYaHei;
            margin: auto;
        }
		table td,
				table th {
				  border: 1px #A3DF7F solid;
				  padding: 1px 1px;
				}
		span, label{vertical-align:middle;}
		/*复选框（多选框）的美化CSS*/
		input[type="checkbox"]{appearance: none; -webkit-appearance: none;outline: none;display:none}
		label{display:inline-block;cursor:pointer;}
		label input[type="checkbox"] + label{margin-top:-1px;width:8px;height:8px;top:0;left:0;background: #FFF;border: 1px solid black;margin-right:3px;}
		label input[type="checkbox"]:checked + label:after{content: "\2713";font-size: 9px;position: relative;top:-5px;left:1px;}
		
    </style>
</head>
<body>
	#if($!isAbsence==1)
	
	<div style="width:1008px;height:720px;background-image: url(#if($!isQmy==1)https://qimiaoyuan-app.oss-cn-hangzhou.aliyuncs.com/image/backgroud/RB_YRSH_QMY.jpeg#end#if($!isQmy==0)https://qimiaoyuan-app.oss-cn-hangzhou.aliyuncs.com/image/backgroud/RB_YRSH_TYY.jpeg#end); background-size: 100% ;">
		
		<div style="width:888px; padding-top:110px;font-size:13px;font-family:MicrosoftYaHei;">
			<div style="width:100%;height:30px;"></div>
			#if($!isQmy == 1)
			<div style="width:100%;height:24px;margin-top:2px;">
				<div style="float:left;width:90px;height:90%;margin-left:236px;"><label><input type="checkbox" disabled #if($!emotion ==1) checked #end ><label></label>平静放松</label></div>
				<div style="float:left;width:90px;height:90%;margin-left:8px;"><label><input type="checkbox" disabled #if($!emotion ==2) checked #end ><label></label>焦虑不安</label></div>
				<div style="float:left;width:90px;height:90%;margin-left:8px;"><label><input type="checkbox" disabled #if($!emotion ==3) checked #end ><label></label>悲伤难过</label></div>
				<div style="float:left;width:90px;height:90%;margin-left:8px;"><label><input type="checkbox" disabled #if($!emotion ==4) checked #end ><label></label>愤怒暴躁</label></div>
				<div style="float:left;width:96px;height:90%;margin-left:8px;"><label><input type="checkbox" disabled #if($!emotion ==5) checked #end ><label></label>传染病预警</label></div>
				<div style="float:left;width:160px;height:90%;font-size:10px;word-wrap: break-word;overflow:hidden;">$!emotionRemarks</div>
			</div>
			<div style="width:100%;height:23px;margin-top:1px;">
				<div style="float:left;width:90px;height:90%;margin-left:326px;"><label><input type="checkbox" disabled #if($!morning ==1) checked #end ><label></label>正常</label></div>
				<div style="float:left;width:90px;height:90%;margin-left:158px;"><label><input type="checkbox" disabled #if($!morning ==2) checked #end ><label></label>异常</label></div>
				<div style="float:left;width:160px;height:90%;margin-left:60px;font-size:10px;word-wrap: break-word;overflow:hidden;">$!morningRemarks</div>
			</div>
			<div style="width:100%;height:23px;margin-top:1px;">
				<div style="float:left;width:90px;height:90%;margin-left:276px;"><label><input type="checkbox" disabled #if($!entrust ==0) checked #end ><label></label>无</label></div>
				<div style="float:left;width:90px;height:90%;margin-left:13px;"><label><input type="checkbox" disabled #if($!entrust ==1) checked #end ><label></label>需要观察</label></div>
				<div style="float:left;width:90px;height:90%;margin-left:34px;"><label><input type="checkbox" disabled #if($!entrust ==2) checked #end ><label></label>委托吃药</label></div>
				<div style="float:left;width:90px;height:90%;margin-left:28px;"><label><input type="checkbox" disabled #if($!entrust ==3) checked #end ><label></label>传染病预警</label></div>
				<div style="float:left;width:160px;height:90%;margin-left:12px;font-size:10px;word-wrap: break-word;overflow:hidden;">$!entrustRemarks</div>
			</div>
			<div style="width:100%;height:23px;margin-top:1px;">
				<div style="float:left;width:90px;height:90%;margin-left:266px;"><label><input type="checkbox" disabled #if($!breakfast ==1) checked #end ><label></label>适量</label></div>
				<div style="float:left;width:90px;height:90%;margin-left:43px;"><label><input type="checkbox" disabled #if($!breakfast ==2) checked #end ><label></label>多</label></div>
				<div style="float:left;width:90px;height:90%;margin-left:34px;"><label><input type="checkbox" disabled #if($!breakfast ==3) checked #end ><label></label>少</label></div>
				<div style="float:left;width:82px;height:90%;margin-left:28px;"><label><input type="checkbox" disabled #if($!breakfast ==0) checked #end ><label></label>无</label></div>
				<div style="float:left;width:160px;height:90%;margin-left:0px;font-size:10px;word-wrap: break-word;overflow:hidden;">$!breakfastRemarks</div>
			</div>
			<div style="width:100%;height:23px;margin-top:1px;">
				<div style="float:left;width:90px;height:90%;margin-left:266px;"><label><input type="checkbox" disabled #if($!staple ==1) checked #end ><label></label>适量</label></div>
				<div style="float:left;width:90px;height:90%;margin-left:43px;"><label><input type="checkbox" disabled #if($!staple ==2) checked #end ><label></label>多</label></div>
				<div style="float:left;width:90px;height:90%;margin-left:34px;"><label><input type="checkbox" disabled #if($!staple ==3) checked #end ><label></label>少</label></div>
				<div style="float:left;width:82px;height:90%;margin-left:28px;"><label><input type="checkbox" disabled #if($!staple ==0) checked #end ><label></label>无</label></div>
				<div style="float:left;width:160px;height:90%;margin-left:0px;font-size:10px;word-wrap: break-word;overflow:hidden;">$!stapleRemarks</div>
			</div>
			<div style="width:100%;height:23px;margin-top:1px;">
				<div style="float:left;width:90px;height:90%;margin-left:266px;"><label><input type="checkbox" disabled #if($!dish ==1) checked #end ><label></label>适量</label></div>
				<div style="float:left;width:90px;height:90%;margin-left:43px;"><label><input type="checkbox" disabled #if($!dish ==2) checked #end ><label></label>多</label></div>
				<div style="float:left;width:90px;height:90%;margin-left:34px;"><label><input type="checkbox" disabled #if($!dish ==3) checked #end ><label></label>少</label></div>
				<div style="float:left;width:82px;height:90%;margin-left:28px;"><label><input type="checkbox" disabled #if($!dish ==0) checked #end ><label></label>无</label></div>
				<div style="float:left;width:160px;height:90%;margin-left:0px;font-size:10px;word-wrap: break-word;overflow:hidden;">$!dishRemarks</div>
			</div>
			<div style="width:100%;height:23px;margin-top:2px;">
				<div style="float:left;width:90px;height:90%;margin-left:248px;"><label><input type="checkbox" disabled #if($!siesta ==1) checked #end ><label></label>1-2小时</label></div>
				<div style="float:left;width:90px;height:90%;margin-left:32px;"><label><input type="checkbox" disabled ><label></label>0.5-1小时</label></div>
				<div style="float:left;width:90px;height:90%;margin-left:36px;"><label><input type="checkbox" disabled #if($!siesta ==3) checked #end ><label></label>少于半小时</label></div>
				<div style="float:left;width:82px;height:90%;margin-left:56px;"><label><input type="checkbox" disabled #if($!siesta ==2) checked #end ><label></label>没有午睡</label></div>
				<div style="float:left;width:160px;height:90%;margin-left:0px;font-size:10px;word-wrap: break-word;overflow:hidden;">$!siestaRemarks</div>
			</div>
			<div style="width:100%;height:23px;margin-top:1px;">
				<div style="float:left;width:90px;height:90%;margin-left:326px;"><label><input type="checkbox" disabled #if($!noon ==1) checked #end ><label></label>正常</label></div>
				<div style="float:left;width:90px;height:90%;margin-left:158px;"><label><input type="checkbox" disabled #if($!noon ==2) checked #end ><label></label>异常</label></div>
				<div style="float:left;width:160px;height:90%;margin-left:60px;font-size:10px;word-wrap: break-word;overflow:hidden;">$!noonRemarks</div>
			</div>
			<div style="width:100%;height:23px;margin-top:2px;">
				<div style="float:left;width:90px;height:90%;margin-left:266px;"><label><input type="checkbox" disabled #if($!snack ==1) checked #end ><label></label>适量</label></div>
				<div style="float:left;width:90px;height:90%;margin-left:43px;"><label><input type="checkbox" disabled #if($!snack ==2) checked #end ><label></label>多</label></div>
				<div style="float:left;width:90px;height:90%;margin-left:34px;"><label><input type="checkbox" disabled #if($!snack ==3) checked #end ><label></label>少</label></div>
				<div style="float:left;width:82px;height:90%;margin-left:28px;"><label><input type="checkbox" disabled #if($!snack ==0) checked #end ><label></label>无</label></div>
				<div style="float:left;width:160px;height:90%;margin-left:0px;font-size:10px;word-wrap: break-word;overflow:hidden;">$!snackRemarks</div>
			</div>
			<div style="width:100%;height:23px;margin-top:1px;">
				<div style="float:left;width:90px;height:90%;margin-left:326px;"><label><input type="checkbox" disabled #if($!late ==1) checked #end ><label></label>正常</label></div>
				<div style="float:left;width:90px;height:90%;margin-left:158px;"><label><input type="checkbox" disabled #if($!late ==2) checked #end ><label></label>异常</label></div>
				<div style="float:left;width:160px;height:90%;margin-left:60px;font-size:10px;word-wrap: break-word;overflow:hidden;">$!lateRemarks</div>
			</div>
			<div style="width:100%;height:23px;margin-top:1px;">
				<div style="float:left;width:90px;height:90%;margin-left:266px;"><label><input type="checkbox" disabled #if($!drink ==1) checked #end ><label></label>正常</label></div>
				<div style="float:left;width:90px;height:90%;margin-left:36px;"><label><input type="checkbox" disabled #if($!drink ==2) checked #end ><label></label>较多</label></div>
				<div style="float:left;width:90px;height:90%;margin-left:34px;"><label><input type="checkbox" disabled #if($!drink ==3) checked #end ><label></label>较少</label></div>
				<div style="float:left;width:82px;height:90%;margin-left:34px;"><label><input type="checkbox" disabled #if($!drink ==0) checked #end ><label></label>无</label></div>
				<div style="float:left;width:160px;height:90%;margin-left:0px;font-size:10px;word-wrap: break-word;overflow:hidden;">$!drinkRemarks</div>
			</div>
			<div style="width:100%;height:23px;margin-top:2px;">
				<div style="float:left;width:90px;height:90%;margin-left:250px;"><label><input type="checkbox" disabled #if($!shit ==1) checked #end ><label></label>有（正常）</label></div>
				<div style="float:left;width:90px;height:90%;margin-left:40px;"><label><input type="checkbox" disabled #if($!shit ==2) checked #end ><label></label>有（干）</label></div>
				<div style="float:left;width:90px;height:90%;margin-left:34px;"><label><input type="checkbox" disabled #if($!shit ==3) checked #end ><label></label>有（稀）</label></div>
				<div style="float:left;width:82px;height:90%;margin-left:46px;"><label><input type="checkbox" disabled #if($!shit ==0) checked #end ><label></label>无</label></div>
				<div style="float:left;width:160px;height:90%;margin-left:0px;font-size:10px;word-wrap: break-word;overflow:hidden;">$!shitRemarks</div>
			</div>
			<div style="width:100%;height:23px;margin-top:1px;">
				<div style="float:left;width:90px;height:90%;margin-left:290px;"><label><input type="checkbox" disabled #if($!ms ==1) checked #end ><label></label>积极</label></div>
				<div style="float:left;width:90px;height:90%;margin-left:75px;"><label><input type="checkbox" disabled #if($!ms ==2) checked #end ><label></label>被动</label></div>
				<div style="float:left;width:90px;height:90%;margin-left:64px;"><label><input type="checkbox" disabled #if($!ms ==3) checked #end ><label></label>未参加</label></div>
				<div style="float:left;width:160px;height:90%;margin-left:23px;font-size:10px;word-wrap: break-word;overflow:hidden;">$!msRemarks</div>
			</div>
			<div style="width:100%;height:23px;margin-top:1px;">
				<div style="float:left;width:90px;height:90%;margin-left:290px;"><label><input type="checkbox" disabled #if($!xxl ==1) checked #end ><label></label>积极</label></div>
				<div style="float:left;width:90px;height:90%;margin-left:75px;"><label><input type="checkbox" disabled #if($!xxl ==2) checked #end ><label></label>被动</label></div>
				<div style="float:left;width:90px;height:90%;margin-left:64px;"><label><input type="checkbox" disabled #if($!xxl ==3) checked #end ><label></label>未参加</label></div>
				<div style="float:left;width:160px;height:90%;margin-left:23px;font-size:10px;word-wrap: break-word;overflow:hidden;">$!xxlRemarks</div>
			</div>
			<div style="width:100%;height:23px;margin-top:1px;">
				<div style="float:left;width:90px;height:90%;margin-left:290px;"><label><input type="checkbox" disabled #if($!yy ==1) checked #end ><label></label>积极</label></div>
				<div style="float:left;width:90px;height:90%;margin-left:75px;"><label><input type="checkbox" disabled #if($!yy ==2) checked #end ><label></label>被动</label></div>
				<div style="float:left;width:90px;height:90%;margin-left:64px;"><label><input type="checkbox" disabled #if($!yy ==3) checked #end ><label></label>未参加</label></div>
				<div style="float:left;width:160px;height:90%;margin-left:23px;font-size:10px;word-wrap: break-word;overflow:hidden;">$!yyRemarks</div>
			</div>
			<div style="width:100%;height:23px;margin-top:1px;">
				<div style="float:left;width:90px;height:90%;margin-left:290px;"><label><input type="checkbox" disabled #if($!spark ==1) checked #end ><label></label>积极</label></div>
				<div style="float:left;width:90px;height:90%;margin-left:75px;"><label><input type="checkbox" disabled #if($!spark ==2) checked #end ><label></label>被动</label></div>
				<div style="float:left;width:90px;height:90%;margin-left:64px;"><label><input type="checkbox" disabled #if($!spark ==3) checked #end ><label></label>未参加</label></div>
				<div style="float:left;width:160px;height:90%;margin-left:23px;font-size:10px;word-wrap: break-word;overflow:hidden;">$!sparkRemarks</div>
			</div>
			<div style="width:100%;height:23px;margin-top:1px;">
				<div style="float:left;width:90px;height:90%;margin-left:290px;"><label><input type="checkbox" disabled #if($!sel ==1) checked #end ><label></label>积极</label></div>
				<div style="float:left;width:90px;height:90%;margin-left:75px;"><label><input type="checkbox" disabled #if($!sel ==2) checked #end ><label></label>被动</label></div>
				<div style="float:left;width:90px;height:90%;margin-left:64px;"><label><input type="checkbox" disabled #if($!sel ==3) checked #end ><label></label>未参加</label></div>
				<div style="float:left;width:160px;height:90%;margin-left:23px;font-size:10px;word-wrap: break-word;overflow:hidden;">$!selRemarks</div>
			</div>
			#end
			#if($!isQmy == 0)
			<div style="width:100%;height:24px;margin-top:2px;">
				<div style="float:left;width:90px;height:90%;margin-left:236px;"><label><input type="checkbox" disabled #if($!emotion ==1) checked #end ><label></label>平静放松</label></div>
				<div style="float:left;width:90px;height:90%;margin-left:8px;"><label><input type="checkbox" disabled #if($!emotion ==2) checked #end ><label></label>焦虑不安</label></div>
				<div style="float:left;width:90px;height:90%;margin-left:8px;"><label><input type="checkbox" disabled #if($!emotion ==3) checked #end ><label></label>悲伤难过</label></div>
				<div style="float:left;width:90px;height:90%;margin-left:8px;"><label><input type="checkbox" disabled #if($!emotion ==4) checked #end ><label></label>愤怒暴躁</label></div>
				<div style="float:left;width:96px;height:90%;margin-left:8px;"><label><input type="checkbox" disabled #if($!emotion ==5) checked #end ><label></label>传染病预警</label></div>
				<div style="float:left;width:160px;height:90%;font-size:10px;word-wrap: break-word;overflow:hidden;">$!emotionRemarks</div>
			</div>
			<div style="width:100%;height:23px;margin-top:1px;">
				<div style="float:left;width:90px;height:90%;margin-left:326px;"><label><input type="checkbox" disabled #if($!morning ==1) checked #end ><label></label>正常</label></div>
				<div style="float:left;width:90px;height:90%;margin-left:158px;"><label><input type="checkbox" disabled #if($!morning ==2) checked #end ><label></label>异常</label></div>
				<div style="margin-left:60px;float:left;width:160px;height:90%;font-size:10px;word-wrap: break-word;overflow:hidden;">$!morningRemarks</div>
			</div>
			<div style="width:100%;height:23px;margin-top:4px;">
				<div style="float:left;width:90px;height:90%;margin-left:276px;"><label><input type="checkbox" disabled #if($!entrust ==0) checked #end ><label></label>无</label></div>
				<div style="float:left;width:90px;height:90%;margin-left:13px;"><label><input type="checkbox" disabled #if($!entrust ==1) checked #end ><label></label>需要观察</label></div>
				<div style="float:left;width:90px;height:90%;margin-left:34px;"><label><input type="checkbox" disabled #if($!entrust ==2) checked #end ><label></label>委托吃药</label></div>
				<div style="float:left;width:90px;height:90%;margin-left:28px;"><label><input type="checkbox" disabled #if($!entrust ==3) checked #end ><label></label>传染病预警</label></div>
				<div style="float:left;width:160px;height:90%;margin-left:12px;font-size:10px;word-wrap: break-word;overflow:hidden;">$!entrustRemarks</div>
			</div>
			<div style="width:100%;height:23px;margin-top:5px;">
				<div style="float:left;width:90px;height:90%;margin-left:266px;"><label><input type="checkbox" disabled #if($!breakfast ==1) checked #end ><label></label>适量</label></div>
				<div style="float:left;width:90px;height:90%;margin-left:43px;"><label><input type="checkbox" disabled #if($!breakfast ==2) checked #end ><label></label>多</label></div>
				<div style="float:left;width:90px;height:90%;margin-left:34px;"><label><input type="checkbox" disabled #if($!breakfast ==3) checked #end ><label></label>少</label></div>
				<div style="float:left;width:82px;height:90%;margin-left:28px;"><label><input type="checkbox" disabled #if($!breakfast ==0) checked #end ><label></label>无</label></div>
				<div style="float:left;width:160px;height:90%;margin-left:0px;font-size:10px;word-wrap: break-word;overflow:hidden;">$!breakfastRemarks</div>
			</div>
			<div style="width:100%;height:23px;margin-top:5px;">
				<div style="float:left;width:90px;height:90%;margin-left:266px;"><label><input type="checkbox" disabled #if($!staple ==1) checked #end ><label></label>适量</label></div>
				<div style="float:left;width:90px;height:90%;margin-left:43px;"><label><input type="checkbox" disabled #if($!staple ==2) checked #end ><label></label>多</label></div>
				<div style="float:left;width:90px;height:90%;margin-left:34px;"><label><input type="checkbox" disabled #if($!staple ==3) checked #end ><label></label>少</label></div>
				<div style="float:left;width:82px;height:90%;margin-left:28px;"><label><input type="checkbox" disabled #if($!staple ==0) checked #end ><label></label>无</label></div>
				<div style="float:left;width:160px;height:90%;margin-left:0px;font-size:10px;word-wrap: break-word;overflow:hidden;">$!stapleRemarks</div>
			</div>
			<div style="width:100%;height:23px;margin-top:5px;">
				<div style="float:left;width:90px;height:90%;margin-left:266px;"><label><input type="checkbox" disabled #if($!dish ==1) checked #end ><label></label>适量</label></div>
				<div style="float:left;width:90px;height:90%;margin-left:43px;"><label><input type="checkbox" disabled #if($!dish ==2) checked #end ><label></label>多</label></div>
				<div style="float:left;width:90px;height:90%;margin-left:34px;"><label><input type="checkbox" disabled #if($!dish ==3) checked #end ><label></label>少</label></div>
				<div style="float:left;width:82px;height:90%;margin-left:28px;"><label><input type="checkbox" disabled #if($!dish ==0) checked #end ><label></label>无</label></div>
				<div style="float:left;width:160px;height:90%;margin-left:0px;font-size:10px;word-wrap: break-word;overflow:hidden;">$!dishRemarks</div>
			</div>
			<div style="width:100%;height:23px;margin-top:4px;">
				<div style="float:left;width:90px;height:90%;margin-left:248px;"><label><input type="checkbox" disabled #if($!siesta ==1) checked #end ><label></label>1-2小时</label></div>
				<div style="float:left;width:90px;height:90%;margin-left:32px;"><label><input type="checkbox" disabled ><label></label>0.5-1小时</label></div>
				<div style="float:left;width:90px;height:90%;margin-left:36px;"><label><input type="checkbox" disabled #if($!siesta ==3) checked #end ><label></label>少于半小时</label></div>
				<div style="float:left;width:82px;height:90%;margin-left:56px;"><label><input type="checkbox" disabled #if($!siesta ==2) checked #end ><label></label>没有午睡</label></div>
				<div style="float:left;width:160px;height:90%;margin-left:0px;font-size:10px;word-wrap: break-word;overflow:hidden;">$!siestaRemarks</div>
			</div>
			<div style="width:100%;height:23px;margin-top:4px;">
				<div style="float:left;width:90px;height:90%;margin-left:326px;"><label><input type="checkbox" disabled #if($!noon ==1) checked #end ><label></label>正常</label></div>
				<div style="float:left;width:90px;height:90%;margin-left:158px;"><label><input type="checkbox" disabled #if($!noon ==2) checked #end ><label></label>异常</label></div>
				<div style="margin-left:60px;float:left;width:160px;height:90%;font-size:10px;word-wrap: break-word;overflow:hidden;">$!noonRemarks</div>
			</div>
			<div style="width:100%;height:23px;margin-top:4px;">
				<div style="float:left;width:90px;height:90%;margin-left:266px;"><label><input type="checkbox" disabled #if($!snack ==1) checked #end ><label></label>适量</label></div>
				<div style="float:left;width:90px;height:90%;margin-left:43px;"><label><input type="checkbox" disabled #if($!snack ==2) checked #end ><label></label>多</label></div>
				<div style="float:left;width:90px;height:90%;margin-left:34px;"><label><input type="checkbox" disabled #if($!snack ==3) checked #end ><label></label>少</label></div>
				<div style="float:left;width:82px;height:90%;margin-left:28px;"><label><input type="checkbox" disabled #if($!snack ==0) checked #end ><label></label>无</label></div>
				<div style="float:left;width:160px;height:90%;margin-left:0px;font-size:10px;word-wrap: break-word;overflow:hidden;">$!snackRemarks</div>
			</div>
			<div style="width:100%;height:23px;margin-top:4px;">
				<div style="float:left;width:90px;height:90%;margin-left:326px;"><label><input type="checkbox" disabled #if($!late ==1) checked #end ><label></label>正常</label></div>
				<div style="float:left;width:90px;height:90%;margin-left:158px;"><label><input type="checkbox" disabled #if($!late ==2) checked #end ><label></label>异常</label></div>
				<div style="margin-left:60px;float:left;width:160px;height:90%;font-size:10px;word-wrap: break-word;overflow:hidden;">$!lateRemarks</div>
			</div>
			<div style="width:100%;height:23px;margin-top:4px;">
				<div style="float:left;width:90px;height:90%;margin-left:266px;"><label><input type="checkbox" disabled #if($!drink ==1) checked #end ><label></label>正常</label></div>
				<div style="float:left;width:90px;height:90%;margin-left:36px;"><label><input type="checkbox" disabled #if($!drink ==2) checked #end ><label></label>较多</label></div>
				<div style="float:left;width:90px;height:90%;margin-left:34px;"><label><input type="checkbox" disabled #if($!drink ==3) checked #end ><label></label>较少</label></div>
				<div style="float:left;width:82px;height:90%;margin-left:34px;"><label><input type="checkbox" disabled #if($!drink ==0) checked #end ><label></label>无</label></div>
				<div style="float:left;width:160px;height:90%;margin-left:0px;font-size:10px;word-wrap: break-word;overflow:hidden;">$!drinkRemarks</div>
			</div>
			<div style="width:100%;height:23px;margin-top:5px;">
				<div style="float:left;width:90px;height:90%;margin-left:250px;"><label><input type="checkbox" disabled #if($!shit ==1) checked #end ><label></label>有（正常）</label></div>
				<div style="float:left;width:90px;height:90%;margin-left:40px;"><label><input type="checkbox" disabled #if($!shit ==2) checked #end ><label></label>有（干）</label></div>
				<div style="float:left;width:90px;height:90%;margin-left:34px;"><label><input type="checkbox" disabled #if($!shit ==3) checked #end ><label></label>有（稀）</label></div>
				<div style="float:left;width:82px;height:90%;margin-left:46px;"><label><input type="checkbox" disabled #if($!shit ==0) checked #end ><label></label>无</label></div>
				<div style="float:left;width:160px;height:90%;margin-left:0px;font-size:10px;word-wrap: break-word;overflow:hidden;">$!shitRemarks</div>
			</div>
			<div style="width:100%;height:23px;margin-top:5px;">
				<div style="float:left;width:90px;height:90%;margin-left:290px;"><label><input type="checkbox" disabled #if($!xxl ==1) checked #end ><label></label>积极</label></div>
				<div style="float:left;width:90px;height:90%;margin-left:75px;"><label><input type="checkbox" disabled #if($!xxl ==2) checked #end ><label></label>被动</label></div>
				<div style="float:left;width:90px;height:90%;margin-left:64px;"><label><input type="checkbox" disabled #if($!xxl ==3) checked #end ><label></label>未参加</label></div>
				<div style="float:left;width:160px;height:90%;margin-left:23px;font-size:10px;word-wrap: break-word;overflow:hidden;">$!xxlRemarks</div>
			</div>
			<div style="width:100%;height:23px;margin-top:5px;">
				<div style="float:left;width:90px;height:90%;margin-left:290px;"><label><input type="checkbox" disabled #if($!spark ==1) checked #end ><label></label>积极</label></div>
				<div style="float:left;width:90px;height:90%;margin-left:75px;"><label><input type="checkbox" disabled #if($!spark ==2) checked #end ><label></label>被动</label></div>
				<div style="float:left;width:90px;height:90%;margin-left:64px;"><label><input type="checkbox" disabled #if($!spark ==3) checked #end ><label></label>未参加</label></div>
				<div style="float:left;width:160px;height:90%;margin-left:23px;font-size:10px;word-wrap: break-word;overflow:hidden;">$!sparkRemarks</div>
			</div>
			#end
		
		</div>
	</div>
	
	#end
</body>
</html>