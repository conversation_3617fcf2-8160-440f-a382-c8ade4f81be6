#set( $layout = "/layout/common-layout.vm")
<div style="margin:10px 0;"></div>
<div id="productcodeconfigSearchForm" style="padding:5px;height:auto">
    <div style="margin-bottom:5px">
        <a href="#" onclick="addProductCategorySpec()" class="easyui-linkbutton" iconCls="icon-add" plain="true">新增</a>
    </div>
</div>

<div id="productcodeconfig_input" class="easyui-window" title="参数配置管理" data-options="modal:true,closed:true,collapsible:false,minimizable:false,maximizable:false" style="width: 400px; padding: 10px;">
    <div style="padding:10px 0 10px 60px">
        <form id="productCategorySpecForm" method="post" action="/OLProduct/categoryspec/save">
            <input  name="id" type="hidden" value=""/>
            <input name="productCategoryId" type="hidden" value="$!{categoryId}"/>
            <table>
                <tr>
                    <td>名称</td>
                    <td><input type="text" autoComplete="off" name="name" value="$!entity.value" class="easyui-validatebox"  data-options="required:true"></td>
                </tr>
                <tr style="display: none;">
                    <td>类型</td>
                    <td>
                        <input type="radio" autoComplete="off" name="showType"  value="2" checked >文字
                        <input type="radio" autoComplete="off" name="showType"  value="1" >图片
                    </td>
                </tr>
                <tr>
                    <td>&nbsp;</td>
                    <td>
                        <input type="button"  value="提交" onclick="saveCatCodeConfig()"></input>
                        <input type="reset" value="重置" ></input>
                    </td>
                </tr>
            </table>
        </form>
    </div>
</div>
<div id="productCategoryConfigList" class="easyui-window" title="文字类" data-options="modal:true,closed:true,collapsible:false,minimizable:false,maximizable:false" style="width:700px; height: 400px;  padding: 10px;">
</div>
<table id="productcodeconfigTable"></table>

<script>
    $('#productcodeconfigTable').datagrid({
        url:'/OLProduct/categoryspec/list?filter_L_productCategoryId=${categoryId}',
        toolbar: '#productcodeconfigSearchForm',
        pagination:true,
        singleSelect:true,
        rownumbers:true,
        columns : [[
            {field : 'name',title : '名称',width:200},
            /*{
                field:'showType',title:'类型',width:200,
                formatter: function(value,row,index){
                    if(row.showType==1){
                        return '图片';
                    }else if(row.showType==2){
                        return '文字';
                    }
                }
            },*/
            {field : 'operate',title : '操作',width:230,
                formatter : function(value, row, index) {
                    return outPutFunction("editProductCategoryCodeConfig",row.id,"编辑")+outPutFunction("freezeProductCategoryCodeConfig",row.id,"删除")+
                            outPutFunction("showProductCategoryConfigList",row.id,"参数配置详情信息");
                }
            }
        ]]
    });
    
    function addProductCategorySpec() {
        jQuery.post('/OLProduct/categoryspec/checkSize',{'categoryId':"$!{categoryId}"},function(data){
            if(data >= 2){
                jQuery.messager.alert('Info','最多添加两种规格','info');
                return;
            } else {
                $("#productCategorySpecForm").form('clear');
                $("#productCategorySpecForm input[name='id']").val(0);
                $("#productCategorySpecForm input[name='type'][value='2']").attr('checked','checked');
                $("#productCategorySpecForm input[name='productCategoryId']").val('$!{categoryId}');
                $("#productcodeconfig_input").window('open');
            }
        });
    }

    //添加的时候要判断商品规格的个数，这里不能超过两个
    function editProductCategoryCodeConfig(infoid){
        $("#adminProductCategoryForm").form('clear');
       jQuery.post('/OLProduct/categoryspec/getById',{'id':infoid}, function(data){
           $("#productCategorySpecForm").form('load',data);
           $("#productcodeconfig_input").window('open');
       });
    }

    function freezeProductCategoryCodeConfig(uid){
        jQuery.messager.confirm('参数配置管理',"确定要删除吗?",function(r){
            if (r){
                jQuery.post("/OLProduct/categoryspec/delete",{'id':uid},function(data){
                    if(data=='success'){
                        $("#productcodeconfigTable").datagrid('reload');
                    }else{
                        jQuery.messager.alert('Info', "删除失败", 'info');
                    }
                });
            }
        });
    }

    function showProductCategoryConfigList(catid){
        $("#productCategoryConfigList").dialog({
            closed: false,
            cache: false,
            title:"类目参数配置",
            href: "/OLProduct/categorySpecDetail/toCategorySpecDetailVM?productCategoryId=$!{categoryId}&specId="+catid,
            width:800,
            modal: true
        });
    }

    $('#productcodeconfigForm').form({
        success:function(data){
            $('#add_productcodeconfig').window('close');
            var str = 'success';
            if(data == str){
                $("#productcodeconfigTable").datagrid('reload');
                $(".easyui-validatebox").val('');
            }else{
                jQuery.messager.alert('Info', "添加失败", 'info');
            }
        }
    });

    //要判断如果是图片类型的只能添加一个
    function saveCatCodeConfig(){
        //要先查数据库看图片特征的有没有，没有的话添加，有的话就不能再添加了
        //得到选中的类型如果选的是图片需要判断，如果不是图片就没必要判断
        var checked = $('input[name="type"]:checked').val();
        //如果是修改的话就不用判断了
        if(checked==1){//选中的是图片类型
            jQuery.post("/OLProduct/categoryspec/checkSize",{'categoryId':'$!{categoryId}'},function(data){
                if(data>=1){
                    alert("图片规格特征最多一个");
                }else{
                    $('#productCategorySpecForm').form('submit',{
                        success : function(data) {
                            if(data =="success") {
                                $('#productcodeconfig_input').window('close');
                                $("#productcodeconfigTable").datagrid('reload');
                            }else {
                                jQuery.messager.alert('Info', "添加失败", 'info');
                            }
                        }
                    });
                }
            });
        }else{//文字类型就直接添加好了
            $('#productCategorySpecForm').form('submit',{
                success : function(data) {
                    if(data =="success") {
                        $('#productcodeconfig_input').window('close');
                        $("#productcodeconfigTable").datagrid('reload');
                    }else {
                        jQuery.messager.alert('Info', "添加失败", 'info');
                    }
                }
            });
        }
    }

</script>

