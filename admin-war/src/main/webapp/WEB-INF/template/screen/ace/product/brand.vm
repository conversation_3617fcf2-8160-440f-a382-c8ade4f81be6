#set( $layout = "/layout/easyui/easyui-layout.vm")

<div style="margin: 10px 0;"></div>
<div id="brandSearchForm1" style="padding:5px;height:auto">
    <div style="margin-bottom: 5px">
        <a href="javascript:void(0)" onclick="editBrand(0)" class="easyui-linkbutton" iconCls="icon-add" plain="true">新增品牌</a>
    </div>
    <div>
        #*<form id="queryBrandForm1">
            品牌名称： <input style="width:120px;margin-right:10px;" class="easyui-validatebox" name="filter_S_name_contains"/>
            <a href="javascript:doSearchObjectTree('brandTable1','queryBrandForm1')" class="easyui-linkbutton" iconCls="icon-search">搜索</a>
        </form>*#
    </div>
</div>

<!-- 编辑 -->
<div id="brand_edit_div1" class="easyui-window" title="品牌管理" data-options="modal:true,closed:true,collapsible:false,minimizable:false,maximizable:false" style="width:500px; height:280px; padding: 10px;">
    <div style="padding: 10px 0 10px 60px">
        <form id="adminBrandForm" method="post" action="/OLProduct/brand/save" enctype="multipart/form-data">
            <input name="id" id="brandId" type="hidden" value="0"/>
            <input name="parentId" id="parentId" type="hidden" value="0"/>
            <table>
                <tr>
                    <td>品牌名称</td>
                    <td><input type="text" autoComplete="off" name="name" class="easyui-validatebox" data-options="required:true"></td>
                </tr>
                <tr>
                    <td>手机端logo</td>
                    <td><input type="file" name="tlogo"></td>
                </tr>
                <tr id="brandLogotr">
                    <td></td>
                    <td id="imgTd"></td>
                </tr>
                <tr>
                    <td>&nbsp;</td>
                    <td>
                        <input type="submit" value="提交"></input>
                        <input type="reset" value="重置" ></input>
                    </td>
                </tr>
            </table>
        </form>
    </div>
</div>

<table id="brandTable1" ></table>

<script>
    $('#brandTable1').treegrid({
        url : '/OLProduct/brand/list',
        idField: 'id',
        treeField: 'name',
        toolbar : '#brandSearchForm1',
        pagination : true,
        singleSelect : true,
        rownumbers : true,
        columns : [[
            {field:'name',title:'品牌名称',align:'center',width:150},
            {field:'logo',title:'品牌LOGO',align:'center',width:100,
                formatter : function(value, row, index){
                    return "<img src='"+row.logo+"' style='width: 80px;height: 40px;'>"
                }
            },
            {field : 'operate',title : '操作',width:350,
                formatter : function(value, row, index) {
                    var url= outPutFunction("editBrand",row.id,"编辑");
                    url += outPutFunction("deleteBrand",row.id,"删除") ;
                    if(row.parentId == 0){
                        url += outPutFunction("addChildBrand", row.id, "添加子品牌");
                    }
                    return url;
                }
            }
        ]]
    });

    function addChildBrand(pid) {
        $("#adminBrandForm").form('clear');
        $("#brandLogotr").hide();
        $("#brandId").val(0);
        $("#parentId").val(pid);
        $('#brand_edit_div1').window('open');
    }

    //删除
    function deleteBrand(uid) {
        jQuery.messager.confirm('信息管理',"确定要删除吗?",function(r){
            if (r){
                jQuery.post("/OLProduct/brand/delete", {"id":uid}, function(data) {
                    if (data == 'success') {
                        $("#brandTable1").treegrid('reload');
                    } else {
                        jQuery.messager.alert('Info', "删除失败", 'info');
                    }
                });
            }
        })
    }
    //编辑
    function editBrand(_pid1){
        $("#adminBrandForm").form('clear');
        $("#brandLogotr").hide();
        $("#brandId").val(0);
        $("#parentId").val(0);
        if(_pid1 != 0){
            jQuery.post('/OLProduct/brand/getById',{'id':_pid1},function(data){
                $("#adminBrandForm").form('load',data);
                if(data.logo){
                    var path = data.logo;
                    $("#imgTd").html('<img src="'+path+'">');
                    $("#brandLogotr").show();
                }
            });
        }
        $('#brand_edit_div1').window('open');
    }

    $("#adminBrandForm").form({
        success : function(data){
            if(data == 'success'){
                $('#brand_edit_div1').window('close');
                $("#brandTable1").treegrid('reload');
            } else {
                jQuery.messager.alert('Info','操作失败','info');
            }
        }
    });
</script>

