#set( $layout = "/layout/easyui/h-layout.vm")

<body>
<div class="wrapper wrapper-content animated fadeInRight">
    <div class="row">
        <div class="col-sm-12" id="tableDiv">
            <div class="ibox float-e-margins">
                <div class="ibox-content">
                    <div class="col-sm-12 no-padding">
                        <button type="button" onclick="system_dictionary_add()" class="btn btn-success btn-sm" ><i class="fa fa-plus"></i>&nbsp;新增</button>
                    </div>

                    <form role="form" class="form-inline" id="system_dictionary_search_form">
                        <div class="form-group form-form-group">
                            <label>key值：</label>
                            <input type="text"  name="key" class="form-control input-sm">
                        </div>
                        <div class="form-group form-form-group">
                            <label >value值：</label>
                            <input type="text"  name="value" class="form-control input-sm">
                        </div>
                        <div class="form-group form-form-group">
                            <label >name值：</label>
                            <input type="text"  name="name" class="form-control input-sm">
                        </div>
                        <button class="btn btn-success btn-sm" type="button" onclick="system_search()"><i class="fa fa-search"></i>&nbsp;查询</button>

                    </form>
                    <!--字典表table-->
                    <table id="system_dictionary_table" class="table table-striped"></table>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="system_dictionary_view_div" class="modal fade" role="dialog" style="display: none;" aria-hidden="true">
    <div class="modal-dialog" >
        <div class="modal-content">
    <!--modald的header。存放标题什么的-->
    <form id="system_dictionary_form" class="form-horizontal" action=""  method="post"  enctype="multipart/form-data">
        <!--隐藏参数-->
        <input type="hidden" name="id" >


        <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal"></button>
            <h4 class="modal-title">字典管理</h4>
        </div>
        <!--modald的body。存放form里面的数据什么的-->

        <div class="modal-body">

            <div class="row form-horizontal">
                <div class="col-sm-12">
                    <div class="form-group">
                        <label class="col-sm-3 control-label"><span style="color: red;">*</span>key值:</label>
                        <div class="col-sm-8">
                                <input class="form-control"  type="text" name="key"   required="required">
                        </div>
                    </div>
                </div>
            </div>


            <div class="row form-horizontal">
                <div class="col-sm-12">
                    <div class="form-group">
                        <label class="col-sm-3 control-label"><span style="color: red;">*</span>name值:</label>
                        <div class="col-sm-8">
                            <input class="form-control"  type="text" name="name"   required="required">

                        </div>
                    </div>
                </div>
            </div>

            <div class="row form-horizontal">
                <div class="col-sm-12">
                    <div class="form-group">
                        <label class="col-sm-3 control-label"><span style="color: red;">*</span>value值:</label>
                        <div class="col-sm-8">
                            <input class="form-control"   name="value"   >

                        </div>
                    </div>
                </div>
            </div>

            <div class="row form-horizontal">
                <div class="col-sm-12">
                    <div class="form-group">
                        <label class="col-sm-3 control-label">图片（超过1MB将会被压缩）：</label>
                        <div class="col-sm-8">
                            <input  class="form-control "  name="imageFile" type="file"/>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row form-horizontal">
                <div class="col-sm-12">
                    <div class="form-group">
                        <label class="col-sm-3 control-label">描述</label>
                        <div class="col-sm-8">
                            <textarea  class="form-control "   name="desc"  maxlength="100"></textarea>
                        </div>
                    </div>
                </div>
            </div>
            <!--modald的footer。存放按钮神马的-->

          #*  <div class="form-group text-center">
                <button type="button" class="btn btn-default" data-dismiss="modal" onclick="closeDepositInput()">关闭</button>
                <button  class="btn btn-primary" type="submit">保存</button>*#

            <div class="modal-footer">
                <div class="row form-horizontal">
                    <div class=" col-sm-12">
                        <div class="form-group">

                            <div  class="col-sm-12 text-center" >
                                 <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                            <button  class="btn btn-primary" type="submit">保存</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
    </form>
        </div>
    </div>
    </div>

</body>
<script>


    //查询。查询方法统一使用system_search，方便回调函数调用
    function system_search(){
       $("#system_dictionary_table").bootstrapTable("refresh");
    }


    $("#system_dictionary_table").bootstrapTable({
        columns: [
            {field : 'key',title : 'Key值',width:100,align:"center"},
            {field : 'name',title : 'name值',width:150,align:"center"},
            {field : 'value',title : 'Value值',width:150,align:"center"},
            {field : 'desc',title : '描述',width:200,align:"center"},

            {field:'image',title:'图片',width:'150', align: "center",
                formatter : function(value, row, index) {
                    if(value!=null&&value!=""){
                    var src="$!{imgRoot}"+value;
                    //	return "<a target='_blank' href='"+value"'><img src='"+value+"' style='width:100px;height:100px'/></a>";
                    return "<a target='_blank'  href='"+src+"'><img src='"+src+"' style='width:50px;height:50px' /></a>";
                    }
                    return "";
                }
            },
            {field : 'operate',title : '操作',width:150,align:"center",
                formatter : function(value, row, index) {
                    var operate="&nbsp;&nbsp;&nbsp;<button class='btn btn-xs btn-warning' type='button' onclick='system_dictionary_edit(this,"+row.id+");'>编辑</button>"
                   
                    operate+="&nbsp;&nbsp;&nbsp;<button class='btn btn-danger btn-xs' type='button' onclick='system_dictionary_delete("+row.id+");'>删除</button>"
                    return operate;
                }
            }
        ],
        url: '/ace/system/dictionary/selectDictionary?sort=key&order=asc',
        cache:false,
        pagination: true,

        pageSize: 10,
        pageList: [10, 20],
        pageNumber:1,
        sidePagination: 'server',
        queryParams: function (params) {
            $('#system_dictionary_search_form').find('input[name]').each(function () {
                // //params
                var name=$(this).attr('name');
                var value=$(this).val();
                if(value==null||value==""){
                    value=null;
                }
                params[name]=value;

            });
            //实例化组件-自带参数
            params['offset']=params.offset;
            params['limit']=params.limit;
            params['sort']=params.sort;
            params['order']=params.order;

            return params;
        },
        onClickRow:function (row, $element, field){
                $element.addClass("row-select");
                $element.siblings('tr').removeClass("row-select");
        }
    });
    //新增。打开新的导航窗口新增
    function system_dictionary_add(){
        $("#system_dictionary_form").form("clear");
        $("#system_dictionary_form input[name='id']").val(0);
        remoteModal("system_dictionary_view_div","");

    }


    //编辑。打开新的窗口编辑
    function system_dictionary_edit(t,id){
        $("#system_dictionary_form").form("clear");
        var key=$(t).parent().parent().children(":first").html();
        var name=$(t).parent().parent().children().eq(1).html();
        var value=$(t).parent().parent().children().eq(2).html();
        var desc=$(t).parent().parent().children().eq(3).html();
        $("#system_dictionary_form").form("load",{"id":id,"key":key,"name":name,"value":value,"desc":desc});
            remoteModal("system_dictionary_view_div","");
        //$('#system_dictionary_view_div').modal({show:true,backdrop:true});
    }

    var validatorMenu=$("#system_dictionary_form").validate({

        submitHandler: function(form) {
            //默认
            //layer.load();
            //加遮罩层
            layer.load(4, {shade: [0.8, '#393D49']})
            //console.info("GGGGGGGGGGGGGGGG")

               var  url="/ace/system/dictionary/saveDictionary";
            $("#system_dictionary_form").attr('action',url);

            $(form).ajaxSubmit({

                //你表单提交就要切换页面，ajax加载又是异步的。两边同时进行，
                //当ajax加载在切换页面之前完成，就会执行回调，反之不会执行
                //加同步提交数据
                async: false,
                success:function(data){
                    //关闭加载
                    layer.closeAll('loading');
                    if(data == 'success'){
                        //刷新列表
                    //先刷新，后关闭。顺序不要反了
                        //刷新列表
                        //refreashTableInTabs( "/ace/sys/dictionary/list");
                        system_search()
                        //关闭窗口
                        $("#system_dictionary_view_div").modal("toggle");

                    }else{
                        layer.msg('保存失败', {
                            icon: 6
                        });
                    }
                }
            });
        }
    }) ;


    function system_dictionary_delete(dictionaryId) {
      var msg="确认要删除这条信息?";
        layer.confirm(msg, function(index){
            layer.load(4, {shade: [0.8, '#393D49']})
            //不要异步操作
            $.ajax({
                type : "post",
                url : "/ace/system/dictionary/delete/"+dictionaryId,
                async : false,
                success : function(data){
                    //关闭加载
                    layer.closeAll('loading');
                    if(data == 'success'){
                        $('#system_dictionary_table').bootstrapTable('refresh');

                    }else{
                        layer.msg('删除失败', {
                            icon: 2
                        });
                    }


                }
            });
            //最后手动关闭
            layer.close(index);

        });
    }
</script>
