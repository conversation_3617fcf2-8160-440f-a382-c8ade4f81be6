#set( $layout = "/layout/easyui/h-layout.vm")
<body class="gray-bg">
<div class="wrapper wrapper-content animated fadeInRight">
        <div class="row">
            <div class="col-sm-12">
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <h5>机构组织管理</h5>
                    </div>
                    <div class="ibox-content">
                        <div class="row form-horizontal">
                        </div>
                        
                        <div class="row">
                            <div class="col-sm-12">
                                <div id="toolbar">
                                    <button onclick="system_org_save(0);"   class="btn btn-primary" type="button">新增</button>
                                </div>
                            </div>
                        </div>
                       <div id="system_org_treeGrid"></div>
                    </div>
                </div>
            </div>
        </div>
</div>
<div id="system_org_div" class="modal fade" role="dialog" style="display: none;" aria-hidden="true">
                    <!--如果单行有两列数据，宽度为70%。单行一列数据，就不填写宽度-->           
    <div class="modal-dialog" >
        <div class="modal-content">
                 <!--modald的header。存放标题什么的-->
                  <form id="system_org_form" class="form-horizontal" action="/ace/system/org/save"  method="post"  enctype="multipart/form-data">
                  <!--隐藏参数-->
                  <input type="hidden" name="id">
                  <input  type="hidden" name="parentId"/>



                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal">×</button>
                        <h4 class="modal-title">机构管理管理</h4>
                    </div>
                      <!--modald的body。存放form里面的数据什么的-->
                    <div class="modal-body">
                        <!-- <div class="row form-horizontal">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">父机构：</label>
                                    <div class="col-sm-8">
                                      <input type="text"   #if($!{isView}==1) disabled="disabled" #end value="$!{storeHouse.address}" name="workUnit" class="form-control canReadBase" >
                                       <input  class="form-control " disabled="disabled" name="parentName"/>
                                       <input  type="hidden" name="parentId"/>
                        
                                    </div>
                                </div>
                            </div>
                        </div> -->
                        <div class="row form-horizontal">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label"><span style="color: red;">*</span>机构编号：</label>
                                    <div class="col-sm-8">
                                       <input  class="form-control "  name="number" required="required"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row form-horizontal">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label"><span style="color: red;">*</span>机构名称：</label>
                                    <div class="col-sm-8">
                                       <input  class="form-control "  name="name" required="required"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row form-horizontal">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">联系人：</label>
                                    <div class="col-sm-8">
                                       <input  class="form-control "  name="mobileName" />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row form-horizontal">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">联系电话：</label>
                                    <div class="col-sm-8">
                                       <input  class="form-control "  name="mobile"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row form-horizontal">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label"><span style="color: red;">*</span>机构KEY(建议名称拼音简写)：</label>
                                    <div class="col-sm-8">
                                       <input  class="form-control "  name="key" required="required"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row form-horizontal">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">描述</label>
                                    <div class="col-sm-8">
                                       <input  class="form-control "  name="desc" />
                                    </div>
                                </div>
                            </div>
                        </div>
                       

                    </div>
                
                    <!--modald的footer。存放按钮神马的-->
                    <div class="modal-footer">
                            
                         <div class="row form-horizontal">
                                <div class=" col-sm-12">
                                    <div class="form-group">
                                        <div  class="col-sm-12 text-center" >
                                             <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                                             <button  class="btn btn-primary" type="submit">保存</button>
                                        </div>
                                    </div>
                                </div>
                        </div>
                    </div>
                </form>
        </div>
    </div>
          
</div>
</body>    

<script type="text/javascript">
      
//获得商家的菜单目录
    getMenuList();

    //获得菜单的全部数据
    function getMenuList(){
        $.ajax({
            type:"get",
            url:"/ace/system/org/getTree",
            async:false,
            data:{},
            success:getMenuData
        });
    }

    //数据获得成功之后，构建treetable
    function getMenuData(data){
        var source =
                {
                    dataType: "json",
                    dataFields: [
                        { name: 'id', type: 'number' },
                        { name: 'number', type: 'string' },
                        { name: 'name', type: 'string' },
                        { name: 'mobile', type: 'string' }, 
                        { name: 'mobileName', type: 'string' },       
                        { name: 'key', type: 'string' },  
                        { name: 'desc', type: 'string' },  
                
                        {name: 'children', type: 'array' },
                        
                        
                        { name: 'expanded', type: 'bool' }
                    ],
                    timeout: 10000,
                    hierarchy:
                            {
                                root: 'children'
                            },
                    id: 'id',
                    localData: data
                };
        var dataAdapter = new $.jqx.dataAdapter(source);
        $("#system_org_treeGrid").jqxTreeGrid(
                {
                    width: '100%',
                    source: dataAdapter,
                    columns: [
                        {text:'组织编号',dataField:'number',width:'10%',cellsAlign: 'center', align: "center"},
                        {text:'组织名称',dataField:'name',width:'15%',cellsAlign: 'center', align: "center"},
                        {text:'联系人',dataField:'mobileName',width:'10%',cellsAlign: 'center', align: "center"},
                        {text:'联系电话',dataField:'mobile',width:'10%',cellsAlign: 'center', align: "center"},
                    
                        {text:'KEY',dataField:'key',width:'10%',cellsAlign: 'center', align: "center"},
                        {text:'备注',dataField:'desc',width:'20%',cellsAlign: 'center', align: "center"},
                       

                        {
                            text: '操作', dataField: 'id', width: '20%',cellsAlign: 'center', align: "center",
                            cellsRenderer: function (rowKey, dataField, value, data) {
                                var edit ="&nbsp;&nbsp;&nbsp;&nbsp;<button class='btn btn-sm btn-primary' type='button' onclick='system_org_save("+data.id+","+data.parentId+");'>编辑</button>";
                               
                                var del ="&nbsp;&nbsp;&nbsp;&nbsp;<button class='btn btn-sm btn-danger' type='button' onclick='system_org_delete("+data.id+");'>删除</button>";
                               
                                var addChildren ="&nbsp;&nbsp;&nbsp;&nbsp;<button class='btn btn-sm btn-warning' type='button' onclick='system_org_save("+0+","+data.id+");'>新增下级组织</button>";


                               // if(data.parentId==0){
                                    return edit+del+addChildren
                                //}else{
                                 //   return edit+del;
                               //}
                            }
                        }
                    ]
                });
    }
    
    function system_org_save(id,parentId){
          $("#system_org_form").form("clear");
           $("#system_org_form").form("load",{"id":id,"parentId":parentId});
        //新增根类目
        if(id!=null&&id!=0){
            $.post("/ace/system/org/findOne",{"id":id},function(data){
                    $("#system_org_form").form("load",data);
            });
        }
           remoteModal("system_org_div",null)

    }


     //删除
     function system_org_delete(id){
            
               var msg="是否确认删除，同时删除该机构下的下级机构?";
               
              layer.confirm(msg, function(index){
                      //do something
                      layer.load(4, {shade: [0.8, '#393D49']})
                      //不要异步操作
                      $.ajax({  
                         type : "post",  
                          url : "/ace/system/org/delete/"+ id,
                          data : {},  
                          async : false,  
                          success : function(data){  
                                            //关闭加载
                                            layer.closeAll('loading');
                                            if(data == 'success'){
                                                        
                                                   //刷新列表
                                                getMenuList();
                                                //关闭窗口
                                               //  closeModal("system_org_div")

                                                    }else{
                                                        layer.msg('删除失败', {
                                                            icon: 2
                                                        });
                                                    }


                                          }  
                            }); 
                        //最后手动关闭 
                      layer.close(index);

                    });  

            

     }


        var e="<i class='fa fa-times-circle'></i> ";
        var validatorMenu=$("#system_org_form").validate({

            submitHandler: function(form) {
                //默认
                //layer.load();
                //加遮罩层
                layer.load(4, {shade: [0.8, '#393D49']})
             
               
            $(form).ajaxSubmit({
               
                //你表单提交就要切换页面，ajax加载又是异步的。两边同时进行，
                //当ajax加载在切换页面之前完成，就会执行回调，反之不会执行
                //加同步提交数据
                    async: false,
                    success:function(data){
                        //关闭加载
                           layer.closeAll('loading');
                        if(data == 'success'){
                            
                               //刷新列表
                                getMenuList();
                                //关闭窗口
                                 closeModal("system_org_div")

                        }else{
                            layer.msg('保存失败', {
                                icon: 6
                            });
                        }
                    }
                });
            }
        }) ;


</script>
