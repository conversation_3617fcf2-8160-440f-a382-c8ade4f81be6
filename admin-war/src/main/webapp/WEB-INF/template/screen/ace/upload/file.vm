#set( $layout = "/layout/easyui/h-layout.vm")
<body class="gray-bg">
<div class="wrapper wrapper-content animated fadeInRight">
    <div class="row">
        <div class="col-sm-12" id="tableDiv">
            <div class="ibox float-e-margins">
                <div class="ibox-content">
                    <div class="col-sm-12 no-padding">
                    <button type="button" onclick="sunday_upload_add()" class="btn btn-success btn-sm" ><i class="fa fa-plus"></i>&nbsp;上传附件</button>
                    </div>

                    <form role="form" class="form-inline" id="sunday_upload_search_form">
                        <input name="outClassId" value="$!{outClassId}" type="hidden"/> 
                        <input name="outClassName" value="$!{outClassName}" type="hidden"/> 
                    </form> 
                    <!--客户表table-->
                    <table id="sunday_upload_table" class="table table-striped"data-sort-name="id" 	data-sort-order="DESC"></table>
                </div>
            </div>
        </div>
    </div>
</div>
<!--附件编辑/查看框-->
<div id="sunday_upload_div" class="modal fade" role="dialog" style="display: none;" aria-hidden="true">  
                     <div class="modal-dialog" >
        <div class="modal-content">
                 <!--modald的header。存放标题什么的-->
                <form id="sunday_upload_form" class="form-horizontal" action="/web/file/uploadSingleFileWithRecord"  method="post"  enctype="multipart/form-data">
                  <!--隐藏参数-->
                  <input type="hidden" name="id" > 
                  <input type="hidden" name="outClassId"/>
                  <input type="hidden" name="outClassName"/>
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal">×</button>
                        <h4 class="modal-title">附件管理</h4>
                    </div>
                      <!--modald的body。存放form里面的数据什么的-->
                    <div class="modal-body">
                        
                        <div class="row form-horizontal">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">类型：</label>
                                    <div class="col-sm-8">
                                       <input  class="form-control "  name="remark" />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row form-horizontal">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">附件：</label>
                                    <div class="col-sm-8">
                                       <!-- <input  class="form-control " type="file"  name="sunday_file"  id="sunday_file"/> -->
                                         <input  class="form-control " type="file"  name="file"  />
                                    </div>
                                </div>
                            </div>
                        </div>
                       

                    </div>
                
                    <!--modald的footer。存放按钮神马的-->
                    <div class="modal-footer">
                            <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                            <button  class="btn btn-primary" type="submit">保存</button>
                             
                    </div>
                </form>
        </div>
    </div>           
   
          
</div>
</body>
<script>
   

    //查询。查询方法统一使用finance_search，方便回调函数调用
    function sunday_upload_search(){
         var $sunday_upload_table=$('#sunday_upload_table'),
         $sunday_upload_search_form=$('#sunday_upload_search_form');

         bootstrapTableSearch(
            $sunday_upload_search_form,
            $sunday_upload_table,
            '$!{adminRoot}/web/file/selectUploadFile?');
        
    }
  
           
        $("#sunday_upload_table").bootstrapTable({
            columns: [
            {field : 'url',title : '文件',width:200,
                formatter : function(value, row, index) {
                    if(value!=null&&value!=""){
                        console.info("A")
                           if(value.indexOf("jpg")!=-1||value.indexOf("png")!=-1||value.indexOf("jpng")!=-1) {
                            var image="<a class ='text-warning'title='点击打开，查看详细' target='_blank' href='"+row.url+"'><img src='"+row.url+"' style='width:100px;height:100px'></img></a>";
                             console.info("B")
                            return image;
                           }else{
                            var aTag="<a class ='text-warning'title='点击打开，查看详细' target='_blank' href='"+row.url+"'>点击打开文件</a>";
                             console.info("C")
                            return aTag;
                           }
                    }else{
                         console.info("D")
                        return "";
                    }
                   //编码提供查看入口。打开新窗口
                   /* var image="<a class ='text-warning'title='点击打开，查看详细' target='_blank' href='"+row.url+"'><img src='"+row.url+"' style='width:100px;height:100px'></img></a>"
                    // var view =  "<a class = 'text-warning' href='javascript:sunday_upload_view("+row.id+")'>"+row.number+"</a>";
                    return image;*/
                }
            },
              {field : 'remark',title : '类型',width:100},
            {field : 'type',title : '文件类型',width:100},
            {field : 'size',title : '文件大小(KB)',width:100},
          
            {field : 'createTime',title : '上传时间',width:100},
            
           
            {field : 'operate',title : '操作',width:250,
                formatter : function(value, row, index) {
                  //  var edit='[<a href="javascript:sunday_upload_edit('+row.id+')">编辑</a>]'
                   /* var edit ="&nbsp;&nbsp;&nbsp;&nbsp;<button class='btn btn-sm btn-primary' type='button' onclick='sunday_upload_edit("+row.id+");'>编辑</button>";*/
                               
                    var del ="&nbsp;&nbsp;&nbsp;&nbsp;<button class='btn btn-sm btn-warning' type='button' onclick='sunday_upload_delete("+row.id+");'>删除</button>";
                               
                     return del;
                    //return edit+del;
                }
            }],
            url: '/web/file/selectUploadFile?outClassId=$!{outClassId}&outClassName=$!{outClassName}',
            cache:false,
            pagination: true,

            pageSize: 10,
            pageList: [10, 20],
            pageNumber:1,
            sidePagination: 'server',
            /*queryParams: function (params) {
                return {
                    page:params.offset/params.limit+1,
                    rows:params.limit
                }
            },*/
            onClickRow:function (row, $element, field){
                    $element.addClass("row-select");
                    $element.siblings('tr').removeClass("row-select");
            }
        });
    //新增。
    function sunday_upload_add(id){
        $("#sunday_upload_form").form("clear");
        $("#sunday_upload_form").form("load",{"id":0,"outClassId":"$!{outClassId}","outClassName":"$!{outClassName}"});
        remoteModal("sunday_upload_div",null);
    }
    /*//删除
    function sunday_upload_edit(id){
        $("#sunday_upload_form").form("clear");
        var selectedRows=$("#sunday_upload_table").bootstrapTable("getSelections");
        if(selectedRows!=null&&selectedRows.length>0&typeof(selectedRows)!="undefined"){
                $("#sunday_upload_form").load(selectedRows[0]);
        }else{
            layer.msg('请选择要编辑的文件', { icon: 6});
            return;
        }
        $.post("/web/file/findOne?id="+id,function(data){
            $("#sunday_upload_form").load(data.result);
        })
        //$("#sunday_upload_form").form("load",{"id":0,"outClassId":"$!{outClassId}","outClassName":"$!{outClassName}"});
        remoteModal("sunday_upload_div",null);
    }*/
    function sunday_upload_delete(id){
        var msg="是否确认删除该附件?";
            layer.confirm(msg, function(index){
                //do something
                layer.load(4, {shade: [0.8, '#393D49']})
                //不要异步操作
                $.ajax({
                    type : "post",
                    url : "/web/file/delete",
                    data : {"id":id},
                    async : false,
                    success : function(data){
                        //关闭加载
                        layer.closeAll('loading');
                        if(data.code == 0){
                                sunday_upload_search();
                            //刷新列表
                         //   getMenuList();
                            //关闭窗口
                            // closeModal("system_menu_div")

                        }else{
                            layer.msg('删除失败', {
                                icon: 2
                            });
                        }

                    }
                });
                //最后手动关闭
                layer.close(index);

            });
    }



    //实例化表单
         var e="<i class='fa fa-times-circle'></i> ";

        var validatorMenu=$("#sunday_upload_form").validate({
             /*  rules:{
               // name:"required",
                //sort:"required",
                sunday_file:{
                    onlyAcceptImg:true
                }
            },
            messages:{
               // name:e+"请输入分类名称",
                //sort:e+"请输入菜单排序",
                sunday_file:e+"只支持jpeg,jpg,png图片格式"
            },*/
            submitHandler: function(form) {
                
                //加遮罩层
                layer.load(4, {shade: [0.8, '#393D49']})
               
          

            $(form).ajaxSubmit({
               
                //你表单提交就要切换页面，ajax加载又是异步的。两边同时进行，
                //当ajax加载在切换页面之前完成，就会执行回调，反之不会执行

                //加同步提交数据
                    //async: false,
                    success:function(data){
                        //关闭加载
                           layer.closeAll('loading');
                        if(data.code == 0){
                            
                            //刷新列表      
                             sunday_upload_search();
                             //关闭窗口
                             $("#sunday_upload_div").modal("toggle");

                        }else{
                            layer.msg('上传失败', {
                                icon: 6
                            });
                        }
                    }
                });
            }
        }) ;

    
    /*$('#sunday_upload_form').form({
        success : function(data) {
            if(data.code == 0){
                            
                //刷新列表      
                 sunday_upload_search();
                 //关闭窗口
                 $("#sunday_upload_div").modal("toggle");

            }else{
                layer.msg('上传失败', {
                    icon: 6
                });
            }
        }
    });*/
</script>
