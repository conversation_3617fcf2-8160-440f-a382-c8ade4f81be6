<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- 日志存储目录 -->
    <property name="logHome" value="/users/jinyulei/logs" />

    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%date{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="rollingFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!--每次日志文件生成名称-->
        <file>${logHome}/yuhua.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!--日志存放位置，按时间来-->
            <fileNamePattern>${logHome}/yuhua.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <!--每日最大日志文件数目-->
            <MaxHistory>9999</MaxHistory>
            <TimeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <!--当日志文件达到10M时，将产生新的日志文件-->
                <MaxFileSize>100MB</MaxFileSize>
            </TimeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <encoder>
            <pattern>%date{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>
    <!--日志文件写入级别-->
    <logger name="org.itboys" level="WARN"/>
    <logger name="druid.sql.Statement" level="debug" />

    <root level="INFO">
        <appender-ref ref="console"/>
        <!--将INFO级别信息追加到日志文件中-->
        <appender-ref ref="rollingFile"/>
    </root>
</configuration>