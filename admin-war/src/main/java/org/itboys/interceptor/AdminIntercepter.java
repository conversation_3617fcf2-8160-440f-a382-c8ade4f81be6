package org.itboys.interceptor;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.itboys.commons.CommonConstants;
import org.itboys.commons.utils.tools.ListUtil;
import org.itboys.commons.utils.tools.LongUtil;
import org.itboys.framework.resource.ResourceHolder;
import org.itboys.framework.spring.interceptor.ServletContextInterceptor;
import org.itboys.mobile.dao.SundayDataMapper;
import org.itboys.mobile.entity.mongo.SundayParent;
import org.itboys.mobile.entity.mongo.SundayStudent;
import org.itboys.mobile.entity.mongo.SundayTeacher;
import org.itboys.mobile.entity.mongo.SundayVisitor;
import org.itboys.mobile.entity.mysql.SundayData;
import org.itboys.mobile.service.mongo.web.SundayParentService;
import org.itboys.mobile.service.mongo.web.SundayStudentService;
import org.itboys.mobile.service.mongo.web.SundayTeacherService;
import org.itboys.mobile.service.mongo.web.SundayVisitorService;
import org.itboys.param.ParamUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR> on 2016/8/11.
 */
public class AdminIntercepter extends ServletContextInterceptor{

	protected Logger logger = LoggerFactory.getLogger(getClass());
	private static final String SYSTEM_WEB = "/ace/system";
	private static final String SYSTEM_WEB2 = "/admin";
	private static final String SUNDAY_WEB = "/sunday/web";
	private static final String SUNDAY_SITE_GT = "/sunday/site/";
	private static final String SUNDAY_SITE_GT_LOGIN = "/sunday/site/login";
	private static final String SUNDAY_SITE_GT_DOLOGIN = "/sunday/site/doLogin";
	private static final String SUNDAY_SITE_GT_REGISTER = "/sunday/site/register";
	private static final String SUNDAY_SITE_GT_DOREGISTER = "/sunday/site/doRegister";
	/**
	 * 是否开启接口的权限校验。
	 * 开启。自行删除后台角色管理页面。template\screen\ace\sys\role-list.vm中【分配菜单权限】按钮
	 * 关闭。自行删除后台角色管理页面。template\screen\ace\sys\role-list.vm中【分配功能权限】按钮
	 */
	public static boolean is_open_operate_permission_check=true;

    @Autowired
    private ResourceHolder resourceHolder;


    @Autowired
	private SundayStudentService studentService;
    @Autowired
	private SundayTeacherService teacherService;

    @Autowired
	private SundayVisitorService visitorService;
    @Autowired
	private SundayDataMapper dataMapper;

    @Autowired
	private SundayParentService parentService;

    @Override
    public boolean preHandle(HttpServletRequest request,
                             HttpServletResponse response, Object handler) throws Exception {

        request.setAttribute("staticRoot", resourceHolder.getStringValue("staticRoot"));
        request.setAttribute("adminRoot", resourceHolder.getStringValue("adminRoot"));
        request.setAttribute("webRoot", resourceHolder.getStringValue("webRoot"));
        request.setAttribute("imgRoot", resourceHolder.getStringValue("imgRoot"));
		request.setAttribute("mobileRoot", resourceHolder.getStringValue("mobileRoot"));
		request.setAttribute("weixinRoot", resourceHolder.getStringValue("weixinRoot"));
		request.setAttribute("webLocation", resourceHolder.getStringValue("webLocation"));
		//2017年3月14日，江雄
		request.setAttribute("dictionaryRoot", resourceHolder.getStringValue("dictionaryRoot"));
		request.setAttribute("dictionaryRootMuilt", resourceHolder.getStringValue("dictionaryRootMuilt"));
		request.setAttribute("ascsPath", resourceHolder.getStringValue("ascsPath"));
		String url = request.getRequestURI();
		String requestParam = ParamUtil.getRequestParam(request);


		/**校验接口权限**/
		if(is_open_operate_permission_check){
			//System.err.print("本次用户权限"+ ListUtil.isNotNull(SystemLoginUserPermission.loginUserOperatePermssion));
			/*if(ListUtil.isNotNull(SystemLoginUserPermission.loginUserOperatePermssion)) {
				for (SystemLoginUserPermission per : SystemLoginUserPermission.loginUserOperatePermssion) {
				}
			}*/
		}

		//2018年6月20日，增加系统是否登陆权限
		if(url.startsWith(SUNDAY_WEB)
				|| url.startsWith(SYSTEM_WEB)){
			Object loginUserId = request.getSession().getAttribute("au");
			if(loginUserId == null){
				response.sendRedirect("/");
				return false;
			}
		}
		if(url.startsWith(SUNDAY_SITE_GT)
				&& !url.startsWith(SUNDAY_SITE_GT_LOGIN)
				&& !url.startsWith(SUNDAY_SITE_GT_DOLOGIN)
				&& !url.startsWith(SUNDAY_SITE_GT_REGISTER)
				&& !url.startsWith(SUNDAY_SITE_GT_DOREGISTER)){
			Object loginUser = request.getSession().getAttribute("siteMember");
			logger.info("----session中获取登陆用户-----"+loginUser);
			if(loginUser == null){
				response.sendRedirect(SUNDAY_SITE_GT_LOGIN);
				return false;
			}
		}
		logger.info("本次请求地址="+url+",本次请求参数="+requestParam+"，登陆人ID="+getMemberId(request));

		//2018年11月28日，捕获数据统计的数据
		//2018年12月17日，捕获安卓-第一cj多传了一个斜杠 不用startWin，使用indexOf
		if(url.indexOf("//")!=-1){
			url=url.replaceAll("//","/");
		}
		//logger.info("------处理后的URL-----"+url);
		if(url.startsWith("/sunday/mobile")&&
				!url.startsWith("/sunday/mobile/member/login")&&
				!url.startsWith("/sunday/mobile/member/forgetPassword")&&
				!url.startsWith("/sunday/mobile/upload")&&
				!url.startsWith("/sunday/mobile/apk")&&
				!url.startsWith("/sunday/mobile/visitor")&&
				!url.startsWith("/sunday/mobile/code")&&
				!url.contains("cancelPushNotice")&&
				!url.contains("getPushNotice")){

			Long mainVersion = getMainVersion(request);

			if(mainVersion != null && mainVersion >= 3){
				String token = getToken(request);
				if(StringUtils.isBlank(token)){
					response.sendError(403, CommonConstants.TOKENMSG);
					return false;
				}

				Map<String,Object> param = new HashMap<>();
				param.put("token",token);
				List<SundayParent> parents = parentService.list(param);

				List<SundayTeacher> teachers = teacherService.list(param);

				List<SundayVisitor> visitors = visitorService.list(param);
				if (ListUtil.isNull(parents)&& ListUtil.isNull(teachers)&&ListUtil.isNull(visitors)){
					response.sendError(403, CommonConstants.TOKENMSG);
					return false;
				}
			}

			Long memberId =getMemberId(request);
			Long guardId = getGuardId(request);

			Long bunchId = getBunchId(request);
			Integer type = null;
			if(LongUtil.isNull(memberId))return false;
			Integer loginType = 1;
			//教师端
			if(LongUtil.isNotZreo(bunchId)){
				logger.info("-拦截器-前置-教师端-memberId="+memberId+"-guardId="+guardId+"-bunchId="+bunchId);
				logger.info("-拦截器-教师端-memberId="+memberId);
/*
				SundayTeacher teacher = teacherService.getById(memberId);
				guardId=teacher.getGuardId();*/
				//兼容2.0和3.0
				SundayTeacher teacher = teacherService.getById(memberId);
				if(LongUtil.isNull(guardId)){
					guardId=teacher.getGuardId();
				}

				logger.info("-拦截器-后置-教师端-memberId="+memberId+"-guardId="+guardId+"-bunchId="+bunchId);
				logger.info("-拦截器-后置-教师端-memberId="+memberId);
				loginType = 2;
			}else{
				logger.info("-拦截器-前置-学生端-memberId="+memberId+"-guardId="+guardId+"-bunchId="+bunchId);
				logger.info("-拦截器-学生端-memberId="+memberId);
				SundayStudent student = studentService.getById(memberId);
				if(student != null){
					guardId=student.getGuardId();
					loginType = 1;
				}else{
					loginType = 6;
				}
				//bunchId=student.getBunchId();
			}

			//判断类型
			////0，登陆页，1，首页、启动（家长端），2首页、启动（教师端），3教学计划，4一周英语，5一周食谱，6一日作息，7园区简介，8宝贝画面，9园区通知，10班级圈，11我的）
			//2018年12月17日，捕获安卓-第一cj多传了一个斜杠 不用startWin，使用indexOf

			//logger.info("aaaaaaaaaaa="+(url.indexOf("/sunday/mobile/h5/plan/v2")!=-1));
			//logger.info("bbbbbbbbb="+(url.indexOf("/sunday/mobile/h5/plan")!=-1));
			if(url.indexOf("/sunday/mobile/member/login")!=-1){
				type=0;
			}else if(url.indexOf("/sunday/mobile/index/getData")!=-1){
				if(LongUtil.isNotZreo(getBunchId(request))){
					type=2;
				}else{
					if(loginType == 6){
						type = 99;
					}else{
						type=1;
					}

				}
			}else if(url.indexOf("/sunday/mobile/h5/plan/v2")!=-1||
					url.indexOf("/sunday/mobile/h5/plan")!=-1){
				type=3;
			}else if(url.indexOf("/sunday/mobile/h5/english")!=-1){
				type=4;
			}else if(url.indexOf("/sunday/mobile/h5/parent/recipe")!=-1||
					url.indexOf("/sunday/mobile/h5/parent/recipe/v2")!=-1||
					url.indexOf("/sunday/mobile/food/getFoodByWeek")!=-1){
				type=5;
			}else if(url.indexOf("/sunday/mobile/opus/getOpus")!=-1){
				type=6;
			}else if(url.indexOf("/sunday/mobile/h5/daily")!=-1){
				type=7;
			}else if(url.indexOf("/sunday/mobile/h5/guard")!=-1){
				type=8;
			}else if(url.indexOf("/sunday/mobile/camera/getDynamic")!=-1){
				type=9;
			}else if(url.indexOf("/sunday/mobile/notice/getNotice")!=-1||
					url.indexOf("/sunday/mobile/h5/notice/detail")!=-1){
				type=10;
			}else if(url.indexOf("/sunday/mobile/dynamic/getDynamic")!=-1){
				type=11;
			}else if(url.indexOf("/sunday/mobile/member/getDetail")!=-1){
				type=12;
			//2019年4月26日，增加园长信箱和成长档案
			}else if(url.indexOf("/sunday/mobile/guard/message/getMessage")!=-1){
				type=13;
			}else if(url.indexOf("/sunday/mobile/history/getHistory")!=-1){
				type=14;
			}
			//2023年8月，增加周报、月报、季报、日报
			else if(url.indexOf("/sunday/mobile/h5/zb/index")!=-1){
				type=15;
			}else if(url.indexOf("/sunday/mobile/h5/yb/index")!=-1){
				type=16;
			}else if(url.indexOf("/sunday/mobile/h5/jb/index")!=-1){
				type=17;
			}else if(url.indexOf("/sunday/mobile/h5/rb/list")!=-1){
				type=18;
			}
			if(type != null){
				dataMapper.insert(new SundayData(memberId, guardId, type));
			}
		}

        return super.preHandle(request, response, handler);
    }

	public Long getMemberId(HttpServletRequest request){
    	Long mainVersion = getMainVersion(request);
		Long memberId = 1L;
		Map<String,Object> param = new HashMap<>();

		logger.error("拦截器-用户ID="+request.getHeader("accountId"));
		memberId = StringUtils.isNotEmpty(request.getHeader("accountId"))?
				Long.valueOf(request.getHeader("accountId")):1L;


		return memberId;
	}
	public Long getBunchId(HttpServletRequest request){
		logger.error("拦截器-班级ID="+request.getHeader("bunchId"));
		Long bunchId = StringUtils.isNotEmpty(request.getHeader("bunchId"))?
				Long.valueOf(request.getHeader("bunchId")):null;
		return bunchId;
	}
	public Long getGuardId(HttpServletRequest request){
		logger.error("拦截器-园区ID="+request.getHeader("guardId"));
		Long bunchId = StringUtils.isNotEmpty(request.getHeader("guardId"))?
				Long.valueOf(request.getHeader("guardId")):null;
		return bunchId;
	}

	public String getToken(HttpServletRequest request){
		logger.error("拦截器-Token="+request.getHeader("token"));
		String token = StringUtils.isNotEmpty(request.getHeader("token"))?
				request.getHeader("token"):null;
		return token;
	}

	public Long getMainVersion(HttpServletRequest request){
		logger.error("拦截器-版本ID="+request.getHeader("mainVersion"));
		Long mainVersion = StringUtils.isNotEmpty(request.getHeader("mainVersion"))?
				Long.valueOf(request.getHeader("mainVersion")):null;
		return mainVersion;
	}
}
