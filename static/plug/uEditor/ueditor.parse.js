/*
 * UEditor
 * version: ueditor
 * build: Wed Aug 10 2016 11:06:16 GMT+0800 (CST)
 */
(function(){(function(){UE=window.UE||{};var C=!!window.ActiveXObject;var B={removeLastbs:function(D){return D.replace(/\/$/,"")},extend:function(G,F){var K=arguments,E=this.isBoolean(K[K.length-1])?K[K.length-1]:false,D=this.isBoolean(K[K.length-1])?K.length-1:K.length;for(var I=1;I<D;I++){var J=K[I];for(var H in J){if(!E||!G.hasOwnProperty(H)){G[H]=J[H]}}}return G},isIE:C,cssRule:C?function(D,E,I){var G,F;I=I||document;if(I.indexList){G=I.indexList}else{G=I.indexList={}}var H;if(!G[D]){if(E===undefined){return""}H=I.createStyleSheet("",F=I.styleSheets.length);G[D]=F}else{H=I.styleSheets[G[D]]}if(E===undefined){return H.cssText}H.cssText=H.cssText+"\n"+(E||"")}:function(D,G,H){H=H||document;var E=H.getElementsByTagName("head")[0],F;if(!(F=H.getElementById(D))){if(G===undefined){return""}F=H.createElement("style");F.id=D;E.appendChild(F)}if(G===undefined){return F.innerHTML}if(G!==""){F.innerHTML=F.innerHTML+"\n"+G}else{E.removeChild(F)}},domReady:function(D){var E=window.document;if(E.readyState==="complete"){D()}else{if(C){(function(){if(E.isReady){return}try{E.documentElement.doScroll("left")}catch(F){setTimeout(arguments.callee,0);return}D()})();window.attachEvent("onload",function(){D()})}else{E.addEventListener("DOMContentLoaded",function(){E.removeEventListener("DOMContentLoaded",arguments.callee,false);D()},false);window.addEventListener("load",function(){D()},false)}}},each:function(F,E,D){if(F==null){return}if(F.length===+F.length){for(var G=0,H=F.length;G<H;G++){if(E.call(D,F[G],G,F)===false){return false}}}else{for(var I in F){if(F.hasOwnProperty(I)){if(E.call(D,F[I],I,F)===false){return false}}}}},inArray:function(F,E){var D=-1;this.each(F,function(H,G){if(H===E){D=G;return false}});return D},pushItem:function(E,D){if(this.inArray(E,D)==-1){E.push(D)}},trim:function(D){return D.replace(/(^[ \t\n\r]+)|([ \t\n\r]+$)/g,"")},indexOf:function(D,E,G){var F=-1;G=this.isNumber(G)?G:0;this.each(D,function(I,H){if(H>=G&&I===E){F=H;return false}});return F},hasClass:function(G,D){D=D.replace(/(^[ ]+)|([ ]+$)/g,"").replace(/[ ]{2,}/g," ").split(" ");for(var E=0,H,F=G.className;H=D[E++];){if(!new RegExp("\\b"+H+"\\b","i").test(F)){return false}}return E-1==D.length},addClass:function(E,G){if(!E){return}G=this.trim(G).replace(/[ ]{2,}/g," ").split(" ");for(var D=0,H,F=E.className;H=G[D++];){if(!new RegExp("\\b"+H+"\\b").test(F)){F+=" "+H}}E.className=B.trim(F)},removeClass:function(E,G){G=this.isArray(G)?G:this.trim(G).replace(/[ ]{2,}/g," ").split(" ");for(var D=0,H,F=E.className;H=G[D++];){F=F.replace(new RegExp("\\b"+H+"\\b"),"")}F=this.trim(F).replace(/[ ]{2,}/g," ");E.className=F;!F&&E.removeAttribute("className")},on:function(I,D,G){var H=this.isArray(D)?D:D.split(/\s+/),J=H.length;if(J){while(J--){D=H[J];if(I.addEventListener){I.addEventListener(D,G,false)}else{if(!G._d){G._d={els:[]}}var E=D+G.toString(),F=B.indexOf(G._d.els,I);if(!G._d[E]||F==-1){if(F==-1){G._d.els.push(I)}if(!G._d[E]){G._d[E]=function(K){return G.call(K.srcElement,K||window.event)}}I.attachEvent("on"+D,G._d[E])}}}}I=null},off:function(E,K,G){var I=this.isArray(K)?K:K.split(/\s+/),H=I.length;if(H){while(H--){K=I[H];if(E.removeEventListener){E.removeEventListener(K,G,false)}else{var J=K+G.toString();try{E.detachEvent("on"+K,G._d?G._d[J]:G)}catch(D){}if(G._d&&G._d[J]){var F=B.indexOf(G._d.els,E);if(F!=-1){G._d.els.splice(F,1)}G._d.els.length==0&&delete G._d[J]}}}}},loadFile:function(){var D=[];function E(J,G){try{for(var F=0,H;H=D[F++];){if(H.doc===J&&H.url==(G.src||G.href)){return H}}}catch(I){return null}}return function(L,G,I){var J=E(L,G);if(J){if(J.ready){I&&I()}else{J.funs.push(I)}return}D.push({doc:L,url:G.src||G.href,funs:[I]});if(!L.body){var F=[];for(var H in G){if(H=="tag"){continue}F.push(H+'="'+G[H]+'"')}L.write("<"+G.tag+" "+F.join(" ")+" ></"+G.tag+">");return}if(G.id&&L.getElementById(G.id)){return}var K=L.createElement(G.tag);delete G.tag;for(var H in G){K.setAttribute(H,G[H])}K.onload=K.onreadystatechange=function(){if(!this.readyState||/loaded|complete/.test(this.readyState)){J=E(L,G);if(J.funs.length>0){J.ready=1;for(var M;M=J.funs.pop();){M()}}K.onload=K.onreadystatechange=null}};K.onerror=function(){throw Error("The load "+(G.href||G.src)+" fails,check the url")};L.getElementsByTagName("head")[0].appendChild(K)}}()};B.each(["String","Function","Array","Number","RegExp","Object","Boolean"],function(D){B["is"+D]=function(E){return Object.prototype.toString.apply(E)=="[object "+D+"]"}});var A={};UE.parse={register:function(E,D){A[E]=D},load:function(D){B.each(A,function(E){E.call(D,B)})}};uParse=function(E,D){B.domReady(function(){var F;if(document.querySelectorAll){F=document.querySelectorAll(E)}else{if(/^#/.test(E)){F=[document.getElementById(E.replace(/^#/,""))]}else{if(/^\./.test(E)){var F=[];B.each(document.getElementsByTagName("*"),function(G){if(G.className&&new RegExp("\\b"+E.replace(/^\./,"")+"\\b","i").test(G.className)){F.push(G)}})}else{F=document.getElementsByTagName(E)}}}B.each(F,function(G){UE.parse.load(B.extend({root:G,selector:E},D))})})}})();UE.parse.register("insertcode",function(B){var A=this.root.getElementsByTagName("pre");if(A.length){if(typeof XRegExp=="undefined"){var C,D;if(this.rootPath!==undefined){C=B.removeLastbs(this.rootPath)+"/third-party/SyntaxHighlighter/shCore.js";D=B.removeLastbs(this.rootPath)+"/third-party/SyntaxHighlighter/shCoreDefault.css"}else{C=this.highlightJsUrl;D=this.highlightCssUrl}B.loadFile(document,{id:"syntaxhighlighter_css",tag:"link",rel:"stylesheet",type:"text/css",href:D});B.loadFile(document,{id:"syntaxhighlighter_js",src:C,tag:"script",type:"text/javascript",defer:"defer"},function(){B.each(A,function(E){if(E&&/brush/i.test(E.className)){SyntaxHighlighter.highlight(E)}})})}else{B.each(A,function(E){if(E&&/brush/i.test(E.className)){SyntaxHighlighter.highlight(E)}})}}});UE.parse.register("table",function(B){var G=this,C=this.root,H=C.getElementsByTagName("table");if(H.length){var A=this.selector;B.cssRule("table",A+" table.noBorderTable td,"+A+" table.noBorderTable th,"+A+" table.noBorderTable caption{border:1px dashed #ddd !important}"+A+" table.sortEnabled tr.firstRow th,"+A+" table.sortEnabled tr.firstRow td{padding-right:20px; background-repeat: no-repeat;background-position: center right; background-image:url("+this.rootPath+"themes/default/images/sortable.png);}"+A+" table.sortEnabled tr.firstRow th:hover,"+A+" table.sortEnabled tr.firstRow td:hover{background-color: #EEE;}"+A+" table{margin-bottom:10px;border-collapse:collapse;display:table;}"+A+" td,"+A+" th{ background:white; padding: 5px 10px;border: 1px solid #DDD;}"+A+" caption{border:1px dashed #DDD;border-bottom:0;padding:3px;text-align:center;}"+A+" th{border-top:1px solid #BBB;background:#F7F7F7;}"+A+" table tr.firstRow th{border-top:2px solid #BBB;background:#F7F7F7;}"+A+" tr.ue-table-interlace-color-single td{ background: #fcfcfc; }"+A+" tr.ue-table-interlace-color-double td{ background: #f7faff; }"+A+" td p{margin:0;padding:0;}",document);B.each("td th caption".split(" "),function(J){var K=C.getElementsByTagName(J);K.length&&B.each(K,function(L){if(!L.firstChild){L.innerHTML="&nbsp;"}})});var H=C.getElementsByTagName("table");B.each(H,function(J){if(/\bsortEnabled\b/.test(J.className)){B.on(J,"click",function(L){var P=L.target||L.srcElement,N=F(P,["td","th"]);var K=F(P,"table"),M=B.indexOf(K.rows[0].cells,N),O=K.getAttribute("data-sort-type");if(M!=-1){I(K,M,G.tableSortCompareFn||O);E(K)}})}});function F(M,J){var K,L=M;J=B.isArray(J)?J:[J];while(L){for(K=0;K<J.length;K++){if(L.tagName==J[K].toUpperCase()){return L}}L=L.parentNode}return null}function I(V,Q,O){var J=V.rows,L=[],T=J[0].cells[0].tagName==="TH",M=0;for(var P=0,R=J.length;P<R;P++){L[P]=J[P]}var K={"reversecurrent":function(X,W){return 1},"orderbyasc":function(Z,Y){var W=Z.innerText||Z.textContent,X=Y.innerText||Y.textContent;return W.localeCompare(X)},"reversebyasc":function(Z,Y){var W=Z.innerHTML,X=Y.innerHTML;return X.localeCompare(W)},"orderbynum":function(Z,Y){var W=Z[B.isIE?"innerText":"textContent"].match(/\d+/),X=Y[B.isIE?"innerText":"textContent"].match(/\d+/);if(W){W=+W[0]}if(X){X=+X[0]}return(W||0)-(X||0)},"reversebynum":function(Z,Y){var W=Z[B.isIE?"innerText":"textContent"].match(/\d+/),X=Y[B.isIE?"innerText":"textContent"].match(/\d+/);if(W){W=+W[0]}if(X){X=+X[0]}return(X||0)-(W||0)}};V.setAttribute("data-sort-type",O&&typeof O==="string"&&K[O]?O:"");T&&L.splice(0,1);L=D(L,function(Y,X){var W;if(O&&typeof O==="function"){W=O.call(this,Y.cells[Q],X.cells[Q])}else{if(O&&typeof O==="number"){W=1}else{if(O&&typeof O==="string"&&K[O]){W=K[O].call(this,Y.cells[Q],X.cells[Q])}else{W=K["orderbyasc"].call(this,Y.cells[Q],X.cells[Q])}}}return W});var U=V.ownerDocument.createDocumentFragment();for(var N=0,R=L.length;N<R;N++){U.appendChild(L[N])}var S=V.getElementsByTagName("tbody")[0];if(!M){S.appendChild(U)}else{S.insertBefore(U,J[M-range.endRowIndex+range.beginRowIndex-1])}}function D(J,M){M=M||function(Q,R){return Q.localeCompare(R)};for(var K=0,N=J.length;K<N;K++){for(var P=K,L=J.length;P<L;P++){if(M(J[K],J[P])>0){var O=J[K];J[K]=J[P];J[P]=O}}}return J}function E(K){if(!B.hasClass(K.rows[0],"firstRow")){for(var J=1;J<K.rows.length;J++){B.removeClass(K.rows[J],"firstRow")}B.addClass(K.rows[0],"firstRow")}}}});UE.parse.register("charts",function(K){K.cssRule("chartsContainerHeight",".edui-chart-container { height:"+(this.chartContainerHeight||300)+"px}");var E=this.rootPath,A=this.root,H=null;if(!E){return}if(H=M()){F()}function M(){if(!A){return null}return I(A)}function I(Q){var T=[],S=Q.getElementsByTagName("table");for(var R=0,P;P=S[R];R++){if(P.getAttribute("data-chart")!==null){T.push(G(P))}}return T.length?T:null}function G(R){var S=R.getAttribute("data-chart"),P={},Q=[];for(var V=0,W;W=R.rows[V];V++){var Y=[];for(var T=0,Z;Z=W.cells[T];T++){var U=(Z.innerText||Z.textContent||"");Y.push(Z.tagName=="TH"?U:(U|0))}Q.push(Y)}S=S.split(";");for(var V=0,X;X=S[V];V++){X=X.split(":");P[X[0]]=X[1]}return{table:R,meta:P,data:Q}}function F(){N()}function N(){if(!window.jQuery){K.loadFile(document,{src:E+"/third-party/jquery-1.10.2.min.js",tag:"script",type:"text/javascript",defer:"defer"},function(){J()})}else{J()}}function J(){if(!window.Highcharts){K.loadFile(document,{src:E+"/third-party/highcharts/highcharts.js",tag:"script",type:"text/javascript",defer:"defer"},function(){C()})}else{C()}}function C(){K.loadFile(document,{src:E+"/dialogs/charts/chart.config.js",tag:"script",type:"text/javascript",defer:"defer"},function(){D()})}function D(){var Q=null,S=null,R=null;for(var P=0,T=H.length;P<T;P++){Q=H[P];S=O(Q);R=L(Q.table);B(R,typeConfig[Q.meta.chartType],S)}}function B(Q,R,P){$(Q).highcharts($.extend({},R,{credits:{enabled:false},exporting:{enabled:false},title:{text:P.title,x:-20},subtitle:{text:P.subTitle,x:-20},xAxis:{title:{text:P.xTitle},categories:P.categories},yAxis:{title:{text:P.yTitle},plotLines:[{value:0,width:1,color:"#808080"}]},tooltip:{enabled:true,valueSuffix:P.suffix},legend:{layout:"vertical",align:"right",verticalAlign:"middle",borderWidth:1},series:P.series}))}function L(P){var Q=document.createElement("div");Q.className="edui-chart-container";P.parentNode.replaceChild(Q,P);return Q}function O(Q){var Z=[],R=[],T=[],S=Q.data,U=Q.meta;if(U.dataFormat!="1"){for(var X=0,V=S.length;X<V;X++){for(var Y=0,W=S[X].length;Y<W;Y++){if(!T[Y]){T[Y]=[]}T[Y][X]=S[X][Y]}}S=T}T={};if(U.chartType!=typeConfig.length-1){R=S[0].slice(1);for(var X=1,P;P=S[X];X++){Z.push({name:P[0],data:P.slice(1)})}T.series=Z;T.categories=R;T.title=U.title;T.subTitle=U.subTitle;T.xTitle=U.xTitle;T.yTitle=U.yTitle;T.suffix=U.suffix}else{var P=[];for(var X=1,V=S[0].length;X<V;X++){P.push([S[0][X],S[1][X]|0])}Z[0]={type:"pie",name:U.tip,data:P};T.series=Z;T.title=U.title;T.suffix=U.suffix}return T}});UE.parse.register("background",function(D){var E=this,B=E.root,C=B.getElementsByTagName("p"),F;for(var A=0,G;G=C[A++];){F=G.getAttribute("data-background");if(F){G.parentNode.removeChild(G)}}F&&D.cssRule("ueditor_background",E.selector+"{"+F+"}",document)});UE.parse.register("list",function(A){var D=[],H={"cn":"cn-1-","cn1":"cn-2-","cn2":"cn-3-","num":"num-1-","num1":"num-2-","num2":"num-3-","dash":"dash","dot":"dot"};A.extend(this,{liiconpath:"http://bs.baidu.com/listicon/",listDefaultPaddingLeft:"20"});var C=this.root,E=C.getElementsByTagName("ol"),G=C.getElementsByTagName("ul"),F=this.selector;if(E.length){B.call(this,E)}if(G.length){B.call(this,G)}if(E.length||G.length){D.push(F+" .list-paddingleft-1{padding-left:0}");D.push(F+" .list-paddingleft-2{padding-left:"+this.listDefaultPaddingLeft+"px}");D.push(F+" .list-paddingleft-3{padding-left:"+this.listDefaultPaddingLeft*2+"px}");A.cssRule("list",F+" ol,"+F+" ul{margin:0;padding:0;}li{clear:both;}"+D.join("\n"),document)}function B(I){var J=this;A.each(I,function(L){if(L.className&&/custom_/i.test(L.className)){var K=L.className.match(/custom_(\w+)/)[1];if(K=="dash"||K=="dot"){A.pushItem(D,F+" li.list-"+H[K]+"{background-image:url("+J.liiconpath+H[K]+".gif)}");A.pushItem(D,F+" ul.custom_"+K+"{list-style:none;} "+F+" ul.custom_"+K+" li{background-position:0 3px;background-repeat:no-repeat}")}else{var M=1;A.each(L.childNodes,function(N){if(N.tagName=="LI"){A.pushItem(D,F+" li.list-"+H[K]+M+"{background-image:url("+J.liiconpath+"list-"+H[K]+M+".gif)}");M++}});A.pushItem(D,F+" ol.custom_"+K+"{list-style:none;}"+F+" ol.custom_"+K+" li{background-position:0 3px;background-repeat:no-repeat}")}switch(K){case"cn":A.pushItem(D,F+" li.list-"+K+"-paddingleft-1{padding-left:25px}");A.pushItem(D,F+" li.list-"+K+"-paddingleft-2{padding-left:40px}");A.pushItem(D,F+" li.list-"+K+"-paddingleft-3{padding-left:55px}");break;case"cn1":A.pushItem(D,F+" li.list-"+K+"-paddingleft-1{padding-left:30px}");A.pushItem(D,F+" li.list-"+K+"-paddingleft-2{padding-left:40px}");A.pushItem(D,F+" li.list-"+K+"-paddingleft-3{padding-left:55px}");break;case"cn2":A.pushItem(D,F+" li.list-"+K+"-paddingleft-1{padding-left:40px}");A.pushItem(D,F+" li.list-"+K+"-paddingleft-2{padding-left:55px}");A.pushItem(D,F+" li.list-"+K+"-paddingleft-3{padding-left:68px}");break;case"num":case"num1":A.pushItem(D,F+" li.list-"+K+"-paddingleft-1{padding-left:25px}");break;case"num2":A.pushItem(D,F+" li.list-"+K+"-paddingleft-1{padding-left:35px}");A.pushItem(D,F+" li.list-"+K+"-paddingleft-2{padding-left:40px}");break;case"dash":A.pushItem(D,F+" li.list-"+K+"-paddingleft{padding-left:35px}");break;case"dot":A.pushItem(D,F+" li.list-"+K+"-paddingleft{padding-left:20px}")}}})}});UE.parse.register("vedio",function(D){var G=this.root.getElementsByTagName("video"),C=this.root.getElementsByTagName("audio");document.createElement("video");document.createElement("audio");if(G.length||C.length){var F=D.removeLastbs(this.rootPath),A=F+"/third-party/video-js/video.js",B=F+"/third-party/video-js/video-js.min.css",E=F+"/third-party/video-js/video-js.swf";if(window.videojs){videojs.autoSetup()}else{D.loadFile(document,{id:"video_css",tag:"link",rel:"stylesheet",type:"text/css",href:B});D.loadFile(document,{id:"video_js",src:A,tag:"script",type:"text/javascript"},function(){videojs.options.flash.swf=E;videojs.autoSetup()})}}})})();