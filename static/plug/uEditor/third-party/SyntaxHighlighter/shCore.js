var XRegExp;if(XRegExp){throw Error("can't load XRegExp twice in the same frame")}(function(A){XRegExp=function(P,U){var Q=[],T=XRegExp.OUTSIDE_CLASS,W=0,R,S,Y,V,X;if(XRegExp.isRegExp(P)){if(U!==A){throw TypeError("can't supply flags when constructing one RegExp from another")}return D(P)}if(B){throw Error("can't call the XRegExp constructor within token definition functions")}U=U||"";R={hasNamedCapture:false,captureNames:[],hasFlag:function(Z){return U.indexOf(Z)>-1},setFlag:function(Z){U+=Z}};while(W<P.length){S=I(P,W,T,R);if(S){Q.push(S.output);W+=(S.match[0].length||1)}else{if(Y=F.exec.call(E[T],P.slice(W))){Q.push(Y[0]);W+=Y[0].length}else{V=P.charAt(W);if(V==="["){T=XRegExp.INSIDE_CLASS}else{if(V==="]"){T=XRegExp.OUTSIDE_CLASS}}Q.push(V);W++}}}X=RegExp(Q.join(""),F.replace.call(U,J,""));X._xregexp={source:P,captureNames:R.hasNamedCapture?R.captureNames:null};return X};XRegExp.version="1.5.1";XRegExp.INSIDE_CLASS=1;XRegExp.OUTSIDE_CLASS=2;var K=/\$(?:(\d\d?|[$&`'])|{([$\w]+)})/g,J=/[^gimy]+|([\s\S])(?=[\s\S]*\1)/g,O=/^(?:[?*+]|{\d+(?:,\d*)?})\??/,B=false,H=[],F={exec:RegExp.prototype.exec,test:RegExp.prototype.test,match:String.prototype.match,replace:String.prototype.replace,split:String.prototype.split},L=F.exec.call(/()??/,"")[1]===A,M=function(){var P=/^/g;F.test.call(P,"");return !P.lastIndex}(),C=RegExp.prototype.sticky!==A,E={};E[XRegExp.INSIDE_CLASS]=/^(?:\\(?:[0-3][0-7]{0,2}|[4-7][0-7]?|x[\dA-Fa-f]{2}|u[\dA-Fa-f]{4}|c[A-Za-z]|[\s\S]))/;E[XRegExp.OUTSIDE_CLASS]=/^(?:\\(?:0(?:[0-3][0-7]{0,2}|[4-7][0-7]?)?|[1-9]\d*|x[\dA-Fa-f]{2}|u[\dA-Fa-f]{4}|c[A-Za-z]|[\s\S])|\(\?[:=!]|[?*+]\?|{\d+(?:,\d*)?}\??)/;XRegExp.addToken=function(P,Q,R,S){H.push({pattern:D(P,"g"+(C?"y":"")),handler:Q,scope:R||XRegExp.OUTSIDE_CLASS,trigger:S||null})};XRegExp.cache=function(R,Q){var P=R+"/"+(Q||"");return XRegExp.cache[P]||(XRegExp.cache[P]=XRegExp(R,Q))};XRegExp.copyAsGlobal=function(P){return D(P,"g")};XRegExp.escape=function(P){return P.replace(/[-[\]{}()*+?.,\\^$|#\s]/g,"\\$&")};XRegExp.execAt=function(T,P,S,U){var Q=D(P,"g"+((U&&C)?"y":"")),R;Q.lastIndex=S=S||0;R=Q.exec(T);if(U&&R&&R.index!==S){R=null}if(P.global){P.lastIndex=R?Q.lastIndex:0}return R};XRegExp.freezeTokens=function(){XRegExp.addToken=function(){throw Error("can't run addToken after freezeTokens")}};XRegExp.isRegExp=function(P){return Object.prototype.toString.call(P)==="[object RegExp]"};XRegExp.iterate=function(T,Q,P,V){var U=D(Q,"g"),R=-1,S;while(S=U.exec(T)){if(Q.global){Q.lastIndex=U.lastIndex}P.call(V,S,++R,T,Q);if(U.lastIndex===S.index){U.lastIndex++}}if(Q.global){Q.lastIndex=0}};XRegExp.matchChain=function(R,P){return function Q(X,U){var W=P[U].regex?P[U]:{regex:P[U]},S=D(W.regex,"g"),T=[],V;for(V=0;V<X.length;V++){XRegExp.iterate(X[V],S,function(Y){T.push(W.backref?(Y[W.backref]||""):Y[0])})}return((U===P.length-1)||!T.length)?T:Q(T,U+1)}([R],0)};RegExp.prototype.apply=function(P,Q){return this.exec(Q[0])};RegExp.prototype.call=function(P,Q){return this.exec(Q)};RegExp.prototype.exec=function(U){var R,T,P,S;if(!this.global){S=this.lastIndex}R=F.exec.apply(this,arguments);if(R){if(!L&&R.length>1&&N(R,"")>-1){P=RegExp(this.source,F.replace.call(G(this),"g",""));F.replace.call((U+"").slice(R.index),P,function(){for(var V=1;V<arguments.length-2;V++){if(arguments[V]===A){R[V]=A}}})}if(this._xregexp&&this._xregexp.captureNames){for(var Q=1;Q<R.length;Q++){T=this._xregexp.captureNames[Q-1];if(T){R[T]=R[Q]}}}if(!M&&this.global&&!R[0].length&&(this.lastIndex>R.index)){this.lastIndex--}}if(!this.global){this.lastIndex=S}return R};RegExp.prototype.test=function(R){var P,Q;if(!this.global){Q=this.lastIndex}P=F.exec.call(this,R);if(P&&!M&&this.global&&!P[0].length&&(this.lastIndex>P.index)){this.lastIndex--}if(!this.global){this.lastIndex=Q}return !!P};String.prototype.match=function(P){if(!XRegExp.isRegExp(P)){P=RegExp(P)}if(P.global){var Q=F.match.apply(this,arguments);P.lastIndex=0;return Q}return P.exec(this)};String.prototype.replace=function(P,U){var V=XRegExp.isRegExp(P),T,Q,S,R;if(V){if(P._xregexp){T=P._xregexp.captureNames}if(!P.global){R=P.lastIndex}}else{P=P+""}if(Object.prototype.toString.call(U)==="[object Function]"){Q=F.replace.call(this+"",P,function(){if(T){arguments[0]=new String(arguments[0]);for(var W=0;W<T.length;W++){if(T[W]){arguments[0][T[W]]=arguments[W+1]}}}if(V&&P.global){P.lastIndex=arguments[arguments.length-2]+arguments[0].length}return U.apply(null,arguments)})}else{S=this+"";Q=F.replace.call(S,P,function(){var W=arguments;return F.replace.call(U+"",K,function(b,X,Z){if(X){switch(X){case"$":return"$";case"&":return W[0];case"`":return W[W.length-1].slice(0,W[W.length-2]);case"'":return W[W.length-1].slice(W[W.length-2]+W[0].length);default:var a="";X=+X;if(!X){return b}while(X>W.length-3){a=String.prototype.slice.call(X,-1)+a;X=Math.floor(X/10)}return(X?W[X]||"":"$")+a}}else{var Y=+Z;if(Y<=W.length-3){return W[Y]}Y=T?N(T,Z):-1;return Y>-1?W[Y+1]:b}})})}if(V){if(P.global){P.lastIndex=0}else{P.lastIndex=R}}return Q};String.prototype.split=function(Q,V){if(!XRegExp.isRegExp(Q)){return F.split.apply(this,arguments)}var T=this+"",U=[],P=0,R,S;if(V===A||+V<0){V=Infinity}else{V=Math.floor(+V);if(!V){return[]}}Q=XRegExp.copyAsGlobal(Q);while(R=Q.exec(T)){if(Q.lastIndex>P){U.push(T.slice(P,R.index));if(R.length>1&&R.index<T.length){Array.prototype.push.apply(U,R.slice(1))}S=R[0].length;P=Q.lastIndex;if(U.length>=V){break}}if(Q.lastIndex===R.index){Q.lastIndex++}}if(P===T.length){if(!F.test.call(Q,"")||S){U.push("")}}else{U.push(T.slice(P))}return U.length>V?U.slice(0,V):U};function D(Q,P){if(!XRegExp.isRegExp(Q)){throw TypeError("type RegExp expected")}var R=Q._xregexp;Q=XRegExp(Q.source,G(Q)+(P||""));if(R){Q._xregexp={source:R.source,captureNames:R.captureNames?R.captureNames.slice(0):null}}return Q}function G(P){return(P.global?"g":"")+(P.ignoreCase?"i":"")+(P.multiline?"m":"")+(P.extended?"x":"")+(P.sticky?"y":"")}function I(T,Q,R,X){var V=H.length,P,W,S;B=true;try{while(V--){S=H[V];if((R&S.scope)&&(!S.trigger||S.trigger.call(X))){S.pattern.lastIndex=Q;W=S.pattern.exec(T);if(W&&W.index===Q){P={output:S.handler.call(X,W,R),match:W};break}}}}catch(U){throw U}finally{B=false}return P}function N(P,S,Q){if(Array.prototype.indexOf){return P.indexOf(S,Q)}for(var R=Q||0;R<P.length;R++){if(P[R]===S){return R}}return -1}XRegExp.addToken(/\(\?#[^)]*\)/,function(P){return F.test.call(O,P.input.slice(P.index+P[0].length))?"":"(?:)"});XRegExp.addToken(/\((?!\?)/,function(){this.captureNames.push(null);return"("});XRegExp.addToken(/\(\?<([$\w]+)>/,function(P){this.captureNames.push(P[1]);this.hasNamedCapture=true;return"("});XRegExp.addToken(/\\k<([\w$]+)>/,function(P){var Q=N(this.captureNames,P[1]);return Q>-1?"\\"+(Q+1)+(isNaN(P.input.charAt(P.index+P[0].length))?"":"(?:)"):P[0]});XRegExp.addToken(/\[\^?]/,function(P){return P[0]==="[]"?"\\b\\B":"[\\s\\S]"});XRegExp.addToken(/^\(\?([imsx]+)\)/,function(P){this.setFlag(P[1]);return""});XRegExp.addToken(/(?:\s+|#.*)+/,function(P){return F.test.call(O,P.input.slice(P.index+P[0].length))?"":"(?:)"},XRegExp.OUTSIDE_CLASS,function(){return this.hasFlag("x")});XRegExp.addToken(/\./,function(){return"[\\s\\S]"},XRegExp.OUTSIDE_CLASS,function(){return this.hasFlag("s")})})();if(typeof(SyntaxHighlighter)=="undefined"){var SyntaxHighlighter=function(){if(typeof(require)!="undefined"&&typeof(XRegExp)=="undefined"){XRegExp=require("XRegExp").XRegExp}var a={defaults:{"class-name":"","first-line":1,"pad-line-numbers":false,"highlight":false,"title":null,"smart-tabs":true,"tab-size":4,"gutter":true,"toolbar":true,"quick-code":true,"collapse":false,"auto-links":false,"light":false,"unindent":true,"html-script":false},config:{space:"&nbsp;",useScriptTags:true,bloggerMode:false,stripBrs:false,tagName:"pre",strings:{expandSource:"expand source",help:"?",alert:"SyntaxHighlighter\n\n",noBrush:"Can't find brush for: ",brushNotHtmlScript:"Brush wasn't configured for html-script option: ",aboutDialog:"@ABOUT@"}},vars:{discoveredBrushes:null,highlighters:{}},brushes:{},regexLib:{multiLineCComments:/\/\*[\s\S]*?\*\//gm,singleLineCComments:/\/\/.*$/gm,singleLinePerlComments:/#.*$/gm,doubleQuotedString:/"([^\\"\n]|\\.)*"/g,singleQuotedString:/'([^\\'\n]|\\.)*'/g,multiLineDoubleQuotedString:new XRegExp('"([^\\\\"]|\\\\.)*"',"gs"),multiLineSingleQuotedString:new XRegExp("'([^\\\\']|\\\\.)*'","gs"),xmlComments:/(&lt;|<)!--[\s\S]*?--(&gt;|>)/gm,url:/\w+:\/\/[\w-.\/?%&=:@;#]*/g,phpScriptTags:{left:/(&lt;|<)\?(?:=|php)?/g,right:/\?(&gt;|>)/g,"eof":true},aspScriptTags:{left:/(&lt;|<)%=?/g,right:/%(&gt;|>)/g},scriptScriptTags:{left:/(&lt;|<)\s*script.*?(&gt;|>)/gi,right:/(&lt;|<)\/\s*script\s*(&gt;|>)/gi}},toolbar:{getHtml:function(n){var k='<div class="toolbar">',p=a.toolbar.items,o=p.list;function m(q,r){return a.toolbar.getButtonHtml(q,r,a.config.strings[r])}for(var l=0;l<o.length;l++){k+=(p[o[l]].getHtml||m)(n,o[l])}k+="</div>";return k},getButtonHtml:function(l,k,m){return'<span><a href="#" class="toolbar_item command_'+k+" "+k+'">'+m+"</a></span>"},handler:function(m){var p=m.target,l=p.className||"";function k(t){var q=new RegExp(t+"_(\\w+)"),s=q.exec(l);return s?s[1]:null}var n=O(L(p,".syntaxhighlighter").id),o=k("command");if(n&&o){a.toolbar.items[o].execute(n)}m.preventDefault()},items:{list:["expandSource","help"],expandSource:{getHtml:function(k){if(k.getParam("collapse")!=true){return""}var l=k.getParam("title");return a.toolbar.getButtonHtml(k,"expandSource",l?l:a.config.strings.expandSource)},execute:function(k){var l=i(k.id);j(l,"collapsed")}},help:{execute:function(l){var k=M("","_blank",500,250,"scrollbars=0"),m=k.document;m.write(a.config.strings.aboutDialog);m.close();k.focus()}}}},findElements:function(k,q){var m=q?[q]:I(document.getElementsByTagName(a.config.tagName)),o=a.config,p=[];if(o.useScriptTags){m=m.concat(f())}if(m.length===0){return p}for(var l=0;l<m.length;l++){var n={target:m[l],params:g(k,C(m[l].className))};if(n.params["brush"]==null){continue}p.push(n)}return p},highlight:function(k,l){var m=this.findElements(k,l),u="innerHTML",v=null,w=a.config;if(m.length===0){return}for(var r=0;r<m.length;r++){var l=m[r],t=l.target,s=l.params,n=s.brush,p;if(n==null){continue}if(s["html-script"]=="true"||a.defaults["html-script"]==true){v=new a.HtmlScript(n);n="htmlscript"}else{var o=h(n);if(o){v=new o()}else{continue}}p=t[u];if(w.useScriptTags){p=X(p)}if((t.title||"")!=""){s.title=t.title}s["brush"]=n;v.init(s);l=v.getDiv(p);if((t.id||"")!=""){l.id=t.id}var q=l.firstChild.firstChild;q.className=l.firstChild.className;t.parentNode.replaceChild(q,t)}},all:function(k){J(window,"load",function(){a.highlight(k)})}};function T(l,k){return l.className.indexOf(k)!=-1}function b(l,k){if(!T(l,k)){l.className+=" "+k}}function j(l,k){l.className=l.className.replace(k,"")}function I(l){var m=[];for(var k=0;k<l.length;k++){m.push(l[k])}return m}function S(k){return k.split(/\r?\n/)}function Z(l){var k="highlighter_";return l.indexOf(k)==0?l:k+l}function O(k){return a.vars.highlighters[Z(k)]}function i(k){return document.getElementById(Z(k))}function U(k){a.vars.highlighters[Z(k.id)]=k}function P(o,l,k){if(o==null){return null}var p=k!=true?o.childNodes:[o.parentNode],r={"#":"id",".":"className"}[l.substr(0,1)]||"nodeName",q,n;q=r!="nodeName"?l.substr(1):l.toUpperCase();if((o[r]||"").indexOf(q)!=-1){return o}for(var m=0;p&&m<p.length&&n==null;m++){n=P(p[m],l,k)}return n}function L(l,k){return P(l,k,true)}function V(k,m,n){n=Math.max(n||0,0);for(var l=n;l<k.length;l++){if(k[l]==m){return l}}return -1}function D(k){return(k||"")+Math.round(Math.random()*1000000).toString()}function g(k,l){var m={},n;for(n in k){m[n]=k[n]}for(n in l){m[n]=l[n]}return m}function c(l){var k={"true":true,"false":false}[l];return k==null?l:k}function M(p,k,m,r,l){var n=(screen.width-m)/2,o=(screen.height-r)/2;l+=", left="+n+", top="+o+", width="+m+", height="+r;l=l.replace(/^,/,"");var q=window.open(p,k,l);q.focus();return q}function J(l,k,m,n){function o(p){p=p||window.event;if(!p.target){p.target=p.srcElement;p.preventDefault=function(){this.returnValue=false}}m.call(n||window,p)}if(l.attachEvent){l.attachEvent("on"+k,o)}else{l.addEventListener(k,o,false)}}function B(k){window.alert(a.config.strings.alert+k)}function h(r,q){var o=a.vars.discoveredBrushes,k=null;if(o==null){o={};for(var l in a.brushes){var p=a.brushes[l],m=p.aliases;if(m==null){continue}p.brushName=l.toLowerCase();for(var n=0;n<m.length;n++){o[m[n]]=l}}a.vars.discoveredBrushes=o}k=a.brushes[o[r]];if(k==null&&q){B(a.config.strings.noBrush+r)}return k}function E(n,k){var m=S(n);for(var l=0;l<m.length;l++){m[l]=k(m[l],l)}return m.join("\r\n")}function K(k){return k.replace(/^[ ]*[\n]+|[\n]*[ ]*$/g,"")}function C(p){var n,q={},r=new XRegExp("^\\[(?<values>(.*?))\\]$"),l=new XRegExp("(?<name>[\\w-]+)\\s*:\\s*(?<value>[\\w-%#]+|\\[.*?\\]|\".*?\"|'.*?')\\s*;?","g");while((n=l.exec(p))!=null){var k=n.value.replace(/^['"]|['"]$/g,"");if(k!=null&&r.test(k)){var o=r.exec(k);k=o.values.length>0?o.values.split(/\s*,\s*/):[]}q[n.name]=k}return q}function Q(l,k){if(l==null||l.length==0||l=="\n"){return l}l=l.replace(/</g,"&lt;");l=l.replace(/ {2,}/g,function(p){var n="";for(var o=0;o<p.length-1;o++){n+=a.config.space}return n+" "});if(k!=null){l=E(l,function(n){if(n.length==0){return""}var m="";n=n.replace(/^(&nbsp;| )+/,function(o){m=o;return""});if(n.length==0){return m}return m+'<code class="'+k+'">'+n+"</code>"})}return l}function H(m,k){var l=m.toString();while(l.length<k){l="0"+l}return l}function R(m,k){var n="";for(var l=0;l<k;l++){n+=" "}return m.replace(/\t/g,n)}function G(n,k){var o=S(n),p="\t",l="";for(var m=0;m<50;m++){l+="                    "}function q(t,s,r){return t.substr(0,s)+l.substr(0,r)+t.substr(s+1,t.length)}n=E(n,function(t){if(t.indexOf(p)==-1){return t}var s=0;while((s=t.indexOf(p))!=-1){var r=k-s%k;t=q(t,s,r)}return t});return n}function W(l){var k=/<br\s*\/?>|&lt;br\s*\/?&gt;/gi;if(a.config.bloggerMode==true){l=l.replace(k,"\n")}if(a.config.stripBrs==true){l=l.replace(k,"")}return l}function Y(k){return k.replace(/^\s+|\s+$/g,"")}function N(n){var m=S(W(n)),r=new Array(),q=/^\s*/,l=1000;for(var o=0;o<m.length&&l>0;o++){var k=m[o];if(Y(k).length==0){continue}var p=q.exec(k);if(p==null){return n}l=Math.min(p[0].length,l)}if(l>0){for(var o=0;o<m.length;o++){m[o]=m[o].substr(l)}}return m.join("\n")}function d(k,l){if(k.index<l.index){return -1}else{if(k.index>l.index){return 1}else{if(k.length<l.length){return -1}else{if(k.length>l.length){return 1}}}}return 0}function F(m,q){function n(s,t){return s[0]}var k=0,l=null,o=[],r=q.func?q.func:n;while((l=q.regex.exec(m))!=null){var p=r(l,q);if(typeof(p)=="string"){p=[new a.Match(p,l.index,q.css)]}o=o.concat(p)}return o}function A(l){var k=/(.*)((&gt;|&lt;).*)/;return l.replace(a.regexLib.url,function(p){var o="",n=null;if(n=k.exec(p)){p=n[1];o=n[2]}return'<a href="'+p+'">'+p+"</a>"+o})}function f(){var k=document.getElementsByTagName("script"),m=[];for(var l=0;l<k.length;l++){if(k[l].type=="syntaxhighlighter"){m.push(k[l])}}return m}function X(k){var o="<![CDATA[",l="]]>",q=Y(k),p=false,m=o.length,n=l.length;if(q.indexOf(o)==0){q=q.substring(m);p=true}var r=q.length;if(q.indexOf(l)==r-n){q=q.substring(0,r-n);p=true}return p?q:k}function e(k){var q=k.target,n=L(q,".syntaxhighlighter"),p=L(q,".container"),l=document.createElement("textarea"),s;if(!p||!n||P(p,"textarea")){return}s=O(n.id);b(n,"source");var m=p.childNodes,r=[];for(var o=0;o<m.length;o++){r.push(m[o].innerText||m[o].textContent)}r=r.join("\r");r=r.replace(/\u00a0/g," ");l.appendChild(document.createTextNode(r));p.appendChild(l);l.focus();l.select();J(l,"blur",function(t){l.parentNode.removeChild(l);j(n,"source")})}a.Match=function(m,k,l){this.value=m;this.index=k;this.length=m.length;this.css=l;this.brushName=null};a.Match.prototype.toString=function(){return this.value};a.HtmlScript=function(q){var l=h(q),m,t=new a.brushes.Xml(),r=null,n=this,p="getDiv getHtml init".split(" ");if(l==null){return}m=new l();for(var o=0;o<p.length;o++){(function(){var u=p[o];n[u]=function(){return t[u].apply(t,arguments)}})()}if(m.htmlScript==null){B(a.config.strings.brushNotHtmlScript+q);return}t.regexList.push({regex:m.htmlScript.code,func:s});function k(v,u){for(var w=0;w<v.length;w++){v[w].index+=u}}function s(x,Ac){var y=x.code,Ab=[],u=m.regexList,w=x.index+x.left.length,Ad=m.htmlScript,v;for(var Aa=0;Aa<u.length;Aa++){v=F(y,u[Aa]);k(v,w);Ab=Ab.concat(v)}if(Ad.left!=null&&x.left!=null){v=F(x.left,Ad.left);k(v,x.index);Ab=Ab.concat(v)}if(Ad.right!=null&&x.right!=null){v=F(x.right,Ad.right);k(v,x.index+x[0].lastIndexOf(x.right));Ab=Ab.concat(v)}for(var z=0;z<Ab.length;z++){Ab[z].brushName=l.brushName}return Ab}};a.Highlighter=function(){};a.Highlighter.prototype={getParam:function(l,m){var k=this.params[l];return c(k==null?m:k)},create:function(k){return document.createElement(k)},findMatches:function(n,l){var m=[];if(n!=null){for(var k=0;k<n.length;k++){if(typeof(n[k])=="object"){m=m.concat(F(l,n[k]))}}}return this.removeNestedMatches(m.sort(d))},removeNestedMatches:function(l){for(var k=0;k<l.length;k++){if(l[k]===null){continue}var p=l[k],m=p.index+p.length;for(var o=k+1;o<l.length&&l[k]!==null;o++){var n=l[o];if(n===null){continue}else{if(n.index>m){break}else{if(n.index==p.index&&n.length>p.length){l[k]=null}else{if(n.index>=p.index&&n.index<m){l[o]=null}}}}}}return l},figureOutLineNumbers:function(k){var l=[],m=parseInt(this.getParam("first-line"));E(k,function(o,n){l.push(n+m)});return l},isLineHighlighted:function(l){var k=this.getParam("highlight",[]);if(typeof(k)!="object"&&k.push==null){k=[k]}return V(k,l.toString())!=-1},getLineHtml:function(k,m,l){var n=["line","number"+m,"index"+k,"alt"+(m%2==0?1:2).toString()];if(this.isLineHighlighted(m)){n.push("highlighted")}if(m==0){n.push("break")}return'<div class="'+n.join(" ")+'">'+l+"</div>"},getLineNumbersHtml:function(m,p){var k="",l=S(m).length,r=parseInt(this.getParam("first-line")),q=this.getParam("pad-line-numbers");if(q==true){q=(r+l-1).toString().length}else{if(isNaN(q)==true){q=0}}for(var n=0;n<l;n++){var o=p?p[n]:r+n,m=o==0?a.config.space:H(o,q);k+=this.getLineHtml(n,o,m)}return k},getCodeLinesHtml:function(t,l){t=Y(t);var o=S(t),n=this.getParam("pad-line-numbers"),u=parseInt(this.getParam("first-line")),t="",m=this.getParam("brush");for(var q=0;q<o.length;q++){var k=o[q],r=/^(&nbsp;|\s)+/.exec(k),p=null,s=l?l[q]:u+q;if(r!=null){p=r[0].toString();k=k.substr(p.length);p=p.replace(" ",a.config.space)}k=Y(k);if(k.length==0){k=a.config.space}t+=this.getLineHtml(q,s,(p!=null?'<code class="'+m+' spaces">'+p+"</code>":"")+k)}return t},getTitleHtml:function(k){return k?"<caption>"+k+"</caption>":""},getMatchesHtml:function(o,q){var r=0,l="",n=this.getParam("brush","");function k(t){var u=t?(t.brushName||n):n;return u?u+" ":""}for(var p=0;p<q.length;p++){var s=q[p],m;if(s===null||s.length===0){continue}m=k(s);l+=Q(o.substr(r,s.index-r),m+"plain")+Q(s.value,m+s.css);r=s.index+s.length+(s.offset||0)}l+=Q(o.substr(r),k()+"plain");return l},getHtml:function(n){var k="",o=["syntaxhighlighter"],p,m,l;if(this.getParam("light")==true){this.params.toolbar=this.params.gutter=false}className="syntaxhighlighter";if(this.getParam("collapse")==true){o.push("collapsed")}if((gutter=this.getParam("gutter"))==false){o.push("nogutter")}o.push(this.getParam("class-name"));o.push(this.getParam("brush"));n=K(n).replace(/\r/g," ");p=this.getParam("tab-size");n=this.getParam("smart-tabs")==true?G(n,p):R(n,p);if(this.getParam("unindent")){n=N(n)}if(gutter){l=this.figureOutLineNumbers(n)}m=this.findMatches(this.regexList,n);k=this.getMatchesHtml(n,m);k=this.getCodeLinesHtml(k,l);if(this.getParam("auto-links")){k=A(k)}if(typeof(navigator)!="undefined"&&navigator.userAgent&&navigator.userAgent.match(/MSIE/)){o.push("ie")}k='<div id="'+Z(this.id)+'" class="'+o.join(" ")+'">'+(this.getParam("toolbar")?a.toolbar.getHtml(this):"")+'<table border="0" cellpadding="0" cellspacing="0">'+this.getTitleHtml(this.getParam("title"))+"<tbody><tr>"+(gutter?'<td class="gutter">'+this.getLineNumbersHtml(n)+"</td>":"")+'<td class="code"><div class="container">'+k+"</div></td></tr></tbody></table></div>";return k},getDiv:function(k){if(k===null){k=""}this.code=k;var l=this.create("div");l.innerHTML=this.getHtml(k);if(this.getParam("toolbar")){J(P(l,".toolbar"),"click",a.toolbar.handler)}if(this.getParam("quick-code")){J(P(l,".code"),"dblclick",e)}return l},init:function(k){this.id=D();U(this);this.params=g(a.defaults,k||{});if(this.getParam("light")==true){this.params.toolbar=this.params.gutter=false}},getKeywords:function(k){k=k.replace(/^\s+|\s+$/g,"").replace(/\s+/g,"|");return"\\b(?:"+k+")\\b"},forHtmlScript:function(l){var k={"end":l.right.source};if(l.eof){k.end="(?:(?:"+k.end+")|$)"}this.htmlScript={left:{regex:l.left,css:"script"},right:{regex:l.right,css:"script"},code:new XRegExp("(?<left>"+l.left.source+")(?<code>.*?)(?<right>"+k.end+")","sgi")}}};return a}()}typeof(exports)!="undefined"?exports.SyntaxHighlighter=SyntaxHighlighter:null;(function(){SyntaxHighlighter=SyntaxHighlighter||(typeof require!=="undefined"?require("shCore").SyntaxHighlighter:null);function A(){var C="class interface function package";var B="-Infinity ...rest Array as AS3 Boolean break case catch const continue Date decodeURI decodeURIComponent default delete do dynamic each else encodeURI encodeURIComponent escape extends false final finally flash_proxy for get if implements import in include Infinity instanceof int internal is isFinite isNaN isXMLName label namespace NaN native new null Null Number Object object_proxy override parseFloat parseInt private protected public return set static String super switch this throw true try typeof uint undefined unescape use void while with";this.regexList=[{regex:SyntaxHighlighter.regexLib.singleLineCComments,css:"comments"},{regex:SyntaxHighlighter.regexLib.multiLineCComments,css:"comments"},{regex:SyntaxHighlighter.regexLib.doubleQuotedString,css:"string"},{regex:SyntaxHighlighter.regexLib.singleQuotedString,css:"string"},{regex:/\b([\d]+(\.[\d]+)?|0x[a-f0-9]+)\b/gi,css:"value"},{regex:new RegExp(this.getKeywords(C),"gm"),css:"color3"},{regex:new RegExp(this.getKeywords(B),"gm"),css:"keyword"},{regex:new RegExp("var","gm"),css:"variable"},{regex:new RegExp("trace","gm"),css:"color1"}];this.forHtmlScript(SyntaxHighlighter.regexLib.scriptScriptTags)}A.prototype=new SyntaxHighlighter.Highlighter();A.aliases=["actionscript3","as3"];SyntaxHighlighter.brushes.AS3=A;typeof(exports)!="undefined"?exports.Brush=A:null})();(function(){SyntaxHighlighter=SyntaxHighlighter||(typeof require!=="undefined"?require("shCore").SyntaxHighlighter:null);function A(){var C="after before beginning continue copy each end every from return get global in local named of set some that the then times to where whose with without";var B="first second third fourth fifth sixth seventh eighth ninth tenth last front back middle";var D="activate add alias AppleScript ask attachment boolean class constant delete duplicate empty exists false id integer list make message modal modified new no paragraph pi properties quit real record remove rest result reveal reverse run running save string true word yes";this.regexList=[{regex:/(--|#).*$/gm,css:"comments"},{regex:/\(\*(?:[\s\S]*?\(\*[\s\S]*?\*\))*[\s\S]*?\*\)/gm,css:"comments"},{regex:/"[\s\S]*?"/gm,css:"string"},{regex:/(?:,|:|¬|'s\b|\(|\)|\{|\}|«|\b\w*»)/g,css:"color1"},{regex:/(-)?(\d)+(\.(\d)?)?(E\+(\d)+)?/g,css:"color1"},{regex:/(?:&(amp;|gt;|lt;)?|=|� |>|<|≥|>=|≤|<=|\*|\+|-|\/|÷|\^)/g,css:"color2"},{regex:/\b(?:and|as|div|mod|not|or|return(?!\s&)(ing)?|equals|(is(n't| not)? )?equal( to)?|does(n't| not) equal|(is(n't| not)? )?(greater|less) than( or equal( to)?)?|(comes|does(n't| not) come) (after|before)|is(n't| not)?( in)? (back|front) of|is(n't| not)? behind|is(n't| not)?( (in|contained by))?|does(n't| not) contain|contain(s)?|(start|begin|end)(s)? with|((but|end) )?(consider|ignor)ing|prop(erty)?|(a )?ref(erence)?( to)?|repeat (until|while|with)|((end|exit) )?repeat|((else|end) )?if|else|(end )?(script|tell|try)|(on )?error|(put )?into|(of )?(it|me)|its|my|with (timeout( of)?|transaction)|end (timeout|transaction))\b/g,css:"keyword"},{regex:/\b\d+(st|nd|rd|th)\b/g,css:"keyword"},{regex:/\b(?:about|above|against|around|at|below|beneath|beside|between|by|(apart|aside) from|(instead|out) of|into|on(to)?|over|since|thr(ough|u)|under)\b/g,css:"color3"},{regex:/\b(?:adding folder items to|after receiving|choose( ((remote )?application|color|folder|from list|URL))?|clipboard info|set the clipboard to|(the )?clipboard|entire contents|display(ing| (alert|dialog|mode))?|document( (edited|file|nib name))?|file( (name|type))?|(info )?for|giving up after|(name )?extension|quoted form|return(ed)?|second(?! item)(s)?|list (disks|folder)|text item(s| delimiters)?|(Unicode )?text|(disk )?item(s)?|((current|list) )?view|((container|key) )?window|with (data|icon( (caution|note|stop))?|parameter(s)?|prompt|properties|seed|title)|case|diacriticals|hyphens|numeric strings|punctuation|white space|folder creation|application(s( folder)?| (processes|scripts position|support))?|((desktop )?(pictures )?|(documents|downloads|favorites|home|keychain|library|movies|music|public|scripts|sites|system|users|utilities|workflows) )folder|desktop|Folder Action scripts|font(s| panel)?|help|internet plugins|modem scripts|(system )?preferences|printer descriptions|scripting (additions|components)|shared (documents|libraries)|startup (disk|items)|temporary items|trash|on server|in AppleTalk zone|((as|long|short) )?user name|user (ID|locale)|(with )?password|in (bundle( with identifier)?|directory)|(close|open for) access|read|write( permission)?|(g|s)et eof|using( delimiters)?|starting at|default (answer|button|color|country code|entr(y|ies)|identifiers|items|name|location|script editor)|hidden( answer)?|open(ed| (location|untitled))?|error (handling|reporting)|(do( shell)?|load|run|store) script|administrator privileges|altering line endings|get volume settings|(alert|boot|input|mount|output|set) volume|output muted|(fax|random )?number|round(ing)?|up|down|toward zero|to nearest|as taught in school|system (attribute|info)|((AppleScript( Studio)?|system) )?version|(home )?directory|(IPv4|primary Ethernet) address|CPU (type|speed)|physical memory|time (stamp|to GMT)|replacing|ASCII (character|number)|localized string|from table|offset|summarize|beep|delay|say|(empty|multiple) selections allowed|(of|preferred) type|invisibles|showing( package contents)?|editable URL|(File|FTP|News|Media|Web) [Ss]ervers|Telnet hosts|Directory services|Remote applications|waiting until completion|saving( (in|to))?|path (for|to( (((current|frontmost) )?application|resource))?)|POSIX (file|path)|(background|RGB) color|(OK|cancel) button name|cancel button|button(s)?|cubic ((centi)?met(re|er)s|yards|feet|inches)|square ((kilo)?met(re|er)s|miles|yards|feet)|(centi|kilo)?met(re|er)s|miles|yards|feet|inches|lit(re|er)s|gallons|quarts|(kilo)?grams|ounces|pounds|degrees (Celsius|Fahrenheit|Kelvin)|print( (dialog|settings))?|clos(e(able)?|ing)|(de)?miniaturized|miniaturizable|zoom(ed|able)|attribute run|action (method|property|title)|phone|email|((start|end)ing|home) page|((birth|creation|current|custom|modification) )?date|((((phonetic )?(first|last|middle))|computer|host|maiden|related) |nick)?name|aim|icq|jabber|msn|yahoo|address(es)?|save addressbook|should enable action|city|country( code)?|formatte(r|d address)|(palette )?label|state|street|zip|AIM [Hh]andle(s)?|my card|select(ion| all)?|unsaved|(alpha )?value|entr(y|ies)|group|(ICQ|Jabber|MSN) handle|person|people|company|department|icon image|job title|note|organization|suffix|vcard|url|copies|collating|pages (across|down)|request print time|target( printer)?|((GUI Scripting|Script menu) )?enabled|show Computer scripts|(de)?activated|awake from nib|became (key|main)|call method|of (class|object)|center|clicked toolbar item|closed|for document|exposed|(can )?hide|idle|keyboard (down|up)|event( (number|type))?|launch(ed)?|load (image|movie|nib|sound)|owner|log|mouse (down|dragged|entered|exited|moved|up)|move|column|localization|resource|script|register|drag (info|types)|resigned (active|key|main)|resiz(e(d)?|able)|right mouse (down|dragged|up)|scroll wheel|(at )?index|should (close|open( untitled)?|quit( after last window closed)?|zoom)|((proposed|screen) )?bounds|show(n)?|behind|in front of|size (mode|to fit)|update(d| toolbar item)?|was (hidden|miniaturized)|will (become active|close|finish launching|hide|miniaturize|move|open|quit|(resign )?active|((maximum|minimum|proposed) )?size|show|zoom)|bundle|data source|movie|pasteboard|sound|tool(bar| tip)|(color|open|save) panel|coordinate system|frontmost|main( (bundle|menu|window))?|((services|(excluded from )?windows) )?menu|((executable|frameworks|resource|scripts|shared (frameworks|support)) )?path|(selected item )?identifier|data|content(s| view)?|character(s)?|click count|(command|control|option|shift) key down|context|delta (x|y|z)|key( code)?|location|pressure|unmodified characters|types|(first )?responder|playing|(allowed|selectable) identifiers|allows customization|(auto saves )?configuration|visible|image( name)?|menu form representation|tag|user(-| )defaults|associated file name|(auto|needs) display|current field editor|floating|has (resize indicator|shadow)|hides when deactivated|level|minimized (image|title)|opaque|position|release when closed|sheet|title(d)?)\b/g,css:"color3"},{regex:new RegExp(this.getKeywords(D),"gm"),css:"color3"},{regex:new RegExp(this.getKeywords(C),"gm"),css:"keyword"},{regex:new RegExp(this.getKeywords(B),"gm"),css:"keyword"}]}A.prototype=new SyntaxHighlighter.Highlighter();A.aliases=["applescript"];SyntaxHighlighter.brushes.AppleScript=A;typeof(exports)!="undefined"?exports.Brush=A:null})();(function(){SyntaxHighlighter=SyntaxHighlighter||(typeof require!=="undefined"?require("shCore").SyntaxHighlighter:null);function A(){var C="if fi then elif else for do done until while break continue case esac function return in eq ne ge le";var B="alias apropos awk basename bash bc bg builtin bzip2 cal cat cd cfdisk chgrp chmod chown chrootcksum clear cmp comm command cp cron crontab csplit cut date dc dd ddrescue declare df diff diff3 dig dir dircolors dirname dirs du echo egrep eject enable env ethtool eval exec exit expand export expr false fdformat fdisk fg fgrep file find fmt fold format free fsck ftp gawk getopts grep groups gzip hash head history hostname id ifconfig import install join kill less let ln local locate logname logout look lpc lpr lprint lprintd lprintq lprm ls lsof make man mkdir mkfifo mkisofs mknod more mount mtools mv netstat nice nl nohup nslookup open op passwd paste pathchk ping popd pr printcap printenv printf ps pushd pwd quota quotacheck quotactl ram rcp read readonly renice remsync rm rmdir rsync screen scp sdiff sed select seq set sftp shift shopt shutdown sleep sort source split ssh strace su sudo sum symlink sync tail tar tee test time times touch top traceroute trap tr true tsort tty type ulimit umask umount unalias uname unexpand uniq units unset unshar useradd usermod users uuencode uudecode v vdir vi watch wc whereis which who whoami Wget xargs yes";this.regexList=[{regex:/^#!.*$/gm,css:"preprocessor bold"},{regex:/\/[\w-\/]+/gm,css:"plain"},{regex:SyntaxHighlighter.regexLib.singleLinePerlComments,css:"comments"},{regex:SyntaxHighlighter.regexLib.doubleQuotedString,css:"string"},{regex:SyntaxHighlighter.regexLib.singleQuotedString,css:"string"},{regex:new RegExp(this.getKeywords(C),"gm"),css:"keyword"},{regex:new RegExp(this.getKeywords(B),"gm"),css:"functions"}]}A.prototype=new SyntaxHighlighter.Highlighter();A.aliases=["bash","shell","sh"];SyntaxHighlighter.brushes.Bash=A;typeof(exports)!="undefined"?exports.Brush=A:null})();(function(){SyntaxHighlighter=SyntaxHighlighter||(typeof require!=="undefined"?require("shCore").SyntaxHighlighter:null);function A(){var B="Abs ACos AddSOAPRequestHeader AddSOAPResponseHeader AjaxLink AjaxOnLoad ArrayAppend ArrayAvg ArrayClear ArrayDeleteAt ArrayInsertAt ArrayIsDefined ArrayIsEmpty ArrayLen ArrayMax ArrayMin ArraySet ArraySort ArraySum ArraySwap ArrayToList Asc ASin Atn BinaryDecode BinaryEncode BitAnd BitMaskClear BitMaskRead BitMaskSet BitNot BitOr BitSHLN BitSHRN BitXor Ceiling CharsetDecode CharsetEncode Chr CJustify Compare CompareNoCase Cos CreateDate CreateDateTime CreateObject CreateODBCDate CreateODBCDateTime CreateODBCTime CreateTime CreateTimeSpan CreateUUID DateAdd DateCompare DateConvert DateDiff DateFormat DatePart Day DayOfWeek DayOfWeekAsString DayOfYear DaysInMonth DaysInYear DE DecimalFormat DecrementValue Decrypt DecryptBinary DeleteClientVariable DeserializeJSON DirectoryExists DollarFormat DotNetToCFType Duplicate Encrypt EncryptBinary Evaluate Exp ExpandPath FileClose FileCopy FileDelete FileExists FileIsEOF FileMove FileOpen FileRead FileReadBinary FileReadLine FileSetAccessMode FileSetAttribute FileSetLastModified FileWrite Find FindNoCase FindOneOf FirstDayOfMonth Fix FormatBaseN GenerateSecretKey GetAuthUser GetBaseTagData GetBaseTagList GetBaseTemplatePath GetClientVariablesList GetComponentMetaData GetContextRoot GetCurrentTemplatePath GetDirectoryFromPath GetEncoding GetException GetFileFromPath GetFileInfo GetFunctionList GetGatewayHelper GetHttpRequestData GetHttpTimeString GetK2ServerDocCount GetK2ServerDocCountLimit GetLocale GetLocaleDisplayName GetLocalHostIP GetMetaData GetMetricData GetPageContext GetPrinterInfo GetProfileSections GetProfileString GetReadableImageFormats GetSOAPRequest GetSOAPRequestHeader GetSOAPResponse GetSOAPResponseHeader GetTempDirectory GetTempFile GetTemplatePath GetTickCount GetTimeZoneInfo GetToken GetUserRoles GetWriteableImageFormats Hash Hour HTMLCodeFormat HTMLEditFormat IIf ImageAddBorder ImageBlur ImageClearRect ImageCopy ImageCrop ImageDrawArc ImageDrawBeveledRect ImageDrawCubicCurve ImageDrawLine ImageDrawLines ImageDrawOval ImageDrawPoint ImageDrawQuadraticCurve ImageDrawRect ImageDrawRoundRect ImageDrawText ImageFlip ImageGetBlob ImageGetBufferedImage ImageGetEXIFTag ImageGetHeight ImageGetIPTCTag ImageGetWidth ImageGrayscale ImageInfo ImageNegative ImageNew ImageOverlay ImagePaste ImageRead ImageReadBase64 ImageResize ImageRotate ImageRotateDrawingAxis ImageScaleToFit ImageSetAntialiasing ImageSetBackgroundColor ImageSetDrawingColor ImageSetDrawingStroke ImageSetDrawingTransparency ImageSharpen ImageShear ImageShearDrawingAxis ImageTranslate ImageTranslateDrawingAxis ImageWrite ImageWriteBase64 ImageXORDrawingMode IncrementValue InputBaseN Insert Int IsArray IsBinary IsBoolean IsCustomFunction IsDate IsDDX IsDebugMode IsDefined IsImage IsImageFile IsInstanceOf IsJSON IsLeapYear IsLocalHost IsNumeric IsNumericDate IsObject IsPDFFile IsPDFObject IsQuery IsSimpleValue IsSOAPRequest IsStruct IsUserInAnyRole IsUserInRole IsUserLoggedIn IsValid IsWDDX IsXML IsXmlAttribute IsXmlDoc IsXmlElem IsXmlNode IsXmlRoot JavaCast JSStringFormat LCase Left Len ListAppend ListChangeDelims ListContains ListContainsNoCase ListDeleteAt ListFind ListFindNoCase ListFirst ListGetAt ListInsertAt ListLast ListLen ListPrepend ListQualify ListRest ListSetAt ListSort ListToArray ListValueCount ListValueCountNoCase LJustify Log Log10 LSCurrencyFormat LSDateFormat LSEuroCurrencyFormat LSIsCurrency LSIsDate LSIsNumeric LSNumberFormat LSParseCurrency LSParseDateTime LSParseEuroCurrency LSParseNumber LSTimeFormat LTrim Max Mid Min Minute Month MonthAsString Now NumberFormat ParagraphFormat ParseDateTime Pi PrecisionEvaluate PreserveSingleQuotes Quarter QueryAddColumn QueryAddRow QueryConvertForGrid QueryNew QuerySetCell QuotedValueList Rand Randomize RandRange REFind REFindNoCase ReleaseComObject REMatch REMatchNoCase RemoveChars RepeatString Replace ReplaceList ReplaceNoCase REReplace REReplaceNoCase Reverse Right RJustify Round RTrim Second SendGatewayMessage SerializeJSON SetEncoding SetLocale SetProfileString SetVariable Sgn Sin Sleep SpanExcluding SpanIncluding Sqr StripCR StructAppend StructClear StructCopy StructCount StructDelete StructFind StructFindKey StructFindValue StructGet StructInsert StructIsEmpty StructKeyArray StructKeyExists StructKeyList StructKeyList StructNew StructSort StructUpdate Tan TimeFormat ToBase64 ToBinary ToScript ToString Trim UCase URLDecode URLEncodedFormat URLSessionFormat Val ValueList VerifyClient Week Wrap Wrap WriteOutput XmlChildPos XmlElemNew XmlFormat XmlGetNodeType XmlNew XmlParse XmlSearch XmlTransform XmlValidate Year YesNoFormat";var C="cfabort cfajaximport cfajaxproxy cfapplet cfapplication cfargument cfassociate cfbreak cfcache cfcalendar cfcase cfcatch cfchart cfchartdata cfchartseries cfcol cfcollection cfcomponent cfcontent cfcookie cfdbinfo cfdefaultcase cfdirectory cfdiv cfdocument cfdocumentitem cfdocumentsection cfdump cfelse cfelseif cferror cfexchangecalendar cfexchangeconnection cfexchangecontact cfexchangefilter cfexchangemail cfexchangetask cfexecute cfexit cffeed cffile cfflush cfform cfformgroup cfformitem cfftp cffunction cfgrid cfgridcolumn cfgridrow cfgridupdate cfheader cfhtmlhead cfhttp cfhttpparam cfif cfimage cfimport cfinclude cfindex cfinput cfinsert cfinterface cfinvoke cfinvokeargument cflayout cflayoutarea cfldap cflocation cflock cflog cflogin cfloginuser cflogout cfloop cfmail cfmailparam cfmailpart cfmenu cfmenuitem cfmodule cfNTauthenticate cfobject cfobjectcache cfoutput cfparam cfpdf cfpdfform cfpdfformparam cfpdfparam cfpdfsubform cfpod cfpop cfpresentation cfpresentationslide cfpresenter cfprint cfprocessingdirective cfprocparam cfprocresult cfproperty cfquery cfqueryparam cfregistry cfreport cfreportparam cfrethrow cfreturn cfsavecontent cfschedule cfscript cfsearch cfselect cfset cfsetting cfsilent cfslider cfsprydataset cfstoredproc cfswitch cftable cftextarea cfthread cfthrow cftimer cftooltip cftrace cftransaction cftree cftreeitem cftry cfupdate cfwddx cfwindow cfxml cfzip cfzipparam";var D="all and any between cross in join like not null or outer some";this.regexList=[{regex:new RegExp("--(.*)$","gm"),css:"comments"},{regex:SyntaxHighlighter.regexLib.xmlComments,css:"comments"},{regex:SyntaxHighlighter.regexLib.doubleQuotedString,css:"string"},{regex:SyntaxHighlighter.regexLib.singleQuotedString,css:"string"},{regex:new RegExp(this.getKeywords(B),"gmi"),css:"functions"},{regex:new RegExp(this.getKeywords(D),"gmi"),css:"color1"},{regex:new RegExp(this.getKeywords(C),"gmi"),css:"keyword"}]}A.prototype=new SyntaxHighlighter.Highlighter();A.aliases=["coldfusion","cf"];SyntaxHighlighter.brushes.ColdFusion=A;typeof(exports)!="undefined"?exports.Brush=A:null})();(function(){SyntaxHighlighter=SyntaxHighlighter||(typeof require!=="undefined"?require("shCore").SyntaxHighlighter:null);function A(){var C="ATOM BOOL BOOLEAN BYTE CHAR COLORREF DWORD DWORDLONG DWORD_PTR DWORD32 DWORD64 FLOAT HACCEL HALF_PTR HANDLE HBITMAP HBRUSH HCOLORSPACE HCONV HCONVLIST HCURSOR HDC HDDEDATA HDESK HDROP HDWP HENHMETAFILE HFILE HFONT HGDIOBJ HGLOBAL HHOOK HICON HINSTANCE HKEY HKL HLOCAL HMENU HMETAFILE HMODULE HMONITOR HPALETTE HPEN HRESULT HRGN HRSRC HSZ HWINSTA HWND INT INT_PTR INT32 INT64 LANGID LCID LCTYPE LGRPID LONG LONGLONG LONG_PTR LONG32 LONG64 LPARAM LPBOOL LPBYTE LPCOLORREF LPCSTR LPCTSTR LPCVOID LPCWSTR LPDWORD LPHANDLE LPINT LPLONG LPSTR LPTSTR LPVOID LPWORD LPWSTR LRESULT PBOOL PBOOLEAN PBYTE PCHAR PCSTR PCTSTR PCWSTR PDWORDLONG PDWORD_PTR PDWORD32 PDWORD64 PFLOAT PHALF_PTR PHANDLE PHKEY PINT PINT_PTR PINT32 PINT64 PLCID PLONG PLONGLONG PLONG_PTR PLONG32 PLONG64 POINTER_32 POINTER_64 PSHORT PSIZE_T PSSIZE_T PSTR PTBYTE PTCHAR PTSTR PUCHAR PUHALF_PTR PUINT PUINT_PTR PUINT32 PUINT64 PULONG PULONGLONG PULONG_PTR PULONG32 PULONG64 PUSHORT PVOID PWCHAR PWORD PWSTR SC_HANDLE SC_LOCK SERVICE_STATUS_HANDLE SHORT SIZE_T SSIZE_T TBYTE TCHAR UCHAR UHALF_PTR UINT UINT_PTR UINT32 UINT64 ULONG ULONGLONG ULONG_PTR ULONG32 ULONG64 USHORT USN VOID WCHAR WORD WPARAM WPARAM WPARAM char bool short int __int32 __int64 __int8 __int16 long float double __wchar_t clock_t _complex _dev_t _diskfree_t div_t ldiv_t _exception _EXCEPTION_POINTERS FILE _finddata_t _finddatai64_t _wfinddata_t _wfinddatai64_t __finddata64_t __wfinddata64_t _FPIEEE_RECORD fpos_t _HEAPINFO _HFILE lconv intptr_t jmp_buf mbstate_t _off_t _onexit_t _PNH ptrdiff_t _purecall_handler sig_atomic_t size_t _stat __stat64 _stati64 terminate_function time_t __time64_t _timeb __timeb64 tm uintptr_t _utimbuf va_list wchar_t wctrans_t wctype_t wint_t signed";var B="auto break case catch class const decltype __finally __exception __try const_cast continue private public protected __declspec default delete deprecated dllexport dllimport do dynamic_cast else enum explicit extern if for friend goto inline mutable naked namespace new noinline noreturn nothrow register reinterpret_cast return selectany sizeof static static_cast struct switch template this thread throw true false try typedef typeid typename union using uuid virtual void volatile whcar_t while";var D="assert isalnum isalpha iscntrl isdigit isgraph islower isprintispunct isspace isupper isxdigit tolower toupper errno localeconv setlocale acos asin atan atan2 ceil cos cosh exp fabs floor fmod frexp ldexp log log10 modf pow sin sinh sqrt tan tanh jmp_buf longjmp setjmp raise signal sig_atomic_t va_arg va_end va_start clearerr fclose feof ferror fflush fgetc fgetpos fgets fopen fprintf fputc fputs fread freopen fscanf fseek fsetpos ftell fwrite getc getchar gets perror printf putc putchar puts remove rename rewind scanf setbuf setvbuf sprintf sscanf tmpfile tmpnam ungetc vfprintf vprintf vsprintf abort abs atexit atof atoi atol bsearch calloc div exit free getenv labs ldiv malloc mblen mbstowcs mbtowc qsort rand realloc srand strtod strtol strtoul system wcstombs wctomb memchr memcmp memcpy memmove memset strcat strchr strcmp strcoll strcpy strcspn strerror strlen strncat strncmp strncpy strpbrk strrchr strspn strstr strtok strxfrm asctime clock ctime difftime gmtime localtime mktime strftime time";this.regexList=[{regex:SyntaxHighlighter.regexLib.singleLineCComments,css:"comments"},{regex:SyntaxHighlighter.regexLib.multiLineCComments,css:"comments"},{regex:SyntaxHighlighter.regexLib.doubleQuotedString,css:"string"},{regex:SyntaxHighlighter.regexLib.singleQuotedString,css:"string"},{regex:/^ *#.*/gm,css:"preprocessor"},{regex:new RegExp(this.getKeywords(C),"gm"),css:"color1 bold"},{regex:new RegExp(this.getKeywords(D),"gm"),css:"functions bold"},{regex:new RegExp(this.getKeywords(B),"gm"),css:"keyword bold"}]}A.prototype=new SyntaxHighlighter.Highlighter();A.aliases=["cpp","c"];SyntaxHighlighter.brushes.Cpp=A;typeof(exports)!="undefined"?exports.Brush=A:null})();(function(){SyntaxHighlighter=SyntaxHighlighter||(typeof require!=="undefined"?require("shCore").SyntaxHighlighter:null);function A(){var B="abstract as base bool break byte case catch char checked class const continue decimal default delegate do double else enum event explicit volatile extern false finally fixed float for foreach get goto if implicit in int interface internal is lock long namespace new null object operator out override params private protected public readonly ref return sbyte sealed set short sizeof stackalloc static string struct switch this throw true try typeof uint ulong unchecked unsafe ushort using virtual void while var from group by into select let where orderby join on equals ascending descending";function C(D,F){var E=(D[0].indexOf("///")==0)?"color1":"comments";return[new SyntaxHighlighter.Match(D[0],D.index,E)]}this.regexList=[{regex:SyntaxHighlighter.regexLib.singleLineCComments,func:C},{regex:SyntaxHighlighter.regexLib.multiLineCComments,css:"comments"},{regex:/@"(?:[^"]|"")*"/g,css:"string"},{regex:SyntaxHighlighter.regexLib.doubleQuotedString,css:"string"},{regex:SyntaxHighlighter.regexLib.singleQuotedString,css:"string"},{regex:/^\s*#.*/gm,css:"preprocessor"},{regex:new RegExp(this.getKeywords(B),"gm"),css:"keyword"},{regex:/\bpartial(?=\s+(?:class|interface|struct)\b)/g,css:"keyword"},{regex:/\byield(?=\s+(?:return|break)\b)/g,css:"keyword"}];this.forHtmlScript(SyntaxHighlighter.regexLib.aspScriptTags)}A.prototype=new SyntaxHighlighter.Highlighter();A.aliases=["c#","c-sharp","csharp"];SyntaxHighlighter.brushes.CSharp=A;typeof(exports)!="undefined"?exports.Brush=A:null})();(function(){SyntaxHighlighter=SyntaxHighlighter||(typeof require!=="undefined"?require("shCore").SyntaxHighlighter:null);function A(){function C(G){return"\\b([a-z_]|)"+G.replace(/ /g,"(?=:)\\b|\\b([a-z_\\*]|\\*|)")+"(?=:)\\b"}function F(G){return"\\b"+G.replace(/ /g,"(?!-)(?!:)\\b|\\b()")+":\\b"}var D="ascent azimuth background-attachment background-color background-image background-position background-repeat background baseline bbox border-collapse border-color border-spacing border-style border-top border-right border-bottom border-left border-top-color border-right-color border-bottom-color border-left-color border-top-style border-right-style border-bottom-style border-left-style border-top-width border-right-width border-bottom-width border-left-width border-width border bottom cap-height caption-side centerline clear clip color content counter-increment counter-reset cue-after cue-before cue cursor definition-src descent direction display elevation empty-cells float font-size-adjust font-family font-size font-stretch font-style font-variant font-weight font height left letter-spacing line-height list-style-image list-style-position list-style-type list-style margin-top margin-right margin-bottom margin-left margin marker-offset marks mathline max-height max-width min-height min-width orphans outline-color outline-style outline-width outline overflow padding-top padding-right padding-bottom padding-left padding page page-break-after page-break-before page-break-inside pause pause-after pause-before pitch pitch-range play-during position quotes right richness size slope src speak-header speak-numeral speak-punctuation speak speech-rate stemh stemv stress table-layout text-align top text-decoration text-indent text-shadow text-transform unicode-bidi unicode-range units-per-em vertical-align visibility voice-family volume white-space widows width widths word-spacing x-height z-index";var E="above absolute all always aqua armenian attr aural auto avoid baseline behind below bidi-override black blink block blue bold bolder both bottom braille capitalize caption center center-left center-right circle close-quote code collapse compact condensed continuous counter counters crop cross crosshair cursive dashed decimal decimal-leading-zero default digits disc dotted double embed embossed e-resize expanded extra-condensed extra-expanded fantasy far-left far-right fast faster fixed format fuchsia gray green groove handheld hebrew help hidden hide high higher icon inline-table inline inset inside invert italic justify landscape large larger left-side left leftwards level lighter lime line-through list-item local loud lower-alpha lowercase lower-greek lower-latin lower-roman lower low ltr marker maroon medium message-box middle mix move narrower navy ne-resize no-close-quote none no-open-quote no-repeat normal nowrap n-resize nw-resize oblique olive once open-quote outset outside overline pointer portrait pre print projection purple red relative repeat repeat-x repeat-y rgb ridge right right-side rightwards rtl run-in screen scroll semi-condensed semi-expanded separate se-resize show silent silver slower slow small small-caps small-caption smaller soft solid speech spell-out square s-resize static status-bar sub super sw-resize table-caption table-cell table-column table-column-group table-footer-group table-header-group table-row table-row-group teal text-bottom text-top thick thin top transparent tty tv ultra-condensed ultra-expanded underline upper-alpha uppercase upper-latin upper-roman url visible wait white wider w-resize x-fast x-high x-large x-loud x-low x-slow x-small x-soft xx-large xx-small yellow";var B="[mM]onospace [tT]ahoma [vV]erdana [aA]rial [hH]elvetica [sS]ans-serif [sS]erif [cC]ourier mono sans serif";this.regexList=[{regex:SyntaxHighlighter.regexLib.multiLineCComments,css:"comments"},{regex:SyntaxHighlighter.regexLib.doubleQuotedString,css:"string"},{regex:SyntaxHighlighter.regexLib.singleQuotedString,css:"string"},{regex:/\#[a-fA-F0-9]{3,6}/g,css:"value"},{regex:/(-?\d+)(\.\d+)?(px|em|pt|\:|\%|)/g,css:"value"},{regex:/!important/g,css:"color3"},{regex:new RegExp(C(D),"gm"),css:"keyword"},{regex:new RegExp(F(E),"g"),css:"value"},{regex:new RegExp(this.getKeywords(B),"g"),css:"color1"}];this.forHtmlScript({left:/(&lt;|<)\s*style.*?(&gt;|>)/gi,right:/(&lt;|<)\/\s*style\s*(&gt;|>)/gi})}A.prototype=new SyntaxHighlighter.Highlighter();A.aliases=["css"];SyntaxHighlighter.brushes.CSS=A;typeof(exports)!="undefined"?exports.Brush=A:null})();(function(){SyntaxHighlighter=SyntaxHighlighter||(typeof require!=="undefined"?require("shCore").SyntaxHighlighter:null);function A(){var B="abs addr and ansichar ansistring array as asm begin boolean byte cardinal case char class comp const constructor currency destructor div do double downto else end except exports extended false file finalization finally for function goto if implementation in inherited int64 initialization integer interface is label library longint longword mod nil not object of on or packed pansichar pansistring pchar pcurrency pdatetime pextended pint64 pointer private procedure program property pshortstring pstring pvariant pwidechar pwidestring protected public published raise real real48 record repeat set shl shortint shortstring shr single smallint string then threadvar to true try type unit until uses val var varirnt while widechar widestring with word write writeln xor";this.regexList=[{regex:/\(\*[\s\S]*?\*\)/gm,css:"comments"},{regex:/{(?!\$)[\s\S]*?}/gm,css:"comments"},{regex:SyntaxHighlighter.regexLib.singleLineCComments,css:"comments"},{regex:SyntaxHighlighter.regexLib.singleQuotedString,css:"string"},{regex:/\{\$[a-zA-Z]+ .+\}/g,css:"color1"},{regex:/\b[\d\.]+\b/g,css:"value"},{regex:/\$[a-zA-Z0-9]+\b/g,css:"value"},{regex:new RegExp(this.getKeywords(B),"gmi"),css:"keyword"}]}A.prototype=new SyntaxHighlighter.Highlighter();A.aliases=["delphi","pascal","pas"];SyntaxHighlighter.brushes.Delphi=A;typeof(exports)!="undefined"?exports.Brush=A:null})();(function(){SyntaxHighlighter=SyntaxHighlighter||(typeof require!=="undefined"?require("shCore").SyntaxHighlighter:null);function A(){this.regexList=[{regex:/^\+\+\+ .*$/gm,css:"color2"},{regex:/^\-\-\- .*$/gm,css:"color2"},{regex:/^\s.*$/gm,css:"color1"},{regex:/^@@.*@@.*$/gm,css:"variable"},{regex:/^\+.*$/gm,css:"string"},{regex:/^\-.*$/gm,css:"color3"}]}A.prototype=new SyntaxHighlighter.Highlighter();A.aliases=["diff","patch"];SyntaxHighlighter.brushes.Diff=A;typeof(exports)!="undefined"?exports.Brush=A:null})();(function(){SyntaxHighlighter=SyntaxHighlighter||(typeof require!=="undefined"?require("shCore").SyntaxHighlighter:null);function A(){var B="after and andalso band begin bnot bor bsl bsr bxor case catch cond div end fun if let not of or orelse query receive rem try when xor module export import define";this.regexList=[{regex:new RegExp("[A-Z][A-Za-z0-9_]+","g"),css:"constants"},{regex:new RegExp("\\%.+","gm"),css:"comments"},{regex:new RegExp("\\?[A-Za-z0-9_]+","g"),css:"preprocessor"},{regex:new RegExp("[a-z0-9_]+:[a-z0-9_]+","g"),css:"functions"},{regex:SyntaxHighlighter.regexLib.doubleQuotedString,css:"string"},{regex:SyntaxHighlighter.regexLib.singleQuotedString,css:"string"},{regex:new RegExp(this.getKeywords(B),"gm"),css:"keyword"}]}A.prototype=new SyntaxHighlighter.Highlighter();A.aliases=["erl","erlang"];SyntaxHighlighter.brushes.Erland=A;typeof(exports)!="undefined"?exports.Brush=A:null})();(function(){SyntaxHighlighter=SyntaxHighlighter||(typeof require!=="undefined"?require("shCore").SyntaxHighlighter:null);function A(){var C="as assert break case catch class continue def default do else extends finally if in implements import instanceof interface new package property return switch throw throws try while public protected private static";var B="void boolean byte char short int long float double";var D="null";var E="allProperties count get size collect each eachProperty eachPropertyName eachWithIndex find findAll findIndexOf grep inject max min reverseEach sort asImmutable asSynchronized flatten intersect join pop reverse subMap toList padRight padLeft contains eachMatch toCharacter toLong toUrl tokenize eachFile eachFileRecurse eachB yte eachLine readBytes readLine getText splitEachLine withReader append encodeBase64 decodeBase64 filterLine transformChar transformLine withOutputStream withPrintWriter withStream withStreams withWriter withWriterAppend write writeLine dump inspect invokeMethod print println step times upto use waitForOrKill getText";this.regexList=[{regex:SyntaxHighlighter.regexLib.singleLineCComments,css:"comments"},{regex:SyntaxHighlighter.regexLib.multiLineCComments,css:"comments"},{regex:SyntaxHighlighter.regexLib.doubleQuotedString,css:"string"},{regex:SyntaxHighlighter.regexLib.singleQuotedString,css:"string"},{regex:/""".*"""/g,css:"string"},{regex:new RegExp("\\b([\\d]+(\\.[\\d]+)?|0x[a-f0-9]+)\\b","gi"),css:"value"},{regex:new RegExp(this.getKeywords(C),"gm"),css:"keyword"},{regex:new RegExp(this.getKeywords(B),"gm"),css:"color1"},{regex:new RegExp(this.getKeywords(D),"gm"),css:"constants"},{regex:new RegExp(this.getKeywords(E),"gm"),css:"functions"}];this.forHtmlScript(SyntaxHighlighter.regexLib.aspScriptTags)}A.prototype=new SyntaxHighlighter.Highlighter();A.aliases=["groovy"];SyntaxHighlighter.brushes.Groovy=A;typeof(exports)!="undefined"?exports.Brush=A:null})();(function(){SyntaxHighlighter=SyntaxHighlighter||(typeof require!=="undefined"?require("shCore").SyntaxHighlighter:null);function A(){var B="abstract assert boolean break byte case catch char class const continue default do double else enum extends false final finally float for goto if implements import instanceof int interface long native new null package private protected public return short static strictfp super switch synchronized this throw throws true transient try void volatile while";this.regexList=[{regex:SyntaxHighlighter.regexLib.singleLineCComments,css:"comments"},{regex:/\/\*([^\*][\s\S]*)?\*\//gm,css:"comments"},{regex:/\/\*(?!\*\/)\*[\s\S]*?\*\//gm,css:"preprocessor"},{regex:SyntaxHighlighter.regexLib.doubleQuotedString,css:"string"},{regex:SyntaxHighlighter.regexLib.singleQuotedString,css:"string"},{regex:/\b([\d]+(\.[\d]+)?|0x[a-f0-9]+)\b/gi,css:"value"},{regex:/(?!\@interface\b)\@[\$\w]+\b/g,css:"color1"},{regex:/\@interface\b/g,css:"color2"},{regex:new RegExp(this.getKeywords(B),"gm"),css:"keyword"}];this.forHtmlScript({left:/(&lt;|<)%[@!=]?/g,right:/%(&gt;|>)/g})}A.prototype=new SyntaxHighlighter.Highlighter();A.aliases=["java"];SyntaxHighlighter.brushes.Java=A;typeof(exports)!="undefined"?exports.Brush=A:null})();(function(){SyntaxHighlighter=SyntaxHighlighter||(typeof require!=="undefined"?require("shCore").SyntaxHighlighter:null);function A(){var C="Boolean Byte Character Double Duration Float Integer Long Number Short String Void";var B="abstract after and as assert at before bind bound break catch class continue def delete else exclusive extends false finally first for from function if import in indexof init insert instanceof into inverse last lazy mixin mod nativearray new not null on or override package postinit protected public public-init public-read replace return reverse sizeof step super then this throw true try tween typeof var where while with attribute let private readonly static trigger";this.regexList=[{regex:SyntaxHighlighter.regexLib.singleLineCComments,css:"comments"},{regex:SyntaxHighlighter.regexLib.multiLineCComments,css:"comments"},{regex:SyntaxHighlighter.regexLib.singleQuotedString,css:"string"},{regex:SyntaxHighlighter.regexLib.doubleQuotedString,css:"string"},{regex:/(-?\.?)(\b(\d*\.?\d+|\d+\.?\d*)(e[+-]?\d+)?|0x[a-f\d]+)\b\.?/gi,css:"color2"},{regex:new RegExp(this.getKeywords(C),"gm"),css:"variable"},{regex:new RegExp(this.getKeywords(B),"gm"),css:"keyword"}];this.forHtmlScript(SyntaxHighlighter.regexLib.aspScriptTags)}A.prototype=new SyntaxHighlighter.Highlighter();A.aliases=["jfx","javafx"];SyntaxHighlighter.brushes.JavaFX=A;typeof(exports)!="undefined"?exports.Brush=A:null})();(function(){SyntaxHighlighter=SyntaxHighlighter||(typeof require!=="undefined"?require("shCore").SyntaxHighlighter:null);function A(){var C="break case catch continue default delete do else false  for function if in instanceof new null return super switch this throw true try typeof var while with";var B=SyntaxHighlighter.regexLib;this.regexList=[{regex:B.multiLineDoubleQuotedString,css:"string"},{regex:B.multiLineSingleQuotedString,css:"string"},{regex:B.singleLineCComments,css:"comments"},{regex:B.multiLineCComments,css:"comments"},{regex:/\s*#.*/gm,css:"preprocessor"},{regex:new RegExp(this.getKeywords(C),"gm"),css:"keyword"}];this.forHtmlScript(B.scriptScriptTags)}A.prototype=new SyntaxHighlighter.Highlighter();A.aliases=["js","jscript","javascript"];SyntaxHighlighter.brushes.JScript=A;typeof(exports)!="undefined"?exports.Brush=A:null})();(function(){SyntaxHighlighter=SyntaxHighlighter||(typeof require!=="undefined"?require("shCore").SyntaxHighlighter:null);function A(){var B="abs accept alarm atan2 bind binmode chdir chmod chomp chop chown chr chroot close closedir connect cos crypt defined delete each endgrent endhostent endnetent endprotoent endpwent endservent eof exec exists exp fcntl fileno flock fork format formline getc getgrent getgrgid getgrnam gethostbyaddr gethostbyname gethostent getlogin getnetbyaddr getnetbyname getnetent getpeername getpgrp getppid getpriority getprotobyname getprotobynumber getprotoent getpwent getpwnam getpwuid getservbyname getservbyport getservent getsockname getsockopt glob gmtime grep hex index int ioctl join keys kill lc lcfirst length link listen localtime lock log lstat map mkdir msgctl msgget msgrcv msgsnd oct open opendir ord pack pipe pop pos print printf prototype push quotemeta rand read readdir readline readlink readpipe recv rename reset reverse rewinddir rindex rmdir scalar seek seekdir select semctl semget semop send setgrent sethostent setnetent setpgrp setpriority setprotoent setpwent setservent setsockopt shift shmctl shmget shmread shmwrite shutdown sin sleep socket socketpair sort splice split sprintf sqrt srand stat study substr symlink syscall sysopen sysread sysseek system syswrite tell telldir time times tr truncate uc ucfirst umask undef unlink unpack unshift utime values vec wait waitpid warn write say";var C="bless caller continue dbmclose dbmopen die do dump else elsif eval exit for foreach goto if import last local my next no our package redo ref require return sub tie tied unless untie until use wantarray while given when default try catch finally has extends with before after around override augment";this.regexList=[{regex:/(<<|&lt;&lt;)((\w+)|(['"])(.+?)\4)[\s\S]+?\n\3\5\n/g,css:"string"},{regex:/#.*$/gm,css:"comments"},{regex:/^#!.*\n/g,css:"preprocessor"},{regex:/-?\w+(?=\s*=(>|&gt;))/g,css:"string"},{regex:/\bq[qwxr]?\([\s\S]*?\)/g,css:"string"},{regex:/\bq[qwxr]?\{[\s\S]*?\}/g,css:"string"},{regex:/\bq[qwxr]?\[[\s\S]*?\]/g,css:"string"},{regex:/\bq[qwxr]?(<|&lt;)[\s\S]*?(>|&gt;)/g,css:"string"},{regex:/\bq[qwxr]?([^\w({<[])[\s\S]*?\1/g,css:"string"},{regex:SyntaxHighlighter.regexLib.doubleQuotedString,css:"string"},{regex:SyntaxHighlighter.regexLib.singleQuotedString,css:"string"},{regex:/(?:&amp;|[$@%*]|\$#)[a-zA-Z_](\w+|::)*/g,css:"variable"},{regex:/\b__(?:END|DATA)__\b[\s\S]*$/g,css:"comments"},{regex:/(^|\n)=\w[\s\S]*?(\n=cut\s*\n|$)/g,css:"comments"},{regex:new RegExp(this.getKeywords(B),"gm"),css:"functions"},{regex:new RegExp(this.getKeywords(C),"gm"),css:"keyword"}];this.forHtmlScript(SyntaxHighlighter.regexLib.phpScriptTags)}A.prototype=new SyntaxHighlighter.Highlighter();A.aliases=["perl","Perl","pl"];SyntaxHighlighter.brushes.Perl=A;typeof(exports)!="undefined"?exports.Brush=A:null})();(function(){SyntaxHighlighter=SyntaxHighlighter||(typeof require!=="undefined"?require("shCore").SyntaxHighlighter:null);function A(){var B="abs acos acosh addcslashes addslashes array_change_key_case array_chunk array_combine array_count_values array_diff array_diff_assoc array_diff_key array_diff_uassoc array_diff_ukey array_fill array_filter array_flip array_intersect array_intersect_assoc array_intersect_key array_intersect_uassoc array_intersect_ukey array_key_exists array_keys array_map array_merge array_merge_recursive array_multisort array_pad array_pop array_product array_push array_rand array_reduce array_reverse array_search array_shift array_slice array_splice array_sum array_udiff array_udiff_assoc array_udiff_uassoc array_uintersect array_uintersect_assoc array_uintersect_uassoc array_unique array_unshift array_values array_walk array_walk_recursive atan atan2 atanh base64_decode base64_encode base_convert basename bcadd bccomp bcdiv bcmod bcmul bindec bindtextdomain bzclose bzcompress bzdecompress bzerrno bzerror bzerrstr bzflush bzopen bzread bzwrite ceil chdir checkdate checkdnsrr chgrp chmod chop chown chr chroot chunk_split class_exists closedir closelog copy cos cosh count count_chars date decbin dechex decoct deg2rad delete ebcdic2ascii echo empty end ereg ereg_replace eregi eregi_replace error_log error_reporting escapeshellarg escapeshellcmd eval exec exit exp explode extension_loaded feof fflush fgetc fgetcsv fgets fgetss file_exists file_get_contents file_put_contents fileatime filectime filegroup fileinode filemtime fileowner fileperms filesize filetype floatval flock floor flush fmod fnmatch fopen fpassthru fprintf fputcsv fputs fread fscanf fseek fsockopen fstat ftell ftok getallheaders getcwd getdate getenv gethostbyaddr gethostbyname gethostbynamel getimagesize getlastmod getmxrr getmygid getmyinode getmypid getmyuid getopt getprotobyname getprotobynumber getrandmax getrusage getservbyname getservbyport gettext gettimeofday gettype glob gmdate gmmktime ini_alter ini_get ini_get_all ini_restore ini_set interface_exists intval ip2long is_a is_array is_bool is_callable is_dir is_double is_executable is_file is_finite is_float is_infinite is_int is_integer is_link is_long is_nan is_null is_numeric is_object is_readable is_real is_resource is_scalar is_soap_fault is_string is_subclass_of is_uploaded_file is_writable is_writeable mkdir mktime nl2br parse_ini_file parse_str parse_url passthru pathinfo print readlink realpath rewind rewinddir rmdir round str_ireplace str_pad str_repeat str_replace str_rot13 str_shuffle str_split str_word_count strcasecmp strchr strcmp strcoll strcspn strftime strip_tags stripcslashes stripos stripslashes stristr strlen strnatcasecmp strnatcmp strncasecmp strncmp strpbrk strpos strptime strrchr strrev strripos strrpos strspn strstr strtok strtolower strtotime strtoupper strtr strval substr substr_compare";var C="abstract and array as break case catch cfunction class clone const continue declare default die do else elseif enddeclare endfor endforeach endif endswitch endwhile extends final for foreach function global goto if implements include include_once interface instanceof insteadof namespace new old_function or private protected public return require require_once static switch trait throw try use var while xor ";var D="__FILE__ __LINE__ __METHOD__ __FUNCTION__ __CLASS__";this.regexList=[{regex:SyntaxHighlighter.regexLib.singleLineCComments,css:"comments"},{regex:SyntaxHighlighter.regexLib.multiLineCComments,css:"comments"},{regex:SyntaxHighlighter.regexLib.doubleQuotedString,css:"string"},{regex:SyntaxHighlighter.regexLib.singleQuotedString,css:"string"},{regex:/\$\w+/g,css:"variable"},{regex:new RegExp(this.getKeywords(B),"gmi"),css:"functions"},{regex:new RegExp(this.getKeywords(D),"gmi"),css:"constants"},{regex:new RegExp(this.getKeywords(C),"gm"),css:"keyword"}];this.forHtmlScript(SyntaxHighlighter.regexLib.phpScriptTags)}A.prototype=new SyntaxHighlighter.Highlighter();A.aliases=["php"];SyntaxHighlighter.brushes.Php=A;typeof(exports)!="undefined"?exports.Brush=A:null})();(function(){SyntaxHighlighter=SyntaxHighlighter||(typeof require!=="undefined"?require("shCore").SyntaxHighlighter:null);function A(){}A.prototype=new SyntaxHighlighter.Highlighter();A.aliases=["text","plain"];SyntaxHighlighter.brushes.Plain=A;typeof(exports)!="undefined"?exports.Brush=A:null})();(function(){SyntaxHighlighter=SyntaxHighlighter||(typeof require!=="undefined"?require("shCore").SyntaxHighlighter:null);function A(){var C="while validateset validaterange validatepattern validatelength validatecount until trap switch return ref process param parameter in if global: function foreach for finally filter end elseif else dynamicparam do default continue cmdletbinding break begin alias \\? % #script #private #local #global mandatory parametersetname position valuefrompipeline valuefrompipelinebypropertyname valuefromremainingarguments helpmessage ";var D=" and as band bnot bor bxor casesensitive ccontains ceq cge cgt cle clike clt cmatch cne cnotcontains cnotlike cnotmatch contains creplace eq exact f file ge gt icontains ieq ige igt ile ilike ilt imatch ine inotcontains inotlike inotmatch ireplace is isnot le like lt match ne not notcontains notlike notmatch or regex replace wildcard";var B="write where wait use update unregister undo trace test tee take suspend stop start split sort skip show set send select scroll resume restore restart resolve resize reset rename remove register receive read push pop ping out new move measure limit join invoke import group get format foreach export expand exit enter enable disconnect disable debug cxnew copy convertto convertfrom convert connect complete compare clear checkpoint aggregate add";var E=" component description example externalhelp forwardhelpcategory forwardhelptargetname forwardhelptargetname functionality inputs link notes outputs parameter remotehelprunspace role synopsis";this.regexList=[{regex:new RegExp("^\\s*#[#\\s]*\\.("+this.getKeywords(E)+").*$","gim"),css:"preprocessor help bold"},{regex:SyntaxHighlighter.regexLib.singleLinePerlComments,css:"comments"},{regex:/(&lt;|<)#[\s\S]*?#(&gt;|>)/gm,css:"comments here"},{regex:new RegExp('@"\\n[\\s\\S]*?\\n"@',"gm"),css:"script string here"},{regex:new RegExp("@'\\n[\\s\\S]*?\\n'@","gm"),css:"script string single here"},{regex:new RegExp('"(?:\\$\\([^\\)]*\\)|[^"]|`"|"")*[^`]"',"g"),css:"string"},{regex:new RegExp("'(?:[^']|'')*'","g"),css:"string single"},{regex:new RegExp("[\\$|@|@@](?:(?:global|script|private|env):)?[A-Z0-9_]+","gi"),css:"variable"},{regex:new RegExp("(?:\\b"+B.replace(/ /g,"\\b|\\b")+")-[a-zA-Z_][a-zA-Z0-9_]*","gmi"),css:"functions"},{regex:new RegExp(this.getKeywords(C),"gmi"),css:"keyword"},{regex:new RegExp("-"+this.getKeywords(D),"gmi"),css:"operator value"},{regex:new RegExp("\\[[A-Z_\\[][A-Z0-9_. `,\\[\\]]*\\]","gi"),css:"constants"},{regex:new RegExp("\\s+-(?!"+this.getKeywords(D)+")[a-zA-Z_][a-zA-Z0-9_]*","gmi"),css:"color1"},]}A.prototype=new SyntaxHighlighter.Highlighter();A.aliases=["powershell","ps","posh"];SyntaxHighlighter.brushes.PowerShell=A;typeof(exports)!="undefined"?exports.Brush=A:null})();(function(){SyntaxHighlighter=SyntaxHighlighter||(typeof require!=="undefined"?require("shCore").SyntaxHighlighter:null);function A(){var D="and assert break class continue def del elif else except exec finally for from global if import in is lambda not or pass print raise return try yield while";var C="__import__ abs all any apply basestring bin bool buffer callable chr classmethod cmp coerce compile complex delattr dict dir divmod enumerate eval execfile file filter float format frozenset getattr globals hasattr hash help hex id input int intern isinstance issubclass iter len list locals long map max min next object oct open ord pow print property range raw_input reduce reload repr reversed round set setattr slice sorted staticmethod str sum super tuple type type unichr unicode vars xrange zip";var B="None True False self cls class_";this.regexList=[{regex:SyntaxHighlighter.regexLib.singleLinePerlComments,css:"comments"},{regex:/^\s*@\w+/gm,css:"decorator"},{regex:/(['\"]{3})([^\1])*?\1/gm,css:"comments"},{regex:/"(?!")(?:\.|\\\"|[^\""\n])*"/gm,css:"string"},{regex:/'(?!')(?:\.|(\\\')|[^\''\n])*'/gm,css:"string"},{regex:/\+|\-|\*|\/|\%|=|==/gm,css:"keyword"},{regex:/\b\d+\.?\w*/g,css:"value"},{regex:new RegExp(this.getKeywords(C),"gmi"),css:"functions"},{regex:new RegExp(this.getKeywords(D),"gm"),css:"keyword"},{regex:new RegExp(this.getKeywords(B),"gm"),css:"color1"}];this.forHtmlScript(SyntaxHighlighter.regexLib.aspScriptTags)}A.prototype=new SyntaxHighlighter.Highlighter();A.aliases=["py","python"];SyntaxHighlighter.brushes.Python=A;typeof(exports)!="undefined"?exports.Brush=A:null})();(function(){SyntaxHighlighter=SyntaxHighlighter||(typeof require!=="undefined"?require("shCore").SyntaxHighlighter:null);function A(){var C="alias and BEGIN begin break case class def define_method defined do each else elsif END end ensure false for if in module new next nil not or raise redo rescue retry return self super then throw true undef unless until when while yield";var B="Array Bignum Binding Class Continuation Dir Exception FalseClass File::Stat File Fixnum Fload Hash Integer IO MatchData Method Module NilClass Numeric Object Proc Range Regexp String Struct::TMS Symbol ThreadGroup Thread Time TrueClass";this.regexList=[{regex:SyntaxHighlighter.regexLib.singleLinePerlComments,css:"comments"},{regex:SyntaxHighlighter.regexLib.doubleQuotedString,css:"string"},{regex:SyntaxHighlighter.regexLib.singleQuotedString,css:"string"},{regex:/\b[A-Z0-9_]+\b/g,css:"constants"},{regex:/:[a-z][A-Za-z0-9_]*/g,css:"color2"},{regex:/(\$|@@|@)\w+/g,css:"variable bold"},{regex:new RegExp(this.getKeywords(C),"gm"),css:"keyword"},{regex:new RegExp(this.getKeywords(B),"gm"),css:"color1"}];this.forHtmlScript(SyntaxHighlighter.regexLib.aspScriptTags)}A.prototype=new SyntaxHighlighter.Highlighter();A.aliases=["ruby","rails","ror","rb"];SyntaxHighlighter.brushes.Ruby=A;typeof(exports)!="undefined"?exports.Brush=A:null})();(function(){SyntaxHighlighter=SyntaxHighlighter||(typeof require!=="undefined"?require("shCore").SyntaxHighlighter:null);function A(){function D(J){return"\\b([a-z_]|)"+J.replace(/ /g,"(?=:)\\b|\\b([a-z_\\*]|\\*|)")+"(?=:)\\b"}function H(J){return"\\b"+J.replace(/ /g,"(?!-)(?!:)\\b|\\b()")+":\\b"}var F="ascent azimuth background-attachment background-color background-image background-position background-repeat background baseline bbox border-collapse border-color border-spacing border-style border-top border-right border-bottom border-left border-top-color border-right-color border-bottom-color border-left-color border-top-style border-right-style border-bottom-style border-left-style border-top-width border-right-width border-bottom-width border-left-width border-width border bottom cap-height caption-side centerline clear clip color content counter-increment counter-reset cue-after cue-before cue cursor definition-src descent direction display elevation empty-cells float font-size-adjust font-family font-size font-stretch font-style font-variant font-weight font height left letter-spacing line-height list-style-image list-style-position list-style-type list-style margin-top margin-right margin-bottom margin-left margin marker-offset marks mathline max-height max-width min-height min-width orphans outline-color outline-style outline-width outline overflow padding-top padding-right padding-bottom padding-left padding page page-break-after page-break-before page-break-inside pause pause-after pause-before pitch pitch-range play-during position quotes right richness size slope src speak-header speak-numeral speak-punctuation speak speech-rate stemh stemv stress table-layout text-align top text-decoration text-indent text-shadow text-transform unicode-bidi unicode-range units-per-em vertical-align visibility voice-family volume white-space widows width widths word-spacing x-height z-index";var I="above absolute all always aqua armenian attr aural auto avoid baseline behind below bidi-override black blink block blue bold bolder both bottom braille capitalize caption center center-left center-right circle close-quote code collapse compact condensed continuous counter counters crop cross crosshair cursive dashed decimal decimal-leading-zero digits disc dotted double embed embossed e-resize expanded extra-condensed extra-expanded fantasy far-left far-right fast faster fixed format fuchsia gray green groove handheld hebrew help hidden hide high higher icon inline-table inline inset inside invert italic justify landscape large larger left-side left leftwards level lighter lime line-through list-item local loud lower-alpha lowercase lower-greek lower-latin lower-roman lower low ltr marker maroon medium message-box middle mix move narrower navy ne-resize no-close-quote none no-open-quote no-repeat normal nowrap n-resize nw-resize oblique olive once open-quote outset outside overline pointer portrait pre print projection purple red relative repeat repeat-x repeat-y rgb ridge right right-side rightwards rtl run-in screen scroll semi-condensed semi-expanded separate se-resize show silent silver slower slow small small-caps small-caption smaller soft solid speech spell-out square s-resize static status-bar sub super sw-resize table-caption table-cell table-column table-column-group table-footer-group table-header-group table-row table-row-group teal text-bottom text-top thick thin top transparent tty tv ultra-condensed ultra-expanded underline upper-alpha uppercase upper-latin upper-roman url visible wait white wider w-resize x-fast x-high x-large x-loud x-low x-slow x-small x-soft xx-large xx-small yellow";var G="[mM]onospace [tT]ahoma [vV]erdana [aA]rial [hH]elvetica [sS]ans-serif [sS]erif [cC]ourier mono sans serif";var C="!important !default";var E="@import @extend @debug @warn @if @for @while @mixin @include";var B=SyntaxHighlighter.regexLib;this.regexList=[{regex:B.multiLineCComments,css:"comments"},{regex:B.singleLineCComments,css:"comments"},{regex:B.doubleQuotedString,css:"string"},{regex:B.singleQuotedString,css:"string"},{regex:/\#[a-fA-F0-9]{3,6}/g,css:"value"},{regex:/\b(-?\d+)(\.\d+)?(px|em|pt|\:|\%|)\b/g,css:"value"},{regex:/\$\w+/g,css:"variable"},{regex:new RegExp(this.getKeywords(C),"g"),css:"color3"},{regex:new RegExp(this.getKeywords(E),"g"),css:"preprocessor"},{regex:new RegExp(D(F),"gm"),css:"keyword"},{regex:new RegExp(H(I),"g"),css:"value"},{regex:new RegExp(this.getKeywords(G),"g"),css:"color1"}]}A.prototype=new SyntaxHighlighter.Highlighter();A.aliases=["sass","scss"];SyntaxHighlighter.brushes.Sass=A;typeof(exports)!="undefined"?exports.Brush=A:null})();(function(){SyntaxHighlighter=SyntaxHighlighter||(typeof require!=="undefined"?require("shCore").SyntaxHighlighter:null);function A(){var C="val sealed case def true trait implicit forSome import match object null finally super override try lazy for var catch throw type extends class while with new final yield abstract else do if return protected private this package false";var B="[_:=><%#@]+";this.regexList=[{regex:SyntaxHighlighter.regexLib.singleLineCComments,css:"comments"},{regex:SyntaxHighlighter.regexLib.multiLineCComments,css:"comments"},{regex:SyntaxHighlighter.regexLib.multiLineSingleQuotedString,css:"string"},{regex:SyntaxHighlighter.regexLib.multiLineDoubleQuotedString,css:"string"},{regex:SyntaxHighlighter.regexLib.singleQuotedString,css:"string"},{regex:/0x[a-f0-9]+|\d+(\.\d+)?/gi,css:"value"},{regex:new RegExp(this.getKeywords(C),"gm"),css:"keyword"},{regex:new RegExp(B,"gm"),css:"keyword"}]}A.prototype=new SyntaxHighlighter.Highlighter();A.aliases=["scala"];SyntaxHighlighter.brushes.Scala=A;typeof(exports)!="undefined"?exports.Brush=A:null})();(function(){SyntaxHighlighter=SyntaxHighlighter||(typeof require!=="undefined"?require("shCore").SyntaxHighlighter:null);function A(){var B="abs avg case cast coalesce convert count current_timestamp current_user day isnull left lower month nullif replace right session_user space substring sum system_user upper user year";var C="absolute action add after alter as asc at authorization begin bigint binary bit by cascade char character check checkpoint close collate column commit committed connect connection constraint contains continue create cube current current_date current_time cursor database date deallocate dec decimal declare default delete desc distinct double drop dynamic else end end-exec escape except exec execute false fetch first float for force foreign forward free from full function global goto grant group grouping having hour ignore index inner insensitive insert instead int integer intersect into is isolation key last level load local max min minute modify move name national nchar next no numeric of off on only open option order out output partial password precision prepare primary prior privileges procedure public read real references relative repeatable restrict return returns revoke rollback rollup rows rule schema scroll second section select sequence serializable set size smallint static statistics table temp temporary then time timestamp to top transaction translation trigger true truncate uncommitted union unique update values varchar varying view when where with work";var D="all and any between cross in join like not null or outer some";this.regexList=[{regex:/--(.*)$/gm,css:"comments"},{regex:SyntaxHighlighter.regexLib.multiLineDoubleQuotedString,css:"string"},{regex:SyntaxHighlighter.regexLib.multiLineSingleQuotedString,css:"string"},{regex:new RegExp(this.getKeywords(B),"gmi"),css:"color2"},{regex:new RegExp(this.getKeywords(D),"gmi"),css:"color1"},{regex:new RegExp(this.getKeywords(C),"gmi"),css:"keyword"}]}A.prototype=new SyntaxHighlighter.Highlighter();A.aliases=["sql"];SyntaxHighlighter.brushes.Sql=A;typeof(exports)!="undefined"?exports.Brush=A:null})();(function(){SyntaxHighlighter=SyntaxHighlighter||(typeof require!=="undefined"?require("shCore").SyntaxHighlighter:null);function A(){var B="AddHandler AddressOf AndAlso Alias And Ansi As Assembly Auto Boolean ByRef Byte ByVal Call Case Catch CBool CByte CChar CDate CDec CDbl Char CInt Class CLng CObj Const CShort CSng CStr CType Date Decimal Declare Default Delegate Dim DirectCast Do Double Each Else ElseIf End Enum Erase Error Event Exit False Finally For Friend Function Get GetType GoSub GoTo Handles If Implements Imports In Inherits Integer Interface Is Let Lib Like Long Loop Me Mod Module MustInherit MustOverride MyBase MyClass Namespace New Next Not Nothing NotInheritable NotOverridable Object On Option Optional Or OrElse Overloads Overridable Overrides ParamArray Preserve Private Property Protected Public RaiseEvent ReadOnly ReDim REM RemoveHandler Resume Return Select Set Shadows Shared Short Single Static Step Stop String Structure Sub SyncLock Then Throw To True Try TypeOf Unicode Until Variant When While With WithEvents WriteOnly Xor";this.regexList=[{regex:/'.*$/gm,css:"comments"},{regex:SyntaxHighlighter.regexLib.doubleQuotedString,css:"string"},{regex:/^\s*#.*$/gm,css:"preprocessor"},{regex:new RegExp(this.getKeywords(B),"gm"),css:"keyword"}];this.forHtmlScript(SyntaxHighlighter.regexLib.aspScriptTags)}A.prototype=new SyntaxHighlighter.Highlighter();A.aliases=["vb","vbnet"];SyntaxHighlighter.brushes.Vb=A;typeof(exports)!="undefined"?exports.Brush=A:null})();(function(){SyntaxHighlighter=SyntaxHighlighter||(typeof require!=="undefined"?require("shCore").SyntaxHighlighter:null);function A(){function B(F,J){var C=SyntaxHighlighter.Match,G=F[0],I=new XRegExp("(&lt;|<)[\\s\\/\\?]*(?<name>[:\\w-\\.]+)","xg").exec(G),D=[];if(F.attributes!=null){var H,E=new XRegExp("(?<name> [\\w:\\-\\.]+)\\s*=\\s*(?<value> \".*?\"|'.*?'|\\w+)","xg");while((H=E.exec(G))!=null){D.push(new C(H.name,F.index+H.index,"color1"));D.push(new C(H.value,F.index+H.index+H[0].indexOf(H.value),"string"))}}if(I!=null){D.push(new C(I.name,F.index+I[0].indexOf(I.name),"keyword"))}return D}this.regexList=[{regex:new XRegExp("(\\&lt;|<)\\!\\[[\\w\\s]*?\\[(.|\\s)*?\\]\\](\\&gt;|>)","gm"),css:"color2"},{regex:SyntaxHighlighter.regexLib.xmlComments,css:"comments"},{regex:new XRegExp("(&lt;|<)[\\s\\/\\?]*(\\w+)(?<attributes>.*?)[\\s\\/\\?]*(&gt;|>)","sg"),func:B}]}A.prototype=new SyntaxHighlighter.Highlighter();A.aliases=["xml","xhtml","xslt","html"];SyntaxHighlighter.brushes.Xml=A;typeof(exports)!="undefined"?exports.Brush=A:null})();