!function(G,E){var F,J={},A=function(O,M){var N,K,L;if("string"==typeof O){return C(O)}for(N=[],K=O.length,L=0;K>L;L++){N.push(C(O[L]))}return M.apply(null,N)},H=function(M,K,L){2===arguments.length&&(L=K,K=null),A(K||[],function(){I(M,L,arguments)})},I=function(N,L,M){var O,K={exports:L};"function"==typeof L&&(M.length||(M=[A,K.exports,K]),O=L.apply(null,M),void 0!==O&&(K.exports=O)),J[N]=K.exports},C=function(K){var L=J[K]||G[K];if(!L){throw new Error("`"+K+"` is undefined")}return L},D=function(P){var N,O,M,Q,K,L;L=function(R){return R&&R.charAt(0).toUpperCase()+R.substr(1)};for(N in J){if(O=P,J.hasOwnProperty(N)){for(M=N.split("/"),K=L(M.pop());Q=L(M.shift());){O[Q]=O[Q]||{},O=O[Q]}O[K]=J[N]}}},B=E(G,H,A);D(B),"object"==typeof module&&"object"==typeof module.exports?module.exports=B:"function"==typeof define&&define.amd?define([],B):(F=G.WebUploader,G.WebUploader=B,G.WebUploader.noConflict=function(){G.WebUploader=F})}(this,function(C,A,B){return A("dollar-third",[],function(){return C.jQuery||C.Zepto}),A("dollar",["dollar-third"],function(D){return D}),A("promise-third",["dollar"],function(D){return{Deferred:D.Deferred,when:D.when,isPromise:function(E){return E&&"function"==typeof E.then}}}),A("promise",["promise-third"],function(D){return D}),A("base",["dollar","promise"],function(H,I){function E(K){return function(){return G.apply(K,arguments)}}function F(L,K){return function(){return L.apply(K,arguments)}}function J(L){var K;return Object.create?Object.create(L):(K=function(){},K.prototype=L,new K)}var D=function(){},G=Function.call;return{version:"0.1.2",$:H,Deferred:I.Deferred,isPromise:I.isPromise,when:I.when,browser:function(O){var M={},N=O.match(/WebKit\/([\d.]+)/),R=O.match(/Chrome\/([\d.]+)/)||O.match(/CriOS\/([\d.]+)/),K=O.match(/MSIE\s([\d\.]+)/)||O.match(/(?:trident)(?:.*rv:([\w.]+))?/i),P=O.match(/Firefox\/([\d.]+)/),Q=O.match(/Safari\/([\d.]+)/),L=O.match(/OPR\/([\d.]+)/);return N&&(M.webkit=parseFloat(N[1])),R&&(M.chrome=parseFloat(R[1])),K&&(M.ie=parseFloat(K[1])),P&&(M.firefox=parseFloat(P[1])),Q&&(M.safari=parseFloat(Q[1])),L&&(M.opera=parseFloat(L[1])),M}(navigator.userAgent),os:function(N){var L={},M=N.match(/(?:Android);?[\s\/]+([\d.]+)?/),K=N.match(/(?:iPad|iPod|iPhone).*OS\s([\d_]+)/);return M&&(L.android=parseFloat(M[1])),K&&(L.ios=parseFloat(K[1].replace(/_/g,"."))),L}(navigator.userAgent),inherits:function(N,M,K){var L;return"function"==typeof M?(L=M,M=null):L=M&&M.hasOwnProperty("constructor")?M.constructor:function(){return N.apply(this,arguments)},H.extend(!0,L,N,K||{}),L.__super__=N.prototype,L.prototype=J(N.prototype),M&&H.extend(!0,L.prototype,M),L},noop:D,bindFn:F,log:function(){return C.console?F(console.log,console):D}(),nextTick:function(){return function(K){setTimeout(K,1)}}(),slice:E([].slice),guid:function(){var K=0;return function(M){for(var N=(+new Date).toString(32),L=0;5>L;L++){N+=Math.floor(65535*Math.random()).toString(32)}return(M||"wu_")+N+(K++).toString(32)}}(),formatSize:function(N,L,M){var K;for(M=M||["B","K","M","G","TB"];(K=M.shift())&&N>1024;){N/=1024}return("B"===K?N:N.toFixed(L||2))+K}}}),A("mediator",["base"],function(H){function F(O,M,N,L){return I.grep(O,function(P){return !(!P||M&&P.e!==M||N&&P.cb!==N&&P.cb._cb!==N||L&&P.ctx!==L)})}function G(N,L,M){I.each((N||"").split(E),function(P,O){M(O,L)})}function K(P,N){for(var O,L=!1,M=-1,Q=P.length;++M<Q;){if(O=P[M],O.cb.apply(O.ctx2,N)===!1){L=!0;break}}return !L}var D,I=H.$,J=[].slice,E=/\s+/;return D={on:function(O,N,L){var M,P=this;return N?(M=this._events||(this._events=[]),G(O,N,function(S,Q){var R={e:S};R.cb=Q,R.ctx=L,R.ctx2=L||P,R.id=M.length,M.push(R)}),this):this},once:function(O,N,L){var M=this;return N?(G(O,N,function(R,P){var Q=function(){return M.off(R,Q),P.apply(L||M,arguments)};Q._cb=P,M.on(R,Q,L)}),M):M},off:function(O,M,N){var L=this._events;return L?O||M||N?(G(O,M,function(Q,P){I.each(F(L,Q,P,N),function(){delete L[this.id]})}),this):(this._events=[],this):this},trigger:function(N){var M,L,O;return this._events&&N?(M=J.call(arguments,1),L=F(this._events,N),O=F(this._events,"all"),K(L,M)&&K(O,arguments)):this}},I.extend({installTo:function(L){return I.extend(L,D)}},D)}),A("uploader",["base","mediator"],function(G,E){function F(H){this.options=D.extend(!0,{},F.options,H),this._init(this.options)}var D=G.$;return F.options={},E.installTo(F.prototype),D.each({upload:"start-upload",stop:"stop-upload",getFile:"get-file",getFiles:"get-files",addFile:"add-file",addFiles:"add-file",sort:"sort-files",removeFile:"remove-file",skipFile:"skip-file",retry:"retry",isInProgress:"is-in-progress",makeThumb:"make-thumb",getDimension:"get-dimension",addButton:"add-btn",getRuntimeType:"get-runtime-type",refresh:"refresh",disable:"disable",enable:"enable",reset:"reset"},function(I,H){F.prototype[I]=function(){return this.request(H,arguments)}}),D.extend(F.prototype,{state:"pending",_init:function(I){var H=this;H.request("init",I,function(){H.state="ready",H.trigger("ready")})},option:function(J,H){var I=this.options;return arguments.length>1?void (D.isPlainObject(H)&&D.isPlainObject(I[J])?D.extend(I[J],H):I[J]=H):J?I[J]:I},getStats:function(){var H=this.request("get-stats");return{successNum:H.numOfSuccess,cancelNum:H.numOfCancel,invalidNum:H.numOfInvalid,uploadFailNum:H.numOfUploadFailed,queueNum:H.numOfQueue}},trigger:function(J){var I=[].slice.call(arguments,1),H=this.options,K="on"+J.substring(0,1).toUpperCase()+J.substring(1);return E.trigger.apply(this,arguments)===!1||D.isFunction(H[K])&&H[K].apply(this,I)===!1||D.isFunction(this[K])&&this[K].apply(this,I)===!1||E.trigger.apply(E,[this,J].concat(I))===!1?!1:!0},request:G.noop}),G.create=F.create=function(H){return new F(H)},G.Uploader=F,F}),A("runtime/runtime",["base","mediator"],function(H,F){function G(J){this.options=D.extend({container:document.body},J),this.uid=H.guid("rt_")}var D=H.$,E={},I=function(K){for(var J in K){if(K.hasOwnProperty(J)){return J}}return null};return D.extend(G.prototype,{getContainer:function(){var L,J,K=this.options;return this._container?this._container:(L=D(K.container||document.body),J=D(document.createElement("div")),J.attr("id","rt_"+this.uid),J.css({position:"absolute",top:"0px",left:"0px",width:"1px",height:"1px",overflow:"hidden"}),L.append(J),L.addClass("webuploader-container"),this._container=J,J)},init:H.noop,exec:H.noop,destroy:function(){this._container&&this._container.parentNode.removeChild(this.__container),this.off()}}),G.orders="html5,flash",G.addRuntime=function(K,J){E[K]=J},G.hasRuntime=function(J){return !!(J?E[J]:I(E))},G.create=function(M,L){var J,K;if(L=L||G.orders,D.each(L.split(/\s*,\s*/g),function(){return E[this]?(J=this,!1):void 0}),J=J||I(E),!J){throw new Error("Runtime Error")}return K=new E[J](M)},F.installTo(G.prototype),G}),A("runtime/client",["base","mediator","runtime/runtime"],function(H,F,G){function D(K,J){var L,I=H.Deferred();this.uid=H.guid("client_"),this.runtimeReady=function(M){return I.done(M)},this.connectRuntime=function(N,M){if(L){throw new Error("already connected!")}return I.done(M),"string"==typeof N&&E.get(N)&&(L=E.get(N)),L=L||E.get(null,J),L?(H.$.extend(L.options,N),L.__promise.then(I.resolve),L.__client++):(L=G.create(N,N.runtimeOrder),L.__promise=I.promise(),L.once("ready",I.resolve),L.init(),E.add(L),L.__client=1),J&&(L.__standalone=J),L},this.getRuntime=function(){return L},this.disconnectRuntime=function(){L&&(L.__client--,L.__client<=0&&(E.remove(L),delete L.__promise,L.destroy()),L=null)},this.exec=function(){if(L){var M=H.slice(arguments);return K&&M.unshift(K),L.exec.apply(this,M)}},this.getRuid=function(){return L&&L.uid},this.destroy=function(M){return function(){M&&M.apply(this,arguments),this.trigger("destroy"),this.off(),this.exec("destroy"),this.disconnectRuntime()}}(this.destroy)}var E;return E=function(){var I={};return{add:function(J){I[J.uid]=J},get:function(K,L){var J;if(K){return I[K]}for(J in I){if(!L||!I[J].__standalone){return I[J]}}return null},remove:function(J){delete I[J.uid]}}}(),F.installTo(D.prototype),D}),A("lib/blob",["base","runtime/client"],function(F,D){function E(I,H){var G=this;G.source=H,G.ruid=I,D.call(G,"Blob"),this.uid=H.uid||this.uid,this.type=H.type||"",this.size=H.size||0,I&&G.connectRuntime(I)}return F.inherits(D,{constructor:E,slice:function(H,G){return this.exec("slice",H,G)},getSource:function(){return this.source}}),E}),A("lib/file",["base","lib/blob"],function(H,F){function G(J,I){var K;F.apply(this,arguments),this.name=I.name||"untitled"+D++,K=E.exec(I.name)?RegExp.$1.toLowerCase():"",!K&&this.type&&(K=/\/(jpg|jpeg|png|gif|bmp)$/i.exec(this.type)?RegExp.$1.toLowerCase():"",this.name+="."+K),!this.type&&~"jpg,jpeg,png,gif,bmp".indexOf(K)&&(this.type="image/"+("jpg"===K?"jpeg":K)),this.ext=K,this.lastModifiedDate=I.lastModifiedDate||(new Date).toLocaleString()}var D=1,E=/\.([^.]+)$/;return H.inherits(F,G)}),A("lib/filepicker",["base","runtime/client","lib/file"],function(F,G,D){function E(I){if(I=this.options=H.extend({},E.options,I),I.container=H(I.id),!I.container.length){throw new Error("按钮指定错误")}I.innerHTML=I.innerHTML||I.label||I.container.html()||"",I.button=H(I.button||document.createElement("div")),I.button.html(I.innerHTML),I.container.html(I.button),G.call(this,"FilePicker",!0)}var H=F.$;return E.options={button:null,container:null,label:null,innerHTML:null,multiple:!0,accept:null,name:"file"},F.inherits(G,{constructor:E,init:function(){var J=this,K=J.options,I=K.button;I.addClass("webuploader-pick"),J.on("all",function(M){var L;switch(M){case"mouseenter":I.addClass("webuploader-pick-hover");break;case"mouseleave":I.removeClass("webuploader-pick-hover");break;case"change":L=J.exec("getFiles"),J.trigger("select",H.map(L,function(N){return N=new D(J.getRuid(),N),N._refer=K.container,N}),K.container)}}),J.connectRuntime(K,function(){J.refresh(),J.exec("init",K),J.trigger("ready")}),H(C).on("resize",function(){J.refresh()})},refresh:function(){var M=this.getRuntime().getContainer(),K=this.options.button,L=K.outerWidth?K.outerWidth():K.width(),I=K.outerHeight?K.outerHeight():K.height(),J=K.offset();L&&I&&M.css({bottom:"auto",right:"auto",width:L+"px",height:I+"px"}).offset(J)},enable:function(){var I=this.options.button;I.removeClass("webuploader-pick-disable"),this.refresh()},disable:function(){var I=this.options.button;this.getRuntime().getContainer().css({top:"-99999px"}),I.addClass("webuploader-pick-disable")},destroy:function(){this.runtime&&(this.exec("destroy"),this.disconnectRuntime())}}),E}),A("widgets/widget",["base","uploader"],function(H,F){function G(N){if(!N){return !1}var L=N.length,M=D.type(N);return 1===N.nodeType&&L?!0:"array"===M||"function"!==M&&"string"!==M&&(0===L||"number"==typeof L&&L>0&&L-1 in N)}function K(L){this.owner=L,this.options=L.options}var D=H.$,I=F.prototype._init,J={},E=[];return D.extend(K.prototype,{init:H.noop,invoke:function(N,L){var M=this.responseMap;return M&&N in M&&M[N] in this&&D.isFunction(this[M[N]])?this[M[N]].apply(this,L):J},request:function(){return this.owner.request.apply(this.owner,arguments)}}),D.extend(F.prototype,{_init:function(){var M=this,L=M._widgets=[];return D.each(E,function(O,N){L.push(new N(M))}),I.apply(M,arguments)},request:function(U,W,L){var V,O,P,M,N=0,S=this._widgets,T=S.length,Q=[],R=[];for(W=G(W)?W:[W];T>N;N++){V=S[N],O=V.invoke(U,W),O!==J&&(H.isPromise(O)?R.push(O):Q.push(O))}return L||R.length?(P=H.when.apply(H,R),M=P.pipe?"pipe":"then",P[M](function(){var X=H.Deferred(),Y=arguments;return setTimeout(function(){X.resolve.apply(X,Y)},1),X.promise()})[M](L||H.noop)):Q[0]}}),F.register=K.register=function(M,N){var O,L={init:"init"};return 1===arguments.length?(N=M,N.responseMap=L):N.responseMap=D.extend(L,M),O=H.inherits(K,N),E.push(O),O},K}),A("widgets/filepicker",["base","uploader","lib/filepicker","widgets/widget"],function(G,E,F){var D=G.$;return D.extend(E.options,{pick:null,accept:null}),E.register({"add-btn":"addButton",refresh:"refresh",disable:"disable",enable:"enable"},{init:function(H){return this.pickers=[],H.pick&&this.addButton(H.pick)},refresh:function(){D.each(this.pickers,function(){this.refresh()})},addButton:function(L){var J,N,H,I=this,K=I.options,M=K.accept;if(L){return H=G.Deferred(),D.isPlainObject(L)||(L={id:L}),J=D.extend({},L,{accept:D.isPlainObject(M)?[M]:M,swf:K.swf,runtimeOrder:K.runtimeOrder}),N=new F(J),N.once("ready",H.resolve),N.on("select",function(O){I.owner.request("add-file",[O])}),N.init(),this.pickers.push(N),H.promise()}},disable:function(){D.each(this.pickers,function(){this.disable()})},enable:function(){D.each(this.pickers,function(){this.enable()})}})}),A("lib/image",["base","runtime/client","lib/blob"],function(H,F,G){function D(I){this.options=E.extend({},D.options,I),F.call(this,"Image"),this.on("load",function(){this._info=this.exec("info"),this._meta=this.exec("meta")})}var E=H.$;return D.options={quality:90,crop:!1,preserveHeaders:!0,allowMagnify:!0},H.inherits(F,{constructor:D,info:function(I){return I?(this._info=I,this):this._info},meta:function(I){return I?(this._meta=I,this):this._meta},loadFromBlob:function(K){var I=this,J=K.getRuid();this.connectRuntime(J,function(){I.exec("init",I.options),I.exec("loadFromBlob",K)})},resize:function(){var I=H.slice(arguments);return this.exec.apply(this,["resize"].concat(I))},getAsDataUrl:function(I){return this.exec("getAsDataUrl",I)},getAsBlob:function(J){var I=this.exec("getAsBlob",J);return new G(this.getRuid(),I)}}),D}),A("widgets/image",["base","uploader","lib/image","widgets/widget"],function(H,F,G){var D,E=H.$;return D=function(L){var J=0,K=[],I=function(){for(var M;K.length&&L>J;){M=K.shift(),J+=M[0],M[1]()}};return function(N,M,O){K.push([M,O]),N.once("destroy",function(){J-=M,setTimeout(I,1)}),setTimeout(I,1)}}(5242880),E.extend(F.options,{thumb:{width:110,height:110,quality:70,allowMagnify:!0,crop:!0,preserveHeaders:!1,type:"image/jpeg"},compress:{width:1600,height:1600,quality:90,allowMagnify:!1,crop:!1,preserveHeaders:!0}}),F.register({"make-thumb":"makeThumb","before-send-file":"compressImage"},{makeThumb:function(M,L,N,I){var J,K;return M=this.request("get-file",M),M.type.match(/^image/)?(J=E.extend({},this.options.thumb),E.isPlainObject(N)&&(J=E.extend(J,N),N=null),N=N||J.width,I=I||J.height,K=new G(J),K.once("load",function(){M._info=M._info||K.info(),M._meta=M._meta||K.meta(),K.resize(N,I)}),K.once("complete",function(){L(!1,K.getAsDataUrl(J.type)),K.destroy()}),K.once("error",function(){L(!0),K.destroy()}),void D(K,M.source.size,function(){M._info&&K.info(M._info),M._meta&&K.meta(M._meta),K.loadFromBlob(M.source)})):void L(!0)},compressImage:function(L){var J,M,I=this.options.compress||this.options.resize,K=I&&I.compressSize||307200;return L=this.request("get-file",L),!I||!~"image/jpeg,image/jpg".indexOf(L.type)||L.size<K||L._compressed?void 0:(I=E.extend({},I),M=H.Deferred(),J=new G(I),M.always(function(){J.destroy(),J=null}),J.once("error",M.reject),J.once("load",function(){L._info=L._info||J.info(),L._meta=L._meta||J.meta(),J.resize(I.width,I.height)}),J.once("complete",function(){var P,O;try{P=J.getAsBlob(I.type),O=L.size,P.size<O&&(L.source=P,L.size=P.size,L.trigger("resize",P.size,O)),L._compressed=!0,M.resolve()}catch(N){M.resolve()}}),L._info&&J.info(L._info),L._meta&&J.meta(L._meta),J.loadFromBlob(L.source),M.promise())}})}),A("file",["base","mediator"],function(I,G){function H(){return J+K++}function L(M){this.name=M.name||"Untitled",this.size=M.size||0,this.type=M.type||"application",this.lastModifiedDate=M.lastModifiedDate||1*new Date,this.id=H(),this.ext=E.exec(this.name)?RegExp.$1:"",this.statusText="",F[this.id]=L.Status.INITED,this.source=M,this.loaded=0,this.on("error",function(N){this.setStatus(L.Status.ERROR,N)})}var D=I.$,J="WU_FILE_",K=0,E=/\.([^.]+)$/,F={};return D.extend(L.prototype,{setStatus:function(O,M){var N=F[this.id];"undefined"!=typeof M&&(this.statusText=M),O!==N&&(F[this.id]=O,this.trigger("statuschange",O,N))},getStatus:function(){return F[this.id]},getSource:function(){return this.source},destory:function(){delete F[this.id]}}),G.installTo(L.prototype),L.Status={INITED:"inited",QUEUED:"queued",PROGRESS:"progress",ERROR:"error",COMPLETE:"complete",CANCELLED:"cancelled",INTERRUPT:"interrupt",INVALID:"invalid"},L}),A("queue",["base","mediator","file"],function(H,F,G){function D(){this.stats={numOfQueue:0,numOfSuccess:0,numOfCancel:0,numOfProgress:0,numOfUploadFailed:0,numOfInvalid:0},this._queue=[],this._map={}}var E=H.$,I=G.Status;return E.extend(D.prototype,{append:function(J){return this._queue.push(J),this._fileAdded(J),this},prepend:function(J){return this._queue.unshift(J),this._fileAdded(J),this},getFile:function(J){return"string"!=typeof J?J:this._map[J]},fetch:function(M){var K,L,J=this._queue.length;for(M=M||I.QUEUED,K=0;J>K;K++){if(L=this._queue[K],M===L.getStatus()){return L}}return null},sort:function(J){"function"==typeof J&&this._queue.sort(J)},getFiles:function(){for(var M,K=[].slice.call(arguments,0),L=[],J=0,N=this._queue.length;N>J;J++){M=this._queue[J],(!K.length||~E.inArray(M.getStatus(),K))&&L.push(M)}return L},_fileAdded:function(L){var J=this,K=this._map[L.id];K||(this._map[L.id]=L,L.on("statuschange",function(N,M){J._onFileStatusChange(N,M)})),L.setStatus(I.QUEUED)},_onFileStatusChange:function(L,J){var K=this.stats;switch(J){case I.PROGRESS:K.numOfProgress--;break;case I.QUEUED:K.numOfQueue--;break;case I.ERROR:K.numOfUploadFailed--;break;case I.INVALID:K.numOfInvalid--}switch(L){case I.QUEUED:K.numOfQueue++;break;case I.PROGRESS:K.numOfProgress++;break;case I.ERROR:K.numOfUploadFailed++;break;case I.COMPLETE:K.numOfSuccess++;break;case I.CANCELLED:K.numOfCancel++;break;case I.INVALID:K.numOfInvalid++}}}),F.installTo(D.prototype),D}),A("widgets/queue",["base","uploader","queue","file","lib/file","runtime/client","widgets/widget"],function(I,G,H,L,D,J){var K=I.$,E=/\.\w+$/,F=L.Status;return G.register({"sort-files":"sortFiles","add-file":"addFiles","get-file":"getFile","fetch-file":"fetchFile","get-stats":"getStats","get-files":"getFiles","remove-file":"removeFile",retry:"retry",reset:"reset","accept-file":"acceptFile"},{init:function(T){var U,M,P,Q,N,O,R,S=this;if(K.isPlainObject(T.accept)&&(T.accept=[T.accept]),T.accept){for(N=[],P=0,M=T.accept.length;M>P;P++){Q=T.accept[P].extensions,Q&&N.push(Q)}N.length&&(O="\\."+N.join(",").replace(/,/g,"$|\\.").replace(/\*/g,".*")+"$"),S.accept=new RegExp(O,"i")}return S.queue=new H,S.stats=S.queue.stats,"html5"===this.request("predict-runtime-type")?(U=I.Deferred(),R=new J("Placeholder"),R.connectRuntime({runtimeOrder:"html5"},function(){S._ruid=R.getRuid(),U.resolve()}),U.promise()):void 0},_wrapFile:function(M){if(!(M instanceof L)){if(!(M instanceof D)){if(!this._ruid){throw new Error("Can't add external files.")}M=new D(this._ruid,M)}M=new L(M)}return M},acceptFile:function(N){var M=!N||N.size<6||this.accept&&E.exec(N.name)&&!this.accept.test(N.name);return !M},_addFile:function(N){var M=this;return N=M._wrapFile(N),M.owner.trigger("beforeFileQueued",N)?M.acceptFile(N)?(M.queue.append(N),M.owner.trigger("fileQueued",N),N):void M.owner.trigger("error","Q_TYPE_DENIED",N):void 0},getFile:function(M){return this.queue.getFile(M)},addFiles:function(N){var M=this;N.length||(N=[N]),N=K.map(N,function(O){return M._addFile(O)}),M.owner.trigger("filesQueued",N),M.options.auto&&M.request("start-upload")},getStats:function(){return this.stats},removeFile:function(N){var M=this;N=N.id?N:M.queue.getFile(N),N.setStatus(F.CANCELLED),M.owner.trigger("fileDequeued",N)},getFiles:function(){return this.queue.getFiles.apply(this.queue,arguments)},fetchFile:function(){return this.queue.fetch.apply(this.queue,arguments)},retry:function(Q,O){var P,M,N,R=this;if(Q){return Q=Q.id?Q:R.queue.getFile(Q),Q.setStatus(F.QUEUED),void (O||R.request("start-upload"))}for(P=R.queue.getFiles(F.ERROR),M=0,N=P.length;N>M;M++){Q=P[M],Q.setStatus(F.QUEUED)}R.request("start-upload")},sortFiles:function(){return this.queue.sort.apply(this.queue,arguments)},reset:function(){this.queue=new H,this.stats=this.queue.stats}})}),A("widgets/runtime",["uploader","runtime/runtime","widgets/widget"],function(E,D){return E.support=function(){return D.hasRuntime.apply(D,arguments)},E.register({"predict-runtime-type":"predictRuntmeType"},{init:function(){if(!this.predictRuntmeType()){throw Error("Runtime Error")}},predictRuntmeType:function(){var I,H,F=this.options.runtimeOrder||D.orders,G=this.type;if(!G){for(F=F.split(/\s*,\s*/g),I=0,H=F.length;H>I;I++){if(D.hasRuntime(F[I])){this.type=G=F[I];break}}}return G}})}),A("lib/transport",["base","runtime/client","mediator"],function(H,F,G){function D(J){var I=this;J=I.options=E.extend(!0,{},D.options,J||{}),F.call(this,"Transport"),this._blob=null,this._formData=J.formData||{},this._headers=J.headers||{},this.on("progress",this._timeout),this.on("load error",function(){I.trigger("progress",1),clearTimeout(I._timer)})}var E=H.$;return D.options={server:"",method:"POST",withCredentials:!1,fileVal:"file",timeout:120000,formData:{},headers:{},sendAsBinary:!1},E.extend(D.prototype,{appendBlob:function(M,K,L){var I=this,J=I.options;I.getRuid()&&I.disconnectRuntime(),I.connectRuntime(K.ruid,function(){I.exec("init")}),I._blob=K,J.fileVal=M||J.fileVal,J.filename=L||J.filename},append:function(J,I){"object"==typeof J?E.extend(this._formData,J):this._formData[J]=I},setRequestHeader:function(J,I){"object"==typeof J?E.extend(this._headers,J):this._headers[J]=I},send:function(I){this.exec("send",I),this._timeout()},abort:function(){return clearTimeout(this._timer),this.exec("abort")},destroy:function(){this.trigger("destroy"),this.off(),this.exec("destroy"),this.disconnectRuntime()},getResponse:function(){return this.exec("getResponse")},getResponseAsJson:function(){return this.exec("getResponseAsJson")},getStatus:function(){return this.exec("getStatus")},_timeout:function(){var J=this,I=J.options.timeout;I&&(clearTimeout(J._timer),J._timer=setTimeout(function(){J.abort(),J.trigger("error","timeout")},I))}}),G.installTo(D.prototype),D}),A("widgets/upload",["base","uploader","file","lib/transport","widgets/widget"],function(H,F,G,K){function D(Q,O){for(var P,T=[],L=Q.source,R=L.size,S=O?Math.ceil(R/O):1,M=0,N=0;S>N;){P=Math.min(O,R-M),T.push({file:Q,start:M,end:O?M+P:R,total:R,chunks:S,chunk:N++}),M+=P}return Q.blocks=T.concat(),Q.remaning=T.length,{file:Q,has:function(){return !!T.length},fetch:function(){return T.shift()}}}var I=H.$,J=H.isPromise,E=G.Status;I.extend(F.options,{prepareNextFile:!1,chunked:!1,chunkSize:5242880,chunkRetry:2,threads:3,formData:null}),F.register({"start-upload":"start","stop-upload":"stop","skip-file":"skipFile","is-in-progress":"isInProgress"},{init:function(){var L=this.owner;this.runing=!1,this.pool=[],this.pending=[],this.remaning=0,this.__tick=H.bindFn(this._tick,this),L.on("uploadComplete",function(M){M.blocks&&I.each(M.blocks,function(O,N){N.transport&&(N.transport.abort(),N.transport.destroy()),delete N.transport}),delete M.blocks,delete M.remaning})},start:function(){var L=this;I.each(L.request("get-files",E.INVALID),function(){L.request("remove-file",this)}),L.runing||(L.runing=!0,I.each(L.pool,function(O,N){var M=N.file;M.getStatus()===E.INTERRUPT&&(M.setStatus(E.PROGRESS),L._trigged=!1,N.transport&&N.transport.send())}),L._trigged=!1,L.owner.trigger("startUpload"),H.nextTick(L.__tick))},stop:function(M){var L=this;L.runing!==!1&&(L.runing=!1,M&&I.each(L.pool,function(O,N){N.transport&&N.transport.abort(),N.file.setStatus(E.INTERRUPT)}),L.owner.trigger("stopUpload"))},isInProgress:function(){return !!this.runing},getStats:function(){return this.request("get-stats")},skipFile:function(M,L){M=this.request("get-file",M),M.setStatus(L||E.COMPLETE),M.skipped=!0,M.blocks&&I.each(M.blocks,function(P,N){var O=N.transport;O&&(O.abort(),O.destroy(),delete N.transport)}),this.owner.trigger("uploadSkip",M)},_tick:function(){var N,O,L=this,M=L.options;return L._promise?L._promise.always(L.__tick):void (L.pool.length<M.threads&&(O=L._nextBlock())?(L._trigged=!1,N=function(P){L._promise=null,P&&P.file&&L._startSend(P),H.nextTick(L.__tick)},L._promise=J(O)?O.always(N):N(O)):L.remaning||L.getStats().numOfQueue||(L.runing=!1,L._trigged||H.nextTick(function(){L.owner.trigger("uploadFinished")}),L._trigged=!0))},_nextBlock:function(){var O,M,N=this,L=N._act,P=N.options;return L&&L.has()&&L.file.getStatus()===E.PROGRESS?(P.prepareNextFile&&!N.pending.length&&N._prepareNextFile(),L.fetch()):N.runing?(!N.pending.length&&N.getStats().numOfQueue&&N._prepareNextFile(),O=N.pending.shift(),M=function(Q){return Q?(L=D(Q,P.chunked?P.chunkSize:0),N._act=L,L.fetch()):null},J(O)?O[O.pipe?"pipe":"then"](M):M(O)):void 0},_prepareNextFile:function(){var O,M=this,N=M.request("fetch-file"),L=M.pending;N&&(O=M.request("before-send-file",N,function(){return N.getStatus()===E.QUEUED?(M.owner.trigger("uploadStart",N),N.setStatus(E.PROGRESS),N):M._finishFile(N)}),O.done(function(){var P=I.inArray(O,L);~P&&L.splice(P,1,N)}),O.fail(function(P){N.setStatus(E.ERROR,P),M.owner.trigger("uploadError",N,P),M.owner.trigger("uploadComplete",N)}),L.push(O))},_popBlock:function(M){var L=I.inArray(M,this.pool);this.pool.splice(L,1),M.file.remaning--,this.remaning--},_startSend:function(N){var O,L=this,M=N.file;L.pool.push(N),L.remaning++,N.blob=1===N.chunks?M.source:M.source.slice(N.start,N.end),O=L.request("before-send",N,function(){M.getStatus()===E.PROGRESS?L._doSend(N):(L._popBlock(N),H.nextTick(L.__tick))}),O.fail(function(){1===M.remaning?L._finishFile(M).always(function(){N.percentage=1,L._popBlock(N),L.owner.trigger("uploadComplete",M),H.nextTick(L.__tick)}):(N.percentage=1,L._popBlock(N),H.nextTick(L.__tick))})},_doSend:function(S){var T,L,U=this,O=U.owner,M=U.options,N=S.file,Q=new K(M),R=I.extend({},M.formData),P=I.extend({},M.headers);S.transport=Q,Q.on("destroy",function(){delete S.transport,U._popBlock(S),H.nextTick(U.__tick)}),Q.on("progress",function(X){var W=0,V=0;W=S.percentage=X,S.chunks>1&&(I.each(N.blocks,function(Z,Y){V+=(Y.percentage||0)*(Y.end-Y.start)}),W=V/N.size),O.trigger("uploadProgress",N,W||0)}),T=function(W){var V;return L=Q.getResponseAsJson()||{},L._raw=Q.getResponse(),V=function(X){W=X},O.trigger("uploadAccept",S,L,V)||(W=W||"server"),W},Q.on("error",function(W,V){S.retried=S.retried||0,S.chunks>1&&~"http,abort".indexOf(W)&&S.retried<M.chunkRetry?(S.retried++,Q.send()):(V||"server"!==W||(W=T(W)),N.setStatus(E.ERROR,W),O.trigger("uploadError",N,W),O.trigger("uploadComplete",N))}),Q.on("load",function(){var V;return(V=T())?void Q.trigger("error",V,!0):void (1===N.remaning?U._finishFile(N,L):Q.destroy())}),R=I.extend(R,{id:N.id,name:N.name,type:N.type,lastModifiedDate:N.lastModifiedDate,size:N.size}),S.chunks>1&&I.extend(R,{chunks:S.chunks,chunk:S.chunk}),O.trigger("uploadBeforeSend",S,R,P),Q.appendBlob(M.fileVal,S.blob,N.name),Q.append(R),Q.setRequestHeader(P),Q.send()},_finishFile:function(O,M,N){var L=this.owner;return L.request("after-send-file",arguments,function(){O.setStatus(E.COMPLETE),L.trigger("uploadSuccess",O,M,N)}).fail(function(P){O.getStatus()===E.PROGRESS&&O.setStatus(E.ERROR,P),L.trigger("uploadError",O,P)}).always(function(){L.trigger("uploadComplete",O)})}})}),A("widgets/validator",["base","uploader","file","widgets/widget"],function(H,F,G){var D,E=H.$,I={};return D={addValidator:function(K,J){I[K]=J},removeValidator:function(J){delete I[J]}},F.register({init:function(){var J=this;E.each(I,function(){this.call(J.owner)})}}),D.addValidator("fileNumLimit",function(){var N=this,L=N.options,M=0,J=L.fileNumLimit>>0,K=!0;J&&(N.on("beforeFileQueued",function(O){return M>=J&&K&&(K=!1,this.trigger("error","Q_EXCEED_NUM_LIMIT",J,O),setTimeout(function(){K=!0},1)),M>=J?!1:!0}),N.on("fileQueued",function(){M++}),N.on("fileDequeued",function(){M--}),N.on("uploadFinished",function(){M=0}))}),D.addValidator("fileSizeLimit",function(){var N=this,L=N.options,M=0,J=L.fileSizeLimit>>0,K=!0;J&&(N.on("beforeFileQueued",function(P){var O=M+P.size>J;return O&&K&&(K=!1,this.trigger("error","Q_EXCEED_SIZE_LIMIT",J,P),setTimeout(function(){K=!0},1)),O?!1:!0}),N.on("fileQueued",function(O){M+=O.size}),N.on("fileDequeued",function(O){M-=O.size}),N.on("uploadFinished",function(){M=0}))}),D.addValidator("fileSingleSizeLimit",function(){var L=this,K=L.options,J=K.fileSingleSizeLimit;J&&L.on("beforeFileQueued",function(M){return M.size>J?(M.setStatus(G.Status.INVALID,"exceed_size"),this.trigger("error","F_EXCEED_SIZE",M),!1):void 0})}),D.addValidator("duplicate",function(){function M(R){for(var P,Q=0,N=0,O=R.length;O>N;N++){P=R.charCodeAt(N),Q=P+(Q<<6)+(Q<<16)-Q}return Q}var K=this,L=K.options,J={};L.duplicate||(K.on("beforeFileQueued",function(N){var O=N.__hash||(N.__hash=M(N.name+N.size+N.lastModifiedDate));return J[O]?(this.trigger("error","F_DUPLICATE",N),!1):void 0}),K.on("fileQueued",function(O){var N=O.__hash;N&&(J[N]=!0)}),K.on("fileDequeued",function(O){var N=O.__hash;N&&delete J[N]}))}),D}),A("runtime/compbase",[],function(){function D(F,E){this.owner=F,this.options=F.options,this.getRuntime=function(){return E},this.getRuid=function(){return E.uid},this.trigger=function(){return F.trigger.apply(F,arguments)}}return D}),A("runtime/flash/runtime",["base","runtime/runtime","runtime/compbase"],function(G,H,K){function D(){var N;try{N=navigator.plugins["Shockwave Flash"],N=N.description}catch(L){try{N=new ActiveXObject("ShockwaveFlash.ShockwaveFlash").GetVariable("$version")}catch(M){N="0.0"}}return N=N.match(/\d+/g),parseFloat(N[0]+"."+N[1],10)}function I(){function M(V,T){var U,R,S=V.type||V;U=S.split("::"),R=U[0],S=U[1],"Ready"===S&&R===P.uid?P.trigger("ready"):Q[R]&&Q[R].trigger(S.toLowerCase(),V,T)}var N={},Q={},L=this.destory,P=this,O=G.guid("webuploader_");H.apply(P,arguments),P.type=E,P.exec=function(V,U){var S,R=this,T=R.uid,W=G.slice(arguments,2);return Q[T]=R,F[V]&&(N[T]||(N[T]=new F[V](R,P)),S=N[T],S[U])?S[U].apply(S,W):P.flashExec.apply(R,arguments)},C[O]=function(){var R=arguments;setTimeout(function(){M.apply(null,R)},1)},this.jsreciver=O,this.destory=function(){return L&&L.apply(this,arguments)},this.flashExec=function(U,T){var R=P.getFlash(),S=G.slice(arguments,2);return R.exec(this.uid,U,T,S)}}var J=G.$,E="flash",F={};return G.inherits(H,{constructor:I,init:function(){var N,M=this.getContainer(),L=this.options;M.css({position:"absolute",top:"-8px",left:"-8px",width:"9px",height:"9px",overflow:"hidden"}),N='<object id="'+this.uid+'" type="application/x-shockwave-flash" data="'+L.swf+'" ',G.browser.ie&&(N+='classid="clsid:d27cdb6e-ae6d-11cf-96b8-************" '),N+='width="100%" height="100%" style="outline:0"><param name="movie" value="'+L.swf+'" /><param name="flashvars" value="uid='+this.uid+"&jsreciver="+this.jsreciver+'" /><param name="wmode" value="transparent" /><param name="allowscriptaccess" value="always" /></object>',M.html(N)},getFlash:function(){return this._flash?this._flash:(this._flash=J("#"+this.uid).get(0),this._flash)}}),I.register=function(M,L){return L=F[M]=G.inherits(K,J.extend({flashExec:function(){var O=this.owner,N=this.getRuntime();return N.flashExec.apply(O,arguments)}},L))},D()>=11.4&&H.addRuntime(E,I),I}),A("runtime/flash/filepicker",["base","runtime/flash/runtime"],function(F,D){var E=F.$;return D.register("FilePicker",{init:function(J){var I,G,H=E.extend({},J);for(I=H.accept&&H.accept.length,G=0;I>G;G++){H.accept[G].title||(H.accept[G].title="Files")}delete H.button,delete H.container,this.flashExec("FilePicker","init",H)},destroy:function(){}})}),A("runtime/flash/image",["runtime/flash/runtime"],function(D){return D.register("Image",{loadFromBlob:function(F){var E=this.owner;E.info()&&this.flashExec("Image","info",E.info()),E.meta()&&this.flashExec("Image","meta",E.meta()),this.flashExec("Image","loadFromBlob",F.uid)}})}),A("runtime/flash/transport",["base","runtime/flash/runtime","runtime/client"],function(G,E,F){var D=G.$;return E.register("Transport",{init:function(){this._status=0,this._response=null,this._responseJson=null},send:function(){var L,J=this.owner,K=this.options,I=this._initAjax(),M=J._blob,H=K.server;I.connectRuntime(M.ruid),K.sendAsBinary?(H+=(/\?/.test(H)?"&":"?")+D.param(J._formData),L=M.uid):(D.each(J._formData,function(O,N){I.exec("append",O,N)}),I.exec("appendBlob",K.fileVal,M.uid,K.filename||J._formData.name||"")),this._setRequestHeader(I,K.headers),I.exec("send",{method:K.method,url:H},L)},getStatus:function(){return this._status},getResponse:function(){return this._response},getResponseAsJson:function(){return this._responseJson},abort:function(){var H=this._xhr;H&&(H.exec("abort"),H.destroy(),this._xhr=H=null)},destroy:function(){this.abort()},_initAjax:function(){var I=this,H=new F("XMLHttpRequest");return H.on("uploadprogress progress",function(J){return I.trigger("progress",J.loaded/J.total)}),H.on("load",function(){var K=H.exec("getStatus"),J="";return H.off(),I._xhr=null,K>=200&&300>K?(I._response=H.exec("getResponse"),I._responseJson=H.exec("getResponseAsJson")):K>=500&&600>K?(I._response=H.exec("getResponse"),I._responseJson=H.exec("getResponseAsJson"),J="server"):J="http",H.destroy(),H=null,J?I.trigger("error",J):I.trigger("load")}),H.on("error",function(){H.off(),I._xhr=null,I.trigger("error","http")}),I._xhr=H,H},_setRequestHeader:function(I,H){D.each(H,function(J,K){I.exec("setRequestHeader",J,K)})}})}),A("preset/flashonly",["base","widgets/filepicker","widgets/image","widgets/queue","widgets/runtime","widgets/upload","widgets/validator","runtime/flash/filepicker","runtime/flash/image","runtime/flash/transport"],function(D){return D}),A("webuploader",["preset/flashonly"],function(D){return D}),B("webuploader")});