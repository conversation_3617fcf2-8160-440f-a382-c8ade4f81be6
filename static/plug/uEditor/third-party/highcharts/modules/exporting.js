(function(O){var R=O.Chart,I=O.addEvent,Q=O.removeEvent,L=O.createElement,M=O.discardElement,J=O.css,N=O.merge,G=O.each,H=O.extend,T=Math.max,K=document,P=window,U=O.isTouchDevice,S=O.Renderer.prototype.symbols,V=O.getOptions(),W;H(<PERSON><PERSON>lang,{printChart:"Print chart",downloadPNG:"Download PNG image",downloadJPEG:"Download JPEG image",downloadPDF:"Download PDF document",downloadSVG:"Download SVG vector image",contextButtonTitle:"Chart context menu"});V.navigation={menuStyle:{border:"1px solid #A0A0A0",background:"#FFFFFF",padding:"5px 0"},menuItemStyle:{padding:"0 10px",background:"none",color:"#303030",fontSize:U?"14px":"11px"},menuItemHoverStyle:{background:"#4572A5",color:"#FFFFFF"},buttonOptions:{symbolFill:"#E0E0E0",symbolSize:14,symbolStroke:"#666",symbolStrokeWidth:3,symbolX:12.5,symbolY:10.5,align:"right",buttonSpacing:3,height:22,theme:{fill:"white",stroke:"none"},verticalAlign:"top",width:24}};V.exporting={type:"image/png",url:"http://export.highcharts.com/",buttons:{contextButton:{menuClassName:"highcharts-contextmenu",symbol:"menu",_titleKey:"contextButtonTitle",menuItems:[{textKey:"printChart",onclick:function(){this.print()}},{separator:!0},{textKey:"downloadPNG",onclick:function(){this.exportChart()}},{textKey:"downloadJPEG",onclick:function(){this.exportChart({type:"image/jpeg"})}},{textKey:"downloadPDF",onclick:function(){this.exportChart({type:"application/pdf"})}},{textKey:"downloadSVG",onclick:function(){this.exportChart({type:"image/svg+xml"})}}]}}};O.post=function(C,D){var A,B;B=L("form",{method:"post",action:C,enctype:"multipart/form-data"},{display:"none"},K.body);for(A in D){L("input",{type:"hidden",name:A,value:D[A]},null,B)}B.submit();M(B)};H(R.prototype,{getSVG:function(F){var X=this,B,E,D,A,C=N(X.options,F);if(!K.createElementNS){K.createElementNS=function(Z,Y){return K.createElement(Y)}}F=L("div",null,{position:"absolute",top:"-9999em",width:X.chartWidth+"px",height:X.chartHeight+"px"},K.body);E=X.renderTo.style.width;A=X.renderTo.style.height;E=C.exporting.sourceWidth||C.chart.width||/px$/.test(E)&&parseInt(E,10)||600;A=C.exporting.sourceHeight||C.chart.height||/px$/.test(A)&&parseInt(A,10)||400;H(C.chart,{animation:!1,renderTo:F,forExport:!0,width:E,height:A});C.exporting.enabled=!1;C.series=[];G(X.series,function(Y){D=N(Y.options,{animation:!1,showCheckbox:!1,visible:Y.visible});D.isInternal||C.series.push(D)});B=new O.Chart(C,X.callback);G(["xAxis","yAxis"],function(Y){G(X[Y],function(e,d){var Z=B[Y][d],i=e.getExtremes(),b=i.userMin,i=i.userMax;Z&&(b!==void 0||i!==void 0)&&Z.setExtremes(b,i,!0,!1)})});E=B.container.innerHTML;C=null;B.destroy();M(F);E=E.replace(/zIndex="[^"]+"/g,"").replace(/isShadow="[^"]+"/g,"").replace(/symbolName="[^"]+"/g,"").replace(/jQuery[0-9]+="[^"]+"/g,"").replace(/url\([^#]+#/g,"url(#").replace(/<svg /,'<svg xmlns:xlink="http://www.w3.org/1999/xlink" ').replace(/ href=/g," xlink:href=").replace(/\n/," ").replace(/<\/svg>.*?$/,"</svg>").replace(/&nbsp;/g," ").replace(/&shy;/g,"­").replace(/<IMG /g,"<image ").replace(/height=([^" ]+)/g,'height="$1"').replace(/width=([^" ]+)/g,'width="$1"').replace(/hc-svg-href="([^"]+)">/g,'xlink:href="$1"/>').replace(/id=([^" >]+)/g,'id="$1"').replace(/class=([^" >]+)/g,'class="$1"').replace(/ transform /g," ").replace(/:(path|rect)/g,"$1").replace(/style="([^"]+)"/g,function(Y){return Y.toLowerCase()});return E=E.replace(/(url\(#highcharts-[0-9]+)&quot;/g,"$1").replace(/&quot;/g,"'")},exportChart:function(B,C){var B=B||{},A=this.options.exporting,A=this.getSVG(N({chart:{borderRadius:0}},A.chartOptions,C,{exporting:{sourceWidth:B.sourceWidth||A.sourceWidth,sourceHeight:B.sourceHeight||A.sourceHeight}})),B=N(this.options.exporting,B);O.post(B.url,{filename:B.filename||"chart",type:B.type,width:B.width||0,scale:B.scale||2,svg:A})},print:function(){var D=this,E=D.container,B=[],C=E.parentNode,F=K.body,A=F.childNodes;if(!D.isPrinting){D.isPrinting=!0,G(A,function(Y,X){if(Y.nodeType===1){B[X]=Y.style.display,Y.style.display="none"}}),F.appendChild(E),P.focus(),P.print(),setTimeout(function(){C.appendChild(E);G(A,function(Y,X){if(Y.nodeType===1){Y.style.display=B[X]}});D.isPrinting=!1},1000)}},contextMenu:function(t,u,z,r,x,F,y){var Aa=this,E=Aa.options.navigation,B=E.menuItemStyle,k=Aa.chartWidth,p=Aa.chartHeight,Z="cache-"+t,X=Aa[Z],A=T(x,F),C,D,Y;if(!X){Aa[Z]=X=L("div",{className:t},{position:"absolute",zIndex:1000,padding:A+"px"},Aa.container),C=L("div",null,H({MozBoxShadow:"3px 3px 10px #888",WebkitBoxShadow:"3px 3px 10px #888",boxShadow:"3px 3px 10px #888"},E.menuStyle),X),D=function(){J(X,{display:"none"});y&&y.setState(0);Aa.openMenu=!1},I(X,"mouseleave",function(){Y=setTimeout(D,500)}),I(X,"mouseenter",function(){clearTimeout(Y)}),I(document,"mousedown",function(b){Aa.pointer.inClass(b.target,t)||D()}),G(u,function(d){if(d){var c=d.separator?L("hr",null,null,C):L("div",{onmouseover:function(){J(this,E.menuItemHoverStyle)},onmouseout:function(){J(this,B)},onclick:function(){D();d.onclick.apply(Aa,arguments)},innerHTML:d.text||Aa.options.lang[d.textKey]},H({cursor:"pointer"},B),C);Aa.exportDivElements.push(c)}}),Aa.exportDivElements.push(C,X),Aa.exportMenuWidth=X.offsetWidth,Aa.exportMenuHeight=X.offsetHeight}u={display:"block"};z+Aa.exportMenuWidth>k?u.right=k-z-x-A+"px":u.left=z-A+"px";r+F+Aa.exportMenuHeight>p&&y.alignOptions.verticalAlign!=="top"?u.bottom=p-r-A+"px":u.top=r+F-A+"px";J(X,u);Aa.openMenu=!0},addButton:function(o){var p=this,s=p.renderer,f=N(p.options.navigation.buttonOptions,o),C=f.onclick,E=f.menuItems,r,A,D={stroke:f.symbolStroke,fill:f.symbolFill},B=f.symbolSize||12;if(!p.btnCount){p.btnCount=0}if(!p.exportDivElements){p.exportDivElements=[],p.exportSVGElements=[]}if(f.enabled!==!1){var Y=f.theme,Z=Y.states,X=Z&&Z.hover,Z=Z&&Z.select,F;delete Y.states;C?F=function(){C.apply(p,arguments)}:E&&(F=function(){p.contextMenu(A.menuClassName,E,A.translateX,A.translateY,A.width,A.height,A);A.setState(2)});f.text&&f.symbol?Y.paddingLeft=O.pick(Y.paddingLeft,25):f.text||H(Y,{width:f.width,height:f.height,padding:0});A=s.button(f.text,0,0,F,Y,X,Z).attr({title:p.options.lang[f._titleKey],"stroke-linecap":"round"});A.menuClassName=o.menuClassName||"highcharts-menu-"+p.btnCount++;f.symbol&&(r=s.symbol(f.symbol,f.symbolX-B/2,f.symbolY-B/2,B,B).attr(H(D,{"stroke-width":f.symbolStrokeWidth||1,zIndex:1})).add(A));A.add().align(H(f,{width:A.width,x:O.pick(f.x,W)}),!0,"spacingBox");W+=(A.width+f.buttonSpacing)*(f.align==="right"?-1:1);p.exportSVGElements.push(A,r)}},destroyExport:function(B){var B=B.target,C,A;for(C=0;C<B.exportSVGElements.length;C++){if(A=B.exportSVGElements[C]){A.onclick=A.ontouchstart=null,B.exportSVGElements[C]=A.destroy()}}for(C=0;C<B.exportDivElements.length;C++){A=B.exportDivElements[C],Q(A,"mouseleave"),B.exportDivElements[C]=A.onmouseout=A.onmouseover=A.ontouchstart=A.onclick=null,M(A)}}});S.menu=function(C,D,A,B){return["M",C,D+2.5,"L",C+A,D+2.5,"M",C,D+B/2+0.5,"L",C+A,D+B/2+0.5,"M",C,D+B-1.5,"L",C+A,D+B-1.5]};R.prototype.callbacks.push(function(C){var D,A=C.options.exporting,B=A.buttons;W=0;if(A.enabled!==!1){for(D in B){C.addButton(B[D])}I(C,"destroy",C.destroyExport)}})})(Highcharts);