(function(G,P){var N,E,O,J=G.Chart,H=G.extend,A=G.each;O=["path","rect","circle"];E={top:0,left:0,center:0.5,middle:0.5,bottom:1,right:1};var C=P.inArray,L=G.merge;function F(Q){var S,R;R={xAxis:0,yAxis:0,title:{style:{},text:"",x:0,y:0},shape:{params:{stroke:"#000000",fill:"transparent",strokeWidth:2}}};S={circle:{params:{x:0,y:0}}};if(S[Q]){R.shape=L(R.shape,S[Q])}return R}function I(Q){return Object.prototype.toString.call(Q)==="[object Array]"}function K(Q){return typeof Q==="number"}function M(Q){return Q!==N&&Q!==null}function B(Q,U,S,T,R){var V=Q.length,W=0;while(W<V){if(typeof Q[W]==="number"&&typeof Q[W+1]==="number"){Q[W]=U.toPixels(Q[W])-T;Q[W+1]=S.toPixels(Q[W+1])-R;W+=2}else{W+=1}}return Q}var D=function(){this.init.apply(this,arguments)};D.prototype={init:function(Q,S){var R=S.shape&&S.shape.type;this.chart=Q;this.options=L({},F(R),S)},render:function(W){var Z=this,T=this.chart,S=Z.chart.renderer,U=Z.group,Y=Z.title,R=Z.shape,V=Z.options,Q=V.title,X=V.shape;if(!U){U=Z.group=S.g()}if(!R&&X&&C(X.type,O)!==-1){R=Z.shape=S[V.shape.type](X.params);R.add(U)}if(!Y&&Q){Y=Z.title=S.label(Q);Y.add(U)}U.add(T.annotations.group);Z.linkObjects();if(W!==false){Z.redraw()}},redraw:function(){var g=this.options,h=this.chart,e=this.group,Z=this.title,Y=this.shape,f=this.linkedObject,Q=h.xAxis[g.xAxis],U=h.yAxis[g.yAxis],d=g.width,j=g.height,R=E[g.anchorY],S=E[g.anchorX],W=false,V,i,c,T,X,a,b;if(f){i=(f instanceof G.Point)?"point":(f instanceof G.Series)?"series":null;if(i==="point"){g.xValue=f.x;g.yValue=f.y;c=f.series}else{if(i==="series"){c=f}}if(e.visibility!==c.group.visibility){e.attr({visibility:c.group.visibility})}}a=(M(g.xValue)?Q.toPixels(g.xValue+Q.minPointOffset)-Q.minPixelPadding:g.x);b=M(g.yValue)?U.toPixels(g.yValue):g.y;if(isNaN(a)||isNaN(b)||!K(a)||!K(b)){return}if(Z){Z.attr(g.title);Z.css(g.title.style);W=true}if(Y){V=H({},g.shape.params);if(g.units==="values"){for(T in V){if(C(T,["width","x"])>-1){V[T]=Q.translate(V[T])}else{if(C(T,["height","y"])>-1){V[T]=U.translate(V[T])}}}if(V.width){V.width-=Q.toPixels(0)-Q.left}if(V.x){V.x+=Q.minPixelPadding}if(g.shape.type==="path"){B(V.d,Q,U,a,b)}}if(g.shape.type==="circle"){V.x+=V.r;V.y+=V.r}W=true;Y.attr(V)}e.bBox=null;if(!K(d)){X=e.getBBox();d=X.width}if(!K(j)){if(!X){X=e.getBBox()}j=X.height}if(!K(S)){S=E.center}if(!K(R)){R=E.center}a=a-d*S;b=b-j*R;if(h.animation&&M(e.translateX)&&M(e.translateY)){e.animate({translateX:a,translateY:b})}else{e.translate(a,b)}},destroy:function(){var T=this,Q=this.chart,S=Q.annotations.allItems,R=S.indexOf(T);if(R>-1){S.splice(R,1)}A(["title","shape","group"],function(U){if(T[U]){T[U].destroy();T[U]=null}});T.group=T.title=T.shape=T.chart=T.options=null},update:function(Q,R){H(this.options,Q);this.linkObjects();this.render(R)},linkObjects:function(){var S=this,Q=S.chart,T=S.linkedObject,U=T&&(T.id||T.options.id),R=S.options,V=R.linkedTo;if(!M(V)){S.linkedObject=null}else{if(!M(T)||V!==U){S.linkedObject=Q.get(V)}}}};H(J.prototype,{annotations:{add:function(S,V){var U=this.allItems,Q=this.chart,R,T;if(!I(S)){S=[S]}T=S.length;while(T--){R=new D(Q,S[T]);U.push(R);R.render(V)}},redraw:function(){A(this.allItems,function(Q){Q.redraw()})}}});J.prototype.callbacks.push(function(Q){var S=Q.options.annotations,R;R=Q.renderer.g("annotations");R.attr({zIndex:7});R.add();Q.annotations.allItems=[];Q.annotations.chart=Q;Q.annotations.group=R;if(I(S)&&S.length>0){Q.annotations.add(Q.options.annotations)}G.addEvent(Q,"redraw",function(){Q.annotations.redraw()})})}(Highcharts,HighchartsAdapter));