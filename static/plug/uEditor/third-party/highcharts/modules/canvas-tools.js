function RGBColor(B){this.ok=!1;<PERSON>.charAt(0)=="#"&&(B=B.substr(1,6));var B=B.replace(/ /g,""),B=B.toLowerCase(),E={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"00ffff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000000",blanchedalmond:"ffebcd",blue:"0000ff",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"00ffff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dodgerblue:"1e90ff",feldspar:"d19275",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"ff00ff",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgrey:"d3d3d3",lightgreen:"90ee90",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslateblue:"8470ff",lightslategray:"778899",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"00ff00",limegreen:"32cd32",linen:"faf0e6",magenta:"ff00ff",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370d8",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"d87093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",red:"ff0000",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",violetred:"d02090",wheat:"f5deb3",white:"ffffff",whitesmoke:"f5f5f5",yellow:"ffff00",yellowgreen:"9acd32"},D;for(D in E){B==D&&(B=E[D])}var A=[{re:/^rgb\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3})\)$/,example:["rgb(123, 234, 45)","rgb(255,234,245)"],process:function(G){return[parseInt(G[1]),parseInt(G[2]),parseInt(G[3])]}},{re:/^(\w{2})(\w{2})(\w{2})$/,example:["#00ff00","336699"],process:function(G){return[parseInt(G[1],16),parseInt(G[2],16),parseInt(G[3],16)]}},{re:/^(\w{1})(\w{1})(\w{1})$/,example:["#fb0","f0f"],process:function(G){return[parseInt(G[1]+G[1],16),parseInt(G[2]+G[2],16),parseInt(G[3]+G[3],16)]}}];for(D=0;D<A.length;D++){var C=A[D].process,F=A[D].re.exec(B);if(F){channels=C(F),this.r=channels[0],this.g=channels[1],this.b=channels[2],this.ok=!0}}this.r=this.r<0||isNaN(this.r)?0:this.r>255?255:this.r;this.g=this.g<0||isNaN(this.g)?0:this.g>255?255:this.g;this.b=this.b<0||isNaN(this.b)?0:this.b>255?255:this.b;this.toRGB=function(){return"rgb("+this.r+", "+this.g+", "+this.b+")"};this.toHex=function(){var H=this.r.toString(16),I=this.g.toString(16),G=this.b.toString(16);H.length==1&&(H="0"+H);I.length==1&&(I="0"+I);G.length==1&&(G="0"+G);return"#"+H+I+G};this.getHelpXML=function(){for(var O=[],J=0;J<A.length;J++){for(var P=A[J].example,I=0;I<P.length;I++){O[O.length]=P[I]}}for(var K in E){O[O.length]=K}P=document.createElement("ul");P.setAttribute("id","rgbcolor-examples");for(J=0;J<O.length;J++){try{var N=document.createElement("li"),M=new RGBColor(O[J]),L=document.createElement("div");L.style.cssText="margin: 3px; border: 1px solid black; background:"+M.toHex()+"; color:"+M.toHex();L.appendChild(document.createTextNode("test"));var H=document.createTextNode(" "+O[J]+" -> "+M.toRGB()+" -> "+M.toHex());N.appendChild(L);N.appendChild(H);P.appendChild(N)}catch(G){}}return P}}if(!window.console){window.console={},window.console.log=function(){},window.console.dir=function(){}}if(!Array.prototype.indexOf){Array.prototype.indexOf=function(A){for(var B=0;B<this.length;B++){if(this[B]==A){return B}}return -1}}(function(){function A(){var B={FRAMERATE:30,MAX_VIRTUAL_PIXELS:30000};B.init=function(C){B.Definitions={};B.Styles={};B.Animations=[];B.Images=[];B.ctx=C;B.ViewPort=new function(){this.viewPorts=[];this.Clear=function(){this.viewPorts=[]};this.SetCurrent=function(E,D){this.viewPorts.push({width:E,height:D})};this.RemoveCurrent=function(){this.viewPorts.pop()};this.Current=function(){return this.viewPorts[this.viewPorts.length-1]};this.width=function(){return this.Current().width};this.height=function(){return this.Current().height};this.ComputeSize=function(D){return D!=null&&typeof D=="number"?D:D=="x"?this.width():D=="y"?this.height():Math.sqrt(Math.pow(this.width(),2)+Math.pow(this.height(),2))/Math.sqrt(2)}}};B.init();B.ImagesLoaded=function(){for(var C=0;C<B.Images.length;C++){if(!B.Images[C].loaded){return !1}}return !0};B.trim=function(C){return C.replace(/^\s+|\s+$/g,"")};B.compressSpaces=function(C){return C.replace(/[\s\r\t\n]+/gm," ")};B.ajax=function(D){var C;return(C=window.XMLHttpRequest?new XMLHttpRequest:new ActiveXObject("Microsoft.XMLHTTP"))?(C.open("GET",D,!1),C.send(null),C.responseText):null};B.parseXml=function(D){if(window.DOMParser){return(new DOMParser).parseFromString(D,"text/xml")}else{var D=D.replace(/<!DOCTYPE svg[^>]*>/,""),C=new ActiveXObject("Microsoft.XMLDOM");C.async="false";C.loadXML(D);return C}};B.Property=function(E,C){this.name=E;this.value=C;this.hasValue=function(){return this.value!=null&&this.value!==""};this.numValue=function(){if(!this.hasValue()){return 0}var F=parseFloat(this.value);(this.value+"").match(/%$/)&&(F/=100);return F};this.valueOrDefault=function(F){return this.hasValue()?this.value:F};this.numValueOrDefault=function(F){return this.hasValue()?this.numValue():F};var D=this;this.Color={addOpacity:function(F){var G=D.value;if(F!=null&&F!=""){var H=new RGBColor(D.value);H.ok&&(G="rgba("+H.r+", "+H.g+", "+H.b+", "+F+")")}return new B.Property(D.name,G)}};this.Definition={getDefinition:function(){var F=D.value.replace(/^(url\()?#([^\)]+)\)?$/,"$2");return B.Definitions[F]},isUrl:function(){return D.value.indexOf("url(")==0},getFillStyle:function(G){var F=this.getDefinition();return F!=null&&F.createGradient?F.createGradient(B.ctx,G):F!=null&&F.createPattern?F.createPattern(B.ctx,G):null}};this.Length={DPI:function(){return 96},EM:function(G){var F=12,H=new B.Property("fontSize",B.Font.Parse(B.ctx.font).fontSize);H.hasValue()&&(F=H.Length.toPixels(G));return F},toPixels:function(F){if(!D.hasValue()){return 0}var G=D.value+"";return G.match(/em$/)?D.numValue()*this.EM(F):G.match(/ex$/)?D.numValue()*this.EM(F)/2:G.match(/px$/)?D.numValue():G.match(/pt$/)?D.numValue()*1.25:G.match(/pc$/)?D.numValue()*15:G.match(/cm$/)?D.numValue()*this.DPI(F)/2.54:G.match(/mm$/)?D.numValue()*this.DPI(F)/25.4:G.match(/in$/)?D.numValue()*this.DPI(F):G.match(/%$/)?D.numValue()*B.ViewPort.ComputeSize(F):D.numValue()}};this.Time={toMilliseconds:function(){if(!D.hasValue()){return 0}var F=D.value+"";if(F.match(/s$/)){return D.numValue()*1000}F.match(/ms$/);return D.numValue()}};this.Angle={toRadians:function(){if(!D.hasValue()){return 0}var F=D.value+"";return F.match(/deg$/)?D.numValue()*(Math.PI/180):F.match(/grad$/)?D.numValue()*(Math.PI/200):F.match(/rad$/)?D.numValue():D.numValue()*(Math.PI/180)}}};B.Font=new function(){this.Styles=["normal","italic","oblique","inherit"];this.Variants=["normal","small-caps","inherit"];this.Weights="normal,bold,bolder,lighter,100,200,300,400,500,600,700,800,900,inherit".split(",");this.CreateFont=function(E,G,H,F,I,D){D=D!=null?this.Parse(D):this.CreateFont("","","","","",B.ctx.font);return{fontFamily:I||D.fontFamily,fontSize:F||D.fontSize,fontStyle:E||D.fontStyle,fontWeight:H||D.fontWeight,fontVariant:G||D.fontVariant,toString:function(){return[this.fontStyle,this.fontVariant,this.fontWeight,this.fontSize,this.fontFamily].join(" ")}}};var C=this;this.Parse=function(K){for(var H={},K=B.trim(B.compressSpaces(K||"")).split(" "),F=!1,D=!1,I=!1,J=!1,E="",G=0;G<K.length;G++){if(!D&&C.Styles.indexOf(K[G])!=-1){if(K[G]!="inherit"){H.fontStyle=K[G]}D=!0}else{if(!J&&C.Variants.indexOf(K[G])!=-1){if(K[G]!="inherit"){H.fontVariant=K[G]}D=J=!0}else{if(!I&&C.Weights.indexOf(K[G])!=-1){if(K[G]!="inherit"){H.fontWeight=K[G]}D=J=I=!0}else{if(F){K[G]!="inherit"&&(E+=K[G])}else{if(K[G]!="inherit"){H.fontSize=K[G].split("/")[0]}D=J=I=F=!0}}}}}if(E!=""){H.fontFamily=E}return H}};B.ToNumberArray=function(D){for(var D=B.trim(B.compressSpaces((D||"").replace(/,/g," "))).split(" "),C=0;C<D.length;C++){D[C]=parseFloat(D[C])}return D};B.Point=function(D,C){this.x=D;this.y=C;this.angleTo=function(E){return Math.atan2(E.y-this.y,E.x-this.x)};this.applyTransform=function(E){var F=this.x*E[1]+this.y*E[3]+E[5];this.x=this.x*E[0]+this.y*E[2]+E[4];this.y=F}};B.CreatePoint=function(C){C=B.ToNumberArray(C);return new B.Point(C[0],C[1])};B.CreatePath=function(E){for(var E=B.ToNumberArray(E),C=[],D=0;D<E.length;D+=2){C.push(new B.Point(E[D],E[D+1]))}return C};B.BoundingBox=function(E,C,D,F){this.y2=this.x2=this.y1=this.x1=Number.NaN;this.x=function(){return this.x1};this.y=function(){return this.y1};this.width=function(){return this.x2-this.x1};this.height=function(){return this.y2-this.y1};this.addPoint=function(G,H){if(G!=null){if(isNaN(this.x1)||isNaN(this.x2)){this.x2=this.x1=G}if(G<this.x1){this.x1=G}if(G>this.x2){this.x2=G}}if(H!=null){if(isNaN(this.y1)||isNaN(this.y2)){this.y2=this.y1=H}if(H<this.y1){this.y1=H}if(H>this.y2){this.y2=H}}};this.addX=function(G){this.addPoint(G,null)};this.addY=function(G){this.addPoint(null,G)};this.addBoundingBox=function(G){this.addPoint(G.x1,G.y1);this.addPoint(G.x2,G.y2)};this.addQuadraticCurve=function(I,K,G,J,L,H){G=I+2/3*(G-I);J=K+2/3*(J-K);this.addBezierCurve(I,K,G,G+1/3*(L-I),J,J+1/3*(H-K),L,H)};this.addBezierCurve=function(O,Q,R,P,J,M,L,K){var H=[O,Q],G=[R,P],I=[J,M],N=[L,K];this.addPoint(H[0],H[1]);this.addPoint(N[0],N[1]);for(i=0;i<=1;i++){O=function(S){return Math.pow(1-S,3)*H[i]+3*Math.pow(1-S,2)*S*G[i]+3*(1-S)*Math.pow(S,2)*I[i]+Math.pow(S,3)*N[i]},Q=6*H[i]-12*G[i]+6*I[i],R=-3*H[i]+9*G[i]-9*I[i]+3*N[i],P=3*G[i]-3*H[i],R==0?Q!=0&&(Q=-P/Q,0<Q&&Q<1&&(i==0&&this.addX(O(Q)),i==1&&this.addY(O(Q)))):(P=Math.pow(Q,2)-4*P*R,P<0||(J=(-Q+Math.sqrt(P))/(2*R),0<J&&J<1&&(i==0&&this.addX(O(J)),i==1&&this.addY(O(J))),Q=(-Q-Math.sqrt(P))/(2*R),0<Q&&Q<1&&(i==0&&this.addX(O(Q)),i==1&&this.addY(O(Q)))))}};this.isPointInBox=function(G,H){return this.x1<=G&&G<=this.x2&&this.y1<=H&&H<=this.y2};this.addPoint(E,C);this.addPoint(D,F)};B.Transform=function(F){var C=this;this.Type={};this.Type.translate=function(H){this.p=B.CreatePoint(H);this.apply=function(I){I.translate(this.p.x||0,this.p.y||0)};this.applyToPoint=function(I){I.applyTransform([1,0,0,1,this.p.x||0,this.p.y||0])}};this.Type.rotate=function(H){H=B.ToNumberArray(H);this.angle=new B.Property("angle",H[0]);this.cx=H[1]||0;this.cy=H[2]||0;this.apply=function(I){I.translate(this.cx,this.cy);I.rotate(this.angle.Angle.toRadians());I.translate(-this.cx,-this.cy)};this.applyToPoint=function(I){var J=this.angle.Angle.toRadians();I.applyTransform([1,0,0,1,this.p.x||0,this.p.y||0]);I.applyTransform([Math.cos(J),Math.sin(J),-Math.sin(J),Math.cos(J),0,0]);I.applyTransform([1,0,0,1,-this.p.x||0,-this.p.y||0])}};this.Type.scale=function(H){this.p=B.CreatePoint(H);this.apply=function(I){I.scale(this.p.x||1,this.p.y||this.p.x||1)};this.applyToPoint=function(I){I.applyTransform([this.p.x||0,0,0,this.p.y||0,0,0])}};this.Type.matrix=function(H){this.m=B.ToNumberArray(H);this.apply=function(I){I.transform(this.m[0],this.m[1],this.m[2],this.m[3],this.m[4],this.m[5])};this.applyToPoint=function(I){I.applyTransform(this.m)}};this.Type.SkewBase=function(H){this.base=C.Type.matrix;this.base(H);this.angle=new B.Property("angle",H)};this.Type.SkewBase.prototype=new this.Type.matrix;this.Type.skewX=function(H){this.base=C.Type.SkewBase;this.base(H);this.m=[1,0,Math.tan(this.angle.Angle.toRadians()),1,0,0]};this.Type.skewX.prototype=new this.Type.SkewBase;this.Type.skewY=function(H){this.base=C.Type.SkewBase;this.base(H);this.m=[1,Math.tan(this.angle.Angle.toRadians()),0,1,0,0]};this.Type.skewY.prototype=new this.Type.SkewBase;this.transforms=[];this.apply=function(H){for(var I=0;I<this.transforms.length;I++){this.transforms[I].apply(H)}};this.applyToPoint=function(H){for(var I=0;I<this.transforms.length;I++){this.transforms[I].applyToPoint(H)}};for(var F=B.trim(B.compressSpaces(F)).split(/\s(?=[a-z])/),E=0;E<F.length;E++){var G=F[E].split("(")[0],D=F[E].split("(")[1].replace(")","");this.transforms.push(new this.Type[G](D))}};B.AspectRatio=function(N,Q,M,G,C,O,P,F,H,K){var Q=B.compressSpaces(Q),Q=Q.replace(/^defer\s/,""),J=Q.split(" ")[0]||"xMidYMid",Q=Q.split(" ")[1]||"meet",I=M/G,E=C/O,D=Math.min(I,E),L=Math.max(I,E);Q=="meet"&&(G*=D,O*=D);Q=="slice"&&(G*=L,O*=L);H=new B.Property("refX",H);K=new B.Property("refY",K);H.hasValue()&&K.hasValue()?N.translate(-D*H.Length.toPixels("x"),-D*K.Length.toPixels("y")):(J.match(/^xMid/)&&(Q=="meet"&&D==E||Q=="slice"&&L==E)&&N.translate(M/2-G/2,0),J.match(/YMid$/)&&(Q=="meet"&&D==I||Q=="slice"&&L==I)&&N.translate(0,C/2-O/2),J.match(/^xMax/)&&(Q=="meet"&&D==E||Q=="slice"&&L==E)&&N.translate(M-G,0),J.match(/YMax$/)&&(Q=="meet"&&D==I||Q=="slice"&&L==I)&&N.translate(0,C-O));J=="none"?N.scale(I,E):Q=="meet"?N.scale(D,D):Q=="slice"&&N.scale(L,L);N.translate(P==null?0:-P,F==null?0:-F)};B.Element={};B.Element.ElementBase=function(F){this.attributes={};this.styles={};this.children=[];this.attribute=function(I,H){var J=this.attributes[I];if(J!=null){return J}J=new B.Property(I,"");H==!0&&(this.attributes[I]=J);return J};this.style=function(I,H){var J=this.styles[I];if(J!=null){return J}J=this.attribute(I);if(J!=null&&J.hasValue()){return J}J=this.parent;if(J!=null&&(J=J.style(I),J!=null&&J.hasValue())){return J}J=new B.Property(I,"");H==!0&&(this.styles[I]=J);return J};this.render=function(H){if(this.style("display").value!="none"&&this.attribute("visibility").value!="hidden"){H.save();this.setContext(H);if(this.attribute("mask").hasValue()){var I=this.attribute("mask").Definition.getDefinition();I!=null&&I.apply(H,this)}else{this.style("filter").hasValue()?(I=this.style("filter").Definition.getDefinition(),I!=null&&I.apply(H,this)):this.renderChildren(H)}this.clearContext(H);H.restore()}};this.setContext=function(){};this.clearContext=function(){};this.renderChildren=function(H){for(var I=0;I<this.children.length;I++){this.children[I].render(H)}};this.addChild=function(I,H){var J=I;H&&(J=B.CreateElement(I));J.parent=this;this.children.push(J)};if(F!=null&&F.nodeType==1){for(var C=0;C<F.childNodes.length;C++){var E=F.childNodes[C];E.nodeType==1&&this.addChild(E,!0)}for(C=0;C<F.attributes.length;C++){E=F.attributes[C],this.attributes[E.nodeName]=new B.Property(E.nodeName,E.nodeValue)}E=B.Styles[F.nodeName];if(E!=null){for(var G in E){this.styles[G]=E[G]}}if(this.attribute("class").hasValue()){for(var C=B.compressSpaces(this.attribute("class").value).split(" "),D=0;D<C.length;D++){E=B.Styles["."+C[D]];if(E!=null){for(G in E){this.styles[G]=E[G]}}E=B.Styles[F.nodeName+"."+C[D]];if(E!=null){for(G in E){this.styles[G]=E[G]}}}}if(this.attribute("style").hasValue()){E=this.attribute("style").value.split(";");for(C=0;C<E.length;C++){B.trim(E[C])!=""&&(F=E[C].split(":"),G=B.trim(F[0]),F=B.trim(F[1]),this.styles[G]=new B.Property(G,F))}}this.attribute("id").hasValue()&&B.Definitions[this.attribute("id").value]==null&&(B.Definitions[this.attribute("id").value]=this)}};B.Element.RenderedElementBase=function(C){this.base=B.Element.ElementBase;this.base(C);this.setContext=function(D){if(this.style("fill").Definition.isUrl()){var E=this.style("fill").Definition.getFillStyle(this);if(E!=null){D.fillStyle=E}}else{if(this.style("fill").hasValue()){E=this.style("fill"),this.style("fill-opacity").hasValue()&&(E=E.Color.addOpacity(this.style("fill-opacity").value)),D.fillStyle=E.value=="none"?"rgba(0,0,0,0)":E.value}}if(this.style("stroke").Definition.isUrl()){if(E=this.style("stroke").Definition.getFillStyle(this),E!=null){D.strokeStyle=E}}else{if(this.style("stroke").hasValue()){E=this.style("stroke"),this.style("stroke-opacity").hasValue()&&(E=E.Color.addOpacity(this.style("stroke-opacity").value)),D.strokeStyle=E.value=="none"?"rgba(0,0,0,0)":E.value}}if(this.style("stroke-width").hasValue()){D.lineWidth=this.style("stroke-width").Length.toPixels()}if(this.style("stroke-linecap").hasValue()){D.lineCap=this.style("stroke-linecap").value}if(this.style("stroke-linejoin").hasValue()){D.lineJoin=this.style("stroke-linejoin").value}if(this.style("stroke-miterlimit").hasValue()){D.miterLimit=this.style("stroke-miterlimit").value}if(typeof D.font!="undefined"){D.font=B.Font.CreateFont(this.style("font-style").value,this.style("font-variant").value,this.style("font-weight").value,this.style("font-size").hasValue()?this.style("font-size").Length.toPixels()+"px":"",this.style("font-family").value).toString()}this.attribute("transform").hasValue()&&(new B.Transform(this.attribute("transform").value)).apply(D);this.attribute("clip-path").hasValue()&&(E=this.attribute("clip-path").Definition.getDefinition(),E!=null&&E.apply(D));if(this.style("opacity").hasValue()){D.globalAlpha=this.style("opacity").numValue()}}};B.Element.RenderedElementBase.prototype=new B.Element.ElementBase;B.Element.PathElementBase=function(C){this.base=B.Element.RenderedElementBase;this.base(C);this.path=function(D){D!=null&&D.beginPath();return new B.BoundingBox};this.renderChildren=function(D){this.path(D);B.Mouse.checkPath(this,D);D.fillStyle!=""&&D.fill();D.strokeStyle!=""&&D.stroke();var F=this.getMarkers();if(F!=null){if(this.style("marker-start").Definition.isUrl()){var G=this.style("marker-start").Definition.getDefinition();G.render(D,F[0][0],F[0][1])}if(this.style("marker-mid").Definition.isUrl()){for(var G=this.style("marker-mid").Definition.getDefinition(),E=1;E<F.length-1;E++){G.render(D,F[E][0],F[E][1])}}this.style("marker-end").Definition.isUrl()&&(G=this.style("marker-end").Definition.getDefinition(),G.render(D,F[F.length-1][0],F[F.length-1][1]))}};this.getBoundingBox=function(){return this.path()};this.getMarkers=function(){return null}};B.Element.PathElementBase.prototype=new B.Element.RenderedElementBase;B.Element.svg=function(C){this.base=B.Element.RenderedElementBase;this.base(C);this.baseClearContext=this.clearContext;this.clearContext=function(D){this.baseClearContext(D);B.ViewPort.RemoveCurrent()};this.baseSetContext=this.setContext;this.setContext=function(E){E.strokeStyle="rgba(0,0,0,0)";E.lineCap="butt";E.lineJoin="miter";E.miterLimit=4;this.baseSetContext(E);this.attribute("x").hasValue()&&this.attribute("y").hasValue()&&E.translate(this.attribute("x").Length.toPixels("x"),this.attribute("y").Length.toPixels("y"));var G=B.ViewPort.width(),H=B.ViewPort.height();if(typeof this.root=="undefined"&&this.attribute("width").hasValue()&&this.attribute("height").hasValue()){var G=this.attribute("width").Length.toPixels("x"),H=this.attribute("height").Length.toPixels("y"),F=0,I=0;this.attribute("refX").hasValue()&&this.attribute("refY").hasValue()&&(F=-this.attribute("refX").Length.toPixels("x"),I=-this.attribute("refY").Length.toPixels("y"));E.beginPath();E.moveTo(F,I);E.lineTo(G,I);E.lineTo(G,H);E.lineTo(F,H);E.closePath();E.clip()}B.ViewPort.SetCurrent(G,H);if(this.attribute("viewBox").hasValue()){var F=B.ToNumberArray(this.attribute("viewBox").value),I=F[0],D=F[1],G=F[2],H=F[3];B.AspectRatio(E,this.attribute("preserveAspectRatio").value,B.ViewPort.width(),G,B.ViewPort.height(),H,I,D,this.attribute("refX").value,this.attribute("refY").value);B.ViewPort.RemoveCurrent();B.ViewPort.SetCurrent(F[2],F[3])}}};B.Element.svg.prototype=new B.Element.RenderedElementBase;B.Element.rect=function(C){this.base=B.Element.PathElementBase;this.base(C);this.path=function(E){var G=this.attribute("x").Length.toPixels("x"),H=this.attribute("y").Length.toPixels("y"),F=this.attribute("width").Length.toPixels("x"),J=this.attribute("height").Length.toPixels("y"),D=this.attribute("rx").Length.toPixels("x"),I=this.attribute("ry").Length.toPixels("y");this.attribute("rx").hasValue()&&!this.attribute("ry").hasValue()&&(I=D);this.attribute("ry").hasValue()&&!this.attribute("rx").hasValue()&&(D=I);E!=null&&(E.beginPath(),E.moveTo(G+D,H),E.lineTo(G+F-D,H),E.quadraticCurveTo(G+F,H,G+F,H+I),E.lineTo(G+F,H+J-I),E.quadraticCurveTo(G+F,H+J,G+F-D,H+J),E.lineTo(G+D,H+J),E.quadraticCurveTo(G,H+J,G,H+J-I),E.lineTo(G,H+I),E.quadraticCurveTo(G,H,G+D,H),E.closePath());return new B.BoundingBox(G,H,G+F,H+J)}};B.Element.rect.prototype=new B.Element.PathElementBase;B.Element.circle=function(C){this.base=B.Element.PathElementBase;this.base(C);this.path=function(D){var F=this.attribute("cx").Length.toPixels("x"),G=this.attribute("cy").Length.toPixels("y"),E=this.attribute("r").Length.toPixels();D!=null&&(D.beginPath(),D.arc(F,G,E,0,Math.PI*2,!0),D.closePath());return new B.BoundingBox(F-E,G-E,F+E,G+E)}};B.Element.circle.prototype=new B.Element.PathElementBase;B.Element.ellipse=function(C){this.base=B.Element.PathElementBase;this.base(C);this.path=function(E){var G=4*((Math.sqrt(2)-1)/3),H=this.attribute("rx").Length.toPixels("x"),F=this.attribute("ry").Length.toPixels("y"),I=this.attribute("cx").Length.toPixels("x"),D=this.attribute("cy").Length.toPixels("y");E!=null&&(E.beginPath(),E.moveTo(I,D-F),E.bezierCurveTo(I+G*H,D-F,I+H,D-G*F,I+H,D),E.bezierCurveTo(I+H,D+G*F,I+G*H,D+F,I,D+F),E.bezierCurveTo(I-G*H,D+F,I-H,D+G*F,I-H,D),E.bezierCurveTo(I-H,D-G*F,I-G*H,D-F,I,D-F),E.closePath());return new B.BoundingBox(I-H,D-F,I+H,D+F)}};B.Element.ellipse.prototype=new B.Element.PathElementBase;B.Element.line=function(C){this.base=B.Element.PathElementBase;this.base(C);this.getPoints=function(){return[new B.Point(this.attribute("x1").Length.toPixels("x"),this.attribute("y1").Length.toPixels("y")),new B.Point(this.attribute("x2").Length.toPixels("x"),this.attribute("y2").Length.toPixels("y"))]};this.path=function(D){var E=this.getPoints();D!=null&&(D.beginPath(),D.moveTo(E[0].x,E[0].y),D.lineTo(E[1].x,E[1].y));return new B.BoundingBox(E[0].x,E[0].y,E[1].x,E[1].y)};this.getMarkers=function(){var E=this.getPoints(),D=E[0].angleTo(E[1]);return[[E[0],D],[E[1],D]]}};B.Element.line.prototype=new B.Element.PathElementBase;B.Element.polyline=function(C){this.base=B.Element.PathElementBase;this.base(C);this.points=B.CreatePath(this.attribute("points").value);this.path=function(D){var E=new B.BoundingBox(this.points[0].x,this.points[0].y);D!=null&&(D.beginPath(),D.moveTo(this.points[0].x,this.points[0].y));for(var F=1;F<this.points.length;F++){E.addPoint(this.points[F].x,this.points[F].y),D!=null&&D.lineTo(this.points[F].x,this.points[F].y)}return E};this.getMarkers=function(){for(var E=[],D=0;D<this.points.length-1;D++){E.push([this.points[D],this.points[D].angleTo(this.points[D+1])])}E.push([this.points[this.points.length-1],E[E.length-1][1]]);return E}};B.Element.polyline.prototype=new B.Element.PathElementBase;B.Element.polygon=function(C){this.base=B.Element.polyline;this.base(C);this.basePath=this.path;this.path=function(E){var D=this.basePath(E);E!=null&&(E.lineTo(this.points[0].x,this.points[0].y),E.closePath());return D}};B.Element.polygon.prototype=new B.Element.polyline;B.Element.path=function(C){this.base=B.Element.PathElementBase;this.base(C);C=this.attribute("d").value;C=C.replace(/,/gm," ");C=C.replace(/([MmZzLlHhVvCcSsQqTtAa])([MmZzLlHhVvCcSsQqTtAa])/gm,"$1 $2");C=C.replace(/([MmZzLlHhVvCcSsQqTtAa])([MmZzLlHhVvCcSsQqTtAa])/gm,"$1 $2");C=C.replace(/([MmZzLlHhVvCcSsQqTtAa])([^\s])/gm,"$1 $2");C=C.replace(/([^\s])([MmZzLlHhVvCcSsQqTtAa])/gm,"$1 $2");C=C.replace(/([0-9])([+\-])/gm,"$1 $2");C=C.replace(/(\.[0-9]*)(\.)/gm,"$1 $2");C=C.replace(/([Aa](\s+[0-9]+){3})\s+([01])\s*([01])/gm,"$1 $3 $4 ");C=B.compressSpaces(C);C=B.trim(C);this.PathParser=new function(D){this.tokens=D.split(" ");this.reset=function(){this.i=-1;this.previousCommand=this.command="";this.start=new B.Point(0,0);this.control=new B.Point(0,0);this.current=new B.Point(0,0);this.points=[];this.angles=[]};this.isEnd=function(){return this.i>=this.tokens.length-1};this.isCommandOrEnd=function(){return this.isEnd()?!0:this.tokens[this.i+1].match(/^[A-Za-z]$/)!=null};this.isRelativeCommand=function(){return this.command==this.command.toLowerCase()};this.getToken=function(){this.i+=1;return this.tokens[this.i]};this.getScalar=function(){return parseFloat(this.getToken())};this.nextCommand=function(){this.previousCommand=this.command;this.command=this.getToken()};this.getPoint=function(){return this.makeAbsolute(new B.Point(this.getScalar(),this.getScalar()))};this.getAsControlPoint=function(){var E=this.getPoint();return this.control=E};this.getAsCurrentPoint=function(){var E=this.getPoint();return this.current=E};this.getReflectedControlPoint=function(){return this.previousCommand.toLowerCase()!="c"&&this.previousCommand.toLowerCase()!="s"?this.current:new B.Point(2*this.current.x-this.control.x,2*this.current.y-this.control.y)};this.makeAbsolute=function(E){if(this.isRelativeCommand()){E.x=this.current.x+E.x,E.y=this.current.y+E.y}return E};this.addMarker=function(F,G,E){E!=null&&this.angles.length>0&&this.angles[this.angles.length-1]==null&&(this.angles[this.angles.length-1]=this.points[this.points.length-1].angleTo(E));this.addMarkerAngle(F,G==null?null:G.angleTo(F))};this.addMarkerAngle=function(E,F){this.points.push(E);this.angles.push(F)};this.getMarkerPoints=function(){return this.points};this.getMarkerAngles=function(){for(var E=0;E<this.angles.length;E++){if(this.angles[E]==null){for(var F=E+1;F<this.angles.length;F++){if(this.angles[F]!=null){this.angles[E]=this.angles[F];break}}}}return this.angles}}(C);this.path=function(R){var N=this.PathParser;N.reset();var O=new B.BoundingBox;for(R!=null&&R.beginPath();!N.isEnd();){switch(N.nextCommand(),N.command.toUpperCase()){case"M":var D=N.getAsCurrentPoint();N.addMarker(D);O.addPoint(D.x,D.y);R!=null&&R.moveTo(D.x,D.y);for(N.start=N.current;!N.isCommandOrEnd();){D=N.getAsCurrentPoint(),N.addMarker(D,N.start),O.addPoint(D.x,D.y),R!=null&&R.lineTo(D.x,D.y)}break;case"L":for(;!N.isCommandOrEnd();){var P=N.current,D=N.getAsCurrentPoint();N.addMarker(D,P);O.addPoint(D.x,D.y);R!=null&&R.lineTo(D.x,D.y)}break;case"H":for(;!N.isCommandOrEnd();){D=new B.Point((N.isRelativeCommand()?N.current.x:0)+N.getScalar(),N.current.y),N.addMarker(D,N.current),N.current=D,O.addPoint(N.current.x,N.current.y),R!=null&&R.lineTo(N.current.x,N.current.y)}break;case"V":for(;!N.isCommandOrEnd();){D=new B.Point(N.current.x,(N.isRelativeCommand()?N.current.y:0)+N.getScalar()),N.addMarker(D,N.current),N.current=D,O.addPoint(N.current.x,N.current.y),R!=null&&R.lineTo(N.current.x,N.current.y)}break;case"C":for(;!N.isCommandOrEnd();){var Q=N.current,P=N.getPoint(),H=N.getAsControlPoint(),D=N.getAsCurrentPoint();N.addMarker(D,H,P);O.addBezierCurve(Q.x,Q.y,P.x,P.y,H.x,H.y,D.x,D.y);R!=null&&R.bezierCurveTo(P.x,P.y,H.x,H.y,D.x,D.y)}break;case"S":for(;!N.isCommandOrEnd();){Q=N.current,P=N.getReflectedControlPoint(),H=N.getAsControlPoint(),D=N.getAsCurrentPoint(),N.addMarker(D,H,P),O.addBezierCurve(Q.x,Q.y,P.x,P.y,H.x,H.y,D.x,D.y),R!=null&&R.bezierCurveTo(P.x,P.y,H.x,H.y,D.x,D.y)}break;case"Q":for(;!N.isCommandOrEnd();){Q=N.current,H=N.getAsControlPoint(),D=N.getAsCurrentPoint(),N.addMarker(D,H,H),O.addQuadraticCurve(Q.x,Q.y,H.x,H.y,D.x,D.y),R!=null&&R.quadraticCurveTo(H.x,H.y,D.x,D.y)}break;case"T":for(;!N.isCommandOrEnd();){Q=N.current,H=N.getReflectedControlPoint(),N.control=H,D=N.getAsCurrentPoint(),N.addMarker(D,H,H),O.addQuadraticCurve(Q.x,Q.y,H.x,H.y,D.x,D.y),R!=null&&R.quadraticCurveTo(H.x,H.y,D.x,D.y)}break;case"A":for(;!N.isCommandOrEnd();){var Q=N.current,I=N.getScalar(),L=N.getScalar(),P=N.getScalar()*(Math.PI/180),K=N.getScalar(),H=N.getScalar(),D=N.getAsCurrentPoint(),J=new B.Point(Math.cos(P)*(Q.x-D.x)/2+Math.sin(P)*(Q.y-D.y)/2,-Math.sin(P)*(Q.x-D.x)/2+Math.cos(P)*(Q.y-D.y)/2),G=Math.pow(J.x,2)/Math.pow(I,2)+Math.pow(J.y,2)/Math.pow(L,2);G>1&&(I*=Math.sqrt(G),L*=Math.sqrt(G));K=(K==H?-1:1)*Math.sqrt((Math.pow(I,2)*Math.pow(L,2)-Math.pow(I,2)*Math.pow(J.y,2)-Math.pow(L,2)*Math.pow(J.x,2))/(Math.pow(I,2)*Math.pow(J.y,2)+Math.pow(L,2)*Math.pow(J.x,2)));isNaN(K)&&(K=0);var F=new B.Point(K*I*J.y/L,K*-L*J.x/I),Q=new B.Point((Q.x+D.x)/2+Math.cos(P)*F.x-Math.sin(P)*F.y,(Q.y+D.y)/2+Math.sin(P)*F.x+Math.cos(P)*F.y),M=function(S,T){return(S[0]*T[0]+S[1]*T[1])/(Math.sqrt(Math.pow(S[0],2)+Math.pow(S[1],2))*Math.sqrt(Math.pow(T[0],2)+Math.pow(T[1],2)))},E=function(S,T){return(S[0]*T[1]<S[1]*T[0]?-1:1)*Math.acos(M(S,T))},K=E([1,0],[(J.x-F.x)/I,(J.y-F.y)/L]),G=[(J.x-F.x)/I,(J.y-F.y)/L],F=[(-J.x-F.x)/I,(-J.y-F.y)/L],J=E(G,F);if(M(G,F)<=-1){J=Math.PI}M(G,F)>=1&&(J=0);H==0&&J>0&&(J-=2*Math.PI);H==1&&J<0&&(J+=2*Math.PI);G=new B.Point(Q.x-I*Math.cos((K+J)/2),Q.y-L*Math.sin((K+J)/2));N.addMarkerAngle(G,(K+J)/2+(H==0?1:-1)*Math.PI/2);N.addMarkerAngle(D,J+(H==0?1:-1)*Math.PI/2);O.addPoint(D.x,D.y);R!=null&&(M=I>L?I:L,D=I>L?1:I/L,I=I>L?L/I:1,R.translate(Q.x,Q.y),R.rotate(P),R.scale(D,I),R.arc(0,0,M,K,K+J,1-H),R.scale(1/D,1/I),R.rotate(-P),R.translate(-Q.x,-Q.y))}break;case"Z":R!=null&&R.closePath(),N.current=N.start}}return O};this.getMarkers=function(){for(var G=this.PathParser.getMarkerPoints(),E=this.PathParser.getMarkerAngles(),F=[],D=0;D<G.length;D++){F.push([G[D],E[D]])}return F}};B.Element.path.prototype=new B.Element.PathElementBase;B.Element.pattern=function(C){this.base=B.Element.ElementBase;this.base(C);this.createPattern=function(D){var E=new B.Element.svg;E.attributes.viewBox=new B.Property("viewBox",this.attribute("viewBox").value);E.attributes.x=new B.Property("x",this.attribute("x").value);E.attributes.y=new B.Property("y",this.attribute("y").value);E.attributes.width=new B.Property("width",this.attribute("width").value);E.attributes.height=new B.Property("height",this.attribute("height").value);E.children=this.children;var F=document.createElement("canvas");F.width=this.attribute("width").Length.toPixels("x");F.height=this.attribute("height").Length.toPixels("y");E.render(F.getContext("2d"));return D.createPattern(F,"repeat")}};B.Element.pattern.prototype=new B.Element.ElementBase;B.Element.marker=function(C){this.base=B.Element.ElementBase;this.base(C);this.baseRender=this.render;this.render=function(D,F,G){D.translate(F.x,F.y);this.attribute("orient").valueOrDefault("auto")=="auto"&&D.rotate(G);this.attribute("markerUnits").valueOrDefault("strokeWidth")=="strokeWidth"&&D.scale(D.lineWidth,D.lineWidth);D.save();var E=new B.Element.svg;E.attributes.viewBox=new B.Property("viewBox",this.attribute("viewBox").value);E.attributes.refX=new B.Property("refX",this.attribute("refX").value);E.attributes.refY=new B.Property("refY",this.attribute("refY").value);E.attributes.width=new B.Property("width",this.attribute("markerWidth").value);E.attributes.height=new B.Property("height",this.attribute("markerHeight").value);E.attributes.fill=new B.Property("fill",this.attribute("fill").valueOrDefault("black"));E.attributes.stroke=new B.Property("stroke",this.attribute("stroke").valueOrDefault("none"));E.children=this.children;E.render(D);D.restore();this.attribute("markerUnits").valueOrDefault("strokeWidth")=="strokeWidth"&&D.scale(1/D.lineWidth,1/D.lineWidth);this.attribute("orient").valueOrDefault("auto")=="auto"&&D.rotate(-G);D.translate(-F.x,-F.y)}};B.Element.marker.prototype=new B.Element.ElementBase;B.Element.defs=function(C){this.base=B.Element.ElementBase;this.base(C);this.render=function(){}};B.Element.defs.prototype=new B.Element.ElementBase;B.Element.GradientBase=function(C){this.base=B.Element.ElementBase;this.base(C);this.gradientUnits=this.attribute("gradientUnits").valueOrDefault("objectBoundingBox");this.stops=[];for(C=0;C<this.children.length;C++){this.stops.push(this.children[C])}this.getGradient=function(){};this.createGradient=function(E,G){var H=this;this.attribute("xlink:href").hasValue()&&(H=this.attribute("xlink:href").Definition.getDefinition());for(var F=this.getGradient(E,G),I=0;I<H.stops.length;I++){F.addColorStop(H.stops[I].offset,H.stops[I].color)}if(this.attribute("gradientTransform").hasValue()){H=B.ViewPort.viewPorts[0];I=new B.Element.rect;I.attributes.x=new B.Property("x",-B.MAX_VIRTUAL_PIXELS/3);I.attributes.y=new B.Property("y",-B.MAX_VIRTUAL_PIXELS/3);I.attributes.width=new B.Property("width",B.MAX_VIRTUAL_PIXELS);I.attributes.height=new B.Property("height",B.MAX_VIRTUAL_PIXELS);var D=new B.Element.g;D.attributes.transform=new B.Property("transform",this.attribute("gradientTransform").value);D.children=[I];I=new B.Element.svg;I.attributes.x=new B.Property("x",0);I.attributes.y=new B.Property("y",0);I.attributes.width=new B.Property("width",H.width);I.attributes.height=new B.Property("height",H.height);I.children=[D];D=document.createElement("canvas");D.width=H.width;D.height=H.height;H=D.getContext("2d");H.fillStyle=F;I.render(H);return H.createPattern(D,"no-repeat")}return F}};B.Element.GradientBase.prototype=new B.Element.ElementBase;B.Element.linearGradient=function(C){this.base=B.Element.GradientBase;this.base(C);this.getGradient=function(H,F){var G=F.getBoundingBox(),E=this.gradientUnits=="objectBoundingBox"?G.x()+G.width()*this.attribute("x1").numValue():this.attribute("x1").Length.toPixels("x"),I=this.gradientUnits=="objectBoundingBox"?G.y()+G.height()*this.attribute("y1").numValue():this.attribute("y1").Length.toPixels("y"),D=this.gradientUnits=="objectBoundingBox"?G.x()+G.width()*this.attribute("x2").numValue():this.attribute("x2").Length.toPixels("x"),G=this.gradientUnits=="objectBoundingBox"?G.y()+G.height()*this.attribute("y2").numValue():this.attribute("y2").Length.toPixels("y");return H.createLinearGradient(E,I,D,G)}};B.Element.linearGradient.prototype=new B.Element.GradientBase;B.Element.radialGradient=function(C){this.base=B.Element.GradientBase;this.base(C);this.getGradient=function(I,G){var H=G.getBoundingBox(),F=this.gradientUnits=="objectBoundingBox"?H.x()+H.width()*this.attribute("cx").numValue():this.attribute("cx").Length.toPixels("x"),J=this.gradientUnits=="objectBoundingBox"?H.y()+H.height()*this.attribute("cy").numValue():this.attribute("cy").Length.toPixels("y"),D=F,E=J;this.attribute("fx").hasValue()&&(D=this.gradientUnits=="objectBoundingBox"?H.x()+H.width()*this.attribute("fx").numValue():this.attribute("fx").Length.toPixels("x"));this.attribute("fy").hasValue()&&(E=this.gradientUnits=="objectBoundingBox"?H.y()+H.height()*this.attribute("fy").numValue():this.attribute("fy").Length.toPixels("y"));H=this.gradientUnits=="objectBoundingBox"?(H.width()+H.height())/2*this.attribute("r").numValue():this.attribute("r").Length.toPixels();return I.createRadialGradient(D,E,0,F,J,H)}};B.Element.radialGradient.prototype=new B.Element.GradientBase;B.Element.stop=function(C){this.base=B.Element.ElementBase;this.base(C);this.offset=this.attribute("offset").numValue();C=this.style("stop-color");this.style("stop-opacity").hasValue()&&(C=C.Color.addOpacity(this.style("stop-opacity").value));this.color=C.value};B.Element.stop.prototype=new B.Element.ElementBase;B.Element.AnimateBase=function(C){this.base=B.Element.ElementBase;this.base(C);B.Animations.push(this);this.duration=0;this.begin=this.attribute("begin").Time.toMilliseconds();this.maxDuration=this.begin+this.attribute("dur").Time.toMilliseconds();this.getProperty=function(){var E=this.attribute("attributeType").value,D=this.attribute("attributeName").value;return E=="CSS"?this.parent.style(D,!0):this.parent.attribute(D,!0)};this.initialValue=null;this.removed=!1;this.calcValue=function(){return""};this.update=function(D){if(this.initialValue==null){this.initialValue=this.getProperty().value}if(this.duration>this.maxDuration){if(this.attribute("repeatCount").value=="indefinite"){this.duration=0}else{return this.attribute("fill").valueOrDefault("remove")=="remove"&&!this.removed?(this.removed=!0,this.getProperty().value=this.initialValue,!0):!1}}this.duration+=D;D=!1;if(this.begin<this.duration){D=this.calcValue(),this.attribute("type").hasValue()&&(D=this.attribute("type").value+"("+D+")"),this.getProperty().value=D,D=!0}return D};this.progress=function(){return(this.duration-this.begin)/(this.maxDuration-this.begin)}};B.Element.AnimateBase.prototype=new B.Element.ElementBase;B.Element.animate=function(C){this.base=B.Element.AnimateBase;this.base(C);this.calcValue=function(){var E=this.attribute("from").numValue(),D=this.attribute("to").numValue();return E+(D-E)*this.progress()}};B.Element.animate.prototype=new B.Element.AnimateBase;B.Element.animateColor=function(C){this.base=B.Element.AnimateBase;this.base(C);this.calcValue=function(){var G=new RGBColor(this.attribute("from").value),E=new RGBColor(this.attribute("to").value);if(G.ok&&E.ok){var F=G.r+(E.r-G.r)*this.progress(),D=G.g+(E.g-G.g)*this.progress(),G=G.b+(E.b-G.b)*this.progress();return"rgb("+parseInt(F,10)+","+parseInt(D,10)+","+parseInt(G,10)+")"}return this.attribute("from").value}};B.Element.animateColor.prototype=new B.Element.AnimateBase;B.Element.animateTransform=function(C){this.base=B.Element.animate;this.base(C)};B.Element.animateTransform.prototype=new B.Element.animate;B.Element.font=function(D){this.base=B.Element.ElementBase;this.base(D);this.horizAdvX=this.attribute("horiz-adv-x").numValue();this.isArabic=this.isRTL=!1;this.missingGlyph=this.fontFace=null;this.glyphs=[];for(D=0;D<this.children.length;D++){var C=this.children[D];if(C.type=="font-face"){this.fontFace=C,C.style("font-family").hasValue()&&(B.Definitions[C.style("font-family").value]=this)}else{if(C.type=="missing-glyph"){this.missingGlyph=C}else{if(C.type=="glyph"){C.arabicForm!=""?(this.isArabic=this.isRTL=!0,typeof this.glyphs[C.unicode]=="undefined"&&(this.glyphs[C.unicode]=[]),this.glyphs[C.unicode][C.arabicForm]=C):this.glyphs[C.unicode]=C}}}}};B.Element.font.prototype=new B.Element.ElementBase;B.Element.fontface=function(C){this.base=B.Element.ElementBase;this.base(C);this.ascent=this.attribute("ascent").value;this.descent=this.attribute("descent").value;this.unitsPerEm=this.attribute("units-per-em").numValue()};B.Element.fontface.prototype=new B.Element.ElementBase;B.Element.missingglyph=function(C){this.base=B.Element.path;this.base(C);this.horizAdvX=0};B.Element.missingglyph.prototype=new B.Element.path;B.Element.glyph=function(C){this.base=B.Element.path;this.base(C);this.horizAdvX=this.attribute("horiz-adv-x").numValue();this.unicode=this.attribute("unicode").value;this.arabicForm=this.attribute("arabic-form").value};B.Element.glyph.prototype=new B.Element.path;B.Element.text=function(E){this.base=B.Element.RenderedElementBase;this.base(E);if(E!=null){this.children=[];for(var C=0;C<E.childNodes.length;C++){var D=E.childNodes[C];D.nodeType==1?this.addChild(D,!0):D.nodeType==3&&this.addChild(new B.Element.tspan(D),!1)}}this.baseSetContext=this.setContext;this.setContext=function(F){this.baseSetContext(F);if(this.style("dominant-baseline").hasValue()){F.textBaseline=this.style("dominant-baseline").value}if(this.style("alignment-baseline").hasValue()){F.textBaseline=this.style("alignment-baseline").value}};this.renderChildren=function(K){for(var M=this.style("text-anchor").valueOrDefault("start"),L=this.attribute("x").Length.toPixels("x"),N=this.attribute("y").Length.toPixels("y"),F=0;F<this.children.length;F++){var G=this.children[F];G.attribute("x").hasValue()?G.x=G.attribute("x").Length.toPixels("x"):(G.attribute("dx").hasValue()&&(L+=G.attribute("dx").Length.toPixels("x")),G.x=L);L=G.measureText(K);if(M!="start"&&(F==0||G.attribute("x").hasValue())){for(var J=L,I=F+1;I<this.children.length;I++){var H=this.children[I];if(H.attribute("x").hasValue()){break}J+=H.measureText(K)}G.x-=M=="end"?J:J/2}L=G.x+L;G.attribute("y").hasValue()?G.y=G.attribute("y").Length.toPixels("y"):(G.attribute("dy").hasValue()&&(N+=G.attribute("dy").Length.toPixels("y")),G.y=N);N=G.y;G.render(K)}}};B.Element.text.prototype=new B.Element.RenderedElementBase;B.Element.TextElementBase=function(C){this.base=B.Element.RenderedElementBase;this.base(C);this.getGlyph=function(H,F,G){var E=F[G],I=null;if(H.isArabic){var D="isolated";if((G==0||F[G-1]==" ")&&G<F.length-2&&F[G+1]!=" "){D="terminal"}G>0&&F[G-1]!=" "&&G<F.length-2&&F[G+1]!=" "&&(D="medial");if(G>0&&F[G-1]!=" "&&(G==F.length-1||F[G+1]==" ")){D="initial"}typeof H.glyphs[E]!="undefined"&&(I=H.glyphs[E][D],I==null&&H.glyphs[E].type=="glyph"&&(I=H.glyphs[E]))}else{I=H.glyphs[E]}if(I==null){I=H.missingGlyph}return I};this.renderChildren=function(K){var J=this.parent.style("font-family").Definition.getDefinition();if(J!=null){var F=this.parent.style("font-size").numValueOrDefault(B.Font.Parse(B.ctx.font).fontSize),D=this.parent.style("font-style").valueOrDefault(B.Font.Parse(B.ctx.font).fontStyle),L=this.getText();J.isRTL&&(L=L.split("").reverse().join(""));for(var M=B.ToNumberArray(this.parent.attribute("dx").value),E=0;E<L.length;E++){var G=this.getGlyph(J,L,E),I=F/J.fontFace.unitsPerEm;K.translate(this.x,this.y);K.scale(I,-I);var H=K.lineWidth;K.lineWidth=K.lineWidth*J.fontFace.unitsPerEm/F;D=="italic"&&K.transform(1,0,0.4,1,0,0);G.render(K);D=="italic"&&K.transform(1,0,-0.4,1,0,0);K.lineWidth=H;K.scale(1/I,-1/I);K.translate(-this.x,-this.y);this.x+=F*(G.horizAdvX||J.horizAdvX)/J.fontFace.unitsPerEm;typeof M[E]!="undefined"&&!isNaN(M[E])&&(this.x+=M[E])}}else{K.strokeStyle!=""&&K.strokeText(B.compressSpaces(this.getText()),this.x,this.y),K.fillStyle!=""&&K.fillText(B.compressSpaces(this.getText()),this.x,this.y)}};this.getText=function(){};this.measureText=function(H){var G=this.parent.style("font-family").Definition.getDefinition();if(G!=null){var H=this.parent.style("font-size").numValueOrDefault(B.Font.Parse(B.ctx.font).fontSize),J=0,F=this.getText();G.isRTL&&(F=F.split("").reverse().join(""));for(var E=B.ToNumberArray(this.parent.attribute("dx").value),D=0;D<F.length;D++){var I=this.getGlyph(G,F,D);J+=(I.horizAdvX||G.horizAdvX)*H/G.fontFace.unitsPerEm;typeof E[D]!="undefined"&&!isNaN(E[D])&&(J+=E[D])}return J}G=B.compressSpaces(this.getText());if(!H.measureText){return G.length*10}H.save();this.setContext(H);G=H.measureText(G).width;H.restore();return G}};B.Element.TextElementBase.prototype=new B.Element.RenderedElementBase;B.Element.tspan=function(C){this.base=B.Element.TextElementBase;this.base(C);this.text=C.nodeType==3?C.nodeValue:C.childNodes.length>0?C.childNodes[0].nodeValue:C.text;this.getText=function(){return this.text}};B.Element.tspan.prototype=new B.Element.TextElementBase;B.Element.tref=function(C){this.base=B.Element.TextElementBase;this.base(C);this.getText=function(){var D=this.attribute("xlink:href").Definition.getDefinition();if(D!=null){return D.children[0].getText()}}};B.Element.tref.prototype=new B.Element.TextElementBase;B.Element.a=function(D){this.base=B.Element.TextElementBase;this.base(D);this.hasText=!0;for(var C=0;C<D.childNodes.length;C++){if(D.childNodes[C].nodeType!=3){this.hasText=!1}}this.text=this.hasText?D.childNodes[0].nodeValue:"";this.getText=function(){return this.text};this.baseRenderChildren=this.renderChildren;this.renderChildren=function(E){if(this.hasText){this.baseRenderChildren(E);var F=new B.Property("fontSize",B.Font.Parse(B.ctx.font).fontSize);B.Mouse.checkBoundingBox(this,new B.BoundingBox(this.x,this.y-F.Length.toPixels("y"),this.x+this.measureText(E),this.y))}else{F=new B.Element.g,F.children=this.children,F.parent=this,F.render(E)}};this.onclick=function(){window.open(this.attribute("xlink:href").value)};this.onmousemove=function(){B.ctx.canvas.style.cursor="pointer"}};B.Element.a.prototype=new B.Element.TextElementBase;B.Element.image=function(D){this.base=B.Element.RenderedElementBase;this.base(D);B.Images.push(this);this.img=document.createElement("img");this.loaded=!1;var C=this;this.img.onload=function(){C.loaded=!0};this.img.src=this.attribute("xlink:href").value;this.renderChildren=function(G){var H=this.attribute("x").Length.toPixels("x"),F=this.attribute("y").Length.toPixels("y"),I=this.attribute("width").Length.toPixels("x"),E=this.attribute("height").Length.toPixels("y");I==0||E==0||(G.save(),G.translate(H,F),B.AspectRatio(G,this.attribute("preserveAspectRatio").value,I,this.img.width,E,this.img.height,0,0),G.drawImage(this.img,0,0),G.restore())}};B.Element.image.prototype=new B.Element.RenderedElementBase;B.Element.g=function(C){this.base=B.Element.RenderedElementBase;this.base(C);this.getBoundingBox=function(){for(var E=new B.BoundingBox,D=0;D<this.children.length;D++){E.addBoundingBox(this.children[D].getBoundingBox())}return E}};B.Element.g.prototype=new B.Element.RenderedElementBase;B.Element.symbol=function(C){this.base=B.Element.RenderedElementBase;this.base(C);this.baseSetContext=this.setContext;this.setContext=function(F){this.baseSetContext(F);if(this.attribute("viewBox").hasValue()){var E=B.ToNumberArray(this.attribute("viewBox").value),G=E[0],D=E[1];width=E[2];height=E[3];B.AspectRatio(F,this.attribute("preserveAspectRatio").value,this.attribute("width").Length.toPixels("x"),width,this.attribute("height").Length.toPixels("y"),height,G,D);B.ViewPort.SetCurrent(E[2],E[3])}}};B.Element.symbol.prototype=new B.Element.RenderedElementBase;B.Element.style=function(J){this.base=B.Element.ElementBase;this.base(J);for(var J=J.childNodes[0].nodeValue+(J.childNodes.length>1?J.childNodes[1].nodeValue:""),J=J.replace(/(\/\*([^*]|[\r\n]|(\*+([^*\/]|[\r\n])))*\*+\/)|(^[\s]*\/\/.*)/gm,""),J=B.compressSpaces(J),J=J.split("}"),M=0;M<J.length;M++){if(B.trim(J[M])!=""){for(var I=J[M].split("{"),E=I[0].split(","),I=I[1].split(";"),C=0;C<E.length;C++){var K=B.trim(E[C]);if(K!=""){for(var L={},D=0;D<I.length;D++){var F=I[D].indexOf(":"),H=I[D].substr(0,F),F=I[D].substr(F+1,I[D].length-F);H!=null&&F!=null&&(L[B.trim(H)]=new B.Property(B.trim(H),B.trim(F)))}B.Styles[K]=L;if(K=="@font-face"){K=L["font-family"].value.replace(/"/g,"");L=L.src.value.split(",");for(D=0;D<L.length;D++){if(L[D].indexOf('format("svg")')>0){H=L[D].indexOf("url");F=L[D].indexOf(")",H);H=L[D].substr(H+5,F-H-6);H=B.parseXml(B.ajax(H)).getElementsByTagName("font");for(F=0;F<H.length;F++){var G=B.CreateElement(H[F]);B.Definitions[K]=G}}}}}}}}};B.Element.style.prototype=new B.Element.ElementBase;B.Element.use=function(C){this.base=B.Element.RenderedElementBase;this.base(C);this.baseSetContext=this.setContext;this.setContext=function(D){this.baseSetContext(D);this.attribute("x").hasValue()&&D.translate(this.attribute("x").Length.toPixels("x"),0);this.attribute("y").hasValue()&&D.translate(0,this.attribute("y").Length.toPixels("y"))};this.getDefinition=function(){var D=this.attribute("xlink:href").Definition.getDefinition();if(this.attribute("width").hasValue()){D.attribute("width",!0).value=this.attribute("width").value}if(this.attribute("height").hasValue()){D.attribute("height",!0).value=this.attribute("height").value}return D};this.path=function(E){var D=this.getDefinition();D!=null&&D.path(E)};this.renderChildren=function(E){var D=this.getDefinition();D!=null&&D.render(E)}};B.Element.use.prototype=new B.Element.RenderedElementBase;B.Element.mask=function(C){this.base=B.Element.ElementBase;this.base(C);this.apply=function(L,J){var K=this.attribute("x").Length.toPixels("x"),D=this.attribute("y").Length.toPixels("y"),M=this.attribute("width").Length.toPixels("x"),N=this.attribute("height").Length.toPixels("y"),E=J.attribute("mask").value;J.attribute("mask").value="";var F=document.createElement("canvas");F.width=K+M;F.height=D+N;var I=F.getContext("2d");this.renderChildren(I);var H=document.createElement("canvas");H.width=K+M;H.height=D+N;var G=H.getContext("2d");J.render(G);G.globalCompositeOperation="destination-in";G.fillStyle=I.createPattern(F,"no-repeat");G.fillRect(0,0,K+M,D+N);L.fillStyle=G.createPattern(H,"no-repeat");L.fillRect(0,0,K+M,D+N);J.attribute("mask").value=E};this.render=function(){}};B.Element.mask.prototype=new B.Element.ElementBase;B.Element.clipPath=function(C){this.base=B.Element.ElementBase;this.base(C);this.apply=function(E){for(var D=0;D<this.children.length;D++){this.children[D].path&&(this.children[D].path(E),E.clip())}};this.render=function(){}};B.Element.clipPath.prototype=new B.Element.ElementBase;B.Element.filter=function(C){this.base=B.Element.ElementBase;this.base(C);this.apply=function(M,K){var L=K.getBoundingBox(),D=this.attribute("x").Length.toPixels("x"),N=this.attribute("y").Length.toPixels("y");if(D==0||N==0){D=L.x1,N=L.y1}var O=this.attribute("width").Length.toPixels("x"),F=this.attribute("height").Length.toPixels("y");if(O==0||F==0){O=L.width(),F=L.height()}L=K.style("filter").value;K.style("filter").value="";var G=0.2*O,J=0.2*F,I=document.createElement("canvas");I.width=O+2*G;I.height=F+2*J;var H=I.getContext("2d");H.translate(-D+G,-N+J);K.render(H);for(var E=0;E<this.children.length;E++){this.children[E].apply(H,0,0,O+2*G,F+2*J)}M.drawImage(I,0,0,O+2*G,F+2*J,D-G,N-J,O+2*G,F+2*J);K.style("filter",!0).value=L};this.render=function(){}};B.Element.filter.prototype=new B.Element.ElementBase;B.Element.feGaussianBlur=function(D){function C(P,O,S,Q,R){for(var I=0;I<R;I++){for(var J=0;J<Q;J++){for(var M=P[I*Q*4+J*4+3]/255,L=0;L<4;L++){for(var K=S[0]*(M==0?255:P[I*Q*4+J*4+L])*(M==0||L==3?1:M),H=1;H<S.length;H++){var G=Math.max(J-H,0),N=P[I*Q*4+G*4+3]/255,G=Math.min(J+H,Q-1),G=P[I*Q*4+G*4+3]/255,F=S[H],E;N==0?E=255:(E=Math.max(J-H,0),E=P[I*Q*4+E*4+L]);N=E*(N==0||L==3?1:N);G==0?E=255:(E=Math.min(J+H,Q-1),E=P[I*Q*4+E*4+L]);K+=F*(N+E*(G==0||L==3?1:G))}O[J*R*4+I*4+L]=K}}}}this.base=B.Element.ElementBase;this.base(D);this.apply=function(J,I,G,K,E){var G=this.attribute("stdDeviation").numValue(),I=J.getImageData(0,0,K,E),G=Math.max(G,0.01),F=Math.ceil(G*4)+1;mask=[];for(var H=0;H<F;H++){mask[H]=Math.exp(-0.5*(H/G)*(H/G))}G=mask;F=0;for(H=1;H<G.length;H++){F+=Math.abs(G[H])}F=2*F+Math.abs(G[0]);for(H=0;H<G.length;H++){G[H]/=F}tmp=[];C(I.data,tmp,G,K,E);C(tmp,I.data,G,E,K);J.clearRect(0,0,K,E);J.putImageData(I,0,0)}};B.Element.filter.prototype=new B.Element.feGaussianBlur;B.Element.title=function(){};B.Element.title.prototype=new B.Element.ElementBase;B.Element.desc=function(){};B.Element.desc.prototype=new B.Element.ElementBase;B.Element.MISSING=function(C){console.log("ERROR: Element '"+C.nodeName+"' not yet implemented.")};B.Element.MISSING.prototype=new B.Element.ElementBase;B.CreateElement=function(E){var C=E.nodeName.replace(/^[^:]+:/,""),C=C.replace(/\-/g,""),D=null,D=typeof B.Element[C]!="undefined"?new B.Element[C](E):new B.Element.MISSING(E);D.type=E.nodeName;return D};B.load=function(D,C){B.loadXml(D,B.ajax(C))};B.loadXml=function(D,C){B.loadXmlDoc(D,B.parseXml(C))};B.loadXmlDoc=function(H,D){B.init(H);var G=function(K){for(var J=H.canvas;J;){K.x-=J.offsetLeft,K.y-=J.offsetTop,J=J.offsetParent}window.scrollX&&(K.x+=window.scrollX);window.scrollY&&(K.y+=window.scrollY);return K};if(B.opts.ignoreMouse!=!0){H.canvas.onclick=function(J){J=G(new B.Point(J!=null?J.clientX:event.clientX,J!=null?J.clientY:event.clientY));B.Mouse.onclick(J.x,J.y)},H.canvas.onmousemove=function(J){J=G(new B.Point(J!=null?J.clientX:event.clientX,J!=null?J.clientY:event.clientY));B.Mouse.onmousemove(J.x,J.y)}}var I=B.CreateElement(D.documentElement),E=I.root=!0,F=function(){B.ViewPort.Clear();H.canvas.parentNode&&B.ViewPort.SetCurrent(H.canvas.parentNode.clientWidth,H.canvas.parentNode.clientHeight);if(B.opts.ignoreDimensions!=!0){if(I.style("width").hasValue()){H.canvas.width=I.style("width").Length.toPixels("x"),H.canvas.style.width=H.canvas.width+"px"}if(I.style("height").hasValue()){H.canvas.height=I.style("height").Length.toPixels("y"),H.canvas.style.height=H.canvas.height+"px"}}var L=H.canvas.clientWidth||H.canvas.width,K=H.canvas.clientHeight||H.canvas.height;B.ViewPort.SetCurrent(L,K);if(B.opts!=null&&B.opts.offsetX!=null){I.attribute("x",!0).value=B.opts.offsetX}if(B.opts!=null&&B.opts.offsetY!=null){I.attribute("y",!0).value=B.opts.offsetY}if(B.opts!=null&&B.opts.scaleWidth!=null&&B.opts.scaleHeight!=null){var M=1,J=1;I.attribute("width").hasValue()&&(M=I.attribute("width").Length.toPixels("x")/B.opts.scaleWidth);I.attribute("height").hasValue()&&(J=I.attribute("height").Length.toPixels("y")/B.opts.scaleHeight);I.attribute("width",!0).value=B.opts.scaleWidth;I.attribute("height",!0).value=B.opts.scaleHeight;I.attribute("viewBox",!0).value="0 0 "+L*M+" "+K*J;I.attribute("preserveAspectRatio",!0).value="none"}B.opts.ignoreClear!=!0&&H.clearRect(0,0,L,K);I.render(H);E&&(E=!1,B.opts!=null&&typeof B.opts.renderCallback=="function"&&B.opts.renderCallback())},C=!0;B.ImagesLoaded()&&(C=!1,F());B.intervalID=setInterval(function(){var J=!1;C&&B.ImagesLoaded()&&(C=!1,J=!0);B.opts.ignoreMouse!=!0&&(J|=B.Mouse.hasEvents());if(B.opts.ignoreAnimation!=!0){for(var K=0;K<B.Animations.length;K++){J|=B.Animations[K].update(1000/B.FRAMERATE)}}B.opts!=null&&typeof B.opts.forceRedraw=="function"&&B.opts.forceRedraw()==!0&&(J=!0);J&&(F(),B.Mouse.runEvents())},1000/B.FRAMERATE)};B.stop=function(){B.intervalID&&clearInterval(B.intervalID)};B.Mouse=new function(){this.events=[];this.hasEvents=function(){return this.events.length!=0};this.onclick=function(D,C){this.events.push({type:"onclick",x:D,y:C,run:function(E){if(E.onclick){E.onclick()}}})};this.onmousemove=function(D,C){this.events.push({type:"onmousemove",x:D,y:C,run:function(E){if(E.onmousemove){E.onmousemove()}}})};this.eventElements=[];this.checkPath=function(E,C){for(var D=0;D<this.events.length;D++){var F=this.events[D];C.isPointInPath&&C.isPointInPath(F.x,F.y)&&(this.eventElements[D]=E)}};this.checkBoundingBox=function(E,C){for(var D=0;D<this.events.length;D++){var F=this.events[D];C.isPointInBox(F.x,F.y)&&(this.eventElements[D]=E)}};this.runEvents=function(){B.ctx.canvas.style.cursor="";for(var E=0;E<this.events.length;E++){for(var C=this.events[E],D=this.eventElements[E];D;){C.run(D),D=D.parent}}this.events=[];this.eventElements=[]}};return B}this.canvg=function(E,D,B){if(E==null&&D==null&&B==null){for(var D=document.getElementsByTagName("svg"),C=0;C<D.length;C++){E=D[C];B=document.createElement("canvas");B.width=E.clientWidth;B.height=E.clientHeight;E.parentNode.insertBefore(B,E);E.parentNode.removeChild(E);var F=document.createElement("div");F.appendChild(E);canvg(B,F.innerHTML)}}else{B=B||{},typeof E=="string"&&(E=document.getElementById(E)),E.svg==null?(C=A(),E.svg=C):(C=E.svg,C.stop()),C.opts=B,E=E.getContext("2d"),typeof D.documentElement!="undefined"?C.loadXmlDoc(E,D):D.substr(0,1)=="<"?C.loadXml(E,D):C.load(E,D)}}})();if(CanvasRenderingContext2D){CanvasRenderingContext2D.prototype.drawSvg=function(B,E,D,A,C){canvg(this.canvas,B,{ignoreMouse:!0,ignoreAnimation:!0,ignoreDimensions:!0,ignoreClear:!0,offsetX:E,offsetY:D,scaleWidth:A,scaleHeight:C})}}(function(C){var F=C.css,E=C.CanVGRenderer,I=C.SVGRenderer,D=C.extend,B=C.merge,A=C.addEvent,G=C.createElement,H=C.discardElement;D(E.prototype,I.prototype);D(E.prototype,{create:function(M,K,L,J){this.setContainer(K,L,J);this.configure(M)},setContainer:function(P,N,O){var R=P.style,J=P.parentNode,Q=R.left,R=R.top,L=P.offsetWidth,M=P.offsetHeight,K={visibility:"hidden",position:"absolute"};this.init.apply(this,[P,N,O]);this.canvas=G("canvas",{width:L,height:M},{position:"relative",left:Q,top:R},P);this.ttLine=G("div",null,K,J);this.ttDiv=G("div",null,K,J);this.ttTimer=void 0;this.hiddenSvg=P=G("div",{width:L,height:M},{visibility:"hidden",left:Q,top:R},J);P.appendChild(this.box)},configure:function(M){var N=this,Q=M.options.tooltip,O=Q.borderWidth,P=N.ttDiv,L=Q.style,J=N.ttLine,K=parseInt(L.padding,10),L=B(L,{padding:K+"px","background-color":Q.backgroundColor,"border-style":"solid","border-width":O+"px","border-radius":Q.borderRadius+"px"});Q.shadow&&(L=B(L,{"box-shadow":"1px 1px 3px gray","-webkit-box-shadow":"1px 1px 3px gray"}));F(P,L);F(J,{"border-left":"1px solid darkgray"});A(M,"tooltipRefresh",function(S){var T=M.container,U=T.offsetLeft,T=T.offsetTop,R;P.innerHTML=S.text;R=M.tooltip.getPosition(P.offsetWidth,P.offsetHeight,{plotX:S.x,plotY:S.y});F(P,{visibility:"visible",left:R.x+"px",top:R.y+"px","border-color":S.borderColor});F(J,{visibility:"visible",left:U+S.x+"px",top:T+M.plotTop+"px",height:M.plotHeight+"px"});N.ttTimer!==void 0&&clearTimeout(N.ttTimer);N.ttTimer=setTimeout(function(){F(P,{visibility:"hidden"});F(J,{visibility:"hidden"})},3000)})},destroy:function(){H(this.canvas);this.ttTimer!==void 0&&clearTimeout(this.ttTimer);H(this.ttLine);H(this.ttDiv);H(this.hiddenSvg);return I.prototype.destroy.apply(this)},color:function(L,J,K){L&&L.linearGradient&&(L=L.stops[L.stops.length-1][1]);return I.prototype.color.call(this,L,J,K)},draw:function(){window.canvg(this.canvas,this.hiddenSvg.innerHTML)}})})(Highcharts);