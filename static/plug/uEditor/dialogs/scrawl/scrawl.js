var scrawl=function(A){A&&this.initOptions(A)};(function(){var C=$G("J_brushBoard"),A=C.getContext("2d"),D=[],B=0;scrawl.prototype={isScrawl:false,brushWidth:-1,brushColor:"",initOptions:function(F){var E=this;E.originalState(F);E._buildToolbarColor(F.colorList);E._addBoardListener(F.saveNum);E._addOPerateListener(F.saveNum);E._addColorBarListener();E._addBrushBarListener();E._addEraserBarListener();E._addAddImgListener();E._addRemoveImgListenter();E._addScalePicListenter();E._addClearSelectionListenter();E._originalColorSelect(F.drawBrushColor);E._originalBrushSelect(F.drawBrushSize);E._clearSelection()},originalState:function(F){var E=this;E.brushWidth=F.drawBrushSize;E.brushColor=F.drawBrushColor;A.lineWidth=E.brushWidth;A.strokeStyle=E.brushColor;A.fillStyle="transparent";A.lineCap="round";A.fill()},_buildToolbarColor:function(G){var E=null,H=[];H.push("<table id='J_colorList'>");for(var F=0,I;I=G[F++];){if((F-1)%5==0){if(F!=1){H.push("</tr>")}H.push("<tr>")}E="#"+I;H.push("<td><a title='"+E+"' href='javascript:void(0)' style='background-color:"+E+"'></a></td>")}H.push("</tr></table>");$G("J_colorBar").innerHTML=H.join("")},_addBoardListener:function(M){var K=this,O=0,H=-1,I=-1,E=false,L=false,N=false,J=0,G,F="";O=parseInt(domUtils.getComputedStyle($G("J_wrap"),"margin-left"));D.push(A.getImageData(0,0,A.canvas.width,A.canvas.height));B+=1;domUtils.on(C,["mousedown","mousemove","mouseup","mouseout"],function(P){G=browser.webkit?P.which:J;switch(P.type){case"mousedown":J=1;F=1;E=true;N=false;L=false;K.isScrawl=true;H=P.clientX-O;I=P.clientY-O;A.beginPath();break;case"mousemove":if(!F&&G==0){return}if(!F&&G){H=P.clientX-O;I=P.clientY-O;A.beginPath();F=1}if(N||!E){return}var Q=P.clientX-O,R=P.clientY-O;A.moveTo(H,I);A.lineTo(Q,R);A.stroke();H=Q;I=R;L=true;break;case"mouseup":J=0;if(!E){return}if(!L){A.arc(H,I,A.lineWidth,0,Math.PI*2,false);A.fillStyle=A.strokeStyle;A.fill()}A.closePath();K._saveOPerate(M);E=false;L=false;N=true;H=-1;I=-1;break;case"mouseout":F="";J=0;if(G==1){return}A.closePath();break}})},_addOPerateListener:function(F){var E=this;domUtils.on($G("J_previousStep"),"click",function(){if(B>1){B-=1;A.clearRect(0,0,A.canvas.width,A.canvas.height);A.putImageData(D[B-1],0,0);E.btn2Highlight("J_nextStep");B==1&&E.btn2disable("J_previousStep")}});domUtils.on($G("J_nextStep"),"click",function(){if(B>0&&B<D.length){A.clearRect(0,0,A.canvas.width,A.canvas.height);A.putImageData(D[B],0,0);B+=1;E.btn2Highlight("J_previousStep");B==D.length&&E.btn2disable("J_nextStep")}});domUtils.on($G("J_clearBoard"),"click",function(){A.clearRect(0,0,A.canvas.width,A.canvas.height);D=[];E._saveOPerate(F);B=1;E.isScrawl=false;E.btn2disable("J_previousStep");E.btn2disable("J_nextStep");E.btn2disable("J_clearBoard")})},_addColorBarListener:function(){var E=this;domUtils.on($G("J_colorBar"),"click",function(F){var G=E.getTarget(F),H=G.title;if(!!H){E._addColorSelect(G);E.brushColor=H;A.globalCompositeOperation="source-over";A.lineWidth=E.brushWidth;A.strokeStyle=H}})},_addBrushBarListener:function(){var E=this;domUtils.on($G("J_brushBar"),"click",function(G){var H=E.getTarget(G),F=browser.ie?H.innerText:H.text;if(!!F){E._addBESelect(H);A.globalCompositeOperation="source-over";A.lineWidth=parseInt(F);A.strokeStyle=E.brushColor;E.brushWidth=A.lineWidth}})},_addEraserBarListener:function(){var E=this;domUtils.on($G("J_eraserBar"),"click",function(G){var H=E.getTarget(G),F=browser.ie?H.innerText:H.text;if(!!F){E._addBESelect(H);A.lineWidth=parseInt(F);A.globalCompositeOperation="destination-out";A.strokeStyle="#FFF"}})},_addAddImgListener:function(){var E=$G("J_imgTxt");if(!window.FileReader){$G("J_addImg").style.display="none";$G("J_removeImg").style.display="none";$G("J_sacleBoard").style.display="none"}domUtils.on(E,"change",function(F){var G=E.parentNode;addMaskLayer(lang.backgroundUploading);var H=F.target||F.srcElement,I=new FileReader();I.onload=function(J){var K=J.target||J.srcElement;ue_callback(K.result,"SUCCESS")};I.readAsDataURL(H.files[0]);G.reset()})},_addRemoveImgListenter:function(){var E=this;domUtils.on($G("J_removeImg"),"click",function(){$G("J_picBoard").innerHTML="";E.btn2disable("J_removeImg");E.btn2disable("J_sacleBoard")})},_addScalePicListenter:function(){domUtils.on($G("J_sacleBoard"),"click",function(){var E=$G("J_picBoard"),F=$G("J_scaleCon"),H=E.children[0];if(H){if(!F){E.style.cssText="position:relative;z-index:999;"+E.style.cssText;H.style.cssText="position: absolute;top:"+(C.height-H.height)/2+"px;left:"+(C.width-H.width)/2+"px;";var G=new ScaleBoy();E.appendChild(G.init());G.startScale(H)}else{if(F.style.visibility=="visible"){F.style.visibility="hidden";E.style.position="";E.style.zIndex=""}else{F.style.visibility="visible";E.style.cssText+="position:relative;z-index:999"}}}})},_addClearSelectionListenter:function(){var E=document;domUtils.on(E,"mousemove",function(F){if(browser.ie&&browser.version<11){E.selection.clear()}else{window.getSelection().removeAllRanges()}})},_clearSelection:function(){var F=["J_operateBar","J_colorBar","J_brushBar","J_eraserBar","J_picBoard"];for(var E=0,G;G=F[E++];){domUtils.unSelectable($G(G))}},_saveOPerate:function(F){var E=this;if(D.length<=F){if(B<D.length){E.btn2disable("J_nextStep");D.splice(B)}D.push(A.getImageData(0,0,A.canvas.width,A.canvas.height));B=D.length}else{D.shift();D.push(A.getImageData(0,0,A.canvas.width,A.canvas.height));B=D.length}E.btn2Highlight("J_previousStep");E.btn2Highlight("J_clearBoard")},_originalColorSelect:function(F){var E=$G("J_colorList").getElementsByTagName("td");for(var H=0,G;G=E[H++];){if(G.children[0].title.toLowerCase()==F){G.children[0].style.opacity=1}}},_originalBrushSelect:function(H){var E=$G("J_brushBar").children;for(var F=0,I;I=E[F++];){if(I.tagName.toLowerCase()=="a"){var G=browser.ie?I.innerText:I.text;if(G.toLowerCase()==H){I.style.opacity=1}}}},_addColorSelect:function(M){var E=this,N=$G("J_colorList").getElementsByTagName("td"),O=$G("J_eraserBar").children,F=$G("J_brushBar").children;for(var J=0,P;P=N[J++];){P.children[0].style.opacity=0.3}for(var H=0,K;K=F[H++];){if(K.tagName.toLowerCase()=="a"){K.style.opacity=0.3;var I=browser.ie?K.innerText:K.text;if(I.toLowerCase()==this.brushWidth){K.style.opacity=1}}}for(var G=0,L;L=O[G++];){if(L.tagName.toLowerCase()=="a"){L.style.opacity=0.3}}M.style.opacity=1;M.blur()},_addBESelect:function(I){var E=$G("J_brushBar").children;var G=$G("J_eraserBar").children;for(var F=0,K;K=E[F++];){if(K.tagName.toLowerCase()=="a"){K.style.opacity=0.3}}for(var J=0,H;H=G[J++];){if(H.tagName.toLowerCase()=="a"){H.style.opacity=0.3}}I.style.opacity=1;I.blur()},getCanvasData:function(){var F=$G("J_picBoard"),I=F.children[0];if(I){var G,H;if(I.style.position=="absolute"){G=parseInt(I.style.left);H=parseInt(I.style.top)}else{G=(F.offsetWidth-I.width)/2;H=(F.offsetHeight-I.height)/2}A.globalCompositeOperation="destination-over";A.drawImage(I,G,H,I.width,I.height)}else{A.globalCompositeOperation="destination-atop";A.fillStyle="#fff";A.fillRect(0,0,C.width,C.height)}try{return C.toDataURL("image/png").substring(22)}catch(E){return""}},btn2Highlight:function(F){var E=$G(F);E.className.indexOf("H")==-1&&(E.className+="H")},btn2disable:function(F){var E=$G(F);E.className.indexOf("H")!=-1&&(E.className=E.className.replace("H",""))},getTarget:function(E){return E.target||E.srcElement}}})();var ScaleBoy=function(){this.dom=null;this.scalingElement=null};(function(){function C(){var H=document,E=H.getElementsByTagName("head")[0],G=H.createElement("style"),F=".scale{visibility:hidden;cursor:move;position:absolute;left:0;top:0;width:100px;height:50px;background-color:#fff;font-size:0;line-height:0;opacity:.4;filter:Alpha(opacity=40);}.scale span{position:absolute;left:0;top:0;width:6px;height:6px;background-color:#006DAE;}.scale .hand0, .scale .hand7{cursor:nw-resize;}.scale .hand1, .scale .hand6{left:50%;margin-left:-3px;cursor:n-resize;}.scale .hand2, .scale .hand4, .scale .hand7{left:100%;margin-left:-6px;}.scale .hand3, .scale .hand4{top:50%;margin-top:-3px;cursor:w-resize;}.scale .hand5, .scale .hand6, .scale .hand7{margin-top:-6px;top:100%;}.scale .hand2, .scale .hand5{cursor:ne-resize;}";G.type="text/css";try{G.appendChild(H.createTextNode(F))}catch(D){G.styleSheet.cssText=F}E.appendChild(G)}function A(){var H=document,G,F=[],E=H.createElement("div");E.id="J_scaleCon";E.className="scale";for(var D=0;D<8;D++){F.push("<span class='hand"+D+"'></span>")}E.innerHTML=F.join("");return E}var B=[[1,1,-1,-1],[0,1,0,-1],[0,1,1,-1],[1,0,-1,0],[0,0,1,0],[1,0,-1,1],[0,0,0,1],[0,0,1,1]];ScaleBoy.prototype={init:function(){C();var D=this,E=D.dom=A();D.scaleMousemove.fp=D;domUtils.on(E,"mousedown",function(F){var G=F.target||F.srcElement;D.start={x:F.clientX,y:F.clientY};if(G.className.indexOf("hand")!=-1){D.dir=G.className.replace("hand","")}domUtils.on(document.body,"mousemove",D.scaleMousemove);F.stopPropagation?F.stopPropagation():F.cancelBubble=true});domUtils.on(document.body,"mouseup",function(F){if(D.start){domUtils.un(document.body,"mousemove",D.scaleMousemove);if(D.moved){D.updateScaledElement({position:{x:E.style.left,y:E.style.top},size:{w:E.style.width,h:E.style.height}})}delete D.start;delete D.moved;delete D.dir}});return E},startScale:function(E){var D=this,F=D.dom;F.style.cssText="visibility:visible;top:"+E.style.top+";left:"+E.style.left+";width:"+E.offsetWidth+"px;height:"+E.offsetHeight+"px;";D.scalingElement=E},updateScaledElement:function(G){var D=this.scalingElement,F=G.position,E=G.size;if(F){typeof F.x!="undefined"&&(D.style.left=F.x);typeof F.y!="undefined"&&(D.style.top=F.y)}if(E){E.w&&(D.style.width=E.w);E.h&&(D.style.height=E.h)}},updateStyleByDir:function(D,E){var G=this,H=G.dom,F;B["def"]=[1,1,0,0];if(B[D][0]!=0){F=parseInt(H.style.left)+E.x;H.style.left=G._validScaledProp("left",F)+"px"}if(B[D][1]!=0){F=parseInt(H.style.top)+E.y;H.style.top=G._validScaledProp("top",F)+"px"}if(B[D][2]!=0){F=H.clientWidth+B[D][2]*E.x;H.style.width=G._validScaledProp("width",F)+"px"}if(B[D][3]!=0){F=H.clientHeight+B[D][3]*E.y;H.style.height=G._validScaledProp("height",F)+"px"}if(D==="def"){G.updateScaledElement({position:{x:H.style.left,y:H.style.top}})}},scaleMousemove:function(F){var G=arguments.callee.fp,H=G.start,D=G.dir||"def",E={x:F.clientX-H.x,y:F.clientY-H.y};G.updateStyleByDir(D,E);arguments.callee.fp.start={x:F.clientX,y:F.clientY};arguments.callee.fp.moved=1},_validScaledProp:function(E,F){var G=this.dom,D=$G("J_picBoard");F=isNaN(F)?0:F;switch(E){case"left":return F<0?0:(F+G.clientWidth)>D.clientWidth?D.clientWidth-G.clientWidth:F;case"top":return F<0?0:(F+G.clientHeight)>D.clientHeight?D.clientHeight-G.clientHeight:F;case"width":return F<=0?1:(F+G.offsetLeft)>D.clientWidth?D.clientWidth-G.offsetLeft:F;case"height":return F<=0?1:(F+G.offsetTop)>D.clientHeight?D.clientHeight-G.offsetTop:F}}}})();function ue_callback(B,F){var C=document,A=$G("J_picBoard"),E=C.createElement("img");function D(H,I,M,G){var L=0,O=0,K,N=H.width||M,J=H.height||G;if(N>I||J>I){if(N>=J){if(L=N-I){K=(L/N).toFixed(2);H.height=J-J*K;H.width=I}}else{if(O=J-I){K=(O/J).toFixed(2);H.width=N-N*K;H.height=I}}}}removeMaskLayer();if(F=="SUCCESS"){A.innerHTML="";E.onload=function(){D(this,300);A.appendChild(E);var G=new scrawl();G.btn2Highlight("J_removeImg");G.btn2Highlight("J_sacleBoard")};E.src=B}else{alert(F)}}function removeMaskLayer(){var A=$G("J_maskLayer");A.className="maskLayerNull";A.innerHTML="";dialog.buttons[0].setDisabled(false)}function addMaskLayer(A){var B=$G("J_maskLayer");dialog.buttons[0].setDisabled(true);B.className="maskLayer";B.innerHTML=A}function exec(scrawlObj){if(scrawlObj.isScrawl){addMaskLayer(lang.scrawlUpLoading);var base64=scrawlObj.getCanvasData();if(!!base64){var options={timeout:100000,onsuccess:function(xhr){if(!scrawlObj.isCancelScrawl){var responseObj;responseObj=eval("("+xhr.responseText+")");if(responseObj.state=="SUCCESS"){var imgObj={},url=editor.options.scrawlUrlPrefix+responseObj.url;imgObj.src=url;imgObj._src=url;imgObj.alt=responseObj.original||"";imgObj.title=responseObj.title||"";editor.execCommand("insertImage",imgObj);dialog.close()}else{alert(responseObj.state)}}},onerror:function(){alert(lang.imageError);dialog.close()}};options[editor.getOpt("scrawlFieldName")]=base64;var actionUrl=editor.getActionUrl(editor.getOpt("scrawlActionName")),params=utils.serializeParam(editor.queryCommandValue("serverparam"))||"",url=utils.formatUrl(actionUrl+(actionUrl.indexOf("?")==-1?"?":"&")+params);ajax.request(url,options)}}else{addMaskLayer(lang.noScarwl+"&nbsp;&nbsp;&nbsp;<input type='button' value='"+lang.continueBtn+"'  onclick='removeMaskLayer()'/>")}};