function clickHandler(F,D,I){for(var C=0,G=F.length;C<G;C++){F[C].className=""}I.className="focus";var A=I.getAttribute("tabSrc");for(var B=0,H=D.length;B<H;B++){var J=D[B],E=J.getAttribute("id");<PERSON><PERSON>onclick=function(){this.style.zoom=1};if(E!=A){J.style.zIndex=1}else{J.style.zIndex=200}}}function switchTab(B){var F=$G(B).children,G=F[0].children,D=F[1].children;for(var A=0,C=G.length;A<C;A++){var E=G[A];if(E.className==="focus"){clickHandler(G,D,E)}E.onclick=function(){clickHandler(G,D,this)}}}switchTab("helptab");document.getElementById("version").innerHTML=parent.UE.version;