/* Select2 4.0.5 | https://github.com/select2/select2/blob/master/LICENSE.md */
(function(){if(jQuery&&jQuery.fn&&jQuery.fn.select2&&jQuery.fn.select2.amd){var A=jQuery.fn.select2.amd}return A.define("select2/i18n/nl",[],function(){return{errorLoading:function(){return"De resultaten konden niet worden geladen."},inputTooLong:function(B){var D=B.input.length-B.maximum,C="Gelieve "+D+" karakters te verwijderen";return C},inputTooShort:function(B){var D=B.minimum-B.input.length,C="Gelieve "+D+" of meer karakters in te voeren";return C},loadingMore:function(){return"Meer resultaten laden…"},maximumSelected:function(B){var D=B.maximum==1?"kan":"kunnen",C="Er "+D+" maar "+B.maximum+" item";return B.maximum!=1&&(C+="s"),C+=" worden geselecteerd",C},noResults:function(){return"Geen resultaten gevonden…"},searching:function(){return"Zoeken…"}}}),{define:A.define,require:A.require}})();