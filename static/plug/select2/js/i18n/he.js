/* Select2 4.0.5 | https://github.com/select2/select2/blob/master/LICENSE.md */
(function(){if(jQuery&&jQuery.fn&&jQuery.fn.select2&&jQuery.fn.select2.amd){var A=jQuery.fn.select2.amd}return A.define("select2/i18n/he",[],function(){return{errorLoading:function(){return"שגיאה בטעינת התוצאות"},inputTooLong:function(B){var D=B.input.length-B.maximum,C="נא למחוק ";return D===1?C+="תו אחד":C+=D+" תווים",C},inputTooShort:function(B){var D=B.minimum-B.input.length,C="נא להכניס ";return D===1?C+="תו אחד":C+=D+" תווים",C+=" או יותר",C},loadingMore:function(){return"טוען תוצאות נוספות…"},maximumSelected:function(B){var C="באפשרותך לבחור עד ";return B.maximum===1?C+="פריט אחד":C+=B.maximum+" פריטים",C},noResults:function(){return"לא נמצאו תוצאות"},searching:function(){return"מחפש…"}}}),{define:A.define,require:A.require}})();