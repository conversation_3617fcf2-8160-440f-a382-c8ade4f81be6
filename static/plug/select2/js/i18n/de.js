/* Select2 4.0.5 | https://github.com/select2/select2/blob/master/LICENSE.md */
(function(){if(jQuery&&jQuery.fn&&jQuery.fn.select2&&jQuery.fn.select2.amd){var A=jQuery.fn.select2.amd}return A.define("select2/i18n/de",[],function(){return{errorLoading:function(){return"Die Ergebnisse konnten nicht geladen werden."},inputTooLong:function(B){var C=B.input.length-B.maximum;return"Bitte "+C+" <PERSON>eichen weniger eingeben"},inputTooShort:function(B){var C=B.minimum-B.input.length;return"Bitte "+C+" Zeichen mehr eingeben"},loadingMore:function(){return"Lade mehr Ergebnisse…"},maximumSelected:function(B){var C="Sie können nur "+B.maximum+" Eintr";return B.maximum===1?C+="ag":C+="äge",C+=" auswählen",C},noResults:function(){return"Keine Übereinstimmungen gefunden"},searching:function(){return"Suche…"}}}),{define:A.define,require:A.require}})();