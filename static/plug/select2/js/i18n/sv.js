/* Select2 4.0.5 | https://github.com/select2/select2/blob/master/LICENSE.md */
(function(){if(jQuery&&jQuery.fn&&jQuery.fn.select2&&jQuery.fn.select2.amd){var A=jQuery.fn.select2.amd}return A.define("select2/i18n/sv",[],function(){return{errorLoading:function(){return"Resultat kunde inte laddas."},inputTooLong:function(B){var D=B.input.length-B.maximum,C="Vänligen sudda ut "+D+" tecken";return C},inputTooShort:function(B){var D=B.minimum-B.input.length,C="Vänligen skriv in "+D+" eller fler tecken";return C},loadingMore:function(){return"Laddar fler resultat…"},maximumSelected:function(B){var C="Du kan max välja "+B.maximum+" element";return C},noResults:function(){return"Inga träffar"},searching:function(){return"Söker…"}}}),{define:A.define,require:A.require}})();