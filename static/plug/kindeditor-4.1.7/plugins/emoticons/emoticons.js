KindEditor.plugin("emoticons",function(A){var C=this,D="emoticons",F=(C.emoticonsPath||C.pluginsPath+"emoticons/images/"),B=C.allowPreviewEmoticons===undefined?true:C.allowPreviewEmoticons,E=1;C.clickToolbar(D,function(){var M=5,W=9,U=135,Y=0,P=M*W,J=Math.ceil(U/P),O=Math.floor(W/2),N=A('<div class="ke-plugin-emoticons"></div>'),L=[],V=C.createMenu({name:D,beforeRemove:function(){T()}});V.div.append(N);var X,I;if(B){X=A('<div class="ke-preview"></div>').css("right",0);I=A('<img class="ke-preview-img" src="'+F+Y+'.gif" />');N.append(X);X.append(I)}function R(a,b,Z){if(X){a.mouseover(function(){if(b>O){X.css("left",0);X.css("right","")}else{X.css("left","");X.css("right",0)}I.attr("src",F+Z+".gif");A(this).addClass("ke-on")})}else{a.mouseover(function(){A(this).addClass("ke-on")})}a.mouseout(function(){A(this).removeClass("ke-on")});a.click(function(c){C.insertHtml('<img src="'+F+Z+'.gif" border="0" alt="" />').hideMenu().focus();c.stop()})}function K(g,f){var h=document.createElement("table");f.append(h);if(X){A(h).mouseover(function(){X.show("block")});A(h).mouseout(function(){X.hide()});L.push(A(h))}h.className="ke-table";h.cellPadding=0;h.cellSpacing=0;h.border=0;var b=(g-1)*P+Y;for(var e=0;e<M;e++){var c=h.insertRow(e);for(var Z=0;Z<W;Z++){var a=A(c.insertCell(Z));a.addClass("ke-cell");R(a,Z,b);var d=A('<span class="ke-img"></span>').css("background-position","-"+(24*b)+"px 0px").css("background-image","url("+F+"static.gif)");a.append(d);L.push(a);b++}}return h}var H=K(E,N);function T(){A.each(L,function(){this.unbind()})}var S;function Q(Z,a){Z.click(function(b){T();H.parentNode.removeChild(H);S.remove();H=K(a,N);G(a);E=a;b.stop()})}function G(c){S=A('<div class="ke-page"></div>');N.append(S);for(var b=1;b<=J;b++){if(c!==b){var Z=A('<a href="javascript:;">['+b+"]</a>");Q(Z,b);S.append(Z);L.push(Z)}else{S.append(A("@["+b+"]"))}S.append(A("@&nbsp;"))}}G(E)})});