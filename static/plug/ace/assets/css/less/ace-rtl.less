//refer to CSS builder tool or build/rtl.js file

 @import "bootstrap/variables.less";
 @import "bootstrap/mixins.less";
 
 @import "ace-features.less";
 @import "variables.less";
 @import "mixins.less";

 
 
 .make-rtl {
	direction: rtl;
	text-align: right;
 }
 .make-ltr {
	direction: ltr;
	text-align: left;
 }

.rtl {
 html& , body& {
	direction: rtl;
	text-align: right;
	overflow-x: hidden;
 }

// .container.main-container {
//	padding: 0;
// }
 
 .nav-scroll.scroll-active .scroll-track {
   right: auto;
   left: 0;
 }
 
  @media only screen and (max-width: @grid-float-breakpoint-max) {
  .navbar .navbar-nav > li, .navbar .navbar-nav > li:first-child {
	border-width: 1px 0 0;
  }
 }

 

 .dropdown-menu {
	text-align: right;
 }

 
 


 .ace-switch + .lbl , .knob-container {
   direction: ltr;
   text-align: left;
 }
 input.ace[type="checkbox"] + .lbl::before, input.ace[type="radio"] + .lbl::before,
 input.ace[type="checkbox"] ~ .lbl::before, input.ace[type="radio"] ~ .lbl::before //for ASP.NET too
 {
	margin-right: auto;
	margin-left: 1px;
 }
 input.ace + .lbl , input.ace ~ .lbl {//for ASP.NET too
	 .checkbox-paddings-rtl() {// a little paddings for .lbl
		 .checkbox-paddingX-rtl (@index) when (@index >= 0) {
			&.padding-@{index}::before {			
				margin-left: unit(@index,px);
			}
		   .checkbox-paddingX-rtl(@index - 4);
		  }
		  .checkbox-paddingX-rtl(16);
	 }
	 .checkbox-paddings-rtl();
 }


 .breadcrumb > li + li:before {
	content: "\f104";
	float: right;//
 }
 
 &.no-skin .sidebar.menu-min .nav-list > li.active > a > .menu-text {
	border-left-color: #A4C6DD;
 }
 
 .nav-list, .dropdown-menu, .item-list, .navbar-nav {
	margin: 0;
 }
 


 
 .ace-file-input .ace-file-container.selected {
	right: 0;
 }
 .ace-file-multiple .ace-file-container .ace-file-name {
	padding: 0;
 }
 .ui-slider-small .ui-slider-handle {
	right: auto;
 }


 li[class*="item-"] {
	border-left:1px solid #DDDDDD;
 }
 .itemdiv.dialogdiv > .body {
    margin-left: 12px;
    margin-right: 50px;
	border-left-width: 1px;
	
	&:before {
		.transform(rotate(45deg));
	}
 }


 .dropdown-colorpicker .dropdown-menu > li {
	float: right;
 }


 &.no-skin .sidebar.menu-min .nav-list > li.active > .submenu,
 &.no-skin .sidebar.menu-min .nav-list > li.active > a > .menu-text {
	border-left-color: #CCC;
 }
 &.no-skin .sidebar.menu-min .nav-list > li.active > .submenu,
 &.skin-3 .sidebar.menu-min .nav-list > li.active > a > .menu-text {
	border-left-color: #A4C6DD;
 }

 @media (min-width: max(@screen-fixed-breadcrumbs , @screen-compact-menu)) {
  .sidebar.menu-min, .sidebar.compact {
	+ .main-content .breadcrumbs-fixed {
		left: 0;
	}
  }
 }


 @media (min-width: @screen-hover-menu) and (max-width: @grid-float-breakpoint-max) {
  .sidebar.navbar-collapse .nav-list > li > .submenu li.hover > .submenu > li > a,
  .sidebar.navbar-collapse .nav-list > li > .submenu li > .submenu > li.hover > .submenu > li > a {
	margin-right: 20px !important;
  }
 }

 /**
 .navbar-fixed-top + .main-container {
	padding-top: @navbar-min-height;
 }
 @media (max-width: @screen-topbar-down) {
	.navbar-fixed-top + .main-container {
		padding-top: (@navbar-min-height * 2);
	}
	.navbar-fixed-top.navbar-collapse + .main-container {
		padding-top: @navbar-min-height;
	}
 }
 */

 @media (min-width: @screen-fixed-breadcrumbs) and (max-width: @grid-float-breakpoint-max) {
	.breadcrumbs-fixed, .sidebar.menu-min + .main-content .breadcrumbs-fixed {
		left: 0 !important;
	}
	.container.main-container {
		.breadcrumbs-fixed, .sidebar.menu-min + .main-content .breadcrumbs-fixed {
			left: auto !important;
		}
	}
 }

 @media (max-width: @screen-xs-max) and (min-width: @screen-fixed-breadcrumbs) {
	.container.main-container  {
		.breadcrumbs-fixed, .sidebar.menu-min + .main-content .breadcrumbs-fixed {
			left: 0 !important;
		}
	}
 }

 
 //if we don't do this, .arrow pointer will be a bit round in FireFox
 .sidebar:not(.h-sidebar) .nav-list > li.highlight.active > a {
	&:after {
		border-width: 20px 10px 21px !important;
		left: -20px !important;
	}
	&:before {
		border-width: 20px 10px 21px !important;
		left: -21px !important;
	}
 }
 @media (min-width: @grid-float-breakpoint) {
	&.no-skin .sidebar.h-sidebar .nav-list > li.active > .submenu {
		border-color: #CCC !important;
	}
	&.skin-3 .sidebar.h-sidebar .nav-list > li.active > .submenu {
		border-color: #A4C6DD !important;
	}

 }
 
 
 //the extra submenu dot
 .sidebar.menu-min .nav-list > li > .submenu:after {
	border-left-width: 0;
	border-right: 1px solid;
	border-right-color: inherit;

	display: block;
	right: -1px;
	left: auto;
 }


 
 .popover.bottom .arrow:after,
 .popover.top .arrow:after {
	margin-left: auto;
	margin-right: -10px;
 }
 
 
 .nav-tabs {
	margin: 0;
 }
 .tabs-below > .nav-tabs {
	margin-top: -1px;
 }
 .nav-tabs > li {
	float: right;
 }
 .nav-tabs > li:first-child > a {
	margin-left: -1px;
    margin-right: auto;
 }
 
 .tabs-left > .nav-tabs > li:first-child > a {
	margin-right: -1px;
	margin-left: auto;
 }
 .tabs-left > .nav-tabs > li.active > a {
	margin-left: -1px;
	margin-right: -1px;
 }
 .tabs-right > .nav-tabs > li.active > a {
	margin-left: -1px;
	margin-right: -2px;
 }
 
 .nav-tabs[class*="tab-color-"] > li > a {
	margin-right: 3px;
	margin-left: auto;
 }

 .nav-stacked > li {
	float: none;
 }
 .dropdown-navbar .nav-tabs > li > a:before {
	left: auto;
	right: 0;
 }


 .nav.nav-tabs {
	 .navtab-paddingX-rtl (@index) when (@index > 0) {
		&.padding-@{index} { padding-left: 0; padding-right: unit(@index,px); }
		.tabs-right > &.padding-@{index} , .tabs-left > &.padding-@{index} { padding-right:0; }
		.navtab-paddingX-rtl(@index - 2);
	  }
	  .navtab-paddingX-rtl(32);
 }
 
 
 //page.inbox.less
 .message-navbar .messagebar-item-left ~ .nav-search {
   left: auto;
 }
 .message-navbar .messagebar-item-right ~ .nav-search {
   left: auto;
   right: 5px;
 }
 .message-navbar .messagebar-item-right ~ .nav-search {
   right: 60px;
 }
 
 
 
 
 
.enable_navbar_dropdown_positions_rtl() when(@enable-navbar-dropdowns = true) {


@media only screen and (max-width: @screen-xs-max) {
 .ace-nav > li:nth-last-child(4) > .dropdown-menu {
	right: auto;
	left: -100px;
	&:before, &:after {
		right: auto;
		left: 120px;
	}
 }

 .ace-nav > li:nth-last-child(3) > .dropdown-menu {
	right: auto;
	left: -80px;
	&:before, &:after {
		right: auto;
		left: 100px;
	}
 }

 .user-menu.dropdown-close {
	right: auto !important;
	left: 0 !important;
 }
}


@media only screen and (min-width: @screen-topbar-down-min) and (max-width: @screen-xs-max) {
  //if there's only one item except for user_info
 .navbar.navbar-collapse {
  .ace-nav > li:nth-last-child(2):nth-child(1) > .dropdown-menu,
  .ace-nav > li:nth-last-child(2):nth-child(2) > .dropdown-menu {
	left: -60px;
	right: auto;

	&:before, &:after {
		right: auto;
		left: 80px;
	}
  }
 }
}


@media only screen and (max-width: @screen-xs) {
 .ace-nav > li:nth-last-child(4) > .dropdown-menu {
	right: auto;
	left: -120px;
	
	&:before, &:after {
		right: auto;
		left: 140px;
	}
 } 
 .ace-nav > li:nth-last-child(3) > .dropdown-menu {
	right: auto;
	left: -120px;
	
	&:before, &:after {
		right: auto;
		left: 110px;
	}
 }
 
 .ace-nav > li:nth-last-child(2) > .dropdown-menu {
	right: auto;
	left: -50px;

	&:before, &:after {
		right: auto;
		left: 70px;
	}
 }
}


@media only screen and (max-width: @screen-topbar-down) {
 .ace-nav > li:nth-last-child(4) > .dropdown-menu {
	left: auto;
	right: -10px;

	&:before, &:after {
		right: 30px;
		left: auto;
	}
 }

 .ace-nav > li:nth-last-child(3) > .dropdown-menu {
	left: auto;
	right: -50px;
	
	&:before, &:after {
		left: auto;
		right: 75px;
	}
 }

 .ace-nav > li:nth-last-child(2) > .dropdown-menu {
	left: auto;
	right: -70px;
	
	&:before, &:after {
		left: auto;
		right: 90px;
	}
 }
}

@media only screen and (max-width: @screen-mini-max) {
  //when there are only two items (apart from user menu)
 .ace-nav > li:nth-last-child(2) > .dropdown-menu {
	left: auto;
	right: -110px;

	&:before, &:after {
		left: auto;
		right: 130px;	
	}
 }

 .ace-nav > li:nth-child(2):nth-last-child(2) > .dropdown-menu {
	left: auto;
	right: -85px;

	&:before, &:after {
		left: auto;
		right: 105px;
	}
 }

 .ace-nav > li:nth-child(1):nth-last-child(3) > .dropdown-menu {
	left: auto;
	right: -35px;

	&:before, &:after {
		left: auto;
		right: 55px;
	}
 }

 //when there is only one item (apart from user menu)
 .ace-nav > li:nth-child(1):nth-last-child(2) > .dropdown-menu {
	left: auto;
	right: -60px;

	&:before, &:after {
		left: auto;
		right: 75px;
	}
 }
}

}
.enable_navbar_dropdown_positions_rtl();

 

 .btn-group-vertical > .btn,
 .btn-group-vertical > .btn-group,
 .btn-group-vertical > .btn-group > .btn {
	float: none;
 }
 
 .input-group-addon:first-child {
	border-right: 1px solid #CCC;
 }
 .input-group-addon:last-child {
	border-left: 1px solid #CCC;
 }
 

 
 
 
 //footer
 .enable_footer_rtl() when (@enable-footer = true) {
 .footer .footer-inner {
	right: 0;
	left: 0;
 }
 .sidebar ~ .footer .footer-inner {
	right: @sidebar-width;
	left: 0;
 }

 @media (min-width: @screen-compact-menu) {
  .sidebar.compact ~ .footer .footer-inner {
	right: @sidebar-compact-width;
	left: 0;
  }
 }
 .sidebar.menu-min ~ .footer .footer-inner {
	right: @sidebar-min-width;
	left: 0;
 }
 @media (min-width: @grid-float-breakpoint) {
   .sidebar.h-sidebar ~ .footer .footer-inner {
	  right: 0;
	  left: 0;
   }
 }
 @media (max-width: @grid-float-breakpoint-max) {
	.footer .footer-inner , .sidebar ~ .footer .footer-inner, .sidebar.compact ~ .footer .footer-inner , .sidebar.menu-min ~ .footer .footer-inner {
		right: 0;
		left: 0;
	}
	.sidebar.responsive-min ~ .footer .footer-inner {
		right: @sidebar-min-width;
		left: 0;
	}
 }


 .enable_container_footer_rtl() when(@enable-container = true) {
	 .container.main-container .footer .footer-inner {
		 @media (min-width: @screen-sm-min) and (max-width: @grid-float-breakpoint-max) {
			margin-right: auto;
		 }
		 @media (min-width: @screen-md-min) {
			margin-left: auto;
			margin-right: @sidebar-width;
		 }
		 @media (min-width: @screen-lg-min) {
			margin-left: auto;
			margin-right: @sidebar-width;
		 }
	 }
	 
	 .enable_compact_menu_footer_container_rtl() when (@enable-compact-menu = true) {
	   .container.main-container .sidebar.compact ~ .footer .footer-inner {
		 @media (min-width: max(@screen-compact-menu, @screen-md-min)) {
			margin-left: auto;
			margin-right: @sidebar-compact-width;
		 }
		 @media (min-width: max(@screen-compact-menu, @screen-lg-min)) {
			margin-left: auto;
			margin-right: @sidebar-compact-width;
		 }
	   }
	 }
	 .enable_compact_menu_footer_container_rtl();
	 
	 
	 .enable_collapse_menu_footer_container_rtl() when (@enable-sidebar-collapse = true) {
	  .container.main-container .sidebar.menu-min ~ .footer .footer-inner {
		 @media (min-width: @screen-md-min) {
			margin-left: auto;
			margin-right: @sidebar-min-width;
		 }
		 @media (min-width: @screen-lg-min) {
			margin-left: auto;
			margin-right: @sidebar-min-width;
		 }
	  }
	 }
	 .enable_collapse_menu_footer_container_rtl();
	 
	 
	 .enable_horizontal_menu_footer_container_rtl() when (@enable-horizontal-menu = true) {
	  .container.main-container .sidebar.h-sidebar ~ .footer .footer-inner {
		 @media (min-width: @grid-float-breakpoint) {
			margin-right: 0;
		 }
		 @media (min-width: @screen-md-min) {
			margin-right: 0;
		 }
		 @media (min-width: @screen-lg-min) {
			margin-right: 0;
		 }
	  }
	 }
	 .enable_horizontal_menu_footer_container_rtl();

	 
	 .enable_responsive_min_menu_footer_container_rtl() when (@enable-minimized-responsive-menu = true) {
	  .container.main-container .sidebar.responsive-min ~ .footer .footer-inner {
	      @media (min-width: @screen-sm-min) and (max-width: @grid-float-breakpoint-max) {
			 margin-right: @sidebar-min-width;
			 margin-left: auto;
		  }
	  }
	 }
	 .enable_responsive_min_menu_footer_container_rtl();
	 
 }
 .enable_container_footer_rtl();
 
 
 
 .enable_footer_responsive_menu_push_rtl() when (@enable-responsive-menu = true) {
    @media only screen and (max-width: @grid-float-breakpoint-max) and (min-width: @screen-compact-menu) {
		.navbar.navbar-fixed-top + .main-container .sidebar.responsive.push_away {
			&.display.compact ~ .footer .footer-inner {
				.transform(translateX(-@sidebar-compact-width));
			}
		}
	}
 
   @media only screen and (max-width: @grid-float-breakpoint-max) {
	  .navbar.navbar-fixed-top + .main-container .sidebar.responsive.push_away {

		&.display ~ .footer .footer-inner {
			.transform(translateX(-@sidebar-width));
		}
		&.display.menu-min ~ .footer .footer-inner {
			.transform(translateX(-@sidebar-min-width));
		}

	  }
	}

 }
 .enable_footer_responsive_menu_push_rtl();

}
.enable_footer_rtl();
 
 
 
 
 
 
 
 
 
 
 //from previous version's RTL file
  blockquote {
	p , small {
		text-align:left;
	}
	small:before {
		content: "";
	}
	small:after {
		content: "\00A0 \2014";
	}
 }
 blockquote.pull-right, .blockquote-reverse {
	p , small {
		text-align:right;
	}
	small:after {
		content: "";
	}
	small:before {
		content: "\2014 \00A0";
	}
 }
 
 
 
 


 //thirdparty-calendar.less
 .fc-grid th {
    text-align: center;
 }
 .external-event {
	> .@{icon}:first-child {
		margin-right: 0;
		margin-left: 5px;

		border-right-width: 0;
		border-left: 1px solid #FFF;
	}  
 }
 
 //thirdparty-colorbox.less
 #cboxCurrent {
	left: auto;
	right: 64px;
 }
 #cboxNext , #cboxPrevious {
	margin-left: 0;
	margin-right: 5px;
 }
 #cboxPrevious {
	left: auto;
	right: 27px;
 }
 #cboxNext {
	left: auto;
	right: 0;
 }
 
 //thirdparty-fuelux.less
 .ace-spinner .spinner-buttons > button.btn:active { left:auto; top:auto; }

 .wizard-steps {
	margin-right: 0;
 }
 .wizard-actions {
	text-align: left;
 }
 .wizard-steps li:first-child:before {
	right: 50%;
	left: auto;
 }
 
 .tree {
	padding-left: 0;
	padding-right: 9px;
	&:before {
		left: auto;
		right: 0;
		border-width: 0 1px 0 0;
	}
	
	.tree-folder {
		.tree-folder-header {
			.tree-folder-name  {
				margin-left: 0;
				margin-right: 2px;
			}
			> .@{icon}:first-child {
				margin: -2px -2px 0 0;
			}
		}
		&:last-child:after {
			left: auto;
			right: -15px;
			border-left: none;
			border-right: 1px solid #FFF;
		}
		.tree-folder-content {
			margin-left: 0;
			margin-right: 23px;
			&:before {
				left: auto;
				right: -14px;
				border-width: 0 1px 0 0;
			}
		}
	}
	
	.tree-item {
		.tree-item-name {
			margin-left: 0;
			margin-right: 3px;
			> .@{icon}:first-child {
				margin-right: 0;
				margin-left: 3px;
			}
		}
	}
	.tree-folder , .tree-item {
		&:before {
			left: auto;
			right: -13px;
		}
	}
	.tree-loading {
		margin-left: 0;
		margin-right: 36px;
	}
 }
 
 
 //thirdpart-gritter.less
 #gritter-notice-wrapper {
	left: 20px;
	right:auto;
 }
 .gritter-close {
	right: auto;
	left: 3px;
 }
 .gritter-image {
	float: right;
 }
 .gritter-with-image , .gritter-without-image {
	float: left;
 }

 //thirdparty-wysiwyg.less
 .wysiwyg-toolbar {
	.dropdown-menu {
		text-align: right;
	}
	.wysiwyg-choose-file {
		margin-left: auto;
	}
	.btn-group > .btn, .btn-group > .inline > .btn {
		float: none;
	}
 }
 .wysiwyg-style1 , .wysiwyg-style2 {
	.btn-group:after{
		left: auto;
		border-left-width: 0;
		right: -2px;
		border-right: 1px solid #E1E6EA;
	}
 }
 .wysiwyg-toolbar {
	.dropdown-menu {
		input[type=text] {
			margin-left: 0;
			margin-right: 8px;
		}
		.btn {
			margin-right: 1px;
			margin-left: 8px;
		}
	}
 }
 .widget-body .md-header {
	margin-left: 0;
	margin-right: 9px;
	.btn-inverse {
		padding-right: 0;
		padding-left: 5px;
	}
 }
 
 
 .enable_plugin_select2_rtl() when(@enable-plugin-select2 = true) {
 //thirdparty-select2.less
 .select2-container .select2-choice {
	padding-left: 0;
	padding-right: 8px;
 }
 .select2-container.select2-allowclear .select2-choice .select2-chosen {
	margin-right: auto;
	margin-left: 42px;
 }

 .select2-container .select2-choice > .select2-chosen {
	margin-left: 26px;
	margin-right: auto;
 }
 .select2-container .select2-choice abbr {
	right: auto;
	left: 20px;
 }
 .select2-container .select2-choice .select2-arrow {
	right: auto;
	left: 0;
 }
 .select2-container .select2-choice .select2-arrow b:before {
	right: 5px;
	left: auto;
 }
 
 .select2-container-multi .select2-choices li {
	float: right;
 }
 .select2-container-multi .select2-choices .select2-search-choice {
	margin: 3px 5px 3px 0;
	padding: 3px 18px 3px 5px;
 }
 
 .select2-results {
	margin: 4px 0 4px 4px;
 }
 
 .select2-drop {
	input {
		padding-right: 5px;
		padding-left: 20px;
	}
	.select2-results {
		padding-right: 4px;
		padding-left: 0;
	}
 }
 
 .select2-search:after {
	right: -20px;
	left: auto;
 }
 .select2-search input.select2-active {
	background-position: 0%;
 }
 }
 .enable_plugin_select2_rtl();

 //thirdparty-editable
 .editable-buttons {
	margin-left: auto;
	margin-right: 1px;
	.btn {
		margin: 0 0 0 1px;
	}
 }

 //thirdparty-jquery-ui.less
 .ui-datepicker .ui-datepicker-prev:before {
	content: "\f061";
 }
 .ui-datepicker .ui-datepicker-next:before {
	content: "\f060";
 }
 .ui-menu .ui-menu-item a .ui-menu-icon {
	float: left;
	&:before {
		content: "\f104";
	}
 }
 .ui-dialog .ui-dialog-titlebar-close, .ui-jqdialog .ui-jqdialog-titlebar-close {
	left: 8px !important;
	right: auto !important;
 }

 .ui-tabs .ui-tabs-nav li {
	float: right;
	margin-right: 0;
	margin-left: 0.2em;
	a {
		float: right;
	}
 }
 .ui-tabs .ui-tabs-nav li.ui-state-default > a {
	margin-right: auto;
	margin-left: -1px;
 }
 .ui-accordion .ui-accordion-header {
	padding-right: 24px;
	padding-left: 8px;
	
	.ui-accordion-header-icon {
		position: absolute;
		left: auto;
		right: 10px;
		&:before {
			 content: "\f0d9";
		}
	}
	&.ui-state-active .ui-accordion-header-icon:before {
		content: "\f0d7";
	}
 }

 //thirdparty-jqgrid.less
 .ui-jqgrid .ui-jqgrid-hdiv {
	border-width: 1px 1px 0 0;
 }
 .ui-jqgrid .ui-jqgrid-labels {
	th {
		border-right-width:0 !important;
		border-left: 1px solid #E1E1E1 !important;
		text-align: right !important;
		&:first-child {
			border-right: 1px solid #E1E1E1 !important;
		}
	}
 }
 .ui-jqgrid-labels th[id*="_cb"]:first-child {
	text-align: center !important;
 }
 .ui-jqgrid-sortable {
	padding-left: 0;
	padding-right: 4px;
 }
 .ui-jqdialog-content .searchFilter table {
	margin-left: auto;
	margin-right: 4px;
 }
 .ui-jqdialog-content .searchFilter {
	.add-group, .add-rule, .delete-group {
		margin-left: auto !important;
		margin-right: 4px !important;
	}
 }
 .ui-jqdialog-content {
	.CaptionTD {
		text-align: left;
	}
 }
 .ui-jqdialog .ui-widget-header{
	.ui-jqdialog-title {
		text-align: right;
		padding-left: 0;
		padding-right: 12px;
		float: right !important;
	}
 }
 
 
 //thirdparty-nestable.less
 .dd-list {
	margin-right: 0;

	.dd-list {
		padding-right: 30px;
		padding-left: 0;
	}	
 }
 .dd2-handle  + .dd2-content,
 .dd2-handle  + .dd2-content[class*="btn-"]
 {
	padding-left: 0;
	padding-right: 44px;
 }
 .dd-item > button {
	float: right;
	margin: 5px 5px 5px 1px;
	left: auto;
	right: 1px;
 }
 .dd2-item.dd-item > button {
	margin-left: 5px;
	margin-right: 34px;
 }
 .dd-dragel {
	> li > .dd-handle {
		border-right: 2px solid #777;
		border-left-width: 0;
	}
 }
.dd-list > li[class*="item-"] {
  border-left-width: 0;
  border-right-width: 0;
  
  > .dd-handle {
	border-right: 2px solid;
	border-right-color: inherit;
	border-left-color: #DAE2EA;
	border-left-width: 1px;
 }
}
.dd-list > li > .dd-handle .sticker {
	right: auto;
	left: 0;
}
.dd2-handle , .dd-dragel > li > .dd2-handle {
	left: auto;
	right: 0;
	border-width: 1px 0 0 1px;
}

 
 //pretty print
 ol.linenums {
	margin-right: 33px;
	li {
		padding-left: 0;
		padding-right: 12px;
	}
 }
 .prettyprint.linenums {
	.box-shadow(~"-40px 0 0 #FBFBFC inset, -41px 0 0 #ECECF0 inset");
 }

 
 
 .tt-dropdown-menu {
	text-align: right;
	direction: rtl;
 }

 
}