(function(B){function F(){var H=document.createElement("input"),I="onpaste";return H.setAttribute(I,""),"function"==typeof H[I]?"paste":"input"}var C,G=F()+".mask",A=navigator.userAgent,D=/iphone/i.test(A),E=/android/i.test(A);B.mask={definitions:{9:"[0-9]",a:"[A-Za-z]","*":"[A-Za-z0-9]"},dataName:"rawMaskFn",placeholder:"_"},B.fn.extend({caret:function(H,J){var I;if(0!==this.length&&!this.is(":hidden")){return"number"==typeof H?(J="number"==typeof J?J:H,this.each(function(){this.setSelectionRange?this.setSelectionRange(H,J):this.createTextRange&&(I=this.createTextRange(),I.collapse(!0),<PERSON><PERSON>moveEnd("character",J),I.moveStart("character",H),<PERSON><PERSON>select())})):(this[0].setSelectionRange?(H=this[0].selectionStart,J=this[0].selectionEnd):document.selection&&document.selection.createRange&&(I=document.selection.createRange(),H=0-I.duplicate().moveStart("character",-100000),J=H+I.text.length),{begin:H,end:J})}},unmask:function(){return this.trigger("unmask")},mask:function(J,H){var N,M,I,K,O,L;return !J&&this.length>0?(N=B(this[0]),N.data(B.mask.dataName)()):(H=B.extend({placeholder:B.mask.placeholder,completed:null},H),M=B.mask.definitions,I=[],K=L=J.length,O=null,B.each(J.split(""),function(P,Q){"?"==Q?(L--,K=P):M[Q]?(I.push(RegExp(M[Q])),null===O&&(O=I.length-1)):I.push(null)}),this.trigger("unmask").each(function(){function X(R){for(;L>++R&&!I[R];){}return R}function f(R){for(;--R>=0&&!I[R];){}return R}function V(R,b){var S,c;if(!(0>R)){for(S=R,c=X(b);L>S;S++){if(I[S]){if(!(L>c&&I[S].test(U[c]))){break}U[S]=U[c],U[c]=H.placeholder,c=X(c)}}W(),a.caret(Math.max(O,R))}}function P(S){var c,b,d,R;for(c=S,b=H.placeholder;L>c;c++){if(I[c]){if(d=X(c),R=U[c],U[c]=b,!(L>d&&I[d].test(R))){break}b=R}}}function e(S){var c,b,d,R=S.which;8===R||46===R||D&&127===R?(c=a.caret(),b=c.begin,d=c.end,0===d-b&&(b=46!==R?f(b):d=X(b-1),d=46===R?X(d):d),T(b,d),V(b,d-1),S.preventDefault()):27==R&&(a.val(Z),a.caret(0,Y()),S.preventDefault())}function Q(c){var S,g,R,b=c.which,d=a.caret();c.ctrlKey||c.altKey||c.metaKey||32>b||b&&(0!==d.end-d.begin&&(T(d.begin,d.end),V(d.begin,d.end-1)),S=X(d.begin-1),L>S&&(g=String.fromCharCode(b),I[S].test(g)&&(P(S),U[S]=g,W(),R=X(S),E?setTimeout(B.proxy(B.fn.caret,a,R),0):a.caret(R),H.completed&&R>=L&&H.completed.call(a))),c.preventDefault())}function T(R,b){var S;for(S=R;b>S&&L>S;S++){I[S]&&(U[S]=H.placeholder)}}function W(){a.val(U.join(""))}function Y(S){var c,b,d=a.val(),R=-1;for(c=0,pos=0;L>c;c++){if(I[c]){for(U[c]=H.placeholder;pos++<d.length;){if(b=d.charAt(pos-1),I[c].test(b)){U[c]=b,R=c;break}}if(pos>d.length){break}}else{U[c]===d.charAt(pos)&&c!==K&&(pos++,R=c)}}return S?W():K>R+1?(a.val(""),T(0,L)):(W(),a.val(a.val().substring(0,R+1))),K?c:O}var a=B(this),U=B.map(J.split(""),function(R){return"?"!=R?M[R]?H.placeholder:R:void 0}),Z=a.val();a.data(B.mask.dataName,function(){return B.map(U,function(R,S){return I[S]&&R!=H.placeholder?R:null}).join("")}),a.attr("readonly")||a.one("unmask",function(){a.unbind(".mask").removeData(B.mask.dataName)}).bind("focus.mask",function(){clearTimeout(C);var R;Z=a.val(),R=Y(),C=setTimeout(function(){W(),R==J.length?a.caret(0,R):a.caret(R)},10)}).bind("blur.mask",function(){Y(),a.val()!=Z&&a.change()}).bind("keydown.mask",e).bind("keypress.mask",Q).bind(G,function(){setTimeout(function(){var R=Y(!0);a.caret(R),H.completed&&R==a.val().length&&H.completed.call(a)},0)}),Y()}))}})})(jQuery);