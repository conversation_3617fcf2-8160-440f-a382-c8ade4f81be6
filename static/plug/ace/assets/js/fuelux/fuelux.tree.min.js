(function(D,B){var A=D.fn.tree;var C=function(F,E){this.$element=D(F);this.options=D.extend({},D.fn.tree.defaults,E);this.$element.on("click",".tree-item",D.proxy(function(G){this.selectItem(G.currentTarget)},this));this.$element.on("click",".tree-folder-header",D.proxy(function(G){this.selectFolder(G.currentTarget)},this));this.render()};C.prototype={constructor:C,render:function(){this.populate(this.$element)},populate:function(H){var F=this;var E=H.parent();var G=E.find(".tree-loader:eq(0)");G.show();this.options.dataSource.data(H.data(),function(I){G.hide();D.each(I.data,function(M,J){var K;if(J.type==="folder"){K=F.$element.find(".tree-folder:eq(0)").clone().show();K.find(".tree-folder-name").html(J.name);K.find(".tree-loader").html(F.options.loadingHTML);var L=K.find(".tree-folder-header");L.data(J);if("icon-class" in J){L.find("i").addClass(J["icon-class"])}if("additionalParameters" in J&&"item-selected" in J.additionalParameters&&J.additionalParameters["item-selected"]==true){setTimeout(function(){L.trigger("click")},0)}}else{if(J.type==="item"){K=F.$element.find(".tree-item:eq(0)").clone().show();K.find(".tree-item-name").html(J.name);K.data(J);if("additionalParameters" in J&&"item-selected" in J.additionalParameters&&J.additionalParameters["item-selected"]==true){K.addClass("tree-selected");K.find("i").removeClass(F.options["unselected-icon"]).addClass(F.options["selected-icon"])}}}var N=J.dataAttributes||[];D.each(N,function(O,P){switch(O){case"class":case"classes":case"className":K.addClass(P);break;default:K.attr(O,P);break}});if(H.hasClass("tree-folder-header")){E.find(".tree-folder-content:eq(0)").append(K)}else{H.append(K)}});F.$element.trigger("loaded",E)})},selectItem:function(E){if(this.options["selectable"]==false){return}var H=D(E);var G=this.$element.find(".tree-selected");var I=[];if(this.options.multiSelect){D.each(G,function(L,J){var K=D(J);if(K[0]!==H[0]){I.push(D(J).data())}})}else{if(G[0]!==H[0]){G.removeClass("tree-selected").find("i").removeClass("icon-ok").addClass("tree-dot");I.push(H.data())}}var F="selected";if(H.hasClass("tree-selected")){F="unselected";H.removeClass("tree-selected");H.find("i").removeClass("icon-ok").addClass("tree-dot")}else{H.addClass("tree-selected");H.find("i").removeClass("tree-dot").addClass("icon-ok");if(this.options.multiSelect){I.push(H.data())}}if(I.length){this.$element.trigger("selected",{info:I})}H.trigger("updated",{info:I,item:H,eventType:F})},selectFolder:function(H){var M=D(H);var E=M.parent();var F=E.find(".tree-folder-content");var G=F.eq(0);var J="."+D.trim(this.options["close-icon"].replace(/\s/g,"."));var K,L,I;if(M.find(J).length){K="opened";L=this.options["close-icon"];I=this.options["open-icon"];G.show();if(!F.children().length){this.populate(M)}}else{J="."+D.trim(this.options["open-icon"].replace(/\s/g,"."));K="closed";L=this.options["open-icon"];I=this.options["close-icon"];G.hide();if(!this.options.cacheItems){G.empty()}}E.find(J).eq(0).removeClass(L).addClass(I);this.$element.trigger(K,M.data())},selectedItems:function(){var E=this.$element.find(".tree-selected");var F=[];D.each(E,function(H,G){F.push(D(G).data())});return F},collapse:function(){var E=this.options.cacheItems;this.$element.find(".icon-folder-open").each(function(){var G=D(this).removeClass("icon-folder-close icon-folder-open").addClass("icon-folder-close");var F=G.parent().parent();var H=F.children(".tree-folder-content");H.hide();if(!E){H.empty()}})}};D.fn.tree=function(E){var H=Array.prototype.slice.call(arguments,1);var F;var G=this.each(function(){var I=D(this);var K=I.data("tree");var J=typeof E==="object"&&E;if(!K){I.data("tree",K=new C(this,J))}if(typeof E==="string"){F=K[E].apply(K,H)}});return F===B?G:F};D.fn.tree.defaults={multiSelect:false,loadingHTML:"<div>Loading...</div>",cacheItems:true};D.fn.tree.Constructor=C;D.fn.tree.noConflict=function(){D.fn.tree=A;return this}})(window.jQuery);