if(!document.createElement("canvas").getContext){(function(){var m=Math;var AN=m.round;var AP=m.sin;var Av=m.cos;var An=m.abs;var Ap=m.sqrt;var AV=10;var AT=AV/2;var AA=+navigator.userAgent.match(/MSIE ([\d.]+)?/)[1];function AC(){return this.context_||(this.context_=new Ay(this))}var AJ=Array.prototype.slice;function AU(D,C,B){var A=AJ.call(arguments,2);return function(){return D.apply(C,A.concat(AJ.call(arguments)))}}function i(A){return String(A).replace(/&/g,"&amp;").replace(/"/g,"&quot;")}function Ac(B,C,A){if(!B.namespaces[C]){B.namespaces.add(C,A,"#default#VML")}}function Ad(B){Ac(B,"g_vml_","urn:schemas-microsoft-com:vml");Ac(B,"g_o_","urn:schemas-microsoft-com:office:office");if(!B.styleSheets.ex_canvas_){var A=B.createStyleSheet();A.owningElement.id="ex_canvas_";A.cssText="canvas{display:inline-block;overflow:hidden;text-align:left;width:300px;height:150px}"}}Ad(document);var Z={init:function(A){var B=A||document;B.createElement("canvas");B.attachEvent("onreadystatechange",AU(this.init_,this,B))},init_:function(A){var B=A.getElementsByTagName("canvas");for(var C=0;C<B.length;C++){this.initElement(B[C])}},initElement:function(B){if(!B.getContext){B.getContext=AC;Ad(B.ownerDocument);B.innerHTML="";B.attachEvent("onpropertychange",AB);B.attachEvent("onresize",Ai);var A=B.attributes;if(A.width&&A.width.specified){B.style.width=A.width.nodeValue+"px"}else{B.width=B.clientWidth}if(A.height&&A.height.specified){B.style.height=A.height.nodeValue+"px"}else{B.height=B.clientHeight}}return B}};function AB(B){var A=B.srcElement;switch(B.propertyName){case"width":A.getContext().clearRect();A.style.width=A.attributes.width.nodeValue+"px";A.firstChild.style.width=A.clientWidth+"px";break;case"height":A.getContext().clearRect();A.style.height=A.attributes.height.nodeValue+"px";A.firstChild.style.height=A.clientHeight+"px";break}}function Ai(B){var A=B.srcElement;if(A.firstChild){A.firstChild.style.width=A.clientWidth+"px";A.firstChild.style.height=A.clientHeight+"px"}}Z.init();var AL=[];for(var p=0;p<16;p++){for(var Aa=0;Aa<16;Aa++){AL[p*16+Aa]=p.toString(16)+Aa.toString(16)}}function At(){return[[1,0,0],[0,1,0],[0,0,1]]}function Al(C,E){var G=At();for(var A=0;A<3;A++){for(var F=0;F<3;F++){var D=0;for(var B=0;B<3;B++){D+=C[A][B]*E[B][F]}G[A][F]=D}}return G}function AH(B,A){A.fillStyle=B.fillStyle;A.lineCap=B.lineCap;A.lineJoin=B.lineJoin;A.lineWidth=B.lineWidth;A.miterLimit=B.miterLimit;A.shadowBlur=B.shadowBlur;A.shadowColor=B.shadowColor;A.shadowOffsetX=B.shadowOffsetX;A.shadowOffsetY=B.shadowOffsetY;A.strokeStyle=B.strokeStyle;A.globalAlpha=B.globalAlpha;A.font=B.font;A.textAlign=B.textAlign;A.textBaseline=B.textBaseline;A.arcScaleX_=B.arcScaleX_;A.arcScaleY_=B.arcScaleY_;A.lineScale_=B.lineScale_}var AQ={aliceblue:"#F0F8FF",antiquewhite:"#FAEBD7",aquamarine:"#7FFFD4",azure:"#F0FFFF",beige:"#F5F5DC",bisque:"#FFE4C4",black:"#000000",blanchedalmond:"#FFEBCD",blueviolet:"#8A2BE2",brown:"#A52A2A",burlywood:"#DEB887",cadetblue:"#5F9EA0",chartreuse:"#7FFF00",chocolate:"#D2691E",coral:"#FF7F50",cornflowerblue:"#6495ED",cornsilk:"#FFF8DC",crimson:"#DC143C",cyan:"#00FFFF",darkblue:"#00008B",darkcyan:"#008B8B",darkgoldenrod:"#B8860B",darkgray:"#A9A9A9",darkgreen:"#006400",darkgrey:"#A9A9A9",darkkhaki:"#BDB76B",darkmagenta:"#8B008B",darkolivegreen:"#556B2F",darkorange:"#FF8C00",darkorchid:"#9932CC",darkred:"#8B0000",darksalmon:"#E9967A",darkseagreen:"#8FBC8F",darkslateblue:"#483D8B",darkslategray:"#2F4F4F",darkslategrey:"#2F4F4F",darkturquoise:"#00CED1",darkviolet:"#9400D3",deeppink:"#FF1493",deepskyblue:"#00BFFF",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1E90FF",firebrick:"#B22222",floralwhite:"#FFFAF0",forestgreen:"#228B22",gainsboro:"#DCDCDC",ghostwhite:"#F8F8FF",gold:"#FFD700",goldenrod:"#DAA520",grey:"#808080",greenyellow:"#ADFF2F",honeydew:"#F0FFF0",hotpink:"#FF69B4",indianred:"#CD5C5C",indigo:"#4B0082",ivory:"#FFFFF0",khaki:"#F0E68C",lavender:"#E6E6FA",lavenderblush:"#FFF0F5",lawngreen:"#7CFC00",lemonchiffon:"#FFFACD",lightblue:"#ADD8E6",lightcoral:"#F08080",lightcyan:"#E0FFFF",lightgoldenrodyellow:"#FAFAD2",lightgreen:"#90EE90",lightgrey:"#D3D3D3",lightpink:"#FFB6C1",lightsalmon:"#FFA07A",lightseagreen:"#20B2AA",lightskyblue:"#87CEFA",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#B0C4DE",lightyellow:"#FFFFE0",limegreen:"#32CD32",linen:"#FAF0E6",magenta:"#FF00FF",mediumaquamarine:"#66CDAA",mediumblue:"#0000CD",mediumorchid:"#BA55D3",mediumpurple:"#9370DB",mediumseagreen:"#3CB371",mediumslateblue:"#7B68EE",mediumspringgreen:"#00FA9A",mediumturquoise:"#48D1CC",mediumvioletred:"#C71585",midnightblue:"#191970",mintcream:"#F5FFFA",mistyrose:"#FFE4E1",moccasin:"#FFE4B5",navajowhite:"#FFDEAD",oldlace:"#FDF5E6",olivedrab:"#6B8E23",orange:"#FFA500",orangered:"#FF4500",orchid:"#DA70D6",palegoldenrod:"#EEE8AA",palegreen:"#98FB98",paleturquoise:"#AFEEEE",palevioletred:"#DB7093",papayawhip:"#FFEFD5",peachpuff:"#FFDAB9",peru:"#CD853F",pink:"#FFC0CB",plum:"#DDA0DD",powderblue:"#B0E0E6",rosybrown:"#BC8F8F",royalblue:"#4169E1",saddlebrown:"#8B4513",salmon:"#FA8072",sandybrown:"#F4A460",seagreen:"#2E8B57",seashell:"#FFF5EE",sienna:"#A0522D",skyblue:"#87CEEB",slateblue:"#6A5ACD",slategray:"#708090",slategrey:"#708090",snow:"#FFFAFA",springgreen:"#00FF7F",steelblue:"#4682B4",tan:"#D2B48C",thistle:"#D8BFD8",tomato:"#FF6347",turquoise:"#40E0D0",violet:"#EE82EE",wheat:"#F5DEB3",whitesmoke:"#F5F5F5",yellowgreen:"#9ACD32"};function As(D){var B=D.indexOf("(",3);var A=D.indexOf(")",B+1);var C=D.substring(B+1,A).split(",");if(C.length!=4||D.charAt(3)!="a"){C[3]=1}return C}function AR(A){return parseFloat(A)/100}function AE(C,B,A){return Math.min(A,Math.max(B,C))}function Ao(H){var E,C,G,D,F,A;D=parseFloat(H[0])/360%360;if(D<0){D++}F=AE(AR(H[1]),0,1);A=AE(AR(H[2]),0,1);if(F==0){E=C=G=A}else{var B=A<0.5?A*(1+F):A+F-A*F;var I=2*A-B;E=AS(I,B,D+1/3);C=AS(I,B,D);G=AS(I,B,D-1/3)}return"#"+AL[Math.floor(E*255)]+AL[Math.floor(C*255)]+AL[Math.floor(G*255)]}function AS(C,A,B){if(B<0){B++}if(B>1){B--}if(6*B<1){return C+(A-C)*6*B}else{if(2*B<1){return A}else{if(3*B<2){return C+(A-C)*(2/3-B)*6}else{return C}}}}var Au={};function Aw(F){if(F in Au){return Au[F]}var A,C=1;F=String(F);if(F.charAt(0)=="#"){A=F}else{if(/^rgb/.test(F)){var B=As(F);var A="#",E;for(var D=0;D<3;D++){if(B[D].indexOf("%")!=-1){E=Math.floor(AR(B[D])*255)}else{E=+B[D]}A+=AL[AE(E,0,255)]}C=+B[3]}else{if(/^hsl/.test(F)){var B=As(F);A=Ao(B);C=B[3]}else{A=AQ[F]||F}}}return Au[F]={color:A,alpha:C}}var AO={style:"normal",variant:"normal",weight:"normal",size:10,family:"sans-serif"};var Ar={};function Az(A){if(Ar[A]){return Ar[A]}var B=document.createElement("div");var C=B.style;try{C.font=A}catch(D){}return Ar[A]={style:C.fontStyle||AO.style,variant:C.fontVariant||AO.variant,weight:C.fontWeight||AO.weight,size:C.fontSize||AO.size,family:C.fontFamily||AO.family}}function AK(D,F){var A={};for(var E in D){A[E]=D[E]}var B=parseFloat(F.currentStyle.fontSize),C=parseFloat(D.size);if(typeof D.size=="number"){A.size=D.size}else{if(D.size.indexOf("px")!=-1){A.size=C}else{if(D.size.indexOf("em")!=-1){A.size=B*C}else{if(D.size.indexOf("%")!=-1){A.size=(B/100)*C}else{if(D.size.indexOf("pt")!=-1){A.size=C/0.75}else{A.size=B}}}}}A.size*=0.981;return A}function AD(A){return A.style+" "+A.variant+" "+A.weight+" "+A.size+"px "+A.family}var AF={butt:"flat",round:"round"};function Ae(A){return AF[A]||"square"}function Ay(A){this.m_=At();this.mStack_=[];this.aStack_=[];this.currentPath_=[];this.strokeStyle="#000";this.fillStyle="#000";this.lineWidth=1;this.lineJoin="miter";this.lineCap="butt";this.miterLimit=AV*1;this.globalAlpha=1;this.font="10px sans-serif";this.textAlign="left";this.textBaseline="alphabetic";this.canvas=A;var C="width:"+A.clientWidth+"px;height:"+A.clientHeight+"px;overflow:hidden;position:absolute";var D=A.ownerDocument.createElement("div");D.style.cssText=C;A.appendChild(D);var B=D.cloneNode(false);B.style.backgroundColor="red";B.style.filter="alpha(opacity=0)";A.appendChild(B);this.element_=D;this.arcScaleX_=1;this.arcScaleY_=1;this.lineScale_=1}var AG=Ay.prototype;AG.clearRect=function(){if(this.textMeasureEl_){this.textMeasureEl_.removeNode(true);this.textMeasureEl_=null}this.element_.innerHTML=""};AG.beginPath=function(){this.currentPath_=[]};AG.moveTo=function(C,A){var B=Ah(this,C,A);this.currentPath_.push({type:"moveTo",x:B.x,y:B.y});this.currentX_=B.x;this.currentY_=B.y};AG.lineTo=function(C,A){var B=Ah(this,C,A);this.currentPath_.push({type:"lineTo",x:B.x,y:B.y});this.currentX_=B.x;this.currentY_=B.y};AG.bezierCurveTo=function(H,D,G,C,A,I){var E=Ah(this,A,I);var F=Ah(this,H,D);var B=Ah(this,G,C);Am(this,F,B,E)};function Am(A,B,C,D){A.currentPath_.push({type:"bezierCurveTo",cp1x:B.x,cp1y:B.y,cp2x:C.x,cp2y:C.y,x:D.x,y:D.y});A.currentX_=D.x;A.currentY_=D.y}AG.quadraticCurveTo=function(B,F,C,D){var E=Ah(this,B,F);var H=Ah(this,C,D);var G={x:this.currentX_+2/3*(E.x-this.currentX_),y:this.currentY_+2/3*(E.y-this.currentY_)};var A={x:G.x+(H.x-this.currentX_)/3,y:G.y+(H.y-this.currentY_)/3};Am(this,G,A,H)};AG.arc=function(D,E,L,N,F,M){L*=AV;var A=M?"at":"wa";var G=D+Av(N)*L-AT;var K=E+AP(N)*L-AT;var I=D+Av(F)*L-AT;var J=E+AP(F)*L-AT;if(G==I&&!M){G+=0.125}var H=Ah(this,D,E);var C=Ah(this,G,K);var B=Ah(this,I,J);this.currentPath_.push({type:A,x:H.x,y:H.y,radius:L,xStart:C.x,yStart:C.y,xEnd:B.x,yEnd:B.y})};AG.rect=function(C,D,A,B){this.moveTo(C,D);this.lineTo(C+A,D);this.lineTo(C+A,D+B);this.lineTo(C,D+B);this.closePath()};AG.strokeRect=function(D,E,A,B){var C=this.currentPath_;this.beginPath();this.moveTo(D,E);this.lineTo(D+A,E);this.lineTo(D+A,E+B);this.lineTo(D,E+B);this.closePath();this.stroke();this.currentPath_=C};AG.fillRect=function(D,E,A,B){var C=this.currentPath_;this.beginPath();this.moveTo(D,E);this.lineTo(D+A,E);this.lineTo(D+A,E+B);this.lineTo(D,E+B);this.closePath();this.fill();this.currentPath_=C};AG.createLinearGradient=function(E,B,A,D){var C=new Ak("gradient");C.x0_=E;C.y0_=B;C.x1_=A;C.y1_=D;return C};AG.createRadialGradient=function(B,E,D,F,C,A){var G=new Ak("gradientradial");G.x0_=B;G.y0_=E;G.r0_=D;G.x1_=F;G.y1_=C;G.r1_=A;return G};AG.drawImage=function(J,X){var N,M,T,P,K,R,F,B;var A=J.runtimeStyle.width;var U=J.runtimeStyle.height;J.runtimeStyle.width="auto";J.runtimeStyle.height="auto";var L=J.width;var Q=J.height;J.runtimeStyle.width=A;J.runtimeStyle.height=U;if(arguments.length==3){N=arguments[1];M=arguments[2];K=R=0;F=T=L;B=P=Q}else{if(arguments.length==5){N=arguments[1];M=arguments[2];T=arguments[3];P=arguments[4];K=R=0;F=L;B=Q}else{if(arguments.length==9){K=arguments[1];R=arguments[2];F=arguments[3];B=arguments[4];N=arguments[5];M=arguments[6];T=arguments[7];P=arguments[8]}else{throw Error("Invalid number of arguments")}}}var I=Ah(this,N,M);var O=F/2;var S=B/2;var G=[];var H=10;var Y=10;G.push(" <g_vml_:group",' coordsize="',AV*H,",",AV*Y,'"',' coordorigin="0,0"',' style="width:',H,"px;height:",Y,"px;position:absolute;");if(this.m_[0][0]!=1||this.m_[0][1]||this.m_[1][1]!=1||this.m_[1][0]){var E=[];E.push("M11=",this.m_[0][0],",","M12=",this.m_[1][0],",","M21=",this.m_[0][1],",","M22=",this.m_[1][1],",","Dx=",AN(I.x/AV),",","Dy=",AN(I.y/AV),"");var C=I;var D=Ah(this,N+T,M);var W=Ah(this,N,M+P);var V=Ah(this,N+T,M+P);C.x=m.max(C.x,D.x,W.x,V.x);C.y=m.max(C.y,D.y,W.y,V.y);G.push("padding:0 ",AN(C.x/AV),"px ",AN(C.y/AV),"px 0;filter:progid:DXImageTransform.Microsoft.Matrix(",E.join(""),", sizingmethod='clip');")}else{G.push("top:",AN(I.y/AV),"px;left:",AN(I.x/AV),"px;")}G.push(' ">','<g_vml_:image src="',J.src,'"',' style="width:',AV*T,"px;"," height:",AV*P,'px"',' cropleft="',K/L,'"',' croptop="',R/Q,'"',' cropright="',(L-K-F)/L,'"',' cropbottom="',(Q-R-B)/Q,'"'," />","</g_vml_:group>");this.element_.insertAdjacentHTML("BeforeEnd",G.join(""))};AG.stroke=function(E){var C=10;var A=10;var K=5000;var H={x:null,y:null};var F={x:null,y:null};for(var J=0;J<this.currentPath_.length;J+=K){var I=[];var D=false;I.push("<g_vml_:shape",' filled="',!!E,'"',' style="position:absolute;width:',C,"px;height:",A,'px;"',' coordorigin="0,0"',' coordsize="',AV*C,",",AV*A,'"',' stroked="',!E,'"',' path="');var L=false;for(var M=J;M<Math.min(J+K,this.currentPath_.length);M++){if(M%K==0&&M>0){I.push(" m ",AN(this.currentPath_[M-1].x),",",AN(this.currentPath_[M-1].y))}var B=this.currentPath_[M];var G;switch(B.type){case"moveTo":G=B;I.push(" m ",AN(B.x),",",AN(B.y));break;case"lineTo":I.push(" l ",AN(B.x),",",AN(B.y));break;case"close":I.push(" x ");B=null;break;case"bezierCurveTo":I.push(" c ",AN(B.cp1x),",",AN(B.cp1y),",",AN(B.cp2x),",",AN(B.cp2y),",",AN(B.x),",",AN(B.y));break;case"at":case"wa":I.push(" ",B.type," ",AN(B.x-this.arcScaleX_*B.radius),",",AN(B.y-this.arcScaleY_*B.radius)," ",AN(B.x+this.arcScaleX_*B.radius),",",AN(B.y+this.arcScaleY_*B.radius)," ",AN(B.xStart),",",AN(B.yStart)," ",AN(B.xEnd),",",AN(B.yEnd));break}if(B){if(H.x==null||B.x<H.x){H.x=B.x}if(F.x==null||B.x>F.x){F.x=B.x}if(H.y==null||B.y<H.y){H.y=B.y}if(F.y==null||B.y>F.y){F.y=B.y}}}I.push(' ">');if(!E){AI(this,I)}else{Ax(this,I,H,F)}I.push("</g_vml_:shape>");this.element_.insertAdjacentHTML("beforeEnd",I.join(""))}};function AI(E,C){var F=Aw(E.strokeStyle);var D=F.color;var B=F.alpha*E.globalAlpha;var A=E.lineScale_*E.lineWidth;if(A<1){B*=A}C.push("<g_vml_:stroke",' opacity="',B,'"',' joinstyle="',E.lineJoin,'"',' miterlimit="',E.miterLimit,'"',' endcap="',Ae(E.lineCap),'"',' weight="',A,'px"',' color="',D,'" />')}function Ax(h,e,W,H){var S=h.fillStyle;var C=h.arcScaleX_;var K=h.arcScaleY_;var f=H.x-W.x;var d=H.y-W.y;if(S instanceof Ak){var a=0;var G={x:0,y:0};var b=0;var n=1;if(S.type_=="gradient"){var Q=S.x0_/C;var k=S.y0_/K;var O=S.x1_/C;var E=S.y1_/K;var c=Ah(h,Q,k);var A=Ah(h,O,E);var g=A.x-c.x;var M=A.y-c.y;a=Math.atan2(g,M)*180/Math.PI;if(a<0){a+=360}if(a<1e-06){a=0}}else{var c=Ah(h,S.x0_,S.y0_);G={x:(c.x-W.x)/f,y:(c.y-W.y)/d};f/=C*AV;d/=K*AV;var T=m.max(f,d);b=2*S.r0_/T;n=2*S.r1_/T-b}var N=S.colors_;N.sort(function(q,o){return q.offset-o.offset});var B=N.length;var X=N[0].color;var F=N[B-1].color;var l=N[0].alpha*h.globalAlpha;var V=N[B-1].alpha*h.globalAlpha;var P=[];for(var D=0;D<B;D++){var U=N[D];P.push(U.offset*n+b+" "+U.color)}e.push('<g_vml_:fill type="',S.type_,'"',' method="none" focus="100%"',' color="',X,'"',' color2="',F,'"',' colors="',P.join(","),'"',' opacity="',V,'"',' g_o_:opacity2="',l,'"',' angle="',a,'"',' focusposition="',G.x,",",G.y,'" />')}else{if(S instanceof Aj){if(f&&d){var L=-W.x;var R=-W.y;e.push("<g_vml_:fill",' position="',L/f*C*C,",",R/d*K*K,'"',' type="tile"',' src="',S.src_,'" />')}}else{var I=Aw(h.fillStyle);var J=I.color;var Y=I.alpha*h.globalAlpha;e.push('<g_vml_:fill color="',J,'" opacity="',Y,'" />')}}}AG.fill=function(){this.stroke(true)};AG.closePath=function(){this.currentPath_.push({type:"close"})};function Ah(D,C,B){var A=D.m_;return{x:AV*(C*A[0][0]+B*A[1][0]+A[2][0])-AT,y:AV*(C*A[0][1]+B*A[1][1]+A[2][1])-AT}}AG.save=function(){var A={};AH(this,A);this.aStack_.push(A);this.mStack_.push(this.m_);this.m_=Al(At(),this.m_)};AG.restore=function(){if(this.aStack_.length){AH(this.aStack_.pop(),this);this.m_=this.mStack_.pop()}};function AM(A){return isFinite(A[0][0])&&isFinite(A[0][1])&&isFinite(A[1][0])&&isFinite(A[1][1])&&isFinite(A[2][0])&&isFinite(A[2][1])}function j(D,A,B){if(!AM(A)){return}D.m_=A;if(B){var C=A[0][0]*A[1][1]-A[0][1]*A[1][0];D.lineScale_=Ap(An(C))}}AG.translate=function(B,C){var A=[[1,0,0],[0,1,0],[B,C,1]];j(this,Al(A,this.m_),false)};AG.rotate=function(D){var B=Av(D);var C=AP(D);var A=[[B,C,0],[-C,B,0],[0,0,1]];j(this,Al(A,this.m_),false)};AG.scale=function(B,C){this.arcScaleX_*=B;this.arcScaleY_*=C;var A=[[B,0,0],[0,C,0],[0,0,1]];j(this,Al(A,this.m_),true)};AG.transform=function(D,C,F,B,G,A){var E=[[D,C,0],[F,B,0],[G,A,1]];j(this,Al(E,this.m_),true)};AG.setTransform=function(C,E,A,F,D,G){var B=[[C,E,0],[A,F,0],[D,G,1]];j(this,B,true)};AG.drawText_=function(L,A,K,O,I){var H=this.m_,E=1000,M=0,Q=E,J={x:0,y:0},R=[];var F=AK(Az(this.font),this.element_);var B=AD(F);var C=this.element_.currentStyle;var D=this.textAlign.toLowerCase();switch(D){case"left":case"center":case"right":break;case"end":D=C.direction=="ltr"?"right":"left";break;case"start":D=C.direction=="rtl"?"right":"left";break;default:D="left"}switch(this.textBaseline){case"hanging":case"top":J.y=F.size/1.75;break;case"middle":break;default:case null:case"alphabetic":case"ideographic":case"bottom":J.y=-F.size/2.25;break}switch(D){case"right":M=E;Q=0.05;break;case"center":M=Q=E/2;break}var G=Ah(this,A+J.x,K+J.y);R.push('<g_vml_:line from="',-M,' 0" to="',Q,' 0.05" ',' coordsize="100 100" coordorigin="0 0"',' filled="',!I,'" stroked="',!!I,'" style="position:absolute;width:1px;height:1px;">');if(I){AI(this,R)}else{Ax(this,R,{x:-M,y:0},{x:Q,y:F.size})}var P=H[0][0].toFixed(3)+","+H[1][0].toFixed(3)+","+H[0][1].toFixed(3)+","+H[1][1].toFixed(3)+",0,0";var N=AN(G.x/AV)+","+AN(G.y/AV);R.push('<g_vml_:skew on="t" matrix="',P,'" ',' offset="',N,'" origin="',M,' 0" />','<g_vml_:path textpathok="true" />','<g_vml_:textpath on="true" string="',i(L),'" style="v-text-align:',D,";font:",i(B),'" /></g_vml_:line>');this.element_.insertAdjacentHTML("beforeEnd",R.join(""))};AG.fillText=function(C,A,B,D){this.drawText_(C,A,B,D,false)};AG.strokeText=function(C,A,B,D){this.drawText_(C,A,B,D,true)};AG.measureText=function(B){if(!this.textMeasureEl_){var A='<span style="position:absolute;top:-20000px;left:0;padding:0;margin:0;border:none;white-space:pre;"></span>';this.element_.insertAdjacentHTML("beforeEnd",A);this.textMeasureEl_=this.element_.lastChild}var C=this.element_.ownerDocument;this.textMeasureEl_.innerHTML="";this.textMeasureEl_.style.font=this.font;this.textMeasureEl_.appendChild(C.createTextNode(B));return{width:this.textMeasureEl_.offsetWidth}};AG.clip=function(){};AG.arcTo=function(){};AG.createPattern=function(B,A){return new Aj(B,A)};function Ak(A){this.type_=A;this.x0_=0;this.y0_=0;this.r0_=0;this.x1_=0;this.y1_=0;this.r1_=0;this.colors_=[]}Ak.prototype.addColorStop=function(B,A){A=Aw(A);this.colors_.push({offset:B,color:A.color,alpha:A.alpha})};function Aj(B,A){Ag(B);switch(A){case"repeat":case null:case"":this.repetition_="repeat";break;case"repeat-x":case"repeat-y":case"no-repeat":this.repetition_=A;break;default:Aq("SYNTAX_ERR")}this.src_=B.src;this.width_=B.width;this.height_=B.height}function Aq(A){throw new Af(A)}function Ag(A){if(!A||A.nodeType!=1||A.tagName!="IMG"){Aq("TYPE_MISMATCH_ERR")}if(A.readyState!="complete"){Aq("INVALID_STATE_ERR")}}function Af(A){this.code=this[A];this.message=A+": DOM Exception "+this.code}var Ab=Af.prototype=new Error;Ab.INDEX_SIZE_ERR=1;Ab.DOMSTRING_SIZE_ERR=2;Ab.HIERARCHY_REQUEST_ERR=3;Ab.WRONG_DOCUMENT_ERR=4;Ab.INVALID_CHARACTER_ERR=5;Ab.NO_DATA_ALLOWED_ERR=6;Ab.NO_MODIFICATION_ALLOWED_ERR=7;Ab.NOT_FOUND_ERR=8;Ab.NOT_SUPPORTED_ERR=9;Ab.INUSE_ATTRIBUTE_ERR=10;Ab.INVALID_STATE_ERR=11;Ab.SYNTAX_ERR=12;Ab.INVALID_MODIFICATION_ERR=13;Ab.NAMESPACE_ERR=14;Ab.INVALID_ACCESS_ERR=15;Ab.VALIDATION_ERR=16;Ab.TYPE_MISMATCH_ERR=17;G_vmlCanvasManager=Z;CanvasRenderingContext2D=Ay;CanvasGradient=Ak;CanvasPattern=Aj;DOMException=Af})()};