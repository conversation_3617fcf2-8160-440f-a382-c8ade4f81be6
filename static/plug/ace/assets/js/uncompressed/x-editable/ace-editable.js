(function(B){var A=function(C){this.init("image",C,A.defaults);if("on_error" in C.image){this.on_error=C.image["on_error"];delete C.image["on_error"]}if("on_success" in C.image){this.on_success=C.image["on_success"];delete C.image["on_success"]}if("max_size" in C.image){this.max_size=C.image["max_size"];delete C.image["max_size"]}this.initImage(C,A.defaults)};B.fn.editableutils.inherit(A,B.fn.editabletypes.abstractinput);B.extend(A.prototype,{initImage:function(C,D){this.options.image=B.extend({},D.image,C.image);this.name=this.options.image.name||"editable-image-input"},render:function(){var C=this;this.$input=this.$tpl.find("input[type=hidden]:eq(0)");this.$file=this.$tpl.find("input[type=file]:eq(0)");this.$file.attr({"name":this.name});this.$input.attr({"name":this.name+"-hidden"});this.options.image.allowExt=this.options.image.allowExt||["jpg","jpeg","png","gif"];this.options.image.allowMime=this.options.image.allowMime||["image/jpg","image/jpeg","image/png","image/gif"];this.options.image.maxSize=C.max_size||this.options.image.maxSize||false;this.options.image.before_remove=this.options.image.before_remove||function(){C.$input.val(null);return true};this.$file.ace_file_input(this.options.image).on("change",function(){var D=(C.$file.val()||C.$file.data("ace_input_files"))?Math.random()+""+(new Date()).getTime():null;C.$input.val(D)}).closest(".ace-file-input").css({"width":"150px"}).closest(".editable-input").addClass("editable-image");this.$file.off("file.error.ace").on("file.error.ace",function(D,E){if(!C.on_error){return}if(E.error_count["ext"]>0||E.error_count["mime"]>0){C.on_error(1)}else{if(E.error_count["size"]>0){C.on_error(2)}}})}});A.defaults=B.extend({},B.fn.editabletypes.abstractinput.defaults,{tpl:'<span><input type="hidden" /></span><span><input type="file" /></span>',inputclass:"",image:{style:"well",btn_choose:"Change Image",btn_change:null,no_icon:"fa fa-picture-o",thumbnail:"large"}});B.fn.editabletypes.image=A}(window.jQuery));(function(B){var A=function(C){this.init("wysiwyg",C,A.defaults);this.options.wysiwyg=B.extend({},A.defaults.wysiwyg,C.wysiwyg)};B.fn.editableutils.inherit(A,B.fn.editabletypes.abstractinput);B.extend(A.prototype,{render:function(){this.$editor=this.$input.nextAll(".wysiwyg-editor:eq(0)");this.$tpl.parent().find(".wysiwyg-editor").show().ace_wysiwyg({toolbar:["bold","italic","strikethrough","underline",null,"foreColor",null,"insertImage"]}).prev().addClass("wysiwyg-style2").closest(".editable-input").addClass("editable-wysiwyg").closest(".editable-container").css({"display":"block"});if(this.options.wysiwyg&&this.options.wysiwyg.css){this.$tpl.closest(".editable-wysiwyg").css(this.options.wysiwyg.css)}},value2html:function(C,D){B(D).html(C);return false},html2value:function(C){return C},value2input:function(C){this.$editor.html(C)},input2value:function(){return this.$editor.html()},activate:function(){}});A.defaults=B.extend({},B.fn.editabletypes.abstractinput.defaults,{tpl:'<input type="hidden" /><div class="wysiwyg-editor"></div>',inputclass:"editable-wysiwyg",wysiwyg:{}});B.fn.editabletypes.wysiwyg=A}(window.jQuery));(function(B){var A=function(E){this.init("spinner",E,A.defaults);this.initSpinner(E,A.defaults);this.nativeUI=false;try{var D=document.createElement("INPUT");D.type="number";this.nativeUI=D.type==="number"&&this.options.spinner.nativeUI===true}catch(C){}};B.fn.editableutils.inherit(A,B.fn.editabletypes.abstractinput);B.extend(A.prototype,{initSpinner:function(C,D){this.options.spinner=B.extend({},D.spinner,C.spinner)},render:function(){},activate:function(){if(this.$input.is(":visible")){this.$input.focus();B.fn.editableutils.setCursorPosition(this.$input.get(0),this.$input.val().length);if(!this.nativeUI){var E=parseInt(this.$input.val());var D=B.extend({value:E},this.options.spinner);this.$input.ace_spinner(D)}else{this.$input.get(0).type="number";var D=["min","max","step"];for(var C=0;C<D.length;C++){if(D[C] in this.options.spinner){this.$input.attr(D[C],this.options.spinner[D[C]])}}}}},autosubmit:function(){this.$input.keydown(function(C){if(C.which===13){B(this).closest("form").submit()}})}});A.defaults=B.extend({},B.fn.editabletypes.abstractinput.defaults,{tpl:'<input type="text" />',inputclass:"",spinner:{min:0,max:100,step:1,icon_up:"fa fa-plus",icon_down:"fa fa-minus",btn_up_class:"btn-success",btn_down_class:"btn-danger"}});B.fn.editabletypes.spinner=A}(window.jQuery));(function(B){var A=function(E){this.init("slider",E,A.defaults);this.initSlider(E,A.defaults);this.nativeUI=false;try{var D=document.createElement("INPUT");D.type="range";this.nativeUI=D.type==="range"&&this.options.slider.nativeUI===true}catch(C){}};B.fn.editableutils.inherit(A,B.fn.editabletypes.abstractinput);B.extend(A.prototype,{initSlider:function(C,D){this.options.slider=B.extend({},D.slider,C.slider)},render:function(){},activate:function(){if(this.$input.is(":visible")){this.$input.focus();B.fn.editableutils.setCursorPosition(this.$input.get(0),this.$input.val().length);if(!this.nativeUI){var C=this;var G=parseInt(this.$input.val());var F=this.options.slider.width||200;var E=B.extend(this.options.slider,{value:G,slide:function(I,H){var J=parseInt(H.value);C.$input.val(J);if(H.handle.firstChild==null){B(H.handle).prepend("<div class='tooltip top in' style='display:none; top:-38px; left:-5px;'><div class='tooltip-arrow'></div><div class='tooltip-inner'></div></div>")}B(H.handle.firstChild).show().children().eq(1).text(J)}});this.$input.parent().addClass("editable-slider").css("width",F+"px").slider(E)}else{this.$input.get(0).type="range";var E=["min","max","step"];for(var D=0;D<E.length;D++){if(E[D] in this.options.slider){this.$input[0][E[D]]=this.options.slider[E[D]]}}var F=this.options.slider.width||200;this.$input.parent().addClass("editable-slider").css("width",F+"px")}}},value2html:function(C,D){},autosubmit:function(){this.$input.keydown(function(C){if(C.which===13){B(this).closest("form").submit()}})}});A.defaults=B.extend({},B.fn.editabletypes.abstractinput.defaults,{tpl:'<input type="text" /><span class="inline ui-slider-green"><span class="slider-display"></span></span>',inputclass:"",slider:{min:1,max:100,step:1,range:"min"}});B.fn.editabletypes.slider=A}(window.jQuery));(function(B){var A=function(E){this.init("adate",E,A.defaults);this.initDate(E,A.defaults);this.nativeUI=false;try{var D=document.createElement("INPUT");D.type="date";this.nativeUI=D.type==="date"&&this.options.date.nativeUI===true}catch(C){}};B.fn.editableutils.inherit(A,B.fn.editabletypes.abstractinput);B.extend(A.prototype,{initDate:function(C,D){this.options.date=B.extend({},D.date,C.date)},render:function(){this.$input=this.$tpl.find("input.date")},activate:function(){if(this.$input.is(":visible")){this.$input.focus()}if(!this.nativeUI){var D=this.$input;this.$input.datepicker(this.options.date);var C=D.data("datepicker");if(C){D.on("click",function(){C.show()}).siblings(".input-group-addon").on("click",function(){C.show()})}}else{this.$input.get(0).type="date"}},autosubmit:function(){this.$input.keydown(function(C){if(C.which===13){B(this).closest("form").submit()}})}});A.defaults=B.extend({},B.fn.editabletypes.abstractinput.defaults,{tpl:'<div class="input-group input-group-compact"><input type="text" class="input-medium date" /><span class="input-group-addon"><i class="fa fa-calendar"></i></span></div>',date:{weekStart:0,startView:0,minViewMode:0}});B.fn.editabletypes.adate=A}(window.jQuery));