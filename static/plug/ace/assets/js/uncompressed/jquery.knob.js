/*jQuery Knob*/
(function(A){if(typeof define==="function"&&define.amd){define(["jquery"],A)}else{A(jQuery)}}(function(D){var A={},C=Math.max,B=Math.min;A.c={};A.c.d=D(document);A.c.t=function(E){return E.originalEvent.touches.length-1};A.o=function(){var E=this;this.o=null;this.$=null;this.i=null;this.g=null;this.v=null;this.cv=null;this.x=0;this.y=0;this.w=0;this.h=0;this.$c=null;this.c=null;this.t=0;this.isInit=false;this.fgColor=null;this.pColor=null;this.dH=null;this.cH=null;this.eH=null;this.rH=null;this.scale=1;this.relative=false;this.relativeWidth=false;this.relativeHeight=false;this.$div=null;this.run=function(){var F=function(G,H){var I;for(I in H){E.o[I]=H[I]}E._carve().init();E._configure()._draw()};if(this.$.data("kontroled")){return}this.$.data("kontroled",true);this.extend();this.o=D.extend({min:this.$.data("min")!==undefined?this.$.data("min"):0,max:this.$.data("max")!==undefined?this.$.data("max"):100,stopper:true,readOnly:this.$.data("readonly")||(this.$.attr("readonly")==="readonly"),cursor:(this.$.data("cursor")===true&&30)||this.$.data("cursor")||0,thickness:(this.$.data("thickness")&&Math.max(Math.min(this.$.data("thickness"),1),0.01))||0.35,lineCap:this.$.data("linecap")||"butt",width:this.$.data("width")||200,height:this.$.data("height")||200,displayInput:this.$.data("displayinput")==null||this.$.data("displayinput"),displayPrevious:this.$.data("displayprevious"),fgColor:this.$.data("fgcolor")||"#87CEEB",inputColor:this.$.data("inputcolor"),font:this.$.data("font")||"Arial",fontWeight:this.$.data("font-weight")||"bold",inline:false,step:this.$.data("step")||1,rotation:this.$.data("rotation"),draw:null,change:null,cancel:null,release:null,format:function(G){return G},parse:function(G){return parseFloat(G)}},this.o);this.o.flip=this.o.rotation==="anticlockwise"||this.o.rotation==="acw";if(!this.o.inputColor){this.o.inputColor=this.o.fgColor}if(this.$.is("fieldset")){this.v={};this.i=this.$.find("input");this.i.each(function(H){var G=D(this);E.i[H]=G;E.v[H]=E.o.parse(G.val());G.bind("change blur",function(){var I={};I[H]=G.val();E.val(I)})});this.$.find("legend").remove()}else{this.i=this.$;this.v=this.o.parse(this.$.val());(this.v==="")&&(this.v=this.o.min);this.$.bind("change blur",function(){E.val(E._validate(E.o.parse(E.$.val())))})}(!this.o.displayInput)&&this.$.hide();this.$c=D(document.createElement("canvas")).attr({width:this.o.width,height:this.o.height});this.$div=D('<div style="'+(this.o.inline?"display:inline;":"")+"width:"+this.o.width+"px;height:"+this.o.height+'px;"></div>');this.$.wrap(this.$div).before(this.$c);this.$div=this.$.parent();if(typeof G_vmlCanvasManager!=="undefined"){G_vmlCanvasManager.initElement(this.$c[0])}this.c=this.$c[0].getContext?this.$c[0].getContext("2d"):null;if(!this.c){throw {name:"CanvasNotSupportedException",message:"Canvas not supported. Please use excanvas on IE8.0.",toString:function(){return this.name+": "+this.message}}}this.scale=(window.devicePixelRatio||1)/(this.c.webkitBackingStorePixelRatio||this.c.mozBackingStorePixelRatio||this.c.msBackingStorePixelRatio||this.c.oBackingStorePixelRatio||this.c.backingStorePixelRatio||1);this.relativeWidth=((this.o.width%1!==0)&&this.o.width.indexOf("%"));this.relativeHeight=((this.o.height%1!==0)&&this.o.height.indexOf("%"));this.relative=(this.relativeWidth||this.relativeHeight);this._carve();if(this.v instanceof Object){this.cv={};this.copy(this.v,this.cv)}else{this.cv=this.v}this.$.bind("configure",F).parent().bind("configure",F);this._listen()._configure()._xy().init();this.isInit=true;this.$.val(this.o.format(this.v));this._draw();return this};this._carve=function(){if(this.relative){var G=this.relativeWidth?this.$div.parent().width()*parseInt(this.o.width)/100:this.$div.parent().width(),F=this.relativeHeight?this.$div.parent().height()*parseInt(this.o.height)/100:this.$div.parent().height();this.w=this.h=Math.min(G,F)}else{this.w=this.o.width;this.h=this.o.height}this.$div.css({"width":this.w+"px","height":this.h+"px"});this.$c.attr({width:this.w,height:this.h});if(this.scale!==1){this.$c[0].width=this.$c[0].width*this.scale;this.$c[0].height=this.$c[0].height*this.scale;this.$c.width(this.w);this.$c.height(this.h)}return this};this._draw=function(){var F=true;E.g=E.c;E.clear();E.dH&&(F=E.dH());(F!==false)&&E.draw()};this._touch=function(G){var F=function(H){var I=E.xy2val(H.originalEvent.touches[E.t].pageX,H.originalEvent.touches[E.t].pageY);if(I==E.cv){return}if(E.cH&&(E.cH(I)===false)){return}E.change(E._validate(I));E._draw()};this.t=A.c.t(G);F(G);A.c.d.bind("touchmove.k",F).bind("touchend.k",function(){A.c.d.unbind("touchmove.k touchend.k");E.val(E.cv)});return this};this._mouse=function(F){var G=function(H){var I=E.xy2val(H.pageX,H.pageY);if(I==E.cv){return}if(E.cH&&(E.cH(I)===false)){return}E.change(E._validate(I));E._draw()};G(F);A.c.d.bind("mousemove.k",G).bind("keyup.k",function(H){if(H.keyCode===27){A.c.d.unbind("mouseup.k mousemove.k keyup.k");if(E.eH&&(E.eH()===false)){return}E.cancel()}}).bind("mouseup.k",function(H){A.c.d.unbind("mousemove.k mouseup.k keyup.k");E.val(E.cv)});return this};this._xy=function(){var F=this.$c.offset();this.x=F.left;this.y=F.top;return this};this._listen=function(){if(!this.o.readOnly){this.$c.bind("mousedown",function(F){F.preventDefault();E._xy()._mouse(F)}).bind("touchstart",function(F){F.preventDefault();E._xy()._touch(F)});this.listen()}else{this.$.attr("readonly","readonly")}if(this.relative){D(window).resize(function(){E._carve().init();E._draw()})}return this};this._configure=function(){if(this.o.draw){this.dH=this.o.draw}if(this.o.change){this.cH=this.o.change}if(this.o.cancel){this.eH=this.o.cancel}if(this.o.release){this.rH=this.o.release}if(this.o.displayPrevious){this.pColor=this.h2rgba(this.o.fgColor,"0.4");this.fgColor=this.h2rgba(this.o.fgColor,"0.6")}else{this.fgColor=this.o.fgColor}return this};this._clear=function(){this.$c[0].width=this.$c[0].width};this._validate=function(F){return(~~(((F<0)?-0.5:0.5)+(F/this.o.step)))*this.o.step};this.listen=function(){};this.extend=function(){};this.init=function(){};this.change=function(F){};this.val=function(F){};this.xy2val=function(F,G){};this.draw=function(){};this.clear=function(){this._clear()};this.h2rgba=function(F,H){var G;F=F.substring(1,7);G=[parseInt(F.substring(0,2),16),parseInt(F.substring(2,4),16),parseInt(F.substring(4,6),16)];return"rgba("+G[0]+","+G[1]+","+G[2]+","+H+")"};this.copy=function(H,G){for(var F in H){G[F]=H[F]}}};A.Dial=function(){A.o.call(this);this.startAngle=null;this.xy=null;this.radius=null;this.lineWidth=null;this.cursorExt=null;this.w2=null;this.PI2=2*Math.PI;this.extend=function(){this.o=D.extend({bgColor:this.$.data("bgcolor")||"#EEEEEE",angleOffset:this.$.data("angleoffset")||0,angleArc:this.$.data("anglearc")||360,inline:true},this.o)};this.val=function(F,E){if(null!=F){F=this.o.parse(F);if(E!==false&&(F!=this.v)&&this.rH&&(this.rH(F)===false)){return}this.cv=this.o.stopper?C(B(F,this.o.max),this.o.min):F;this.v=this.cv;this.$.val(this.o.format(this.v));this._draw()}else{return this.v}};this.xy2val=function(F,G){var H,E;H=Math.atan2(F-(this.x+this.w2),-(G-this.y-this.w2))-this.angleOffset;if(this.o.flip){H=this.angleArc-H-this.PI2}if(this.angleArc!=this.PI2&&(H<0)&&(H>-0.5)){H=0}else{if(H<0){H+=this.PI2}}E=~~(0.5+(H*(this.o.max-this.o.min)/this.angleArc))+this.o.min;this.o.stopper&&(E=C(B(E,this.o.max),this.o.min));return E};this.listen=function(){var E=this,G,L,J=function(M){M.preventDefault();var N=M.originalEvent,Q=N.detail||N.wheelDeltaX,O=N.detail||N.wheelDeltaY,P=E._validate(E.o.parse(E.$.val()))+(Q>0||O>0?E.o.step:Q<0||O<0?-E.o.step:0);P=C(B(P,E.o.max),E.o.min);E.val(P,false);if(E.rH){clearTimeout(G);G=setTimeout(function(){E.rH(P);G=null},100);if(!L){L=setTimeout(function(){if(G){E.rH(P)}L=null},200)}}},F,K,I=1,H={37:-E.o.step,38:E.o.step,39:E.o.step,40:-E.o.step};this.$.bind("keydown",function(M){var N=M.keyCode;if(N>=96&&N<=105){N=M.keyCode=N-48}F=parseInt(String.fromCharCode(N));if(isNaN(F)){(N!==13)&&(N!==8)&&(N!==9)&&(N!==189)&&(N!==190||E.$.val().match(/\./))&&M.preventDefault();if(D.inArray(N,[37,38,39,40])>-1){M.preventDefault();var O=E.o.parse(E.$.val())+H[N]*I;E.o.stopper&&(O=C(B(O,E.o.max),E.o.min));E.change(O);E._draw();K=window.setTimeout(function(){I*=2},30)}}}).bind("keyup",function(M){if(isNaN(F)){if(K){window.clearTimeout(K);K=null;I=1;E.val(E.$.val())}}else{(E.$.val()>E.o.max&&E.$.val(E.o.max))||(E.$.val()<E.o.min&&E.$.val(E.o.min))}});this.$c.bind("mousewheel DOMMouseScroll",J);this.$.bind("mousewheel DOMMouseScroll",J)};this.init=function(){if(this.v<this.o.min||this.v>this.o.max){this.v=this.o.min}this.$.val(this.v);this.w2=this.w/2;this.cursorExt=this.o.cursor/100;this.xy=this.w2*this.scale;this.lineWidth=this.xy*this.o.thickness;this.lineCap=this.o.lineCap;this.radius=this.xy-this.lineWidth/2;this.o.angleOffset&&(this.o.angleOffset=isNaN(this.o.angleOffset)?0:this.o.angleOffset);this.o.angleArc&&(this.o.angleArc=isNaN(this.o.angleArc)?this.PI2:this.o.angleArc);this.angleOffset=this.o.angleOffset*Math.PI/180;this.angleArc=this.o.angleArc*Math.PI/180;this.startAngle=1.5*Math.PI+this.angleOffset;this.endAngle=1.5*Math.PI+this.angleOffset+this.angleArc;var E=C(String(Math.abs(this.o.max)).length,String(Math.abs(this.o.min)).length,2)+2;this.o.displayInput&&this.i.css({"width":((this.w/2+4)>>0)+"px","height":((this.w/3)>>0)+"px","position":"absolute","vertical-align":"middle","margin-top":((this.w/3)>>0)+"px","margin-left":"-"+((this.w*3/4+2)>>0)+"px","border":0,"background":"none","font":this.o.fontWeight+" "+((this.w/E)>>0)+"px "+this.o.font,"text-align":"center","color":this.o.inputColor||this.o.fgColor,"padding":"0px","-webkit-appearance":"none"})||this.i.css({"width":"0px","visibility":"hidden"})};this.change=function(E){this.cv=E;this.$.val(this.o.format(E))};this.angle=function(E){return(E-this.o.min)*this.angleArc/(this.o.max-this.o.min)};this.arc=function(F){var E,G;F=this.angle(F);if(this.o.flip){E=this.endAngle+1e-05;G=E-F-1e-05}else{E=this.startAngle-1e-05;G=E+F+1e-05}this.o.cursor&&(E=G-this.cursorExt)&&(G=G+this.cursorExt);return{s:E,e:G,d:this.o.flip&&!this.o.cursor}};this.draw=function(){var G=this.g,H=this.arc(this.cv),F,E=1;G.lineWidth=this.lineWidth;G.lineCap=this.lineCap;G.beginPath();G.strokeStyle=this.o.bgColor;G.arc(this.xy,this.xy,this.radius,this.endAngle-1e-05,this.startAngle+1e-05,true);G.stroke();if(this.o.displayPrevious){F=this.arc(this.v);G.beginPath();G.strokeStyle=this.pColor;G.arc(this.xy,this.xy,this.radius,F.s,F.e,F.d);G.stroke();E=(this.cv==this.v)}G.beginPath();G.strokeStyle=E?this.o.fgColor:this.fgColor;G.arc(this.xy,this.xy,this.radius,H.s,H.e,H.d);G.stroke()};this.cancel=function(){this.val(this.v)}};D.fn.dial=D.fn.knob=function(E){return this.each(function(){var F=new A.Dial();F.o=E;F.$=D(this);F.run()}).parent()}}));