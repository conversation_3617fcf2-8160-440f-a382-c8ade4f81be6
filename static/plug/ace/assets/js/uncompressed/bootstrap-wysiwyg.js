(function(B){var A=function(C){var E=B.Deferred(),D=new FileReader();D.onload=function(F){E.resolve(F.target.result)};D.onerror=E.reject;D.onprogress=E.notify;D.readAsDataURL(C);return E.promise()};B.fn.cleanHtml=function(){var C=B(this).html();return C&&C.replace(/(<br>|\s|<div><br><\/div>|&nbsp;)*$/,"")};B.fn.wysiwyg=function(N){var C=this,P,L,E,G=function(){if(L.activeToolbarClass){B(L.toolbarSelector).find(E).each(function(){try{var S=B(this).data(L.commandRole);if(document.queryCommandState(S)){B(this).addClass(L.activeToolbarClass)}else{B(this).removeClass(L.activeToolbarClass)}}catch(R){}})}},K=function(R,S){var U=R.split(" "),T=U.shift(),V=U.join(" ")+(S||"");document.execCommand(T,0,V);G()},F=function(R){B.each(R,function(T,S){C.keydown(T,function(U){if(C.attr("contenteditable")&&C.is(":visible")){U.preventDefault();U.stopPropagation();K(S)}}).keyup(T,function(U){if(C.attr("contenteditable")&&C.is(":visible")){U.preventDefault();U.stopPropagation()}})})},D=function(){try{var S=window.getSelection();if(S.getRangeAt&&S.rangeCount){return S.getRangeAt(0)}}catch(R){}},Q=function(){P=D()},O=function(){try{var T=window.getSelection();if(P){try{T.removeAllRanges()}catch(S){document.body.createTextRange().select();document.selection.empty()}T.addRange(P)}}catch(R){}},I=function(R){C.focus();B.each(R,function(T,S){if(/^image\//.test(S.type)){B.when(A(S)).done(function(U){K("insertimage",U)}).fail(function(U){L.fileUploadError("file-reader",U)})}else{L.fileUploadError("unsupported-file-type",S.type)}})},H=function(R,S){O();if(document.queryCommandSupported("hiliteColor")){document.execCommand("hiliteColor",0,S||"transparent")}Q();R.data(L.selectionMarker,S)},M=function(T,R){T.find(E).click(function(){O();C.focus();K(B(this).data(R.commandRole));Q()});T.find("[data-toggle=dropdown]").click(O);var S=!!window.navigator.msPointerEnabled||(!!document.all&&!!document.addEventListener);T.find("input[type=text][data-"+R.commandRole+"]").on("webkitspeechchange change",function(){var U=this.value;this.value="";O();if(U){C.focus();K(B(this).data(R.commandRole),U)}Q()}).on("focus",function(){if(S){return}var U=B(this);if(!U.data(R.selectionMarker)){H(U,R.selectionColor);U.focus()}}).on("blur",function(){if(S){return}var U=B(this);if(U.data(R.selectionMarker)){H(U,false)}});T.find("input[type=file][data-"+R.commandRole+"]").change(function(){O();if(this.type==="file"&&this.files&&this.files.length>0){I(this.files)}Q();this.value=""})},J=function(){C.on("dragenter dragover",false).on("drop",function(R){var S=R.originalEvent.dataTransfer;R.stopPropagation();R.preventDefault();if(S&&S.files&&S.files.length>0){I(S.files)}})};L=B.extend({},B.fn.wysiwyg.defaults,N);E="a[data-"+L.commandRole+"],button[data-"+L.commandRole+"],input[type=button][data-"+L.commandRole+"]";F(L.hotKeys);if(L.dragAndDropImages){J()}M(B(L.toolbarSelector),L);C.attr("contenteditable",true).on("mouseup keyup mouseout",function(){Q();G()});B(window).bind("touchend",function(S){var R=(C.is(S.target)||C.has(S.target).length>0),U=D(),T=U&&(U.startContainer===U.endContainer&&U.startOffset===U.endOffset);if(!T||R){Q();G()}});return this};B.fn.wysiwyg.defaults={hotKeys:{"ctrl+b meta+b":"bold","ctrl+i meta+i":"italic","ctrl+u meta+u":"underline","ctrl+z meta+z":"undo","ctrl+y meta+y meta+shift+z":"redo","ctrl+l meta+l":"justifyleft","ctrl+r meta+r":"justifyright","ctrl+e meta+e":"justifycenter","ctrl+j meta+j":"justifyfull","shift+tab":"outdent","tab":"indent"},toolbarSelector:"[data-role=editor-toolbar]",commandRole:"edit",activeToolbarClass:"btn-info",selectionMarker:"edit-focus-marker",selectionColor:"darkgrey",dragAndDropImages:true,fileUploadError:function(D,C){console.log("File upload error",D,C)}}}(window.jQuery));