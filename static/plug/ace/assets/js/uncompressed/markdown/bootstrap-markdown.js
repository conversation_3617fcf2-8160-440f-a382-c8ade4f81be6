!function(E){var A=function(G,F){this.$ns="bootstrap-markdown";this.$element=E(G);this.$editable={el:null,type:null,attrKeys:[],attrValues:[],content:null};this.$options=E.extend(true,{},E.fn.markdown.defaults,F,this.$element.data(),this.$element.data("options"));this.$oldContent=null;this.$isPreview=false;this.$editor=null;this.$textarea=null;this.$handler=[];this.$callback=[];this.$nextTab=[];this.showEditor()};A.prototype={constructor:A,__alterButtons:function(H,I){var G=this.$handler,J=(H=="all"),F=this;E.each(G,function(M,L){var K=true;if(J){K=false}else{K=L.indexOf(H)<0}if(K==false){I(F.$editor.find('button[data-handler="'+L+'"]'))}})},__buildButtons:function(S,W){var O,J=this.$ns,H=this.$handler,G=this.$callback;for(O=0;O<S.length;O++){var Y,I=S[O];for(Y=0;Y<I.length;Y++){var F,X=I[Y].data,L=E("<div/>",{"class":"btn-group"});for(F=0;F<X.length;F++){var M=X[F],V="",N=J+"-"+M.name,T=M.icon instanceof Object?M.icon[this.$options.iconlibrary]:M.icon,K=M.btnText?M.btnText:"",R=M.btnClass?M.btnClass:"btn",U=M.tabIndex?M.tabIndex:"-1",Q=typeof M.hotkey!=="undefined"?M.hotkey:"",P=typeof jQuery.hotkeys!=="undefined"&&Q!==""?" ("+Q+")":"";if(M.toggle==true){V=' data-toggle="button"'}L.append('<button type="button" class="'+R+' btn-default btn-sm" title="'+this.__localize(M.title)+P+'" tabindex="'+U+'" data-provider="'+J+'" data-handler="'+N+'" data-hotkey="'+Q+'"'+V+'><span class="'+T+'"></span> '+this.__localize(K)+"</button>");H.push(N);G.push(M.callback)}W.append(L)}}return W},__setListener:function(){var F=typeof this.$textarea.attr("rows")!="undefined",H=this.$textarea.val().split("\n").length>5?this.$textarea.val().split("\n").length:"5",G=F?this.$textarea.attr("rows"):H;this.$textarea.attr("rows",G);if(this.$options.resize){this.$textarea.css("resize",this.$options.resize)}this.$textarea.on("focus",E.proxy(this.focus,this)).on("keypress",E.proxy(this.keypress,this)).on("keyup",E.proxy(this.keyup,this)).on("change",E.proxy(this.change,this));if(this.eventSupported("keydown")){this.$textarea.on("keydown",E.proxy(this.keydown,this))}this.$textarea.data("markdown",this)},__handle:function(H){var L=E(H.currentTarget),I=this.$handler,F=this.$callback,J=L.attr("data-handler"),G=I.indexOf(J),K=F[G];E(H.currentTarget).focus();K(this);this.change(this);if(J.indexOf("cmdSave")<0){this.$textarea.focus()}H.preventDefault()},__localize:function(H){var G=E.fn.markdown.messages,F=this.$options.language;if(typeof G!=="undefined"&&typeof G[F]!=="undefined"&&typeof G[F][H]!=="undefined"){return G[F][H]}return H},showEditor:function(){var W=this,T,K=this.$ns,V=this.$element,S=V.css("height"),N=V.css("width"),Y=this.$editable,L=this.$handler,H=this.$callback,R=this.$options,M=E("<div/>",{"class":"md-editor",click:function(){W.focus()}});if(this.$editor==null){var G=E("<div/>",{"class":"md-header btn-toolbar"});var F=[];if(R.buttons.length>0){F=F.concat(R.buttons[0])}if(R.additionalButtons.length>0){F=F.concat(R.additionalButtons[0])}if(R.reorderButtonGroups.length>0){F=F.filter(function(Z){return R.reorderButtonGroups.indexOf(Z.name)>-1}).sort(function(c,Z){if(R.reorderButtonGroups.indexOf(c.name)<R.reorderButtonGroups.indexOf(Z.name)){return -1}if(R.reorderButtonGroups.indexOf(c.name)>R.reorderButtonGroups.indexOf(Z.name)){return 1}return 0})}if(F.length>0){G=this.__buildButtons([F],G)}M.append(G);if(V.is("textarea")){V.before(M);T=V;T.addClass("md-input");M.append(T)}else{var J=(typeof toMarkdown=="function")?toMarkdown(V.html()):V.html(),I=E.trim(J);T=E("<textarea/>",{"class":"md-input","val":I});M.append(T);Y.el=V;Y.type=V.prop("tagName").toLowerCase();Y.content=V.html();E(V[0].attributes).each(function(){Y.attrKeys.push(this.nodeName);Y.attrValues.push(this.nodeValue)});V.replaceWith(M)}var Q=E("<div/>",{"class":"md-footer"}),U=false,O="";if(R.savable){U=true;var P="cmdSave";L.push(P);H.push(R.onSave);Q.append('<button class="btn btn-success" data-provider="'+K+'" data-handler="'+P+'"><i class="icon icon-white icon-ok"></i> '+this.__localize("Save")+"</button>")}O=typeof R.footer==="function"?R.footer(this):R.footer;if(E.trim(O)!==""){U=true;Q.append(O)}if(U){M.append(Q)}if(R.width&&R.width!=="inherit"){if(jQuery.isNumeric(R.width)){M.css("display","table");T.css("width",R.width+"px")}else{M.addClass(R.width)}}if(R.height&&R.height!=="inherit"){if(jQuery.isNumeric(R.height)){var X=R.height;if(G){X=Math.max(0,X-G.outerHeight())}if(Q){X=Math.max(0,X-Q.outerHeight())}T.css("height",X+"px")}else{M.addClass(R.height)}}this.$editor=M;this.$textarea=T;this.$editable=Y;this.$oldContent=this.getContent();this.__setListener();this.$editor.attr("id",(new Date).getTime());this.$editor.on("click",'[data-provider="bootstrap-markdown"]',E.proxy(this.__handle,this));if(this.$element.is(":disabled")||this.$element.is("[readonly]")){this.disableButtons("all")}if(this.eventSupported("keydown")&&typeof jQuery.hotkeys==="object"){G.find('[data-provider="bootstrap-markdown"]').each(function(){var Z=E(this),a=Z.attr("data-hotkey");if(a.toLowerCase()!==""){T.bind("keydown",a,function(){Z.trigger("click");return false})}})}}else{this.$editor.show()}if(R.autofocus){this.$textarea.focus();this.$editor.addClass("active")}if(R.initialstate==="preview"){this.showPreview()}this.hideButtons(R.hiddenButtons);this.disableButtons(R.disabledButtons);R.onShow(this);return this},parseContent:function(){var G,F=this.$options.onPreview(this);if(typeof F=="string"){G=F}else{var H=this.$textarea.val();if(typeof markdown=="object"){G=markdown.toHTML(H)}else{if(typeof marked=="function"){G=marked(H)}else{G=H}}}return G},showPreview:function(){var G=this.$options,I=this.$textarea,J=I.next(),F=E("<div/>",{"class":"md-preview","data-provider":"markdown-preview"}),H;this.$isPreview=true;this.disableButtons("all").enableButtons("cmdPreview");H=this.parseContent();F.html(H);if(J&&J.attr("class")=="md-footer"){F.insertBefore(J)}else{I.parent().append(F)}F.css({width:I.outerWidth()+"px",height:I.outerHeight()+"px"});I.hide();F.data("markdown",this);return this},hidePreview:function(){this.$isPreview=false;var F=this.$editor.find('div[data-provider="markdown-preview"]');F.remove();this.enableButtons("all");this.$textarea.show();this.__setListener();return this},isDirty:function(){return this.$oldContent!=this.getContent()},getContent:function(){return this.$textarea.val()},setContent:function(F){this.$textarea.val(F);return this},findSelection:function(F){var I=this.getContent(),H;if(H=I.indexOf(F),H>=0&&F.length>0){var J=this.getSelection(),G;this.setSelection(H,H+F.length);G=this.getSelection();this.setSelection(J.start,J.end);return G}else{return null}},getSelection:function(){var F=this.$textarea[0];return(("selectionStart" in F&&function(){var G=F.selectionEnd-F.selectionStart;return{start:F.selectionStart,end:F.selectionEnd,length:G,text:F.value.substr(F.selectionStart,G)}})||function(){return null})()},setSelection:function(H,G){var F=this.$textarea[0];return(("selectionStart" in F&&function(){F.selectionStart=H;F.selectionEnd=G;return})||function(){return null})()},replaceSelection:function(G){var F=this.$textarea[0];return(("selectionStart" in F&&function(){F.value=F.value.substr(0,F.selectionStart)+G+F.value.substr(F.selectionEnd,F.value.length);F.selectionStart=F.value.length;return this})||function(){F.value+=G;return jQuery(F)})()},getNextTab:function(){if(this.$nextTab.length==0){return null}else{var G,F=this.$nextTab.shift();if(typeof F=="function"){G=F()}else{if(typeof F=="object"&&F.length>0){G=F}}return G}},setNextTab:function(I,H){if(typeof I=="string"){var G=this;this.$nextTab.push(function(){return G.findSelection(I)})}else{if(typeof I=="numeric"&&typeof H=="numeric"){var F=this.getSelection();this.setSelection(I,H);this.$nextTab.push(this.getSelection());this.setSelection(F.start,F.end)}}return},__parseButtonNameParam:function(F){var G=[];if(typeof F=="string"){G.push(F)}else{G=F}return G},enableButtons:function(G){var H=this.__parseButtonNameParam(G),F=this;E.each(H,function(I,J){F.__alterButtons(H[I],function(K){K.removeAttr("disabled")})});return this},disableButtons:function(G){var H=this.__parseButtonNameParam(G),F=this;E.each(H,function(I,J){F.__alterButtons(H[I],function(K){K.attr("disabled","disabled")})});return this},hideButtons:function(G){var H=this.__parseButtonNameParam(G),F=this;E.each(H,function(I,J){F.__alterButtons(H[I],function(K){K.addClass("hidden")})});return this},showButtons:function(G){var H=this.__parseButtonNameParam(G),F=this;E.each(H,function(I,J){F.__alterButtons(H[I],function(K){K.removeClass("hidden")})});return this},eventSupported:function(F){var G=F in this.$element;if(!G){this.$element.setAttribute(F,"return;");G=typeof this.$element[F]==="function"}return G},keyup:function(G){var I=false;switch(G.keyCode){case 40:case 38:case 16:case 17:case 18:break;case 9:var J;if(J=this.getNextTab(),J!=null){var H=this;setTimeout(function(){H.setSelection(J.start,J.end)},500);I=true}else{var F=this.getSelection();if(F.start==F.end&&F.end==this.getContent().length){I=false}else{this.setSelection(this.getContent().length,this.getContent().length);I=true}}break;case 13:case 27:I=false;break;default:I=false}if(I){G.stopPropagation();G.preventDefault()}this.$options.onChange(this)},change:function(F){this.$options.onChange(this);return this},focus:function(F){var H=this.$options,G=H.hideable,I=this.$editor;I.addClass("active");E(document).find(".md-editor").each(function(){if(E(this).attr("id")!=I.attr("id")){var J;if(J=E(this).find("textarea").data("markdown"),J==null){J=E(this).find('div[data-provider="markdown-preview"]').data("markdown")}if(J){J.blur()}}});H.onFocus(this);return this},blur:function(F){var K=this.$options,G=K.hideable,H=this.$editor,I=this.$editable;if(H.hasClass("active")||this.$element.parent().length==0){H.removeClass("active");if(G){if(I.el!=null){var J=E("<"+I.type+"/>"),M=this.getContent(),L=(typeof markdown=="object")?markdown.toHTML(M):M;E(I.attrKeys).each(function(O,N){J.attr(I.attrKeys[O],I.attrValues[O])});J.html(L);H.replaceWith(J)}else{H.hide()}}K.onBlur(this)}return this}};var B=E.fn.markdown;E.fn.markdown=function(F){return this.each(function(){var G=E(this),I=G.data("markdown"),H=typeof F=="object"&&F;if(!I){G.data("markdown",(I=new A(this,H)))}})};E.fn.markdown.messages={};E.fn.markdown.defaults={autofocus:false,hideable:false,savable:false,width:"inherit",height:"inherit",resize:"none",iconlibrary:"glyph",language:"en",initialstate:"editor",buttons:[[{name:"groupFont",data:[{name:"cmdBold",hotkey:"Ctrl+B",title:"Bold",icon:{glyph:"glyphicon glyphicon-bold",fa:"fa fa-bold","fa-3":"icon-bold"},callback:function(G){var F,I,J=G.getSelection(),H=G.getContent();if(J.length==0){F=G.__localize("strong text")}else{F=J.text}if(H.substr(J.start-2,2)=="**"&&H.substr(J.end,2)=="**"){G.setSelection(J.start-2,J.end+2);G.replaceSelection(F);I=J.start-2}else{G.replaceSelection("**"+F+"**");I=J.start+2}G.setSelection(I,I+F.length)}},{name:"cmdItalic",title:"Italic",hotkey:"Ctrl+I",icon:{glyph:"glyphicon glyphicon-italic",fa:"fa fa-italic","fa-3":"icon-italic"},callback:function(G){var F,I,J=G.getSelection(),H=G.getContent();if(J.length==0){F=G.__localize("emphasized text")}else{F=J.text}if(H.substr(J.start-1,1)=="*"&&H.substr(J.end,1)=="*"){G.setSelection(J.start-1,J.end+1);G.replaceSelection(F);I=J.start-1}else{G.replaceSelection("*"+F+"*");I=J.start+1}G.setSelection(I,I+F.length)}},{name:"cmdHeading",title:"Heading",hotkey:"Ctrl+H",icon:{glyph:"glyphicon glyphicon-header",fa:"fa fa-font","fa-3":"icon-font"},callback:function(H){var F,J,L=H.getSelection(),I=H.getContent(),G,K;if(L.length==0){F=H.__localize("heading text")}else{F=L.text+"\n"}if((G=4,I.substr(L.start-G,G)=="### ")||(G=3,I.substr(L.start-G,G)=="###")){H.setSelection(L.start-G,L.end);H.replaceSelection(F);J=L.start-G}else{if(L.start>0&&(K=I.substr(L.start-1,1),!!K&&K!="\n")){H.replaceSelection("\n\n### "+F);J=L.start+6}else{H.replaceSelection("### "+F);J=L.start+4}}H.setSelection(J,J+F.length)}}]},{name:"groupLink",data:[{name:"cmdUrl",title:"URL/Link",hotkey:"Ctrl+L",icon:{glyph:"glyphicon glyphicon-link",fa:"fa fa-link","fa-3":"icon-link"},callback:function(G){var F,J,K=G.getSelection(),H=G.getContent(),I;if(K.length==0){F=G.__localize("enter link description here")}else{F=K.text}if("bootbox" in window){bootbox.prompt(G.__localize("Insert Hyperlink"),function(L){if(L!=null&&L!=""&&L!="http://"){G.replaceSelection("["+F+"]("+L+")");J=K.start+1;G.setSelection(J,J+F.length)}})}else{I=prompt(G.__localize("Insert Hyperlink"),"http://");if(I!=null&&I!=""&&I!="http://"){G.replaceSelection("["+F+"]("+I+")");J=K.start+1;G.setSelection(J,J+F.length)}}}},{name:"cmdImage",title:"Image",hotkey:"Ctrl+G",icon:{glyph:"glyphicon glyphicon-picture",fa:"fa fa-picture-o","fa-3":"icon-picture"},callback:function(G){var F,J,K=G.getSelection(),H=G.getContent(),I;if(K.length==0){F=G.__localize("enter image description here")}else{F=K.text}if("bootbox" in window){bootbox.prompt(G.__localize("Insert Image Hyperlink"),function(L){if(L!=null){G.replaceSelection("!["+F+"]("+L+' "'+G.__localize("enter image title here")+'")');J=K.start+2;G.setNextTab(G.__localize("enter image title here"));G.setSelection(J,J+F.length)}})}else{I=prompt(G.__localize("Insert Image Hyperlink"),"http://");if(I!=null){G.replaceSelection("!["+F+"]("+I+' "'+G.__localize("enter image title here")+'")');J=K.start+2;G.setNextTab(G.__localize("enter image title here"));G.setSelection(J,J+F.length)}}}}]},{name:"groupMisc",data:[{name:"cmdList",hotkey:"Ctrl+U",title:"Unordered List",icon:{glyph:"glyphicon glyphicon-list",fa:"fa fa-list","fa-3":"icon-list-ul"},callback:function(G){var F,J,K=G.getSelection(),H=G.getContent();if(K.length==0){F=G.__localize("list text here");G.replaceSelection("- "+F);J=K.start+2}else{if(K.text.indexOf("\n")<0){F=K.text;G.replaceSelection("- "+F);J=K.start+2}else{var I=[];I=K.text.split("\n");F=I[0];E.each(I,function(M,L){I[M]="- "+L});G.replaceSelection("\n\n"+I.join("\n"));J=K.start+4}}G.setSelection(J,J+F.length)}},{name:"cmdListO",hotkey:"Ctrl+O",title:"Ordered List",icon:{glyph:"glyphicon glyphicon-th-list",fa:"fa fa-list-ol","fa-3":"icon-list-ol"},callback:function(G){var F,J,K=G.getSelection(),H=G.getContent();if(K.length==0){F=G.__localize("list text here");G.replaceSelection("1. "+F);J=K.start+3}else{if(K.text.indexOf("\n")<0){F=K.text;G.replaceSelection("1. "+F);J=K.start+3}else{var I=[];I=K.text.split("\n");F=I[0];E.each(I,function(M,L){I[M]="1. "+L});G.replaceSelection("\n\n"+I.join("\n"));J=K.start+5}}G.setSelection(J,J+F.length)}},{name:"cmdCode",hotkey:"Ctrl+K",title:"Code",icon:{glyph:"glyphicon glyphicon-asterisk",fa:"fa fa-code","fa-3":"icon-code"},callback:function(G){var F,I,J=G.getSelection(),H=G.getContent();if(J.length==0){F=G.__localize("code text here")}else{F=J.text}if(H.substr(J.start-1,1)=="`"&&H.substr(J.end,1)=="`"){G.setSelection(J.start-1,J.end+1);G.replaceSelection(F);I=J.start-1}else{G.replaceSelection("`"+F+"`");I=J.start+1}G.setSelection(I,I+F.length)}},{name:"cmdQuote",hotkey:"Ctrl+Q",title:"Quote",icon:{glyph:"glyphicon glyphicon-comment",fa:"fa fa-quote-left","fa-3":"icon-quote-left"},callback:function(G){var F,J,K=G.getSelection(),H=G.getContent();if(K.length==0){F=G.__localize("quote here");G.replaceSelection("> "+F);J=K.start+2}else{if(K.text.indexOf("\n")<0){F=K.text;G.replaceSelection("> "+F);J=K.start+2}else{var I=[];I=K.text.split("\n");F=I[0];E.each(I,function(M,L){I[M]="> "+L});G.replaceSelection("\n\n"+I.join("\n"));J=K.start+4}}G.setSelection(J,J+F.length)}}]},{name:"groupUtil",data:[{name:"cmdPreview",toggle:true,hotkey:"Ctrl+P",title:"Preview",btnText:"Preview",btnClass:"btn btn-primary btn-sm",icon:{glyph:"glyphicon glyphicon-search",fa:"fa fa-search","fa-3":"icon-search"},callback:function(F){var G=F.$isPreview,H;if(G==false){F.showPreview()}else{F.hidePreview()}}}]}]],additionalButtons:[],reorderButtonGroups:[],hiddenButtons:[],disabledButtons:[],footer:"",onShow:function(F){},onPreview:function(F){},onSave:function(F){},onBlur:function(F){},onFocus:function(F){},onChange:function(F){}};E.fn.markdown.Constructor=A;E.fn.markdown.noConflict=function(){E.fn.markdown=B;return this};var C=function(F){var G=F;if(G.data("markdown")){G.data("markdown").showEditor();return}G.markdown()};var D=function(G){var H=false,F,I=E(G.currentTarget);if((G.type=="focusin"||G.type=="click")&&I.length==1&&typeof I[0]=="object"){F=I[0].activeElement;if(!E(F).data("markdown")){if(typeof E(F).parent().parent().parent().attr("class")=="undefined"||E(F).parent().parent().parent().attr("class").indexOf("md-editor")<0){if(typeof E(F).parent().parent().attr("class")=="undefined"||E(F).parent().parent().attr("class").indexOf("md-editor")<0){H=true}}else{H=false}}if(H){E(document).find(".md-editor").each(function(){var K=E(F).parent();if(E(this).attr("id")!=K.attr("id")){var J;if(J=E(this).find("textarea").data("markdown"),J==null){J=E(this).find('div[data-provider="markdown-preview"]').data("markdown")}if(J){J.blur()}}})}G.stopPropagation()}};E(document).on("click.markdown.data-api",'[data-provide="markdown-editable"]',function(F){C(E(this));F.preventDefault()}).on("click",function(F){D(F)}).on("focusin",function(F){D(F)}).ready(function(){E('textarea[data-provide="markdown"]').each(function(){C(E(this))})})}(window.jQuery);