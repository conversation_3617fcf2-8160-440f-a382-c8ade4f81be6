(function(A,B,C){(function(D){if(typeof define==="function"&&define.amd){define(["jquery"],D)}else{if(jQuery&&!jQuery.fn.sparkline){D(jQuery)}}}(function(W){var d={},l,J,X,K,D,E,f,j,Z,U,k,O,Y,g,T,h,a,Q,H,V,M,i,G,P,b,L,I,e,c,S,N,F,R=0;l=function(){return{common:{type:"line",lineColor:"#00f",fillColor:"#cdf",defaultPixelsPerValue:3,width:"auto",height:"auto",composite:false,tagValuesAttribute:"values",tagOptionsPrefix:"spark",enableTagOptions:false,enableHighlight:true,highlightLighten:1.4,tooltipSkipNull:true,tooltipPrefix:"",tooltipSuffix:"",disableHiddenCheck:false,numberFormatter:false,numberDigitGroupCount:3,numberDigitGroupSep:",",numberDecimalMark:".",disableTooltips:false,disableInteraction:false},line:{spotColor:"#f80",highlightSpotColor:"#5f5",highlightLineColor:"#f22",spotRadius:1.5,minSpotColor:"#f80",maxSpotColor:"#f80",lineWidth:1,normalRangeMin:C,normalRangeMax:C,normalRangeColor:"#ccc",drawNormalOnTop:false,chartRangeMin:C,chartRangeMax:C,chartRangeMinX:C,chartRangeMaxX:C,tooltipFormat:new X('<span style="color: {{color}}">&#9679;</span> {{prefix}}{{y}}{{suffix}}')},bar:{barColor:"#3366cc",negBarColor:"#f44",stackedBarColor:["#3366cc","#dc3912","#ff9900","#109618","#66aa00","#dd4477","#0099c6","#990099"],zeroColor:C,nullColor:C,zeroAxis:true,barWidth:4,barSpacing:1,chartRangeMax:C,chartRangeMin:C,chartRangeClip:false,colorMap:C,tooltipFormat:new X('<span style="color: {{color}}">&#9679;</span> {{prefix}}{{value}}{{suffix}}')},tristate:{barWidth:4,barSpacing:1,posBarColor:"#6f6",negBarColor:"#f44",zeroBarColor:"#999",colorMap:{},tooltipFormat:new X('<span style="color: {{color}}">&#9679;</span> {{value:map}}'),tooltipValueLookups:{map:{"-1":"Loss","0":"Draw","1":"Win"}}},discrete:{lineHeight:"auto",thresholdColor:C,thresholdValue:0,chartRangeMax:C,chartRangeMin:C,chartRangeClip:false,tooltipFormat:new X("{{prefix}}{{value}}{{suffix}}")},bullet:{targetColor:"#f33",targetWidth:3,performanceColor:"#33f",rangeColors:["#d3dafe","#a8b6ff","#7f94ff"],base:C,tooltipFormat:new X("{{fieldkey:fields}} - {{value}}"),tooltipValueLookups:{fields:{r:"Range",p:"Performance",t:"Target"}}},pie:{offset:0,sliceColors:["#3366cc","#dc3912","#ff9900","#109618","#66aa00","#dd4477","#0099c6","#990099"],borderWidth:0,borderColor:"#000",tooltipFormat:new X('<span style="color: {{color}}">&#9679;</span> {{value}} ({{percent.1}}%)')},box:{raw:false,boxLineColor:"#000",boxFillColor:"#cdf",whiskerColor:"#000",outlierLineColor:"#333",outlierFillColor:"#fff",medianColor:"#f00",showOutliers:true,outlierIQR:1.5,spotRadius:1.5,target:C,targetColor:"#4a2",chartRangeMax:C,chartRangeMin:C,tooltipFormat:new X("{{field:fields}}: {{value}}"),tooltipFormatFieldlistKey:"field",tooltipValueLookups:{fields:{lq:"Lower Quartile",med:"Median",uq:"Upper Quartile",lo:"Left Outlier",ro:"Right Outlier",lw:"Left Whisker",rw:"Right Whisker"}}}}};L='.jqstooltip { position: absolute;left: 0px;top: 0px;visibility: hidden;background: rgb(0, 0, 0) transparent;background-color: rgba(0,0,0,0.6);filter:progid:DXImageTransform.Microsoft.gradient(startColorstr=#99000000, endColorstr=#99000000);-ms-filter: "progid:DXImageTransform.Microsoft.gradient(startColorstr=#99000000, endColorstr=#99000000)";color: white;font: 10px arial, san serif;text-align: left;white-space: nowrap;padding: 5px;border: 1px solid white;z-index: 10000;}.jqsfield { color: white;font: 10px arial, san serif;text-align: left;}';J=function(){var m,n;m=function(){this.init.apply(this,arguments)};if(arguments.length>1){if(arguments[0]){m.prototype=W.extend(new arguments[0](),arguments[arguments.length-1]);m._super=arguments[0].prototype}else{m.prototype=arguments[arguments.length-1]}if(arguments.length>2){n=Array.prototype.slice.call(arguments,1,-1);n.unshift(m.prototype);W.extend.apply(W,n)}}else{m.prototype=arguments[0]}m.prototype.cls=m;return m};W.SPFormatClass=X=J({fre:/\{\{([\w.]+?)(:(.+?))?\}\}/g,precre:/(\w+)\.(\d+)/,init:function(m,n){this.format=m;this.fclass=n},render:function(t,m,q){var s=this,u=t,n,v,p,o,r;return this.format.replace(this.fre,function(){var w;v=arguments[1];p=arguments[3];n=s.precre.exec(v);if(n){r=n[2];v=n[1]}else{r=false}o=u[v];if(o===C){return""}if(p&&m&&m[p]){w=m[p];if(w.get){return m[p].get(o)||o}else{return m[p][o]||o}}if(Z(o)){if(q.get("numberFormatter")){o=q.get("numberFormatter")(o)}else{o=g(o,r,q.get("numberDigitGroupCount"),q.get("numberDigitGroupSep"),q.get("numberDecimalMark"))}}return o})}});W.spformat=function(m,n){return new X(m,n)};K=function(n,m,o){if(n<m){return m}if(n>o){return o}return n};D=function(o,n){var m;if(n===2){m=B.floor(o.length/2);return o.length%2?o[m]:(o[m-1]+o[m])/2}else{if(o.length%2){m=(o.length*n+n)/4;return m%1?(o[B.floor(m)]+o[B.floor(m)-1])/2:o[m-1]}else{m=(o.length*n+2)/4;return m%1?(o[B.floor(m)]+o[B.floor(m)-1])/2:o[m-1]}}};E=function(n){var m;switch(n){case"undefined":n=C;break;case"null":n=null;break;case"true":n=true;break;case"false":n=false;break;default:m=parseFloat(n);if(n==m){n=m}}return n};f=function(o){var m,n=[];for(m=o.length;m--;){n[m]=E(o[m])}return n};j=function(q,o){var m,n,p=[];for(m=0,n=q.length;m<n;m++){if(q[m]!==o){p.push(q[m])}}return p};Z=function(m){return !isNaN(parseFloat(m))&&isFinite(m)};g=function(r,q,m,t,s){var o,n;r=(q===false?parseFloat(r).toString():r.toFixed(q)).split("");o=(o=W.inArray(".",r))<0?r.length:o;if(o<r.length){r[o]=s}for(n=o-m;n>0;n-=m){r.splice(n,0,t)}return r.join("")};U=function(p,o,n){var m;for(m=o.length;m--;){if(n&&o[m]===null){continue}if(o[m]!==p){return false}}return true};k=function(o){var n=0,m;for(m=o.length;m--;){n+=typeof o[m]==="number"?o[m]:0}return n};Y=function(m){return W.isArray(m)?m:[m]};O=function(n){var m;if(A.createStyleSheet){A.createStyleSheet().cssText=n}else{m=A.createElement("style");m.type="text/css";A.getElementsByTagName("head")[0].appendChild(m);m[(typeof A.body.style.WebkitAppearance=="string")?"innerText":"innerHTML"]=n}};W.fn.simpledraw=function(r,n,o,q){var p,s;if(o&&(p=this.data("_jqs_vcanvas"))){return p}if(W.fn.sparkline.canvas===false){return false}else{if(W.fn.sparkline.canvas===C){var m=A.createElement("canvas");if(!!(m.getContext&&m.getContext("2d"))){W.fn.sparkline.canvas=function(w,t,v,u){return new S(w,t,v,u)}}else{if(A.namespaces&&!A.namespaces.v){A.namespaces.add("v","urn:schemas-microsoft-com:vml","#default#VML");W.fn.sparkline.canvas=function(w,t,v,u){return new N(w,t,v)}}else{W.fn.sparkline.canvas=false;return false}}}}if(r===C){r=W(this).innerWidth()}if(n===C){n=W(this).innerHeight()}p=W.fn.sparkline.canvas(r,n,this,q);s=W(this).data("_jqs_mhandler");if(s){s.registerCanvas(p)}return p};W.fn.cleardraw=function(){var m=this.data("_jqs_vcanvas");if(m){m.reset()}};W.RangeMapClass=T=J({init:function(n){var m,p,o=[];for(m in n){if(n.hasOwnProperty(m)&&typeof m==="string"&&m.indexOf(":")>-1){p=m.split(":");p[0]=p[0].length===0?-Infinity:parseFloat(p[0]);p[1]=p[1].length===0?Infinity:parseFloat(p[1]);p[2]=n[m];o.push(p)}}this.map=n;this.rangelist=o||false},get:function(p){var o=this.rangelist,m,q,n;if((n=this.map[p])!==C){return n}if(o){for(m=o.length;m--;){q=o[m];if(q[0]<=p&&q[1]>=p){return q[2]}}}return C}});W.range_map=function(m){return new T(m)};h=J({init:function(m,n){var o=W(m);this.$el=o;this.options=n;this.currentPageX=0;this.currentPageY=0;this.el=m;this.splist=[];this.tooltip=null;this.over=false;this.displayTooltips=!n.get("disableTooltips");this.highlightEnabled=!n.get("disableHighlight")},registerSparkline:function(m){this.splist.push(m);if(this.over){this.updateDisplay()}},registerCanvas:function(m){var n=W(m.canvas);this.canvas=m;this.$canvas=n;n.mouseenter(W.proxy(this.mouseenter,this));n.mouseleave(W.proxy(this.mouseleave,this));n.click(W.proxy(this.mouseclick,this))},reset:function(m){this.splist=[];if(this.tooltip&&m){this.tooltip.remove();this.tooltip=C}},mouseclick:function(m){var n=W.Event("sparklineClick");n.originalEvent=m;n.sparklines=this.splist;this.$el.trigger(n)},mouseenter:function(m){W(A.body).unbind("mousemove.jqs");W(A.body).bind("mousemove.jqs",W.proxy(this.mousemove,this));this.over=true;this.currentPageX=m.pageX;this.currentPageY=m.pageY;this.currentEl=m.target;if(!this.tooltip&&this.displayTooltips){this.tooltip=new a(this.options);this.tooltip.updatePosition(m.pageX,m.pageY)}this.updateDisplay()},mouseleave:function(){W(A.body).unbind("mousemove.jqs");var q=this.splist,o=q.length,m=false,p,n;this.over=false;this.currentEl=null;if(this.tooltip){this.tooltip.remove();this.tooltip=null}for(n=0;n<o;n++){p=q[n];if(p.clearRegionHighlight()){m=true}}if(m){this.canvas.render()}},mousemove:function(m){this.currentPageX=m.pageX;this.currentPageY=m.pageY;this.currentEl=m.target;if(this.tooltip){this.tooltip.updatePosition(m.pageX,m.pageY)}this.updateDisplay()},updateDisplay:function(){var p=this.splist,m=p.length,w=false,o=this.$canvas.offset(),t=this.currentPageX-o.left,q=this.currentPageY-o.top,v,n,s,r,u;if(!this.over){return}for(s=0;s<m;s++){n=p[s];r=n.setRegionHighlight(this.currentEl,t,q);if(r){w=true}}if(w){u=W.Event("sparklineRegionChange");u.sparklines=this.splist;this.$el.trigger(u);if(this.tooltip){v="";for(s=0;s<m;s++){n=p[s];v+=n.getCurrentRegionTooltip()}this.tooltip.setContent(v)}if(!this.disableHighlight){this.canvas.render()}}if(r===null){this.mouseleave()}}});a=J({sizeStyle:"position: static !important;display: block !important;visibility: hidden !important;float: left !important;",init:function(p){var n=p.get("tooltipClassname","jqstooltip"),o=this.sizeStyle,m;this.container=p.get("tooltipContainer")||A.body;this.tooltipOffsetX=p.get("tooltipOffsetX",10);this.tooltipOffsetY=p.get("tooltipOffsetY",12);W("#jqssizetip").remove();W("#jqstooltip").remove();this.sizetip=W("<div/>",{id:"jqssizetip",style:o,"class":n});this.tooltip=W("<div/>",{id:"jqstooltip","class":n}).appendTo(this.container);m=this.tooltip.offset();this.offsetLeft=m.left;this.offsetTop=m.top;this.hidden=true;W(window).unbind("resize.jqs scroll.jqs");W(window).bind("resize.jqs scroll.jqs",W.proxy(this.updateWindowDims,this));this.updateWindowDims()},updateWindowDims:function(){this.scrollTop=W(window).scrollTop();this.scrollLeft=W(window).scrollLeft();this.scrollRight=this.scrollLeft+W(window).width();this.updatePosition()},getSize:function(m){this.sizetip.html(m).appendTo(this.container);this.width=this.sizetip.width()+1;this.height=this.sizetip.height();this.sizetip.remove()},setContent:function(m){if(!m){this.tooltip.css("visibility","hidden");this.hidden=true;return}this.getSize(m);this.tooltip.html(m).css({"width":this.width,"height":this.height,"visibility":"visible"});if(this.hidden){this.hidden=false;this.updatePosition()}},updatePosition:function(m,n){if(m===C){if(this.mousex===C){return}m=this.mousex-this.offsetLeft;n=this.mousey-this.offsetTop}else{this.mousex=m=m-this.offsetLeft;this.mousey=n=n-this.offsetTop}if(!this.height||!this.width||this.hidden){return}n-=this.height+this.tooltipOffsetY;m+=this.tooltipOffsetX;if(n<this.scrollTop){n=this.scrollTop}if(m<this.scrollLeft){m=this.scrollLeft}else{if(m+this.width>this.scrollRight){m=this.scrollRight-this.width}}this.tooltip.css({"left":m,"top":n})},remove:function(){this.tooltip.remove();this.sizetip.remove();this.sizetip=this.tooltip=C;W(window).unbind("resize.jqs scroll.jqs")}});I=function(){O(L)};W(I);F=[];W.fn.sparkline=function(n,m){return this.each(function(){var r=new W.fn.sparkline.options(this,m),q=W(this),o,p;o=function(){var x,w,t,s,v,u,y;if(n==="html"||n===C){y=this.getAttribute(r.get("tagValuesAttribute"));if(y===C||y===null){y=q.html()}x=y.replace(/(^\s*<!--)|(-->\s*$)|\s+/g,"").split(",")}else{x=n}w=r.get("width")==="auto"?x.length*r.get("defaultPixelsPerValue"):r.get("width");if(r.get("height")==="auto"){if(!r.get("composite")||!W.data(this,"_jqs_vcanvas")){s=A.createElement("span");s.innerHTML="a";q.html(s);t=W(s).innerHeight()||W(s).height();W(s).remove();s=null}}else{t=r.get("height")}if(!r.get("disableInteraction")){v=W.data(this,"_jqs_mhandler");if(!v){v=new h(this,r);W.data(this,"_jqs_mhandler",v)}else{if(!r.get("composite")){v.reset()}}}else{v=false}if(r.get("composite")&&!W.data(this,"_jqs_vcanvas")){if(!W.data(this,"_jqs_errnotify")){alert("Attempted to attach a composite sparkline to an element with no existing sparkline");W.data(this,"_jqs_errnotify",true)}return}u=new W.fn.sparkline[r.get("type")](this,x,r,w,t);u.render();if(v){v.registerSparkline(u)}};if((W(this).html()&&!r.get("disableHiddenCheck")&&W(this).is(":hidden"))||!W(this).parents("body").length){if(!r.get("composite")&&W.data(this,"_jqs_pending")){for(p=F.length;p;p--){if(F[p-1][0]==this){F.splice(p-1,1)}}}F.push([this,o]);W.data(this,"_jqs_pending",true)}else{o.call(this)}})};W.fn.sparkline.defaults=l();W.sparkline_display_visible=function(){var m,n,o;var p=[];for(n=0,o=F.length;n<o;n++){m=F[n][0];if(W(m).is(":visible")&&!W(m).parents().is(":hidden")){F[n][1].call(m);W.data(F[n][0],"_jqs_pending",false);p.push(n)}else{if(!W(m).closest("html").length&&!W.data(m,"_jqs_pending")){W.data(F[n][0],"_jqs_pending",false);p.push(n)}}}for(n=p.length;n;n--){F.splice(p[n-1],1)}};W.fn.sparkline.options=J({init:function(m,q){var p,r,o,n;this.userOptions=q=q||{};this.tag=m;this.tagValCache={};r=W.fn.sparkline.defaults;o=r.common;this.tagOptionsPrefix=q.enableTagOptions&&(q.tagOptionsPrefix||o.tagOptionsPrefix);n=this.getTagSetting("type");if(n===d){p=r[q.type||o.type]}else{p=r[n]}this.mergedOptions=W.extend({},o,p,q)},getTagSetting:function(m){var o=this.tagOptionsPrefix,r,n,q,p;if(o===false||o===C){return d}if(this.tagValCache.hasOwnProperty(m)){r=this.tagValCache.key}else{r=this.tag.getAttribute(o+m);if(r===C||r===null){r=d}else{if(r.substr(0,1)==="["){r=r.substr(1,r.length-2).split(",");for(n=r.length;n--;){r[n]=E(r[n].replace(/(^\s*)|(\s*$)/g,""))}}else{if(r.substr(0,1)==="{"){q=r.substr(1,r.length-2).split(",");r={};for(n=q.length;n--;){p=q[n].split(":",2);r[p[0].replace(/(^\s*)|(\s*$)/g,"")]=E(p[1].replace(/(^\s*)|(\s*$)/g,""))}}else{r=E(r)}}}this.tagValCache.key=r}return r},get:function(m,o){var n=this.getTagSetting(m),p;if(n!==d){return n}return(p=this.mergedOptions[m])===C?o:p}});W.fn.sparkline._base=J({disabled:false,init:function(m,q,p,o,n){this.el=m;this.$el=W(m);this.values=q;this.options=p;this.width=o;this.height=n;this.currentRegion=C},initTarget:function(){var m=!this.options.get("disableInteraction");if(!(this.target=this.$el.simpledraw(this.width,this.height,this.options.get("composite"),m))){this.disabled=true}else{this.canvasWidth=this.target.pixelWidth;this.canvasHeight=this.target.pixelHeight}},render:function(){if(this.disabled){this.el.innerHTML="";return false}return true},getRegion:function(m,n){},setRegionHighlight:function(m,p,q){var n=this.currentRegion,o=!this.options.get("disableHighlight"),r;if(p>this.canvasWidth||q>this.canvasHeight||p<0||q<0){return null}r=this.getRegion(m,p,q);if(n!==r){if(n!==C&&o){this.removeHighlight()}this.currentRegion=r;if(r!==C&&o){this.renderHighlight()}return true}return false},clearRegionHighlight:function(){if(this.currentRegion!==C){this.removeHighlight();this.currentRegion=C;return true}return false},renderHighlight:function(){this.changeHighlight(true)},removeHighlight:function(){this.changeHighlight(false)},changeHighlight:function(m){},getCurrentRegionTooltip:function(){var Aa=this.options,u="",t=[],x,r,Ab,w,m,z,Ac,n,v,s,p,y,o,q;if(this.currentRegion===C){return""}x=this.getCurrentRegionFields();p=Aa.get("tooltipFormatter");if(p){return p(this,Aa,x)}if(Aa.get("tooltipChartTitle")){u+='<div class="jqs jqstitle">'+Aa.get("tooltipChartTitle")+"</div>\n"}r=this.options.get("tooltipFormat");if(!r){return""}if(!W.isArray(r)){r=[r]}if(!W.isArray(x)){x=[x]}Ac=this.options.get("tooltipFormatFieldlist");n=this.options.get("tooltipFormatFieldlistKey");if(Ac&&n){v=[];for(z=x.length;z--;){s=x[z][n];if((q=W.inArray(s,Ac))!=-1){v[q]=x[z]}}x=v}Ab=r.length;o=x.length;for(z=0;z<Ab;z++){y=r[z];if(typeof y==="string"){y=new X(y)}w=y.fclass||"jqsfield";for(q=0;q<o;q++){if(!x[q].isNull||!Aa.get("tooltipSkipNull")){W.extend(x[q],{prefix:Aa.get("tooltipPrefix"),suffix:Aa.get("tooltipSuffix")});m=y.render(x[q],Aa.get("tooltipValueLookups"),Aa);t.push('<div class="'+w+'">'+m+"</div>")}}}if(t.length){return u+t.join("\n")}return""},getCurrentRegionFields:function(){},calcHighlightColor:function(n,q){var p=q.get("highlightColor"),s=q.get("highlightLighten"),t,m,o,r;if(p){return p}if(s){t=/^#([0-9a-f])([0-9a-f])([0-9a-f])$/i.exec(n)||/^#([0-9a-f]{2})([0-9a-f]{2})([0-9a-f]{2})$/i.exec(n);if(t){o=[];m=n.length===4?16:1;for(r=0;r<3;r++){o[r]=K(B.round(parseInt(t[r+1],16)*m*s),0,255)}return"rgb("+o.join(",")+")"}}return n}});Q={changeHighlight:function(p){var o=this.currentRegion,q=this.target,m=this.regionShapes[o],n;if(m){n=this.renderRegion(o,p);if(W.isArray(n)||W.isArray(m)){q.replaceWithShapes(m,n);this.regionShapes[o]=W.map(n,function(r){return r.id})}else{q.replaceWithShape(m,n);this.regionShapes[o]=n.id}}},render:function(){var r=this.values,q=this.target,p=this.regionShapes,m,n,o,s;if(!this.cls._super.render.call(this)){return}for(o=r.length;o--;){m=this.renderRegion(o);if(m){if(W.isArray(m)){n=[];for(s=m.length;s--;){m[s].append();n.push(m[s].id)}p[o]=n}else{m.append();p[o]=m.id}}else{p[o]=null}}q.render()}};W.fn.sparkline.line=H=J(W.fn.sparkline._base,{type:"line",init:function(m,q,p,o,n){H._super.init.call(this,m,q,p,o,n);this.vertices=[];this.regionMap=[];this.xvalues=[];this.yvalues=[];this.yminmax=[];this.hightlightSpotId=null;this.lastShapeId=null;this.initTarget()},getRegion:function(m,p,q){var n,o=this.regionMap;for(n=o.length;n--;){if(o[n]!==null&&p>=o[n][0]&&p<=o[n][1]){return o[n][2]}}return C},getCurrentRegionFields:function(){var m=this.currentRegion;return{isNull:this.yvalues[m]===null,x:this.xvalues[m],y:this.yvalues[m],color:this.options.get("lineColor"),fillColor:this.options.get("fillColor"),offset:m}},renderHighlight:function(){var u=this.currentRegion,q=this.target,p=this.vertices[u],o=this.options,n=o.get("spotRadius"),m=o.get("highlightSpotColor"),t=o.get("highlightLineColor"),s,r;if(!p){return}if(n&&m){s=q.drawCircle(p[0],p[1],n,C,m);this.highlightSpotId=s.id;q.insertAfterShape(this.lastShapeId,s)}if(t){r=q.drawLine(p[0],this.canvasTop,p[0],this.canvasTop+this.canvasHeight,t);this.highlightLineId=r.id;q.insertAfterShape(this.lastShapeId,r)}},removeHighlight:function(){var m=this.target;if(this.highlightSpotId){m.removeShapeId(this.highlightSpotId);this.highlightSpotId=null}if(this.highlightLineId){m.removeShapeId(this.highlightLineId);this.highlightLineId=null}},scanValues:function(){var u=this.values,m=u.length,o=this.xvalues,q=this.yvalues,v=this.yminmax,t,s,n,p,r;for(t=0;t<m;t++){s=u[t];n=typeof(u[t])==="string";p=typeof(u[t])==="object"&&u[t] instanceof Array;r=n&&u[t].split(":");if(n&&r.length===2){o.push(Number(r[0]));q.push(Number(r[1]));v.push(Number(r[1]))}else{if(p){o.push(s[0]);q.push(s[1]);v.push(s[1])}else{o.push(t);if(u[t]===null||u[t]==="null"){q.push(null)}else{q.push(Number(s));v.push(Number(s))}}}}if(this.options.get("xvalues")){o=this.options.get("xvalues")}this.maxy=this.maxyorg=B.max.apply(B,v);this.miny=this.minyorg=B.min.apply(B,v);this.maxx=B.max.apply(B,o);this.minx=B.min.apply(B,o);this.xvalues=o;this.yvalues=q;this.yminmax=v},processRangeOptions:function(){var n=this.options,o=n.get("normalRangeMin"),m=n.get("normalRangeMax");if(o!==C){if(o<this.miny){this.miny=o}if(m>this.maxy){this.maxy=m}}if(n.get("chartRangeMin")!==C&&(n.get("chartRangeClip")||n.get("chartRangeMin")<this.miny)){this.miny=n.get("chartRangeMin")}if(n.get("chartRangeMax")!==C&&(n.get("chartRangeClip")||n.get("chartRangeMax")>this.maxy)){this.maxy=n.get("chartRangeMax")}if(n.get("chartRangeMinX")!==C&&(n.get("chartRangeClipX")||n.get("chartRangeMinX")<this.minx)){this.minx=n.get("chartRangeMinX")}if(n.get("chartRangeMaxX")!==C&&(n.get("chartRangeClipX")||n.get("chartRangeMaxX")>this.maxx)){this.maxx=n.get("chartRangeMaxX")}},drawNormalRange:function(o,t,s,p,n){var r=this.options.get("normalRangeMin"),q=this.options.get("normalRangeMax"),m=t+B.round(s-(s*((q-this.miny)/n))),u=B.round((s*(q-r))/n);this.target.drawRect(o,m,p,u,C,this.options.get("normalRangeColor")).append()},render:function(){var Ah=this.options,Ao=this.target,r=this.canvasWidth,Al=this.canvasHeight,Ad=this.vertices,t=Ah.get("spotRadius"),Ai=this.regionMap,z,Ap,n,Am,m,Ae,Ac,At,Aq,Ar,q,p,An,Ak,Ag,s,v,u,w,Af,As,o,Ab,Aa,Aj;if(!H._super.render.call(this)){return}this.scanValues();this.processRangeOptions();Ab=this.xvalues;Aa=this.yvalues;if(!this.yminmax.length||this.yvalues.length<2){return}Am=m=0;z=this.maxx-this.minx===0?1:this.maxx-this.minx;Ap=this.maxy-this.miny===0?1:this.maxy-this.miny;n=this.yvalues.length-1;if(t&&(r<(t*4)||Al<(t*4))){t=0}if(t){As=Ah.get("highlightSpotColor")&&!Ah.get("disableInteraction");if(As||Ah.get("minSpotColor")||(Ah.get("spotColor")&&Aa[n]===this.miny)){Al-=B.ceil(t)}if(As||Ah.get("maxSpotColor")||(Ah.get("spotColor")&&Aa[n]===this.maxy)){Al-=B.ceil(t);Am+=B.ceil(t)}if(As||((Ah.get("minSpotColor")||Ah.get("maxSpotColor"))&&(Aa[0]===this.miny||Aa[0]===this.maxy))){m+=B.ceil(t);r-=B.ceil(t)}if(As||Ah.get("spotColor")||(Ah.get("minSpotColor")||Ah.get("maxSpotColor")&&(Aa[n]===this.miny||Aa[n]===this.maxy))){r-=B.ceil(t)}}Al--;if(Ah.get("normalRangeMin")!==C&&!Ah.get("drawNormalOnTop")){this.drawNormalRange(m,Am,Al,r,Ap)}Ac=[];At=[Ac];Ak=Ag=null;s=Aa.length;for(Aj=0;Aj<s;Aj++){Aq=Ab[Aj];q=Ab[Aj+1];Ar=Aa[Aj];p=m+B.round((Aq-this.minx)*(r/z));An=Aj<s-1?m+B.round((q-this.minx)*(r/z)):r;Ag=p+((An-p)/2);Ai[Aj]=[Ak||0,Ag,Aj];Ak=Ag;if(Ar===null){if(Aj){if(Aa[Aj-1]!==null){Ac=[];At.push(Ac)}Ad.push(null)}}else{if(Ar<this.miny){Ar=this.miny}if(Ar>this.maxy){Ar=this.maxy}if(!Ac.length){Ac.push([p,Am+Al])}Ae=[p,Am+B.round(Al-(Al*((Ar-this.miny)/Ap)))];Ac.push(Ae);Ad.push(Ae)}}v=[];u=[];w=At.length;for(Aj=0;Aj<w;Aj++){Ac=At[Aj];if(Ac.length){if(Ah.get("fillColor")){Ac.push([Ac[Ac.length-1][0],(Am+Al)]);u.push(Ac.slice(0));Ac.pop()}if(Ac.length>2){Ac[0]=[Ac[0][0],Ac[1][1]]}v.push(Ac)}}w=u.length;for(Aj=0;Aj<w;Aj++){Ao.drawShape(u[Aj],Ah.get("fillColor"),Ah.get("fillColor")).append()}if(Ah.get("normalRangeMin")!==C&&Ah.get("drawNormalOnTop")){this.drawNormalRange(m,Am,Al,r,Ap)}w=v.length;for(Aj=0;Aj<w;Aj++){Ao.drawShape(v[Aj],Ah.get("lineColor"),C,Ah.get("lineWidth")).append()}if(t&&Ah.get("valueSpots")){Af=Ah.get("valueSpots");if(Af.get===C){Af=new T(Af)}for(Aj=0;Aj<s;Aj++){o=Af.get(Aa[Aj]);if(o){Ao.drawCircle(m+B.round((Ab[Aj]-this.minx)*(r/z)),Am+B.round(Al-(Al*((Aa[Aj]-this.miny)/Ap))),t,C,o).append()}}}if(t&&Ah.get("spotColor")&&Aa[n]!==null){Ao.drawCircle(m+B.round((Ab[Ab.length-1]-this.minx)*(r/z)),Am+B.round(Al-(Al*((Aa[n]-this.miny)/Ap))),t,C,Ah.get("spotColor")).append()}if(this.maxy!==this.minyorg){if(t&&Ah.get("minSpotColor")){Aq=Ab[W.inArray(this.minyorg,Aa)];Ao.drawCircle(m+B.round((Aq-this.minx)*(r/z)),Am+B.round(Al-(Al*((this.minyorg-this.miny)/Ap))),t,C,Ah.get("minSpotColor")).append()}if(t&&Ah.get("maxSpotColor")){Aq=Ab[W.inArray(this.maxyorg,Aa)];Ao.drawCircle(m+B.round((Aq-this.minx)*(r/z)),Am+B.round(Al-(Al*((this.maxyorg-this.miny)/Ap))),t,C,Ah.get("maxSpotColor")).append()}}this.lastShapeId=Ao.getLastShapeId();this.canvasTop=Am;Ao.render()}});W.fn.sparkline.bar=V=J(W.fn.sparkline._base,Q,{type:"bar",init:function(Ad,Af,q,s,u){var Ab=parseInt(q.get("barWidth"),10),Ak=parseInt(q.get("barSpacing"),10),v=q.get("chartRangeMin"),p=q.get("chartRangeMax"),At=q.get("chartRangeClip"),m=Infinity,Ai=-Infinity,Av,x,r,Aw,Ar,z,n,Ap,Am,t,Aj,Au,Ac,As,w,Ae,Aq,Ag,o,y,Ao,An,Ah;V._super.init.call(this,Ad,Af,q,s,u);for(z=0,n=Af.length;z<n;z++){y=Af[z];Av=typeof(y)==="string"&&y.indexOf(":")>-1;if(Av||W.isArray(y)){w=true;if(Av){y=Af[z]=f(y.split(":"))}y=j(y,null);x=B.min.apply(B,y);r=B.max.apply(B,y);if(x<m){m=x}if(r>Ai){Ai=r}}}this.stacked=w;this.regionShapes={};this.barWidth=Ab;this.barSpacing=Ak;this.totalBarWidth=Ab+Ak;this.width=s=(Af.length*Ab)+((Af.length-1)*Ak);this.initTarget();if(At){Ac=v===C?-Infinity:v;As=p===C?Infinity:p}Ar=[];Aw=w?[]:Ar;var Al=[];var Aa=[];for(z=0,n=Af.length;z<n;z++){if(w){Ae=Af[z];Af[z]=o=[];Al[z]=0;Aw[z]=Aa[z]=0;for(Aq=0,Ag=Ae.length;Aq<Ag;Aq++){y=o[Aq]=At?K(Ae[Aq],Ac,As):Ae[Aq];if(y!==null){if(y>0){Al[z]+=y}if(m<0&&Ai>0){if(y<0){Aa[z]+=B.abs(y)}else{Aw[z]+=y}}else{Aw[z]+=B.abs(y-(y<0?Ai:m))}Ar.push(y)}}}else{y=At?K(Af[z],Ac,As):Af[z];y=Af[z]=E(y);if(y!==null){Ar.push(y)}}}this.max=Au=B.max.apply(B,Ar);this.min=Aj=B.min.apply(B,Ar);this.stackMax=Ai=w?B.max.apply(B,Al):Au;this.stackMin=m=w?B.min.apply(B,Ar):Aj;if(q.get("chartRangeMin")!==C&&(q.get("chartRangeClip")||q.get("chartRangeMin")<Aj)){Aj=q.get("chartRangeMin")}if(q.get("chartRangeMax")!==C&&(q.get("chartRangeClip")||q.get("chartRangeMax")>Au)){Au=q.get("chartRangeMax")}this.zeroAxis=Am=q.get("zeroAxis",true);if(Aj<=0&&Au>=0&&Am){t=0}else{if(Am==false){t=Aj}else{if(Aj>0){t=Aj}else{t=Au}}}this.xaxisOffset=t;Ap=w?(B.max.apply(B,Aw)+B.max.apply(B,Aa)):Au-Aj;this.canvasHeightEf=(Am&&Aj<0)?this.canvasHeight-2:this.canvasHeight-1;if(Aj<t){An=(w&&Au>=0)?Ai:Au;Ao=(An-t)/Ap*this.canvasHeight;if(Ao!==B.ceil(Ao)){this.canvasHeightEf-=2;Ao=B.ceil(Ao)}}else{Ao=this.canvasHeight}this.yoffset=Ao;if(W.isArray(q.get("colorMap"))){this.colorMapByIndex=q.get("colorMap");this.colorMapByValue=null}else{this.colorMapByIndex=null;this.colorMapByValue=q.get("colorMap");if(this.colorMapByValue&&this.colorMapByValue.get===C){this.colorMapByValue=new T(this.colorMapByValue)}}this.range=Ap},getRegion:function(m,o,p){var n=B.floor(o/this.totalBarWidth);return(n<0||n>=this.values.length)?C:n},getCurrentRegionFields:function(){var o=this.currentRegion,q=Y(this.values[o]),n=[],p,m;for(m=q.length;m--;){p=q[m];n.push({isNull:p===null,value:p,color:this.calcColor(m,p,o),offset:o})}return n},calcColor:function(r,n,s){var t=this.colorMapByIndex,p=this.colorMapByValue,q=this.options,o,m;if(this.stacked){o=q.get("stackedBarColor")}else{o=(n<0)?q.get("negBarColor"):q.get("barColor")}if(n===0&&q.get("zeroColor")!==C){o=q.get("zeroColor")}if(p&&(m=p.get(n))){o=m}else{if(t&&t.length>s){o=t[s]}}return W.isArray(o)?o[r%o.length]:o},renderRegion:function(Af,z){var Aj=this.values[Af],Ab=this.options,s=this.xaxisOffset,q=[],o=this.range,r=this.stacked,Ad=this.target,u=Af*this.totalBarWidth,v=this.canvasHeightEf,Ac=this.yoffset,Ag,Ah,t,m,Ai,Ae,w,Aa,p,n;Aj=W.isArray(Aj)?Aj:[Aj];w=Aj.length;Aa=Aj[0];m=U(null,Aj);n=U(s,Aj,true);if(m){if(Ab.get("nullColor")){t=z?Ab.get("nullColor"):this.calcHighlightColor(Ab.get("nullColor"),Ab);Ag=(Ac>0)?Ac-1:Ac;return Ad.drawRect(u,Ag,this.barWidth-1,0,t,t)}else{return C}}Ai=Ac;for(Ae=0;Ae<w;Ae++){Aa=Aj[Ae];if(r&&Aa===s){if(!n||p){continue}p=true}if(o>0){Ah=B.floor(v*((B.abs(Aa-s)/o)))+1}else{Ah=1}if(Aa<s||(Aa===s&&Ac===0)){Ag=Ai;Ai+=Ah}else{Ag=Ac-Ah;Ac-=Ah}t=this.calcColor(Ae,Aa,Af);if(z){t=this.calcHighlightColor(t,Ab)}q.push(Ad.drawRect(u,Ag,this.barWidth-1,Ah-1,t,t))}if(q.length===1){return q[0]}return q}});W.fn.sparkline.tristate=M=J(W.fn.sparkline._base,Q,{type:"tristate",init:function(m,q,p,o,n){var s=parseInt(p.get("barWidth"),10),r=parseInt(p.get("barSpacing"),10);M._super.init.call(this,m,q,p,o,n);this.regionShapes={};this.barWidth=s;this.barSpacing=r;this.totalBarWidth=s+r;this.values=W.map(q,Number);this.width=o=(q.length*s)+((q.length-1)*r);if(W.isArray(p.get("colorMap"))){this.colorMapByIndex=p.get("colorMap");this.colorMapByValue=null}else{this.colorMapByIndex=null;this.colorMapByValue=p.get("colorMap");if(this.colorMapByValue&&this.colorMapByValue.get===C){this.colorMapByValue=new T(this.colorMapByValue)}}this.initTarget()},getRegion:function(m,n,o){return B.floor(n/this.totalBarWidth)},getCurrentRegionFields:function(){var m=this.currentRegion;return{isNull:this.values[m]===C,value:this.values[m],color:this.calcColor(this.values[m],m),offset:m}},calcColor:function(o,s){var q=this.values,p=this.options,t=this.colorMapByIndex,r=this.colorMapByValue,n,m;if(r&&(m=r.get(o))){n=m}else{if(t&&t.length>s){n=t[s]}else{if(q[s]<0){n=p.get("negBarColor")}else{if(q[s]>0){n=p.get("posBarColor")}else{n=p.get("zeroBarColor")}}}}return n},renderRegion:function(v,q){var s=this.values,o=this.options,r=this.target,u,n,t,m,w,p;u=r.pixelHeight;t=B.round(u/2);m=v*this.totalBarWidth;if(s[v]<0){w=t;n=t-1}else{if(s[v]>0){w=0;n=t-1}else{w=t-1;n=2}}p=this.calcColor(s[v],v);if(p===null){return}if(q){p=this.calcHighlightColor(p,o)}return r.drawRect(m,w,this.barWidth-1,n-1,p,p)}});W.fn.sparkline.discrete=i=J(W.fn.sparkline._base,Q,{type:"discrete",init:function(m,q,p,o,n){i._super.init.call(this,m,q,p,o,n);this.regionShapes={};this.values=q=W.map(q,Number);this.min=B.min.apply(B,q);this.max=B.max.apply(B,q);this.range=this.max-this.min;this.width=o=p.get("width")==="auto"?q.length*2:this.width;this.interval=B.floor(o/q.length);this.itemWidth=o/q.length;if(p.get("chartRangeMin")!==C&&(p.get("chartRangeClip")||p.get("chartRangeMin")<this.min)){this.min=p.get("chartRangeMin")}if(p.get("chartRangeMax")!==C&&(p.get("chartRangeClip")||p.get("chartRangeMax")>this.max)){this.max=p.get("chartRangeMax")}this.initTarget();if(this.target){this.lineHeight=p.get("lineHeight")==="auto"?B.round(this.canvasHeight*0.3):p.get("lineHeight")}},getRegion:function(m,n,o){return B.floor(n/this.itemWidth)},getCurrentRegionFields:function(){var m=this.currentRegion;return{isNull:this.values[m]===C,value:this.values[m],offset:m}},renderRegion:function(Ac,s){var y=this.values,u=this.options,o=this.min,p=this.max,Ab=this.range,v=this.interval,q=this.target,Aa=this.canvasHeight,r=this.lineHeight,t=Aa-r,m,w,n,z;w=K(y[Ac],o,p);z=Ac*v;m=B.round(t-t*((w-o)/Ab));n=(u.get("thresholdColor")&&w<u.get("thresholdValue"))?u.get("thresholdColor"):u.get("lineColor");if(s){n=this.calcHighlightColor(n,u)}return q.drawLine(z,m,z,m+r,n)}});W.fn.sparkline.bullet=G=J(W.fn.sparkline._base,{type:"bullet",init:function(p,s,q,r,m){var n,o,t;G._super.init.call(this,p,s,q,r,m);this.values=s=f(s);t=s.slice();t[0]=t[0]===null?t[2]:t[0];t[1]=s[1]===null?t[2]:t[1];n=B.min.apply(B,s);o=B.max.apply(B,s);if(q.get("base")===C){n=n<0?n:0}else{n=q.get("base")}this.min=n;this.max=o;this.range=o-n;this.shapes={};this.valueShapes={};this.regiondata={};this.width=r=q.get("width")==="auto"?"4.0em":r;this.target=this.$el.simpledraw(r,m,q.get("composite"));if(!s.length){this.disabled=true}this.initTarget()},getRegion:function(m,o,p){var n=this.target.getShapeAt(m,o,p);return(n!==C&&this.shapes[n]!==C)?this.shapes[n]:C},getCurrentRegionFields:function(){var m=this.currentRegion;return{fieldkey:m.substr(0,1),value:this.values[m.substr(1)],region:m}},changeHighlight:function(o){var m=this.currentRegion,n=this.valueShapes[m],p;delete this.shapes[n];switch(m.substr(0,1)){case"r":p=this.renderRange(m.substr(1),o);break;case"p":p=this.renderPerformance(o);break;case"t":p=this.renderTarget(o);break}this.valueShapes[m]=p.id;this.shapes[p.id]=m;this.target.replaceWithShape(n,p)},renderRange:function(n,o){var m=this.values[n],q=B.round(this.canvasWidth*((m-this.min)/this.range)),p=this.options.get("rangeColors")[n-2];if(o){p=this.calcHighlightColor(p,this.options)}return this.target.drawRect(0,0,q-1,this.canvasHeight-1,p,p)},renderPerformance:function(n){var m=this.values[1],p=B.round(this.canvasWidth*((m-this.min)/this.range)),o=this.options.get("performanceColor");if(n){o=this.calcHighlightColor(o,this.options)}return this.target.drawRect(0,B.round(this.canvasHeight*0.3),p-1,B.round(this.canvasHeight*0.4)-1,o,o)},renderTarget:function(p){var q=this.values[0],o=B.round(this.canvasWidth*((q-this.min)/this.range)-(this.options.get("targetWidth")/2)),m=B.round(this.canvasHeight*0.1),n=this.canvasHeight-(m*2),r=this.options.get("targetColor");if(p){r=this.calcHighlightColor(r,this.options)}return this.target.drawRect(o,m,this.options.get("targetWidth")-1,n-1,r,r)},render:function(){var m=this.values.length,o=this.target,n,p;if(!G._super.render.call(this)){return}for(n=2;n<m;n++){p=this.renderRange(n).append();this.shapes[p.id]="r"+n;this.valueShapes["r"+n]=p.id}if(this.values[1]!==null){p=this.renderPerformance().append();this.shapes[p.id]="p1";this.valueShapes.p1=p.id}if(this.values[0]!==null){p=this.renderTarget().append();this.shapes[p.id]="t0";this.valueShapes.t0=p.id}o.render()}});W.fn.sparkline.pie=P=J(W.fn.sparkline._base,{type:"pie",init:function(m,s,r,o,n){var p=0,q;P._super.init.call(this,m,s,r,o,n);this.shapes={};this.valueShapes={};this.values=s=W.map(s,Number);if(r.get("width")==="auto"){this.width=this.height}if(s.length>0){for(q=s.length;q--;){p+=s[q]}}this.total=p;this.initTarget();this.radius=B.floor(B.min(this.canvasWidth,this.canvasHeight)/2)},getRegion:function(m,o,p){var n=this.target.getShapeAt(m,o,p);return(n!==C&&this.shapes[n]!==C)?this.shapes[n]:C},getCurrentRegionFields:function(){var m=this.currentRegion;return{isNull:this.values[m]===C,value:this.values[m],percent:this.values[m]/this.total*100,color:this.options.get("sliceColors")[m%this.options.get("sliceColors").length],offset:m}},changeHighlight:function(p){var n=this.currentRegion,m=this.renderSlice(n,p),o=this.valueShapes[n];delete this.shapes[o];this.target.replaceWithShape(o,m);this.valueShapes[n]=m.id;this.shapes[m.id]=n},renderSlice:function(z,s){var x=this.target,v=this.options,w=this.radius,p=v.get("borderWidth"),n=v.get("offset"),o=2*B.PI,u=this.values,r=this.total,Aa=n?(2*B.PI)*(n/360):0,y,Ab,t,q,m;q=u.length;for(t=0;t<q;t++){y=Aa;Ab=Aa;if(r>0){Ab=Aa+(o*(u[t]/r))}if(z===t){m=v.get("sliceColors")[t%v.get("sliceColors").length];if(s){m=this.calcHighlightColor(m,v)}return x.drawPieSlice(w,w,w-p,y,Ab,C,m)}Aa=Ab}},render:function(){var r=this.target,o=this.values,p=this.options,q=this.radius,n=p.get("borderWidth"),s,m;if(!P._super.render.call(this)){return}if(n){r.drawCircle(q,q,B.floor(q-(n/2)),p.get("borderColor"),C,n).append()}for(m=o.length;m--;){if(o[m]){s=this.renderSlice(m).append();this.valueShapes[m]=s.id;this.shapes[s.id]=m}}r.render()}});W.fn.sparkline.box=b=J(W.fn.sparkline._base,{type:"box",init:function(m,q,p,o,n){b._super.init.call(this,m,q,p,o,n);this.values=W.map(q,Number);this.width=p.get("width")==="auto"?"4.0em":o;this.initTarget();if(!this.values.length){this.disabled=1}},getRegion:function(){return 1},getCurrentRegionFields:function(){var m=[{field:"lq",value:this.quartiles[0]},{field:"med",value:this.quartiles[1]},{field:"uq",value:this.quartiles[2]}];if(this.loutlier!==C){m.push({field:"lo",value:this.loutlier})}if(this.routlier!==C){m.push({field:"ro",value:this.routlier})}if(this.lwhisker!==C){m.push({field:"lw",value:this.lwhisker})}if(this.rwhisker!==C){m.push({field:"rw",value:this.rwhisker})}return m},render:function(){var Ad=this.target,y=this.values,Ae=y.length,x=this.options,r=this.canvasWidth,z=this.canvasHeight,u=x.get("chartRangeMin")===C?B.min.apply(B,y):x.get("chartRangeMin"),n=x.get("chartRangeMax")===C?B.max.apply(B,y):x.get("chartRangeMax"),m=0,Ab,q,s,Af,o,w,Ac,p,t,v,Aa;if(!b._super.render.call(this)){return}if(x.get("raw")){if(x.get("showOutliers")&&y.length>5){q=y[0];Ab=y[1];Af=y[2];o=y[3];w=y[4];Ac=y[5];p=y[6]}else{Ab=y[0];Af=y[1];o=y[2];w=y[3];Ac=y[4]}}else{y.sort(function(Ah,Ag){return Ah-Ag});Af=D(y,1);o=D(y,2);w=D(y,3);s=w-Af;if(x.get("showOutliers")){Ab=Ac=C;for(t=0;t<Ae;t++){if(Ab===C&&y[t]>Af-(s*x.get("outlierIQR"))){Ab=y[t]}if(y[t]<w+(s*x.get("outlierIQR"))){Ac=y[t]}}q=y[0];p=y[Ae-1]}else{Ab=y[0];Ac=y[Ae-1]}}this.quartiles=[Af,o,w];this.lwhisker=Ab;this.rwhisker=Ac;this.loutlier=q;this.routlier=p;Aa=r/(n-u+1);if(x.get("showOutliers")){m=B.ceil(x.get("spotRadius"));r-=2*B.ceil(x.get("spotRadius"));Aa=r/(n-u+1);if(q<Ab){Ad.drawCircle((q-u)*Aa+m,z/2,x.get("spotRadius"),x.get("outlierLineColor"),x.get("outlierFillColor")).append()}if(p>Ac){Ad.drawCircle((p-u)*Aa+m,z/2,x.get("spotRadius"),x.get("outlierLineColor"),x.get("outlierFillColor")).append()}}Ad.drawRect(B.round((Af-u)*Aa+m),B.round(z*0.1),B.round((w-Af)*Aa),B.round(z*0.8),x.get("boxLineColor"),x.get("boxFillColor")).append();Ad.drawLine(B.round((Ab-u)*Aa+m),B.round(z/2),B.round((Af-u)*Aa+m),B.round(z/2),x.get("lineColor")).append();Ad.drawLine(B.round((Ab-u)*Aa+m),B.round(z/4),B.round((Ab-u)*Aa+m),B.round(z-z/4),x.get("whiskerColor")).append();Ad.drawLine(B.round((Ac-u)*Aa+m),B.round(z/2),B.round((w-u)*Aa+m),B.round(z/2),x.get("lineColor")).append();Ad.drawLine(B.round((Ac-u)*Aa+m),B.round(z/4),B.round((Ac-u)*Aa+m),B.round(z-z/4),x.get("whiskerColor")).append();Ad.drawLine(B.round((o-u)*Aa+m),B.round(z*0.1),B.round((o-u)*Aa+m),B.round(z*0.9),x.get("medianColor")).append();if(x.get("target")){v=B.ceil(x.get("spotRadius"));Ad.drawLine(B.round((x.get("target")-u)*Aa+m),B.round((z/2)-v),B.round((x.get("target")-u)*Aa+m),B.round((z/2)+v),x.get("targetColor")).append();Ad.drawLine(B.round((x.get("target")-u)*Aa+m-v),B.round(z/2),B.round((x.get("target")-u)*Aa+m+v),B.round(z/2),x.get("targetColor")).append()}Ad.render()}});e=J({init:function(o,p,m,n){this.target=o;this.id=p;this.type=m;this.args=n},append:function(){this.target.appendShape(this);return this}});c=J({_pxregex:/(\d+)(px)?\s*$/i,init:function(o,m,n){if(!o){return}this.width=o;this.height=m;this.target=n;this.lastShapeId=null;if(n[0]){n=n[0]}W.data(n,"_jqs_vcanvas",this)},drawLine:function(q,r,m,n,o,p){return this.drawShape([[q,r],[m,n]],o,p)},drawShape:function(p,m,o,n){return this._genShape("Shape",[p,m,o,n])},drawCircle:function(n,o,p,q,r,m){return this._genShape("Circle",[n,o,p,q,r,m])},drawPieSlice:function(o,p,q,n,m,r,s){return this._genShape("PieSlice",[o,p,q,n,m,r,s])},drawRect:function(n,o,p,m,q,r){return this._genShape("Rect",[n,o,p,m,q,r])},getElement:function(){return this.canvas},getLastShapeId:function(){return this.lastShapeId},reset:function(){alert("reset not implemented")},_insert:function(m,n){W(n).html(m)},_calculatePixelDims:function(p,m,o){var n;n=this._pxregex.exec(m);if(n){this.pixelHeight=n[1]}else{this.pixelHeight=W(o).height()}n=this._pxregex.exec(p);if(n){this.pixelWidth=n[1]}else{this.pixelWidth=W(o).width()}},_genShape:function(n,m){var o=R++;m.unshift(o);return new e(this,o,n,m)},appendShape:function(m){alert("appendShape not implemented")},replaceWithShape:function(m,n){alert("replaceWithShape not implemented")},insertAfterShape:function(m,n){alert("insertAfterShape not implemented")},removeShapeId:function(m){alert("removeShapeId not implemented")},getShapeAt:function(m,n,o){alert("getShapeAt not implemented")},render:function(){alert("render not implemented")}});S=J(c,{init:function(p,m,o,n){S._super.init.call(this,p,m,o);this.canvas=A.createElement("canvas");if(o[0]){o=o[0]}W.data(o,"_jqs_vcanvas",this);W(this.canvas).css({display:"inline-block",width:p,height:m,verticalAlign:"top"});this._insert(this.canvas,o);this._calculatePixelDims(p,m,this.canvas);this.canvas.width=this.pixelWidth;this.canvas.height=this.pixelHeight;this.interact=n;this.shapes={};this.shapeseq=[];this.currentTargetShapeId=C;W(this.canvas).css({width:this.pixelWidth,height:this.pixelHeight})},_getContext:function(n,p,o){var m=this.canvas.getContext("2d");if(n!==C){m.strokeStyle=n}m.lineWidth=o===C?1:o;if(p!==C){m.fillStyle=p}return m},reset:function(){var m=this._getContext();m.clearRect(0,0,this.pixelWidth,this.pixelHeight);this.shapes={};this.shapeseq=[];this.currentTargetShapeId=C},_drawShape:function(s,r,t,m,q){var o=this._getContext(t,m,q),p,n;o.beginPath();o.moveTo(r[0][0]+0.5,r[0][1]+0.5);for(p=1,n=r.length;p<n;p++){o.lineTo(r[p][0]+0.5,r[p][1]+0.5)}if(t!==C){o.stroke()}if(m!==C){o.fill()}if(this.targetX!==C&&this.targetY!==C&&o.isPointInPath(this.targetX,this.targetY)){this.currentTargetShapeId=s}},_drawCircle:function(t,r,s,p,o,m,q){var n=this._getContext(o,m,q);n.beginPath();n.arc(r,s,p,0,2*B.PI,false);if(this.targetX!==C&&this.targetY!==C&&n.isPointInPath(this.targetX,this.targetY)){this.currentTargetShapeId=t}if(o!==C){n.stroke()}if(m!==C){n.fill()}},_drawPieSlice:function(t,r,s,q,u,m,p,o){var n=this._getContext(p,o);n.beginPath();n.moveTo(r,s);n.arc(r,s,q,u,m,false);n.lineTo(r,s);n.closePath();if(p!==C){n.stroke()}if(o){n.fill()}if(this.targetX!==C&&this.targetY!==C&&n.isPointInPath(this.targetX,this.targetY)){this.currentTargetShapeId=t}},_drawRect:function(n,o,p,q,m,r,s){return this._drawShape(n,[[o,p],[o+q,p],[o+q,p+m],[o,p+m],[o,p]],r,s)},appendShape:function(m){this.shapes[m.id]=m;this.shapeseq.push(m.id);this.lastShapeId=m.id;return m.id},replaceWithShape:function(o,p){var n=this.shapeseq,m;this.shapes[p.id]=p;for(m=n.length;m--;){if(n[m]==o){n[m]=p.id}}delete this.shapes[o]},replaceWithShapes:function(n,p){var o=this.shapeseq,s={},q,m,r;for(m=n.length;m--;){s[n[m]]=true}for(m=o.length;m--;){q=o[m];if(s[q]){o.splice(m,1);delete this.shapes[q];r=m}}for(m=p.length;m--;){o.splice(r,0,p[m].id);this.shapes[p[m].id]=p[m]}},insertAfterShape:function(o,p){var n=this.shapeseq,m;for(m=n.length;m--;){if(n[m]===o){n.splice(m+1,0,p.id);this.shapes[p.id]=p;return}}},removeShapeId:function(o){var n=this.shapeseq,m;for(m=n.length;m--;){if(n[m]===o){n.splice(m,1);break}}delete this.shapes[o]},getShapeAt:function(m,n,o){this.targetX=n;this.targetY=o;this.render();return this.currentTargetShapeId},render:function(){var p=this.shapeseq,r=this.shapes,n=p.length,m=this._getContext(),q,s,o;m.clearRect(0,0,this.pixelWidth,this.pixelHeight);for(o=0;o<n;o++){q=p[o];s=r[q];this["_draw"+s.type].apply(this,s.args)}if(!this.interact){this.shapes={};this.shapeseq=[]}}});N=J(c,{init:function(p,m,o){var n;N._super.init.call(this,p,m,o);if(o[0]){o=o[0]}W.data(o,"_jqs_vcanvas",this);this.canvas=A.createElement("span");W(this.canvas).css({display:"inline-block",position:"relative",overflow:"hidden",width:p,height:m,margin:"0px",padding:"0px",verticalAlign:"top"});this._insert(this.canvas,o);this._calculatePixelDims(p,m,this.canvas);this.canvas.width=this.pixelWidth;this.canvas.height=this.pixelHeight;n='<v:group coordorigin="0 0" coordsize="'+this.pixelWidth+" "+this.pixelHeight+'" style="position:absolute;top:0;left:0;width:'+this.pixelWidth+"px;height="+this.pixelHeight+'px;"></v:group>';this.canvas.insertAdjacentHTML("beforeEnd",n);this.group=W(this.canvas).children()[0];this.rendered=false;this.prerender=""},_drawShape:function(v,u,w,n,s){var x=[],m,y,o,t,p,q,r;for(r=0,q=u.length;r<q;r++){x[r]=""+(u[r][0])+","+(u[r][1])}m=x.splice(0,1);s=s===C?1:s;y=w===C?' stroked="false" ':' strokeWeight="'+s+'px" strokeColor="'+w+'" ';o=n===C?' filled="false"':' fillColor="'+n+'" filled="true" ';t=x[0]===x[x.length-1]?"x ":"";p='<v:shape coordorigin="0 0" coordsize="'+this.pixelWidth+" "+this.pixelHeight+'"  id="jqsshape'+v+'" '+y+o+' style="position:absolute;left:0px;top:0px;height:'+this.pixelHeight+"px;width:"+this.pixelWidth+'px;padding:0px;margin:0px;"  path="m '+m+" l "+x.join(", ")+" "+t+'e"> </v:shape>';return p},_drawCircle:function(u,s,t,q,p,m,r){var v,n,o;s-=q;t-=q;v=p===C?' stroked="false" ':' strokeWeight="'+r+'px" strokeColor="'+p+'" ';n=m===C?' filled="false"':' fillColor="'+m+'" filled="true" ';o='<v:oval  id="jqsshape'+u+'" '+v+n+' style="position:absolute;top:'+t+"px; left:"+s+"px; width:"+(q*2)+"px; height:"+(q*2)+'px"></v:oval>';return o},_drawPieSlice:function(Ab,z,Aa,u,Ad,m,s,r){var v,Ac,p,t,w,q,n,o;if(Ad===m){return""}if((m-Ad)===(2*B.PI)){Ad=0;m=(2*B.PI)}Ac=z+B.round(B.cos(Ad)*u);p=Aa+B.round(B.sin(Ad)*u);t=z+B.round(B.cos(m)*u);w=Aa+B.round(B.sin(m)*u);if(Ac===t&&p===w){if((m-Ad)<B.PI){return""}Ac=t=z+u;p=w=Aa}if(Ac===t&&p===w&&(m-Ad)<B.PI){return""}v=[z-u,Aa-u,z+u,Aa+u,Ac,p,t,w];q=s===C?' stroked="false" ':' strokeWeight="1px" strokeColor="'+s+'" ';n=r===C?' filled="false"':' fillColor="'+r+'" filled="true" ';o='<v:shape coordorigin="0 0" coordsize="'+this.pixelWidth+" "+this.pixelHeight+'"  id="jqsshape'+Ab+'" '+q+n+' style="position:absolute;left:0px;top:0px;height:'+this.pixelHeight+"px;width:"+this.pixelWidth+'px;padding:0px;margin:0px;"  path="m '+z+","+Aa+" wa "+v.join(", ")+' x e"> </v:shape>';return o},_drawRect:function(n,o,p,q,m,r,s){return this._drawShape(n,[[o,p],[o,p+m],[o+q,p+m],[o+q,p],[o,p]],r,s)},reset:function(){this.group.innerHTML=""},appendShape:function(n){var m=this["_draw"+n.type].apply(this,n.args);if(this.rendered){this.group.insertAdjacentHTML("beforeEnd",m)}else{this.prerender+=m}this.lastShapeId=n.id;return n.id},replaceWithShape:function(m,p){var o=W("#jqsshape"+m),n=this["_draw"+p.type].apply(this,p.args);o[0].outerHTML=n},replaceWithShapes:function(n,o){var p=W("#jqsshape"+n[0]),q="",r=o.length,m;for(m=0;m<r;m++){q+=this["_draw"+o[m].type].apply(this,o[m].args)}p[0].outerHTML=q;for(m=1;m<n.length;m++){W("#jqsshape"+n[m]).remove()}},insertAfterShape:function(m,p){var o=W("#jqsshape"+m),n=this["_draw"+p.type].apply(this,p.args);o[0].insertAdjacentHTML("afterEnd",n)},removeShapeId:function(m){var n=W("#jqsshape"+m);this.group.removeChild(n[0])},getShapeAt:function(m,o,p){var n=m.id.substr(8);return n},render:function(){if(!this.rendered){this.group.innerHTML=this.prerender;this.rendered=true}}})}))}(document,Math));