(function(A,B){if(typeof exports==="object"){module.exports=B(require("jquery"))}else{if(typeof define==="function"&&define.amd){define(["jquery"],B)}else{B(A.jQuery)}}}(this,function(C){var B=function(H,K){var G;var D=document.createElement("canvas");H.appendChild(D);if(typeof(G_vmlCanvasManager)!=="undefined"){G_vmlCanvasManager.initElement(D)}var I=D.getContext("2d");D.width=D.height=K.size;var F=1;if(window.devicePixelRatio>1){F=window.devicePixelRatio;D.style.width=D.style.height=[K.size,"px"].join("");D.width=D.height=K.size*F;I.scale(F,F)}I.translate(K.size/2,K.size/2);I.rotate((-1/2+K.rotate/180)*Math.PI);var L=(K.size-K.lineWidth)/2;if(K.scaleColor&&K.scaleLength){L-=K.scaleLength+2}Date.now=Date.now||function(){return +(new Date())};var M=function(R,Q,O){O=Math.min(Math.max(-1,O||0),1);var P=O<=0?true:false;I.beginPath();I.arc(0,0,L,0,Math.PI*2*O,P);I.strokeStyle=R;I.lineWidth=Q;I.stroke()};var E=function(){var O;var P;I.lineWidth=1;I.fillStyle=K.scaleColor;I.save();for(var Q=24;Q>0;--Q){if(Q%6===0){P=K.scaleLength;O=0}else{P=K.scaleLength*0.6;O=K.scaleLength-P}I.fillRect(-K.size/2+O,0,P,1);I.rotate(Math.PI/12)}I.restore()};var N=(function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(O){window.setTimeout(O,1000/60)}}());var J=function(){if(K.scaleColor){E()}if(K.trackColor){M(K.trackColor,K.lineWidth,1)}};this.getCanvas=function(){return D};this.getCtx=function(){return I};this.clear=function(){I.clearRect(K.size/-2,K.size/-2,K.size,K.size)};this.draw=function(O){if(!!K.scaleColor||!!K.trackColor){if(I.getImageData&&I.putImageData){if(!G){J();G=I.getImageData(0,0,K.size*F,K.size*F)}else{I.putImageData(G,0,0)}}else{this.clear();J()}}else{this.clear()}I.lineCap=K.lineCap;var P;if(typeof(K.barColor)==="function"){P=K.barColor(O)}else{P=K.barColor}M(P,K.lineWidth,O/100)}.bind(this);this.animate=function(O,P){var Q=Date.now();K.onStart(O,P);var R=function(){var T=Math.min(Date.now()-Q,K.animate.duration);var S=K.easing(this,T,O,P-O,K.animate.duration);this.draw(S);K.onStep(O,P,S);if(T>=K.animate.duration){K.onStop(O,P)}else{N(R)}}.bind(this);N(R)}.bind(this)};var A=function(D,E){var H={barColor:"#ef1e25",trackColor:"#f9f9f9",scaleColor:"#dfe0e0",scaleLength:5,lineCap:"round",lineWidth:3,size:110,rotate:0,animate:{duration:1000,enabled:true},easing:function(K,L,M,N,J){L=L/(J/2);if(L<1){return N/2*L*L+M}return -N/2*((--L)*(L-2)-1)+M},onStart:function(J,K){return},onStep:function(J,K,L){return},onStop:function(J,K){return}};if(typeof(B)!=="undefined"){H.renderer=B}else{if(typeof(SVGRenderer)!=="undefined"){H.renderer=SVGRenderer}else{throw new Error("Please load either the SVG- or the CanvasRenderer")}}var G={};var I=0;var F=function(){this.el=D;this.options=G;for(var J in H){if(H.hasOwnProperty(J)){G[J]=E&&typeof(E[J])!=="undefined"?E[J]:H[J];if(typeof(G[J])==="function"){G[J]=G[J].bind(this)}}}if(typeof(G.easing)==="string"&&typeof(jQuery)!=="undefined"&&jQuery.isFunction(jQuery.easing[G.easing])){G.easing=jQuery.easing[G.easing]}else{G.easing=H.easing}if(typeof(G.animate)==="number"){G.animate={duration:G.animate,enabled:true}}if(typeof(G.animate)==="boolean"&&!G.animate){G.animate={duration:1000,enabled:G.animate}}this.renderer=new G.renderer(D,G);this.renderer.draw(I);if(D.dataset&&D.dataset.percent){this.update(parseFloat(D.dataset.percent))}else{if(D.getAttribute&&D.getAttribute("data-percent")){this.update(parseFloat(D.getAttribute("data-percent")))}}D.style["width"]=D.style["height"]=G.size+"px";D.style["lineHeight"]=(G.size-1)+"px"}.bind(this);this.update=function(J){J=parseFloat(J);if(G.animate.enabled){this.renderer.animate(I,J)}else{this.renderer.draw(J)}I=J;return this}.bind(this);this.disableAnimation=function(){G.animate.enabled=false;return this};this.enableAnimation=function(){G.animate.enabled=true;return this};F()};C.fn.easyPieChart=function(D){return this.each(function(){var E;if(!C.data(this,"easyPieChart")){E=C.extend({},D,C(this).data());C.data(this,"easyPieChart",new A(this,E))}})}}));