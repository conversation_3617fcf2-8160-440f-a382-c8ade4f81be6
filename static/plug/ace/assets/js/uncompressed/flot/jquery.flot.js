(function(B){B.color={};B.color.make=function(D,C,F,G){var E={};E.r=D||0;E.g=C||0;E.b=F||0;E.a=G!=null?G:1;E.add=function(J,H){for(var I=0;I<J.length;++I){E[J.charAt(I)]+=H}return E.normalize()};E.scale=function(I,J){for(var H=0;H<I.length;++H){E[I.charAt(H)]*=J}return E.normalize()};E.toString=function(){if(E.a>=1){return"rgb("+[E.r,E.g,E.b].join(",")+")"}else{return"rgba("+[E.r,E.g,E.b,E.a].join(",")+")"}};E.normalize=function(){function H(I,J,K){return J<I?I:J>K?K:J}E.r=H(0,parseInt(E.r),255);E.g=H(0,parseInt(E.g),255);E.b=H(0,parseInt(E.b),255);E.a=H(0,E.a,1);return E};E.clone=function(){return B.color.make(E.r,E.b,E.g,E.a)};return E.normalize()};B.color.extract=function(C,D){var E;do{E=C.css(D).toLowerCase();if(E!=""&&E!="transparent"){break}C=C.parent()}while(C.length&&!B.nodeName(C.get(0),"body"));if(E=="rgba(0, 0, 0, 0)"){E="transparent"}return B.color.parse(E)};B.color.parse=function(D){var C,E=B.color.make;if(C=/rgb\(\s*([0-9]{1,3})\s*,\s*([0-9]{1,3})\s*,\s*([0-9]{1,3})\s*\)/.exec(D)){return E(parseInt(C[1],10),parseInt(C[2],10),parseInt(C[3],10))}if(C=/rgba\(\s*([0-9]{1,3})\s*,\s*([0-9]{1,3})\s*,\s*([0-9]{1,3})\s*,\s*([0-9]+(?:\.[0-9]+)?)\s*\)/.exec(D)){return E(parseInt(C[1],10),parseInt(C[2],10),parseInt(C[3],10),parseFloat(C[4]))}if(C=/rgb\(\s*([0-9]+(?:\.[0-9]+)?)\%\s*,\s*([0-9]+(?:\.[0-9]+)?)\%\s*,\s*([0-9]+(?:\.[0-9]+)?)\%\s*\)/.exec(D)){return E(parseFloat(C[1])*2.55,parseFloat(C[2])*2.55,parseFloat(C[3])*2.55)}if(C=/rgba\(\s*([0-9]+(?:\.[0-9]+)?)\%\s*,\s*([0-9]+(?:\.[0-9]+)?)\%\s*,\s*([0-9]+(?:\.[0-9]+)?)\%\s*,\s*([0-9]+(?:\.[0-9]+)?)\s*\)/.exec(D)){return E(parseFloat(C[1])*2.55,parseFloat(C[2])*2.55,parseFloat(C[3])*2.55,parseFloat(C[4]))}if(C=/#([a-fA-F0-9]{2})([a-fA-F0-9]{2})([a-fA-F0-9]{2})/.exec(D)){return E(parseInt(C[1],16),parseInt(C[2],16),parseInt(C[3],16))}if(C=/#([a-fA-F0-9])([a-fA-F0-9])([a-fA-F0-9])/.exec(D)){return E(parseInt(C[1]+C[1],16),parseInt(C[2]+C[2],16),parseInt(C[3]+C[3],16))}var F=B.trim(D).toLowerCase();if(F=="transparent"){return E(255,255,255,0)}else{C=A[F]||[0,0,0];return E(C[0],C[1],C[2])}};var A={aqua:[0,255,255],azure:[240,255,255],beige:[245,245,220],black:[0,0,0],blue:[0,0,255],brown:[165,42,42],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgrey:[169,169,169],darkgreen:[0,100,0],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkviolet:[148,0,211],fuchsia:[255,0,255],gold:[255,215,0],green:[0,128,0],indigo:[75,0,130],khaki:[240,230,140],lightblue:[173,216,230],lightcyan:[224,255,255],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightyellow:[255,255,224],lime:[0,255,0],magenta:[255,0,255],maroon:[128,0,0],navy:[0,0,128],olive:[128,128,0],orange:[255,165,0],pink:[255,192,203],purple:[128,0,128],violet:[128,0,128],red:[255,0,0],silver:[192,192,192],white:[255,255,255],yellow:[255,255,0]}})(jQuery);(function(E){var C=Object.prototype.hasOwnProperty;if(!E.fn.detach){E.fn.detach=function(){return this.each(function(){if(this.parentNode){this.parentNode.removeChild(this)}})}}function D(G,H){var I=H.children("."+G)[0];if(I==null){I=document.createElement("canvas");I.className=G;E(I).css({direction:"ltr",position:"absolute",left:0,top:0}).appendTo(H);if(!I.getContext){if(window.G_vmlCanvasManager){I=window.G_vmlCanvasManager.initElement(I)}else{throw new Error("Canvas is not available. If you're using IE with a fall-back such as Excanvas, then there's either a mistake in your conditional include, or the page has no DOCTYPE and is rendering in Quirks Mode.")}}}this.element=I;var F=this.context=I.getContext("2d");var K=window.devicePixelRatio||1,J=F.webkitBackingStorePixelRatio||F.mozBackingStorePixelRatio||F.msBackingStorePixelRatio||F.oBackingStorePixelRatio||F.backingStorePixelRatio||1;this.pixelRatio=K/J;this.resize(H.width(),H.height());this.textContainer=null;this.text={};this._textCache={}}D.prototype.resize=function(J,G){if(J<=0||G<=0){throw new Error("Invalid dimensions for plot, width = "+J+", height = "+G)}var H=this.element,F=this.context,I=this.pixelRatio;if(this.width!=J){H.width=J*I;H.style.width=J+"px";this.width=J}if(this.height!=G){H.height=G*I;H.style.height=G+"px";this.height=G}F.restore();F.save();F.scale(I,I)};D.prototype.clear=function(){this.context.clearRect(0,0,this.width,this.height)};D.prototype.render=function(){var O=this._textCache;for(var H in O){if(C.call(O,H)){var K=this.getTextLayer(H),F=O[H];K.hide();for(var M in F){if(C.call(F,M)){var N=F[M];for(var J in N){if(C.call(N,J)){var G=N[J].positions;for(var I=0,L;L=G[I];I++){if(L.active){if(!L.rendered){K.append(L.element);L.rendered=true}}else{G.splice(I--,1);if(L.rendered){L.element.detach()}}}if(G.length==0){delete N[J]}}}}}K.show()}}};D.prototype.getTextLayer=function(G){var F=this.text[G];if(F==null){if(this.textContainer==null){this.textContainer=E("<div class='flot-text'></div>").css({position:"absolute",top:0,left:0,bottom:0,right:0,"font-size":"smaller",color:"#545454"}).insertAfter(this.element)}F=this.text[G]=E("<div></div>").addClass(G).css({position:"absolute",top:0,left:0,bottom:0,right:0}).appendTo(this.textContainer)}return F};D.prototype.getTextInfo=function(O,J,H,L,K){var I,F,M,N;J=""+J;if(typeof H==="object"){I=H.style+" "+H.variant+" "+H.weight+" "+H.size+"px/"+H.lineHeight+"px "+H.family}else{I=H}F=this._textCache[O];if(F==null){F=this._textCache[O]={}}M=F[I];if(M==null){M=F[I]={}}N=M[J];if(N==null){var G=E("<div></div>").html(J).css({position:"absolute","max-width":K,top:-9999}).appendTo(this.getTextLayer(O));if(typeof H==="object"){G.css({font:I,color:H.color})}else{if(typeof H==="string"){G.addClass(H)}}N=M[J]={width:G.outerWidth(true),height:G.outerHeight(true),element:G,positions:[]};G.detach()}return N};D.prototype.addText=function(R,O,P,K,I,N,M,G,F){var Q=this.getTextInfo(R,K,I,N,M),H=Q.positions;if(G=="center"){O-=Q.width/2}else{if(G=="right"){O-=Q.width}}if(F=="middle"){P-=Q.height/2}else{if(F=="bottom"){P-=Q.height}}for(var J=0,L;L=H[J];J++){if(L.x==O&&L.y==P){L.active=true;return}}L={active:true,rendered:false,element:H.length?Q.element.clone():Q.element,x:O,y:P};H.push(L);L.element.css({top:Math.round(P),left:Math.round(O),"text-align":G})};D.prototype.removeText=function(R,P,Q,L,H,O){if(L==null){var F=this._textCache[R];if(F!=null){for(var N in F){if(C.call(F,N)){var M=F[N];for(var K in M){if(C.call(M,K)){var G=M[K].positions;for(var J=0,I;I=G[J];J++){I.active=false}}}}}}}else{var G=this.getTextInfo(R,L,H,O).positions;for(var J=0,I;I=G[J];J++){if(I.x==P&&I.y==Q){I.active=false}}}};function A(Aj,w,J,m){var Ag=[],S={colors:["#edc240","#afd8f8","#cb4b4b","#4da74d","#9440ed"],legend:{show:true,noColumns:1,labelFormatter:null,labelBoxBorderColor:"#ccc",container:null,position:"ne",margin:5,backgroundColor:null,backgroundOpacity:0.85,sorted:null},xaxis:{show:null,position:"bottom",mode:null,font:null,color:null,tickColor:null,transform:null,inverseTransform:null,min:null,max:null,autoscaleMargin:null,ticks:null,tickFormatter:null,labelWidth:null,labelHeight:null,reserveSpace:null,tickLength:null,alignTicksWithAxis:null,tickDecimals:null,tickSize:null,minTickSize:null},yaxis:{autoscaleMargin:0.02,position:"left"},xaxes:[],yaxes:[],series:{points:{show:false,radius:3,lineWidth:2,fill:true,fillColor:"#ffffff",symbol:"circle"},lines:{lineWidth:2,fill:false,fillColor:null,steps:false},bars:{show:false,lineWidth:2,barWidth:1,fill:true,fillColor:null,align:"left",horizontal:false,zero:true},shadowSize:3,highlightColor:null},grid:{show:true,aboveData:false,color:"#545454",backgroundColor:null,borderColor:null,tickColor:null,margin:0,labelMargin:5,axisMargin:8,borderWidth:2,minBorderMargin:null,markings:null,markingsColor:"#f4f4f4",markingsLineWidth:2,clickable:false,hoverable:false,autoHighlight:true,mouseActiveRadius:10},interaction:{redrawOverlayInterval:1000/60},hooks:{}},Aw=null,x=null,Au=null,Y=null,T=null,i=[],Am=[],o={left:0,right:0,top:0,bottom:0},Ai=0,Ap=0,k={processOptions:[],processRawData:[],processDatapoints:[],processOffset:[],drawBackground:[],drawSeries:[],draw:[],bindEvents:[],drawOverlay:[],shutdown:[]},Ah=this;Ah.setData=l;Ah.setupGrid=Ao;Ah.draw=M;Ah.getPlaceholder=function(){return Aj};Ah.getCanvas=function(){return Aw.element};Ah.getPlotOffset=function(){return o};Ah.width=function(){return Ai};Ah.height=function(){return Ap};Ah.offset=function(){var Ax=Au.offset();Ax.left+=o.left;Ax.top+=o.top;return Ax};Ah.getData=function(){return Ag};Ah.getAxes=function(){var Ay={},Ax;E.each(i.concat(Am),function(AA,Az){if(Az){Ay[Az.direction+(Az.n!=1?Az.n:"")+"axis"]=Az}});return Ay};Ah.getXAxes=function(){return i};Ah.getYAxes=function(){return Am};Ah.c2p=Al;Ah.p2c=b;Ah.getOptions=function(){return S};Ah.highlight=H;Ah.unhighlight=R;Ah.triggerRedrawOverlay=V;Ah.pointOffset=function(Ax){return{left:parseInt(i[h(Ax,"x")-1].p2c(+Ax.x)+o.left,10),top:parseInt(Am[h(Ax,"y")-1].p2c(+Ax.y)+o.top,10)}};Ah.shutdown=U;Ah.destroy=function(){U();Aj.removeData("plot").empty();Ag=[];S=null;Aw=null;x=null;Au=null;Y=null;T=null;i=[];Am=[];k=null;r=[];Ah=null};Ah.resize=function(){var Ay=Aj.width(),Ax=Aj.height();Aw.resize(Ay,Ax);x.resize(Ay,Ax)};Ah.hooks=k;v(Ah);At(J);z();l(w);Ao();M();N();function f(Ay,Az){Az=[Ah].concat(Az);for(var Ax=0;Ax<Ay.length;++Ax){Ay[Ax].apply(this,Az)}}function v(){var Az={Canvas:D};for(var Ax=0;Ax<m.length;++Ax){var Ay=m[Ax];Ay.init(Ah,Az);if(Ay.options){E.extend(true,S,Ay.options)}}}function At(AB){E.extend(true,S,AB);if(AB&&AB.colors){S.colors=AB.colors}if(S.xaxis.color==null){S.xaxis.color=E.color.parse(S.grid.color).scale("a",0.22).toString()}if(S.yaxis.color==null){S.yaxis.color=E.color.parse(S.grid.color).scale("a",0.22).toString()}if(S.xaxis.tickColor==null){S.xaxis.tickColor=S.grid.tickColor||S.xaxis.color}if(S.yaxis.tickColor==null){S.yaxis.tickColor=S.grid.tickColor||S.yaxis.color}if(S.grid.borderColor==null){S.grid.borderColor=S.grid.color}if(S.grid.tickColor==null){S.grid.tickColor=E.color.parse(S.grid.color).scale("a",0.22).toString()}var AA,Ax,Ay,AC=Aj.css("font-size"),Az=AC?+AC.replace("px",""):13,AE={style:Aj.css("font-style"),size:Math.round(0.8*Az),variant:Aj.css("font-variant"),weight:Aj.css("font-weight"),family:Aj.css("font-family")};Ay=S.xaxes.length||1;for(AA=0;AA<Ay;++AA){Ax=S.xaxes[AA];if(Ax&&!Ax.tickColor){Ax.tickColor=Ax.color}Ax=E.extend(true,{},S.xaxis,Ax);S.xaxes[AA]=Ax;if(Ax.font){Ax.font=E.extend({},AE,Ax.font);if(!Ax.font.color){Ax.font.color=Ax.color}if(!Ax.font.lineHeight){Ax.font.lineHeight=Math.round(Ax.font.size*1.15)}}}Ay=S.yaxes.length||1;for(AA=0;AA<Ay;++AA){Ax=S.yaxes[AA];if(Ax&&!Ax.tickColor){Ax.tickColor=Ax.color}Ax=E.extend(true,{},S.yaxis,Ax);S.yaxes[AA]=Ax;if(Ax.font){Ax.font=E.extend({},AE,Ax.font);if(!Ax.font.color){Ax.font.color=Ax.color}if(!Ax.font.lineHeight){Ax.font.lineHeight=Math.round(Ax.font.size*1.15)}}}if(S.xaxis.noTicks&&S.xaxis.ticks==null){S.xaxis.ticks=S.xaxis.noTicks}if(S.yaxis.noTicks&&S.yaxis.ticks==null){S.yaxis.ticks=S.yaxis.noTicks}if(S.x2axis){S.xaxes[1]=E.extend(true,{},S.xaxis,S.x2axis);S.xaxes[1].position="top";if(S.x2axis.min==null){S.xaxes[1].min=null}if(S.x2axis.max==null){S.xaxes[1].max=null}}if(S.y2axis){S.yaxes[1]=E.extend(true,{},S.yaxis,S.y2axis);S.yaxes[1].position="right";if(S.y2axis.min==null){S.yaxes[1].min=null}if(S.y2axis.max==null){S.yaxes[1].max=null}}if(S.grid.coloredAreas){S.grid.markings=S.grid.coloredAreas}if(S.grid.coloredAreasColor){S.grid.markingsColor=S.grid.coloredAreasColor}if(S.lines){E.extend(true,S.series.lines,S.lines)}if(S.points){E.extend(true,S.series.points,S.points)}if(S.bars){E.extend(true,S.series.bars,S.bars)}if(S.shadowSize!=null){S.series.shadowSize=S.shadowSize}if(S.highlightColor!=null){S.series.highlightColor=S.highlightColor}for(AA=0;AA<S.xaxes.length;++AA){Ab(i,AA+1).options=S.xaxes[AA]}for(AA=0;AA<S.yaxes.length;++AA){Ab(Am,AA+1).options=S.yaxes[AA]}for(var AD in k){if(S.hooks[AD]&&S.hooks[AD].length){k[AD]=k[AD].concat(S.hooks[AD])}}f(k.processOptions,[S])}function l(Ax){Ag=Ad(Ax);Ak();c()}function Ad(Ax){var AA=[];for(var Ay=0;Ay<Ax.length;++Ay){var Az=E.extend(true,{},S.series);if(Ax[Ay].data!=null){Az.data=Ax[Ay].data;delete Ax[Ay].data;E.extend(true,Az,Ax[Ay]);Ax[Ay].data=Az.data}else{Az.data=Ax[Ay]}AA.push(Az)}return AA}function h(Ax,Ay){var Az=Ax[Ay+"axis"];if(typeof Az=="object"){Az=Az.n}if(typeof Az!="number"){Az=1}return Az}function L(){return E.grep(i.concat(Am),function(Ax){return Ax})}function Al(Ay){var Az={},Ax,AA;for(Ax=0;Ax<i.length;++Ax){AA=i[Ax];if(AA&&AA.used){Az["x"+AA.n]=AA.c2p(Ay.left)}}for(Ax=0;Ax<Am.length;++Ax){AA=Am[Ax];if(AA&&AA.used){Az["y"+AA.n]=AA.c2p(Ay.top)}}if(Az.x1!==undefined){Az.x=Az.x1}if(Az.y1!==undefined){Az.y=Az.y1}return Az}function b(Az){var AA={},Ay,AB,Ax;for(Ay=0;Ay<i.length;++Ay){AB=i[Ay];if(AB&&AB.used){Ax="x"+AB.n;if(Az[Ax]==null&&AB.n==1){Ax="x"}if(Az[Ax]!=null){AA.left=AB.p2c(Az[Ax]);break}}}for(Ay=0;Ay<Am.length;++Ay){AB=Am[Ay];if(AB&&AB.used){Ax="y"+AB.n;if(Az[Ax]==null&&AB.n==1){Ax="y"}if(Az[Ax]!=null){AA.top=AB.p2c(Az[Ax]);break}}}return AA}function Ab(Ax,Ay){if(!Ax[Ay-1]){Ax[Ay-1]={n:Ay,direction:Ax==i?"x":"y",options:E.extend(true,{},Ax==i?S.xaxis:S.yaxis)}}return Ax[Ay-1]}function Ak(){var Az=Ag.length,AF=-1,AE;for(AE=0;AE<Ag.length;++AE){var AC=Ag[AE].color;if(AC!=null){Az--;if(typeof AC=="number"&&AC>AF){AF=AC}}}if(Az<=AF){Az=AF+1}var AG,AH=[],AI=S.colors,AB=AI.length,AD=0;for(AE=0;AE<Az;AE++){AG=E.color.parse(AI[AE%AB]||"#666");if(AE%AB==0&&AE){if(AD>=0){if(AD<0.5){AD=-AD-0.2}else{AD=0}}else{AD=-AD}}AH[AE]=AG.scale("rgb",1+AD)}var Ax=0,Ay;for(AE=0;AE<Ag.length;++AE){Ay=Ag[AE];if(Ay.color==null){Ay.color=AH[Ax].toString();++Ax}else{if(typeof Ay.color=="number"){Ay.color=AH[Ay.color].toString()}}if(Ay.lines.show==null){var AA,AJ=true;for(AA in Ay){if(Ay[AA]&&Ay[AA].show){AJ=false;break}}if(AJ){Ay.lines.show=true}}if(Ay.lines.zero==null){Ay.lines.zero=!!Ay.lines.fill}Ay.xaxis=Ab(i,h(Ay,"x"));Ay.yaxis=Ab(Am,h(Ay,"y"))}}function c(){var AD=Number.POSITIVE_INFINITY,AL=Number.NEGATIVE_INFINITY,AG=Number.MAX_VALUE,AE,AB,AC,AF,Ax,AO,AJ,Ay,AV,AW,AM,AQ,AH,Az,AT,AP;function AN(A1,AZ,A0){if(AZ<A1.datamin&&AZ!=-AG){A1.datamin=AZ}if(A0>A1.datamax&&A0!=AG){A1.datamax=A0}}E.each(L(),function(A0,AZ){AZ.datamin=AD;AZ.datamax=AL;AZ.used=false});for(AE=0;AE<Ag.length;++AE){AO=Ag[AE];AO.datapoints={points:[]};f(k.processRawData,[AO,AO.data,AO.datapoints])}for(AE=0;AE<Ag.length;++AE){AO=Ag[AE];AT=AO.data;AP=AO.datapoints.format;if(!AP){AP=[];AP.push({x:true,number:true,required:true});AP.push({y:true,number:true,required:true});if(AO.bars.show||(AO.lines.show&&AO.lines.fill)){var AS=!!((AO.bars.show&&AO.bars.zero)||(AO.lines.show&&AO.lines.zero));AP.push({y:true,number:true,required:false,defaultValue:0,autoscale:AS});if(AO.bars.horizontal){delete AP[AP.length-1].y;AP[AP.length-1].x=true}}AO.datapoints.format=AP}if(AO.datapoints.pointsize!=null){continue}AO.datapoints.pointsize=AP.length;Ay=AO.datapoints.pointsize;AJ=AO.datapoints.points;var AI=AO.lines.show&&AO.lines.steps;AO.xaxis.used=AO.yaxis.used=true;for(AB=AC=0;AB<AT.length;++AB,AC+=Ay){Az=AT[AB];var AR=Az==null;if(!AR){for(AF=0;AF<Ay;++AF){AQ=Az[AF];AH=AP[AF];if(AH){if(AH.number&&AQ!=null){AQ=+AQ;if(isNaN(AQ)){AQ=null}else{if(AQ==Infinity){AQ=AG}else{if(AQ==-Infinity){AQ=-AG}}}}if(AQ==null){if(AH.required){AR=true}if(AH.defaultValue!=null){AQ=AH.defaultValue}}}AJ[AC+AF]=AQ}}if(AR){for(AF=0;AF<Ay;++AF){AQ=AJ[AC+AF];if(AQ!=null){AH=AP[AF];if(AH.autoscale!==false){if(AH.x){AN(AO.xaxis,AQ,AQ)}if(AH.y){AN(AO.yaxis,AQ,AQ)}}}AJ[AC+AF]=null}}else{if(AI&&AC>0&&AJ[AC-Ay]!=null&&AJ[AC-Ay]!=AJ[AC]&&AJ[AC-Ay+1]!=AJ[AC+1]){for(AF=0;AF<Ay;++AF){AJ[AC+Ay+AF]=AJ[AC+AF]}AJ[AC+1]=AJ[AC-Ay+1];AC+=Ay}}}}for(AE=0;AE<Ag.length;++AE){AO=Ag[AE];f(k.processDatapoints,[AO,AO.datapoints])}for(AE=0;AE<Ag.length;++AE){AO=Ag[AE];AJ=AO.datapoints.points;Ay=AO.datapoints.pointsize;AP=AO.datapoints.format;var AA=AD,AX=AD,AK=AL,AU=AL;for(AB=0;AB<AJ.length;AB+=Ay){if(AJ[AB]==null){continue}for(AF=0;AF<Ay;++AF){AQ=AJ[AB+AF];AH=AP[AF];if(!AH||AH.autoscale===false||AQ==AG||AQ==-AG){continue}if(AH.x){if(AQ<AA){AA=AQ}if(AQ>AK){AK=AQ}}if(AH.y){if(AQ<AX){AX=AQ}if(AQ>AU){AU=AQ}}}}if(AO.bars.show){var AY;switch(AO.bars.align){case"left":AY=0;break;case"right":AY=-AO.bars.barWidth;break;default:AY=-AO.bars.barWidth/2}if(AO.bars.horizontal){AX+=AY;AU+=AY+AO.bars.barWidth}else{AA+=AY;AK+=AY+AO.bars.barWidth}}AN(AO.xaxis,AA,AK);AN(AO.yaxis,AX,AU)}E.each(L(),function(A0,AZ){if(AZ.datamin==AD){AZ.datamin=null}if(AZ.datamax==AL){AZ.datamax=null}})}function z(){Aj.css("padding",0).children().filter(function(){return !E(this).hasClass("flot-overlay")&&!E(this).hasClass("flot-base")}).remove();if(Aj.css("position")=="static"){Aj.css("position","relative")}Aw=new D("flot-base",Aj);x=new D("flot-overlay",Aj);Y=Aw.context;T=x.context;Au=E(x.element).unbind();var Ax=Aj.data("plot");if(Ax){Ax.shutdown();x.clear()}Aj.data("plot",Ah)}function N(){if(S.grid.hoverable){Au.mousemove(Ac);Au.bind("mouseleave",F)}if(S.grid.clickable){Au.click(Af)}f(k.bindEvents,[Au])}function U(){if(y){clearTimeout(y)}Au.unbind("mousemove",Ac);Au.unbind("mouseleave",F);Au.unbind("click",Af);f(k.shutdown,[Au])}function s(AC){function AB(AD){return AD}var Ay,Az,AA=AC.options.transform||AB,Ax=AC.options.inverseTransform;if(AC.direction=="x"){Ay=AC.scale=Ai/Math.abs(AA(AC.max)-AA(AC.min));Az=Math.min(AA(AC.max),AA(AC.min))}else{Ay=AC.scale=Ap/Math.abs(AA(AC.max)-AA(AC.min));Ay=-Ay;Az=Math.max(AA(AC.max),AA(AC.min))}if(AA==AB){AC.p2c=function(AD){return(AD-Az)*Ay}}else{AC.p2c=function(AD){return(AA(AD)-Az)*Ay}}if(!Ax){AC.c2p=function(AD){return Az+AD/Ay}}else{AC.c2p=function(AD){return Ax(Az+AD/Ay)}}}function Aa(Ax){var AC=Ax.options,AG=Ax.ticks||[],AB=AC.labelWidth||0,Az=AC.labelHeight||0,AD=AB||(Ax.direction=="x"?Math.floor(Aw.width/(AG.length||1)):null),AE=Ax.direction+"Axis "+Ax.direction+Ax.n+"Axis",AI="flot-"+Ax.direction+"-axis flot-"+Ax.direction+Ax.n+"-axis "+AE,AF=AC.font||"flot-tick-label tickLabel";for(var AA=0;AA<AG.length;++AA){var Ay=AG[AA];if(!Ay.label){continue}var AH=Aw.getTextInfo(AI,Ay.label,AF,null,AD);AB=Math.max(AB,AH.width);Az=Math.max(Az,AH.height)}Ax.labelWidth=AC.labelWidth||AB;Ax.labelHeight=AC.labelHeight||Az}function I(Ay){var AF=Ay.labelWidth,AC=Ay.labelHeight,AE=Ay.options.position,AB=Ay.direction==="x",AI=Ay.options.tickLength,AH=S.grid.axisMargin,Az=S.grid.labelMargin,AG=true,Ax=true,AA=true,AD=false;E.each(AB?i:Am,function(AJ,AK){if(AK&&(AK.show||AK.reserveSpace)){if(AK===Ay){AD=true}else{if(AK.options.position===AE){if(AD){Ax=false}else{AG=false}}}if(!AD){AA=false}}});if(Ax){AH=0}if(AI==null){AI=AA?"full":5}if(!isNaN(+AI)){Az+=+AI}if(AB){AC+=Az;if(AE=="bottom"){o.bottom+=AC+AH;Ay.box={top:Aw.height-o.bottom,height:AC}}else{Ay.box={top:o.top+AH,height:AC};o.top+=AC+AH}}else{AF+=Az;if(AE=="left"){Ay.box={left:o.left+AH,width:AF};o.left+=AF+AH}else{o.right+=AF+AH;Ay.box={left:Aw.width-o.right,width:AF}}}Ay.position=AE;Ay.tickLength=AI;Ay.box.padding=Az;Ay.innermost=AG}function d(Ax){if(Ax.direction=="x"){Ax.box.left=o.left-Ax.labelWidth/2;Ax.box.width=Aw.width-o.left-o.right+Ax.labelWidth}else{Ax.box.top=o.top-Ax.labelHeight/2;Ax.box.height=Aw.height-o.bottom-o.top+Ax.labelHeight}}function g(){var AA=S.grid.minBorderMargin,Az,Ax;if(AA==null){AA=0;for(Ax=0;Ax<Ag.length;++Ax){AA=Math.max(AA,2*(Ag[Ax].points.radius+Ag[Ax].points.lineWidth/2))}}var Ay={left:AA,right:AA,top:AA,bottom:AA};E.each(L(),function(AC,AB){if(AB.reserveSpace&&AB.ticks&&AB.ticks.length){if(AB.direction==="x"){Ay.left=Math.max(Ay.left,AB.labelWidth/2);Ay.right=Math.max(Ay.right,AB.labelWidth/2)}else{Ay.bottom=Math.max(Ay.bottom,AB.labelHeight/2);Ay.top=Math.max(Ay.top,AB.labelHeight/2)}}});o.left=Math.ceil(Math.max(Ay.left,o.left));o.right=Math.ceil(Math.max(Ay.right,o.right));o.top=Math.ceil(Math.max(Ay.top,o.top));o.bottom=Math.ceil(Math.max(Ay.bottom,o.bottom))}function Ao(){var Ay,Ax=L(),AA=S.grid.show;for(var AC in o){var Az=S.grid.margin||0;o[AC]=typeof Az=="number"?Az:Az[AC]||0}f(k.processOffset,[o]);for(var AC in o){if(typeof(S.grid.borderWidth)=="object"){o[AC]+=AA?S.grid.borderWidth[AC]:0}else{o[AC]+=AA?S.grid.borderWidth:0}}E.each(Ax,function(AF,AE){var AD=AE.options;AE.show=AD.show==null?AE.used:AD.show;AE.reserveSpace=AD.reserveSpace==null?AE.show:AD.reserveSpace;K(AE)});if(AA){var AB=E.grep(Ax,function(AD){return AD.show||AD.reserveSpace});E.each(AB,function(AE,AD){a(AD);e(AD);Q(AD,AD.ticks);Aa(AD)});for(Ay=AB.length-1;Ay>=0;--Ay){I(AB[Ay])}g();E.each(AB,function(AE,AD){d(AD)})}Ai=Aw.width-o.left-o.right;Ap=Aw.height-o.bottom-o.top;E.each(Ax,function(AE,AD){s(AD)});if(AA){n()}Aq()}function K(AD){var Az=AD.options,Ax=+(Az.min!=null?Az.min:AD.datamin),AB=+(Az.max!=null?Az.max:AD.datamax),AC=AB-Ax;if(AC==0){var Ay=AB==0?1:0.01;if(Az.min==null){Ax-=Ay}if(Az.max==null||Az.min!=null){AB+=Ay}}else{var AA=Az.autoscaleMargin;if(AA!=null){if(Az.min==null){Ax-=AC*AA;if(Ax<0&&AD.datamin!=null&&AD.datamin>=0){Ax=0}}if(Az.max==null){AB+=AC*AA;if(AB>0&&AD.datamax!=null&&AD.datamax<=0){AB=0}}}}AD.min=Ax;AD.max=AB}function a(Ay){var AC=Ay.options;var AA;if(typeof AC.ticks=="number"&&AC.ticks>0){AA=AC.ticks}else{AA=0.3*Math.sqrt(Ay.direction=="x"?Aw.width:Aw.height)}var AI=(Ay.max-Ay.min)/AA,AG=-Math.floor(Math.log(AI)/Math.LN10),AE=AC.tickDecimals;if(AE!=null&&AG>AE){AG=AE}var AJ=Math.pow(10,-AG),AD=AI/AJ,AB;if(AD<1.5){AB=1}else{if(AD<3){AB=2;if(AD>2.25&&(AE==null||AG+1<=AE)){AB=2.5;++AG}}else{if(AD<7.5){AB=5}else{AB=10}}}AB*=AJ;if(AC.minTickSize!=null&&AB<AC.minTickSize){AB=AC.minTickSize}Ay.delta=AI;Ay.tickDecimals=Math.max(0,AE!=null?AE:AG);Ay.tickSize=AC.tickSize||AB;if(AC.mode=="time"&&!Ay.tickGenerator){throw new Error("Time mode requires the flot.time plugin.")}if(!Ay.tickGenerator){Ay.tickGenerator=function(AP){var AO=[],AK=B(AP.min,AP.tickSize),AL=0,AN=Number.NaN,AM;do{AM=AN;AN=AK+AL*AP.tickSize;AO.push(AN);++AL}while(AN<AP.max&&AN!=AM);return AO};Ay.tickFormatter=function(AN,AP){var AL=AP.tickDecimals?Math.pow(10,AP.tickDecimals):1;var AK=""+Math.round(AN*AL)/AL;if(AP.tickDecimals!=null){var AO=AK.indexOf(".");var AM=AO==-1?0:AK.length-AO-1;if(AM<AP.tickDecimals){return(AM?AK:AK+".")+(""+AL).substr(1,AP.tickDecimals-AM)}}return AK}}if(E.isFunction(AC.tickFormatter)){Ay.tickFormatter=function(AK,AL){return""+AC.tickFormatter(AK,AL)}}if(AC.alignTicksWithAxis!=null){var Az=(Ay.direction=="x"?i:Am)[AC.alignTicksWithAxis-1];if(Az&&Az.used&&Az!=Ay){var AH=Ay.tickGenerator(Ay);if(AH.length>0){if(AC.min==null){Ay.min=Math.min(Ay.min,AH[0])}if(AC.max==null&&AH.length>1){Ay.max=Math.max(Ay.max,AH[AH.length-1])}}Ay.tickGenerator=function(AN){var AM=[],AL,AK;for(AK=0;AK<Az.ticks.length;++AK){AL=(Az.ticks[AK].v-Az.min)/(Az.max-Az.min);AL=AN.min+AL*(AN.max-AN.min);AM.push(AL)}return AM};if(!Ay.mode&&AC.tickDecimals==null){var AF=Math.max(0,-Math.floor(Math.log(Ay.delta)/Math.LN10)+1),Ax=Ay.tickGenerator(Ay);if(!(Ax.length>1&&/\..*0$/.test((Ax[1]-Ax[0]).toFixed(AF)))){Ay.tickDecimals=AF}}}}}function e(AD){var AB=AD.options.ticks,AC=[];if(AB==null||(typeof AB=="number"&&AB>0)){AC=AD.tickGenerator(AD)}else{if(AB){if(E.isFunction(AB)){AC=AB(AD)}else{AC=AB}}}var Ay,Az;AD.ticks=[];for(Ay=0;Ay<AC.length;++Ay){var Ax=null;var AA=AC[Ay];if(typeof AA=="object"){Az=+AA[0];if(AA.length>1){Ax=AA[1]}}else{Az=+AA}if(Ax==null){Ax=AD.tickFormatter(Az,AD)}if(!isNaN(Az)){AD.ticks.push({v:Az,label:Ax})}}}function Q(Ay,Ax){if(Ay.options.autoscaleMargin&&Ax.length>0){if(Ay.options.min==null){Ay.min=Math.min(Ay.min,Ax[0].v)}if(Ay.options.max==null&&Ax.length>1){Ay.max=Math.max(Ay.max,Ax[Ax.length-1].v)}}}function M(){Aw.clear();f(k.drawBackground,[Y]);var Ay=S.grid;if(Ay.show&&Ay.backgroundColor){u()}if(Ay.show&&!Ay.aboveData){j()}for(var Ax=0;Ax<Ag.length;++Ax){f(k.drawSeries,[Y,Ag[Ax]]);p(Ag[Ax])}f(k.draw,[Y]);if(Ay.show&&Ay.aboveData){j()}Aw.render();V()}function G(Az,AC){var Ay,Ax,AF,AE,AD=L();for(var AB=0;AB<AD.length;++AB){Ay=AD[AB];if(Ay.direction==AC){AE=AC+Ay.n+"axis";if(!Az[AE]&&Ay.n==1){AE=AC+"axis"}if(Az[AE]){Ax=Az[AE].from;AF=Az[AE].to;break}}}if(!Az[AE]){Ay=AC=="x"?i[0]:Am[0];Ax=Az[AC+"1"];AF=Az[AC+"2"]}if(Ax!=null&&AF!=null&&Ax>AF){var AA=Ax;Ax=AF;AF=AA}return{from:Ax,to:AF,axis:Ay}}function u(){Y.save();Y.translate(o.left,o.top);Y.fillStyle=P(S.grid.backgroundColor,Ap,0,"rgba(255, 255, 255, 0)");Y.fillRect(0,0,Ai,Ap);Y.restore()}function j(){var AD,AO,AK,AG;Y.save();Y.translate(o.left,o.top);var Ax=S.grid.markings;if(Ax){if(E.isFunction(Ax)){AO=Ah.getAxes();AO.xmin=AO.xaxis.min;AO.xmax=AO.xaxis.max;AO.ymin=AO.yaxis.min;AO.ymax=AO.yaxis.max;Ax=Ax(AO)}for(AD=0;AD<Ax.length;++AD){var AE=Ax[AD],AC=G(AE,"x"),AN=G(AE,"y");if(AC.from==null){AC.from=AC.axis.min}if(AC.to==null){AC.to=AC.axis.max}if(AN.from==null){AN.from=AN.axis.min}if(AN.to==null){AN.to=AN.axis.max}if(AC.to<AC.axis.min||AC.from>AC.axis.max||AN.to<AN.axis.min||AN.from>AN.axis.max){continue}AC.from=Math.max(AC.from,AC.axis.min);AC.to=Math.min(AC.to,AC.axis.max);AN.from=Math.max(AN.from,AN.axis.min);AN.to=Math.min(AN.to,AN.axis.max);var Ay=AC.from===AC.to,AR=AN.from===AN.to;if(Ay&&AR){continue}AC.from=Math.floor(AC.axis.p2c(AC.from));AC.to=Math.floor(AC.axis.p2c(AC.to));AN.from=Math.floor(AN.axis.p2c(AN.from));AN.to=Math.floor(AN.axis.p2c(AN.to));if(Ay||AR){var AL=AE.lineWidth||S.grid.markingsLineWidth,AF=AL%2?0.5:0;Y.beginPath();Y.strokeStyle=AE.color||S.grid.markingsColor;Y.lineWidth=AL;if(Ay){Y.moveTo(AC.to+AF,AN.from);Y.lineTo(AC.to+AF,AN.to)}else{Y.moveTo(AC.from,AN.to+AF);Y.lineTo(AC.to,AN.to+AF)}Y.stroke()}else{Y.fillStyle=AE.color||S.grid.markingsColor;Y.fillRect(AC.from,AN.to,AC.to-AC.from,AN.from-AN.to)}}}AO=L();AK=S.grid.borderWidth;for(var AM=0;AM<AO.length;++AM){var AI=AO[AM],AB=AI.box,AA=AI.tickLength,AP,AQ,AH,AJ;if(!AI.show||AI.ticks.length==0){continue}Y.lineWidth=1;if(AI.direction=="x"){AP=0;if(AA=="full"){AQ=(AI.position=="top"?0:Ap)}else{AQ=AB.top-o.top+(AI.position=="top"?AB.height:0)}}else{AQ=0;if(AA=="full"){AP=(AI.position=="left"?0:Ai)}else{AP=AB.left-o.left+(AI.position=="left"?AB.width:0)}}if(!AI.innermost){Y.strokeStyle=AI.options.color;Y.beginPath();AH=AJ=0;if(AI.direction=="x"){AH=Ai+1}else{AJ=Ap+1}if(Y.lineWidth==1){if(AI.direction=="x"){AQ=Math.floor(AQ)+0.5}else{AP=Math.floor(AP)+0.5}}Y.moveTo(AP,AQ);Y.lineTo(AP+AH,AQ+AJ);Y.stroke()}Y.strokeStyle=AI.options.tickColor;Y.beginPath();for(AD=0;AD<AI.ticks.length;++AD){var Az=AI.ticks[AD].v;AH=AJ=0;if(isNaN(Az)||Az<AI.min||Az>AI.max||(AA=="full"&&((typeof AK=="object"&&AK[AI.position]>0)||AK>0)&&(Az==AI.min||Az==AI.max))){continue}if(AI.direction=="x"){AP=AI.p2c(Az);AJ=AA=="full"?-Ap:AA;if(AI.position=="top"){AJ=-AJ}}else{AQ=AI.p2c(Az);AH=AA=="full"?-Ai:AA;if(AI.position=="left"){AH=-AH}}if(Y.lineWidth==1){if(AI.direction=="x"){AP=Math.floor(AP)+0.5}else{AQ=Math.floor(AQ)+0.5}}Y.moveTo(AP,AQ);Y.lineTo(AP+AH,AQ+AJ)}Y.stroke()}if(AK){AG=S.grid.borderColor;if(typeof AK=="object"||typeof AG=="object"){if(typeof AK!=="object"){AK={top:AK,right:AK,bottom:AK,left:AK}}if(typeof AG!=="object"){AG={top:AG,right:AG,bottom:AG,left:AG}}if(AK.top>0){Y.strokeStyle=AG.top;Y.lineWidth=AK.top;Y.beginPath();Y.moveTo(0-AK.left,0-AK.top/2);Y.lineTo(Ai,0-AK.top/2);Y.stroke()}if(AK.right>0){Y.strokeStyle=AG.right;Y.lineWidth=AK.right;Y.beginPath();Y.moveTo(Ai+AK.right/2,0-AK.top);Y.lineTo(Ai+AK.right/2,Ap);Y.stroke()}if(AK.bottom>0){Y.strokeStyle=AG.bottom;Y.lineWidth=AK.bottom;Y.beginPath();Y.moveTo(Ai+AK.right,Ap+AK.bottom/2);Y.lineTo(0,Ap+AK.bottom/2);Y.stroke()}if(AK.left>0){Y.strokeStyle=AG.left;Y.lineWidth=AK.left;Y.beginPath();Y.moveTo(0-AK.left/2,Ap+AK.bottom);Y.lineTo(0-AK.left/2,0);Y.stroke()}}else{Y.lineWidth=AK;Y.strokeStyle=S.grid.borderColor;Y.strokeRect(-AK/2,-AK/2,Ai+AK,Ap+AK)}}Y.restore()}function n(){E.each(L(),function(AB,Ay){var AA=Ay.box,AE=Ay.direction+"Axis "+Ay.direction+Ay.n+"Axis",AC="flot-"+Ay.direction+"-axis flot-"+Ay.direction+Ay.n+"-axis "+AE,AF=Ay.options.font||"flot-tick-label tickLabel",AI,AG,AH,Az,Ax;Aw.removeText(AC);if(!Ay.show||Ay.ticks.length==0){return}for(var AD=0;AD<Ay.ticks.length;++AD){AI=Ay.ticks[AD];if(!AI.label||AI.v<Ay.min||AI.v>Ay.max){continue}if(Ay.direction=="x"){Az="center";AG=o.left+Ay.p2c(AI.v);if(Ay.position=="bottom"){AH=AA.top+AA.padding}else{AH=AA.top+AA.height-AA.padding;Ax="bottom"}}else{Ax="middle";AH=o.top+Ay.p2c(AI.v);if(Ay.position=="left"){AG=AA.left+AA.width-AA.padding;Az="right"}else{AG=AA.left+AA.padding}}Aw.addText(AC,AG,AH,AI.label,AF,null,null,Az,Ax)}})}function p(Ax){if(Ax.lines.show){An(Ax)}if(Ax.bars.show){Z(Ax)}if(Ax.points.show){t(Ax)}}function An(AB){function AC(AN,AE,AF,AR,AG){var AP=AN.points,AK=AN.pointsize,AM=null,AL=null;Y.beginPath();for(var AQ=AK;AQ<AP.length;AQ+=AK){var AO=AP[AQ-AK],AH=AP[AQ-AK+1],AI=AP[AQ],AJ=AP[AQ+1];if(AO==null||AI==null){continue}if(AH<=AJ&&AH<AG.min){if(AJ<AG.min){continue}AO=(AG.min-AH)/(AJ-AH)*(AI-AO)+AO;AH=AG.min}else{if(AJ<=AH&&AJ<AG.min){if(AH<AG.min){continue}AI=(AG.min-AH)/(AJ-AH)*(AI-AO)+AO;AJ=AG.min}}if(AH>=AJ&&AH>AG.max){if(AJ>AG.max){continue}AO=(AG.max-AH)/(AJ-AH)*(AI-AO)+AO;AH=AG.max}else{if(AJ>=AH&&AJ>AG.max){if(AH>AG.max){continue}AI=(AG.max-AH)/(AJ-AH)*(AI-AO)+AO;AJ=AG.max}}if(AO<=AI&&AO<AR.min){if(AI<AR.min){continue}AH=(AR.min-AO)/(AI-AO)*(AJ-AH)+AH;AO=AR.min}else{if(AI<=AO&&AI<AR.min){if(AO<AR.min){continue}AJ=(AR.min-AO)/(AI-AO)*(AJ-AH)+AH;AI=AR.min}}if(AO>=AI&&AO>AR.max){if(AI>AR.max){continue}AH=(AR.max-AO)/(AI-AO)*(AJ-AH)+AH;AO=AR.max}else{if(AI>=AO&&AI>AR.max){if(AO>AR.max){continue}AJ=(AR.max-AO)/(AI-AO)*(AJ-AH)+AH;AI=AR.max}}if(AO!=AM||AH!=AL){Y.moveTo(AR.p2c(AO)+AE,AG.p2c(AH)+AF)}AM=AI;AL=AJ;Y.lineTo(AR.p2c(AI)+AE,AG.p2c(AJ)+AF)}Y.stroke()}function AD(AK,AV,AE){var AM=AK.points,AF=AK.pointsize,AL=Math.min(Math.max(0,AE.min),AE.max),AH=0,AP,AI=false,AJ=1,AU=0,AS=0;while(true){if(AF>0&&AH>AM.length+AF){break}AH+=AF;var AQ=AM[AH-AF],AR=AM[AH-AF+AJ],AT=AM[AH],AO=AM[AH+AJ];if(AI){if(AF>0&&AQ!=null&&AT==null){AS=AH;AF=-AF;AJ=2;continue}if(AF<0&&AH==AU+AF){Y.fill();AI=false;AF=-AF;AJ=1;AH=AU=AS+AF;continue}}if(AQ==null||AT==null){continue}if(AQ<=AT&&AQ<AV.min){if(AT<AV.min){continue}AR=(AV.min-AQ)/(AT-AQ)*(AO-AR)+AR;AQ=AV.min}else{if(AT<=AQ&&AT<AV.min){if(AQ<AV.min){continue}AO=(AV.min-AQ)/(AT-AQ)*(AO-AR)+AR;AT=AV.min}}if(AQ>=AT&&AQ>AV.max){if(AT>AV.max){continue}AR=(AV.max-AQ)/(AT-AQ)*(AO-AR)+AR;AQ=AV.max}else{if(AT>=AQ&&AT>AV.max){if(AQ>AV.max){continue}AO=(AV.max-AQ)/(AT-AQ)*(AO-AR)+AR;AT=AV.max}}if(!AI){Y.beginPath();Y.moveTo(AV.p2c(AQ),AE.p2c(AL));AI=true}if(AR>=AE.max&&AO>=AE.max){Y.lineTo(AV.p2c(AQ),AE.p2c(AE.max));Y.lineTo(AV.p2c(AT),AE.p2c(AE.max));continue}else{if(AR<=AE.min&&AO<=AE.min){Y.lineTo(AV.p2c(AQ),AE.p2c(AE.min));Y.lineTo(AV.p2c(AT),AE.p2c(AE.min));continue}}var AN=AQ,AG=AT;if(AR<=AO&&AR<AE.min&&AO>=AE.min){AQ=(AE.min-AR)/(AO-AR)*(AT-AQ)+AQ;AR=AE.min}else{if(AO<=AR&&AO<AE.min&&AR>=AE.min){AT=(AE.min-AR)/(AO-AR)*(AT-AQ)+AQ;AO=AE.min}}if(AR>=AO&&AR>AE.max&&AO<=AE.max){AQ=(AE.max-AR)/(AO-AR)*(AT-AQ)+AQ;AR=AE.max}else{if(AO>=AR&&AO>AE.max&&AR<=AE.max){AT=(AE.max-AR)/(AO-AR)*(AT-AQ)+AQ;AO=AE.max}}if(AQ!=AN){Y.lineTo(AV.p2c(AN),AE.p2c(AR))}Y.lineTo(AV.p2c(AQ),AE.p2c(AR));Y.lineTo(AV.p2c(AT),AE.p2c(AO));if(AT!=AG){Y.lineTo(AV.p2c(AT),AE.p2c(AO));Y.lineTo(AV.p2c(AG),AE.p2c(AO))}}}Y.save();Y.translate(o.left,o.top);Y.lineJoin="round";var Az=AB.lines.lineWidth,Ax=AB.shadowSize;if(Az>0&&Ax>0){Y.lineWidth=Ax;Y.strokeStyle="rgba(0,0,0,0.1)";var AA=Math.PI/18;AC(AB.datapoints,Math.sin(AA)*(Az/2+Ax/2),Math.cos(AA)*(Az/2+Ax/2),AB.xaxis,AB.yaxis);Y.lineWidth=Ax/2;AC(AB.datapoints,Math.sin(AA)*(Az/2+Ax/4),Math.cos(AA)*(Az/2+Ax/4),AB.xaxis,AB.yaxis)}Y.lineWidth=Az;Y.strokeStyle=AB.color;var Ay=X(AB.lines,AB.color,0,Ap);if(Ay){Y.fillStyle=Ay;AD(AB.datapoints,AB.xaxis,AB.yaxis)}if(Az>0){AC(AB.datapoints,0,0,AB.xaxis,AB.yaxis)}Y.restore()}function t(AB){function Az(AN,AL,AF,AG,AH,AQ,AE,AK){var AM=AN.points,AI=AN.pointsize;for(var AJ=0;AJ<AM.length;AJ+=AI){var AO=AM[AJ],AP=AM[AJ+1];if(AO==null||AO<AQ.min||AO>AQ.max||AP<AE.min||AP>AE.max){continue}Y.beginPath();AO=AQ.p2c(AO);AP=AE.p2c(AP)+AG;if(AK=="circle"){Y.arc(AO,AP,AL,0,AH?Math.PI:Math.PI*2,false)}else{AK(Y,AO,AP,AL,AH)}Y.closePath();if(AF){Y.fillStyle=AF;Y.fill()}Y.stroke()}}Y.save();Y.translate(o.left,o.top);var Ay=AB.points.lineWidth,Ax=AB.shadowSize,AA=AB.points.radius,AD=AB.points.symbol;if(Ay==0){Ay=0.0001}if(Ay>0&&Ax>0){var AC=Ax/2;Y.lineWidth=AC;Y.strokeStyle="rgba(0,0,0,0.1)";Az(AB.datapoints,AA,null,AC+AC/2,true,AB.xaxis,AB.yaxis,AD);Y.strokeStyle="rgba(0,0,0,0.2)";Az(AB.datapoints,AA,null,AC/2,true,AB.xaxis,AB.yaxis,AD)}Y.lineWidth=Ay;Y.strokeStyle=AB.color;Az(AB.datapoints,AA,X(AB.points,AB.color),0,false,AB.xaxis,AB.yaxis,AD);Y.restore()}function O(AN,AO,AD,Ay,AI,AF,AQ,Ax,AE,AP,AK){var AL,AA,AH,AJ,AM,AC,AG,AB,Az;if(AP){AB=AC=AG=true;AM=false;AL=AD;AA=AN;AJ=AO+Ay;AH=AO+AI;if(AA<AL){Az=AA;AA=AL;AL=Az;AM=true;AC=false}}else{AM=AC=AG=true;AB=false;AL=AN+Ay;AA=AN+AI;AH=AD;AJ=AO;if(AJ<AH){Az=AJ;AJ=AH;AH=Az;AB=true;AG=false}}if(AA<AQ.min||AL>AQ.max||AJ<Ax.min||AH>Ax.max){return}if(AL<AQ.min){AL=AQ.min;AM=false}if(AA>AQ.max){AA=AQ.max;AC=false}if(AH<Ax.min){AH=Ax.min;AB=false}if(AJ>Ax.max){AJ=Ax.max;AG=false}AL=AQ.p2c(AL);AH=Ax.p2c(AH);AA=AQ.p2c(AA);AJ=Ax.p2c(AJ);if(AF){AE.fillStyle=AF(AH,AJ);AE.fillRect(AL,AJ,AA-AL,AH-AJ)}if(AK>0&&(AM||AC||AG||AB)){AE.beginPath();AE.moveTo(AL,AH);if(AM){AE.lineTo(AL,AJ)}else{AE.moveTo(AL,AJ)}if(AG){AE.lineTo(AA,AJ)}else{AE.moveTo(AA,AJ)}if(AC){AE.lineTo(AA,AH)}else{AE.moveTo(AA,AH)}if(AB){AE.lineTo(AL,AH)}else{AE.moveTo(AL,AH)}AE.stroke()}}function Z(Az){function AA(AH,AC,AD,AE,AJ,AB){var AI=AH.points,AF=AH.pointsize;for(var AG=0;AG<AI.length;AG+=AF){if(AI[AG]==null){continue}O(AI[AG],AI[AG+1],AI[AG+2],AC,AD,AE,AJ,AB,Y,Az.bars.horizontal,Az.bars.lineWidth)}}Y.save();Y.translate(o.left,o.top);Y.lineWidth=Az.bars.lineWidth;Y.strokeStyle=Az.color;var Ax;switch(Az.bars.align){case"left":Ax=0;break;case"right":Ax=-Az.bars.barWidth;break;default:Ax=-Az.bars.barWidth/2}var Ay=Az.bars.fill?function(AC,AB){return X(Az.bars,Az.color,AC,AB)}:null;AA(Az.datapoints,Ax,Ax+Az.bars.barWidth,Ay,Az.xaxis,Az.yaxis);Y.restore()}function X(Ay,Az,AC,AA){var Ax=Ay.fill;if(!Ax){return null}if(Ay.fillColor){return P(Ay.fillColor,AC,AA,Az)}var AB=E.color.parse(Az);AB.a=typeof Ax=="number"?Ax:0.4;AB.normalize();return AB.toString()}function Aq(){if(S.legend.container!=null){E(S.legend.container).html("")}else{Aj.find(".legend").remove()}if(!S.legend.show){return}var AH=[],AK=[],Ax=false,AJ=S.legend.labelFormatter,Az,AG;for(var AE=0;AE<Ag.length;++AE){Az=Ag[AE];if(Az.label){AG=AJ?AJ(Az.label,Az):Az.label;if(AG){AK.push({label:AG,color:Az.color})}}}if(S.legend.sorted){if(E.isFunction(S.legend.sorted)){AK.sort(S.legend.sorted)}else{if(S.legend.sorted=="reverse"){AK.reverse()}else{var AB=S.legend.sorted!="descending";AK.sort(function(AO,AN){return AO.label==AN.label?0:((AO.label<AN.label)!=AB?1:-1)})}}}for(var AE=0;AE<AK.length;++AE){var AA=AK[AE];if(AE%S.legend.noColumns==0){if(Ax){AH.push("</tr>")}AH.push("<tr>");Ax=true}AH.push('<td class="legendColorBox"><div style="border:1px solid '+S.legend.labelBoxBorderColor+';padding:1px"><div style="width:4px;height:0;border:5px solid '+AA.color+';overflow:hidden"></div></div></td><td class="legendLabel">'+AA.label+"</td>")}if(Ax){AH.push("</tr>")}if(AH.length==0){return}var AM='<table style="font-size:smaller;color:'+S.grid.color+'">'+AH.join("")+"</table>";if(S.legend.container!=null){E(S.legend.container).html(AM)}else{var AF="",Ay=S.legend.position,AL=S.legend.margin;if(AL[0]==null){AL=[AL,AL]}if(Ay.charAt(0)=="n"){AF+="top:"+(AL[1]+o.top)+"px;"}else{if(Ay.charAt(0)=="s"){AF+="bottom:"+(AL[1]+o.bottom)+"px;"}}if(Ay.charAt(1)=="e"){AF+="right:"+(AL[0]+o.right)+"px;"}else{if(Ay.charAt(1)=="w"){AF+="left:"+(AL[0]+o.left)+"px;"}}var AD=E('<div class="legend">'+AM.replace('style="','style="position:absolute;'+AF+";")+"</div>").appendTo(Aj);if(S.legend.backgroundOpacity!=0){var AI=S.legend.backgroundColor;if(AI==null){AI=S.grid.backgroundColor;if(AI&&typeof AI=="string"){AI=E.color.parse(AI)}else{AI=E.color.extract(AD,"background-color")}AI.a=1;AI=AI.toString()}var AC=AD.children();E('<div style="position:absolute;width:'+AC.width()+"px;height:"+AC.height()+"px;"+AF+"background-color:"+AI+';"> </div>').prependTo(AD).css("opacity",S.legend.backgroundOpacity)}}}var r=[],y=null;function Av(AH,AS,Az){var AO=S.grid.mouseActiveRadius,AA=AO*AO+1,AB=null,AM=false,AE,AD,Ay;for(AE=Ag.length-1;AE>=0;--AE){if(!Az(Ag[AE])){continue}var AN=Ag[AE],AW=AN.xaxis,Ax=AN.yaxis,AJ=AN.datapoints.points,AC=AW.c2p(AH),AL=Ax.c2p(AS),AU=AO/AW.scale,AI=AO/Ax.scale;Ay=AN.datapoints.pointsize;if(AW.options.inverseTransform){AU=Number.MAX_VALUE}if(Ax.options.inverseTransform){AI=Number.MAX_VALUE}if(AN.lines.show||AN.points.show){for(AD=0;AD<AJ.length;AD+=Ay){var AT=AJ[AD],AQ=AJ[AD+1];if(AT==null){continue}if(AT-AC>AU||AT-AC<-AU||AQ-AL>AI||AQ-AL<-AI){continue}var AF=Math.abs(AW.p2c(AT)-AH),AR=Math.abs(Ax.p2c(AQ)-AS),AV=AF*AF+AR*AR;if(AV<AA){AA=AV;AB=[AE,AD/Ay]}}}if(AN.bars.show&&!AB){var AP,AK;switch(AN.bars.align){case"left":AP=0;break;case"right":AP=-AN.bars.barWidth;break;default:AP=-AN.bars.barWidth/2}AK=AP+AN.bars.barWidth;for(AD=0;AD<AJ.length;AD+=Ay){var AT=AJ[AD],AQ=AJ[AD+1],AG=AJ[AD+2];if(AT==null){continue}if(Ag[AE].bars.horizontal?(AC<=Math.max(AG,AT)&&AC>=Math.min(AG,AT)&&AL>=AQ+AP&&AL<=AQ+AK):(AC>=AT+AP&&AC<=AT+AK&&AL>=Math.min(AG,AQ)&&AL<=Math.max(AG,AQ))){AB=[AE,AD/Ay]}}}}if(AB){AE=AB[0];AD=AB[1];Ay=Ag[AE].datapoints.pointsize;return{datapoint:Ag[AE].datapoints.points.slice(AD*Ay,(AD+1)*Ay),dataIndex:AD,series:Ag[AE],seriesIndex:AE}}return null}function Ac(Ax){if(S.grid.hoverable){Ae("plothover",Ax,function(Ay){return Ay["hoverable"]!=false})}}function F(Ax){if(S.grid.hoverable){Ae("plothover",Ax,function(Ay){return false})}}function Af(Ax){Ae("plotclick",Ax,function(Ay){return Ay["clickable"]!=false})}function Ae(AE,Az,Ay){var Ax=Au.offset(),AA=Az.pageX-Ax.left-o.left,AB=Az.pageY-Ax.top-o.top,AD=Al({left:AA,top:AB});AD.pageX=Az.pageX;AD.pageY=Az.pageY;var AG=Av(AA,AB,Ay);if(AG){AG.pageX=parseInt(AG.series.xaxis.p2c(AG.datapoint[0])+Ax.left+o.left,10);AG.pageY=parseInt(AG.series.yaxis.p2c(AG.datapoint[1])+Ax.top+o.top,10)}if(S.grid.autoHighlight){for(var AF=0;AF<r.length;++AF){var AC=r[AF];if(AC.auto==AE&&!(AG&&AC.series==AG.series&&AC.point[0]==AG.datapoint[0]&&AC.point[1]==AG.datapoint[1])){R(AC.series,AC.point)}}if(AG){H(AG.series,AG.datapoint,AE)}}Aj.trigger(AE,[AD,AG])}function V(){var Ax=S.interaction.redrawOverlayInterval;if(Ax==-1){q();return}if(!y){y=setTimeout(q,Ax)}}function q(){y=null;T.save();x.clear();T.translate(o.left,o.top);var Ax,Ay;for(Ax=0;Ax<r.length;++Ax){Ay=r[Ax];if(Ay.series.bars.show){As(Ay.series,Ay.point)}else{W(Ay.series,Ay.point)}}T.restore();f(k.drawOverlay,[T])}function H(Ay,Az,AB){if(typeof Ay=="number"){Ay=Ag[Ay]}if(typeof Az=="number"){var AA=Ay.datapoints.pointsize;Az=Ay.datapoints.points.slice(AA*Az,AA*(Az+1))}var Ax=Ar(Ay,Az);if(Ax==-1){r.push({series:Ay,point:Az,auto:AB});V()}else{if(!AB){r[Ax].auto=false}}}function R(Ay,Az){if(Ay==null&&Az==null){r=[];V();return}if(typeof Ay=="number"){Ay=Ag[Ay]}if(typeof Az=="number"){var AA=Ay.datapoints.pointsize;Az=Ay.datapoints.points.slice(AA*Az,AA*(Az+1))}var Ax=Ar(Ay,Az);if(Ax!=-1){r.splice(Ax,1);V()}}function Ar(Az,AA){for(var Ay=0;Ay<r.length;++Ay){var Ax=r[Ay];if(Ax.series==Az&&Ax.point[0]==AA[0]&&Ax.point[1]==AA[1]){return Ay}}return -1}function W(AB,AD){var AE=AD[0],AC=AD[1],AF=AB.xaxis,Ax=AB.yaxis,Az=(typeof AB.highlightColor==="string")?AB.highlightColor:E.color.parse(AB.color).scale("a",0.5).toString();if(AE<AF.min||AE>AF.max||AC<Ax.min||AC>Ax.max){return}var Ay=AB.points.radius+AB.points.lineWidth/2;T.lineWidth=Ay;T.strokeStyle=Az;var AA=1.5*Ay;AE=AF.p2c(AE);AC=Ax.p2c(AC);T.beginPath();if(AB.points.symbol=="circle"){T.arc(AE,AC,AA,0,2*Math.PI,false)}else{AB.points.symbol(T,AE,AC,AA,false)}T.closePath();T.stroke()}function As(Az,Ay){var Ax=(typeof Az.highlightColor==="string")?Az.highlightColor:E.color.parse(Az.color).scale("a",0.5).toString(),AB=Ax,AA;switch(Az.bars.align){case"left":AA=0;break;case"right":AA=-Az.bars.barWidth;break;default:AA=-Az.bars.barWidth/2}T.lineWidth=Az.bars.lineWidth;T.strokeStyle=Ax;O(Ay[0],Ay[1],Ay[2]||0,AA,AA+Az.bars.barWidth,function(){return AB},Az.xaxis,Az.yaxis,T,Az.bars.horizontal,Az.bars.lineWidth)}function P(Ay,AD,Az,AF){if(typeof Ay=="string"){return Ay}else{var AB=Y.createLinearGradient(0,Az,0,AD);for(var AA=0,AC=Ay.colors.length;AA<AC;++AA){var AE=Ay.colors[AA];if(typeof AE!="string"){var Ax=E.color.parse(AF);if(AE.brightness!=null){Ax=Ax.scale("rgb",AE.brightness)}if(AE.opacity!=null){Ax.a*=AE.opacity}AE=Ax.toString()}AB.addColorStop(AA/(AC-1),AE)}return AB}}}E.plot=function(G,I,F){var H=new A(E(G),I,F,E.plot.plugins);return H};E.plot.version="0.8.3";E.plot.plugins=[];E.fn.plot=function(G,F){return this.each(function(){E.plot(this,G,F)})};function B(F,G){return G*Math.floor(F/G)}})(jQuery);