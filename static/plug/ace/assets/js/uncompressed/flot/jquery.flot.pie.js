(function(E){var A=10;var C=0.95;function D(X){var F=null,Z=null,W=null,a=null,Y=null,L=null,K=false,M=null;var P=[];X.hooks.processOptions.push(function(d,c){if(c.series.pie.show){c.grid.show=false;if(c.series.pie.label.show=="auto"){if(c.legend.show){c.series.pie.label.show=false}else{c.series.pie.label.show=true}}if(c.series.pie.radius=="auto"){if(c.series.pie.label.show){c.series.pie.radius=3/4}else{c.series.pie.radius=1}}if(c.series.pie.tilt>1){c.series.pie.tilt=1}else{if(c.series.pie.tilt<0){c.series.pie.tilt=0}}}});X.hooks.bindEvents.push(function(e,d){var c=e.getOptions();if(c.series.pie.show){if(c.grid.hoverable){d.unbind("mousemove").mousemove(S)}if(c.grid.clickable){d.unbind("click").click(Q)}}});X.hooks.processDatapoints.push(function(e,f,g,d){var c=e.getOptions();if(c.series.pie.show){H(e,f,g,d)}});X.hooks.drawOverlay.push(function(e,c){var d=e.getOptions();if(d.series.pie.show){J(e,c)}});X.hooks.draw.push(function(e,c){var d=e.getOptions();if(d.series.pie.show){I(e,c)}});function H(d,e,c){if(!K){K=true;F=d.getCanvas();Z=E(F).parent();W=d.getOptions();d.setData(R(d.getData()))}}function R(c){var h=0,k=0,j=0,d=W.series.pie.combine.color,e=[];for(var g=0;g<c.length;++g){var f=c[g].data;if(E.isArray(f)&&f.length==1){f=f[0]}if(E.isArray(f)){if(!isNaN(parseFloat(f[1]))&&isFinite(f[1])){f[1]=+f[1]}else{f[1]=0}}else{if(!isNaN(parseFloat(f))&&isFinite(f)){f=[1,+f]}else{f=[1,0]}}c[g].data=[f]}for(var g=0;g<c.length;++g){h+=c[g].data[0][1]}for(var g=0;g<c.length;++g){var f=c[g].data[0][1];if(f/h<=W.series.pie.combine.threshold){k+=f;j++;if(!d){d=c[g].color}}}for(var g=0;g<c.length;++g){var f=c[g].data[0][1];if(j<2||f/h>W.series.pie.combine.threshold){e.push(E.extend(c[g],{data:[[1,f]],color:c[g].color,label:c[g].label,angle:f*Math.PI*2/h,percent:f/(h/100)}))}}if(j>1){e.push({data:[[1,k]],color:d,label:W.series.pie.combine.label,angle:k*Math.PI*2/h,percent:k/(h/100)})}return e}function I(d,h){if(!Z){return}var c=d.getPlaceholder().width(),i=d.getPlaceholder().height(),j=Z.children().filter(".legend").children().width()||0;M=h;K=false;a=Math.min(c,i/W.series.pie.tilt)/2;L=i/2+W.series.pie.offset.top;Y=c/2;if(W.series.pie.offset.left=="auto"){if(W.legend.position.match("w")){Y+=j/2}else{Y-=j/2}if(Y<a){Y=a}else{if(Y>c-a){Y=c-a}}}else{Y+=W.series.pie.offset.left}var g=d.getData(),e=0;do{if(e>0){a*=C}e+=1;l();if(W.series.pie.tilt<=0.8){f()}}while(!k()&&e<A);if(e>=A){l();Z.prepend("<div class='error'>Could not draw pie with labels contained inside canvas</div>")}if(d.setSeries&&d.insertLegend){d.setSeries(g);d.insertLegend()}function l(){M.clearRect(0,0,c,i);Z.children().filter(".pieLabel, .pieLabelBackground").remove()}function f(){var o=W.series.pie.shadow.left;var p=W.series.pie.shadow.top;var n=10;var r=W.series.pie.shadow.alpha;var q=W.series.pie.radius>1?W.series.pie.radius:a*W.series.pie.radius;if(q>=c/2-o||q*W.series.pie.tilt>=i/2-p||q<=n){return}M.save();M.translate(o,p);M.globalAlpha=r;M.fillStyle="#000";M.translate(Y,L);M.scale(1,W.series.pie.tilt);for(var m=1;m<=n;m++){M.beginPath();M.arc(0,0,q,0,Math.PI*2,false);M.fill();q-=m}M.restore()}function k(){var p=Math.PI*W.series.pie.startAngle;var o=W.series.pie.radius>1?W.series.pie.radius:a*W.series.pie.radius;M.save();M.translate(Y,L);M.scale(1,W.series.pie.tilt);M.save();var q=p;for(var n=0;n<g.length;++n){g[n].startAngle=q;m(g[n].angle,g[n].color,true)}M.restore();if(W.series.pie.stroke.width>0){M.save();M.lineWidth=W.series.pie.stroke.width;q=p;for(var n=0;n<g.length;++n){m(g[n].angle,W.series.pie.stroke.color,false)}M.restore()}U(M);M.restore();if(W.series.pie.label.show){return r()}else{return true}function m(u,t,s){if(u<=0||isNaN(u)){return}if(s){M.fillStyle=t}else{M.strokeStyle=t;M.lineJoin="round"}M.beginPath();if(Math.abs(u-Math.PI*2)>1e-09){M.moveTo(0,0)}M.arc(0,0,o,q,q+u/2,false);M.arc(0,0,o,q+u/2,q+u,false);M.closePath();q+=u;if(s){M.fill()}else{M.stroke()}}function r(){var v=p;var u=W.series.pie.label.radius>1?W.series.pie.label.radius:a*W.series.pie.label.radius;for(var s=0;s<g.length;++s){if(g[s].percent>=W.series.pie.label.threshold*100){if(!t(g[s],v,s)){return false}}v+=g[s].angle}return true;function t(w,Am,Ab){if(w.data[0][1]==0){return true}var Aj=W.legend.labelFormatter,Af,Al=W.series.pie.label.formatter;if(Aj){Af=Aj(w.label,w)}else{Af=w.label}if(Al){Af=Al(Af,w)}var Ae=((Am+w.angle)+Am)/2;var Ai=Y+Math.round(Math.cos(Ae)*u);var z=L+Math.round(Math.sin(Ae)*u)*W.series.pie.tilt;var Ak="<span class='pieLabel' id='pieLabel"+Ab+"' style='position:absolute;top:"+z+"px;left:"+Ai+"px;'>"+Af+"</span>";Z.append(Ak);var Ah=Z.children("#pieLabel"+Ab);var Ac=(z-Ah.height()/2);var Ad=(Ai-Ah.width()/2);Ah.css("top",Ac);Ah.css("left",Ad);if(0-Ac>0||0-Ad>0||i-(Ac+Ah.height())<0||c-(Ad+Ah.width())<0){return false}if(W.series.pie.label.background.opacity!=0){var Aa=W.series.pie.label.background.color;if(Aa==null){Aa=w.color}var Ag="top:"+Ac+"px;left:"+Ad+"px;";E("<div class='pieLabelBackground' style='position:absolute;width:"+Ah.width()+"px;height:"+Ah.height()+"px;"+Ag+"background-color:"+Aa+";'></div>").css("opacity",W.series.pie.label.background.opacity).insertBefore(Ah)}return true}}}}function U(d){if(W.series.pie.innerRadius>0){d.save();var c=W.series.pie.innerRadius>1?W.series.pie.innerRadius:a*W.series.pie.innerRadius;d.globalCompositeOperation="destination-out";d.beginPath();d.fillStyle=W.series.pie.stroke.color;d.arc(0,0,c,0,Math.PI*2,false);d.fill();d.closePath();d.restore();d.save();d.beginPath();d.strokeStyle=W.series.pie.stroke.color;d.arc(0,0,c,0,Math.PI*2,false);d.stroke();d.closePath();d.restore()}}function N(d,f){for(var h=false,e=-1,g=d.length,k=g-1;++e<g;k=e){((d[e][1]<=f[1]&&f[1]<d[k][1])||(d[k][1]<=f[1]&&f[1]<d[e][1]))&&(f[0]<(d[k][0]-d[e][0])*(f[1]-d[e][1])/(d[k][1]-d[e][1])+d[e][0])&&(h=!h)}return h}function T(n,v){var f=X.getData(),u=X.getOptions(),l=u.series.pie.radius>1?u.series.pie.radius:a*u.series.pie.radius,w,z;for(var k=0;k<f.length;++k){var c=f[k];if(c.pie.show){M.save();M.beginPath();M.moveTo(0,0);M.arc(0,0,l,c.startAngle,c.startAngle+c.angle/2,false);M.arc(0,0,l,c.startAngle+c.angle/2,c.startAngle+c.angle,false);M.closePath();w=n-Y;z=v-L;if(M.isPointInPath){if(M.isPointInPath(n-Y,v-L)){M.restore();return{datapoint:[c.percent,c.data],dataIndex:0,series:c,seriesIndex:k}}}else{var d=l*Math.cos(c.startAngle),e=l*Math.sin(c.startAngle),h=l*Math.cos(c.startAngle+c.angle/4),j=l*Math.sin(c.startAngle+c.angle/4),q=l*Math.cos(c.startAngle+c.angle/2),r=l*Math.sin(c.startAngle+c.angle/2),p=l*Math.cos(c.startAngle+c.angle/1.5),Aa=l*Math.sin(c.startAngle+c.angle/1.5),g=l*Math.cos(c.startAngle+c.angle),o=l*Math.sin(c.startAngle+c.angle),t=[[0,0],[d,e],[h,j],[q,r],[p,Aa],[g,o]],m=[w,z];if(N(t,m)){M.restore();return{datapoint:[c.percent,c.data],dataIndex:0,series:c,seriesIndex:k}}}M.restore()}}return null}function S(c){O("plothover",c)}function Q(c){O("plotclick",c)}function O(m,c){var d=X.offset();var g=parseInt(c.pageX-d.left);var j=parseInt(c.pageY-d.top);var n=T(g,j);if(W.grid.autoHighlight){for(var l=0;l<P.length;++l){var k=P[l];if(k.auto==m&&!(n&&k.series==n.series)){G(k.series)}}}if(n){V(n.series,m)}var f={pageX:c.pageX,pageY:c.pageY};Z.trigger(m,[f,n])}function V(d,e){var c=b(d);if(c==-1){P.push({series:d,auto:e});X.triggerRedrawOverlay()}else{if(!e){P[c].auto=false}}}function G(d){if(d==null){P=[];X.triggerRedrawOverlay()}var c=b(d);if(c!=-1){P.splice(c,1);X.triggerRedrawOverlay()}}function b(e){for(var d=0;d<P.length;++d){var c=P[d];if(c.series==e){return d}}return -1}function J(g,e){var f=g.getOptions();var h=f.series.pie.radius>1?f.series.pie.radius:a*f.series.pie.radius;e.save();e.translate(Y,L);e.scale(1,f.series.pie.tilt);for(var c=0;c<P.length;++c){d(P[c].series)}U(e);e.restore();function d(i){if(i.angle<=0||isNaN(i.angle)){return}e.fillStyle="rgba(255, 255, 255, "+f.series.pie.highlight.opacity+")";e.beginPath();if(Math.abs(i.angle-Math.PI*2)>1e-09){e.moveTo(0,0)}e.arc(0,0,h,i.startAngle,i.startAngle+i.angle/2,false);e.arc(0,0,h,i.startAngle+i.angle/2,i.startAngle+i.angle,false);e.closePath();e.fill()}}}var B={series:{pie:{show:false,radius:"auto",innerRadius:0,startAngle:3/2,tilt:1,shadow:{left:5,top:15,alpha:0.02},offset:{top:0,left:"auto"},stroke:{color:"#fff",width:1},label:{show:"auto",formatter:function(F,G){return"<div style='font-size:x-small;text-align:center;padding:2px;color:"+G.color+";'>"+F+"<br/>"+Math.round(G.percent)+"%</div>"},radius:1,background:{color:null,opacity:0},threshold:0},combine:{threshold:-1,color:null,label:"Other"},highlight:{opacity:0.5}}}};E.plot.plugins.push({init:D,options:B,name:"pie",version:"1.1"})})(jQuery);