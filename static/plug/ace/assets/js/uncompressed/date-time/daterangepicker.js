!function(C,B){var A=function(H,E,D){this.parentEl="body";this.element=C(H);var F='<div class="daterangepicker dropdown-menu"><div class="calendar left"></div><div class="calendar right"></div><div class="ranges"><div class="range_inputs"><div class="daterangepicker_start_input"><label for="daterangepicker_start"></label><input class="input-mini" type="text" name="daterangepicker_start" value="" disabled="disabled" /></div><div class="daterangepicker_end_input"><label for="daterangepicker_end"></label><input class="input-mini" type="text" name="daterangepicker_end" value="" disabled="disabled" /></div><button class="applyBtn" disabled="disabled"></button>&nbsp;<button class="cancelBtn"></button></div></div></div>';if(typeof E!=="object"||E===null){E={}}this.parentEl=(typeof E==="object"&&E.parentEl&&C(E.parentEl).length)?C(E.parentEl):C(this.parentEl);this.container=C(F).appendTo(this.parentEl);this.setOptions(E,D);var G=this.container;C.each(this.buttonClasses,function(I,J){G.find("button").addClass(J)});this.container.find(".daterangepicker_start_input label").html(this.locale.fromLabel);this.container.find(".daterangepicker_end_input label").html(this.locale.toLabel);if(this.applyClass.length){this.container.find(".applyBtn").addClass(this.applyClass)}if(this.cancelClass.length){this.container.find(".cancelBtn").addClass(this.cancelClass)}this.container.find(".applyBtn").html(this.locale.applyLabel);this.container.find(".cancelBtn").html(this.locale.cancelLabel);this.container.find(".calendar").on("click.daterangepicker",".prev",C.proxy(this.clickPrev,this)).on("click.daterangepicker",".next",C.proxy(this.clickNext,this)).on("click.daterangepicker","td.available",C.proxy(this.clickDate,this)).on("mouseenter.daterangepicker","td.available",C.proxy(this.enterDate,this)).on("mouseleave.daterangepicker","td.available",C.proxy(this.updateFormInputs,this)).on("change.daterangepicker","select.yearselect",C.proxy(this.updateMonthYear,this)).on("change.daterangepicker","select.monthselect",C.proxy(this.updateMonthYear,this)).on("change.daterangepicker","select.hourselect,select.minuteselect,select.ampmselect",C.proxy(this.updateTime,this));this.container.find(".ranges").on("click.daterangepicker","button.applyBtn",C.proxy(this.clickApply,this)).on("click.daterangepicker","button.cancelBtn",C.proxy(this.clickCancel,this)).on("click.daterangepicker",".daterangepicker_start_input,.daterangepicker_end_input",C.proxy(this.showCalendars,this)).on("click.daterangepicker","li",C.proxy(this.clickRange,this)).on("mouseenter.daterangepicker","li",C.proxy(this.enterRange,this)).on("mouseleave.daterangepicker","li",C.proxy(this.updateFormInputs,this));if(this.element.is("input")){this.element.on({"click.daterangepicker":C.proxy(this.show,this),"focus.daterangepicker":C.proxy(this.show,this),"keyup.daterangepicker":C.proxy(this.updateFromControl,this)})}else{this.element.on("click.daterangepicker",C.proxy(this.toggle,this))}};A.prototype={constructor:A,setOptions:function(J,D){this.startDate=B().startOf("day");this.endDate=B().endOf("day");this.minDate=false;this.maxDate=false;this.dateLimit=false;this.showDropdowns=false;this.showWeekNumbers=false;this.timePicker=false;this.timePickerIncrement=30;this.timePicker12Hour=true;this.singleDatePicker=false;this.ranges={};this.opens="right";if(this.element.hasClass("pull-right")){this.opens="left"}this.buttonClasses=["btn","btn-small"];this.applyClass="btn-success";this.cancelClass="btn-default";this.format="MM/DD/YYYY";this.separator=" - ";this.locale={applyLabel:"Apply",cancelLabel:"Cancel",fromLabel:"From",toLabel:"To",weekLabel:"W",customRangeLabel:"Custom Range",daysOfWeek:B()._lang._weekdaysMin.slice(),monthNames:B()._lang._monthsShort.slice(),firstDay:0};this.cb=function(){};if(typeof J.format==="string"){this.format=J.format}if(typeof J.separator==="string"){this.separator=J.separator}if(typeof J.startDate==="string"){this.startDate=B(J.startDate,this.format)}if(typeof J.endDate==="string"){this.endDate=B(J.endDate,this.format)}if(typeof J.minDate==="string"){this.minDate=B(J.minDate,this.format)}if(typeof J.maxDate==="string"){this.maxDate=B(J.maxDate,this.format)}if(typeof J.startDate==="object"){this.startDate=B(J.startDate)}if(typeof J.endDate==="object"){this.endDate=B(J.endDate)}if(typeof J.minDate==="object"){this.minDate=B(J.minDate)}if(typeof J.maxDate==="object"){this.maxDate=B(J.maxDate)}if(typeof J.applyClass==="string"){this.applyClass=J.applyClass}if(typeof J.cancelClass==="string"){this.cancelClass=J.cancelClass}if(typeof J.dateLimit==="object"){this.dateLimit=J.dateLimit}if(typeof J.locale==="object"){if(typeof J.locale.daysOfWeek==="object"){this.locale.daysOfWeek=J.locale.daysOfWeek.slice()}if(typeof J.locale.monthNames==="object"){this.locale.monthNames=J.locale.monthNames.slice()}if(typeof J.locale.firstDay==="number"){this.locale.firstDay=J.locale.firstDay;var K=J.locale.firstDay;while(K>0){this.locale.daysOfWeek.push(this.locale.daysOfWeek.shift());K--}}if(typeof J.locale.applyLabel==="string"){this.locale.applyLabel=J.locale.applyLabel}if(typeof J.locale.cancelLabel==="string"){this.locale.cancelLabel=J.locale.cancelLabel}if(typeof J.locale.fromLabel==="string"){this.locale.fromLabel=J.locale.fromLabel}if(typeof J.locale.toLabel==="string"){this.locale.toLabel=J.locale.toLabel}if(typeof J.locale.weekLabel==="string"){this.locale.weekLabel=J.locale.weekLabel}if(typeof J.locale.customRangeLabel==="string"){this.locale.customRangeLabel=J.locale.customRangeLabel}}if(typeof J.opens==="string"){this.opens=J.opens}if(typeof J.showWeekNumbers==="boolean"){this.showWeekNumbers=J.showWeekNumbers}if(typeof J.buttonClasses==="string"){this.buttonClasses=[J.buttonClasses]}if(typeof J.buttonClasses==="object"){this.buttonClasses=J.buttonClasses}if(typeof J.showDropdowns==="boolean"){this.showDropdowns=J.showDropdowns}if(typeof J.singleDatePicker==="boolean"){this.singleDatePicker=J.singleDatePicker}if(typeof J.timePicker==="boolean"){this.timePicker=J.timePicker}if(typeof J.timePickerIncrement==="number"){this.timePickerIncrement=J.timePickerIncrement}if(typeof J.timePicker12Hour==="boolean"){this.timePicker12Hour=J.timePicker12Hour}var E,G,M;if(typeof J.startDate==="undefined"&&typeof J.endDate==="undefined"){if(C(this.element).is("input[type=text]")){var I=C(this.element).val();var L=I.split(this.separator);E=G=null;if(L.length==2){E=B(L[0],this.format);G=B(L[1],this.format)}else{if(this.singleDatePicker){E=B(I,this.format);G=B(I,this.format)}}if(E!==null&&G!==null){this.startDate=E;this.endDate=G}}}if(typeof J.ranges==="object"){for(M in J.ranges){E=B(J.ranges[M][0]);G=B(J.ranges[M][1]);if(this.minDate&&E.isBefore(this.minDate)){E=B(this.minDate)}if(this.maxDate&&G.isAfter(this.maxDate)){G=B(this.maxDate)}if((this.minDate&&G.isBefore(this.minDate))||(this.maxDate&&E.isAfter(this.maxDate))){continue}this.ranges[M]=[E,G]}var F="<ul>";for(M in this.ranges){F+="<li>"+M+"</li>"}F+="<li>"+this.locale.customRangeLabel+"</li>";F+="</ul>";this.container.find(".ranges ul").remove();this.container.find(".ranges").prepend(F)}if(typeof D==="function"){this.cb=D}if(!this.timePicker){this.startDate=this.startDate.startOf("day");this.endDate=this.endDate.endOf("day")}if(this.singleDatePicker){this.opens="right";this.container.find(".calendar.right").show();this.container.find(".calendar.left").hide();this.container.find(".ranges").hide();if(!this.container.find(".calendar.right").hasClass("single")){this.container.find(".calendar.right").addClass("single")}}else{this.container.find(".calendar.right").removeClass("single");this.container.find(".ranges").show()}this.oldStartDate=this.startDate.clone();this.oldEndDate=this.endDate.clone();this.oldChosenLabel=this.chosenLabel;this.leftCalendar={month:B([this.startDate.year(),this.startDate.month(),1,this.startDate.hour(),this.startDate.minute()]),calendar:[]};this.rightCalendar={month:B([this.endDate.year(),this.endDate.month(),1,this.endDate.hour(),this.endDate.minute()]),calendar:[]};if(this.opens=="right"){var H=this.container.find(".calendar.left");var N=this.container.find(".calendar.right");H.removeClass("left").addClass("right");N.removeClass("right").addClass("left")}if(typeof J.ranges==="undefined"&&!this.singleDatePicker){this.container.addClass("show-calendar")}this.container.addClass("opens"+this.opens);this.updateView();this.updateCalendars()},setStartDate:function(D){if(typeof D==="string"){this.startDate=B(D,this.format)}if(typeof D==="object"){this.startDate=B(D)}if(!this.timePicker){this.startDate=this.startDate.startOf("day")}this.oldStartDate=this.startDate.clone();this.updateView();this.updateCalendars()},setEndDate:function(D){if(typeof D==="string"){this.endDate=B(D,this.format)}if(typeof D==="object"){this.endDate=B(D)}if(!this.timePicker){this.endDate=this.endDate.endOf("day")}this.oldEndDate=this.endDate.clone();this.updateView();this.updateCalendars()},updateView:function(){this.leftCalendar.month.month(this.startDate.month()).year(this.startDate.year());this.rightCalendar.month.month(this.endDate.month()).year(this.endDate.year());this.updateFormInputs()},updateFormInputs:function(){this.container.find("input[name=daterangepicker_start]").val(this.startDate.format(this.format));this.container.find("input[name=daterangepicker_end]").val(this.endDate.format(this.format));if(this.startDate.isSame(this.endDate)||this.startDate.isBefore(this.endDate)){this.container.find("button.applyBtn").removeAttr("disabled")}else{this.container.find("button.applyBtn").attr("disabled","disabled")}},updateFromControl:function(){if(!this.element.is("input")){return}if(!this.element.val().length){return}var E=this.element.val().split(this.separator),F=null,D=null;if(E.length===2){F=B(E[0],this.format);D=B(E[1],this.format)}if(this.singleDatePicker||F===null||D===null){F=B(this.element.val(),this.format);D=F}if(D.isBefore(F)){return}this.oldStartDate=this.startDate.clone();this.oldEndDate=this.endDate.clone();this.startDate=F;this.endDate=D;if(!this.startDate.isSame(this.oldStartDate)||!this.endDate.isSame(this.oldEndDate)){this.notify()}this.updateCalendars()},notify:function(){this.updateView();this.cb(this.startDate,this.endDate,this.chosenLabel)},move:function(){var D={top:0,left:0};if(!this.parentEl.is("body")){D={top:this.parentEl.offset().top-this.parentEl.scrollTop(),left:this.parentEl.offset().left-this.parentEl.scrollLeft()}}if(this.opens=="left"){this.container.css({top:this.element.offset().top+this.element.outerHeight()-D.top,right:C(window).width()-this.element.offset().left-this.element.outerWidth()-D.left,left:"auto"});if(this.container.offset().left<0){this.container.css({right:"auto",left:9})}}else{this.container.css({top:this.element.offset().top+this.element.outerHeight()-D.top,left:this.element.offset().left-D.left,right:"auto"});if(this.container.offset().left+this.container.outerWidth()>C(window).width()){this.container.css({left:"auto",right:0})}}},toggle:function(D){if(this.element.hasClass("active")){this.hide()}else{this.show()}},show:function(D){this.element.addClass("active");this.container.show();this.move();this._outsideClickProxy=C.proxy(function(E){this.outsideClick(E)},this);C(document).on("mousedown.daterangepicker",this._outsideClickProxy).on("click.daterangepicker","[data-toggle=dropdown]",this._outsideClickProxy).on("focusin.daterangepicker",this._outsideClickProxy);this.element.trigger("show.daterangepicker",this)},outsideClick:function(D){var E=C(D.target);if(E.closest(this.element).length||E.closest(this.container).length||E.closest(".calendar-date").length){return}this.hide()},hide:function(D){C(document).off("mousedown.daterangepicker",this._outsideClickProxy).off("click.daterangepicker",this._outsideClickProxy).off("focusin.daterangepicker",this._outsideClickProxy);this.element.removeClass("active");this.container.hide();if(!this.startDate.isSame(this.oldStartDate)||!this.endDate.isSame(this.oldEndDate)){this.notify()}this.oldStartDate=this.startDate.clone();this.oldEndDate=this.endDate.clone();this.element.trigger("hide.daterangepicker",this)},enterRange:function(E){var D=E.target.innerHTML;if(D==this.locale.customRangeLabel){this.updateView()}else{var F=this.ranges[D];this.container.find("input[name=daterangepicker_start]").val(F[0].format(this.format));this.container.find("input[name=daterangepicker_end]").val(F[1].format(this.format))}},showCalendars:function(){this.container.addClass("show-calendar");this.move()},hideCalendars:function(){this.container.removeClass("show-calendar")},updateInputText:function(){if(this.element.is("input")&&!this.singleDatePicker){this.element.val(this.startDate.format(this.format)+this.separator+this.endDate.format(this.format))}else{if(this.element.is("input")){this.element.val(this.startDate.format(this.format))}}},clickRange:function(E){var D=E.target.innerHTML;this.chosenLabel=D;if(D==this.locale.customRangeLabel){this.showCalendars()}else{var F=this.ranges[D];this.startDate=F[0];this.endDate=F[1];if(!this.timePicker){this.startDate.startOf("day");this.endDate.endOf("day")}this.leftCalendar.month.month(this.startDate.month()).year(this.startDate.year()).hour(this.startDate.hour()).minute(this.startDate.minute());this.rightCalendar.month.month(this.endDate.month()).year(this.endDate.year()).hour(this.endDate.hour()).minute(this.endDate.minute());this.updateCalendars();this.updateInputText();this.hideCalendars();this.hide();this.element.trigger("apply.daterangepicker",this)}},clickPrev:function(D){var E=C(D.target).parents(".calendar");if(E.hasClass("left")){this.leftCalendar.month.subtract("month",1)}else{this.rightCalendar.month.subtract("month",1)}this.updateCalendars()},clickNext:function(D){var E=C(D.target).parents(".calendar");if(E.hasClass("left")){this.leftCalendar.month.add("month",1)}else{this.rightCalendar.month.add("month",1)}this.updateCalendars()},enterDate:function(D){var G=C(D.target).attr("data-title");var F=G.substr(1,1);var H=G.substr(3,1);var E=C(D.target).parents(".calendar");if(E.hasClass("left")){this.container.find("input[name=daterangepicker_start]").val(this.leftCalendar.calendar[F][H].format(this.format))}else{this.container.find("input[name=daterangepicker_end]").val(this.rightCalendar.calendar[F][H].format(this.format))}},clickDate:function(D){var K=C(D.target).attr("data-title");var J=K.substr(1,1);var M=K.substr(3,1);var H=C(D.target).parents(".calendar");var I,G;if(H.hasClass("left")){I=this.leftCalendar.calendar[J][M];G=this.endDate;if(typeof this.dateLimit==="object"){var L=B(I).add(this.dateLimit).startOf("day");if(G.isAfter(L)){G=L}}}else{I=this.startDate;G=this.rightCalendar.calendar[J][M];if(typeof this.dateLimit==="object"){var E=B(G).subtract(this.dateLimit).startOf("day");if(I.isBefore(E)){I=E}}}if(this.singleDatePicker&&H.hasClass("left")){G=I.clone()}else{if(this.singleDatePicker&&H.hasClass("right")){I=G.clone()}}H.find("td").removeClass("active");if(I.isSame(G)||I.isBefore(G)){C(D.target).addClass("active");this.startDate=I;this.endDate=G;this.chosenLabel=this.locale.customRangeLabel}else{if(I.isAfter(G)){C(D.target).addClass("active");var F=this.endDate.diff(this.startDate);this.startDate=I;this.endDate=B(I).add("ms",F);this.chosenLabel=this.locale.customRangeLabel}}this.leftCalendar.month.month(this.startDate.month()).year(this.startDate.year());this.rightCalendar.month.month(this.endDate.month()).year(this.endDate.year());this.updateCalendars();if(!this.timePicker){G.endOf("day")}if(this.singleDatePicker){this.clickApply()}},clickApply:function(D){this.updateInputText();this.hide();this.element.trigger("apply.daterangepicker",this)},clickCancel:function(D){this.startDate=this.oldStartDate;this.endDate=this.oldEndDate;this.chosenLabel=this.oldChosenLabel;this.updateView();this.updateCalendars();this.hide();this.element.trigger("cancel.daterangepicker",this)},updateMonthYear:function(E){var G=C(E.target).closest(".calendar").hasClass("left"),D=G?"left":"right",H=this.container.find(".calendar."+D);var I=parseInt(H.find(".monthselect").val(),10);var F=H.find(".yearselect").val();this[D+"Calendar"].month.month(I).year(F);this.updateCalendars()},updateTime:function(D){var I=C(D.target).closest(".calendar"),J=I.hasClass("left");var H=parseInt(I.find(".hourselect").val(),10);var G=parseInt(I.find(".minuteselect").val(),10);if(this.timePicker12Hour){var K=I.find(".ampmselect").val();if(K==="PM"&&H<12){H+=12}if(K==="AM"&&H===12){H=0}}if(J){var E=this.startDate.clone();E.hour(H);E.minute(G);this.startDate=E;this.leftCalendar.month.hour(H).minute(G)}else{var F=this.endDate.clone();F.hour(H);F.minute(G);this.endDate=F;this.rightCalendar.month.hour(H).minute(G)}this.updateCalendars()},updateCalendars:function(){this.leftCalendar.calendar=this.buildCalendar(this.leftCalendar.month.month(),this.leftCalendar.month.year(),this.leftCalendar.month.hour(),this.leftCalendar.month.minute(),"left");this.rightCalendar.calendar=this.buildCalendar(this.rightCalendar.month.month(),this.rightCalendar.month.year(),this.rightCalendar.month.hour(),this.rightCalendar.month.minute(),"right");this.container.find(".calendar.left").empty().html(this.renderCalendar(this.leftCalendar.calendar,this.startDate,this.minDate,this.maxDate));this.container.find(".calendar.right").empty().html(this.renderCalendar(this.rightCalendar.calendar,this.endDate,this.startDate,this.maxDate));this.container.find(".ranges li").removeClass("active");var F=true;var D=0;for(var E in this.ranges){if(this.timePicker){if(this.startDate.isSame(this.ranges[E][0])&&this.endDate.isSame(this.ranges[E][1])){F=false;this.chosenLabel=this.container.find(".ranges li:eq("+D+")").addClass("active").html()}}else{if(this.startDate.format("YYYY-MM-DD")==this.ranges[E][0].format("YYYY-MM-DD")&&this.endDate.format("YYYY-MM-DD")==this.ranges[E][1].format("YYYY-MM-DD")){F=false;this.chosenLabel=this.container.find(".ranges li:eq("+D+")").addClass("active").html()}}D++}if(F){this.chosenLabel=this.container.find(".ranges li:last").addClass("active").html()}},buildCalendar:function(E,N,M,L,F){var I=B([N,E,1]);var Q=B(I).subtract("month",1).month();var J=B(I).subtract("month",1).year();var H=B([J,Q]).daysInMonth();var P=I.day();var K;var D=[];for(K=0;K<6;K++){D[K]=[]}var O=H-P+this.locale.firstDay+1;if(O>H){O-=7}if(P==this.locale.firstDay){O=H-6}var G=B([J,Q,O,12,L]);var S,R;for(K=0,S=0,R=0;K<42;K++,S++,G=B(G).add("hour",24)){if(K>0&&S%7===0){S=0;R++}D[R][S]=G.clone().hour(M);G.hour(12)}return D},renderDropdowns:function(H,P,M){var F=H.month();var I='<select class="monthselect">';var O=false;var D=false;for(var K=0;K<12;K++){if((!O||K>=P.month())&&(!D||K<=M.month())){I+="<option value='"+K+"'"+(K===F?" selected='selected'":"")+">"+this.locale.monthNames[K]+"</option>"}}I+="</select>";var E=H.year();var L=(M&&M.year())||(E+5);var G=(P&&P.year())||(E-50);var J='<select class="yearselect">';for(var N=G;N<=L;N++){J+='<option value="'+N+'"'+(N===E?' selected="selected"':"")+">"+N+"</option>"}J+="</select>";return I+J},renderCalendar:function(E,M,R,P){var F='<div class="calendar-date">';F+='<table class="table-condensed">';F+="<thead>";F+="<tr>";if(this.showWeekNumbers){F+="<th></th>"}if(!R||R.isBefore(E[1][1])){F+='<th class="prev available"><i class="fa fa-arrow-left icon-arrow-left glyphicon glyphicon-arrow-left"></i></th>'}else{F+="<th></th>"}var L=this.locale.monthNames[E[1][1].month()]+E[1][1].format(" YYYY");if(this.showDropdowns){L=this.renderDropdowns(E[1][1],R,P)}F+='<th colspan="5" class="month">'+L+"</th>";if(!P||P.isAfter(E[1][1])){F+='<th class="next available"><i class="fa fa-arrow-right icon-arrow-right glyphicon glyphicon-arrow-right"></i></th>'}else{F+="<th></th>"}F+="</tr>";F+="<tr>";if(this.showWeekNumbers){F+='<th class="week">'+this.locale.weekLabel+"</th>"}C.each(this.locale.daysOfWeek,function(S,T){F+="<th>"+T+"</th>"});F+="</tr>";F+="</thead>";F+="<tbody>";for(var Q=0;Q<6;Q++){F+="<tr>";if(this.showWeekNumbers){F+='<td class="week">'+E[Q][0].week()+"</td>"}for(var D=0;D<7;D++){var I="available ";I+=(E[Q][D].month()==E[1][1].month())?"":"off";if((R&&E[Q][D].isBefore(R,"day"))||(P&&E[Q][D].isAfter(P,"day"))){I=" off disabled "}else{if(E[Q][D].format("YYYY-MM-DD")==M.format("YYYY-MM-DD")){I+=" active ";if(E[Q][D].format("YYYY-MM-DD")==this.startDate.format("YYYY-MM-DD")){I+=" start-date "}if(E[Q][D].format("YYYY-MM-DD")==this.endDate.format("YYYY-MM-DD")){I+=" end-date "}}else{if(E[Q][D]>=this.startDate&&E[Q][D]<=this.endDate){I+=" in-range ";if(E[Q][D].isSame(this.startDate)){I+=" start-date "}if(E[Q][D].isSame(this.endDate)){I+=" end-date "}}}}var O="r"+Q+"c"+D;F+='<td class="'+I.replace(/\s+/g," ").replace(/^\s?(.*?)\s?$/,"$1")+'" data-title="'+O+'">'+E[Q][D].date()+"</td>"}F+="</tr>"}F+="</tbody>";F+="</table>";F+="</div>";var N;if(this.timePicker){F+='<div class="calendar-time">';F+='<select class="hourselect">';var G=0;var J=23;var H=M.hour();if(this.timePicker12Hour){G=1;J=12;if(H>=12){H-=12}if(H===0){H=12}}for(N=G;N<=J;N++){if(N==H){F+='<option value="'+N+'" selected="selected">'+N+"</option>"}else{F+='<option value="'+N+'">'+N+"</option>"}}F+="</select> : ";F+='<select class="minuteselect">';for(N=0;N<60;N+=this.timePickerIncrement){var K=N;if(K<10){K="0"+K}if(N==M.minute()){F+='<option value="'+N+'" selected="selected">'+K+"</option>"}else{F+='<option value="'+N+'">'+K+"</option>"}}F+="</select> ";if(this.timePicker12Hour){F+='<select class="ampmselect">';if(M.hour()>=12){F+='<option value="AM">AM</option><option value="PM" selected="selected">PM</option>'}else{F+='<option value="AM" selected="selected">AM</option><option value="PM">PM</option>'}F+="</select>"}F+="</div>"}return F},remove:function(){this.container.remove();this.element.off(".daterangepicker");this.element.removeData("daterangepicker")}};C.fn.daterangepicker=function(E,D){this.each(function(){var F=C(this);if(F.data("daterangepicker")){F.data("daterangepicker").remove()}F.data("daterangepicker",new A(F,E,D))});return this}}(window.jQuery,window.moment);