(function($){$.jgrid=$.jgrid||{};$.extend($.jgrid,{version:"4.6.0",htmlDecode:function(value){if(value&&(value==="&nbsp;"||value==="&#160;"||(value.length===1&&value.charCodeAt(0)===160))){return""}return !value?value:String(value).replace(/&gt;/g,">").replace(/&lt;/g,"<").replace(/&quot;/g,'"').replace(/&amp;/g,"&")},htmlEncode:function(value){return !value?value:String(value).replace(/&/g,"&amp;").replace(/\"/g,"&quot;").replace(/</g,"&lt;").replace(/>/g,"&gt;")},format:function(format){var args=$.makeArray(arguments).slice(1);if(format==null){format=""}return format.replace(/\{(\d+)\}/g,function(m,i){return args[i]})},msie:navigator.appName==="Microsoft Internet Explorer",msiever:function(){var rv=-1;var ua=navigator.userAgent;var re=new RegExp("MSIE ([0-9]{1,}[.0-9]{0,})");if(re.exec(ua)!=null){rv=parseFloat(RegExp.$1)}return rv},getCellIndex:function(cell){var c=$(cell);if(c.is("tr")){return -1}c=(!c.is("td")&&!c.is("th")?c.closest("td,th"):c)[0];if($.jgrid.msie){return $.inArray(c,c.parentNode.cells)}return c.cellIndex},stripHtml:function(v){v=String(v);var regexp=/<("[^"]*"|'[^']*'|[^'">])*>/gi;if(v){v=v.replace(regexp,"");return(v&&v!=="&nbsp;"&&v!=="&#160;")?v.replace(/\"/g,"'"):""}return v},stripPref:function(pref,id){var obj=$.type(pref);if(obj==="string"||obj==="number"){pref=String(pref);id=pref!==""?String(id).replace(String(pref),""):id}return id},parse:function(jsonString){var js=jsonString;if(js.substr(0,9)==="while(1);"){js=js.substr(9)}if(js.substr(0,2)==="/*"){js=js.substr(2,js.length-4)}if(!js){js="{}"}return($.jgrid.useJSON===true&&typeof JSON==="object"&&typeof JSON.parse==="function")?JSON.parse(js):eval("("+js+")")},parseDate:function(format,date,newformat,opts){var token=/\\.|[dDjlNSwzWFmMntLoYyaABgGhHisueIOPTZcrU]/g,timezone=/\b(?:[PMCEA][SDP]T|(?:Pacific|Mountain|Central|Eastern|Atlantic) (?:Standard|Daylight|Prevailing) Time|(?:GMT|UTC)(?:[-+]\d{4})?)\b/g,timezoneClip=/[^-+\dA-Z]/g,msDateRegExp=new RegExp("^/Date\\((([-+])?[0-9]+)(([-+])([0-9]{2})([0-9]{2}))?\\)/$"),msMatch=((typeof date==="string")?date.match(msDateRegExp):null),pad=function(value,length){value=String(value);length=parseInt(length,10)||2;while(value.length<length){value="0"+value}return value},ts={m:1,d:1,y:1970,h:0,i:0,s:0,u:0},timestamp=0,dM,k,hl,h12to24=function(ampm,h){if(ampm===0){if(h===12){h=0}}else{if(h!==12){h+=12}}return h};if(opts===undefined){opts=$.jgrid.formatter.date}if(opts.parseRe===undefined){opts.parseRe=/[#%\\\/:_;.,\t\s-]/}if(opts.masks.hasOwnProperty(format)){format=opts.masks[format]}if(date&&date!=null){if(!isNaN(date-0)&&String(format).toLowerCase()==="u"){timestamp=new Date(parseFloat(date)*1000)}else{if(date.constructor===Date){timestamp=date}else{if(msMatch!==null){timestamp=new Date(parseInt(msMatch[1],10));if(msMatch[3]){var offset=Number(msMatch[5])*60+Number(msMatch[6]);offset*=((msMatch[4]==="-")?1:-1);offset-=timestamp.getTimezoneOffset();timestamp.setTime(Number(Number(timestamp)+(offset*60*1000)))}}else{var offset=0;if(opts.srcformat==="ISO8601Long"&&date.charAt(date.length-1)==="Z"){offset-=(new Date()).getTimezoneOffset()}date=String(date).replace(/\T/g,"#").replace(/\t/,"%").split(opts.parseRe);format=format.replace(/\T/g,"#").replace(/\t/,"%").split(opts.parseRe);for(k=0,hl=format.length;k<hl;k++){if(format[k]==="M"){dM=$.inArray(date[k],opts.monthNames);if(dM!==-1&&dM<12){date[k]=dM+1;ts.m=date[k]}}if(format[k]==="F"){dM=$.inArray(date[k],opts.monthNames,12);if(dM!==-1&&dM>11){date[k]=dM+1-12;ts.m=date[k]}}if(format[k]==="a"){dM=$.inArray(date[k],opts.AmPm);if(dM!==-1&&dM<2&&date[k]===opts.AmPm[dM]){date[k]=dM;ts.h=h12to24(date[k],ts.h)}}if(format[k]==="A"){dM=$.inArray(date[k],opts.AmPm);if(dM!==-1&&dM>1&&date[k]===opts.AmPm[dM]){date[k]=dM-2;ts.h=h12to24(date[k],ts.h)}}if(format[k]==="g"){ts.h=parseInt(date[k],10)}if(date[k]!==undefined){ts[format[k].toLowerCase()]=parseInt(date[k],10)}}if(ts.f){ts.m=ts.f}if(ts.m===0&&ts.y===0&&ts.d===0){return"&#160;"}ts.m=parseInt(ts.m,10)-1;var ty=ts.y;if(ty>=70&&ty<=99){ts.y=1900+ts.y}else{if(ty>=0&&ty<=69){ts.y=2000+ts.y}}timestamp=new Date(ts.y,ts.m,ts.d,ts.h,ts.i,ts.s,ts.u);if(offset>0){timestamp.setTime(Number(Number(timestamp)+(offset*60*1000)))}}}}}else{timestamp=new Date(ts.y,ts.m,ts.d,ts.h,ts.i,ts.s,ts.u)}if(newformat===undefined){return timestamp}if(opts.masks.hasOwnProperty(newformat)){newformat=opts.masks[newformat]}else{if(!newformat){newformat="Y-m-d"}}var G=timestamp.getHours(),i=timestamp.getMinutes(),j=timestamp.getDate(),n=timestamp.getMonth()+1,o=timestamp.getTimezoneOffset(),s=timestamp.getSeconds(),u=timestamp.getMilliseconds(),w=timestamp.getDay(),Y=timestamp.getFullYear(),N=(w+6)%7+1,z=(new Date(Y,n-1,j)-new Date(Y,0,1))/86400000,flags={d:pad(j),D:opts.dayNames[w],j:j,l:opts.dayNames[w+7],N:N,S:opts.S(j),w:w,z:z,W:N<5?Math.floor((z+N-1)/7)+1:Math.floor((z+N-1)/7)||((new Date(Y-1,0,1).getDay()+6)%7<4?53:52),F:opts.monthNames[n-1+12],m:pad(n),M:opts.monthNames[n-1],n:n,t:"?",L:"?",o:"?",Y:Y,y:String(Y).substring(2),a:G<12?opts.AmPm[0]:opts.AmPm[1],A:G<12?opts.AmPm[2]:opts.AmPm[3],B:"?",g:G%12||12,G:G,h:pad(G%12||12),H:pad(G),i:pad(i),s:pad(s),u:u,e:"?",I:"?",O:(o>0?"-":"+")+pad(Math.floor(Math.abs(o)/60)*100+Math.abs(o)%60,4),P:"?",T:(String(timestamp).match(timezone)||[""]).pop().replace(timezoneClip,""),Z:"?",c:"?",r:"?",U:Math.floor(timestamp/1000)};return newformat.replace(token,function($0){return flags.hasOwnProperty($0)?flags[$0]:$0.substring(1)})},jqID:function(sid){return String(sid).replace(/[!"#$%&'()*+,.\/:; <=>?@\[\\\]\^`{|}~]/g,"\\$&")},guid:1,uidPref:"jqg",randId:function(prefix){return(prefix||$.jgrid.uidPref)+($.jgrid.guid++)},getAccessor:function(obj,expr){var ret,p,prm=[],i;if(typeof expr==="function"){return expr(obj)}ret=obj[expr];if(ret===undefined){try{if(typeof expr==="string"){prm=expr.split(".")}i=prm.length;if(i){ret=obj;while(ret&&i--){p=prm.shift();ret=ret[p]}}}catch(e){}}return ret},getXmlData:function(obj,expr,returnObj){var ret,m=typeof expr==="string"?expr.match(/^(.*)\[(\w+)\]$/):null;if(typeof expr==="function"){return expr(obj)}if(m&&m[2]){return m[1]?$(m[1],obj).attr(m[2]):$(obj).attr(m[2])}ret=$(expr,obj);if(returnObj){return ret}return ret.length>0?$(ret).text():undefined},cellWidth:function(){var $testDiv=$("<div class='ui-jqgrid' style='left:10000px'><table class='ui-jqgrid-btable' style='width:5px;'><tr class='jqgrow'><td style='width:5px;display:block;'></td></tr></table></div>"),testCell=$testDiv.appendTo("body").find("td").width();$testDiv.remove();return Math.abs(testCell-5)>0.1},cell_width:true,ajaxOptions:{},from:function(source){var QueryObject=function(d,q){if(typeof d==="string"){d=$.data(d)}var self=this,_data=d,_usecase=true,_trim=false,_query=q,_stripNum=/[\$,%]/g,_lastCommand=null,_lastField=null,_orDepth=0,_negate=false,_queuedOperator="",_sorting=[],_useProperties=true;if(typeof d==="object"&&d.push){if(d.length>0){if(typeof d[0]!=="object"){_useProperties=false}else{_useProperties=true}}}else{throw"data provides is not an array"}this._hasData=function(){return _data===null?false:_data.length===0?false:true};this._getStr=function(s){var phrase=[];if(_trim){phrase.push("jQuery.trim(")}phrase.push("String("+s+")");if(_trim){phrase.push(")")}if(!_usecase){phrase.push(".toLowerCase()")}return phrase.join("")};this._strComp=function(val){if(typeof val==="string"){return".toString()"}return""};this._group=function(f,u){return({field:f.toString(),unique:u,items:[]})};this._toStr=function(phrase){if(_trim){phrase=$.trim(phrase)}phrase=phrase.toString().replace(/\\/g,"\\\\").replace(/\"/g,'\\"');return _usecase?phrase:phrase.toLowerCase()};this._funcLoop=function(func){var results=[];$.each(_data,function(i,v){results.push(func(v))});return results};this._append=function(s){var i;if(_query===null){_query=""}else{_query+=_queuedOperator===""?" && ":_queuedOperator}for(i=0;i<_orDepth;i++){_query+="("}if(_negate){_query+="!"}_query+="("+s+")";_negate=false;_queuedOperator="";_orDepth=0};this._setCommand=function(f,c){_lastCommand=f;_lastField=c};this._resetNegate=function(){_negate=false};this._repeatCommand=function(f,v){if(_lastCommand===null){return self}if(f!==null&&v!==null){return _lastCommand(f,v)}if(_lastField===null){return _lastCommand(f)}if(!_useProperties){return _lastCommand(f)}return _lastCommand(_lastField,f)};this._equals=function(a,b){return(self._compare(a,b,1)===0)};this._compare=function(a,b,d){var toString=Object.prototype.toString;if(d===undefined){d=1}if(a===undefined){a=null}if(b===undefined){b=null}if(a===null&&b===null){return 0}if(a===null&&b!==null){return 1}if(a!==null&&b===null){return -1}if(toString.call(a)==="[object Date]"&&toString.call(b)==="[object Date]"){if(a<b){return -d}if(a>b){return d}return 0}if(!_usecase&&typeof a!=="number"&&typeof b!=="number"){a=String(a);b=String(b)}if(a<b){return -d}if(a>b){return d}return 0};this._performSort=function(){if(_sorting.length===0){return}_data=self._doSort(_data,0)};this._doSort=function(d,q){var by=_sorting[q].by,dir=_sorting[q].dir,type=_sorting[q].type,dfmt=_sorting[q].datefmt,sfunc=_sorting[q].sfunc;if(q===_sorting.length-1){return self._getOrder(d,by,dir,type,dfmt,sfunc)}q++;var values=self._getGroup(d,by,dir,type,dfmt),results=[],i,j,sorted;for(i=0;i<values.length;i++){sorted=self._doSort(values[i].items,q);for(j=0;j<sorted.length;j++){results.push(sorted[j])}}return results};this._getOrder=function(data,by,dir,type,dfmt,sfunc){var sortData=[],_sortData=[],newDir=dir==="a"?1:-1,i,ab,j,findSortKey;if(type===undefined){type="text"}if(type==="float"||type==="number"||type==="currency"||type==="numeric"){findSortKey=function($cell){var key=parseFloat(String($cell).replace(_stripNum,""));return isNaN(key)?0:key}}else{if(type==="int"||type==="integer"){findSortKey=function($cell){return $cell?parseFloat(String($cell).replace(_stripNum,"")):0}}else{if(type==="date"||type==="datetime"){findSortKey=function($cell){return $.jgrid.parseDate(dfmt,$cell).getTime()}}else{if($.isFunction(type)){findSortKey=type}else{findSortKey=function($cell){$cell=$cell?$.trim(String($cell)):"";return _usecase?$cell:$cell.toLowerCase()}}}}}$.each(data,function(i,v){ab=by!==""?$.jgrid.getAccessor(v,by):v;if(ab===undefined){ab=""}ab=findSortKey(ab,v);_sortData.push({"vSort":ab,"index":i})});if($.isFunction(sfunc)){_sortData.sort(function(a,b){a=a.vSort;b=b.vSort;return sfunc.call(this,a,b,newDir)})}else{_sortData.sort(function(a,b){a=a.vSort;b=b.vSort;return self._compare(a,b,newDir)})}j=0;var nrec=data.length;while(j<nrec){i=_sortData[j].index;sortData.push(data[i]);j++}return sortData};this._getGroup=function(data,by,dir,type,dfmt){var results=[],group=null,last=null,val;$.each(self._getOrder(data,by,dir,type,dfmt),function(i,v){val=$.jgrid.getAccessor(v,by);if(val==null){val=""}if(!self._equals(last,val)){last=val;if(group!==null){results.push(group)}group=self._group(by,val)}group.items.push(v)});if(group!==null){results.push(group)}return results};this.ignoreCase=function(){_usecase=false;return self};this.useCase=function(){_usecase=true;return self};this.trim=function(){_trim=true;return self};this.noTrim=function(){_trim=false;return self};this.execute=function(){var match=_query,results=[];if(match===null){return self}$.each(_data,function(){if(eval(match)){results.push(this)}});_data=results;return self};this.data=function(){return _data};this.select=function(f){self._performSort();if(!self._hasData()){return[]}self.execute();if($.isFunction(f)){var results=[];$.each(_data,function(i,v){results.push(f(v))});return results}return _data};this.hasMatch=function(){if(!self._hasData()){return false}self.execute();return _data.length>0};this.andNot=function(f,v,x){_negate=!_negate;return self.and(f,v,x)};this.orNot=function(f,v,x){_negate=!_negate;return self.or(f,v,x)};this.not=function(f,v,x){return self.andNot(f,v,x)};this.and=function(f,v,x){_queuedOperator=" && ";if(f===undefined){return self}return self._repeatCommand(f,v,x)};this.or=function(f,v,x){_queuedOperator=" || ";if(f===undefined){return self}return self._repeatCommand(f,v,x)};this.orBegin=function(){_orDepth++;return self};this.orEnd=function(){if(_query!==null){_query+=")"}return self};this.isNot=function(f){_negate=!_negate;return self.is(f)};this.is=function(f){self._append("this."+f);self._resetNegate();return self};this._compareValues=function(func,f,v,how,t){var fld;if(_useProperties){fld="jQuery.jgrid.getAccessor(this,'"+f+"')"}else{fld="this"}if(v===undefined){v=null}var val=v,swst=t.stype===undefined?"text":t.stype;if(v!==null){switch(swst){case"int":case"integer":val=(isNaN(Number(val))||val==="")?"0":val;fld="parseInt("+fld+",10)";val="parseInt("+val+",10)";break;case"float":case"number":case"numeric":val=String(val).replace(_stripNum,"");val=(isNaN(Number(val))||val==="")?"0":val;fld="parseFloat("+fld+")";val="parseFloat("+val+")";break;case"date":case"datetime":val=String($.jgrid.parseDate(t.newfmt||"Y-m-d",val).getTime());fld='jQuery.jgrid.parseDate("'+t.srcfmt+'",'+fld+").getTime()";break;default:fld=self._getStr(fld);val=self._getStr('"'+self._toStr(val)+'"')}}self._append(fld+" "+how+" "+val);self._setCommand(func,f);self._resetNegate();return self};this.equals=function(f,v,t){return self._compareValues(self.equals,f,v,"==",t)};this.notEquals=function(f,v,t){return self._compareValues(self.equals,f,v,"!==",t)};this.isNull=function(f,v,t){return self._compareValues(self.equals,f,null,"===",t)};this.greater=function(f,v,t){return self._compareValues(self.greater,f,v,">",t)};this.less=function(f,v,t){return self._compareValues(self.less,f,v,"<",t)};this.greaterOrEquals=function(f,v,t){return self._compareValues(self.greaterOrEquals,f,v,">=",t)};this.lessOrEquals=function(f,v,t){return self._compareValues(self.lessOrEquals,f,v,"<=",t)};this.startsWith=function(f,v){var val=(v==null)?f:v,length=_trim?$.trim(val.toString()).length:val.toString().length;if(_useProperties){self._append(self._getStr("jQuery.jgrid.getAccessor(this,'"+f+"')")+".substr(0,"+length+") == "+self._getStr('"'+self._toStr(v)+'"'))}else{if(v!=null){length=_trim?$.trim(v.toString()).length:v.toString().length}self._append(self._getStr("this")+".substr(0,"+length+") == "+self._getStr('"'+self._toStr(f)+'"'))}self._setCommand(self.startsWith,f);self._resetNegate();return self};this.endsWith=function(f,v){var val=(v==null)?f:v,length=_trim?$.trim(val.toString()).length:val.toString().length;if(_useProperties){self._append(self._getStr("jQuery.jgrid.getAccessor(this,'"+f+"')")+".substr("+self._getStr("jQuery.jgrid.getAccessor(this,'"+f+"')")+".length-"+length+","+length+') == "'+self._toStr(v)+'"')}else{self._append(self._getStr("this")+".substr("+self._getStr("this")+'.length-"'+self._toStr(f)+'".length,"'+self._toStr(f)+'".length) == "'+self._toStr(f)+'"')}self._setCommand(self.endsWith,f);self._resetNegate();return self};this.contains=function(f,v){if(_useProperties){self._append(self._getStr("jQuery.jgrid.getAccessor(this,'"+f+"')")+'.indexOf("'+self._toStr(v)+'",0) > -1')}else{self._append(self._getStr("this")+'.indexOf("'+self._toStr(f)+'",0) > -1')}self._setCommand(self.contains,f);self._resetNegate();return self};this.groupBy=function(by,dir,type,datefmt){if(!self._hasData()){return null}return self._getGroup(_data,by,dir,type,datefmt)};this.orderBy=function(by,dir,stype,dfmt,sfunc){dir=dir==null?"a":$.trim(dir.toString().toLowerCase());if(stype==null){stype="text"}if(dfmt==null){dfmt="Y-m-d"}if(sfunc==null){sfunc=false}if(dir==="desc"||dir==="descending"){dir="d"}if(dir==="asc"||dir==="ascending"){dir="a"}_sorting.push({by:by,dir:dir,type:stype,datefmt:dfmt,sfunc:sfunc});return self};return self};return new QueryObject(source,null)},getMethod:function(name){return this.getAccessor($.fn.jqGrid,name)},extend:function(methods){$.extend($.fn.jqGrid,methods);if(!this.no_legacy_api){$.fn.extend(methods)}}});$.fn.jqGrid=function(pin){if(typeof pin==="string"){var fn=$.jgrid.getMethod(pin);if(!fn){throw ("jqGrid - No such method: "+pin)}var args=$.makeArray(arguments).slice(1);return fn.apply(this,args)}return this.each(function(){if(this.grid){return}var p=$.extend(true,{url:"",height:150,page:1,rowNum:20,rowTotal:null,records:0,pager:"",pgbuttons:true,pginput:true,colModel:[],rowList:[],colNames:[],sortorder:"asc",sortname:"",datatype:"xml",mtype:"GET",altRows:false,selarrrow:[],savedRow:[],shrinkToFit:true,xmlReader:{},jsonReader:{},subGrid:false,subGridModel:[],reccount:0,lastpage:0,lastsort:0,selrow:null,beforeSelectRow:null,onSelectRow:null,onSortCol:null,ondblClickRow:null,onRightClickRow:null,onPaging:null,onSelectAll:null,onInitGrid:null,loadComplete:null,gridComplete:null,loadError:null,loadBeforeSend:null,afterInsertRow:null,beforeRequest:null,beforeProcessing:null,onHeaderClick:null,viewrecords:false,loadonce:false,multiselect:false,multikey:false,editurl:null,search:false,caption:"",hidegrid:true,hiddengrid:false,postData:{},userData:{},treeGrid:false,treeGridModel:"nested",treeReader:{},treeANode:-1,ExpandColumn:null,tree_root_level:0,prmNames:{page:"page",rows:"rows",sort:"sidx",order:"sord",search:"_search",nd:"nd",id:"id",oper:"oper",editoper:"edit",addoper:"add",deloper:"del",subgridid:"id",npage:null,totalrows:"totalrows"},forceFit:false,gridstate:"visible",cellEdit:false,cellsubmit:"remote",nv:0,loadui:"enable",toolbar:[false,""],scroll:false,multiboxonly:false,deselectAfterSort:true,scrollrows:false,autowidth:false,scrollOffset:18,cellLayout:5,subGridWidth:20,multiselectWidth:20,gridview:false,rownumWidth:25,rownumbers:false,pagerpos:"center",recordpos:"right",footerrow:false,userDataOnFooter:false,hoverrows:true,altclass:"ui-priority-secondary",viewsortcols:[false,"vertical",true],resizeclass:"",autoencode:false,remapColumns:[],ajaxGridOptions:{},direction:"ltr",toppager:false,headertitles:false,scrollTimeout:40,data:[],_index:{},grouping:false,groupingView:{groupField:[],groupOrder:[],groupText:[],groupColumnShow:[],groupSummary:[],showSummaryOnHide:false,sortitems:[],sortnames:[],summary:[],summaryval:[],plusicon:"ui-icon-circlesmall-plus",minusicon:"ui-icon-circlesmall-minus",displayField:[],groupSummaryPos:[],formatDisplayField:[],_locgr:false},ignoreCase:false,cmTemplate:{},idPrefix:"",multiSort:false},$.jgrid.defaults,pin||{});var ts=this,grid={headers:[],cols:[],footers:[],dragStart:function(i,x,y){var gridLeftPos=$(this.bDiv).offset().left;this.resizing={idx:i,startX:x.clientX,sOL:x.clientX-gridLeftPos};this.hDiv.style.cursor="col-resize";this.curGbox=$("#rs_m"+$.jgrid.jqID(p.id),"#gbox_"+$.jgrid.jqID(p.id));this.curGbox.css({display:"block",left:x.clientX-gridLeftPos,top:y[1],height:y[2]});$(ts).triggerHandler("jqGridResizeStart",[x,i]);if($.isFunction(p.resizeStart)){p.resizeStart.call(ts,x,i)}document.onselectstart=function(){return false}},dragMove:function(x){if(this.resizing){var diff=x.clientX-this.resizing.startX,h=this.headers[this.resizing.idx],newWidth=p.direction==="ltr"?h.width+diff:h.width-diff,hn,nWn;if(newWidth>33){this.curGbox.css({left:this.resizing.sOL+diff});if(p.forceFit===true){hn=this.headers[this.resizing.idx+p.nv];nWn=p.direction==="ltr"?hn.width-diff:hn.width+diff;if(nWn>33){h.newWidth=newWidth;hn.newWidth=nWn}}else{this.newWidth=p.direction==="ltr"?p.tblwidth+diff:p.tblwidth-diff;h.newWidth=newWidth}}}},dragEnd:function(){this.hDiv.style.cursor="default";if(this.resizing){var idx=this.resizing.idx,nw=this.headers[idx].newWidth||this.headers[idx].width;nw=parseInt(nw,10);this.resizing=false;$("#rs_m"+$.jgrid.jqID(p.id)).css("display","none");p.colModel[idx].width=nw;this.headers[idx].width=nw;this.headers[idx].el.style.width=nw+"px";this.cols[idx].style.width=nw+"px";if(this.footers.length>0){this.footers[idx].style.width=nw+"px"}if(p.forceFit===true){nw=this.headers[idx+p.nv].newWidth||this.headers[idx+p.nv].width;this.headers[idx+p.nv].width=nw;this.headers[idx+p.nv].el.style.width=nw+"px";this.cols[idx+p.nv].style.width=nw+"px";if(this.footers.length>0){this.footers[idx+p.nv].style.width=nw+"px"}p.colModel[idx+p.nv].width=nw}else{p.tblwidth=this.newWidth||p.tblwidth;$("table:first",this.bDiv).css("width",p.tblwidth+"px");$("table:first",this.hDiv).css("width",p.tblwidth+"px");this.hDiv.scrollLeft=this.bDiv.scrollLeft;if(p.footerrow){$("table:first",this.sDiv).css("width",p.tblwidth+"px");this.sDiv.scrollLeft=this.bDiv.scrollLeft}}$(ts).triggerHandler("jqGridResizeStop",[nw,idx]);if($.isFunction(p.resizeStop)){p.resizeStop.call(ts,nw,idx)}}this.curGbox=null;document.onselectstart=function(){return true}},populateVisible:function(){if(grid.timer){clearTimeout(grid.timer)}grid.timer=null;var dh=$(grid.bDiv).height();if(!dh){return}var table=$("table:first",grid.bDiv);var rows,rh;if(table[0].rows.length){try{rows=table[0].rows[1];rh=rows?$(rows).outerHeight()||grid.prevRowHeight:grid.prevRowHeight}catch(pv){rh=grid.prevRowHeight}}if(!rh){return}grid.prevRowHeight=rh;var rn=p.rowNum;var scrollTop=grid.scrollTop=grid.bDiv.scrollTop;var ttop=Math.round(table.position().top)-scrollTop;var tbot=ttop+table.height();var div=rh*rn;var page,npage,empty;if(tbot<dh&&ttop<=0&&(p.lastpage===undefined||parseInt((tbot+scrollTop+div-1)/div,10)<=p.lastpage)){npage=parseInt((dh-tbot+div-1)/div,10);if(tbot>=0||npage<2||p.scroll===true){page=Math.round((tbot+scrollTop)/div)+1;ttop=-1}else{ttop=1}}if(ttop>0){page=parseInt(scrollTop/div,10)+1;npage=parseInt((scrollTop+dh)/div,10)+2-page;empty=true}if(npage){if(p.lastpage&&(page>p.lastpage||p.lastpage===1||(page===p.page&&page===p.lastpage))){return}if(grid.hDiv.loading){grid.timer=setTimeout(grid.populateVisible,p.scrollTimeout)}else{p.page=page;if(empty){grid.selectionPreserver(table[0]);grid.emptyRows.call(table[0],false,false)}grid.populate(npage)}}},scrollGrid:function(e){if(p.scroll){var scrollTop=grid.bDiv.scrollTop;if(grid.scrollTop===undefined){grid.scrollTop=0}if(scrollTop!==grid.scrollTop){grid.scrollTop=scrollTop;if(grid.timer){clearTimeout(grid.timer)}grid.timer=setTimeout(grid.populateVisible,p.scrollTimeout)}}grid.hDiv.scrollLeft=grid.bDiv.scrollLeft;if(p.footerrow){grid.sDiv.scrollLeft=grid.bDiv.scrollLeft}if(e){e.stopPropagation()}},selectionPreserver:function(ts){var p=ts.p,sr=p.selrow,sra=p.selarrrow?$.makeArray(p.selarrrow):null,left=ts.grid.bDiv.scrollLeft,restoreSelection=function(){var i;p.selrow=null;p.selarrrow=[];if(p.multiselect&&sra&&sra.length>0){for(i=0;i<sra.length;i++){if(sra[i]!==sr){$(ts).jqGrid("setSelection",sra[i],false,null)}}}if(sr){$(ts).jqGrid("setSelection",sr,false,null)}ts.grid.bDiv.scrollLeft=left;$(ts).unbind(".selectionPreserver",restoreSelection)};$(ts).bind("jqGridGridComplete.selectionPreserver",restoreSelection)}};if(this.tagName.toUpperCase()!=="TABLE"){alert("Element is not a table");return}if(document.documentMode!==undefined){if(document.documentMode<=5){alert("Grid can not be used in this ('quirks') mode!");return}}$(this).empty().attr("tabindex","0");this.p=p;this.p.useProp=!!$.fn.prop;var i,dir;if(this.p.colNames.length===0){for(i=0;i<this.p.colModel.length;i++){this.p.colNames[i]=this.p.colModel[i].label||this.p.colModel[i].name}}if(this.p.colNames.length!==this.p.colModel.length){alert($.jgrid.errors.model);return}var gv=$("<div class='ui-jqgrid-view'></div>"),isMSIE=$.jgrid.msie;ts.p.direction=$.trim(ts.p.direction.toLowerCase());if($.inArray(ts.p.direction,["ltr","rtl"])===-1){ts.p.direction="ltr"}dir=ts.p.direction;$(gv).insertBefore(this);$(this).removeClass("scroll").appendTo(gv);var eg=$("<div class='ui-jqgrid ui-widget ui-widget-content ui-corner-all'></div>");$(eg).attr({"id":"gbox_"+this.id,"dir":dir}).insertBefore(gv);$(gv).attr("id","gview_"+this.id).appendTo(eg);$("<div class='ui-widget-overlay jqgrid-overlay' id='lui_"+this.id+"'></div>").insertBefore(gv);$("<div class='loading ui-state-default ui-state-active' id='load_"+this.id+"'>"+this.p.loadtext+"</div>").insertBefore(gv);$(this).attr({cellspacing:"0",cellpadding:"0",border:"0","role":"grid","aria-multiselectable":!!this.p.multiselect,"aria-labelledby":"gbox_"+this.id});var sortkeys=["shiftKey","altKey","ctrlKey"],intNum=function(val,defval){val=parseInt(val,10);if(isNaN(val)){return defval||0}return val},formatCol=function(pos,rowInd,tv,rawObject,rowId,rdata){var cm=ts.p.colModel[pos],ral=cm.align,result='style="',clas=cm.classes,nm=cm.name,celp,acp=[];if(ral){result+="text-align:"+ral+";"}if(cm.hidden===true){result+="display:none;"}if(rowInd===0){result+="width: "+grid.headers[pos].width+"px;"}else{if(cm.cellattr&&$.isFunction(cm.cellattr)){celp=cm.cellattr.call(ts,rowId,tv,rawObject,cm,rdata);if(celp&&typeof celp==="string"){celp=celp.replace(/style/i,"style").replace(/title/i,"title");if(celp.indexOf("title")>-1){cm.title=false}if(celp.indexOf("class")>-1){clas=undefined}acp=celp.replace("-style","-sti").split(/style/);if(acp.length===2){acp[1]=$.trim(acp[1].replace("-sti","-style").replace("=",""));if(acp[1].indexOf("'")===0||acp[1].indexOf('"')===0){acp[1]=acp[1].substring(1)}result+=acp[1].replace(/'/gi,'"')}else{result+='"'}}}}if(!acp.length){acp[0]="";result+='"'}result+=(clas!==undefined?(' class="'+clas+'"'):"")+((cm.title&&tv)?(' title="'+$.jgrid.stripHtml(tv)+'"'):"");result+=' aria-describedby="'+ts.p.id+"_"+nm+'"';return result+acp[0]},cellVal=function(val){return val==null||val===""?"&#160;":(ts.p.autoencode?$.jgrid.htmlEncode(val):String(val))},formatter=function(rowId,cellval,colpos,rwdat,_act){var cm=ts.p.colModel[colpos],v;if(cm.formatter!==undefined){rowId=String(ts.p.idPrefix)!==""?$.jgrid.stripPref(ts.p.idPrefix,rowId):rowId;var opts={rowId:rowId,colModel:cm,gid:ts.p.id,pos:colpos};if($.isFunction(cm.formatter)){v=cm.formatter.call(ts,cellval,opts,rwdat,_act)}else{if($.fmatter){v=$.fn.fmatter.call(ts,cm.formatter,cellval,opts,rwdat,_act)}else{v=cellVal(cellval)}}}else{v=cellVal(cellval)}return v},addCell=function(rowId,cell,pos,irow,srvr,rdata){var v,prp;v=formatter(rowId,cell,pos,srvr,"add");prp=formatCol(pos,irow,v,srvr,rowId,rdata);return'<td role="gridcell" '+prp+">"+v+"</td>"},addMulti=function(rowid,pos,irow,checked){var v='<input role="checkbox" type="checkbox" id="jqg_'+ts.p.id+"_"+rowid+'" class="cbox" name="jqg_'+ts.p.id+"_"+rowid+'"'+(checked?'checked="checked"':"")+"/>",prp=formatCol(pos,irow,"",null,rowid,true);return'<td role="gridcell" '+prp+">"+v+"</td>"},addRowNum=function(pos,irow,pG,rN){var v=(parseInt(pG,10)-1)*parseInt(rN,10)+1+irow,prp=formatCol(pos,irow,v,null,irow,true);return'<td role="gridcell" class="ui-state-default jqgrid-rownum" '+prp+">"+v+"</td>"},reader=function(datatype){var field,f=[],j=0,i;for(i=0;i<ts.p.colModel.length;i++){field=ts.p.colModel[i];if(field.name!=="cb"&&field.name!=="subgrid"&&field.name!=="rn"){f[j]=datatype==="local"?field.name:((datatype==="xml"||datatype==="xmlstring")?field.xmlmap||field.name:field.jsonmap||field.name);if(ts.p.keyIndex!==false&&field.key===true){ts.p.keyName=f[j]}j++}}return f},orderedCols=function(offset){var order=ts.p.remapColumns;if(!order||!order.length){order=$.map(ts.p.colModel,function(v,i){return i})}if(offset){order=$.map(order,function(v){return v<offset?null:v-offset})}return order},emptyRows=function(scroll,locdata){var firstrow;if(this.p.deepempty){$(this.rows).slice(1).remove()}else{firstrow=this.rows.length>0?this.rows[0]:null;$(this.firstChild).empty().append(firstrow)}if(scroll&&this.p.scroll){$(this.grid.bDiv.firstChild).css({height:"auto"});$(this.grid.bDiv.firstChild.firstChild).css({height:0,display:"none"});if(this.grid.bDiv.scrollTop!==0){this.grid.bDiv.scrollTop=0}}if(locdata===true&&this.p.treeGrid){this.p.data=[];this.p._index={}}},refreshIndex=function(){var datalen=ts.p.data.length,idname,i,val,ni=ts.p.rownumbers===true?1:0,gi=ts.p.multiselect===true?1:0,si=ts.p.subGrid===true?1:0;if(ts.p.keyIndex===false||ts.p.loadonce===true){idname=ts.p.localReader.id}else{idname=ts.p.colModel[ts.p.keyIndex+gi+si+ni].name}for(i=0;i<datalen;i++){val=$.jgrid.getAccessor(ts.p.data[i],idname);if(val===undefined){val=String(i+1)}ts.p._index[val]=i}},constructTr=function(id,hide,altClass,rd,cur,selected){var tabindex="-1",restAttr="",attrName,style=hide?"display:none;":"",classes="ui-widget-content jqgrow ui-row-"+ts.p.direction+(altClass?" "+altClass:"")+(selected?" ui-state-highlight":""),rowAttrObj=$(ts).triggerHandler("jqGridRowAttr",[rd,cur,id]);if(typeof rowAttrObj!=="object"){rowAttrObj=$.isFunction(ts.p.rowattr)?ts.p.rowattr.call(ts,rd,cur,id):{}}if(!$.isEmptyObject(rowAttrObj)){if(rowAttrObj.hasOwnProperty("id")){id=rowAttrObj.id;delete rowAttrObj.id}if(rowAttrObj.hasOwnProperty("tabindex")){tabindex=rowAttrObj.tabindex;delete rowAttrObj.tabindex}if(rowAttrObj.hasOwnProperty("style")){style+=rowAttrObj.style;delete rowAttrObj.style}if(rowAttrObj.hasOwnProperty("class")){classes+=" "+rowAttrObj["class"];delete rowAttrObj["class"]}try{delete rowAttrObj.role}catch(ra){}for(attrName in rowAttrObj){if(rowAttrObj.hasOwnProperty(attrName)){restAttr+=" "+attrName+"="+rowAttrObj[attrName]}}}return'<tr role="row" id="'+id+'" tabindex="'+tabindex+'" class="'+classes+'"'+(style===""?"":' style="'+style+'"')+restAttr+">"},addXmlData=function(xml,t,rcnt,more,adjust){var startReq=new Date(),locdata=(ts.p.datatype!=="local"&&ts.p.loadonce)||ts.p.datatype==="xmlstring",xmlid="_id_",xmlRd=ts.p.xmlReader,frd=ts.p.datatype==="local"?"local":"xml";if(locdata){ts.p.data=[];ts.p._index={};ts.p.localReader.id=xmlid}ts.p.reccount=0;if($.isXMLDoc(xml)){if(ts.p.treeANode===-1&&!ts.p.scroll){emptyRows.call(ts,false,true);rcnt=1}else{rcnt=rcnt>1?rcnt:1}}else{return}var self=$(ts),i,fpos,ir=0,v,gi=ts.p.multiselect===true?1:0,si=0,addSubGridCell,ni=ts.p.rownumbers===true?1:0,idn,getId,f=[],F,rd={},xmlr,rid,rowData=[],cn=(ts.p.altRows===true)?ts.p.altclass:"",cn1;if(ts.p.subGrid===true){si=1;addSubGridCell=$.jgrid.getMethod("addSubGridCell")}if(!xmlRd.repeatitems){f=reader(frd)}if(ts.p.keyIndex===false){idn=$.isFunction(xmlRd.id)?xmlRd.id.call(ts,xml):xmlRd.id}else{idn=ts.p.keyIndex}if(f.length>0&&!isNaN(idn)){idn=ts.p.keyName}if(String(idn).indexOf("[")===-1){if(f.length){getId=function(trow,k){return $(idn,trow).text()||k}}else{getId=function(trow,k){return $(xmlRd.cell,trow).eq(idn).text()||k}}}else{getId=function(trow,k){return trow.getAttribute(idn.replace(/[\[\]]/g,""))||k}}ts.p.userData={};ts.p.page=intNum($.jgrid.getXmlData(xml,xmlRd.page),ts.p.page);ts.p.lastpage=intNum($.jgrid.getXmlData(xml,xmlRd.total),1);ts.p.records=intNum($.jgrid.getXmlData(xml,xmlRd.records));if($.isFunction(xmlRd.userdata)){ts.p.userData=xmlRd.userdata.call(ts,xml)||{}}else{$.jgrid.getXmlData(xml,xmlRd.userdata,true).each(function(){ts.p.userData[this.getAttribute("name")]=$(this).text()})}var gxml=$.jgrid.getXmlData(xml,xmlRd.root,true);gxml=$.jgrid.getXmlData(gxml,xmlRd.row,true);if(!gxml){gxml=[]}var gl=gxml.length,j=0,grpdata=[],rn=parseInt(ts.p.rowNum,10),br=ts.p.scroll?$.jgrid.randId():1,altr;if(gl>0&&ts.p.page<=0){ts.p.page=1}if(gxml&&gl){if(adjust){rn*=adjust+1}var afterInsRow=$.isFunction(ts.p.afterInsertRow),hiderow=false,groupingPrepare;if(ts.p.grouping){hiderow=ts.p.groupingView.groupCollapse===true;groupingPrepare=$.jgrid.getMethod("groupingPrepare")}while(j<gl){xmlr=gxml[j];rid=getId(xmlr,br+j);rid=ts.p.idPrefix+rid;altr=rcnt===0?0:rcnt+1;cn1=(altr+j)%2===1?cn:"";var iStartTrTag=rowData.length;rowData.push("");if(ni){rowData.push(addRowNum(0,j,ts.p.page,ts.p.rowNum))}if(gi){rowData.push(addMulti(rid,ni,j,false))}if(si){rowData.push(addSubGridCell.call(self,gi+ni,j+rcnt))}if(xmlRd.repeatitems){if(!F){F=orderedCols(gi+si+ni)}var cells=$.jgrid.getXmlData(xmlr,xmlRd.cell,true);$.each(F,function(k){var cell=cells[this];if(!cell){return false}v=cell.textContent||cell.text;rd[ts.p.colModel[k+gi+si+ni].name]=v;rowData.push(addCell(rid,v,k+gi+si+ni,j+rcnt,xmlr,rd))})}else{for(i=0;i<f.length;i++){v=$.jgrid.getXmlData(xmlr,f[i]);rd[ts.p.colModel[i+gi+si+ni].name]=v;rowData.push(addCell(rid,v,i+gi+si+ni,j+rcnt,xmlr,rd))}}rowData[iStartTrTag]=constructTr(rid,hiderow,cn1,rd,xmlr,false);rowData.push("</tr>");if(ts.p.grouping){grpdata.push(rowData);if(!ts.p.groupingView._locgr){groupingPrepare.call(self,rd,j)}rowData=[]}if(locdata||ts.p.treeGrid===true){rd[xmlid]=$.jgrid.stripPref(ts.p.idPrefix,rid);ts.p.data.push(rd);ts.p._index[rd[xmlid]]=ts.p.data.length-1}if(ts.p.gridview===false){$("tbody:first",t).append(rowData.join(""));self.triggerHandler("jqGridAfterInsertRow",[rid,rd,xmlr]);if(afterInsRow){ts.p.afterInsertRow.call(ts,rid,rd,xmlr)}rowData=[]}rd={};ir++;j++;if(ir===rn){break}}}if(ts.p.gridview===true){fpos=ts.p.treeANode>-1?ts.p.treeANode:0;if(ts.p.grouping){if(!locdata){self.jqGrid("groupingRender",grpdata,ts.p.colModel.length,ts.p.page,rn)}grpdata=null}else{if(ts.p.treeGrid===true&&fpos>0){$(ts.rows[fpos]).after(rowData.join(""))}else{$("tbody:first",t).append(rowData.join(""))}}}if(ts.p.subGrid===true){try{self.jqGrid("addSubGrid",gi+ni)}catch(_){}}ts.p.totaltime=new Date()-startReq;if(ir>0){if(ts.p.records===0){ts.p.records=gl}}rowData=null;if(ts.p.treeGrid===true){try{self.jqGrid("setTreeNode",fpos+1,ir+fpos+1)}catch(e){}}if(!ts.p.treeGrid&&!ts.p.scroll){ts.grid.bDiv.scrollTop=0}ts.p.reccount=ir;ts.p.treeANode=-1;if(ts.p.userDataOnFooter){self.jqGrid("footerData","set",ts.p.userData,true)}if(locdata){ts.p.records=gl;ts.p.lastpage=Math.ceil(gl/rn)}if(!more){ts.updatepager(false,true)}if(locdata){while(ir<gl){xmlr=gxml[ir];rid=getId(xmlr,ir+br);rid=ts.p.idPrefix+rid;if(xmlRd.repeatitems){if(!F){F=orderedCols(gi+si+ni)}var cells2=$.jgrid.getXmlData(xmlr,xmlRd.cell,true);$.each(F,function(k){var cell=cells2[this];if(!cell){return false}v=cell.textContent||cell.text;rd[ts.p.colModel[k+gi+si+ni].name]=v})}else{for(i=0;i<f.length;i++){v=$.jgrid.getXmlData(xmlr,f[i]);rd[ts.p.colModel[i+gi+si+ni].name]=v}}rd[xmlid]=$.jgrid.stripPref(ts.p.idPrefix,rid);if(ts.p.grouping){groupingPrepare.call(self,rd,ir)}ts.p.data.push(rd);ts.p._index[rd[xmlid]]=ts.p.data.length-1;rd={};ir++}if(ts.p.grouping){ts.p.groupingView._locgr=true;self.jqGrid("groupingRender",grpdata,ts.p.colModel.length,ts.p.page,rn);grpdata=null}}},addJSONData=function(data,t,rcnt,more,adjust){var startReq=new Date();if(data){if(ts.p.treeANode===-1&&!ts.p.scroll){emptyRows.call(ts,false,true);rcnt=1}else{rcnt=rcnt>1?rcnt:1}}else{return}var dReader,locid="_id_",frd,locdata=(ts.p.datatype!=="local"&&ts.p.loadonce)||ts.p.datatype==="jsonstring";if(locdata){ts.p.data=[];ts.p._index={};ts.p.localReader.id=locid}ts.p.reccount=0;if(ts.p.datatype==="local"){dReader=ts.p.localReader;frd="local"}else{dReader=ts.p.jsonReader;frd="json"}var self=$(ts),ir=0,v,i,j,f=[],cur,gi=ts.p.multiselect?1:0,si=ts.p.subGrid===true?1:0,addSubGridCell,ni=ts.p.rownumbers===true?1:0,arrayReader=orderedCols(gi+si+ni),objectReader=reader(frd),rowReader,len,drows,idn,rd={},fpos,idr,rowData=[],cn=(ts.p.altRows===true)?ts.p.altclass:"",cn1;ts.p.page=intNum($.jgrid.getAccessor(data,dReader.page),ts.p.page);ts.p.lastpage=intNum($.jgrid.getAccessor(data,dReader.total),1);ts.p.records=intNum($.jgrid.getAccessor(data,dReader.records));ts.p.userData=$.jgrid.getAccessor(data,dReader.userdata)||{};if(si){addSubGridCell=$.jgrid.getMethod("addSubGridCell")}if(ts.p.keyIndex===false){idn=$.isFunction(dReader.id)?dReader.id.call(ts,data):dReader.id}else{idn=ts.p.keyIndex}if(!dReader.repeatitems){f=objectReader;if(f.length>0&&!isNaN(idn)){idn=ts.p.keyName}}drows=$.jgrid.getAccessor(data,dReader.root);if(drows==null&&$.isArray(data)){drows=data}if(!drows){drows=[]}len=drows.length;i=0;if(len>0&&ts.p.page<=0){ts.p.page=1}var rn=parseInt(ts.p.rowNum,10),br=ts.p.scroll?$.jgrid.randId():1,altr,selected=false,selr;if(adjust){rn*=adjust+1}if(ts.p.datatype==="local"&&!ts.p.deselectAfterSort){selected=true}var afterInsRow=$.isFunction(ts.p.afterInsertRow),grpdata=[],hiderow=false,groupingPrepare;if(ts.p.grouping){hiderow=ts.p.groupingView.groupCollapse===true;groupingPrepare=$.jgrid.getMethod("groupingPrepare")}while(i<len){cur=drows[i];idr=$.jgrid.getAccessor(cur,idn);if(idr===undefined){if(typeof idn==="number"&&ts.p.colModel[idn+gi+si+ni]!=null){idr=$.jgrid.getAccessor(cur,ts.p.colModel[idn+gi+si+ni].name)}if(idr===undefined){idr=br+i;if(f.length===0){if(dReader.cell){var ccur=$.jgrid.getAccessor(cur,dReader.cell)||cur;idr=ccur!=null&&ccur[idn]!==undefined?ccur[idn]:idr;ccur=null}}}}idr=ts.p.idPrefix+idr;altr=rcnt===1?0:rcnt;cn1=(altr+i)%2===1?cn:"";if(selected){if(ts.p.multiselect){selr=($.inArray(idr,ts.p.selarrrow)!==-1)}else{selr=(idr===ts.p.selrow)}}var iStartTrTag=rowData.length;rowData.push("");if(ni){rowData.push(addRowNum(0,i,ts.p.page,ts.p.rowNum))}if(gi){rowData.push(addMulti(idr,ni,i,selr))}if(si){rowData.push(addSubGridCell.call(self,gi+ni,i+rcnt))}rowReader=objectReader;if(dReader.repeatitems){if(dReader.cell){cur=$.jgrid.getAccessor(cur,dReader.cell)||cur}if($.isArray(cur)){rowReader=arrayReader}}for(j=0;j<rowReader.length;j++){v=$.jgrid.getAccessor(cur,rowReader[j]);rd[ts.p.colModel[j+gi+si+ni].name]=v;rowData.push(addCell(idr,v,j+gi+si+ni,i+rcnt,cur,rd))}rowData[iStartTrTag]=constructTr(idr,hiderow,cn1,rd,cur,selr);rowData.push("</tr>");if(ts.p.grouping){grpdata.push(rowData);if(!ts.p.groupingView._locgr){groupingPrepare.call(self,rd,i)}rowData=[]}if(locdata||ts.p.treeGrid===true){rd[locid]=$.jgrid.stripPref(ts.p.idPrefix,idr);ts.p.data.push(rd);ts.p._index[rd[locid]]=ts.p.data.length-1}if(ts.p.gridview===false){$("#"+$.jgrid.jqID(ts.p.id)+" tbody:first").append(rowData.join(""));self.triggerHandler("jqGridAfterInsertRow",[idr,rd,cur]);if(afterInsRow){ts.p.afterInsertRow.call(ts,idr,rd,cur)}rowData=[]}rd={};ir++;i++;if(ir===rn){break}}if(ts.p.gridview===true){fpos=ts.p.treeANode>-1?ts.p.treeANode:0;if(ts.p.grouping){if(!locdata){self.jqGrid("groupingRender",grpdata,ts.p.colModel.length,ts.p.page,rn);grpdata=null}}else{if(ts.p.treeGrid===true&&fpos>0){$(ts.rows[fpos]).after(rowData.join(""))}else{$("#"+$.jgrid.jqID(ts.p.id)+" tbody:first").append(rowData.join(""))}}}if(ts.p.subGrid===true){try{self.jqGrid("addSubGrid",gi+ni)}catch(_){}}ts.p.totaltime=new Date()-startReq;if(ir>0){if(ts.p.records===0){ts.p.records=len}}rowData=null;if(ts.p.treeGrid===true){try{self.jqGrid("setTreeNode",fpos+1,ir+fpos+1)}catch(e){}}if(!ts.p.treeGrid&&!ts.p.scroll){ts.grid.bDiv.scrollTop=0}ts.p.reccount=ir;ts.p.treeANode=-1;if(ts.p.userDataOnFooter){self.jqGrid("footerData","set",ts.p.userData,true)}if(locdata){ts.p.records=len;ts.p.lastpage=Math.ceil(len/rn)}if(!more){ts.updatepager(false,true)}if(locdata){while(ir<len&&drows[ir]){cur=drows[ir];idr=$.jgrid.getAccessor(cur,idn);if(idr===undefined){if(typeof idn==="number"&&ts.p.colModel[idn+gi+si+ni]!=null){idr=$.jgrid.getAccessor(cur,ts.p.colModel[idn+gi+si+ni].name)}if(idr===undefined){idr=br+ir;if(f.length===0){if(dReader.cell){var ccur2=$.jgrid.getAccessor(cur,dReader.cell)||cur;idr=ccur2!=null&&ccur2[idn]!==undefined?ccur2[idn]:idr;ccur2=null}}}}if(cur){idr=ts.p.idPrefix+idr;rowReader=objectReader;if(dReader.repeatitems){if(dReader.cell){cur=$.jgrid.getAccessor(cur,dReader.cell)||cur}if($.isArray(cur)){rowReader=arrayReader}}for(j=0;j<rowReader.length;j++){rd[ts.p.colModel[j+gi+si+ni].name]=$.jgrid.getAccessor(cur,rowReader[j])}rd[locid]=$.jgrid.stripPref(ts.p.idPrefix,idr);if(ts.p.grouping){groupingPrepare.call(self,rd,ir)}ts.p.data.push(rd);ts.p._index[rd[locid]]=ts.p.data.length-1;rd={}}ir++}if(ts.p.grouping){ts.p.groupingView._locgr=true;self.jqGrid("groupingRender",grpdata,ts.p.colModel.length,ts.p.page,rn);grpdata=null}}},addLocalData=function(){var st=ts.p.multiSort?[]:"",sto=[],fndsort=false,cmtypes={},grtypes=[],grindexes=[],srcformat,sorttype,newformat;if(!$.isArray(ts.p.data)){return}var grpview=ts.p.grouping?ts.p.groupingView:false,lengrp,gin;$.each(ts.p.colModel,function(){sorttype=this.sorttype||"text";if(sorttype==="date"||sorttype==="datetime"){if(this.formatter&&typeof this.formatter==="string"&&this.formatter==="date"){if(this.formatoptions&&this.formatoptions.srcformat){srcformat=this.formatoptions.srcformat}else{srcformat=$.jgrid.formatter.date.srcformat}if(this.formatoptions&&this.formatoptions.newformat){newformat=this.formatoptions.newformat}else{newformat=$.jgrid.formatter.date.newformat}}else{srcformat=newformat=this.datefmt||"Y-m-d"}cmtypes[this.name]={"stype":sorttype,"srcfmt":srcformat,"newfmt":newformat,"sfunc":this.sortfunc||null}}else{cmtypes[this.name]={"stype":sorttype,"srcfmt":"","newfmt":"","sfunc":this.sortfunc||null}}if(ts.p.grouping){for(gin=0,lengrp=grpview.groupField.length;gin<lengrp;gin++){if(this.name===grpview.groupField[gin]){var grindex=this.name;if(this.index){grindex=this.index}grtypes[gin]=cmtypes[grindex];grindexes[gin]=grindex}}}if(ts.p.multiSort){if(this.lso){st.push(this.name);var tmplso=this.lso.split("-");sto.push(tmplso[tmplso.length-1])}}else{if(!fndsort&&(this.index===ts.p.sortname||this.name===ts.p.sortname)){st=this.name;fndsort=true}}});if(ts.p.treeGrid){$(ts).jqGrid("SortTree",st,ts.p.sortorder,cmtypes[st].stype||"text",cmtypes[st].srcfmt||"");return}var compareFnMap={"eq":function(queryObj){return queryObj.equals},"ne":function(queryObj){return queryObj.notEquals},"lt":function(queryObj){return queryObj.less},"le":function(queryObj){return queryObj.lessOrEquals},"gt":function(queryObj){return queryObj.greater},"ge":function(queryObj){return queryObj.greaterOrEquals},"cn":function(queryObj){return queryObj.contains},"nc":function(queryObj,op){return op==="OR"?queryObj.orNot().contains:queryObj.andNot().contains},"bw":function(queryObj){return queryObj.startsWith},"bn":function(queryObj,op){return op==="OR"?queryObj.orNot().startsWith:queryObj.andNot().startsWith},"en":function(queryObj,op){return op==="OR"?queryObj.orNot().endsWith:queryObj.andNot().endsWith},"ew":function(queryObj){return queryObj.endsWith},"ni":function(queryObj,op){return op==="OR"?queryObj.orNot().equals:queryObj.andNot().equals},"in":function(queryObj){return queryObj.equals},"nu":function(queryObj){return queryObj.isNull},"nn":function(queryObj,op){return op==="OR"?queryObj.orNot().isNull:queryObj.andNot().isNull}},query=$.jgrid.from(ts.p.data);if(ts.p.ignoreCase){query=query.ignoreCase()}function tojLinq(group){var s=0,index,gor,ror,opr,rule;if(group.groups!=null){gor=group.groups.length&&group.groupOp.toString().toUpperCase()==="OR";if(gor){query.orBegin()}for(index=0;index<group.groups.length;index++){if(s>0&&gor){query.or()}try{tojLinq(group.groups[index])}catch(e){alert(e)}s++}if(gor){query.orEnd()}}if(group.rules!=null){try{ror=group.rules.length&&group.groupOp.toString().toUpperCase()==="OR";if(ror){query.orBegin()}for(index=0;index<group.rules.length;index++){rule=group.rules[index];opr=group.groupOp.toString().toUpperCase();if(compareFnMap[rule.op]&&rule.field){if(s>0&&opr&&opr==="OR"){query=query.or()}query=compareFnMap[rule.op](query,opr)(rule.field,rule.data,cmtypes[rule.field])}s++}if(ror){query.orEnd()}}catch(g){alert(g)}}}if(ts.p.search===true){var srules=ts.p.postData.filters;if(srules){if(typeof srules==="string"){srules=$.jgrid.parse(srules)}tojLinq(srules)}else{try{query=compareFnMap[ts.p.postData.searchOper](query)(ts.p.postData.searchField,ts.p.postData.searchString,cmtypes[ts.p.postData.searchField])}catch(se){}}}if(ts.p.grouping){for(gin=0;gin<lengrp;gin++){query.orderBy(grindexes[gin],grpview.groupOrder[gin],grtypes[gin].stype,grtypes[gin].srcfmt)}}if(ts.p.multiSort){$.each(st,function(i){query.orderBy(this,sto[i],cmtypes[this].stype,cmtypes[this].srcfmt,cmtypes[this].sfunc)})}else{if(st&&ts.p.sortorder&&fndsort){if(ts.p.sortorder.toUpperCase()==="DESC"){query.orderBy(ts.p.sortname,"d",cmtypes[st].stype,cmtypes[st].srcfmt,cmtypes[st].sfunc)}else{query.orderBy(ts.p.sortname,"a",cmtypes[st].stype,cmtypes[st].srcfmt,cmtypes[st].sfunc)}}}var queryResults=query.select(),recordsperpage=parseInt(ts.p.rowNum,10),total=queryResults.length,page=parseInt(ts.p.page,10),totalpages=Math.ceil(total/recordsperpage),retresult={};if((ts.p.search||ts.p.resetsearch)&&ts.p.grouping&&ts.p.groupingView._locgr){ts.p.groupingView.groups=[];var j,grPrepare=$.jgrid.getMethod("groupingPrepare"),key,udc;if(ts.p.footerrow&&ts.p.userDataOnFooter){for(key in ts.p.userData){if(ts.p.userData.hasOwnProperty(key)){ts.p.userData[key]=0}}udc=true}for(j=0;j<total;j++){if(udc){for(key in ts.p.userData){ts.p.userData[key]+=parseFloat(queryResults[j][key]||0)}}grPrepare.call($(ts),queryResults[j],j,recordsperpage)}}queryResults=queryResults.slice((page-1)*recordsperpage,page*recordsperpage);query=null;cmtypes=null;retresult[ts.p.localReader.total]=totalpages;retresult[ts.p.localReader.page]=page;retresult[ts.p.localReader.records]=total;retresult[ts.p.localReader.root]=queryResults;retresult[ts.p.localReader.userdata]=ts.p.userData;queryResults=null;return retresult},updatepager=function(rn,dnd){var cp,last,base,from,to,tot,fmt,pgboxes="",sppg,tspg=ts.p.pager?"_"+$.jgrid.jqID(ts.p.pager.substr(1)):"",tspg_t=ts.p.toppager?"_"+ts.p.toppager.substr(1):"";base=parseInt(ts.p.page,10)-1;if(base<0){base=0}base=base*parseInt(ts.p.rowNum,10);to=base+ts.p.reccount;if(ts.p.scroll){var rows=$("tbody:first > tr:gt(0)",ts.grid.bDiv);base=to-rows.length;ts.p.reccount=rows.length;var rh=rows.outerHeight()||ts.grid.prevRowHeight;if(rh){var top=base*rh;var height=parseInt(ts.p.records,10)*rh;$(">div:first",ts.grid.bDiv).css({height:height}).children("div:first").css({height:top,display:top?"":"none"});if(ts.grid.bDiv.scrollTop==0&&ts.p.page>1){ts.grid.bDiv.scrollTop=ts.p.rowNum*(ts.p.page-1)*rh}}ts.grid.bDiv.scrollLeft=ts.grid.hDiv.scrollLeft}pgboxes=ts.p.pager||"";pgboxes+=ts.p.toppager?(pgboxes?","+ts.p.toppager:ts.p.toppager):"";if(pgboxes){fmt=$.jgrid.formatter.integer||{};cp=intNum(ts.p.page);last=intNum(ts.p.lastpage);$(".selbox",pgboxes)[this.p.useProp?"prop":"attr"]("disabled",false);if(ts.p.pginput===true){$(".ui-pg-input",pgboxes).val(ts.p.page);sppg=ts.p.toppager?"#sp_1"+tspg+",#sp_1"+tspg_t:"#sp_1"+tspg;$(sppg).html($.fmatter?$.fmatter.util.NumberFormat(ts.p.lastpage,fmt):ts.p.lastpage)}if(ts.p.viewrecords){if(ts.p.reccount===0){$(".ui-paging-info",pgboxes).html(ts.p.emptyrecords)}else{from=base+1;tot=ts.p.records;if($.fmatter){from=$.fmatter.util.NumberFormat(from,fmt);to=$.fmatter.util.NumberFormat(to,fmt);tot=$.fmatter.util.NumberFormat(tot,fmt)}$(".ui-paging-info",pgboxes).html($.jgrid.format(ts.p.recordtext,from,to,tot))}}if(ts.p.pgbuttons===true){if(cp<=0){cp=last=0}if(cp===1||cp===0){$("#first"+tspg+", #prev"+tspg).addClass("ui-state-disabled").removeClass("ui-state-hover");if(ts.p.toppager){$("#first_t"+tspg_t+", #prev_t"+tspg_t).addClass("ui-state-disabled").removeClass("ui-state-hover")}}else{$("#first"+tspg+", #prev"+tspg).removeClass("ui-state-disabled");if(ts.p.toppager){$("#first_t"+tspg_t+", #prev_t"+tspg_t).removeClass("ui-state-disabled")}}if(cp===last||cp===0){$("#next"+tspg+", #last"+tspg).addClass("ui-state-disabled").removeClass("ui-state-hover");if(ts.p.toppager){$("#next_t"+tspg_t+", #last_t"+tspg_t).addClass("ui-state-disabled").removeClass("ui-state-hover")}}else{$("#next"+tspg+", #last"+tspg).removeClass("ui-state-disabled");if(ts.p.toppager){$("#next_t"+tspg_t+", #last_t"+tspg_t).removeClass("ui-state-disabled")}}}}if(rn===true&&ts.p.rownumbers===true){$(">td.jqgrid-rownum",ts.rows).each(function(i){$(this).html(base+1+i)})}if(dnd&&ts.p.jqgdnd){$(ts).jqGrid("gridDnD","updateDnD")}$(ts).triggerHandler("jqGridGridComplete");if($.isFunction(ts.p.gridComplete)){ts.p.gridComplete.call(ts)}$(ts).triggerHandler("jqGridAfterGridComplete")},beginReq=function(){ts.grid.hDiv.loading=true;if(ts.p.hiddengrid){return}switch(ts.p.loadui){case"disable":break;case"enable":$("#load_"+$.jgrid.jqID(ts.p.id)).show();break;case"block":$("#lui_"+$.jgrid.jqID(ts.p.id)).show();$("#load_"+$.jgrid.jqID(ts.p.id)).show();break}},endReq=function(){ts.grid.hDiv.loading=false;switch(ts.p.loadui){case"disable":break;case"enable":$("#load_"+$.jgrid.jqID(ts.p.id)).hide();break;case"block":$("#lui_"+$.jgrid.jqID(ts.p.id)).hide();$("#load_"+$.jgrid.jqID(ts.p.id)).hide();break}},populate=function(npage){if(!ts.grid.hDiv.loading){var pvis=ts.p.scroll&&npage===false,prm={},dt,dstr,pN=ts.p.prmNames;if(ts.p.page<=0){ts.p.page=Math.min(1,ts.p.lastpage)}if(pN.search!==null){prm[pN.search]=ts.p.search}if(pN.nd!==null){prm[pN.nd]=new Date().getTime()}if(pN.rows!==null){prm[pN.rows]=ts.p.rowNum}if(pN.page!==null){prm[pN.page]=ts.p.page}if(pN.sort!==null){prm[pN.sort]=ts.p.sortname}if(pN.order!==null){prm[pN.order]=ts.p.sortorder}if(ts.p.rowTotal!==null&&pN.totalrows!==null){prm[pN.totalrows]=ts.p.rowTotal}var lcf=$.isFunction(ts.p.loadComplete),lc=lcf?ts.p.loadComplete:null;var adjust=0;npage=npage||1;if(npage>1){if(pN.npage!==null){prm[pN.npage]=npage;adjust=npage-1;npage=1}else{lc=function(req){ts.p.page++;ts.grid.hDiv.loading=false;if(lcf){ts.p.loadComplete.call(ts,req)}populate(npage-1)}}}else{if(pN.npage!==null){delete ts.p.postData[pN.npage]}}if(ts.p.grouping){$(ts).jqGrid("groupingSetup");var grp=ts.p.groupingView,gi,gs="";for(gi=0;gi<grp.groupField.length;gi++){var index=grp.groupField[gi];$.each(ts.p.colModel,function(cmIndex,cmValue){if(cmValue.name===index&&cmValue.index){index=cmValue.index}});gs+=index+" "+grp.groupOrder[gi]+", "}prm[pN.sort]=gs+prm[pN.sort]}$.extend(ts.p.postData,prm);var rcnt=!ts.p.scroll?1:ts.rows.length-1;var bfr=$(ts).triggerHandler("jqGridBeforeRequest");if(bfr===false||bfr==="stop"){return}if($.isFunction(ts.p.datatype)){ts.p.datatype.call(ts,ts.p.postData,"load_"+ts.p.id,rcnt,npage,adjust);return}if($.isFunction(ts.p.beforeRequest)){bfr=ts.p.beforeRequest.call(ts);if(bfr===undefined){bfr=true}if(bfr===false){return}}dt=ts.p.datatype.toLowerCase();switch(dt){case"json":case"jsonp":case"xml":case"script":$.ajax($.extend({url:ts.p.url,type:ts.p.mtype,dataType:dt,data:$.isFunction(ts.p.serializeGridData)?ts.p.serializeGridData.call(ts,ts.p.postData):ts.p.postData,success:function(data,st,xhr){if($.isFunction(ts.p.beforeProcessing)){if(ts.p.beforeProcessing.call(ts,data,st,xhr)===false){endReq();return}}if(dt==="xml"){addXmlData(data,ts.grid.bDiv,rcnt,npage>1,adjust)}else{addJSONData(data,ts.grid.bDiv,rcnt,npage>1,adjust)}$(ts).triggerHandler("jqGridLoadComplete",[data]);if(lc){lc.call(ts,data)}$(ts).triggerHandler("jqGridAfterLoadComplete",[data]);if(pvis){ts.grid.populateVisible()}if(ts.p.loadonce||ts.p.treeGrid){ts.p.datatype="local"}data=null;if(npage===1){endReq()}},error:function(xhr,st,err){if($.isFunction(ts.p.loadError)){ts.p.loadError.call(ts,xhr,st,err)}if(npage===1){endReq()}xhr=null},beforeSend:function(xhr,settings){var gotoreq=true;if($.isFunction(ts.p.loadBeforeSend)){gotoreq=ts.p.loadBeforeSend.call(ts,xhr,settings)}if(gotoreq===undefined){gotoreq=true}if(gotoreq===false){return false}beginReq()}},$.jgrid.ajaxOptions,ts.p.ajaxGridOptions));break;case"xmlstring":beginReq();dstr=typeof ts.p.datastr!=="string"?ts.p.datastr:$.parseXML(ts.p.datastr);addXmlData(dstr,ts.grid.bDiv);$(ts).triggerHandler("jqGridLoadComplete",[dstr]);if(lcf){ts.p.loadComplete.call(ts,dstr)}$(ts).triggerHandler("jqGridAfterLoadComplete",[dstr]);ts.p.datatype="local";ts.p.datastr=null;endReq();break;case"jsonstring":beginReq();if(typeof ts.p.datastr==="string"){dstr=$.jgrid.parse(ts.p.datastr)}else{dstr=ts.p.datastr}addJSONData(dstr,ts.grid.bDiv);$(ts).triggerHandler("jqGridLoadComplete",[dstr]);if(lcf){ts.p.loadComplete.call(ts,dstr)}$(ts).triggerHandler("jqGridAfterLoadComplete",[dstr]);ts.p.datatype="local";ts.p.datastr=null;endReq();break;case"local":case"clientside":beginReq();ts.p.datatype="local";var req=addLocalData();addJSONData(req,ts.grid.bDiv,rcnt,npage>1,adjust);$(ts).triggerHandler("jqGridLoadComplete",[req]);if(lc){lc.call(ts,req)}$(ts).triggerHandler("jqGridAfterLoadComplete",[req]);if(pvis){ts.grid.populateVisible()}endReq();break}}},setHeadCheckBox=function(checked){$("#cb_"+$.jgrid.jqID(ts.p.id),ts.grid.hDiv)[ts.p.useProp?"prop":"attr"]("checked",checked);var fid=ts.p.frozenColumns?ts.p.id+"_frozen":"";if(fid){$("#cb_"+$.jgrid.jqID(ts.p.id),ts.grid.fhDiv)[ts.p.useProp?"prop":"attr"]("checked",checked)}},setPager=function(pgid,tp){var sep="<td class='ui-pg-button ui-state-disabled' style='width:4px;'><span class='ui-separator'></span></td>",pginp="",pgl="<table cellspacing='0' cellpadding='0' border='0' style='table-layout:auto;' class='ui-pg-table'><tbody><tr>",str="",pgcnt,lft,cent,rgt,twd,tdw,i,clearVals=function(onpaging){var ret;if($.isFunction(ts.p.onPaging)){ret=ts.p.onPaging.call(ts,onpaging)}if(ret==="stop"){return false}ts.p.selrow=null;if(ts.p.multiselect){ts.p.selarrrow=[];setHeadCheckBox(false)}ts.p.savedRow=[];return true};pgid=pgid.substr(1);tp+="_"+pgid;pgcnt="pg_"+pgid;lft=pgid+"_left";cent=pgid+"_center";rgt=pgid+"_right";$("#"+$.jgrid.jqID(pgid)).append("<div id='"+pgcnt+"' class='ui-pager-control' role='group'><table cellspacing='0' cellpadding='0' border='0' class='ui-pg-table' style='width:100%;table-layout:fixed;height:100%;' role='row'><tbody><tr><td id='"+lft+"' align='left'></td><td id='"+cent+"' align='center' style='white-space:pre;'></td><td id='"+rgt+"' align='right'></td></tr></tbody></table></div>").attr("dir","ltr");if(ts.p.rowList.length>0){str="<td dir='"+dir+"'>";str+="<select class='ui-pg-selbox' role='listbox'>";for(i=0;i<ts.p.rowList.length;i++){str+='<option role="option" value="'+ts.p.rowList[i]+'"'+((ts.p.rowNum===ts.p.rowList[i])?' selected="selected"':"")+">"+ts.p.rowList[i]+"</option>"}str+="</select></td>"}if(dir==="rtl"){pgl+=str}if(ts.p.pginput===true){pginp="<td dir='"+dir+"'>"+$.jgrid.format(ts.p.pgtext||"","<input class='ui-pg-input' type='text' size='2' maxlength='7' value='0' role='textbox'/>","<span id='sp_1_"+$.jgrid.jqID(pgid)+"'></span>")+"</td>"}if(ts.p.pgbuttons===true){var po=["first"+tp,"prev"+tp,"next"+tp,"last"+tp];if(dir==="rtl"){po.reverse()}pgl+="<td id='"+po[0]+"' class='ui-pg-button ui-corner-all'><span class='ui-icon ui-icon-seek-first'></span></td>";pgl+="<td id='"+po[1]+"' class='ui-pg-button ui-corner-all'><span class='ui-icon ui-icon-seek-prev'></span></td>";pgl+=pginp!==""?sep+pginp+sep:"";pgl+="<td id='"+po[2]+"' class='ui-pg-button ui-corner-all'><span class='ui-icon ui-icon-seek-next'></span></td>";pgl+="<td id='"+po[3]+"' class='ui-pg-button ui-corner-all'><span class='ui-icon ui-icon-seek-end'></span></td>"}else{if(pginp!==""){pgl+=pginp}}if(dir==="ltr"){pgl+=str}pgl+="</tr></tbody></table>";if(ts.p.viewrecords===true){$("td#"+pgid+"_"+ts.p.recordpos,"#"+pgcnt).append("<div dir='"+dir+"' style='text-align:"+ts.p.recordpos+"' class='ui-paging-info'></div>")}$("td#"+pgid+"_"+ts.p.pagerpos,"#"+pgcnt).append(pgl);tdw=$(".ui-jqgrid").css("font-size")||"11px";$(document.body).append("<div id='testpg' class='ui-jqgrid ui-widget ui-widget-content' style='font-size:"+tdw+";visibility:hidden;' ></div>");twd=$(pgl).clone().appendTo("#testpg").width();$("#testpg").remove();if(twd>0){if(pginp!==""){twd+=50}$("td#"+pgid+"_"+ts.p.pagerpos,"#"+pgcnt).width(twd)}ts.p._nvtd=[];ts.p._nvtd[0]=twd?Math.floor((ts.p.width-twd)/2):Math.floor(ts.p.width/3);ts.p._nvtd[1]=0;pgl=null;$(".ui-pg-selbox","#"+pgcnt).bind("change",function(){if(!clearVals("records")){return false}ts.p.page=Math.round(ts.p.rowNum*(ts.p.page-1)/this.value-0.5)+1;ts.p.rowNum=this.value;if(ts.p.pager){$(".ui-pg-selbox",ts.p.pager).val(this.value)}if(ts.p.toppager){$(".ui-pg-selbox",ts.p.toppager).val(this.value)}populate();return false});if(ts.p.pgbuttons===true){$(".ui-pg-button","#"+pgcnt).hover(function(){if($(this).hasClass("ui-state-disabled")){this.style.cursor="default"}else{$(this).addClass("ui-state-hover");this.style.cursor="pointer"}},function(){if(!$(this).hasClass("ui-state-disabled")){$(this).removeClass("ui-state-hover");this.style.cursor="default"}});$("#first"+$.jgrid.jqID(tp)+", #prev"+$.jgrid.jqID(tp)+", #next"+$.jgrid.jqID(tp)+", #last"+$.jgrid.jqID(tp)).click(function(){if($(this).hasClass("ui-state-disabled")){return false}var cp=intNum(ts.p.page,1),last=intNum(ts.p.lastpage,1),selclick=false,fp=true,pp=true,np=true,lp=true;if(last===0||last===1){fp=false;pp=false;np=false;lp=false}else{if(last>1&&cp>=1){if(cp===1){fp=false;pp=false}else{if(cp===last){np=false;lp=false}}}else{if(last>1&&cp===0){np=false;lp=false;cp=last-1}}}if(!clearVals(this.id)){return false}if(this.id==="first"+tp&&fp){ts.p.page=1;selclick=true}if(this.id==="prev"+tp&&pp){ts.p.page=(cp-1);selclick=true}if(this.id==="next"+tp&&np){ts.p.page=(cp+1);selclick=true}if(this.id==="last"+tp&&lp){ts.p.page=last;selclick=true}if(selclick){populate()}return false})}if(ts.p.pginput===true){$("input.ui-pg-input","#"+pgcnt).keypress(function(e){var key=e.charCode||e.keyCode||0;if(key===13){if(!clearVals("user")){return false}$(this).val(intNum($(this).val(),1));ts.p.page=($(this).val()>0)?$(this).val():ts.p.page;populate();return false}return this})}},multiSort=function(iCol,obj){var splas,sort="",cm=ts.p.colModel,fs=false,ls,selTh=ts.p.frozenColumns?obj:ts.grid.headers[iCol].el,so="";$("span.ui-grid-ico-sort",selTh).addClass("ui-state-disabled");$(selTh).attr("aria-selected","false");if(cm[iCol].lso){if(cm[iCol].lso==="asc"){cm[iCol].lso+="-desc";so="desc"}else{if(cm[iCol].lso==="desc"){cm[iCol].lso+="-asc";so="asc"}else{if(cm[iCol].lso==="asc-desc"||cm[iCol].lso==="desc-asc"){cm[iCol].lso=""}}}}else{cm[iCol].lso=so=cm[iCol].firstsortorder||"asc"}if(so){$("span.s-ico",selTh).show();$("span.ui-icon-"+so,selTh).removeClass("ui-state-disabled");$(selTh).attr("aria-selected","true")}else{if(!ts.p.viewsortcols[0]){$("span.s-ico",selTh).hide()}}ts.p.sortorder="";$.each(cm,function(i){if(this.lso){if(i>0&&fs){sort+=", "}splas=this.lso.split("-");sort+=cm[i].index||cm[i].name;sort+=" "+splas[splas.length-1];fs=true;ts.p.sortorder=splas[splas.length-1]}});ls=sort.lastIndexOf(ts.p.sortorder);sort=sort.substring(0,ls);ts.p.sortname=sort},sortData=function(index,idxcol,reload,sor,obj){if(!ts.p.colModel[idxcol].sortable){return}if(ts.p.savedRow.length>0){return}if(!reload){if(ts.p.lastsort===idxcol){if(ts.p.sortorder==="asc"){ts.p.sortorder="desc"}else{if(ts.p.sortorder==="desc"){ts.p.sortorder="asc"}}}else{ts.p.sortorder=ts.p.colModel[idxcol].firstsortorder||"asc"}ts.p.page=1}if(ts.p.multiSort){multiSort(idxcol,obj)}else{if(sor){if(ts.p.lastsort===idxcol&&ts.p.sortorder===sor&&!reload){return}ts.p.sortorder=sor}var previousSelectedTh=ts.grid.headers[ts.p.lastsort].el,newSelectedTh=ts.p.frozenColumns?obj:ts.grid.headers[idxcol].el;$("span.ui-grid-ico-sort",previousSelectedTh).addClass("ui-state-disabled");$(previousSelectedTh).attr("aria-selected","false");if(ts.p.frozenColumns){ts.grid.fhDiv.find("span.ui-grid-ico-sort").addClass("ui-state-disabled");ts.grid.fhDiv.find("th").attr("aria-selected","false")}$("span.ui-icon-"+ts.p.sortorder,newSelectedTh).removeClass("ui-state-disabled");$(newSelectedTh).attr("aria-selected","true");if(!ts.p.viewsortcols[0]){if(ts.p.lastsort!==idxcol){if(ts.p.frozenColumns){ts.grid.fhDiv.find("span.s-ico").hide()}$("span.s-ico",previousSelectedTh).hide();$("span.s-ico",newSelectedTh).show()}}index=index.substring(5+ts.p.id.length+1);ts.p.sortname=ts.p.colModel[idxcol].index||index}if($(ts).triggerHandler("jqGridSortCol",[ts.p.sortname,idxcol,ts.p.sortorder])==="stop"){ts.p.lastsort=idxcol;return}if($.isFunction(ts.p.onSortCol)){if(ts.p.onSortCol.call(ts,ts.p.sortname,idxcol,ts.p.sortorder)==="stop"){ts.p.lastsort=idxcol;return}}if(ts.p.datatype==="local"){if(ts.p.deselectAfterSort){$(ts).jqGrid("resetSelection")}}else{ts.p.selrow=null;if(ts.p.multiselect){setHeadCheckBox(false)}ts.p.selarrrow=[];ts.p.savedRow=[]}if(ts.p.scroll){var sscroll=ts.grid.bDiv.scrollLeft;emptyRows.call(ts,true,false);ts.grid.hDiv.scrollLeft=sscroll}if(ts.p.subGrid&&ts.p.datatype==="local"){$("td.sgexpanded","#"+$.jgrid.jqID(ts.p.id)).each(function(){$(this).trigger("click")})}populate();ts.p.lastsort=idxcol;if(ts.p.sortname!==index&&idxcol){ts.p.lastsort=idxcol}},setColWidth=function(){var initwidth=0,brd=$.jgrid.cell_width?0:intNum(ts.p.cellLayout,0),vc=0,lvc,scw=intNum(ts.p.scrollOffset,0),cw,hs=false,aw,gw=0,cr;$.each(ts.p.colModel,function(){if(this.hidden===undefined){this.hidden=false}if(ts.p.grouping&&ts.p.autowidth){var ind=$.inArray(this.name,ts.p.groupingView.groupField);if(ind>=0&&ts.p.groupingView.groupColumnShow.length>ind){this.hidden=!ts.p.groupingView.groupColumnShow[ind]}}this.widthOrg=cw=intNum(this.width,0);if(this.hidden===false){initwidth+=cw+brd;if(this.fixed){gw+=cw+brd}else{vc++}}});if(isNaN(ts.p.width)){ts.p.width=initwidth+((ts.p.shrinkToFit===false&&!isNaN(ts.p.height))?scw:0)}grid.width=ts.p.width;ts.p.tblwidth=initwidth;if(ts.p.shrinkToFit===false&&ts.p.forceFit===true){ts.p.forceFit=false}if(ts.p.shrinkToFit===true&&vc>0){aw=grid.width-brd*vc-gw;if(!isNaN(ts.p.height)){aw-=scw;hs=true}initwidth=0;$.each(ts.p.colModel,function(i){if(this.hidden===false&&!this.fixed){cw=Math.round(aw*this.width/(ts.p.tblwidth-brd*vc-gw));this.width=cw;initwidth+=cw;lvc=i}});cr=0;if(hs){if(grid.width-gw-(initwidth+brd*vc)!==scw){cr=grid.width-gw-(initwidth+brd*vc)-scw}}else{if(!hs&&Math.abs(grid.width-gw-(initwidth+brd*vc))!==1){cr=grid.width-gw-(initwidth+brd*vc)}}ts.p.colModel[lvc].width+=cr;ts.p.tblwidth=initwidth+cr+brd*vc+gw;if(ts.p.tblwidth>ts.p.width){ts.p.colModel[lvc].width-=(ts.p.tblwidth-parseInt(ts.p.width,10));ts.p.tblwidth=ts.p.width}}},nextVisible=function(iCol){var ret=iCol,j=iCol,i;for(i=iCol+1;i<ts.p.colModel.length;i++){if(ts.p.colModel[i].hidden!==true){j=i;break}}return j-ret},getOffset=function(iCol){var $th=$(ts.grid.headers[iCol].el),ret=[$th.position().left+$th.outerWidth()];if(ts.p.direction==="rtl"){ret[0]=ts.p.width-ret[0]}ret[0]-=ts.grid.bDiv.scrollLeft;ret.push($(ts.grid.hDiv).position().top);ret.push($(ts.grid.bDiv).offset().top-$(ts.grid.hDiv).offset().top+$(ts.grid.bDiv).height());return ret},getColumnHeaderIndex=function(th){var i,headers=ts.grid.headers,ci=$.jgrid.getCellIndex(th);for(i=0;i<headers.length;i++){if(th===headers[i].el){ci=i;break}}return ci};this.p.id=this.id;if($.inArray(ts.p.multikey,sortkeys)===-1){ts.p.multikey=false}ts.p.keyIndex=false;ts.p.keyName=false;for(i=0;i<ts.p.colModel.length;i++){ts.p.colModel[i]=$.extend(true,{},ts.p.cmTemplate,ts.p.colModel[i].template||{},ts.p.colModel[i]);if(ts.p.keyIndex===false&&ts.p.colModel[i].key===true){ts.p.keyIndex=i}}ts.p.sortorder=ts.p.sortorder.toLowerCase();$.jgrid.cell_width=$.jgrid.cellWidth();if(ts.p.grouping===true){ts.p.scroll=false;ts.p.rownumbers=false;ts.p.treeGrid=false;ts.p.gridview=true}if(this.p.treeGrid===true){try{$(this).jqGrid("setTreeGrid")}catch(_){}if(ts.p.datatype!=="local"){ts.p.localReader={id:"_id_"}}}if(this.p.subGrid){try{$(ts).jqGrid("setSubGrid")}catch(s){}}if(this.p.multiselect){this.p.colNames.unshift("<input role='checkbox' id='cb_"+this.p.id+"' class='cbox' type='checkbox'/>");this.p.colModel.unshift({name:"cb",width:$.jgrid.cell_width?ts.p.multiselectWidth+ts.p.cellLayout:ts.p.multiselectWidth,sortable:false,resizable:false,hidedlg:true,search:false,align:"center",fixed:true})}if(this.p.rownumbers){this.p.colNames.unshift("");this.p.colModel.unshift({name:"rn",width:ts.p.rownumWidth,sortable:false,resizable:false,hidedlg:true,search:false,align:"center",fixed:true})}ts.p.xmlReader=$.extend(true,{root:"rows",row:"row",page:"rows>page",total:"rows>total",records:"rows>records",repeatitems:true,cell:"cell",id:"[id]",userdata:"userdata",subgrid:{root:"rows",row:"row",repeatitems:true,cell:"cell"}},ts.p.xmlReader);ts.p.jsonReader=$.extend(true,{root:"rows",page:"page",total:"total",records:"records",repeatitems:true,cell:"cell",id:"id",userdata:"userdata",subgrid:{root:"rows",repeatitems:true,cell:"cell"}},ts.p.jsonReader);ts.p.localReader=$.extend(true,{root:"rows",page:"page",total:"total",records:"records",repeatitems:false,cell:"cell",id:"id",userdata:"userdata",subgrid:{root:"rows",repeatitems:true,cell:"cell"}},ts.p.localReader);if(ts.p.scroll){ts.p.pgbuttons=false;ts.p.pginput=false;ts.p.rowList=[]}if(ts.p.data.length){refreshIndex()}var thead="<thead><tr class='ui-jqgrid-labels' role='rowheader'>",tdc,idn,w,res,sort,td,ptr,tbody,imgs,iac="",idc="",sortarr=[],sortord=[],sotmp=[];if(ts.p.shrinkToFit===true&&ts.p.forceFit===true){for(i=ts.p.colModel.length-1;i>=0;i--){if(!ts.p.colModel[i].hidden){ts.p.colModel[i].resizable=false;break}}}if(ts.p.viewsortcols[1]==="horizontal"){iac=" ui-i-asc";idc=" ui-i-desc"}tdc=isMSIE?"class='ui-th-div-ie'":"";imgs="<span class='s-ico' style='display:none'><span sort='asc' class='ui-grid-ico-sort ui-icon-asc"+iac+" ui-state-disabled ui-icon ui-icon-triangle-1-n ui-sort-"+dir+"'></span>";imgs+="<span sort='desc' class='ui-grid-ico-sort ui-icon-desc"+idc+" ui-state-disabled ui-icon ui-icon-triangle-1-s ui-sort-"+dir+"'></span></span>";if(ts.p.multiSort){sortarr=ts.p.sortname.split(",");for(i=0;i<sortarr.length;i++){sotmp=$.trim(sortarr[i]).split(" ");sortarr[i]=$.trim(sotmp[0]);sortord[i]=sotmp[1]?$.trim(sotmp[1]):ts.p.sortorder||"asc"}}for(i=0;i<this.p.colNames.length;i++){var tooltip=ts.p.headertitles?(' title="'+$.jgrid.stripHtml(ts.p.colNames[i])+'"'):"";thead+="<th id='"+ts.p.id+"_"+ts.p.colModel[i].name+"' role='columnheader' class='ui-state-default ui-th-column ui-th-"+dir+"'"+tooltip+">";idn=ts.p.colModel[i].index||ts.p.colModel[i].name;thead+="<div id='jqgh_"+ts.p.id+"_"+ts.p.colModel[i].name+"' "+tdc+">"+ts.p.colNames[i];if(!ts.p.colModel[i].width){ts.p.colModel[i].width=150}else{ts.p.colModel[i].width=parseInt(ts.p.colModel[i].width,10)}if(typeof ts.p.colModel[i].title!=="boolean"){ts.p.colModel[i].title=true}ts.p.colModel[i].lso="";if(idn===ts.p.sortname){ts.p.lastsort=i}if(ts.p.multiSort){sotmp=$.inArray(idn,sortarr);if(sotmp!==-1){ts.p.colModel[i].lso=sortord[sotmp]}}thead+=imgs+"</div></th>"}thead+="</tr></thead>";imgs=null;$(this).append(thead);$("thead tr:first th",this).hover(function(){$(this).addClass("ui-state-hover")},function(){$(this).removeClass("ui-state-hover")});if(this.p.multiselect){var emp=[],chk;$("#cb_"+$.jgrid.jqID(ts.p.id),this).bind("click",function(){ts.p.selarrrow=[];var froz=ts.p.frozenColumns===true?ts.p.id+"_frozen":"";if(this.checked){$(ts.rows).each(function(i){if(i>0){if(!$(this).hasClass("ui-subgrid")&&!$(this).hasClass("jqgroup")&&!$(this).hasClass("ui-state-disabled")){$("#jqg_"+$.jgrid.jqID(ts.p.id)+"_"+$.jgrid.jqID(this.id))[ts.p.useProp?"prop":"attr"]("checked",true);$(this).addClass("ui-state-highlight").attr("aria-selected","true");ts.p.selarrrow.push(this.id);ts.p.selrow=this.id;if(froz){$("#jqg_"+$.jgrid.jqID(ts.p.id)+"_"+$.jgrid.jqID(this.id),ts.grid.fbDiv)[ts.p.useProp?"prop":"attr"]("checked",true);$("#"+$.jgrid.jqID(this.id),ts.grid.fbDiv).addClass("ui-state-highlight")}}}});chk=true;emp=[]}else{$(ts.rows).each(function(i){if(i>0){if(!$(this).hasClass("ui-subgrid")&&!$(this).hasClass("ui-state-disabled")){$("#jqg_"+$.jgrid.jqID(ts.p.id)+"_"+$.jgrid.jqID(this.id))[ts.p.useProp?"prop":"attr"]("checked",false);$(this).removeClass("ui-state-highlight").attr("aria-selected","false");emp.push(this.id);if(froz){$("#jqg_"+$.jgrid.jqID(ts.p.id)+"_"+$.jgrid.jqID(this.id),ts.grid.fbDiv)[ts.p.useProp?"prop":"attr"]("checked",false);$("#"+$.jgrid.jqID(this.id),ts.grid.fbDiv).removeClass("ui-state-highlight")}}}});ts.p.selrow=null;chk=false}$(ts).triggerHandler("jqGridSelectAll",[chk?ts.p.selarrrow:emp,chk]);if($.isFunction(ts.p.onSelectAll)){ts.p.onSelectAll.call(ts,chk?ts.p.selarrrow:emp,chk)}})}if(ts.p.autowidth===true){var pw=$(eg).innerWidth();ts.p.width=pw>0?pw:"nw"}setColWidth();$(eg).css("width",grid.width+"px").append("<div class='ui-jqgrid-resize-mark' id='rs_m"+ts.p.id+"'>&#160;</div>");$(gv).css("width",grid.width+"px");thead=$("thead:first",ts).get(0);var tfoot="";if(ts.p.footerrow){tfoot+="<table role='grid' style='width:"+ts.p.tblwidth+"px' class='ui-jqgrid-ftable' cellspacing='0' cellpadding='0' border='0'><tbody><tr role='row' class='ui-widget-content footrow footrow-"+dir+"'>"}var thr=$("tr:first",thead),firstr="<tr class='jqgfirstrow' role='row' style='height:auto'>";ts.p.disableClick=false;$("th",thr).each(function(j){w=ts.p.colModel[j].width;if(ts.p.colModel[j].resizable===undefined){ts.p.colModel[j].resizable=true}if(ts.p.colModel[j].resizable){res=document.createElement("span");$(res).html("&#160;").addClass("ui-jqgrid-resize ui-jqgrid-resize-"+dir).css("cursor","col-resize");$(this).addClass(ts.p.resizeclass)}else{res=""}$(this).css("width",w+"px").prepend(res);res=null;var hdcol="";if(ts.p.colModel[j].hidden){$(this).css("display","none");hdcol="display:none;"}firstr+="<td role='gridcell' style='height:0px;width:"+w+"px;"+hdcol+"'></td>";grid.headers[j]={width:w,el:this};sort=ts.p.colModel[j].sortable;if(typeof sort!=="boolean"){ts.p.colModel[j].sortable=true;sort=true}var nm=ts.p.colModel[j].name;if(!(nm==="cb"||nm==="subgrid"||nm==="rn")){if(ts.p.viewsortcols[2]){$(">div",this).addClass("ui-jqgrid-sortable")}}if(sort){if(ts.p.multiSort){if(ts.p.viewsortcols[0]){$("div span.s-ico",this).show();if(ts.p.colModel[j].lso){$("div span.ui-icon-"+ts.p.colModel[j].lso,this).removeClass("ui-state-disabled")}}else{if(ts.p.colModel[j].lso){$("div span.s-ico",this).show();$("div span.ui-icon-"+ts.p.colModel[j].lso,this).removeClass("ui-state-disabled")}}}else{if(ts.p.viewsortcols[0]){$("div span.s-ico",this).show();if(j===ts.p.lastsort){$("div span.ui-icon-"+ts.p.sortorder,this).removeClass("ui-state-disabled")}}else{if(j===ts.p.lastsort){$("div span.s-ico",this).show();$("div span.ui-icon-"+ts.p.sortorder,this).removeClass("ui-state-disabled")}}}}if(ts.p.footerrow){tfoot+="<td role='gridcell' "+formatCol(j,0,"",null,"",false)+">&#160;</td>"}}).mousedown(function(e){if($(e.target).closest("th>span.ui-jqgrid-resize").length!==1){return}var ci=getColumnHeaderIndex(this);if(ts.p.forceFit===true){ts.p.nv=nextVisible(ci)}grid.dragStart(ci,e,getOffset(ci));return false}).click(function(e){if(ts.p.disableClick){ts.p.disableClick=false;return false}var s="th>div.ui-jqgrid-sortable",r,d;if(!ts.p.viewsortcols[2]){s="th>div>span>span.ui-grid-ico-sort"}var t=$(e.target).closest(s);if(t.length!==1){return}var ci;if(ts.p.frozenColumns){var tid=$(this)[0].id.substring(ts.p.id.length+1);$(ts.p.colModel).each(function(i){if(this.name===tid){ci=i;return false}})}else{ci=getColumnHeaderIndex(this)}if(!ts.p.viewsortcols[2]){r=true;d=t.attr("sort")}if(ci!=null){sortData($("div",this)[0].id,ci,r,d,this)}return false});if(ts.p.sortable&&$.fn.sortable){try{$(ts).jqGrid("sortableColumns",thr)}catch(e){}}if(ts.p.footerrow){tfoot+="</tr></tbody></table>"}firstr+="</tr>";tbody=document.createElement("tbody");this.appendChild(tbody);$(this).addClass("ui-jqgrid-btable").append(firstr);firstr=null;var hTable=$("<table class='ui-jqgrid-htable' style='width:"+ts.p.tblwidth+"px' role='grid' aria-labelledby='gbox_"+this.id+"' cellspacing='0' cellpadding='0' border='0'></table>").append(thead),hg=(ts.p.caption&&ts.p.hiddengrid===true)?true:false,hb=$("<div class='ui-jqgrid-hbox"+(dir==="rtl"?"-rtl":"")+"'></div>");thead=null;grid.hDiv=document.createElement("div");$(grid.hDiv).css({width:grid.width+"px"}).addClass("ui-state-default ui-jqgrid-hdiv").append(hb);$(hb).append(hTable);hTable=null;if(hg){$(grid.hDiv).hide()}if(ts.p.pager){if(typeof ts.p.pager==="string"){if(ts.p.pager.substr(0,1)!=="#"){ts.p.pager="#"+ts.p.pager}}else{ts.p.pager="#"+$(ts.p.pager).attr("id")}$(ts.p.pager).css({width:grid.width+"px"}).addClass("ui-state-default ui-jqgrid-pager ui-corner-bottom").appendTo(eg);if(hg){$(ts.p.pager).hide()}setPager(ts.p.pager,"")}if(ts.p.cellEdit===false&&ts.p.hoverrows===true){$(ts).bind("mouseover",function(e){ptr=$(e.target).closest("tr.jqgrow");if($(ptr).attr("class")!=="ui-subgrid"){$(ptr).addClass("ui-state-hover")}}).bind("mouseout",function(e){ptr=$(e.target).closest("tr.jqgrow");$(ptr).removeClass("ui-state-hover")})}var ri,ci,tdHtml;$(ts).before(grid.hDiv).click(function(e){td=e.target;ptr=$(td,ts.rows).closest("tr.jqgrow");if($(ptr).length===0||ptr[0].className.indexOf("ui-state-disabled")>-1||($(td,ts).closest("table.ui-jqgrid-btable").attr("id")||"").replace("_frozen","")!==ts.id){return this}var scb=$(td).hasClass("cbox"),cSel=$(ts).triggerHandler("jqGridBeforeSelectRow",[ptr[0].id,e]);cSel=(cSel===false||cSel==="stop")?false:true;if(cSel&&$.isFunction(ts.p.beforeSelectRow)){cSel=ts.p.beforeSelectRow.call(ts,ptr[0].id,e)}if(td.tagName==="A"||((td.tagName==="INPUT"||td.tagName==="TEXTAREA"||td.tagName==="OPTION"||td.tagName==="SELECT")&&!scb)){return}if(cSel===true){ri=ptr[0].id;ci=$.jgrid.getCellIndex(td);tdHtml=$(td).closest("td,th").html();$(ts).triggerHandler("jqGridCellSelect",[ri,ci,tdHtml,e]);if($.isFunction(ts.p.onCellSelect)){ts.p.onCellSelect.call(ts,ri,ci,tdHtml,e)}if(ts.p.cellEdit===true){if(ts.p.multiselect&&scb){$(ts).jqGrid("setSelection",ri,true,e)}else{ri=ptr[0].rowIndex;try{$(ts).jqGrid("editCell",ri,ci,true)}catch(_){}}}else{if(!ts.p.multikey){if(ts.p.multiselect&&ts.p.multiboxonly){if(scb){$(ts).jqGrid("setSelection",ri,true,e)}else{var frz=ts.p.frozenColumns?ts.p.id+"_frozen":"";$(ts.p.selarrrow).each(function(i,n){var trid=$(ts).jqGrid("getGridRowById",n);$(trid).removeClass("ui-state-highlight");$("#jqg_"+$.jgrid.jqID(ts.p.id)+"_"+$.jgrid.jqID(n))[ts.p.useProp?"prop":"attr"]("checked",false);if(frz){$("#"+$.jgrid.jqID(n),"#"+$.jgrid.jqID(frz)).removeClass("ui-state-highlight");$("#jqg_"+$.jgrid.jqID(ts.p.id)+"_"+$.jgrid.jqID(n),"#"+$.jgrid.jqID(frz))[ts.p.useProp?"prop":"attr"]("checked",false)}});ts.p.selarrrow=[];$(ts).jqGrid("setSelection",ri,true,e)}}else{$(ts).jqGrid("setSelection",ri,true,e)}}else{if(e[ts.p.multikey]){$(ts).jqGrid("setSelection",ri,true,e)}else{if(ts.p.multiselect&&scb){scb=$("#jqg_"+$.jgrid.jqID(ts.p.id)+"_"+ri).is(":checked");$("#jqg_"+$.jgrid.jqID(ts.p.id)+"_"+ri)[ts.p.useProp?"prop":"attr"]("checked",scb)}}}}}}).bind("reloadGrid",function(e,opts){if(ts.p.treeGrid===true){ts.p.datatype=ts.p.treedatatype}if(opts&&opts.current){ts.grid.selectionPreserver(ts)}if(ts.p.datatype==="local"){$(ts).jqGrid("resetSelection");if(ts.p.data.length){refreshIndex()}}else{if(!ts.p.treeGrid){ts.p.selrow=null;if(ts.p.multiselect){ts.p.selarrrow=[];setHeadCheckBox(false)}ts.p.savedRow=[]}}if(ts.p.scroll){emptyRows.call(ts,true,false)}if(opts&&opts.page){var page=opts.page;if(page>ts.p.lastpage){page=ts.p.lastpage}if(page<1){page=1}ts.p.page=page;if(ts.grid.prevRowHeight){ts.grid.bDiv.scrollTop=(page-1)*ts.grid.prevRowHeight*ts.p.rowNum}else{ts.grid.bDiv.scrollTop=0}}if(ts.grid.prevRowHeight&&ts.p.scroll){delete ts.p.lastpage;ts.grid.populateVisible()}else{ts.grid.populate()}if(ts.p._inlinenav===true){$(ts).jqGrid("showAddEditButtons")}return false}).dblclick(function(e){td=e.target;ptr=$(td,ts.rows).closest("tr.jqgrow");if($(ptr).length===0){return}ri=ptr[0].rowIndex;ci=$.jgrid.getCellIndex(td);$(ts).triggerHandler("jqGridDblClickRow",[$(ptr).attr("id"),ri,ci,e]);if($.isFunction(ts.p.ondblClickRow)){ts.p.ondblClickRow.call(ts,$(ptr).attr("id"),ri,ci,e)}}).bind("contextmenu",function(e){td=e.target;ptr=$(td,ts.rows).closest("tr.jqgrow");if($(ptr).length===0){return}if(!ts.p.multiselect){$(ts).jqGrid("setSelection",ptr[0].id,true,e)}ri=ptr[0].rowIndex;ci=$.jgrid.getCellIndex(td);$(ts).triggerHandler("jqGridRightClickRow",[$(ptr).attr("id"),ri,ci,e]);if($.isFunction(ts.p.onRightClickRow)){ts.p.onRightClickRow.call(ts,$(ptr).attr("id"),ri,ci,e)}});grid.bDiv=document.createElement("div");if(isMSIE){if(String(ts.p.height).toLowerCase()==="auto"){ts.p.height="100%"}}$(grid.bDiv).append($('<div style="position:relative;'+(isMSIE&&$.jgrid.msiever()<8?"height:0.01%;":"")+'"></div>').append("<div></div>").append(this)).addClass("ui-jqgrid-bdiv").css({height:ts.p.height+(isNaN(ts.p.height)?"":"px"),width:(grid.width)+"px"}).scroll(grid.scrollGrid);$("table:first",grid.bDiv).css({width:ts.p.tblwidth+"px"});if(!$.support.tbody){if($("tbody",this).length===2){$("tbody:gt(0)",this).remove()}}if(ts.p.multikey){if($.jgrid.msie){$(grid.bDiv).bind("selectstart",function(){return false})}else{$(grid.bDiv).bind("mousedown",function(){return false})}}if(hg){$(grid.bDiv).hide()}grid.cDiv=document.createElement("div");var arf=ts.p.hidegrid===true?$("<a role='link' class='ui-jqgrid-titlebar-close ui-corner-all HeaderButton' />").hover(function(){arf.addClass("ui-state-hover")},function(){arf.removeClass("ui-state-hover")}).append("<span class='ui-icon ui-icon-circle-triangle-n'></span>").css((dir==="rtl"?"left":"right"),"0px"):"";$(grid.cDiv).append(arf).append("<span class='ui-jqgrid-title'>"+ts.p.caption+"</span>").addClass("ui-jqgrid-titlebar ui-jqgrid-caption"+(dir==="rtl"?"-rtl":"")+" ui-widget-header ui-corner-top ui-helper-clearfix");$(grid.cDiv).insertBefore(grid.hDiv);if(ts.p.toolbar[0]){grid.uDiv=document.createElement("div");if(ts.p.toolbar[1]==="top"){$(grid.uDiv).insertBefore(grid.hDiv)}else{if(ts.p.toolbar[1]==="bottom"){$(grid.uDiv).insertAfter(grid.hDiv)}}if(ts.p.toolbar[1]==="both"){grid.ubDiv=document.createElement("div");$(grid.uDiv).addClass("ui-userdata ui-state-default").attr("id","t_"+this.id).insertBefore(grid.hDiv);$(grid.ubDiv).addClass("ui-userdata ui-state-default").attr("id","tb_"+this.id).insertAfter(grid.hDiv);if(hg){$(grid.ubDiv).hide()}}else{$(grid.uDiv).width(grid.width).addClass("ui-userdata ui-state-default").attr("id","t_"+this.id)}if(hg){$(grid.uDiv).hide()}}if(ts.p.toppager){ts.p.toppager=$.jgrid.jqID(ts.p.id)+"_toppager";grid.topDiv=$("<div id='"+ts.p.toppager+"'></div>")[0];ts.p.toppager="#"+ts.p.toppager;$(grid.topDiv).addClass("ui-state-default ui-jqgrid-toppager").width(grid.width).insertBefore(grid.hDiv);setPager(ts.p.toppager,"_t")}if(ts.p.footerrow){grid.sDiv=$("<div class='ui-jqgrid-sdiv'></div>")[0];hb=$("<div class='ui-jqgrid-hbox"+(dir==="rtl"?"-rtl":"")+"'></div>");$(grid.sDiv).append(hb).width(grid.width).insertAfter(grid.hDiv);$(hb).append(tfoot);grid.footers=$(".ui-jqgrid-ftable",grid.sDiv)[0].rows[0].cells;if(ts.p.rownumbers){grid.footers[0].className="ui-state-default jqgrid-rownum"}if(hg){$(grid.sDiv).hide()}}hb=null;if(ts.p.caption){var tdt=ts.p.datatype;if(ts.p.hidegrid===true){$(".ui-jqgrid-titlebar-close",grid.cDiv).click(function(e){var onHdCl=$.isFunction(ts.p.onHeaderClick),elems=".ui-jqgrid-bdiv, .ui-jqgrid-hdiv, .ui-jqgrid-pager, .ui-jqgrid-sdiv",counter,self=this;if(ts.p.toolbar[0]===true){if(ts.p.toolbar[1]==="both"){elems+=", #"+$(grid.ubDiv).attr("id")}elems+=", #"+$(grid.uDiv).attr("id")}counter=$(elems,"#gview_"+$.jgrid.jqID(ts.p.id)).length;if(ts.p.gridstate==="visible"){$(elems,"#gbox_"+$.jgrid.jqID(ts.p.id)).slideUp("fast",function(){counter--;if(counter===0){$("span",self).removeClass("ui-icon-circle-triangle-n").addClass("ui-icon-circle-triangle-s");ts.p.gridstate="hidden";if($("#gbox_"+$.jgrid.jqID(ts.p.id)).hasClass("ui-resizable")){$(".ui-resizable-handle","#gbox_"+$.jgrid.jqID(ts.p.id)).hide()}$(ts).triggerHandler("jqGridHeaderClick",[ts.p.gridstate,e]);if(onHdCl){if(!hg){ts.p.onHeaderClick.call(ts,ts.p.gridstate,e)}}}})}else{if(ts.p.gridstate==="hidden"){$(elems,"#gbox_"+$.jgrid.jqID(ts.p.id)).slideDown("fast",function(){counter--;if(counter===0){$("span",self).removeClass("ui-icon-circle-triangle-s").addClass("ui-icon-circle-triangle-n");if(hg){ts.p.datatype=tdt;populate();hg=false}ts.p.gridstate="visible";if($("#gbox_"+$.jgrid.jqID(ts.p.id)).hasClass("ui-resizable")){$(".ui-resizable-handle","#gbox_"+$.jgrid.jqID(ts.p.id)).show()}$(ts).triggerHandler("jqGridHeaderClick",[ts.p.gridstate,e]);if(onHdCl){if(!hg){ts.p.onHeaderClick.call(ts,ts.p.gridstate,e)}}}})}}return false});if(hg){ts.p.datatype="local";$(".ui-jqgrid-titlebar-close",grid.cDiv).trigger("click")}}}else{$(grid.cDiv).hide()}$(grid.hDiv).after(grid.bDiv).mousemove(function(e){if(grid.resizing){grid.dragMove(e);return false}});$(".ui-jqgrid-labels",grid.hDiv).bind("selectstart",function(){return false});$(document).bind("mouseup.jqGrid"+ts.p.id,function(){if(grid.resizing){grid.dragEnd();return false}return true});ts.formatCol=formatCol;ts.sortData=sortData;ts.updatepager=updatepager;ts.refreshIndex=refreshIndex;ts.setHeadCheckBox=setHeadCheckBox;ts.constructTr=constructTr;ts.formatter=function(rowId,cellval,colpos,rwdat,act){return formatter(rowId,cellval,colpos,rwdat,act)};$.extend(grid,{populate:populate,emptyRows:emptyRows,beginReq:beginReq,endReq:endReq});this.grid=grid;ts.addXmlData=function(d){addXmlData(d,ts.grid.bDiv)};ts.addJSONData=function(d){addJSONData(d,ts.grid.bDiv)};this.grid.cols=this.rows[0].cells;$(ts).triggerHandler("jqGridInitGrid");if($.isFunction(ts.p.onInitGrid)){ts.p.onInitGrid.call(ts)}populate();ts.p.hiddengrid=false})};$.jgrid.extend({getGridParam:function(pName){var $t=this[0];if(!$t||!$t.grid){return}if(!pName){return $t.p}return $t.p[pName]!==undefined?$t.p[pName]:null},setGridParam:function(newParams){return this.each(function(){if(this.grid&&typeof newParams==="object"){$.extend(true,this.p,newParams)}})},getGridRowById:function(rowid){var row;this.each(function(){try{var i=this.rows.length;while(i--){if(rowid.toString()===this.rows[i].id){row=this.rows[i];break}}}catch(e){row=$(this.grid.bDiv).find("#"+$.jgrid.jqID(rowid))}});return row},getDataIDs:function(){var ids=[],i=0,len,j=0;this.each(function(){len=this.rows.length;if(len&&len>0){while(i<len){if($(this.rows[i]).hasClass("jqgrow")){ids[j]=this.rows[i].id;j++}i++}}});return ids},setSelection:function(selection,onsr,e){return this.each(function(){var $t=this,stat,pt,ner,ia,tpsr,fid;if(selection===undefined){return}onsr=onsr===false?false:true;pt=$($t).jqGrid("getGridRowById",selection);if(!pt||!pt.className||pt.className.indexOf("ui-state-disabled")>-1){return}function scrGrid(iR){var ch=$($t.grid.bDiv)[0].clientHeight,st=$($t.grid.bDiv)[0].scrollTop,rpos=$($t.rows[iR]).position().top,rh=$t.rows[iR].clientHeight;if(rpos+rh>=ch+st){$($t.grid.bDiv)[0].scrollTop=rpos-(ch+st)+rh+st}else{if(rpos<ch+st){if(rpos<st){$($t.grid.bDiv)[0].scrollTop=rpos}}}}if($t.p.scrollrows===true){ner=$($t).jqGrid("getGridRowById",selection).rowIndex;if(ner>=0){scrGrid(ner)}}if($t.p.frozenColumns===true){fid=$t.p.id+"_frozen"}if(!$t.p.multiselect){if(pt.className!=="ui-subgrid"){if($t.p.selrow!==pt.id){$($($t).jqGrid("getGridRowById",$t.p.selrow)).removeClass("ui-state-highlight").attr({"aria-selected":"false","tabindex":"-1"});$(pt).addClass("ui-state-highlight").attr({"aria-selected":"true","tabindex":"0"});if(fid){$("#"+$.jgrid.jqID($t.p.selrow),"#"+$.jgrid.jqID(fid)).removeClass("ui-state-highlight");$("#"+$.jgrid.jqID(selection),"#"+$.jgrid.jqID(fid)).addClass("ui-state-highlight")}stat=true}else{stat=false}$t.p.selrow=pt.id;if(onsr){$($t).triggerHandler("jqGridSelectRow",[pt.id,stat,e]);if($t.p.onSelectRow){$t.p.onSelectRow.call($t,pt.id,stat,e)}}}}else{$t.setHeadCheckBox(false);$t.p.selrow=pt.id;ia=$.inArray($t.p.selrow,$t.p.selarrrow);if(ia===-1){if(pt.className!=="ui-subgrid"){$(pt).addClass("ui-state-highlight").attr("aria-selected","true")}stat=true;$t.p.selarrrow.push($t.p.selrow)}else{if(pt.className!=="ui-subgrid"){$(pt).removeClass("ui-state-highlight").attr("aria-selected","false")}stat=false;$t.p.selarrrow.splice(ia,1);tpsr=$t.p.selarrrow[0];$t.p.selrow=(tpsr===undefined)?null:tpsr}$("#jqg_"+$.jgrid.jqID($t.p.id)+"_"+$.jgrid.jqID(pt.id))[$t.p.useProp?"prop":"attr"]("checked",stat);if(fid){if(ia===-1){$("#"+$.jgrid.jqID(selection),"#"+$.jgrid.jqID(fid)).addClass("ui-state-highlight")}else{$("#"+$.jgrid.jqID(selection),"#"+$.jgrid.jqID(fid)).removeClass("ui-state-highlight")}$("#jqg_"+$.jgrid.jqID($t.p.id)+"_"+$.jgrid.jqID(selection),"#"+$.jgrid.jqID(fid))[$t.p.useProp?"prop":"attr"]("checked",stat)}if(onsr){$($t).triggerHandler("jqGridSelectRow",[pt.id,stat,e]);if($t.p.onSelectRow){$t.p.onSelectRow.call($t,pt.id,stat,e)}}}})},resetSelection:function(rowid){return this.each(function(){var t=this,sr,fid;if(t.p.frozenColumns===true){fid=t.p.id+"_frozen"}if(rowid!==undefined){sr=rowid===t.p.selrow?t.p.selrow:rowid;$("#"+$.jgrid.jqID(t.p.id)+" tbody:first tr#"+$.jgrid.jqID(sr)).removeClass("ui-state-highlight").attr("aria-selected","false");if(fid){$("#"+$.jgrid.jqID(sr),"#"+$.jgrid.jqID(fid)).removeClass("ui-state-highlight")}if(t.p.multiselect){$("#jqg_"+$.jgrid.jqID(t.p.id)+"_"+$.jgrid.jqID(sr),"#"+$.jgrid.jqID(t.p.id))[t.p.useProp?"prop":"attr"]("checked",false);if(fid){$("#jqg_"+$.jgrid.jqID(t.p.id)+"_"+$.jgrid.jqID(sr),"#"+$.jgrid.jqID(fid))[t.p.useProp?"prop":"attr"]("checked",false)}t.setHeadCheckBox(false)}sr=null}else{if(!t.p.multiselect){if(t.p.selrow){$("#"+$.jgrid.jqID(t.p.id)+" tbody:first tr#"+$.jgrid.jqID(t.p.selrow)).removeClass("ui-state-highlight").attr("aria-selected","false");if(fid){$("#"+$.jgrid.jqID(t.p.selrow),"#"+$.jgrid.jqID(fid)).removeClass("ui-state-highlight")}t.p.selrow=null}}else{$(t.p.selarrrow).each(function(i,n){$($(t).jqGrid("getGridRowById",n)).removeClass("ui-state-highlight").attr("aria-selected","false");$("#jqg_"+$.jgrid.jqID(t.p.id)+"_"+$.jgrid.jqID(n))[t.p.useProp?"prop":"attr"]("checked",false);if(fid){$("#"+$.jgrid.jqID(n),"#"+$.jgrid.jqID(fid)).removeClass("ui-state-highlight");$("#jqg_"+$.jgrid.jqID(t.p.id)+"_"+$.jgrid.jqID(n),"#"+$.jgrid.jqID(fid))[t.p.useProp?"prop":"attr"]("checked",false)}});t.setHeadCheckBox(false);t.p.selarrrow=[];t.p.selrow=null}}if(t.p.cellEdit===true){if(parseInt(t.p.iCol,10)>=0&&parseInt(t.p.iRow,10)>=0){$("td:eq("+t.p.iCol+")",t.rows[t.p.iRow]).removeClass("edit-cell ui-state-highlight");$(t.rows[t.p.iRow]).removeClass("selected-row ui-state-hover")}}t.p.savedRow=[]})},getRowData:function(rowid){var res={},resall,getall=false,len,j=0;this.each(function(){var $t=this,nm,ind;if(rowid===undefined){getall=true;resall=[];len=$t.rows.length}else{ind=$($t).jqGrid("getGridRowById",rowid);if(!ind){return res}len=2}while(j<len){if(getall){ind=$t.rows[j]}if($(ind).hasClass("jqgrow")){$('td[role="gridcell"]',ind).each(function(i){nm=$t.p.colModel[i].name;if(nm!=="cb"&&nm!=="subgrid"&&nm!=="rn"){if($t.p.treeGrid===true&&nm===$t.p.ExpandColumn){res[nm]=$.jgrid.htmlDecode($("span:first",this).html())}else{try{res[nm]=$.unformat.call($t,this,{rowId:ind.id,colModel:$t.p.colModel[i]},i)}catch(e){res[nm]=$.jgrid.htmlDecode($(this).html())}}}});if(getall){resall.push(res);res={}}}j++}});return resall||res},delRowData:function(rowid){var success=false,rowInd,ia;this.each(function(){var $t=this;rowInd=$($t).jqGrid("getGridRowById",rowid);if(!rowInd){return false}$(rowInd).remove();$t.p.records--;$t.p.reccount--;$t.updatepager(true,false);success=true;if($t.p.multiselect){ia=$.inArray(rowid,$t.p.selarrrow);if(ia!==-1){$t.p.selarrrow.splice(ia,1)}}if($t.p.multiselect&&$t.p.selarrrow.length>0){$t.p.selrow=$t.p.selarrrow[$t.p.selarrrow.length-1]}else{$t.p.selrow=null}if($t.p.datatype==="local"){var id=$.jgrid.stripPref($t.p.idPrefix,rowid),pos=$t.p._index[id];if(pos!==undefined){$t.p.data.splice(pos,1);$t.refreshIndex()}}if($t.p.altRows===true&&success){var cn=$t.p.altclass;$($t.rows).each(function(i){if(i%2===1){$(this).addClass(cn)}else{$(this).removeClass(cn)}})}});return success},setRowData:function(rowid,data,cssp){var nm,success=true,title;this.each(function(){if(!this.grid){return false}var t=this,vl,ind,cp=typeof cssp,lcdata={};ind=$(this).jqGrid("getGridRowById",rowid);if(!ind){return false}if(data){try{$(this.p.colModel).each(function(i){nm=this.name;var dval=$.jgrid.getAccessor(data,nm);if(dval!==undefined){lcdata[nm]=this.formatter&&typeof this.formatter==="string"&&this.formatter==="date"?$.unformat.date.call(t,dval,this):dval;vl=t.formatter(rowid,dval,i,data,"edit");title=this.title?{"title":$.jgrid.stripHtml(vl)}:{};if(t.p.treeGrid===true&&nm===t.p.ExpandColumn){$("td[role='gridcell']:eq("+i+") > span:first",ind).html(vl).attr(title)}else{$("td[role='gridcell']:eq("+i+")",ind).html(vl).attr(title)}}});if(t.p.datatype==="local"){var id=$.jgrid.stripPref(t.p.idPrefix,rowid),pos=t.p._index[id],key;if(t.p.treeGrid){for(key in t.p.treeReader){if(t.p.treeReader.hasOwnProperty(key)){delete lcdata[t.p.treeReader[key]]}}}if(pos!==undefined){t.p.data[pos]=$.extend(true,t.p.data[pos],lcdata)}lcdata=null}}catch(e){success=false}}if(success){if(cp==="string"){$(ind).addClass(cssp)}else{if(cssp!==null&&cp==="object"){$(ind).css(cssp)}}$(t).triggerHandler("jqGridAfterGridComplete")}});return success},addRowData:function(rowid,rdata,pos,src){if(!pos){pos="last"}var success=false,nm,row,gi,si,ni,sind,i,v,prp="",aradd,cnm,cn,data,cm,id;if(rdata){if($.isArray(rdata)){aradd=true;pos="last";cnm=rowid}else{rdata=[rdata];aradd=false}this.each(function(){var t=this,datalen=rdata.length;ni=t.p.rownumbers===true?1:0;gi=t.p.multiselect===true?1:0;si=t.p.subGrid===true?1:0;if(!aradd){if(rowid!==undefined){rowid=String(rowid)}else{rowid=$.jgrid.randId();if(t.p.keyIndex!==false){cnm=t.p.colModel[t.p.keyIndex+gi+si+ni].name;if(rdata[0][cnm]!==undefined){rowid=rdata[0][cnm]}}}}cn=t.p.altclass;var k=0,cna="",lcdata={},air=$.isFunction(t.p.afterInsertRow)?true:false;while(k<datalen){data=rdata[k];row=[];if(aradd){try{rowid=data[cnm];if(rowid===undefined){rowid=$.jgrid.randId()}}catch(e){rowid=$.jgrid.randId()}cna=t.p.altRows===true?(t.rows.length-1)%2===0?cn:"":""}id=rowid;rowid=t.p.idPrefix+rowid;if(ni){prp=t.formatCol(0,1,"",null,rowid,true);row[row.length]='<td role="gridcell" class="ui-state-default jqgrid-rownum" '+prp+">0</td>"}if(gi){v='<input role="checkbox" type="checkbox" id="jqg_'+t.p.id+"_"+rowid+'" class="cbox"/>';prp=t.formatCol(ni,1,"",null,rowid,true);row[row.length]='<td role="gridcell" '+prp+">"+v+"</td>"}if(si){row[row.length]=$(t).jqGrid("addSubGridCell",gi+ni,1)}for(i=gi+si+ni;i<t.p.colModel.length;i++){cm=t.p.colModel[i];nm=cm.name;lcdata[nm]=data[nm];v=t.formatter(rowid,$.jgrid.getAccessor(data,nm),i,data);prp=t.formatCol(i,1,v,data,rowid,lcdata);row[row.length]='<td role="gridcell" '+prp+">"+v+"</td>"}row.unshift(t.constructTr(rowid,false,cna,lcdata,data,false));row[row.length]="</tr>";if(t.rows.length===0){$("table:first",t.grid.bDiv).append(row.join(""))}else{switch(pos){case"last":$(t.rows[t.rows.length-1]).after(row.join(""));sind=t.rows.length-1;break;case"first":$(t.rows[0]).after(row.join(""));sind=1;break;case"after":sind=$(t).jqGrid("getGridRowById",src);if(sind){if($(t.rows[sind.rowIndex+1]).hasClass("ui-subgrid")){$(t.rows[sind.rowIndex+1]).after(row)}else{$(sind).after(row.join(""))}sind=sind.rowIndex+1}break;case"before":sind=$(t).jqGrid("getGridRowById",src);if(sind){$(sind).before(row.join(""));sind=sind.rowIndex-1}break}}if(t.p.subGrid===true){$(t).jqGrid("addSubGrid",gi+ni,sind)}t.p.records++;t.p.reccount++;$(t).triggerHandler("jqGridAfterInsertRow",[rowid,data,data]);if(air){t.p.afterInsertRow.call(t,rowid,data,data)}k++;if(t.p.datatype==="local"){lcdata[t.p.localReader.id]=id;t.p._index[id]=t.p.data.length;t.p.data.push(lcdata);lcdata={}}}if(t.p.altRows===true&&!aradd){if(pos==="last"){if((t.rows.length-1)%2===1){$(t.rows[t.rows.length-1]).addClass(cn)}}else{$(t.rows).each(function(i){if(i%2===1){$(this).addClass(cn)}else{$(this).removeClass(cn)}})}}t.updatepager(true,true);success=true})}return success},footerData:function(action,data,format){var nm,success=false,res={},title;function isEmpty(obj){var i;for(i in obj){if(obj.hasOwnProperty(i)){return false}}return true}if(action==undefined){action="get"}if(typeof format!=="boolean"){format=true}action=action.toLowerCase();this.each(function(){var t=this,vl;if(!t.grid||!t.p.footerrow){return false}if(action==="set"){if(isEmpty(data)){return false}}success=true;$(this.p.colModel).each(function(i){nm=this.name;if(action==="set"){if(data[nm]!==undefined){vl=format?t.formatter("",data[nm],i,data,"edit"):data[nm];title=this.title?{"title":$.jgrid.stripHtml(vl)}:{};$("tr.footrow td:eq("+i+")",t.grid.sDiv).html(vl).attr(title);success=true}}else{if(action==="get"){res[nm]=$("tr.footrow td:eq("+i+")",t.grid.sDiv).html()}}})});return action==="get"?res:success},showHideCol:function(colname,show){return this.each(function(){var $t=this,fndh=false,brd=$.jgrid.cell_width?0:$t.p.cellLayout,cw;if(!$t.grid){return}if(typeof colname==="string"){colname=[colname]}show=show!=="none"?"":"none";var sw=show===""?true:false,gh=$t.p.groupHeader&&(typeof $t.p.groupHeader==="object"||$.isFunction($t.p.groupHeader));if(gh){$($t).jqGrid("destroyGroupHeader",false)}$(this.p.colModel).each(function(i){if($.inArray(this.name,colname)!==-1&&this.hidden===sw){if($t.p.frozenColumns===true&&this.frozen===true){return true}$("tr[role=rowheader]",$t.grid.hDiv).each(function(){$(this.cells[i]).css("display",show)});$($t.rows).each(function(){if(!$(this).hasClass("jqgroup")){$(this.cells[i]).css("display",show)}});if($t.p.footerrow){$("tr.footrow td:eq("+i+")",$t.grid.sDiv).css("display",show)}cw=parseInt(this.width,10);if(show==="none"){$t.p.tblwidth-=cw+brd}else{$t.p.tblwidth+=cw+brd}this.hidden=!sw;fndh=true;$($t).triggerHandler("jqGridShowHideCol",[sw,this.name,i])}});if(fndh===true){if($t.p.shrinkToFit===true&&!isNaN($t.p.height)){$t.p.tblwidth+=parseInt($t.p.scrollOffset,10)}$($t).jqGrid("setGridWidth",$t.p.shrinkToFit===true?$t.p.tblwidth:$t.p.width)}if(gh){$($t).jqGrid("setGroupHeaders",$t.p.groupHeader)}})},hideCol:function(colname){return this.each(function(){$(this).jqGrid("showHideCol",colname,"none")})},showCol:function(colname){return this.each(function(){$(this).jqGrid("showHideCol",colname,"")})},remapColumns:function(permutation,updateCells,keepHeader){function resortArray(a){var ac;if(a.length){ac=$.makeArray(a)}else{ac=$.extend({},a)}$.each(permutation,function(i){a[i]=ac[this]})}var ts=this.get(0);function resortRows(parent,clobj){$(">tr"+(clobj||""),parent).each(function(){var row=this;var elems=$.makeArray(row.cells);$.each(permutation,function(){var e=elems[this];if(e){row.appendChild(e)}})})}resortArray(ts.p.colModel);resortArray(ts.p.colNames);resortArray(ts.grid.headers);resortRows($("thead:first",ts.grid.hDiv),keepHeader&&":not(.ui-jqgrid-labels)");if(updateCells){resortRows($("#"+$.jgrid.jqID(ts.p.id)+" tbody:first"),".jqgfirstrow, tr.jqgrow, tr.jqfoot")}if(ts.p.footerrow){resortRows($("tbody:first",ts.grid.sDiv))}if(ts.p.remapColumns){if(!ts.p.remapColumns.length){ts.p.remapColumns=$.makeArray(permutation)}else{resortArray(ts.p.remapColumns)}}ts.p.lastsort=$.inArray(ts.p.lastsort,permutation);if(ts.p.treeGrid){ts.p.expColInd=$.inArray(ts.p.expColInd,permutation)}$(ts).triggerHandler("jqGridRemapColumns",[permutation,updateCells,keepHeader])},setGridWidth:function(nwidth,shrink){return this.each(function(){if(!this.grid){return}var $t=this,cw,initwidth=0,brd=$.jgrid.cell_width?0:$t.p.cellLayout,lvc,vc=0,hs=false,scw=$t.p.scrollOffset,aw,gw=0,cr;if(typeof shrink!=="boolean"){shrink=$t.p.shrinkToFit}if(isNaN(nwidth)){return}nwidth=parseInt(nwidth,10);$t.grid.width=$t.p.width=nwidth;$("#gbox_"+$.jgrid.jqID($t.p.id)).css("width",nwidth+"px");$("#gview_"+$.jgrid.jqID($t.p.id)).css("width",nwidth+"px");$($t.grid.bDiv).css("width",nwidth+"px");$($t.grid.hDiv).css("width",nwidth+"px");if($t.p.pager){$($t.p.pager).css("width",nwidth+"px")}if($t.p.toppager){$($t.p.toppager).css("width",nwidth+"px")}if($t.p.toolbar[0]===true){$($t.grid.uDiv).css("width",nwidth+"px");if($t.p.toolbar[1]==="both"){$($t.grid.ubDiv).css("width",nwidth+"px")}}if($t.p.footerrow){$($t.grid.sDiv).css("width",nwidth+"px")}if(shrink===false&&$t.p.forceFit===true){$t.p.forceFit=false}if(shrink===true){$.each($t.p.colModel,function(){if(this.hidden===false){cw=this.widthOrg;initwidth+=cw+brd;if(this.fixed){gw+=cw+brd}else{vc++}}});if(vc===0){return}$t.p.tblwidth=initwidth;aw=nwidth-brd*vc-gw;if(!isNaN($t.p.height)){if($($t.grid.bDiv)[0].clientHeight<$($t.grid.bDiv)[0].scrollHeight||$t.rows.length===1){hs=true;aw-=scw}}initwidth=0;var cle=$t.grid.cols.length>0;$.each($t.p.colModel,function(i){if(this.hidden===false&&!this.fixed){cw=this.widthOrg;cw=Math.round(aw*cw/($t.p.tblwidth-brd*vc-gw));if(cw<0){return}this.width=cw;initwidth+=cw;$t.grid.headers[i].width=cw;$t.grid.headers[i].el.style.width=cw+"px";if($t.p.footerrow){$t.grid.footers[i].style.width=cw+"px"}if(cle){$t.grid.cols[i].style.width=cw+"px"}lvc=i}});if(!lvc){return}cr=0;if(hs){if(nwidth-gw-(initwidth+brd*vc)!==scw){cr=nwidth-gw-(initwidth+brd*vc)-scw}}else{if(Math.abs(nwidth-gw-(initwidth+brd*vc))!==1){cr=nwidth-gw-(initwidth+brd*vc)}}$t.p.colModel[lvc].width+=cr;$t.p.tblwidth=initwidth+cr+brd*vc+gw;if($t.p.tblwidth>nwidth){var delta=$t.p.tblwidth-parseInt(nwidth,10);$t.p.tblwidth=nwidth;cw=$t.p.colModel[lvc].width=$t.p.colModel[lvc].width-delta}else{cw=$t.p.colModel[lvc].width}$t.grid.headers[lvc].width=cw;$t.grid.headers[lvc].el.style.width=cw+"px";if(cle){$t.grid.cols[lvc].style.width=cw+"px"}if($t.p.footerrow){$t.grid.footers[lvc].style.width=cw+"px"}}if($t.p.tblwidth){$("table:first",$t.grid.bDiv).css("width",$t.p.tblwidth+"px");$("table:first",$t.grid.hDiv).css("width",$t.p.tblwidth+"px");$t.grid.hDiv.scrollLeft=$t.grid.bDiv.scrollLeft;if($t.p.footerrow){$("table:first",$t.grid.sDiv).css("width",$t.p.tblwidth+"px")}}})},setGridHeight:function(nh){return this.each(function(){var $t=this;if(!$t.grid){return}var bDiv=$($t.grid.bDiv);bDiv.css({height:nh+(isNaN(nh)?"":"px")});if($t.p.frozenColumns===true){$("#"+$.jgrid.jqID($t.p.id)+"_frozen").parent().height(bDiv.height()-16)}$t.p.height=nh;if($t.p.scroll){$t.grid.populateVisible()}})},setCaption:function(newcap){return this.each(function(){this.p.caption=newcap;$("span.ui-jqgrid-title, span.ui-jqgrid-title-rtl",this.grid.cDiv).html(newcap);$(this.grid.cDiv).show()})},setLabel:function(colname,nData,prop,attrp){return this.each(function(){var $t=this,pos=-1;if(!$t.grid){return}if(colname!==undefined){$($t.p.colModel).each(function(i){if(this.name===colname){pos=i;return false}})}else{return}if(pos>=0){var thecol=$("tr.ui-jqgrid-labels th:eq("+pos+")",$t.grid.hDiv);if(nData){var ico=$(".s-ico",thecol);$("[id^=jqgh_]",thecol).empty().html(nData).append(ico);$t.p.colNames[pos]=nData}if(prop){if(typeof prop==="string"){$(thecol).addClass(prop)}else{$(thecol).css(prop)}}if(typeof attrp==="object"){$(thecol).attr(attrp)}}})},setCell:function(rowid,colname,nData,cssp,attrp,forceupd){return this.each(function(){var $t=this,pos=-1,v,title;if(!$t.grid){return}if(isNaN(colname)){$($t.p.colModel).each(function(i){if(this.name===colname){pos=i;return false}})}else{pos=parseInt(colname,10)}if(pos>=0){var ind=$($t).jqGrid("getGridRowById",rowid);if(ind){var tcell=$("td:eq("+pos+")",ind);if(nData!==""||forceupd===true){v=$t.formatter(rowid,nData,pos,ind,"edit");title=$t.p.colModel[pos].title?{"title":$.jgrid.stripHtml(v)}:{};if($t.p.treeGrid&&$(".tree-wrap",$(tcell)).length>0){$("span",$(tcell)).html(v).attr(title)}else{$(tcell).html(v).attr(title)}if($t.p.datatype==="local"){var cm=$t.p.colModel[pos],index;nData=cm.formatter&&typeof cm.formatter==="string"&&cm.formatter==="date"?$.unformat.date.call($t,nData,cm):nData;index=$t.p._index[$.jgrid.stripPref($t.p.idPrefix,rowid)];if(index!==undefined){$t.p.data[index][cm.name]=nData}}}if(typeof cssp==="string"){$(tcell).addClass(cssp)}else{if(cssp){$(tcell).css(cssp)}}if(typeof attrp==="object"){$(tcell).attr(attrp)}}}})},getCell:function(rowid,col){var ret=false;this.each(function(){var $t=this,pos=-1;if(!$t.grid){return}if(isNaN(col)){$($t.p.colModel).each(function(i){if(this.name===col){pos=i;return false}})}else{pos=parseInt(col,10)}if(pos>=0){var ind=$($t).jqGrid("getGridRowById",rowid);if(ind){try{ret=$.unformat.call($t,$("td:eq("+pos+")",ind),{rowId:ind.id,colModel:$t.p.colModel[pos]},pos)}catch(e){ret=$.jgrid.htmlDecode($("td:eq("+pos+")",ind).html())}}}});return ret},getCol:function(col,obj,mathopr){var ret=[],val,sum=0,min,max,v;obj=typeof obj!=="boolean"?false:obj;if(mathopr===undefined){mathopr=false}this.each(function(){var $t=this,pos=-1;if(!$t.grid){return}if(isNaN(col)){$($t.p.colModel).each(function(i){if(this.name===col){pos=i;return false}})}else{pos=parseInt(col,10)}if(pos>=0){var ln=$t.rows.length,i=0,dlen=0;if(ln&&ln>0){while(i<ln){if($($t.rows[i]).hasClass("jqgrow")){try{val=$.unformat.call($t,$($t.rows[i].cells[pos]),{rowId:$t.rows[i].id,colModel:$t.p.colModel[pos]},pos)}catch(e){val=$.jgrid.htmlDecode($t.rows[i].cells[pos].innerHTML)}if(mathopr){v=parseFloat(val);if(!isNaN(v)){sum+=v;if(max===undefined){max=min=v}min=Math.min(min,v);max=Math.max(max,v);dlen++}}else{if(obj){ret.push({id:$t.rows[i].id,value:val})}else{ret.push(val)}}}i++}if(mathopr){switch(mathopr.toLowerCase()){case"sum":ret=sum;break;case"avg":ret=sum/dlen;break;case"count":ret=(ln-1);break;case"min":ret=min;break;case"max":ret=max;break}}}}});return ret},clearGridData:function(clearfooter){return this.each(function(){var $t=this;if(!$t.grid){return}if(typeof clearfooter!=="boolean"){clearfooter=false}if($t.p.deepempty){$("#"+$.jgrid.jqID($t.p.id)+" tbody:first tr:gt(0)").remove()}else{var trf=$("#"+$.jgrid.jqID($t.p.id)+" tbody:first tr:first")[0];$("#"+$.jgrid.jqID($t.p.id)+" tbody:first").empty().append(trf)}if($t.p.footerrow&&clearfooter){$(".ui-jqgrid-ftable td",$t.grid.sDiv).html("&#160;")}$t.p.selrow=null;$t.p.selarrrow=[];$t.p.savedRow=[];$t.p.records=0;$t.p.page=1;$t.p.lastpage=0;$t.p.reccount=0;$t.p.data=[];$t.p._index={};$t.updatepager(true,false)})},getInd:function(rowid,rc){var ret=false,rw;this.each(function(){rw=$(this).jqGrid("getGridRowById",rowid);if(rw){ret=rc===true?rw:rw.rowIndex}});return ret},bindKeys:function(settings){var o=$.extend({onEnter:null,onSpace:null,onLeftKey:null,onRightKey:null,scrollingRows:true},settings||{});return this.each(function(){var $t=this;if(!$("body").is("[role]")){$("body").attr("role","application")}$t.p.scrollrows=o.scrollingRows;$($t).keydown(function(event){var target=$($t).find("tr[tabindex=0]")[0],id,r,mind,expanded=$t.p.treeReader.expanded_field;if(target){mind=$t.p._index[$.jgrid.stripPref($t.p.idPrefix,target.id)];if(event.keyCode===37||event.keyCode===38||event.keyCode===39||event.keyCode===40){if(event.keyCode===38){r=target.previousSibling;id="";if(r){if($(r).is(":hidden")){while(r){r=r.previousSibling;if(!$(r).is(":hidden")&&$(r).hasClass("jqgrow")){id=r.id;break}}}else{id=r.id}}$($t).jqGrid("setSelection",id,true,event);event.preventDefault()}if(event.keyCode===40){r=target.nextSibling;id="";if(r){if($(r).is(":hidden")){while(r){r=r.nextSibling;if(!$(r).is(":hidden")&&$(r).hasClass("jqgrow")){id=r.id;break}}}else{id=r.id}}$($t).jqGrid("setSelection",id,true,event);event.preventDefault()}if(event.keyCode===37){if($t.p.treeGrid&&$t.p.data[mind][expanded]){$(target).find("div.treeclick").trigger("click")}$($t).triggerHandler("jqGridKeyLeft",[$t.p.selrow]);if($.isFunction(o.onLeftKey)){o.onLeftKey.call($t,$t.p.selrow)}}if(event.keyCode===39){if($t.p.treeGrid&&!$t.p.data[mind][expanded]){$(target).find("div.treeclick").trigger("click")}$($t).triggerHandler("jqGridKeyRight",[$t.p.selrow]);if($.isFunction(o.onRightKey)){o.onRightKey.call($t,$t.p.selrow)}}}else{if(event.keyCode===13){$($t).triggerHandler("jqGridKeyEnter",[$t.p.selrow]);if($.isFunction(o.onEnter)){o.onEnter.call($t,$t.p.selrow)}}else{if(event.keyCode===32){$($t).triggerHandler("jqGridKeySpace",[$t.p.selrow]);if($.isFunction(o.onSpace)){o.onSpace.call($t,$t.p.selrow)}}}}}})})},unbindKeys:function(){return this.each(function(){$(this).unbind("keydown")})},getLocalRow:function(rowid){var ret=false,ind;this.each(function(){if(rowid!==undefined){ind=this.p._index[$.jgrid.stripPref(this.p.idPrefix,rowid)];if(ind>=0){ret=this.p.data[ind]}}});return ret}})})(jQuery);(function(A){A.jgrid.extend({getColProp:function(B){var E={},F=this[0];if(!F.grid){return false}var D=F.p.colModel,C;for(C=0;C<D.length;C++){if(D[C].name===B){E=D[C];break}}return E},setColProp:function(B,C){return this.each(function(){if(this.grid){if(C){var E=this.p.colModel,D;for(D=0;D<E.length;D++){if(E[D].name===B){A.extend(true,this.p.colModel[D],C);break}}}}})},sortGrid:function(B,D,C){return this.each(function(){var I=this,H=-1,E,G=false;if(!I.grid){return}if(!B){B=I.p.sortname}for(E=0;E<I.p.colModel.length;E++){if(I.p.colModel[E].index===B||I.p.colModel[E].name===B){H=E;if(I.p.frozenColumns===true&&I.p.colModel[E].frozen===true){G=I.grid.fhDiv.find("#"+I.p.id+"_"+B)}break}}if(H!==-1){var F=I.p.colModel[H].sortable;if(!G){G=I.grid.headers[H].el}if(typeof F!=="boolean"){F=true}if(typeof D!=="boolean"){D=false}if(F){I.sortData("jqgh_"+I.p.id+"_"+B,H,D,C,G)}}})},clearBeforeUnload:function(){return this.each(function(){var D=this.grid;if(A.isFunction(D.emptyRows)){D.emptyRows.call(this,true,true)}A(document).unbind("mouseup.jqGrid"+this.p.id);A(D.hDiv).unbind("mousemove");A(this).unbind();D.dragEnd=null;D.dragMove=null;D.dragStart=null;D.emptyRows=null;D.populate=null;D.populateVisible=null;D.scrollGrid=null;D.selectionPreserver=null;D.bDiv=null;D.cDiv=null;D.hDiv=null;D.cols=null;var B,C=D.headers.length;for(B=0;B<C;B++){D.headers[B].el=null}this.formatCol=null;this.sortData=null;this.updatepager=null;this.refreshIndex=null;this.setHeadCheckBox=null;this.constructTr=null;this.formatter=null;this.addXmlData=null;this.addJSONData=null;this.grid=null})},GridDestroy:function(){return this.each(function(){if(this.grid){if(this.p.pager){A(this.p.pager).remove()}try{A(this).jqGrid("clearBeforeUnload");A("#gbox_"+A.jgrid.jqID(this.id)).remove()}catch(B){}}})},GridUnload:function(){return this.each(function(){if(!this.grid){return}var B={id:A(this).attr("id"),cl:A(this).attr("class")};if(this.p.pager){A(this.p.pager).empty().removeClass("ui-state-default ui-jqgrid-pager ui-corner-bottom")}var D=document.createElement("table");A(D).attr({id:B.id});D.className=B.cl;var C=A.jgrid.jqID(this.id);A(D).removeClass("ui-jqgrid-btable");if(A(this.p.pager).parents("#gbox_"+C).length===1){A(D).insertBefore("#gbox_"+C).show();A(this.p.pager).insertBefore("#gbox_"+C)}else{A(D).insertBefore("#gbox_"+C).show()}A(this).jqGrid("clearBeforeUnload");A("#gbox_"+C).remove()})},setGridState:function(B){return this.each(function(){if(!this.grid){return}var C=this;if(B==="hidden"){A(".ui-jqgrid-bdiv, .ui-jqgrid-hdiv","#gview_"+A.jgrid.jqID(C.p.id)).slideUp("fast");if(C.p.pager){A(C.p.pager).slideUp("fast")}if(C.p.toppager){A(C.p.toppager).slideUp("fast")}if(C.p.toolbar[0]===true){if(C.p.toolbar[1]==="both"){A(C.grid.ubDiv).slideUp("fast")}A(C.grid.uDiv).slideUp("fast")}if(C.p.footerrow){A(".ui-jqgrid-sdiv","#gbox_"+A.jgrid.jqID(C.p.id)).slideUp("fast")}A(".ui-jqgrid-titlebar-close span",C.grid.cDiv).removeClass("ui-icon-circle-triangle-n").addClass("ui-icon-circle-triangle-s");C.p.gridstate="hidden"}else{if(B==="visible"){A(".ui-jqgrid-hdiv, .ui-jqgrid-bdiv","#gview_"+A.jgrid.jqID(C.p.id)).slideDown("fast");if(C.p.pager){A(C.p.pager).slideDown("fast")}if(C.p.toppager){A(C.p.toppager).slideDown("fast")}if(C.p.toolbar[0]===true){if(C.p.toolbar[1]==="both"){A(C.grid.ubDiv).slideDown("fast")}A(C.grid.uDiv).slideDown("fast")}if(C.p.footerrow){A(".ui-jqgrid-sdiv","#gbox_"+A.jgrid.jqID(C.p.id)).slideDown("fast")}A(".ui-jqgrid-titlebar-close span",C.grid.cDiv).removeClass("ui-icon-circle-triangle-s").addClass("ui-icon-circle-triangle-n");C.p.gridstate="visible"}}})},filterToolbar:function(B){B=A.extend({autosearch:true,searchOnEnter:true,beforeSearch:null,afterSearch:null,beforeClear:null,afterClear:null,searchurl:"",stringResult:false,groupOp:"AND",defaultSearch:"bw",searchOperators:false,resetIcon:"x",operands:{"eq":"==","ne":"!","lt":"<","le":"<=","gt":">","ge":">=","bw":"^","bn":"!^","in":"=","ni":"!=","ew":"|","en":"!@","cn":"~","nc":"!~","nu":"#","nn":"!#"}},A.jgrid.search,B||{});return this.each(function(){var I=this;if(this.ftoolbar){return}var F=function(){var R={},M=0,K,J,Q={},N;A.each(I.p.colModel,function(){var U=A("#gs_"+A.jgrid.jqID(this.name),(this.frozen===true&&I.p.frozenColumns===true)?I.grid.fhDiv:I.grid.hDiv);J=this.index||this.name;if(B.searchOperators){N=U.parent().prev().children("a").attr("soper")||B.defaultSearch}else{N=(this.searchoptions&&this.searchoptions.sopt)?this.searchoptions.sopt[0]:this.stype==="select"?"eq":B.defaultSearch}K=this.stype==="custom"&&A.isFunction(this.searchoptions.custom_value)&&U.length>0&&U[0].nodeName.toUpperCase()==="SPAN"?this.searchoptions.custom_value.call(I,U.children(".customelement:first"),"get"):U.val();if(K||N==="nu"||N==="nn"){R[J]=K;Q[J]=N;M++}else{try{delete I.p.postData[J]}catch(V){}}});var L=M>0?true:false;if(B.stringResult===true||I.p.datatype==="local"){var O='{"groupOp":"'+B.groupOp+'","rules":[';var P=0;A.each(R,function(U,V){if(P>0){O+=","}O+='{"field":"'+U+'",';O+='"op":"'+Q[U]+'",';V+="";O+='"data":"'+V.replace(/\\/g,"\\\\").replace(/\"/g,'\\"')+'"}';P++});O+="]}";A.extend(I.p.postData,{filters:O});A.each(["searchField","searchString","searchOper"],function(U,V){if(I.p.postData.hasOwnProperty(V)){delete I.p.postData[V]}})}else{A.extend(I.p.postData,R)}var T;if(I.p.searchurl){T=I.p.url;A(I).jqGrid("setGridParam",{url:I.p.searchurl})}var S=A(I).triggerHandler("jqGridToolbarBeforeSearch")==="stop"?true:false;if(!S&&A.isFunction(B.beforeSearch)){S=B.beforeSearch.call(I)}if(!S){A(I).jqGrid("setGridParam",{search:L}).trigger("reloadGrid",[{page:1}])}if(T){A(I).jqGrid("setGridParam",{url:T})}A(I).triggerHandler("jqGridToolbarAfterSearch");if(A.isFunction(B.afterSearch)){B.afterSearch.call(I)}},C=function(J){var Q={},N=0,K;J=(typeof J!=="boolean")?true:J;A.each(I.p.colModel,function(){var U,T=A("#gs_"+A.jgrid.jqID(this.name),(this.frozen===true&&I.p.frozenColumns===true)?I.grid.fhDiv:I.grid.hDiv);if(this.searchoptions&&this.searchoptions.defaultValue!==undefined){U=this.searchoptions.defaultValue}K=this.index||this.name;switch(this.stype){case"select":T.find("option").each(function(W){if(W===0){this.selected=true}if(A(this).val()===U){this.selected=true;return false}});if(U!==undefined){Q[K]=U;N++}else{try{delete I.p.postData[K]}catch(S){}}break;case"text":T.val(U||"");if(U!==undefined){Q[K]=U;N++}else{try{delete I.p.postData[K]}catch(V){}}break;case"custom":if(A.isFunction(this.searchoptions.custom_value)&&T.length>0&&T[0].nodeName.toUpperCase()==="SPAN"){this.searchoptions.custom_value.call(I,T.children(".customelement:first"),"set",U||"")}break}});var L=N>0?true:false;I.p.resetsearch=true;if(B.stringResult===true||I.p.datatype==="local"){var P='{"groupOp":"'+B.groupOp+'","rules":[';var O=0;A.each(Q,function(S,T){if(O>0){P+=","}P+='{"field":"'+S+'",';P+='"op":"eq",';T+="";P+='"data":"'+T.replace(/\\/g,"\\\\").replace(/\"/g,'\\"')+'"}';O++});P+="]}";A.extend(I.p.postData,{filters:P});A.each(["searchField","searchString","searchOper"],function(S,T){if(I.p.postData.hasOwnProperty(T)){delete I.p.postData[T]}})}else{A.extend(I.p.postData,Q)}var R;if(I.p.searchurl){R=I.p.url;A(I).jqGrid("setGridParam",{url:I.p.searchurl})}var M=A(I).triggerHandler("jqGridToolbarBeforeClear")==="stop"?true:false;if(!M&&A.isFunction(B.beforeClear)){M=B.beforeClear.call(I)}if(!M){if(J){A(I).jqGrid("setGridParam",{search:L}).trigger("reloadGrid",[{page:1}])}}if(R){A(I).jqGrid("setGridParam",{url:R})}A(I).triggerHandler("jqGridToolbarAfterClear");if(A.isFunction(B.afterClear)){B.afterClear()}},H=function(){var J=A("tr.ui-search-toolbar",I.grid.hDiv),K=I.p.frozenColumns===true?A("tr.ui-search-toolbar",I.grid.fhDiv):false;if(J.css("display")==="none"){J.show();if(K){K.show()}}else{J.hide();if(K){K.hide()}}},D=function(L,R,Q){A("#sopt_menu").remove();R=parseInt(R,10);Q=parseInt(Q,10)+18;var N=A(".ui-jqgrid-view").css("font-size")||"11px";var P='<ul id="sopt_menu" class="ui-search-menu" role="menu" tabindex="0" style="font-size:'+N+";left:"+R+"px;top:"+Q+'px;">',T=A(L).attr("soper"),U,W=[],M;var S=0,K=A(L).attr("colname"),V=I.p.colModel.length;while(S<V){if(I.p.colModel[S].name===K){break}S++}var O=I.p.colModel[S],J=A.extend({},O.searchoptions);if(!J.sopt){J.sopt=[];J.sopt[0]=O.stype==="select"?"eq":B.defaultSearch}A.each(B.odata,function(){W.push(this.oper)});for(S=0;S<J.sopt.length;S++){M=A.inArray(J.sopt[S],W);if(M!==-1){U=T===B.odata[M].oper?"ui-state-highlight":"";P+='<li class="ui-menu-item '+U+'" role="presentation"><a class="ui-corner-all g-menu-item" tabindex="0" role="menuitem" value="'+B.odata[M].oper+'" oper="'+B.operands[B.odata[M].oper]+'"><table cellspacing="0" cellpadding="0" border="0"><tr><td width="25px">'+B.operands[B.odata[M].oper]+"</td><td>"+B.odata[M].text+"</td></tr></table></a></li>"}}P+="</ul>";A("body").append(P);A("#sopt_menu").addClass("ui-menu ui-widget ui-widget-content ui-corner-all");A("#sopt_menu > li > a").hover(function(){A(this).addClass("ui-state-hover")},function(){A(this).removeClass("ui-state-hover")}).click(function(X){var Z=A(this).attr("value"),Y=A(this).attr("oper");A(I).triggerHandler("jqGridToolbarSelectOper",[Z,Y,L]);A("#sopt_menu").hide();A(L).text(Y).attr("soper",Z);if(B.autosearch===true){var a=A(L).parent().next().children()[0];if(A(a).val()||Z==="nu"||Z==="nn"){F()}}})};var E=A("<tr class='ui-search-toolbar' role='rowheader'></tr>");var G;A.each(I.p.colModel,function(R){var Z=this,b,X,j,U="",h="=",g,Q,P=A("<th role='columnheader' class='ui-state-default ui-th-column ui-th-"+I.p.direction+"'></th>"),T=A("<div style='position:relative;height:100%;padding-right:0.3em;padding-left:0.3em;'></div>"),c=A("<table class='ui-search-table' cellspacing='0'><tr><td class='ui-search-oper'></td><td class='ui-search-input'></td><td class='ui-search-clear'></td></tr></table>");if(this.hidden===true){A(P).css("display","none")}this.search=this.search===false?false:true;if(this.stype===undefined){this.stype="text"}b=A.extend({},this.searchoptions||{});if(this.search){if(B.searchOperators){g=(b.sopt)?b.sopt[0]:Z.stype==="select"?"eq":B.defaultSearch;for(Q=0;Q<B.odata.length;Q++){if(B.odata[Q].oper===g){h=B.operands[g]||"";break}}var a=b.searchtitle!=null?b.searchtitle:B.operandTitle;U="<a title='"+a+"' style='padding-right: 0.5em;' soper='"+g+"' class='soptclass' colname='"+this.name+"'>"+h+"</a>"}A("td:eq(0)",c).attr("colindex",R).append(U);if(b.clearSearch===undefined){b.clearSearch=true}if(b.clearSearch){var d=B.resetTitle||"Clear Search Value";A("td:eq(2)",c).append("<a title='"+d+"' style='padding-right: 0.3em;padding-left: 0.3em;' class='clearsearchclass'>"+B.resetIcon+"</a>")}else{A("td:eq(2)",c).hide()}switch(this.stype){case"select":X=this.surl||b.dataUrl;if(X){j=T;A(j).append(c);A.ajax(A.extend({url:X,dataType:"html",success:function(i){if(b.buildSelect!==undefined){var e=b.buildSelect(i);if(e){A("td:eq(1)",c).append(e)}}else{A("td:eq(1)",c).append(i)}if(b.defaultValue!==undefined){A("select",j).val(b.defaultValue)}A("select",j).attr({name:Z.index||Z.name,id:"gs_"+Z.name});if(b.attr){A("select",j).attr(b.attr)}A("select",j).css({width:"100%"});A.jgrid.bindEv.call(I,A("select",j)[0],b);if(B.autosearch===true){A("select",j).change(function(){F();return false})}i=null}},A.jgrid.ajaxOptions,I.p.ajaxSelectOptions||{}))}else{var f,M,N;if(Z.searchoptions){f=Z.searchoptions.value===undefined?"":Z.searchoptions.value;M=Z.searchoptions.separator===undefined?":":Z.searchoptions.separator;N=Z.searchoptions.delimiter===undefined?";":Z.searchoptions.delimiter}else{if(Z.editoptions){f=Z.editoptions.value===undefined?"":Z.editoptions.value;M=Z.editoptions.separator===undefined?":":Z.editoptions.separator;N=Z.editoptions.delimiter===undefined?";":Z.editoptions.delimiter}}if(f){var S=document.createElement("select");S.style.width="100%";A(S).attr({name:Z.index||Z.name,id:"gs_"+Z.name});var W,K,Y,O;if(typeof f==="string"){g=f.split(N);for(O=0;O<g.length;O++){W=g[O].split(M);K=document.createElement("option");K.value=W[0];K.innerHTML=W[1];S.appendChild(K)}}else{if(typeof f==="object"){for(Y in f){if(f.hasOwnProperty(Y)){K=document.createElement("option");K.value=Y;K.innerHTML=f[Y];S.appendChild(K)}}}}if(b.defaultValue!==undefined){A(S).val(b.defaultValue)}if(b.attr){A(S).attr(b.attr)}A(T).append(c);A.jgrid.bindEv.call(I,S,b);A("td:eq(1)",c).append(S);if(B.autosearch===true){A(S).change(function(){F();return false})}}}break;case"text":var V=b.defaultValue!==undefined?b.defaultValue:"";A("td:eq(1)",c).append("<input type='text' style='width:100%;padding:0px;' name='"+(Z.index||Z.name)+"' id='gs_"+Z.name+"' value='"+V+"'/>");A(T).append(c);if(b.attr){A("input",T).attr(b.attr)}A.jgrid.bindEv.call(I,A("input",T)[0],b);if(B.autosearch===true){if(B.searchOnEnter){A("input",T).keypress(function(k){var i=k.charCode||k.keyCode||0;if(i===13){F();return false}return this})}else{A("input",T).keydown(function(k){var i=k.which;switch(i){case 13:return false;case 9:case 16:case 37:case 38:case 39:case 40:case 27:break;default:if(G){clearTimeout(G)}G=setTimeout(function(){F()},500)}})}}break;case"custom":A("td:eq(1)",c).append("<span style='width:95%;padding:0px;' name='"+(Z.index||Z.name)+"' id='gs_"+Z.name+"'/>");A(T).append(c);try{if(A.isFunction(b.custom_element)){var J=b.custom_element.call(I,b.defaultValue!==undefined?b.defaultValue:"",b);if(J){J=A(J).addClass("customelement");A(T).find(">span").append(J)}else{throw"e2"}}else{throw"e1"}}catch(L){if(L==="e1"){A.jgrid.info_dialog(A.jgrid.errors.errcap,"function 'custom_element' "+A.jgrid.edit.msg.nodefined,A.jgrid.edit.bClose)}if(L==="e2"){A.jgrid.info_dialog(A.jgrid.errors.errcap,"function 'custom_element' "+A.jgrid.edit.msg.novalue,A.jgrid.edit.bClose)}else{A.jgrid.info_dialog(A.jgrid.errors.errcap,typeof L==="string"?L:L.message,A.jgrid.edit.bClose)}}break}}A(P).append(T);A(E).append(P);if(!B.searchOperators){A("td:eq(0)",c).hide()}});A("table thead",I.grid.hDiv).append(E);if(B.searchOperators){A(".soptclass",E).click(function(K){var J=A(this).offset(),L=(J.left),M=(J.top);D(this,L,M);K.stopPropagation()});A("body").on("click",function(J){if(J.target.className!=="soptclass"){A("#sopt_menu").hide()}})}A(".clearsearchclass",E).click(function(K){var N=A(this).parents("tr:first"),L=parseInt(A("td.ui-search-oper",N).attr("colindex"),10),M=A.extend({},I.p.colModel[L].searchoptions||{}),J=M.defaultValue?M.defaultValue:"";if(I.p.colModel[L].stype==="select"){if(J){A("td.ui-search-input select",N).val(J)}else{A("td.ui-search-input select",N)[0].selectedIndex=0}}else{A("td.ui-search-input input",N).val(J)}if(B.autosearch===true){F()}});this.ftoolbar=true;this.triggerToolbar=F;this.clearToolbar=C;this.toggleToolbar=H})},destroyFilterToolbar:function(){return this.each(function(){if(!this.ftoolbar){return}this.triggerToolbar=null;this.clearToolbar=null;this.toggleToolbar=null;this.ftoolbar=false;A(this.grid.hDiv).find("table thead tr.ui-search-toolbar").remove()})},destroyGroupHeader:function(B){if(B===undefined){B=true}return this.each(function(){var K=this,H,G,J,L,N,I,M=K.grid,D=A("table.ui-jqgrid-htable thead",M.hDiv),E=K.p.colModel,F;if(!M){return}A(this).unbind(".setGroupHeaders");H=A("<tr>",{role:"rowheader"}).addClass("ui-jqgrid-labels");L=M.headers;for(G=0,J=L.length;G<J;G++){F=E[G].hidden?"none":"";N=A(L[G].el).width(L[G].width).css("display",F);try{N.removeAttr("rowSpan")}catch(C){N.attr("rowSpan",1)}H.append(N);I=N.children("span.ui-jqgrid-resize");if(I.length>0){I[0].style.height=""}N.children("div")[0].style.top=""}A(D).children("tr.ui-jqgrid-labels").remove();A(D).prepend(H);if(B===true){A(K).jqGrid("setGridParam",{"groupHeader":null})}})},setGroupHeaders:function(B){B=A.extend({useColSpanStyle:false,groupHeaders:[]},B||{});return this.each(function(){this.p.groupHeader=B;var E=this,X,J,G=0,L,H,I,Q,N,Z,T,D,V,W,R=E.p.colModel,M=R.length,S=E.grid.headers,P=A("table.ui-jqgrid-htable",E.grid.hDiv),O=P.children("thead").children("tr.ui-jqgrid-labels:last").addClass("jqg-second-row-header"),U=P.children("thead"),F,C=P.find(".jqg-first-row-header");if(C[0]===undefined){C=A("<tr>",{role:"row","aria-hidden":"true"}).addClass("jqg-first-row-header").css("height","auto")}else{C.empty()}var Y,K=function(d,b){var a=b.length,c;for(c=0;c<a;c++){if(b[c].startColumnName===d){return c}}return -1};A(E).prepend(U);L=A("<tr>",{role:"rowheader"}).addClass("ui-jqgrid-labels jqg-third-row-header");for(X=0;X<M;X++){I=S[X].el;Q=A(I);J=R[X];N={height:"0px",width:S[X].width+"px",display:(J.hidden?"none":"")};A("<th>",{role:"gridcell"}).css(N).addClass("ui-first-th-"+E.p.direction).appendTo(C);I.style.width="";Z=K(J.name,B.groupHeaders);if(Z>=0){T=B.groupHeaders[Z];D=T.numberOfColumns;V=T.titleText;for(W=0,Z=0;Z<D&&(X+Z<M);Z++){if(!R[X+Z].hidden){W++}}H=A("<th>").attr({role:"columnheader"}).addClass("ui-state-default ui-th-column-header ui-th-"+E.p.direction).css({"height":"22px","border-top":"0 none"}).html(V);if(W>0){H.attr("colspan",String(W))}if(E.p.headertitles){H.attr("title",H.text())}if(W===0){H.hide()}Q.before(H);L.append(I);G=D-1}else{if(G===0){if(B.useColSpanStyle){Q.attr("rowspan","2")}else{A("<th>",{role:"columnheader"}).addClass("ui-state-default ui-th-column-header ui-th-"+E.p.direction).css({"display":J.hidden?"none":"","border-top":"0 none"}).insertBefore(Q);L.append(I)}}else{L.append(I);G--}}}F=A(E).children("thead");F.prepend(C);L.insertAfter(O);P.append(F);if(B.useColSpanStyle){P.find("span.ui-jqgrid-resize").each(function(){var a=A(this).parent();if(a.is(":visible")){this.style.cssText="height: "+a.height()+"px !important; cursor: col-resize;"}});P.find("div.ui-jqgrid-sortable").each(function(){var b=A(this),a=b.parent();if(a.is(":visible")&&a.is(":has(span.ui-jqgrid-resize)")){b.css("top",(a.height()-b.outerHeight())/2+"px")}})}Y=F.find("tr.jqg-first-row-header");A(E).bind("jqGridResizeStop.setGroupHeaders",function(b,a,c){Y.find("th").eq(c).width(a)})})},setFrozenColumns:function(){return this.each(function(){if(!this.grid){return}var M=this,F=M.p.colModel,J=0,N=F.length,I=-1,D=false;if(M.p.subGrid===true||M.p.treeGrid===true||M.p.cellEdit===true||M.p.sortable||M.p.scroll){return}if(M.p.rownumbers){J++}if(M.p.multiselect){J++}while(J<N){if(F[J].frozen===true){D=true;I=J}else{break}J++}if(I>=0&&D){var H=M.p.caption?A(M.grid.cDiv).outerHeight():0,G=A(".ui-jqgrid-htable","#gview_"+A.jgrid.jqID(M.p.id)).height();if(M.p.toppager){H=H+A(M.grid.topDiv).outerHeight()}if(M.p.toolbar[0]===true){if(M.p.toolbar[1]!=="bottom"){H=H+A(M.grid.uDiv).outerHeight()}}M.grid.fhDiv=A('<div style="position:absolute;left:0px;top:'+H+"px;height:"+G+'px;" class="frozen-div ui-state-default ui-jqgrid-hdiv"></div>');M.grid.fbDiv=A('<div style="position:absolute;left:0px;top:'+(parseInt(H,10)+parseInt(G,10)+1)+'px;overflow-y:hidden" class="frozen-bdiv ui-jqgrid-bdiv"></div>');A("#gview_"+A.jgrid.jqID(M.p.id)).append(M.grid.fhDiv);var E=A(".ui-jqgrid-htable","#gview_"+A.jgrid.jqID(M.p.id)).clone(true);if(M.p.groupHeader){A("tr.jqg-first-row-header, tr.jqg-third-row-header",E).each(function(){A("th:gt("+I+")",this).remove()});var B=-1,K=-1,C,L;A("tr.jqg-second-row-header th",E).each(function(){C=parseInt(A(this).attr("colspan"),10);L=parseInt(A(this).attr("rowspan"),10);if(L){B++;K++}if(C){B=B+C;K++}if(B===I){return false}});if(B!==I){K=I}A("tr.jqg-second-row-header",E).each(function(){A("th:gt("+K+")",this).remove()})}else{A("tr",E).each(function(){A("th:gt("+I+")",this).remove()})}A(E).width(1);A(M.grid.fhDiv).append(E).mousemove(function(O){if(M.grid.resizing){M.grid.dragMove(O);return false}});A(M).bind("jqGridResizeStop.setFrozenColumns",function(P,R,S){var O=A(".ui-jqgrid-htable",M.grid.fhDiv);A("th:eq("+S+")",O).width(R);var Q=A(".ui-jqgrid-btable",M.grid.fbDiv);A("tr:first td:eq("+S+")",Q).width(R)});A(M).bind("jqGridSortCol.setFrozenColumns",function(O,R,Q){var P=A("tr.ui-jqgrid-labels:last th:eq("+M.p.lastsort+")",M.grid.fhDiv),S=A("tr.ui-jqgrid-labels:last th:eq("+Q+")",M.grid.fhDiv);A("span.ui-grid-ico-sort",P).addClass("ui-state-disabled");A(P).attr("aria-selected","false");A("span.ui-icon-"+M.p.sortorder,S).removeClass("ui-state-disabled");A(S).attr("aria-selected","true");if(!M.p.viewsortcols[0]){if(M.p.lastsort!==Q){A("span.s-ico",P).hide();A("span.s-ico",S).show()}}});A("#gview_"+A.jgrid.jqID(M.p.id)).append(M.grid.fbDiv);A(M.grid.bDiv).scroll(function(){A(M.grid.fbDiv).scrollTop(A(this).scrollTop())});if(M.p.hoverrows===true){A("#"+A.jgrid.jqID(M.p.id)).unbind("mouseover").unbind("mouseout")}A(M).bind("jqGridAfterGridComplete.setFrozenColumns",function(){A("#"+A.jgrid.jqID(M.p.id)+"_frozen").remove();A(M.grid.fbDiv).height(A(M.grid.bDiv).height()-16);var O=A("#"+A.jgrid.jqID(M.p.id)).clone(true);A("tr[role=row]",O).each(function(){A("td[role=gridcell]:gt("+I+")",this).remove()});A(O).width(1).attr("id",M.p.id+"_frozen");A(M.grid.fbDiv).append(O);if(M.p.hoverrows===true){A("tr.jqgrow",O).hover(function(){A(this).addClass("ui-state-hover");A("#"+A.jgrid.jqID(this.id),"#"+A.jgrid.jqID(M.p.id)).addClass("ui-state-hover")},function(){A(this).removeClass("ui-state-hover");A("#"+A.jgrid.jqID(this.id),"#"+A.jgrid.jqID(M.p.id)).removeClass("ui-state-hover")});A("tr.jqgrow","#"+A.jgrid.jqID(M.p.id)).hover(function(){A(this).addClass("ui-state-hover");A("#"+A.jgrid.jqID(this.id),"#"+A.jgrid.jqID(M.p.id)+"_frozen").addClass("ui-state-hover")},function(){A(this).removeClass("ui-state-hover");A("#"+A.jgrid.jqID(this.id),"#"+A.jgrid.jqID(M.p.id)+"_frozen").removeClass("ui-state-hover")})}O=null});if(!M.grid.hDiv.loading){A(M).triggerHandler("jqGridAfterGridComplete")}M.p.frozenColumns=true}})},destroyFrozenColumns:function(){return this.each(function(){if(!this.grid){return}if(this.p.frozenColumns===true){var C=this;A(C.grid.fhDiv).remove();A(C.grid.fbDiv).remove();C.grid.fhDiv=null;C.grid.fbDiv=null;A(this).unbind(".setFrozenColumns");if(C.p.hoverrows===true){var B;A("#"+A.jgrid.jqID(C.p.id)).bind("mouseover",function(D){B=A(D.target).closest("tr.jqgrow");if(A(B).attr("class")!=="ui-subgrid"){A(B).addClass("ui-state-hover")}}).bind("mouseout",function(D){B=A(D.target).closest("tr.jqgrow");A(B).removeClass("ui-state-hover")})}this.p.frozenColumns=false}})}})})(jQuery);(function(K){K.fn.jqm=function(A){var F={overlay:50,closeoverlay:true,overlayClass:"jqmOverlay",closeClass:"jqmClose",trigger:".jqModal",ajax:I,ajaxText:"",target:I,modal:I,toTop:I,onShow:I,onHide:I,onLoad:I};return this.each(function(){if(this._jqm){return D[this._jqm].c=K.extend({},D[this._jqm].c,A)}C++;this._jqm=C;D[C]={c:K.extend(F,K.jqm.params,A),a:I,w:K(this).addClass("jqmID"+C),s:C};if(F.trigger){K(this).jqmAddTrigger(F.trigger)}})};K.fn.jqmAddClose=function(A){return M(this,A,"jqmHide")};K.fn.jqmAddTrigger=function(A){return M(this,A,"jqmShow")};K.fn.jqmShow=function(A){return this.each(function(){K.jqm.open(this._jqm,A)})};K.fn.jqmHide=function(A){return this.each(function(){K.jqm.close(this._jqm,A)})};K.jqm={hash:{},open:function(H,L){var P=D[H],S=P.c,F="."+S.closeClass,R=(parseInt(P.w.css("z-index")));R=(R>0)?R:3000;var Q=K("<div></div>").css({height:"100%",width:"100%",position:"fixed",left:0,top:0,"z-index":R-1,opacity:S.overlay/100});if(P.a){return I}P.t=L;P.a=true;P.w.css("z-index",R);if(S.modal){if(!G[0]){setTimeout(function(){E("bind")},1)}G.push(H)}else{if(S.overlay>0){if(S.closeoverlay){P.w.jqmAddClose(Q)}}else{Q=I}}P.o=(Q)?Q.addClass(S.overlayClass).prependTo("body"):I;if(S.ajax){var A=S.target||P.w,O=S.ajax;A=(typeof A=="string")?K(A,P.w):K(A);O=(O.substr(0,1)=="@")?K(L).attr(O.substring(1)):O;A.html(S.ajaxText).load(O,function(){if(S.onLoad){S.onLoad.call(this,P)}if(F){P.w.jqmAddClose(K(F,P.w))}B(P)})}else{if(F){P.w.jqmAddClose(K(F,P.w))}}if(S.toTop&&P.o){P.w.before('<span id="jqmP'+P.w[0]._jqm+'"></span>').insertAfter(P.o)}(S.onShow)?S.onShow(P):P.w.show();B(P);return I},close:function(F){var A=D[F];if(!A.a){return I}A.a=I;if(G[0]){G.pop();if(!G[0]){E("unbind")}}if(A.c.toTop&&A.o){K("#jqmP"+A.w[0]._jqm).after(A.w).remove()}if(A.c.onHide){A.c.onHide(A)}else{A.w.hide();if(A.o){A.o.remove()}}return I},params:{}};var C=0,D=K.jqm.hash,G=[],I=false,B=function(A){N(A)},N=function(A){try{K(":input:visible",A.w)[0].focus()}catch(F){}},E=function(A){K(document)[A]("keypress",J)[A]("keydown",J)[A]("mousedown",J)},J=function(H){var A=D[G[G.length-1]],F=(!K(H.target).parents(".jqmID"+A.s)[0]);if(F){K(".jqmID"+A.s).each(function(){var O=K(this),L=O.offset();if(L.top<=H.pageY&&H.pageY<=L.top+O.height()&&L.left<=H.pageX&&H.pageX<=L.left+O.width()){F=false;return false}});N(A)}return !F},M=function(A,F,H){return A.each(function(){var L=this._jqm;K(F).each(function(){if(!this[H]){this[H]=[];K(this).click(function(){for(var O in {jqmShow:1,jqmHide:1}){for(var P in this[O]){if(D[this[O][P]]){D[this[O][P]].w[O](this)}}}return I})}this[H].push(L)})})}})(jQuery);(function(H){H.fn.jqDrag=function(E){return D(this,E,"d")};H.fn.jqResize=function(E,J){return D(this,E,"r",J)};H.jqDnR={dnr:{},e:0,drag:function(E){if(B.k=="d"){F.css({left:B.X+E.pageX-B.pX,top:B.Y+E.pageY-B.pY})}else{F.css({width:Math.max(E.pageX-B.pX+B.W,0),height:Math.max(E.pageY-B.pY+B.H,0)});if(G){A.css({width:Math.max(E.pageX-G.pX+G.W,0),height:Math.max(E.pageY-G.pY+G.H,0)})}}return false},stop:function(){H(document).unbind("mousemove",K.drag).unbind("mouseup",K.stop)}};var K=H.jqDnR,B=K.dnr,F=K.e,A,G,D=function(L,E,M,J){return L.each(function(){E=(E)?H(E,L):L;E.bind("mousedown",{e:L,k:M},function(Q){var N=Q.data,P={};F=N.e;A=J?H(J):false;if(F.css("position")!="relative"){try{F.position(P)}catch(O){}}B={X:P.left||I("left")||0,Y:P.top||I("top")||0,W:I("width")||F[0].scrollWidth||0,H:I("height")||F[0].scrollHeight||0,pX:Q.pageX,pY:Q.pageY,k:N.k};if(A&&N.k!="d"){G={X:P.left||C("left")||0,Y:P.top||C("top")||0,W:A[0].offsetWidth||C("width")||0,H:A[0].offsetHeight||C("height")||0,pX:Q.pageX,pY:Q.pageY,k:N.k}}else{G=false}if(H("input.hasDatepicker",F[0])[0]){try{H("input.hasDatepicker",F[0]).datepicker("hide")}catch(R){}}H(document).mousemove(H.jqDnR.drag).mouseup(H.jqDnR.stop);return false})})},I=function(E){return parseInt(F.css(E),10)||false},C=function(E){return parseInt(A.css(E),10)||false}})(jQuery);var xmlJsonClass={xml2json:function(D,B){if(D.nodeType===9){D=D.documentElement}var C=this.removeWhite(D);var A=this.toObj(C);var E=this.toJson(A,D.nodeName,"\t");return"{\n"+B+(B?E.replace(/\t/g,B):E.replace(/\t|\n/g,""))+"\n}"},json2xml:function(A,B){var E=function(H,G,J){var F="";var K,L;if(H instanceof Array){if(H.length===0){F+=J+"<"+G+">__EMPTY_ARRAY_</"+G+">\n"}else{for(K=0,L=H.length;K<L;K+=1){var M=J+E(H[K],G,J+"\t")+"\n";F+=M}}}else{if(typeof(H)==="object"){var I=false;F+=J+"<"+G;var N;for(N in H){if(H.hasOwnProperty(N)){if(N.charAt(0)==="@"){F+=" "+N.substr(1)+'="'+H[N].toString()+'"'}else{I=true}}}F+=I?">":"/>";if(I){for(N in H){if(H.hasOwnProperty(N)){if(N==="#text"){F+=H[N]}else{if(N==="#cdata"){F+="<![CDATA["+H[N]+"]]>"}else{if(N.charAt(0)!=="@"){F+=E(H[N],N,J+"\t")}}}}}F+=(F.charAt(F.length-1)==="\n"?J:"")+"</"+G+">"}}else{if(typeof(H)==="function"){F+=J+"<"+G+"><![CDATA["+H+"]]></"+G+">"}else{if(H===undefined){H=""}if(H.toString()==='""'||H.toString().length===0){F+=J+"<"+G+">__EMPTY_STRING_</"+G+">"}else{F+=J+"<"+G+">"+H.toString()+"</"+G+">"}}}}return F};var D="";var C;for(C in A){if(A.hasOwnProperty(C)){D+=E(A[C],C,"")}}return B?D.replace(/\t/g,B):D.replace(/\t|\n/g,"")},toObj:function(A){var F={};var C=/function/i;if(A.nodeType===1){if(A.attributes.length){var D;for(D=0;D<A.attributes.length;D+=1){F["@"+A.attributes[D].nodeName]=(A.attributes[D].nodeValue||"").toString()}}if(A.firstChild){var H=0,B=0,E=false;var G;for(G=A.firstChild;G;G=G.nextSibling){if(G.nodeType===1){E=true}else{if(G.nodeType===3&&G.nodeValue.match(/[^ \f\n\r\t\v]/)){H+=1}else{if(G.nodeType===4){B+=1}}}}if(E){if(H<2&&B<2){this.removeWhite(A);for(G=A.firstChild;G;G=G.nextSibling){if(G.nodeType===3){F["#text"]=this.escape(G.nodeValue)}else{if(G.nodeType===4){if(C.test(G.nodeValue)){F[G.nodeName]=[F[G.nodeName],G.nodeValue]}else{F["#cdata"]=this.escape(G.nodeValue)}}else{if(F[G.nodeName]){if(F[G.nodeName] instanceof Array){F[G.nodeName][F[G.nodeName].length]=this.toObj(G)}else{F[G.nodeName]=[F[G.nodeName],this.toObj(G)]}}else{F[G.nodeName]=this.toObj(G)}}}}}else{if(!A.attributes.length){F=this.escape(this.innerXml(A))}else{F["#text"]=this.escape(this.innerXml(A))}}}else{if(H){if(!A.attributes.length){F=this.escape(this.innerXml(A));if(F==="__EMPTY_ARRAY_"){F="[]"}else{if(F==="__EMPTY_STRING_"){F=""}}}else{F["#text"]=this.escape(this.innerXml(A))}}else{if(B){if(B>1){F=this.escape(this.innerXml(A))}else{for(G=A.firstChild;G;G=G.nextSibling){if(C.test(A.firstChild.nodeValue)){F=A.firstChild.nodeValue;break}else{F["#cdata"]=this.escape(G.nodeValue)}}}}}}}if(!A.attributes.length&&!A.firstChild){F=null}}else{if(A.nodeType===9){F=this.toObj(A.documentElement)}else{alert("unhandled node type: "+A.nodeType)}}return F},toJson:function(I,A,F,L){if(L===undefined){L=true}var C=A?('"'+A+'"'):"",E="\t",B="\n";if(!L){E="";B=""}if(I==="[]"){C+=(A?":[]":"[]")}else{if(I instanceof Array){var H,G,D=[];for(G=0,H=I.length;G<H;G+=1){D[G]=this.toJson(I[G],"",F+E,L)}C+=(A?":[":"[")+(D.length>1?(B+F+E+D.join(","+B+F+E)+B+F):D.join(""))+"]"}else{if(I===null){C+=(A&&":")+"null"}else{if(typeof(I)==="object"){var K=[],J;for(J in I){if(I.hasOwnProperty(J)){K[K.length]=this.toJson(I[J],J,F+E,L)}}C+=(A?":{":"{")+(K.length>1?(B+F+E+K.join(","+B+F+E)+B+F):K.join(""))+"}"}else{if(typeof(I)==="string"){C+=(A&&":")+'"'+I.replace(/\\/g,"\\\\").replace(/\"/g,'\\"')+'"'}else{C+=(A&&":")+I.toString()}}}}}return C},innerXml:function(B){var A="";if("innerHTML" in B){A=B.innerHTML}else{var C=function(F){var G="",E;if(F.nodeType===1){G+="<"+F.nodeName;for(E=0;E<F.attributes.length;E+=1){G+=" "+F.attributes[E].nodeName+'="'+(F.attributes[E].nodeValue||"").toString()+'"'}if(F.firstChild){G+=">";for(var H=F.firstChild;H;H=H.nextSibling){G+=C(H)}G+="</"+F.nodeName+">"}else{G+="/>"}}else{if(F.nodeType===3){G+=F.nodeValue}else{if(F.nodeType===4){G+="<![CDATA["+F.nodeValue+"]]>"}}}return G};for(var D=B.firstChild;D;D=D.nextSibling){A+=C(D)}}return A},escape:function(A){return A.replace(/[\\]/g,"\\\\").replace(/[\"]/g,'\\"').replace(/[\n]/g,"\\n").replace(/[\r]/g,"\\r")},removeWhite:function(A){A.normalize();var B;for(B=A.firstChild;B;){if(B.nodeType===3){if(!B.nodeValue.match(/[^ \f\n\r\t\v]/)){var C=B.nextSibling;A.removeChild(B);B=C}else{B=B.nextSibling}}else{if(B.nodeType===1){this.removeWhite(B);B=B.nextSibling}else{B=B.nextSibling}}}return A}};(function(A){A.fmatter={};A.extend(A.fmatter,{isBoolean:function(B){return typeof B==="boolean"},isObject:function(B){return(B&&(typeof B==="object"||A.isFunction(B)))||false},isString:function(B){return typeof B==="string"},isNumber:function(B){return typeof B==="number"&&isFinite(B)},isValue:function(B){return(this.isObject(B)||this.isString(B)||this.isNumber(B)||this.isBoolean(B))},isEmpty:function(B){if(!this.isString(B)&&this.isValue(B)){return false}if(!this.isValue(B)){return true}B=A.trim(B).replace(/\&nbsp\;/ig,"").replace(/\&#160\;/ig,"");return B===""}});A.fn.fmatter=function(E,D,C,H,B){var F=D;C=A.extend({},A.jgrid.formatter,C);try{F=A.fn.fmatter[E].call(this,D,C,H,B)}catch(G){}return F};A.fmatter.util={NumberFormat:function(C,G){if(!A.fmatter.isNumber(C)){C*=1}if(A.fmatter.isNumber(C)){var I=(C<0);var H=String(C);var B=G.decimalSeparator||".";var D;if(A.fmatter.isNumber(G.decimalPlaces)){var J=G.decimalPlaces;var K=Math.pow(10,J);H=String(Math.round(C*K)/K);D=H.lastIndexOf(".");if(J>0){if(D<0){H+=B;D=H.length-1}else{if(B!=="."){H=H.replace(".",B)}}while((H.length-1-D)<J){H+="0"}}}if(G.thousandsSeparator){var E=G.thousandsSeparator;D=H.lastIndexOf(B);D=(D>-1)?D:H.length;var L=H.substring(D);var M=-1,F;for(F=D;F>0;F--){M++;if((M%3===0)&&(F!==D)&&(!I||(F>1))){L=E+L}L=H.charAt(F-1)+L}H=L}H=(G.prefix)?G.prefix+H:H;H=(G.suffix)?H+G.suffix:H;return H}return C}};A.fn.fmatter.defaultFormat=function(C,B){return(A.fmatter.isValue(C)&&C!=="")?C:B.defaultValue||"&#160;"};A.fn.fmatter.email=function(C,B){if(!A.fmatter.isEmpty(C)){return'<a href="mailto:'+C+'">'+C+"</a>"}return A.fn.fmatter.defaultFormat(C,B)};A.fn.fmatter.checkbox=function(E,C){var F=A.extend({},C.checkbox),B;if(C.colModel!==undefined&&C.colModel.formatoptions!==undefined){F=A.extend({},F,C.colModel.formatoptions)}if(F.disabled===true){B='disabled="disabled"'}else{B=""}if(A.fmatter.isEmpty(E)||E===undefined){E=A.fn.fmatter.defaultFormat(E,F)}E=String(E);E=(E+"").toLowerCase();var D=E.search(/(false|f|0|no|n|off|undefined)/i)<0?" checked='checked' ":"";return'<input type="checkbox" '+D+' value="'+E+'" offval="no" '+B+"/>"};A.fn.fmatter.link=function(C,B){var D={target:B.target};var E="";if(B.colModel!==undefined&&B.colModel.formatoptions!==undefined){D=A.extend({},D,B.colModel.formatoptions)}if(D.target){E="target="+D.target}if(!A.fmatter.isEmpty(C)){return"<a "+E+' href="'+C+'">'+C+"</a>"}return A.fn.fmatter.defaultFormat(C,B)};A.fn.fmatter.showlink=function(D,C){var E={baseLinkUrl:C.baseLinkUrl,showAction:C.showAction,addParam:C.addParam||"",target:C.target,idName:C.idName},F="",B;if(C.colModel!==undefined&&C.colModel.formatoptions!==undefined){E=A.extend({},E,C.colModel.formatoptions)}if(E.target){F="target="+E.target}B=E.baseLinkUrl+E.showAction+"?"+E.idName+"="+C.rowId+E.addParam;if(A.fmatter.isString(D)||A.fmatter.isNumber(D)){return"<a "+F+' href="'+B+'">'+D+"</a>"}return A.fn.fmatter.defaultFormat(D,C)};A.fn.fmatter.integer=function(C,B){var D=A.extend({},B.integer);if(B.colModel!==undefined&&B.colModel.formatoptions!==undefined){D=A.extend({},D,B.colModel.formatoptions)}if(A.fmatter.isEmpty(C)){return D.defaultValue}return A.fmatter.util.NumberFormat(C,D)};A.fn.fmatter.number=function(C,B){var D=A.extend({},B.number);if(B.colModel!==undefined&&B.colModel.formatoptions!==undefined){D=A.extend({},D,B.colModel.formatoptions)}if(A.fmatter.isEmpty(C)){return D.defaultValue}return A.fmatter.util.NumberFormat(C,D)};A.fn.fmatter.currency=function(C,B){var D=A.extend({},B.currency);if(B.colModel!==undefined&&B.colModel.formatoptions!==undefined){D=A.extend({},D,B.colModel.formatoptions)}if(A.fmatter.isEmpty(C)){return D.defaultValue}return A.fmatter.util.NumberFormat(C,D)};A.fn.fmatter.date=function(D,C,F,B){var E=A.extend({},C.date);if(C.colModel!==undefined&&C.colModel.formatoptions!==undefined){E=A.extend({},E,C.colModel.formatoptions)}if(!E.reformatAfterEdit&&B==="edit"){return A.fn.fmatter.defaultFormat(D,C)}if(!A.fmatter.isEmpty(D)){return A.jgrid.parseDate(E.srcformat,D,E.newformat,E)}return A.fn.fmatter.defaultFormat(D,C)};A.fn.fmatter.select=function(C,I){C=String(C);var G=false,M=[],F,J;if(I.colModel.formatoptions!==undefined){G=I.colModel.formatoptions.value;F=I.colModel.formatoptions.separator===undefined?":":I.colModel.formatoptions.separator;J=I.colModel.formatoptions.delimiter===undefined?";":I.colModel.formatoptions.delimiter}else{if(I.colModel.editoptions!==undefined){G=I.colModel.editoptions.value;F=I.colModel.editoptions.separator===undefined?":":I.colModel.editoptions.separator;J=I.colModel.editoptions.delimiter===undefined?";":I.colModel.editoptions.delimiter}}if(G){var L=I.colModel.editoptions.multiple===true?true:false,D=[],E;if(L){D=C.split(",");D=A.map(D,function(N){return A.trim(N)})}if(A.fmatter.isString(G)){var K=G.split(J),H=0,B;for(B=0;B<K.length;B++){E=K[B].split(F);if(E.length>2){E[1]=A.map(E,function(O,N){if(N>0){return O}}).join(F)}if(L){if(A.inArray(E[0],D)>-1){M[H]=E[1];H++}}else{if(A.trim(E[0])===A.trim(C)){M[0]=E[1];break}}}}else{if(A.fmatter.isObject(G)){if(L){M=A.map(D,function(N){return G[N]})}else{M[0]=G[C]||""}}}}C=M.join(", ");return C===""?A.fn.fmatter.defaultFormat(C,I):C};A.fn.fmatter.rowactions=function(M){var I=A(this).closest("tr.jqgrow"),N=I.attr("id"),B=A(this).closest("table.ui-jqgrid-btable").attr("id").replace(/_frozen([^_]*)$/,"$1"),J=A("#"+B),L=J[0],C=L.p,G=C.colModel[A.jgrid.getCellIndex(this)],K=G.frozen?A("tr#"+N+" td:eq("+A.jgrid.getCellIndex(this)+") > div",J):A(this).parent(),H={extraparam:{}},F=function(P,O){if(A.isFunction(H.afterSave)){H.afterSave.call(L,P,O)}K.find("div.ui-inline-edit,div.ui-inline-del").show();K.find("div.ui-inline-save,div.ui-inline-cancel").hide()},E=function(O){if(A.isFunction(H.afterRestore)){H.afterRestore.call(L,O)}K.find("div.ui-inline-edit,div.ui-inline-del").show();K.find("div.ui-inline-save,div.ui-inline-cancel").hide()};if(G.formatoptions!==undefined){H=A.extend(H,G.formatoptions)}if(C.editOptions!==undefined){H.editOptions=C.editOptions}if(C.delOptions!==undefined){H.delOptions=C.delOptions}if(I.hasClass("jqgrid-new-row")){H.extraparam[C.prmNames.oper]=C.prmNames.addoper}var D={keys:H.keys,oneditfunc:H.onEdit,successfunc:H.onSuccess,url:H.url,extraparam:H.extraparam,aftersavefunc:F,errorfunc:H.onError,afterrestorefunc:E,restoreAfterError:H.restoreAfterError,mtype:H.mtype};switch(M){case"edit":J.jqGrid("editRow",N,D);K.find("div.ui-inline-edit,div.ui-inline-del").hide();K.find("div.ui-inline-save,div.ui-inline-cancel").show();J.triggerHandler("jqGridAfterGridComplete");break;case"save":if(J.jqGrid("saveRow",N,D)){K.find("div.ui-inline-edit,div.ui-inline-del").show();K.find("div.ui-inline-save,div.ui-inline-cancel").hide();J.triggerHandler("jqGridAfterGridComplete")}break;case"cancel":J.jqGrid("restoreRow",N,E);K.find("div.ui-inline-edit,div.ui-inline-del").show();K.find("div.ui-inline-save,div.ui-inline-cancel").hide();J.triggerHandler("jqGridAfterGridComplete");break;case"del":J.jqGrid("delGridRow",N,H.delOptions);break;case"formedit":J.jqGrid("setSelection",N);J.jqGrid("editGridRow",N,H.editOptions);break}};A.fn.fmatter.actions=function(C,B){var D={keys:false,editbutton:true,delbutton:true,editformbutton:false},G=B.rowId,E="",F;if(B.colModel.formatoptions!==undefined){D=A.extend(D,B.colModel.formatoptions)}if(G===undefined||A.fmatter.isEmpty(G)){return""}if(D.editformbutton){F="id='jEditButton_"+G+"' onclick=jQuery.fn.fmatter.rowactions.call(this,'formedit'); onmouseover=jQuery(this).addClass('ui-state-hover'); onmouseout=jQuery(this).removeClass('ui-state-hover'); ";E+="<div title='"+A.jgrid.nav.edittitle+"' style='float:left;cursor:pointer;' class='ui-pg-div ui-inline-edit' "+F+"><span class='ui-icon ui-icon-pencil'></span></div>"}else{if(D.editbutton){F="id='jEditButton_"+G+"' onclick=jQuery.fn.fmatter.rowactions.call(this,'edit'); onmouseover=jQuery(this).addClass('ui-state-hover'); onmouseout=jQuery(this).removeClass('ui-state-hover') ";E+="<div title='"+A.jgrid.nav.edittitle+"' style='float:left;cursor:pointer;' class='ui-pg-div ui-inline-edit' "+F+"><span class='ui-icon ui-icon-pencil'></span></div>"}}if(D.delbutton){F="id='jDeleteButton_"+G+"' onclick=jQuery.fn.fmatter.rowactions.call(this,'del'); onmouseover=jQuery(this).addClass('ui-state-hover'); onmouseout=jQuery(this).removeClass('ui-state-hover'); ";E+="<div title='"+A.jgrid.nav.deltitle+"' style='float:left;margin-left:5px;' class='ui-pg-div ui-inline-del' "+F+"><span class='ui-icon ui-icon-trash'></span></div>"}F="id='jSaveButton_"+G+"' onclick=jQuery.fn.fmatter.rowactions.call(this,'save'); onmouseover=jQuery(this).addClass('ui-state-hover'); onmouseout=jQuery(this).removeClass('ui-state-hover'); ";E+="<div title='"+A.jgrid.edit.bSubmit+"' style='float:left;display:none' class='ui-pg-div ui-inline-save' "+F+"><span class='ui-icon ui-icon-disk'></span></div>";F="id='jCancelButton_"+G+"' onclick=jQuery.fn.fmatter.rowactions.call(this,'cancel'); onmouseover=jQuery(this).addClass('ui-state-hover'); onmouseout=jQuery(this).removeClass('ui-state-hover'); ";E+="<div title='"+A.jgrid.edit.bCancel+"' style='float:left;display:none;margin-left:5px;' class='ui-pg-div ui-inline-cancel' "+F+"><span class='ui-icon ui-icon-cancel'></span></div>";return"<div style='margin-left:8px;'>"+E+"</div>"};A.unformat=function(C,I,J,K){var M,G=I.colModel.formatter,L=I.colModel.formatoptions||{},E,B=/([\.\*\_\'\(\)\{\}\+\?\\])/g,D=I.colModel.unformat||(A.fn.fmatter[G]&&A.fn.fmatter[G].unformat);if(D!==undefined&&A.isFunction(D)){M=D.call(this,A(C).text(),I,C)}else{if(G!==undefined&&A.fmatter.isString(G)){var H=A.jgrid.formatter||{},F;switch(G){case"integer":L=A.extend({},H.integer,L);E=L.thousandsSeparator.replace(B,"\\$1");F=new RegExp(E,"g");M=A(C).text().replace(F,"");break;case"number":L=A.extend({},H.number,L);E=L.thousandsSeparator.replace(B,"\\$1");F=new RegExp(E,"g");M=A(C).text().replace(F,"").replace(L.decimalSeparator,".");break;case"currency":L=A.extend({},H.currency,L);E=L.thousandsSeparator.replace(B,"\\$1");F=new RegExp(E,"g");M=A(C).text();if(L.prefix&&L.prefix.length){M=M.substr(L.prefix.length)}if(L.suffix&&L.suffix.length){M=M.substr(0,M.length-L.suffix.length)}M=M.replace(F,"").replace(L.decimalSeparator,".");break;case"checkbox":var N=(I.colModel.editoptions)?I.colModel.editoptions.value.split(":"):["Yes","No"];M=A("input",C).is(":checked")?N[0]:N[1];break;case"select":M=A.unformat.select(C,I,J,K);break;case"actions":return"";default:M=A(C).text()}}}return M!==undefined?M:K===true?A(C).text():A.jgrid.htmlDecode(A(C).html())};A.unformat.select=function(C,L,M,N){var P=[];var Q=A(C).text();if(N===true){return Q}var O=A.extend({},L.colModel.formatoptions!==undefined?L.colModel.formatoptions:L.colModel.editoptions),G=O.separator===undefined?":":O.separator,J=O.delimiter===undefined?";":O.delimiter;if(O.value){var H=O.value,I=O.multiple===true?true:false,E=[],F;if(I){E=Q.split(",");E=A.map(E,function(R){return A.trim(R)})}if(A.fmatter.isString(H)){var B=H.split(J),D=0,K;for(K=0;K<B.length;K++){F=B[K].split(G);if(F.length>2){F[1]=A.map(F,function(S,R){if(R>0){return S}}).join(G)}if(I){if(A.inArray(F[1],E)>-1){P[D]=F[0];D++}}else{if(A.trim(F[1])===A.trim(Q)){P[0]=F[0];break}}}}else{if(A.fmatter.isObject(H)||A.isArray(H)){if(!I){E[0]=Q}P=A.map(E,function(R){var S;A.each(H,function(T,U){if(U===R){S=T;return false}});if(S!==undefined){return S}})}}return P.join(", ")}return Q||""};A.unformat.date=function(C,B){var D=A.jgrid.formatter.date||{};if(B.formatoptions!==undefined){D=A.extend({},D,B.formatoptions)}if(!A.fmatter.isEmpty(C)){return A.jgrid.parseDate(D.newformat,C,D.srcformat,D)}return A.fn.fmatter.defaultFormat(C,B)}})(jQuery);(function(A){A.extend(A.jgrid,{showModal:function(B){B.w.show()},closeModal:function(B){B.w.hide().attr("aria-hidden","true");if(B.o){B.o.remove()}},hideModal:function(E,C){C=A.extend({jqm:true,gb:""},C||{});if(C.onClose){var D=C.gb&&typeof C.gb==="string"&&C.gb.substr(0,6)==="#gbox_"?C.onClose.call(A("#"+C.gb.substr(6))[0],E):C.onClose(E);if(typeof D==="boolean"&&!D){return}}if(A.fn.jqm&&C.jqm===true){A(E).attr("aria-hidden","true").jqmHide()}else{if(C.gb!==""){try{A(".jqgrid-overlay:first",C.gb).hide()}catch(B){}}A(E).hide().attr("aria-hidden","true")}},findPos:function(B){var C=0,D=0;if(B.offsetParent){do{C+=B.offsetLeft;D+=B.offsetTop}while(B=B.offsetParent)}return[C,D]},createModal:function(C,F,N,D,J,E,P){N=A.extend(true,{},A.jgrid.jqModal||{},N);var M=document.createElement("div"),Q,T=this;P=A.extend({},P||{});Q=A(N.gbox).attr("dir")==="rtl"?true:false;M.className="ui-widget ui-widget-content ui-corner-all ui-jqdialog";M.id=C.themodal;var I=document.createElement("div");I.className="ui-jqdialog-titlebar ui-widget-header ui-corner-all ui-helper-clearfix";I.id=C.modalhead;A(I).append("<span class='ui-jqdialog-title'>"+N.caption+"</span>");var S=A("<a class='ui-jqdialog-titlebar-close ui-corner-all'></a>").hover(function(){S.addClass("ui-state-hover")},function(){S.removeClass("ui-state-hover")}).append("<span class='ui-icon ui-icon-closethick'></span>");A(I).append(S);if(Q){M.dir="rtl";A(".ui-jqdialog-title",I).css("float","right");A(".ui-jqdialog-titlebar-close",I).css("left",0.3+"em")}else{M.dir="ltr";A(".ui-jqdialog-title",I).css("float","left");A(".ui-jqdialog-titlebar-close",I).css("right",0.3+"em")}var L=document.createElement("div");A(L).addClass("ui-jqdialog-content ui-widget-content").attr("id",C.modalcontent);A(L).append(F);M.appendChild(L);A(M).prepend(I);if(E===true){A("body").append(M)}else{if(typeof E==="string"){A(E).append(M)}else{A(M).insertBefore(D)}}A(M).css(P);if(N.jqModal===undefined){N.jqModal=true}var K={};if(A.fn.jqm&&N.jqModal===true){if(N.left===0&&N.top===0&&N.overlay){var H=[];H=A.jgrid.findPos(J);N.left=H[0]+4;N.top=H[1]+4}K.top=N.top+"px";K.left=N.left}else{if(N.left!==0||N.top!==0){K.left=N.left;K.top=N.top+"px"}}A("a.ui-jqdialog-titlebar-close",I).click(function(){var U=A("#"+A.jgrid.jqID(C.themodal)).data("onClose")||N.onClose;var V=A("#"+A.jgrid.jqID(C.themodal)).data("gbox")||N.gbox;T.hideModal("#"+A.jgrid.jqID(C.themodal),{gb:V,jqm:N.jqModal,onClose:U});return false});if(N.width===0||!N.width){N.width=300}if(N.height===0||!N.height){N.height=200}if(!N.zIndex){var R=A(D).parents("*[role=dialog]").filter(":first").css("z-index");if(R){N.zIndex=parseInt(R,10)+2}else{N.zIndex=950}}var G=0;if(Q&&K.left&&!E){G=A(N.gbox).width()-(!isNaN(N.width)?parseInt(N.width,10):0)-8;K.left=parseInt(K.left,10)+parseInt(G,10)}if(K.left){K.left+="px"}A(M).css(A.extend({width:isNaN(N.width)?"auto":N.width+"px",height:isNaN(N.height)?"auto":N.height+"px",zIndex:N.zIndex,overflow:"hidden"},K)).attr({tabIndex:"-1","role":"dialog","aria-labelledby":C.modalhead,"aria-hidden":"true"});if(N.drag===undefined){N.drag=true}if(N.resize===undefined){N.resize=true}if(N.drag){A(I).css("cursor","move");if(A.fn.jqDrag){A(M).jqDrag(I)}else{try{A(M).draggable({handle:A("#"+A.jgrid.jqID(I.id))})}catch(O){}}}if(N.resize){if(A.fn.jqResize){A(M).append("<div class='jqResize ui-resizable-handle ui-resizable-se ui-icon ui-icon-gripsmall-diagonal-se'></div>");A("#"+A.jgrid.jqID(C.themodal)).jqResize(".jqResize",C.scrollelm?"#"+A.jgrid.jqID(C.scrollelm):false)}else{try{A(M).resizable({handles:"se, sw",alsoResize:C.scrollelm?"#"+A.jgrid.jqID(C.scrollelm):false})}catch(B){}}}if(N.closeOnEscape===true){A(M).keydown(function(U){if(U.which==27){var V=A("#"+A.jgrid.jqID(C.themodal)).data("onClose")||N.onClose;T.hideModal("#"+A.jgrid.jqID(C.themodal),{gb:N.gbox,jqm:N.jqModal,onClose:V})}})}},viewModal:function(C,B){B=A.extend({toTop:true,overlay:10,modal:false,overlayClass:"ui-widget-overlay",onShow:A.jgrid.showModal,onHide:A.jgrid.closeModal,gbox:"",jqm:true,jqM:true},B||{});if(A.fn.jqm&&B.jqm===true){if(B.jqM){A(C).attr("aria-hidden","false").jqm(B).jqmShow()}else{A(C).attr("aria-hidden","false").jqmShow()}}else{if(B.gbox!==""){A(".jqgrid-overlay:first",B.gbox).show();A(C).data("gbox",B.gbox)}A(C).show().attr("aria-hidden","false");try{A(":input:visible",C)[0].focus()}catch(D){}}},info_dialog:function(F,O,D,M){var C={width:290,height:"auto",dataheight:"auto",drag:true,resize:false,left:250,top:170,zIndex:1000,jqModal:true,modal:false,closeOnEscape:true,align:"center",buttonalign:"center",buttons:[]};A.extend(true,C,A.jgrid.jqModal||{},{caption:"<b>"+F+"</b>"},M||{});var K=C.jqModal,G=this;if(A.fn.jqm&&!K){K=false}var E="",H;if(C.buttons.length>0){for(H=0;H<C.buttons.length;H++){if(C.buttons[H].id===undefined){C.buttons[H].id="info_button_"+H}E+="<a id='"+C.buttons[H].id+"' class='fm-button ui-state-default ui-corner-all'>"+C.buttons[H].text+"</a>"}}var L=isNaN(C.dataheight)?C.dataheight:C.dataheight+"px",I="text-align:"+C.align+";";var J="<div id='info_id'>";J+="<div id='infocnt' style='margin:0px;padding-bottom:1em;width:100%;overflow:auto;position:relative;height:"+L+";"+I+"'>"+O+"</div>";J+=D?"<div class='ui-widget-content ui-helper-clearfix' style='text-align:"+C.buttonalign+";padding-bottom:0.8em;padding-top:0.5em;background-image: none;border-width: 1px 0 0 0;'><a id='closedialog' class='fm-button ui-state-default ui-corner-all'>"+D+"</a>"+E+"</div>":E!==""?"<div class='ui-widget-content ui-helper-clearfix' style='text-align:"+C.buttonalign+";padding-bottom:0.8em;padding-top:0.5em;background-image: none;border-width: 1px 0 0 0;'>"+E+"</div>":"";J+="</div>";try{if(A("#info_dialog").attr("aria-hidden")==="false"){A.jgrid.hideModal("#info_dialog",{jqm:K})}A("#info_dialog").remove()}catch(B){}A.jgrid.createModal({themodal:"info_dialog",modalhead:"info_head",modalcontent:"info_content",scrollelm:"infocnt"},J,C,"","",true);if(E){A.each(C.buttons,function(P){A("#"+A.jgrid.jqID(this.id),"#info_id").bind("click",function(){C.buttons[P].onClick.call(A("#info_dialog"));return false})})}A("#closedialog","#info_id").click(function(){G.hideModal("#info_dialog",{jqm:K,onClose:A("#info_dialog").data("onClose")||C.onClose,gb:A("#info_dialog").data("gbox")||C.gbox});return false});A(".fm-button","#info_dialog").hover(function(){A(this).addClass("ui-state-hover")},function(){A(this).removeClass("ui-state-hover")});if(A.isFunction(C.beforeOpen)){C.beforeOpen()}A.jgrid.viewModal("#info_dialog",{onHide:function(P){P.w.hide().remove();if(P.o){P.o.remove()}},modal:C.modal,jqm:K});if(A.isFunction(C.afterOpen)){C.afterOpen()}try{A("#info_dialog").focus()}catch(N){}},bindEv:function(B,C){var D=this;if(A.isFunction(C.dataInit)){C.dataInit.call(D,B,C)}if(C.dataEvents){A.each(C.dataEvents,function(){if(this.data!==undefined){A(B).bind(this.type,this.data,this.fn)}else{A(B).bind(this.type,this.fn)}})}},createEl:function(Q,U,P,L,V){var G="",R=this;function M(a,d,c){var b=["dataInit","dataEvents","dataUrl","buildSelect","sopt","searchhidden","defaultValue","attr","custom_element","custom_value"];if(c!==undefined&&A.isArray(c)){A.merge(b,c)}A.each(d,function(e,f){if(A.inArray(e,b)===-1){A(a).attr(e,f)}});if(!d.hasOwnProperty("id")){A(a).attr("id",A.jgrid.randId())}}switch(Q){case"textarea":G=document.createElement("textarea");if(L){if(!U.cols){A(G).css({width:"98%"})}}else{if(!U.cols){U.cols=20}}if(!U.rows){U.rows=2}if(P==="&nbsp;"||P==="&#160;"||(P.length===1&&P.charCodeAt(0)===160)){P=""}G.value=P;M(G,U);A(G).attr({"role":"textbox","multiline":"true"});break;case"checkbox":G=document.createElement("input");G.type="checkbox";if(!U.value){var N=(P+"").toLowerCase();if(N.search(/(false|f|0|no|n|off|undefined)/i)<0&&N!==""){G.checked=true;G.defaultChecked=true;G.value=P}else{G.value="on"}A(G).attr("offval","off")}else{var J=U.value.split(":");if(P===J[0]){G.checked=true;G.defaultChecked=true}G.value=J[0];A(G).attr("offval",J[1])}M(G,U,["value"]);A(G).attr("role","checkbox");break;case"select":G=document.createElement("select");G.setAttribute("role","select");var O,F=[];if(U.multiple===true){O=true;G.multiple="multiple";A(G).attr("aria-multiselectable","true")}else{O=false}if(U.dataUrl!==undefined){var S=U.name?String(U.id).substring(0,String(U.id).length-String(U.name).length-1):String(U.id),C=U.postData||V.postData;if(R.p&&R.p.idPrefix){S=A.jgrid.stripPref(R.p.idPrefix,S)}A.ajax(A.extend({url:A.isFunction(U.dataUrl)?U.dataUrl.call(R,S,P,String(U.name)):U.dataUrl,type:"GET",dataType:"html",data:A.isFunction(C)?C.call(R,S,P,String(U.name)):C,context:{elem:G,options:U,vl:P},success:function(h){var g=[],e=this.elem,c=this.vl,d=A.extend({},this.options),f=d.multiple===true,b=A.isFunction(d.buildSelect)?d.buildSelect.call(R,h):h;if(typeof b==="string"){b=A(A.trim(b)).html()}if(b){A(e).append(b);M(e,d,C?["postData"]:undefined);if(d.size===undefined){d.size=f?3:1}if(f){g=c.split(",");g=A.map(g,function(a){return A.trim(a)})}else{g[0]=A.trim(c)}setTimeout(function(){A("option",e).each(function(a){if(a===0&&e.multiple){this.selected=false}A(this).attr("role","option");if(A.inArray(A.trim(A(this).text()),g)>-1||A.inArray(A.trim(A(this).val()),g)>-1){this.selected="selected"}})},0)}}},V||{}))}else{if(U.value){var H;if(U.size===undefined){U.size=O?3:1}if(O){F=P.split(",");F=A.map(F,function(a){return A.trim(a)})}if(typeof U.value==="function"){U.value=U.value()}var Z,W,B,E=U.separator===undefined?":":U.separator,X=U.delimiter===undefined?";":U.delimiter;if(typeof U.value==="string"){Z=U.value.split(X);for(H=0;H<Z.length;H++){W=Z[H].split(E);if(W.length>2){W[1]=A.map(W,function(a,b){if(b>0){return a}}).join(E)}B=document.createElement("option");B.setAttribute("role","option");B.value=W[0];B.innerHTML=W[1];G.appendChild(B);if(!O&&(A.trim(W[0])===A.trim(P)||A.trim(W[1])===A.trim(P))){B.selected="selected"}if(O&&(A.inArray(A.trim(W[1]),F)>-1||A.inArray(A.trim(W[0]),F)>-1)){B.selected="selected"}}}else{if(typeof U.value==="object"){var K=U.value,I;for(I in K){if(K.hasOwnProperty(I)){B=document.createElement("option");B.setAttribute("role","option");B.value=I;B.innerHTML=K[I];G.appendChild(B);if(!O&&(A.trim(I)===A.trim(P)||A.trim(K[I])===A.trim(P))){B.selected="selected"}if(O&&(A.inArray(A.trim(K[I]),F)>-1||A.inArray(A.trim(I),F)>-1)){B.selected="selected"}}}}}M(G,U,["value"])}}break;case"text":case"password":case"button":var T;if(Q==="button"){T="button"}else{T="textbox"}G=document.createElement("input");G.type=Q;G.value=P;M(G,U);if(Q!=="button"){if(L){if(!U.size){A(G).css({width:"98%"})}}else{if(!U.size){U.size=20}}}A(G).attr("role",T);break;case"image":case"file":G=document.createElement("input");G.type=Q;M(G,U);break;case"custom":G=document.createElement("span");try{if(A.isFunction(U.custom_element)){var Y=U.custom_element.call(R,P,U);if(Y){Y=A(Y).addClass("customelement").attr({id:U.id,name:U.name});A(G).empty().append(Y)}else{throw"e2"}}else{throw"e1"}}catch(D){if(D==="e1"){A.jgrid.info_dialog(A.jgrid.errors.errcap,"function 'custom_element' "+A.jgrid.edit.msg.nodefined,A.jgrid.edit.bClose)}if(D==="e2"){A.jgrid.info_dialog(A.jgrid.errors.errcap,"function 'custom_element' "+A.jgrid.edit.msg.novalue,A.jgrid.edit.bClose)}else{A.jgrid.info_dialog(A.jgrid.errors.errcap,typeof D==="string"?D:D.message,A.jgrid.edit.bClose)}}break}return G},checkDate:function(E,N){var J=function(O){return(((O%4===0)&&(O%100!==0||(O%400===0)))?29:28)},D={},L;E=E.toLowerCase();if(E.indexOf("/")!==-1){L="/"}else{if(E.indexOf("-")!==-1){L="-"}else{if(E.indexOf(".")!==-1){L="."}else{L="/"}}}E=E.split(L);N=N.split(L);if(N.length!==3){return false}var G=-1,H,M=-1,C=-1,I;for(I=0;I<E.length;I++){var F=isNaN(N[I])?0:parseInt(N[I],10);D[E[I]]=F;H=E[I];if(H.indexOf("y")!==-1){G=I}if(H.indexOf("m")!==-1){C=I}if(H.indexOf("d")!==-1){M=I}}if(E[G]==="y"||E[G]==="yyyy"){H=4}else{if(E[G]==="yy"){H=2}else{H=-1}}var B=[0,31,29,31,30,31,30,31,31,30,31,30,31],K;if(G===-1){return false}K=D[E[G]].toString();if(H===2&&K.length===1){H=1}if(K.length!==H||(D[E[G]]===0&&N[G]!=="00")){return false}if(C===-1){return false}K=D[E[C]].toString();if(K.length<1||D[E[C]]<1||D[E[C]]>12){return false}if(M===-1){return false}K=D[E[M]].toString();if(K.length<1||D[E[M]]<1||D[E[M]]>31||(D[E[C]]===2&&D[E[M]]>J(D[E[G]]))||D[E[M]]>B[D[E[C]]]){return false}return true},isEmpty:function(B){if(B.match(/^\s+$/)||B===""){return true}return false},checkTime:function(C){var B=/^(\d{1,2}):(\d{2})([apAP][Mm])?$/,D;if(!A.jgrid.isEmpty(C)){D=C.match(B);if(D){if(D[3]){if(D[1]<1||D[1]>12){return false}}else{if(D[1]>23){return false}}if(D[2]>59){return false}}else{return false}}return true},checkValues:function(I,E,M,L){var F,J,D,G,C,O=this,K=O.p.colModel;if(M===undefined){if(typeof E==="string"){for(J=0,C=K.length;J<C;J++){if(K[J].name===E){F=K[J].editrules;E=J;if(K[J].formoptions!=null){D=K[J].formoptions.label}break}}}else{if(E>=0){F=K[E].editrules}}}else{F=M;D=L===undefined?"_":L}if(F){if(!D){D=O.p.colNames!=null?O.p.colNames[E]:K[E].label}if(F.required===true){if(A.jgrid.isEmpty(I)){return[false,D+": "+A.jgrid.edit.msg.required,""]}}var H=F.required===false?false:true;if(F.number===true){if(!(H===false&&A.jgrid.isEmpty(I))){if(isNaN(I)){return[false,D+": "+A.jgrid.edit.msg.number,""]}}}if(F.minValue!==undefined&&!isNaN(F.minValue)){if(parseFloat(I)<parseFloat(F.minValue)){return[false,D+": "+A.jgrid.edit.msg.minValue+" "+F.minValue,""]}}if(F.maxValue!==undefined&&!isNaN(F.maxValue)){if(parseFloat(I)>parseFloat(F.maxValue)){return[false,D+": "+A.jgrid.edit.msg.maxValue+" "+F.maxValue,""]}}var B;if(F.email===true){if(!(H===false&&A.jgrid.isEmpty(I))){B=/^((([a-z]|\d|[!#\$%&'\*\+\-\/=\?\^_`{\|}~]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])+(\.([a-z]|\d|[!#\$%&'\*\+\-\/=\?\^_`{\|}~]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])+)*)|((\x22)((((\x20|\x09)*(\x0d\x0a))?(\x20|\x09)+)?(([\x01-\x08\x0b\x0c\x0e-\x1f\x7f]|\x21|[\x23-\x5b]|[\x5d-\x7e]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(\\([\x01-\x09\x0b\x0c\x0d-\x7f]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]))))*(((\x20|\x09)*(\x0d\x0a))?(\x20|\x09)+)?(\x22)))@((([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.)+(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.?$/i;if(!B.test(I)){return[false,D+": "+A.jgrid.edit.msg.email,""]}}}if(F.integer===true){if(!(H===false&&A.jgrid.isEmpty(I))){if(isNaN(I)){return[false,D+": "+A.jgrid.edit.msg.integer,""]}if((I%1!==0)||(I.indexOf(".")!==-1)){return[false,D+": "+A.jgrid.edit.msg.integer,""]}}}if(F.date===true){if(!(H===false&&A.jgrid.isEmpty(I))){if(K[E].formatoptions&&K[E].formatoptions.newformat){G=K[E].formatoptions.newformat;if(A.jgrid.formatter.date.masks.hasOwnProperty(G)){G=A.jgrid.formatter.date.masks[G]}}else{G=K[E].datefmt||"Y-m-d"}if(!A.jgrid.checkDate(G,I)){return[false,D+": "+A.jgrid.edit.msg.date+" - "+G,""]}}}if(F.time===true){if(!(H===false&&A.jgrid.isEmpty(I))){if(!A.jgrid.checkTime(I)){return[false,D+": "+A.jgrid.edit.msg.date+" - hh:mm (am/pm)",""]}}}if(F.url===true){if(!(H===false&&A.jgrid.isEmpty(I))){B=/^(((https?)|(ftp)):\/\/([\-\w]+\.)+\w{2,3}(\/[%\-\w]+(\.\w{2,})?)*(([\w\-\.\?\\\/+@&#;`~=%!]*)(\.\w{2,})?)*\/?)/i;if(!B.test(I)){return[false,D+": "+A.jgrid.edit.msg.url,""]}}}if(F.custom===true){if(!(H===false&&A.jgrid.isEmpty(I))){if(A.isFunction(F.custom_func)){var N=F.custom_func.call(O,I,D,E);return A.isArray(N)?N:[false,A.jgrid.edit.msg.customarray,""]}return[false,A.jgrid.edit.msg.customfcheck,""]}}}return[true,"",""]}})})(jQuery);(function(A){A.fn.jqFilter=function(C){if(typeof C==="string"){var D=A.fn.jqFilter[C];if(!D){throw ("jqFilter - No such method: "+C)}var E=A.makeArray(arguments).slice(1);return D.apply(this,E)}var B=A.extend(true,{filter:null,columns:[],onChange:null,afterRedraw:null,checkValues:null,error:false,errmsg:"",errorcheck:true,showQuery:true,sopt:null,ops:[],operands:null,numopts:["eq","ne","lt","le","gt","ge","nu","nn","in","ni"],stropts:["eq","ne","bw","bn","ew","en","cn","nc","nu","nn","in","ni"],strarr:["text","string","blob"],groupOps:[{op:"AND",text:"AND"},{op:"OR",text:"OR"}],groupButton:true,ruleButtons:true,direction:"ltr"},A.jgrid.filter,C||{});return this.each(function(){if(this.filter){return}this.p=B;if(this.p.filter===null||this.p.filter===undefined){this.p.filter={groupOp:this.p.groupOps[0].op,rules:[],groups:[]}}var F,I=this.p.columns.length,H,K=/msie/i.test(navigator.userAgent)&&!window.opera;this.p.initFilter=A.extend(true,{},this.p.filter);if(!I){return}for(F=0;F<I;F++){H=this.p.columns[F];if(H.stype){H.inputtype=H.stype}else{if(!H.inputtype){H.inputtype="text"}}if(H.sorttype){H.searchtype=H.sorttype}else{if(!H.searchtype){H.searchtype="string"}}if(H.hidden===undefined){H.hidden=false}if(!H.label){H.label=H.name}if(H.index){H.name=H.index}if(!H.hasOwnProperty("searchoptions")){H.searchoptions={}}if(!H.hasOwnProperty("searchrules")){H.searchrules={}}}if(this.p.showQuery){A(this).append("<table class='queryresult ui-widget ui-widget-content' style='display:block;max-width:440px;border:0px none;' dir='"+this.p.direction+"'><tbody><tr><td class='query'></td></tr></tbody></table>")}var J=function(){return A("#"+A.jgrid.jqID(B.id))[0]||null};var G=function(O,L){var N=[true,""],P=J();if(A.isFunction(L.searchrules)){N=L.searchrules.call(P,O,L)}else{if(A.jgrid&&A.jgrid.checkValues){try{N=A.jgrid.checkValues.call(P,O,-1,L.searchrules,L.label)}catch(M){}}}if(N&&N.length&&N[0]===false){B.error=!N[0];B.errmsg=N[1]}};this.onchange=function(){this.p.error=false;this.p.errmsg="";return A.isFunction(this.p.onChange)?this.p.onChange.call(this,this.p):false};this.reDraw=function(){A("table.group:first",this).remove();var L=this.createTableForGroup(B.filter,null);A(this).append(L);if(A.isFunction(this.p.afterRedraw)){this.p.afterRedraw.call(this,this.p)}};this.createTableForGroup=function(W,c){var a=this,O;var N=A("<table class='group ui-widget ui-widget-content' style='border:0px none;'><tbody></tbody></table>"),M="left";if(this.p.direction==="rtl"){M="right";N.attr("dir","rtl")}if(c===null){N.append("<tr class='error' style='display:none;'><th colspan='5' class='ui-state-error' align='"+M+"'></th></tr>")}var V=A("<tr></tr>");N.append(V);var Z=A("<th colspan='5' align='"+M+"'></th>");V.append(Z);if(this.p.ruleButtons===true){var U=A("<select class='opsel'></select>");Z.append(U);var L="",S;for(O=0;O<B.groupOps.length;O++){S=W.groupOp===a.p.groupOps[O].op?" selected='selected'":"";L+="<option value='"+a.p.groupOps[O].op+"'"+S+">"+a.p.groupOps[O].text+"</option>"}U.append(L).bind("change",function(){W.groupOp=A(U).val();a.onchange()})}var b="<span></span>";if(this.p.groupButton){b=A("<input type='button' value='+ {}' title='Add subgroup' class='add-group'/>");b.bind("click",function(){if(W.groups===undefined){W.groups=[]}W.groups.push({groupOp:B.groupOps[0].op,rules:[],groups:[]});a.reDraw();a.onchange();return false})}Z.append(b);if(this.p.ruleButtons===true){var Q=A("<input type='button' value='+' title='Add rule' class='add-rule ui-add'/>"),X;Q.bind("click",function(){if(W.rules===undefined){W.rules=[]}for(O=0;O<a.p.columns.length;O++){var d=(a.p.columns[O].search===undefined)?true:a.p.columns[O].search,g=(a.p.columns[O].hidden===true),e=(a.p.columns[O].searchoptions.searchhidden===true);if((e&&d)||(d&&!g)){X=a.p.columns[O];break}}var f;if(X.searchoptions.sopt){f=X.searchoptions.sopt}else{if(a.p.sopt){f=a.p.sopt}else{if(A.inArray(X.searchtype,a.p.strarr)!==-1){f=a.p.stropts}else{f=a.p.numopts}}}W.rules.push({field:X.name,op:f[0],data:""});a.reDraw();return false});Z.append(Q)}if(c!==null){var P=A("<input type='button' value='-' title='Delete group' class='delete-group'/>");Z.append(P);P.bind("click",function(){for(O=0;O<c.groups.length;O++){if(c.groups[O]===W){c.groups.splice(O,1);break}}a.reDraw();a.onchange();return false})}if(W.groups!==undefined){for(O=0;O<W.groups.length;O++){var Y=A("<tr></tr>");N.append(Y);var T=A("<td class='first'></td>");Y.append(T);var R=A("<td colspan='4'></td>");R.append(this.createTableForGroup(W.groups[O],W));Y.append(R)}}if(W.groupOp===undefined){W.groupOp=a.p.groupOps[0].op}if(W.rules!==undefined){for(O=0;O<W.rules.length;O++){N.append(this.createTableRowForRule(W.rules[O],W))}}return N};this.createTableRowForRule=function(N,b){var g=this,Z=J(),a=A("<tr></tr>"),S,d,V,c,L="",W;a.append("<td class='first'></td>");var M=A("<td class='columns'></td>");a.append(M);var k=A("<select></select>"),X,U=[];M.append(k);k.bind("change",function(){N.field=A(k).val();V=A(this).parents("tr:first");for(S=0;S<g.p.columns.length;S++){if(g.p.columns[S].name===N.field){c=g.p.columns[S];break}}if(!c){return}c.searchoptions.id=A.jgrid.randId();if(K&&c.inputtype==="text"){if(!c.searchoptions.size){c.searchoptions.size=10}}var j=A.jgrid.createEl.call(Z,c.inputtype,c.searchoptions,"",true,g.p.ajaxSelectOptions||{},true);A(j).addClass("input-elm");if(c.searchoptions.sopt){d=c.searchoptions.sopt}else{if(g.p.sopt){d=g.p.sopt}else{if(A.inArray(c.searchtype,g.p.strarr)!==-1){d=g.p.stropts}else{d=g.p.numopts}}}var m="",n=0;U=[];A.each(g.p.ops,function(){U.push(this.oper)});for(S=0;S<d.length;S++){X=A.inArray(d[S],U);if(X!==-1){if(n===0){N.op=g.p.ops[X].oper}m+="<option value='"+g.p.ops[X].oper+"'>"+g.p.ops[X].text+"</option>";n++}}A(".selectopts",V).empty().append(m);A(".selectopts",V)[0].selectedIndex=0;if(A.jgrid.msie&&A.jgrid.msiever()<9){var i=parseInt(A("select.selectopts",V)[0].offsetWidth,10)+1;A(".selectopts",V).width(i);A(".selectopts",V).css("width","auto")}A(".data",V).empty().append(j);A.jgrid.bindEv.call(Z,j,c.searchoptions);A(".input-elm",V).bind("change",function(o){var p=o.target;N.data=p.nodeName.toUpperCase()==="SPAN"&&c.searchoptions&&A.isFunction(c.searchoptions.custom_value)?c.searchoptions.custom_value.call(Z,A(p).children(".customelement:first"),"get"):p.value;g.onchange()});setTimeout(function(){N.data=A(j).val();g.onchange()},0)});var Q=0;for(S=0;S<g.p.columns.length;S++){var O=(g.p.columns[S].search===undefined)?true:g.p.columns[S].search,h=(g.p.columns[S].hidden===true),e=(g.p.columns[S].searchoptions.searchhidden===true);if((e&&O)||(O&&!h)){W="";if(N.field===g.p.columns[S].name){W=" selected='selected'";Q=S}L+="<option value='"+g.p.columns[S].name+"'"+W+">"+g.p.columns[S].label+"</option>"}}k.append(L);var T=A("<td class='operators'></td>");a.append(T);c=B.columns[Q];c.searchoptions.id=A.jgrid.randId();if(K&&c.inputtype==="text"){if(!c.searchoptions.size){c.searchoptions.size=10}}var l=A.jgrid.createEl.call(Z,c.inputtype,c.searchoptions,N.data,true,g.p.ajaxSelectOptions||{},true);if(N.op==="nu"||N.op==="nn"){A(l).attr("readonly","true");A(l).attr("disabled","true")}var P=A("<select class='selectopts'></select>");T.append(P);P.bind("change",function(){N.op=A(P).val();V=A(this).parents("tr:first");var i=A(".input-elm",V)[0];if(N.op==="nu"||N.op==="nn"){N.data="";if(i.tagName.toUpperCase()!=="SELECT"){i.value=""}i.setAttribute("readonly","true");i.setAttribute("disabled","true")}else{if(i.tagName.toUpperCase()==="SELECT"){N.data=i.value}i.removeAttribute("readonly");i.removeAttribute("disabled")}g.onchange()});if(c.searchoptions.sopt){d=c.searchoptions.sopt}else{if(g.p.sopt){d=g.p.sopt}else{if(A.inArray(c.searchtype,g.p.strarr)!==-1){d=g.p.stropts}else{d=g.p.numopts}}}L="";A.each(g.p.ops,function(){U.push(this.oper)});for(S=0;S<d.length;S++){X=A.inArray(d[S],U);if(X!==-1){W=N.op===g.p.ops[X].oper?" selected='selected'":"";L+="<option value='"+g.p.ops[X].oper+"'"+W+">"+g.p.ops[X].text+"</option>"}}P.append(L);var Y=A("<td class='data'></td>");a.append(Y);Y.append(l);A.jgrid.bindEv.call(Z,l,c.searchoptions);A(l).addClass("input-elm").bind("change",function(){N.data=c.inputtype==="custom"?c.searchoptions.custom_value.call(Z,A(this).children(".customelement:first"),"get"):A(this).val();g.onchange()});var R=A("<td></td>");a.append(R);if(this.p.ruleButtons===true){var f=A("<input type='button' value='-' title='Delete rule' class='delete-rule ui-del'/>");R.append(f);f.bind("click",function(){for(S=0;S<b.rules.length;S++){if(b.rules[S]===N){b.rules.splice(S,1);break}}g.reDraw();g.onchange();return false})}return a};this.getStringForGroup=function(L){var M="(",P;if(L.groups!==undefined){for(P=0;P<L.groups.length;P++){if(M.length>1){M+=" "+L.groupOp+" "}try{M+=this.getStringForGroup(L.groups[P])}catch(O){alert(O)}}}if(L.rules!==undefined){try{for(P=0;P<L.rules.length;P++){if(M.length>1){M+=" "+L.groupOp+" "}M+=this.getStringForRule(L.rules[P])}}catch(N){alert(N)}}M+=")";if(M==="()"){return""}return M};this.getStringForRule=function(O){var P="",L="",Q,M,R,N,S=["int","integer","float","number","currency"];for(Q=0;Q<this.p.ops.length;Q++){if(this.p.ops[Q].oper===O.op){P=this.p.operands.hasOwnProperty(O.op)?this.p.operands[O.op]:"";L=this.p.ops[Q].oper;break}}for(Q=0;Q<this.p.columns.length;Q++){if(this.p.columns[Q].name===O.field){M=this.p.columns[Q];break}}if(M==undefined){return""}N=O.data;if(L==="bw"||L==="bn"){N=N+"%"}if(L==="ew"||L==="en"){N="%"+N}if(L==="cn"||L==="nc"){N="%"+N+"%"}if(L==="in"||L==="ni"){N=" ("+N+")"}if(B.errorcheck){G(O.data,M)}if(A.inArray(M.searchtype,S)!==-1||L==="nn"||L==="nu"){R=O.field+" "+P+" "+N}else{R=O.field+" "+P+' "'+N+'"'}return R};this.resetFilter=function(){this.p.filter=A.extend(true,{},this.p.initFilter);this.reDraw();this.onchange()};this.hideError=function(){A("th.ui-state-error",this).html("");A("tr.error",this).hide()};this.showError=function(){A("th.ui-state-error",this).html(this.p.errmsg);A("tr.error",this).show()};this.toUserFriendlyString=function(){return this.getStringForGroup(B.filter)};this.toString=function(){var M=this;function L(P){if(M.p.errorcheck){var O,Q;for(O=0;O<M.p.columns.length;O++){if(M.p.columns[O].name===P.field){Q=M.p.columns[O];break}}if(Q){G(P.data,Q)}}return P.op+"(item."+P.field+",'"+P.data+"')"}function N(O){var P="(",Q;if(O.groups!==undefined){for(Q=0;Q<O.groups.length;Q++){if(P.length>1){if(O.groupOp==="OR"){P+=" || "}else{P+=" && "}}P+=N(O.groups[Q])}}if(O.rules!==undefined){for(Q=0;Q<O.rules.length;Q++){if(P.length>1){if(O.groupOp==="OR"){P+=" || "}else{P+=" && "}}P+=L(O.rules[Q])}}P+=")";if(P==="()"){return""}return P}return N(this.p.filter)};this.reDraw();if(this.p.showQuery){this.onchange()}this.filter=true})};A.extend(A.fn.jqFilter,{toSQLString:function(){var B="";this.each(function(){B=this.toUserFriendlyString()});return B},filterData:function(){var B;this.each(function(){B=this.p.filter});return B},getParameter:function(B){if(B!==undefined){if(this.p.hasOwnProperty(B)){return this.p[B]}}return this.p},resetFilter:function(){return this.each(function(){this.resetFilter()})},addFilter:function(B){if(typeof B==="string"){B=A.jgrid.parse(B)}this.each(function(){this.p.filter=B;this.reDraw();this.onchange()})}})})(jQuery);(function(B){var A={};B.jgrid.extend({searchGrid:function(C){C=B.extend(true,{recreateFilter:false,drag:true,sField:"searchField",sValue:"searchString",sOper:"searchOper",sFilter:"filters",loadDefaults:true,beforeShowSearch:null,afterShowSearch:null,onInitializeSearch:null,afterRedraw:null,afterChange:null,closeAfterSearch:false,closeAfterReset:false,closeOnEscape:false,searchOnEnter:false,multipleSearch:false,multipleGroup:false,top:0,left:0,jqModal:true,modal:false,resize:true,width:450,height:"auto",dataheight:"auto",showQuery:false,errorcheck:true,sopt:null,stringResult:undefined,onClose:null,onSearch:null,onReset:null,toTop:true,overlay:30,columns:[],tmplNames:null,tmplFilters:null,tmplLabel:" Template: ",showOnLoad:false,layer:null,operands:{"eq":"=","ne":"<>","lt":"<","le":"<=","gt":">","ge":">=","bw":"LIKE","bn":"NOT LIKE","in":"IN","ni":"NOT IN","ew":"LIKE","en":"NOT LIKE","cn":"LIKE","nc":"NOT LIKE","nu":"IS NULL","nn":"ISNOT NULL"}},B.jgrid.search,C||{});return this.each(function(){var S=this;if(!S.grid){return}var O="fbox_"+S.p.id,E=true,I=true,R={themodal:"searchmod"+O,modalhead:"searchhd"+O,modalcontent:"searchcnt"+O,scrollelm:O},D=S.p.postData[C.sFilter];if(typeof D==="string"){D=B.jgrid.parse(D)}if(C.recreateFilter===true){B("#"+B.jgrid.jqID(R.themodal)).remove()}function H(X){E=B(S).triggerHandler("jqGridFilterBeforeShow",[X]);if(E===undefined){E=true}if(E&&B.isFunction(C.beforeShowSearch)){E=C.beforeShowSearch.call(S,X)}if(E){B.jgrid.viewModal("#"+B.jgrid.jqID(R.themodal),{gbox:"#gbox_"+B.jgrid.jqID(O),jqm:C.jqModal,modal:C.modal,overlay:C.overlay,toTop:C.toTop});B(S).triggerHandler("jqGridFilterAfterShow",[X]);if(B.isFunction(C.afterShowSearch)){C.afterShowSearch.call(S,X)}}}if(B("#"+B.jgrid.jqID(R.themodal))[0]!==undefined){H(B("#fbox_"+B.jgrid.jqID(+S.p.id)))}else{var M=B("<div><div id='"+O+"' class='searchFilter' style='overflow:auto'></div></div>").insertBefore("#gview_"+B.jgrid.jqID(S.p.id)),K="left",U="";if(S.p.direction==="rtl"){K="right";U=" style='text-align:left'";M.attr("dir","rtl")}var P=B.extend([],S.p.colModel),N="<a id='"+O+"_search' class='fm-button ui-state-default ui-corner-all fm-button-icon-right ui-reset'><span class='ui-icon ui-icon-search'></span>"+C.Find+"</a>",F="<a id='"+O+"_reset' class='fm-button ui-state-default ui-corner-all fm-button-icon-left ui-search'><span class='ui-icon ui-icon-arrowreturnthick-1-w'></span>"+C.Reset+"</a>",T="",L="",W,Q=false,G,J=-1;if(C.showQuery){T="<a id='"+O+"_query' class='fm-button ui-state-default ui-corner-all fm-button-icon-left'><span class='ui-icon ui-icon-comment'></span>Query</a>"}if(!C.columns.length){B.each(P,function(X,Y){if(!Y.label){Y.label=S.p.colNames[X]}if(!Q){var Z=(Y.search===undefined)?true:Y.search,b=(Y.hidden===true),a=(Y.searchoptions&&Y.searchoptions.searchhidden===true);if((a&&Z)||(Z&&!b)){Q=true;W=Y.index||Y.name;J=X}}})}else{P=C.columns;J=0;W=P[0].index||P[0].name}if((!D&&W)||C.multipleSearch===false){var V="eq";if(J>=0&&P[J].searchoptions&&P[J].searchoptions.sopt){V=P[J].searchoptions.sopt[0]}else{if(C.sopt&&C.sopt.length){V=C.sopt[0]}}D={groupOp:"AND",rules:[{field:W,op:V,data:""}]}}Q=false;if(C.tmplNames&&C.tmplNames.length){Q=true;L=C.tmplLabel;L+="<select class='ui-template'>";L+="<option value='default'>Default</option>";B.each(C.tmplNames,function(X,Y){L+="<option value='"+X+"'>"+Y+"</option>"});L+="</select>"}G="<table class='EditTable' style='border:0px none;margin-top:5px' id='"+O+"_2'><tbody><tr><td colspan='2'><hr class='ui-widget-content' style='margin:1px'/></td></tr><tr><td class='EditButton' style='text-align:"+K+"'>"+F+L+"</td><td class='EditButton' "+U+">"+T+N+"</td></tr></tbody></table>";O=B.jgrid.jqID(O);B("#"+O).jqFilter({columns:P,filter:C.loadDefaults?D:null,showQuery:C.showQuery,errorcheck:C.errorcheck,sopt:C.sopt,groupButton:C.multipleGroup,ruleButtons:C.multipleSearch,afterRedraw:C.afterRedraw,ops:C.odata,operands:C.operands,ajaxSelectOptions:S.p.ajaxSelectOptions,groupOps:C.groupOps,onChange:function(){if(this.p.showQuery){B(".query",this).html(this.toUserFriendlyString())}if(B.isFunction(C.afterChange)){C.afterChange.call(S,B("#"+O),C)}},direction:S.p.direction,id:S.p.id});M.append(G);if(Q&&C.tmplFilters&&C.tmplFilters.length){B(".ui-template",M).bind("change",function(){var X=B(this).val();if(X==="default"){B("#"+O).jqFilter("addFilter",D)}else{B("#"+O).jqFilter("addFilter",C.tmplFilters[parseInt(X,10)])}return false})}if(C.multipleGroup===true){C.multipleSearch=true}B(S).triggerHandler("jqGridFilterInitialize",[B("#"+O)]);if(B.isFunction(C.onInitializeSearch)){C.onInitializeSearch.call(S,B("#"+O))}C.gbox="#gbox_"+O;if(C.layer){B.jgrid.createModal(R,M,C,"#gview_"+B.jgrid.jqID(S.p.id),B("#gbox_"+B.jgrid.jqID(S.p.id))[0],"#"+B.jgrid.jqID(C.layer),{position:"relative"})}else{B.jgrid.createModal(R,M,C,"#gview_"+B.jgrid.jqID(S.p.id),B("#gbox_"+B.jgrid.jqID(S.p.id))[0])}if(C.searchOnEnter||C.closeOnEscape){B("#"+B.jgrid.jqID(R.themodal)).keydown(function(X){var Y=B(X.target);if(C.searchOnEnter&&X.which===13&&!Y.hasClass("add-group")&&!Y.hasClass("add-rule")&&!Y.hasClass("delete-group")&&!Y.hasClass("delete-rule")&&(!Y.hasClass("fm-button")||!Y.is("[id$=_query]"))){B("#"+O+"_search").click();return false}if(C.closeOnEscape&&X.which===27){B("#"+B.jgrid.jqID(R.modalhead)).find(".ui-jqdialog-titlebar-close").click();return false}})}if(T){B("#"+O+"_query").bind("click",function(){B(".queryresult",M).toggle();return false})}if(C.stringResult===undefined){C.stringResult=C.multipleSearch}B("#"+O+"_search").bind("click",function(){var b=B("#"+O),Y={},Z,c;b.find(".input-elm:focus").change();c=b.jqFilter("filterData");if(C.errorcheck){b[0].hideError();if(!C.showQuery){b.jqFilter("toSQLString")}if(b[0].p.error){b[0].showError();return false}}if(C.stringResult){try{Z=xmlJsonClass.toJson(c,"","",false)}catch(X){try{Z=JSON.stringify(c)}catch(a){}}if(typeof Z==="string"){Y[C.sFilter]=Z;B.each([C.sField,C.sValue,C.sOper],function(){Y[this]=""})}}else{if(C.multipleSearch){Y[C.sFilter]=c;B.each([C.sField,C.sValue,C.sOper],function(){Y[this]=""})}else{Y[C.sField]=c.rules[0].field;Y[C.sValue]=c.rules[0].data;Y[C.sOper]=c.rules[0].op;Y[C.sFilter]=""}}S.p.search=true;B.extend(S.p.postData,Y);I=B(S).triggerHandler("jqGridFilterSearch");if(I===undefined){I=true}if(I&&B.isFunction(C.onSearch)){I=C.onSearch.call(S,S.p.filters)}if(I!==false){B(S).trigger("reloadGrid",[{page:1}])}if(C.closeAfterSearch){B.jgrid.hideModal("#"+B.jgrid.jqID(R.themodal),{gb:"#gbox_"+B.jgrid.jqID(S.p.id),jqm:C.jqModal,onClose:C.onClose})}return false});B("#"+O+"_reset").bind("click",function(){var X={},Y=B("#"+O);S.p.search=false;S.p.resetsearch=true;if(C.multipleSearch===false){X[C.sField]=X[C.sValue]=X[C.sOper]=""}else{X[C.sFilter]=""}Y[0].resetFilter();if(Q){B(".ui-template",M).val("default")}B.extend(S.p.postData,X);I=B(S).triggerHandler("jqGridFilterReset");if(I===undefined){I=true}if(I&&B.isFunction(C.onReset)){I=C.onReset.call(S)}if(I!==false){B(S).trigger("reloadGrid",[{page:1}])}if(C.closeAfterReset){B.jgrid.hideModal("#"+B.jgrid.jqID(R.themodal),{gb:"#gbox_"+B.jgrid.jqID(S.p.id),jqm:C.jqModal,onClose:C.onClose})}return false});H(B("#"+O));B(".fm-button:not(.ui-state-disabled)",M).hover(function(){B(this).addClass("ui-state-hover")},function(){B(this).removeClass("ui-state-hover")})}})},editGridRow:function(D,C){C=B.extend(true,{top:0,left:0,width:300,datawidth:"auto",height:"auto",dataheight:"auto",modal:false,overlay:30,drag:true,resize:true,url:null,mtype:"POST",clearAfterAdd:true,closeAfterEdit:false,reloadAfterSubmit:true,onInitializeForm:null,beforeInitData:null,beforeShowForm:null,afterShowForm:null,beforeSubmit:null,afterSubmit:null,onclickSubmit:null,afterComplete:null,onclickPgButtons:null,afterclickPgButtons:null,editData:{},recreateForm:false,jqModal:true,closeOnEscape:false,addedrow:"first",topinfo:"",bottominfo:"",saveicon:[],closeicon:[],savekey:[false,13],navkeys:[false,38,40],checkOnSubmit:false,checkOnUpdate:false,_savedData:{},processing:false,onClose:null,ajaxEditOptions:{},serializeEditData:null,viewPagerButtons:true,overlayClass:"ui-widget-overlay"},B.jgrid.edit,C||{});A[B(this)[0].p.id]=C;return this.each(function(){var m=this;if(!m.grid||!D){return}var j=m.p.id,W="FrmGrid_"+j,k="TblGrid_"+j,r="#"+B.jgrid.jqID(k),h={themodal:"editmod"+j,modalhead:"edithd"+j,modalcontent:"editcnt"+j,scrollelm:W},q=B.isFunction(A[m.p.id].beforeShowForm)?A[m.p.id].beforeShowForm:false,F=B.isFunction(A[m.p.id].afterShowForm)?A[m.p.id].afterShowForm:false,Z=B.isFunction(A[m.p.id].beforeInitData)?A[m.p.id].beforeInitData:false,Y=B.isFunction(A[m.p.id].onInitializeForm)?A[m.p.id].onInitializeForm:false,p=true,K=1,a=0,H,d,S;W=B.jgrid.jqID(W);if(D==="new"){D="_empty";S="add";C.caption=A[m.p.id].addCaption}else{C.caption=A[m.p.id].editCaption;S="edit"}if(!C.recreateForm){if(B(m).data("formProp")){B.extend(A[B(this)[0].p.id],B(m).data("formProp"))}}var I=true;if(C.checkOnUpdate&&C.jqModal&&!C.modal){I=false}function w(){B(r+" > tbody > tr > td > .FormElement").each(function(){var z=B(".customelement",this);if(z.length){var y=z[0],x=B(y).attr("name");B.each(m.p.colModel,function(){if(this.name===x&&this.editoptions&&B.isFunction(this.editoptions.custom_value)){try{H[x]=this.editoptions.custom_value.call(m,B("#"+B.jgrid.jqID(x),r),"get");if(H[x]===undefined){throw"e1"}}catch(Ac){if(Ac==="e1"){B.jgrid.info_dialog(B.jgrid.errors.errcap,"function 'custom_value' "+B.jgrid.edit.msg.novalue,B.jgrid.edit.bClose)}else{B.jgrid.info_dialog(B.jgrid.errors.errcap,Ac.message,B.jgrid.edit.bClose)}}return true}})}else{switch(B(this).get(0).type){case"checkbox":if(B(this).is(":checked")){H[this.name]=B(this).val()}else{var Ab=B(this).attr("offval");H[this.name]=Ab}break;case"select-one":H[this.name]=B("option:selected",this).val();break;case"select-multiple":H[this.name]=B(this).val();if(H[this.name]){H[this.name]=H[this.name].join(",")}else{H[this.name]=""}var Aa=[];B("option:selected",this).each(function(Ac,Ad){Aa[Ac]=B(Ad).text()});break;case"password":case"text":case"textarea":case"button":H[this.name]=B(this).val();break}if(m.p.autoencode){H[this.name]=B.jgrid.htmlEncode(H[this.name])}}});return true}function v(Ai,Ad,Am,An){var Ah,Ag,Aa,Ae=0,y,Ak,Af,x=[],z=false,Aj="<td class='CaptionTD'>&#160;</td><td class='DataTD'>&#160;</td>",Ac="",Ab;for(Ab=1;Ab<=An;Ab++){Ac+=Aj}if(Ai!=="_empty"){z=B(Ad).jqGrid("getInd",Ai)}B(Ad.p.colModel).each(function(Ap){Ah=this.name;if(this.editrules&&this.editrules.edithidden===true){Ag=false}else{Ag=this.hidden===true?true:false}Ak=Ag?"style='display:none'":"";if(Ah!=="cb"&&Ah!=="subgrid"&&this.editable===true&&Ah!=="rn"){if(z===false){y=""}else{if(Ah===Ad.p.ExpandColumn&&Ad.p.treeGrid===true){y=B("td[role='gridcell']:eq("+Ap+")",Ad.rows[z]).text()}else{try{y=B.unformat.call(Ad,B("td[role='gridcell']:eq("+Ap+")",Ad.rows[z]),{rowId:Ai,colModel:this},Ap)}catch(Au){y=(this.edittype&&this.edittype==="textarea")?B("td[role='gridcell']:eq("+Ap+")",Ad.rows[z]).text():B("td[role='gridcell']:eq("+Ap+")",Ad.rows[z]).html()}if(!y||y==="&nbsp;"||y==="&#160;"||(y.length===1&&y.charCodeAt(0)===160)){y=""}}}var Aq=B.extend({},this.editoptions||{},{id:Ah,name:Ah}),Ar=B.extend({},{elmprefix:"",elmsuffix:"",rowabove:false,rowcontent:""},this.formoptions||{}),Ao=parseInt(Ar.rowpos,10)||Ae+1,At=parseInt((parseInt(Ar.colpos,10)||1)*2,10);if(Ai==="_empty"&&Aq.defaultValue){y=B.isFunction(Aq.defaultValue)?Aq.defaultValue.call(m):Aq.defaultValue}if(!this.edittype){this.edittype="text"}if(m.p.autoencode){y=B.jgrid.htmlDecode(y)}Af=B.jgrid.createEl.call(m,this.edittype,Aq,y,false,B.extend({},B.jgrid.ajaxOptions,Ad.p.ajaxSelectOptions||{}));if(A[m.p.id].checkOnSubmit||A[m.p.id].checkOnUpdate){A[m.p.id]._savedData[Ah]=y}B(Af).addClass("FormElement");if(B.inArray(this.edittype,["text","textarea","password","select"])>-1){B(Af).addClass("ui-widget-content ui-corner-all")}Aa=B(Am).find("tr[rowpos="+Ao+"]");if(Ar.rowabove){var As=B("<tr><td class='contentinfo' colspan='"+(An*2)+"'>"+Ar.rowcontent+"</td></tr>");B(Am).append(As);As[0].rp=Ao}if(Aa.length===0){Aa=B("<tr "+Ak+" rowpos='"+Ao+"'></tr>").addClass("FormData").attr("id","tr_"+Ah);B(Aa).append(Ac);B(Am).append(Aa);Aa[0].rp=Ao}B("td:eq("+(At-2)+")",Aa[0]).html(Ar.label===undefined?Ad.p.colNames[Ap]:Ar.label);B("td:eq("+(At-1)+")",Aa[0]).append(Ar.elmprefix).append(Af).append(Ar.elmsuffix);if(this.edittype==="custom"&&B.isFunction(Aq.custom_value)){Aq.custom_value.call(m,B("#"+Ah,"#"+W),"set",y)}B.jgrid.bindEv.call(m,Af,Aq);x[Ae]=Ap;Ae++}});if(Ae>0){var Al=B("<tr class='FormData' style='display:none'><td class='CaptionTD'></td><td colspan='"+(An*2-1)+"' class='DataTD'><input class='FormElement' id='id_g' type='text' name='"+Ad.p.id+"_id' value='"+Ai+"'/></td></tr>");Al[0].rp=Ae+999;B(Am).append(Al);if(A[m.p.id].checkOnSubmit||A[m.p.id].checkOnUpdate){A[m.p.id]._savedData[Ad.p.id+"_id"]=Ai}}return x}function c(Ai,Af,Ah){var x,Ag=0,Ab,Aa,y,Ad,z;if(A[m.p.id].checkOnSubmit||A[m.p.id].checkOnUpdate){A[m.p.id]._savedData={};A[m.p.id]._savedData[Af.p.id+"_id"]=Ai}var Ae=Af.p.colModel;if(Ai==="_empty"){B(Ae).each(function(){x=this.name;y=B.extend({},this.editoptions||{});Aa=B("#"+B.jgrid.jqID(x),"#"+Ah);if(Aa&&Aa.length&&Aa[0]!==null){Ad="";if(this.edittype==="custom"&&B.isFunction(y.custom_value)){y.custom_value.call(m,B("#"+x,"#"+Ah),"set",Ad)}else{if(y.defaultValue){Ad=B.isFunction(y.defaultValue)?y.defaultValue.call(m):y.defaultValue;if(Aa[0].type==="checkbox"){z=Ad.toLowerCase();if(z.search(/(false|f|0|no|n|off|undefined)/i)<0&&z!==""){Aa[0].checked=true;Aa[0].defaultChecked=true;Aa[0].value=Ad}else{Aa[0].checked=false;Aa[0].defaultChecked=false}}else{Aa.val(Ad)}}else{if(Aa[0].type==="checkbox"){Aa[0].checked=false;Aa[0].defaultChecked=false;Ad=B(Aa).attr("offval")}else{if(Aa[0].type&&Aa[0].type.substr(0,6)==="select"){Aa[0].selectedIndex=0}else{Aa.val(Ad)}}}}if(A[m.p.id].checkOnSubmit===true||A[m.p.id].checkOnUpdate){A[m.p.id]._savedData[x]=Ad}}});B("#id_g","#"+Ah).val(Ai);return}var Ac=B(Af).jqGrid("getInd",Ai,true);if(!Ac){return}B('td[role="gridcell"]',Ac).each(function(Ak){x=Ae[Ak].name;if(x!=="cb"&&x!=="subgrid"&&x!=="rn"&&Ae[Ak].editable===true){if(x===Af.p.ExpandColumn&&Af.p.treeGrid===true){Ab=B(this).text()}else{try{Ab=B.unformat.call(Af,B(this),{rowId:Ai,colModel:Ae[Ak]},Ak)}catch(An){Ab=Ae[Ak].edittype==="textarea"?B(this).text():B(this).html()}}if(m.p.autoencode){Ab=B.jgrid.htmlDecode(Ab)}if(A[m.p.id].checkOnSubmit===true||A[m.p.id].checkOnUpdate){A[m.p.id]._savedData[x]=Ab}x=B.jgrid.jqID(x);switch(Ae[Ak].edittype){case"password":case"text":case"button":case"image":case"textarea":if(Ab==="&nbsp;"||Ab==="&#160;"||(Ab.length===1&&Ab.charCodeAt(0)===160)){Ab=""}B("#"+x,"#"+Ah).val(Ab);break;case"select":var Am=Ab.split(",");Am=B.map(Am,function(Ao){return B.trim(Ao)});B("#"+x+" option","#"+Ah).each(function(){if(!Ae[Ak].editoptions.multiple&&(B.trim(Ab)===B.trim(B(this).text())||Am[0]===B.trim(B(this).text())||Am[0]===B.trim(B(this).val()))){this.selected=true}else{if(Ae[Ak].editoptions.multiple){if(B.inArray(B.trim(B(this).text()),Am)>-1||B.inArray(B.trim(B(this).val()),Am)>-1){this.selected=true}else{this.selected=false}}else{this.selected=false}}});break;case"checkbox":Ab=String(Ab);if(Ae[Ak].editoptions&&Ae[Ak].editoptions.value){var Aj=Ae[Ak].editoptions.value.split(":");if(Aj[0]===Ab){B("#"+x,"#"+Ah)[m.p.useProp?"prop":"attr"]({"checked":true,"defaultChecked":true})}else{B("#"+x,"#"+Ah)[m.p.useProp?"prop":"attr"]({"checked":false,"defaultChecked":false})}}else{Ab=Ab.toLowerCase();if(Ab.search(/(false|f|0|no|n|off|undefined)/i)<0&&Ab!==""){B("#"+x,"#"+Ah)[m.p.useProp?"prop":"attr"]("checked",true);B("#"+x,"#"+Ah)[m.p.useProp?"prop":"attr"]("defaultChecked",true)}else{B("#"+x,"#"+Ah)[m.p.useProp?"prop":"attr"]("checked",false);B("#"+x,"#"+Ah)[m.p.useProp?"prop":"attr"]("defaultChecked",false)}}break;case"custom":try{if(Ae[Ak].editoptions&&B.isFunction(Ae[Ak].editoptions.custom_value)){Ae[Ak].editoptions.custom_value.call(m,B("#"+x,"#"+Ah),"set",Ab)}else{throw"e1"}}catch(Al){if(Al==="e1"){B.jgrid.info_dialog(B.jgrid.errors.errcap,"function 'custom_value' "+B.jgrid.edit.msg.nodefined,B.jgrid.edit.bClose)}else{B.jgrid.info_dialog(B.jgrid.errors.errcap,Al.message,B.jgrid.edit.bClose)}}break}Ag++}});if(Ag>0){B("#id_g",r).val(Ai)}}function G(){B.each(m.p.colModel,function(x,y){if(y.editoptions&&y.editoptions.NullIfEmpty===true){if(H.hasOwnProperty(y.name)&&H[y.name]===""){H[y.name]="null"}}})}function O(){var Ah,Aj=[true,"",""],Ae={},Ak=m.p.prmNames,Aa,Ab,Ai,Af,Ag;var z=B(m).triggerHandler("jqGridAddEditBeforeCheckValues",[B("#"+W),S]);if(z&&typeof z==="object"){H=z}if(B.isFunction(A[m.p.id].beforeCheckValues)){z=A[m.p.id].beforeCheckValues.call(m,H,B("#"+W),S);if(z&&typeof z==="object"){H=z}}for(Ai in H){if(H.hasOwnProperty(Ai)){Aj=B.jgrid.checkValues.call(m,H[Ai],Ai);if(Aj[0]===false){break}}}G();if(Aj[0]){Ae=B(m).triggerHandler("jqGridAddEditClickSubmit",[A[m.p.id],H,S]);if(Ae===undefined&&B.isFunction(A[m.p.id].onclickSubmit)){Ae=A[m.p.id].onclickSubmit.call(m,A[m.p.id],H,S)||{}}Aj=B(m).triggerHandler("jqGridAddEditBeforeSubmit",[H,B("#"+W),S]);if(Aj===undefined){Aj=[true,"",""]}if(Aj[0]&&B.isFunction(A[m.p.id].beforeSubmit)){Aj=A[m.p.id].beforeSubmit.call(m,H,B("#"+W),S)}}if(Aj[0]&&!A[m.p.id].processing){A[m.p.id].processing=true;B("#sData",r+"_2").addClass("ui-state-active");Ab=Ak.oper;Aa=Ak.id;H[Ab]=(B.trim(H[m.p.id+"_id"])==="_empty")?Ak.addoper:Ak.editoper;if(H[Ab]!==Ak.addoper){H[Aa]=H[m.p.id+"_id"]}else{if(H[Aa]===undefined){H[Aa]=H[m.p.id+"_id"]}}delete H[m.p.id+"_id"];H=B.extend(H,A[m.p.id].editData,Ae);if(m.p.treeGrid===true){if(H[Ab]===Ak.addoper){Af=B(m).jqGrid("getGridParam","selrow");var Ac=m.p.treeGridModel==="adjacency"?m.p.treeReader.parent_id_field:"parent_id";H[Ac]=Af}for(Ag in m.p.treeReader){if(m.p.treeReader.hasOwnProperty(Ag)){var y=m.p.treeReader[Ag];if(H.hasOwnProperty(y)){if(H[Ab]===Ak.addoper&&Ag==="parent_id_field"){continue}delete H[y]}}}}H[Aa]=B.jgrid.stripPref(m.p.idPrefix,H[Aa]);var x=B.extend({url:A[m.p.id].url||B(m).jqGrid("getGridParam","editurl"),type:A[m.p.id].mtype,data:B.isFunction(A[m.p.id].serializeEditData)?A[m.p.id].serializeEditData.call(m,H):H,complete:function(Ao,An){var Al;H[Aa]=m.p.idPrefix+H[Aa];if(Ao.status>=300&&Ao.status!==304){Aj[0]=false;Aj[1]=B(m).triggerHandler("jqGridAddEditErrorTextFormat",[Ao,S]);if(B.isFunction(A[m.p.id].errorTextFormat)){Aj[1]=A[m.p.id].errorTextFormat.call(m,Ao,S)}else{Aj[1]=An+" Status: '"+Ao.statusText+"'. Error code: "+Ao.status}}else{Aj=B(m).triggerHandler("jqGridAddEditAfterSubmit",[Ao,H,S]);if(Aj===undefined){Aj=[true,"",""]}if(Aj[0]&&B.isFunction(A[m.p.id].afterSubmit)){Aj=A[m.p.id].afterSubmit.call(m,Ao,H,S)}}if(Aj[0]===false){B("#FormError>td",r).html(Aj[1]);B("#FormError",r).show()}else{if(m.p.autoencode){B.each(H,function(Ap,Aq){H[Ap]=B.jgrid.htmlDecode(Aq)})}if(H[Ab]===Ak.addoper){if(!Aj[2]){Aj[2]=B.jgrid.randId()}H[Aa]=Aj[2];if(A[m.p.id].reloadAfterSubmit){B(m).trigger("reloadGrid")}else{if(m.p.treeGrid===true){B(m).jqGrid("addChildNode",Aj[2],Af,H)}else{B(m).jqGrid("addRowData",Aj[2],H,C.addedrow)}}if(A[m.p.id].closeAfterAdd){if(m.p.treeGrid!==true){B(m).jqGrid("setSelection",Aj[2])}B.jgrid.hideModal("#"+B.jgrid.jqID(h.themodal),{gb:"#gbox_"+B.jgrid.jqID(j),jqm:C.jqModal,onClose:A[m.p.id].onClose})}else{if(A[m.p.id].clearAfterAdd){c("_empty",m,W)}}}else{if(A[m.p.id].reloadAfterSubmit){B(m).trigger("reloadGrid");if(!A[m.p.id].closeAfterEdit){setTimeout(function(){B(m).jqGrid("setSelection",H[Aa])},1000)}}else{if(m.p.treeGrid===true){B(m).jqGrid("setTreeRow",H[Aa],H)}else{B(m).jqGrid("setRowData",H[Aa],H)}}if(A[m.p.id].closeAfterEdit){B.jgrid.hideModal("#"+B.jgrid.jqID(h.themodal),{gb:"#gbox_"+B.jgrid.jqID(j),jqm:C.jqModal,onClose:A[m.p.id].onClose})}}if(B.isFunction(A[m.p.id].afterComplete)){Ah=Ao;setTimeout(function(){B(m).triggerHandler("jqGridAddEditAfterComplete",[Ah,H,B("#"+W),S]);A[m.p.id].afterComplete.call(m,Ah,H,B("#"+W),S);Ah=null},500)}if(A[m.p.id].checkOnSubmit||A[m.p.id].checkOnUpdate){B("#"+W).data("disabled",false);if(A[m.p.id]._savedData[m.p.id+"_id"]!=="_empty"){for(Al in A[m.p.id]._savedData){if(A[m.p.id]._savedData.hasOwnProperty(Al)&&H[Al]){A[m.p.id]._savedData[Al]=H[Al]}}}}}A[m.p.id].processing=false;B("#sData",r+"_2").removeClass("ui-state-active");try{B(":input:visible","#"+W)[0].focus()}catch(Am){}}},B.jgrid.ajaxOptions,A[m.p.id].ajaxEditOptions);if(!x.url&&!A[m.p.id].useDataProxy){if(B.isFunction(m.p.dataProxy)){A[m.p.id].useDataProxy=true}else{Aj[0]=false;Aj[1]+=" "+B.jgrid.errors.nourl}}if(Aj[0]){if(A[m.p.id].useDataProxy){var Ad=m.p.dataProxy.call(m,x,"set_"+m.p.id);if(Ad===undefined){Ad=[true,""]}if(Ad[0]===false){Aj[0]=false;Aj[1]=Ad[1]||"Error deleting the selected row!"}else{if(x.data.oper===Ak.addoper&&A[m.p.id].closeAfterAdd){B.jgrid.hideModal("#"+B.jgrid.jqID(h.themodal),{gb:"#gbox_"+B.jgrid.jqID(j),jqm:C.jqModal,onClose:A[m.p.id].onClose})}if(x.data.oper===Ak.editoper&&A[m.p.id].closeAfterEdit){B.jgrid.hideModal("#"+B.jgrid.jqID(h.themodal),{gb:"#gbox_"+B.jgrid.jqID(j),jqm:C.jqModal,onClose:A[m.p.id].onClose})}}}else{B.ajax(x)}}}if(Aj[0]===false){B("#FormError>td",r).html(Aj[1]);B("#FormError",r).show()}}function s(y,z){var Aa=false,x;for(x in y){if(y.hasOwnProperty(x)&&y[x]!=z[x]){Aa=true;break}}return Aa}function o(){var x=true;B("#FormError",r).hide();if(A[m.p.id].checkOnUpdate){H={};w();d=s(H,A[m.p.id]._savedData);if(d){B("#"+W).data("disabled",true);B(".confirm","#"+h.themodal).show();x=false}}return x}function X(){var x;if(D!=="_empty"&&m.p.savedRow!==undefined&&m.p.savedRow.length>0&&B.isFunction(B.fn.jqGrid.restoreRow)){for(x=0;x<m.p.savedRow.length;x++){if(m.p.savedRow[x].id==D){B(m).jqGrid("restoreRow",D);break}}}}function l(y,x){var z=x[1].length-1;if(y===0){B("#pData",r+"_2").addClass("ui-state-disabled")}else{if(x[1][y-1]!==undefined&&B("#"+B.jgrid.jqID(x[1][y-1])).hasClass("ui-state-disabled")){B("#pData",r+"_2").addClass("ui-state-disabled")}else{B("#pData",r+"_2").removeClass("ui-state-disabled")}}if(y===z){B("#nData",r+"_2").addClass("ui-state-disabled")}else{if(x[1][y+1]!==undefined&&B("#"+B.jgrid.jqID(x[1][y+1])).hasClass("ui-state-disabled")){B("#nData",r+"_2").addClass("ui-state-disabled")}else{B("#nData",r+"_2").removeClass("ui-state-disabled")}}}function b(){var x=B(m).jqGrid("getDataIDs"),z=B("#id_g",r).val(),y=B.inArray(z,x);return[y,x]}var T=isNaN(A[B(this)[0].p.id].dataheight)?A[B(this)[0].p.id].dataheight:A[B(this)[0].p.id].dataheight+"px",R=isNaN(A[B(this)[0].p.id].datawidth)?A[B(this)[0].p.id].datawidth:A[B(this)[0].p.id].datawidth+"px",u=B("<form name='FormPost' id='"+W+"' class='FormGrid' onSubmit='return false;' style='width:"+R+";overflow:auto;position:relative;height:"+T+";'></form>").data("disabled",false),V=B("<table id='"+k+"' class='EditTable' cellspacing='0' cellpadding='0' border='0'><tbody></tbody></table>");p=B(m).triggerHandler("jqGridAddEditBeforeInitData",[B("#"+W),S]);if(p===undefined){p=true}if(p&&Z){p=Z.call(m,B("#"+W),S)}if(p===false){return}X();B(m.p.colModel).each(function(){var x=this.formoptions;K=Math.max(K,x?x.colpos||0:0);a=Math.max(a,x?x.rowpos||0:0)});B(u).append(V);var J=B("<tr id='FormError' style='display:none'><td class='ui-state-error' colspan='"+(K*2)+"'></td></tr>");J[0].rp=0;B(V).append(J);J=B("<tr style='display:none' class='tinfo'><td class='topinfo' colspan='"+(K*2)+"'>"+A[m.p.id].topinfo+"</td></tr>");J[0].rp=0;B(V).append(J);var t=m.p.direction==="rtl"?true:false,E=t?"nData":"pData",f=t?"pData":"nData";v(D,m,V,K);var L="<a id='"+E+"' class='fm-button ui-state-default ui-corner-left'><span class='ui-icon ui-icon-triangle-1-w'></span></a>",n="<a id='"+f+"' class='fm-button ui-state-default ui-corner-right'><span class='ui-icon ui-icon-triangle-1-e'></span></a>",Q="<a id='sData' class='fm-button ui-state-default ui-corner-all'>"+C.bSubmit+"</a>",e="<a id='cData' class='fm-button ui-state-default ui-corner-all'>"+C.bCancel+"</a>";var M="<table border='0' cellspacing='0' cellpadding='0' class='EditTable' id='"+k+"_2'><tbody><tr><td colspan='2'><hr class='ui-widget-content' style='margin:1px'/></td></tr><tr id='Act_Buttons'><td class='navButton'>"+(t?n+L:L+n)+"</td><td class='EditButton'>"+Q+e+"</td></tr>";M+="<tr style='display:none' class='binfo'><td class='bottominfo' colspan='2'>"+A[m.p.id].bottominfo+"</td></tr>";M+="</tbody></table>";if(a>0){var P=[];B.each(B(V)[0].rows,function(x,y){P[x]=y});P.sort(function(y,x){if(y.rp>x.rp){return 1}if(y.rp<x.rp){return -1}return 0});B.each(P,function(y,x){B("tbody",V).append(x)})}C.gbox="#gbox_"+B.jgrid.jqID(j);var N=false;if(C.closeOnEscape===true){C.closeOnEscape=false;N=true}var g=B("<div></div>").append(u).append(M);B.jgrid.createModal(h,g,A[B(this)[0].p.id],"#gview_"+B.jgrid.jqID(m.p.id),B("#gbox_"+B.jgrid.jqID(m.p.id))[0]);if(t){B("#pData, #nData",r+"_2").css("float","right");B(".EditButton",r+"_2").css("text-align","left")}if(A[m.p.id].topinfo){B(".tinfo",r).show()}if(A[m.p.id].bottominfo){B(".binfo",r+"_2").show()}g=null;M=null;B("#"+B.jgrid.jqID(h.themodal)).keydown(function(y){var x=y.target;if(B("#"+W).data("disabled")===true){return false}if(A[m.p.id].savekey[0]===true&&y.which===A[m.p.id].savekey[1]){if(x.tagName!=="TEXTAREA"){B("#sData",r+"_2").trigger("click");return false}}if(y.which===27){if(!o()){return false}if(N){B.jgrid.hideModal("#"+B.jgrid.jqID(h.themodal),{gb:C.gbox,jqm:C.jqModal,onClose:A[m.p.id].onClose})}return false}if(A[m.p.id].navkeys[0]===true){if(B("#id_g",r).val()==="_empty"){return true}if(y.which===A[m.p.id].navkeys[1]){B("#pData",r+"_2").trigger("click");return false}if(y.which===A[m.p.id].navkeys[2]){B("#nData",r+"_2").trigger("click");return false}}});if(C.checkOnUpdate){B("a.ui-jqdialog-titlebar-close span","#"+B.jgrid.jqID(h.themodal)).removeClass("jqmClose");B("a.ui-jqdialog-titlebar-close","#"+B.jgrid.jqID(h.themodal)).unbind("click").click(function(){if(!o()){return false}B.jgrid.hideModal("#"+B.jgrid.jqID(h.themodal),{gb:"#gbox_"+B.jgrid.jqID(j),jqm:C.jqModal,onClose:A[m.p.id].onClose});return false})}C.saveicon=B.extend([true,"left","ui-icon-disk"],C.saveicon);C.closeicon=B.extend([true,"left","ui-icon-close"],C.closeicon);if(C.saveicon[0]===true){B("#sData",r+"_2").addClass(C.saveicon[1]==="right"?"fm-button-icon-right":"fm-button-icon-left").append("<span class='ui-icon "+C.saveicon[2]+"'></span>")}if(C.closeicon[0]===true){B("#cData",r+"_2").addClass(C.closeicon[1]==="right"?"fm-button-icon-right":"fm-button-icon-left").append("<span class='ui-icon "+C.closeicon[2]+"'></span>")}if(A[m.p.id].checkOnSubmit||A[m.p.id].checkOnUpdate){Q="<a id='sNew' class='fm-button ui-state-default ui-corner-all' style='z-index:1002'>"+C.bYes+"</a>";n="<a id='nNew' class='fm-button ui-state-default ui-corner-all' style='z-index:1002'>"+C.bNo+"</a>";e="<a id='cNew' class='fm-button ui-state-default ui-corner-all' style='z-index:1002'>"+C.bExit+"</a>";var i=C.zIndex||999;i++;B("<div class='"+C.overlayClass+" jqgrid-overlay confirm' style='z-index:"+i+";display:none;'>&#160;</div><div class='confirm ui-widget-content ui-jqconfirm' style='z-index:"+(i+1)+"'>"+C.saveData+"<br/><br/>"+Q+n+e+"</div>").insertAfter("#"+W);B("#sNew","#"+B.jgrid.jqID(h.themodal)).click(function(){O();B("#"+W).data("disabled",false);B(".confirm","#"+B.jgrid.jqID(h.themodal)).hide();return false});B("#nNew","#"+B.jgrid.jqID(h.themodal)).click(function(){B(".confirm","#"+B.jgrid.jqID(h.themodal)).hide();B("#"+W).data("disabled",false);setTimeout(function(){B(":input:visible","#"+W)[0].focus()},0);return false});B("#cNew","#"+B.jgrid.jqID(h.themodal)).click(function(){B(".confirm","#"+B.jgrid.jqID(h.themodal)).hide();B("#"+W).data("disabled",false);B.jgrid.hideModal("#"+B.jgrid.jqID(h.themodal),{gb:"#gbox_"+B.jgrid.jqID(j),jqm:C.jqModal,onClose:A[m.p.id].onClose});return false})}B(m).triggerHandler("jqGridAddEditInitializeForm",[B("#"+W),S]);if(Y){Y.call(m,B("#"+W),S)}if(D==="_empty"||!A[m.p.id].viewPagerButtons){B("#pData,#nData",r+"_2").hide()}else{B("#pData,#nData",r+"_2").show()}B(m).triggerHandler("jqGridAddEditBeforeShowForm",[B("#"+W),S]);if(q){q.call(m,B("#"+W),S)}B("#"+B.jgrid.jqID(h.themodal)).data("onClose",A[m.p.id].onClose);B.jgrid.viewModal("#"+B.jgrid.jqID(h.themodal),{gbox:"#gbox_"+B.jgrid.jqID(j),jqm:C.jqModal,overlay:C.overlay,modal:C.modal,overlayClass:C.overlayClass,onHide:function(x){B(m).data("formProp",{top:parseFloat(B(x.w).css("top")),left:parseFloat(B(x.w).css("left")),width:B(x.w).width(),height:B(x.w).height(),dataheight:B("#"+W).height(),datawidth:B("#"+W).width()});x.w.remove();if(x.o){x.o.remove()}}});if(!I){B("."+B.jgrid.jqID(C.overlayClass)).click(function(){if(!o()){return false}B.jgrid.hideModal("#"+B.jgrid.jqID(h.themodal),{gb:"#gbox_"+B.jgrid.jqID(j),jqm:C.jqModal,onClose:A[m.p.id].onClose});return false})}B(".fm-button","#"+B.jgrid.jqID(h.themodal)).hover(function(){B(this).addClass("ui-state-hover")},function(){B(this).removeClass("ui-state-hover")});B("#sData",r+"_2").click(function(){H={};B("#FormError",r).hide();w();if(H[m.p.id+"_id"]==="_empty"){O()}else{if(C.checkOnSubmit===true){d=s(H,A[m.p.id]._savedData);if(d){B("#"+W).data("disabled",true);B(".confirm","#"+B.jgrid.jqID(h.themodal)).show()}else{O()}}else{O()}}return false});B("#cData",r+"_2").click(function(){if(!o()){return false}B.jgrid.hideModal("#"+B.jgrid.jqID(h.themodal),{gb:"#gbox_"+B.jgrid.jqID(j),jqm:C.jqModal,onClose:A[m.p.id].onClose});return false});B("#nData",r+"_2").click(function(){if(!o()){return false}B("#FormError",r).hide();var x=b();x[0]=parseInt(x[0],10);if(x[0]!==-1&&x[1][x[0]+1]){B(m).triggerHandler("jqGridAddEditClickPgButtons",["next",B("#"+W),x[1][x[0]]]);var y;if(B.isFunction(C.onclickPgButtons)){y=C.onclickPgButtons.call(m,"next",B("#"+W),x[1][x[0]]);if(y!==undefined&&y===false){return false}}if(B("#"+B.jgrid.jqID(x[1][x[0]+1])).hasClass("ui-state-disabled")){return false}c(x[1][x[0]+1],m,W);B(m).jqGrid("setSelection",x[1][x[0]+1]);B(m).triggerHandler("jqGridAddEditAfterClickPgButtons",["next",B("#"+W),x[1][x[0]]]);if(B.isFunction(C.afterclickPgButtons)){C.afterclickPgButtons.call(m,"next",B("#"+W),x[1][x[0]+1])}l(x[0]+1,x)}return false});B("#pData",r+"_2").click(function(){if(!o()){return false}B("#FormError",r).hide();var x=b();if(x[0]!==-1&&x[1][x[0]-1]){B(m).triggerHandler("jqGridAddEditClickPgButtons",["prev",B("#"+W),x[1][x[0]]]);var y;if(B.isFunction(C.onclickPgButtons)){y=C.onclickPgButtons.call(m,"prev",B("#"+W),x[1][x[0]]);if(y!==undefined&&y===false){return false}}if(B("#"+B.jgrid.jqID(x[1][x[0]-1])).hasClass("ui-state-disabled")){return false}c(x[1][x[0]-1],m,W);B(m).jqGrid("setSelection",x[1][x[0]-1]);B(m).triggerHandler("jqGridAddEditAfterClickPgButtons",["prev",B("#"+W),x[1][x[0]]]);if(B.isFunction(C.afterclickPgButtons)){C.afterclickPgButtons.call(m,"prev",B("#"+W),x[1][x[0]-1])}l(x[0]-1,x)}return false});B(m).triggerHandler("jqGridAddEditAfterShowForm",[B("#"+W),S]);if(F){F.call(m,B("#"+W),S)}var U=b();l(U[0],U)})},viewGridRow:function(D,C){C=B.extend(true,{top:0,left:0,width:0,datawidth:"auto",height:"auto",dataheight:"auto",modal:false,overlay:30,drag:true,resize:true,jqModal:true,closeOnEscape:false,labelswidth:"30%",closeicon:[],navkeys:[false,38,40],onClose:null,beforeShowForm:null,beforeInitData:null,viewPagerButtons:true,recreateForm:false},B.jgrid.view,C||{});A[B(this)[0].p.id]=C;return this.each(function(){var M=this;if(!M.grid||!D){return}var R=M.p.id,b="ViewGrid_"+B.jgrid.jqID(R),N="ViewTbl_"+B.jgrid.jqID(R),V="ViewGrid_"+R,S="ViewTbl_"+R,U={themodal:"viewmod"+R,modalhead:"viewhd"+R,modalcontent:"viewcnt"+R,scrollelm:b},f=B.isFunction(A[M.p.id].beforeInitData)?A[M.p.id].beforeInitData:false,E=true,I=1,c=0;if(!C.recreateForm){if(B(M).data("viewProp")){B.extend(A[B(this)[0].p.id],B(M).data("viewProp"))}}function W(){if(A[M.p.id].closeOnEscape===true||A[M.p.id].navkeys[0]===true){setTimeout(function(){B(".ui-jqdialog-titlebar-close","#"+B.jgrid.jqID(U.modalhead)).focus()},0)}}function Y(y,r,Ad,Ae){var x,u,o,s=0,m,Aa,j=[],n=false,p,z="<td class='CaptionTD form-view-label ui-widget-content' width='"+C.labelswidth+"'>&#160;</td><td class='DataTD form-view-data ui-helper-reset ui-widget-content'>&#160;</td>",q="",k="<td class='CaptionTD form-view-label ui-widget-content'>&#160;</td><td class='DataTD form-view-data ui-widget-content'>&#160;</td>",Ac=["integer","number","currency"],v=0,w=0,t,h,l;for(p=1;p<=Ae;p++){q+=p===1?z:k}B(r.p.colModel).each(function(){if(this.editrules&&this.editrules.edithidden===true){u=false}else{u=this.hidden===true?true:false}if(!u&&this.align==="right"){if(this.formatter&&B.inArray(this.formatter,Ac)!==-1){v=Math.max(v,parseInt(this.width,10))}else{w=Math.max(w,parseInt(this.width,10))}}});t=v!==0?v:w!==0?w:0;n=B(r).jqGrid("getInd",y);B(r.p.colModel).each(function(Af){x=this.name;h=false;if(this.editrules&&this.editrules.edithidden===true){u=false}else{u=this.hidden===true?true:false}Aa=u?"style='display:none'":"";l=(typeof this.viewable!=="boolean")?true:this.viewable;if(x!=="cb"&&x!=="subgrid"&&x!=="rn"&&l){if(n===false){m=""}else{if(x===r.p.ExpandColumn&&r.p.treeGrid===true){m=B("td:eq("+Af+")",r.rows[n]).text()}else{m=B("td:eq("+Af+")",r.rows[n]).html()}}h=this.align==="right"&&t!==0?true:false;var Ah=B.extend({},{rowabove:false,rowcontent:""},this.formoptions||{}),Ag=parseInt(Ah.rowpos,10)||s+1,Aj=parseInt((parseInt(Ah.colpos,10)||1)*2,10);if(Ah.rowabove){var Ai=B("<tr><td class='contentinfo' colspan='"+(Ae*2)+"'>"+Ah.rowcontent+"</td></tr>");B(Ad).append(Ai);Ai[0].rp=Ag}o=B(Ad).find("tr[rowpos="+Ag+"]");if(o.length===0){o=B("<tr "+Aa+" rowpos='"+Ag+"'></tr>").addClass("FormData").attr("id","trv_"+x);B(o).append(q);B(Ad).append(o);o[0].rp=Ag}B("td:eq("+(Aj-2)+")",o[0]).html("<b>"+(Ah.label===undefined?r.p.colNames[Af]:Ah.label)+"</b>");B("td:eq("+(Aj-1)+")",o[0]).append("<span>"+m+"</span>").attr("id","v_"+x);if(h){B("td:eq("+(Aj-1)+") span",o[0]).css({"text-align":"right",width:t+"px"})}j[s]=Af;s++}});if(s>0){var Ab=B("<tr class='FormData' style='display:none'><td class='CaptionTD'></td><td colspan='"+(Ae*2-1)+"' class='DataTD'><input class='FormElement' id='id_g' type='text' name='id' value='"+y+"'/></td></tr>");Ab[0].rp=s+99;B(Ad).append(Ab)}return j}function O(n,i){var k,l,j=0,h,m;m=B(i).jqGrid("getInd",n,true);if(!m){return}B("td",m).each(function(o){k=i.p.colModel[o].name;if(i.p.colModel[o].editrules&&i.p.colModel[o].editrules.edithidden===true){l=false}else{l=i.p.colModel[o].hidden===true?true:false}if(k!=="cb"&&k!=="subgrid"&&k!=="rn"){if(k===i.p.ExpandColumn&&i.p.treeGrid===true){h=B(this).text()}else{h=B(this).html()}k=B.jgrid.jqID("v_"+k);B("#"+k+" span","#"+N).html(h);if(l){B("#"+k,"#"+N).parents("tr:first").hide()}j++}});if(j>0){B("#id_g","#"+N).val(n)}}function X(i,h){var j=h[1].length-1;if(i===0){B("#pData","#"+N+"_2").addClass("ui-state-disabled")}else{if(h[1][i-1]!==undefined&&B("#"+B.jgrid.jqID(h[1][i-1])).hasClass("ui-state-disabled")){B("#pData",N+"_2").addClass("ui-state-disabled")}else{B("#pData","#"+N+"_2").removeClass("ui-state-disabled")}}if(i===j){B("#nData","#"+N+"_2").addClass("ui-state-disabled")}else{if(h[1][i+1]!==undefined&&B("#"+B.jgrid.jqID(h[1][i+1])).hasClass("ui-state-disabled")){B("#nData",N+"_2").addClass("ui-state-disabled")}else{B("#nData","#"+N+"_2").removeClass("ui-state-disabled")}}}function e(){var h=B(M).jqGrid("getDataIDs"),j=B("#id_g","#"+N).val(),i=B.inArray(j,h);return[i,h]}var T=isNaN(A[B(this)[0].p.id].dataheight)?A[B(this)[0].p.id].dataheight:A[B(this)[0].p.id].dataheight+"px",Z=isNaN(A[B(this)[0].p.id].datawidth)?A[B(this)[0].p.id].datawidth:A[B(this)[0].p.id].datawidth+"px",G=B("<form name='FormPost' id='"+V+"' class='FormGrid' style='width:"+Z+";overflow:auto;position:relative;height:"+T+";'></form>"),g=B("<table id='"+S+"' class='EditTable' cellspacing='1' cellpadding='2' border='0' style='table-layout:fixed'><tbody></tbody></table>");if(f){E=f.call(M,B("#"+b));if(E===undefined){E=true}}if(E===false){return}B(M.p.colModel).each(function(){var h=this.formoptions;I=Math.max(I,h?h.colpos||0:0);c=Math.max(c,h?h.rowpos||0:0)});B(G).append(g);Y(D,M,g,I);var P=M.p.direction==="rtl"?true:false,a=P?"nData":"pData",F=P?"pData":"nData",L="<a id='"+a+"' class='fm-button ui-state-default ui-corner-left'><span class='ui-icon ui-icon-triangle-1-w'></span></a>",Q="<a id='"+F+"' class='fm-button ui-state-default ui-corner-right'><span class='ui-icon ui-icon-triangle-1-e'></span></a>",J="<a id='cData' class='fm-button ui-state-default ui-corner-all'>"+C.bClose+"</a>";if(c>0){var d=[];B.each(B(g)[0].rows,function(h,j){d[h]=j});d.sort(function(i,h){if(i.rp>h.rp){return 1}if(i.rp<h.rp){return -1}return 0});B.each(d,function(i,h){B("tbody",g).append(h)})}C.gbox="#gbox_"+B.jgrid.jqID(R);var H=B("<div></div>").append(G).append("<table border='0' class='EditTable' id='"+N+"_2'><tbody><tr id='Act_Buttons'><td class='navButton' width='"+C.labelswidth+"'>"+(P?Q+L:L+Q)+"</td><td class='EditButton'>"+J+"</td></tr></tbody></table>");B.jgrid.createModal(U,H,C,"#gview_"+B.jgrid.jqID(M.p.id),B("#gview_"+B.jgrid.jqID(M.p.id))[0]);if(P){B("#pData, #nData","#"+N+"_2").css("float","right");B(".EditButton","#"+N+"_2").css("text-align","left")}if(!C.viewPagerButtons){B("#pData, #nData","#"+N+"_2").hide()}H=null;B("#"+U.themodal).keydown(function(h){if(h.which===27){if(A[M.p.id].closeOnEscape){B.jgrid.hideModal("#"+B.jgrid.jqID(U.themodal),{gb:C.gbox,jqm:C.jqModal,onClose:C.onClose})}return false}if(C.navkeys[0]===true){if(h.which===C.navkeys[1]){B("#pData","#"+N+"_2").trigger("click");return false}if(h.which===C.navkeys[2]){B("#nData","#"+N+"_2").trigger("click");return false}}});C.closeicon=B.extend([true,"left","ui-icon-close"],C.closeicon);if(C.closeicon[0]===true){B("#cData","#"+N+"_2").addClass(C.closeicon[1]==="right"?"fm-button-icon-right":"fm-button-icon-left").append("<span class='ui-icon "+C.closeicon[2]+"'></span>")}if(B.isFunction(C.beforeShowForm)){C.beforeShowForm.call(M,B("#"+b))}B.jgrid.viewModal("#"+B.jgrid.jqID(U.themodal),{gbox:"#gbox_"+B.jgrid.jqID(R),jqm:C.jqModal,overlay:C.overlay,modal:C.modal,onHide:function(i){B(M).data("viewProp",{top:parseFloat(B(i.w).css("top")),left:parseFloat(B(i.w).css("left")),width:B(i.w).width(),height:B(i.w).height(),dataheight:B("#"+b).height(),datawidth:B("#"+b).width()});i.w.remove();if(i.o){i.o.remove()}}});B(".fm-button:not(.ui-state-disabled)","#"+N+"_2").hover(function(){B(this).addClass("ui-state-hover")},function(){B(this).removeClass("ui-state-hover")});W();B("#cData","#"+N+"_2").click(function(){B.jgrid.hideModal("#"+B.jgrid.jqID(U.themodal),{gb:"#gbox_"+B.jgrid.jqID(R),jqm:C.jqModal,onClose:C.onClose});return false});B("#nData","#"+N+"_2").click(function(){B("#FormError","#"+N).hide();var h=e();h[0]=parseInt(h[0],10);if(h[0]!==-1&&h[1][h[0]+1]){if(B.isFunction(C.onclickPgButtons)){C.onclickPgButtons.call(M,"next",B("#"+b),h[1][h[0]])}O(h[1][h[0]+1],M);B(M).jqGrid("setSelection",h[1][h[0]+1]);if(B.isFunction(C.afterclickPgButtons)){C.afterclickPgButtons.call(M,"next",B("#"+b),h[1][h[0]+1])}X(h[0]+1,h)}W();return false});B("#pData","#"+N+"_2").click(function(){B("#FormError","#"+N).hide();var h=e();if(h[0]!==-1&&h[1][h[0]-1]){if(B.isFunction(C.onclickPgButtons)){C.onclickPgButtons.call(M,"prev",B("#"+b),h[1][h[0]])}O(h[1][h[0]-1],M);B(M).jqGrid("setSelection",h[1][h[0]-1]);if(B.isFunction(C.afterclickPgButtons)){C.afterclickPgButtons.call(M,"prev",B("#"+b),h[1][h[0]-1])}X(h[0]-1,h)}W();return false});var K=e();X(K[0],K)})},delGridRow:function(C,D){D=B.extend(true,{top:0,left:0,width:240,height:"auto",dataheight:"auto",modal:false,overlay:30,drag:true,resize:true,url:"",mtype:"POST",reloadAfterSubmit:true,beforeShowForm:null,beforeInitData:null,afterShowForm:null,beforeSubmit:null,onclickSubmit:null,afterSubmit:null,jqModal:true,closeOnEscape:false,delData:{},delicon:[],cancelicon:[],onClose:null,ajaxDelOptions:{},processing:false,serializeDelData:null,useDataProxy:false},B.jgrid.del,D||{});A[B(this)[0].p.id]=D;return this.each(function(){var N=this;if(!N.grid){return}if(!C){return}var P=B.isFunction(A[N.p.id].beforeShowForm),Q=B.isFunction(A[N.p.id].afterShowForm),V=B.isFunction(A[N.p.id].beforeInitData)?A[N.p.id].beforeInitData:false,K=N.p.id,R={},E=true,H="DelTbl_"+B.jgrid.jqID(K),T,U,O,M,I="DelTbl_"+K,G={themodal:"delmod"+K,modalhead:"delhd"+K,modalcontent:"delcnt"+K,scrollelm:H};if(B.isArray(C)){C=C.join()}if(B("#"+B.jgrid.jqID(G.themodal))[0]!==undefined){if(V){E=V.call(N,B("#"+H));if(E===undefined){E=true}}if(E===false){return}B("#DelData>td","#"+H).text(C);B("#DelError","#"+H).hide();if(A[N.p.id].processing===true){A[N.p.id].processing=false;B("#dData","#"+H).removeClass("ui-state-active")}if(P){A[N.p.id].beforeShowForm.call(N,B("#"+H))}B.jgrid.viewModal("#"+B.jgrid.jqID(G.themodal),{gbox:"#gbox_"+B.jgrid.jqID(K),jqm:A[N.p.id].jqModal,jqM:false,overlay:A[N.p.id].overlay,modal:A[N.p.id].modal});if(Q){A[N.p.id].afterShowForm.call(N,B("#"+H))}}else{var L=isNaN(A[N.p.id].dataheight)?A[N.p.id].dataheight:A[N.p.id].dataheight+"px",S=isNaN(D.datawidth)?D.datawidth:D.datawidth+"px",W="<div id='"+I+"' class='formdata' style='width:"+S+";overflow:auto;position:relative;height:"+L+";'>";W+="<table class='DelTable'><tbody>";W+="<tr id='DelError' style='display:none'><td class='ui-state-error'></td></tr>";W+="<tr id='DelData' style='display:none'><td >"+C+"</td></tr>";W+='<tr><td class="delmsg" style="white-space:pre;">'+A[N.p.id].msg+"</td></tr><tr><td >&#160;</td></tr>";W+="</tbody></table></div>";var J="<a id='dData' class='fm-button ui-state-default ui-corner-all'>"+D.bSubmit+"</a>",F="<a id='eData' class='fm-button ui-state-default ui-corner-all'>"+D.bCancel+"</a>";W+="<table cellspacing='0' cellpadding='0' border='0' class='EditTable' id='"+H+"_2'><tbody><tr><td><hr class='ui-widget-content' style='margin:1px'/></td></tr><tr><td class='DelButton EditButton'>"+J+"&#160;"+F+"</td></tr></tbody></table>";D.gbox="#gbox_"+B.jgrid.jqID(K);B.jgrid.createModal(G,W,D,"#gview_"+B.jgrid.jqID(N.p.id),B("#gview_"+B.jgrid.jqID(N.p.id))[0]);if(V){E=V.call(N,B("#"+H));if(E===undefined){E=true}}if(E===false){return}B(".fm-button","#"+H+"_2").hover(function(){B(this).addClass("ui-state-hover")},function(){B(this).removeClass("ui-state-hover")});D.delicon=B.extend([true,"left","ui-icon-scissors"],A[N.p.id].delicon);D.cancelicon=B.extend([true,"left","ui-icon-cancel"],A[N.p.id].cancelicon);if(D.delicon[0]===true){B("#dData","#"+H+"_2").addClass(D.delicon[1]==="right"?"fm-button-icon-right":"fm-button-icon-left").append("<span class='ui-icon "+D.delicon[2]+"'></span>")}if(D.cancelicon[0]===true){B("#eData","#"+H+"_2").addClass(D.cancelicon[1]==="right"?"fm-button-icon-right":"fm-button-icon-left").append("<span class='ui-icon "+D.cancelicon[2]+"'></span>")}B("#dData","#"+H+"_2").click(function(){var b=[true,""],X,Y=B("#DelData>td","#"+H).text();R={};if(B.isFunction(A[N.p.id].onclickSubmit)){R=A[N.p.id].onclickSubmit.call(N,A[N.p.id],Y)||{}}if(B.isFunction(A[N.p.id].beforeSubmit)){b=A[N.p.id].beforeSubmit.call(N,Y)}if(b[0]&&!A[N.p.id].processing){A[N.p.id].processing=true;O=N.p.prmNames;T=B.extend({},A[N.p.id].delData,R);M=O.oper;T[M]=O.deloper;U=O.id;Y=String(Y).split(",");if(!Y.length){return false}for(X in Y){if(Y.hasOwnProperty(X)){Y[X]=B.jgrid.stripPref(N.p.idPrefix,Y[X])}}T[U]=Y.join();B(this).addClass("ui-state-active");var a=B.extend({url:A[N.p.id].url||B(N).jqGrid("getGridParam","editurl"),type:A[N.p.id].mtype,data:B.isFunction(A[N.p.id].serializeDelData)?A[N.p.id].serializeDelData.call(N,T):T,complete:function(g,f){var c;if(g.status>=300&&g.status!==304){b[0]=false;if(B.isFunction(A[N.p.id].errorTextFormat)){b[1]=A[N.p.id].errorTextFormat.call(N,g)}else{b[1]=f+" Status: '"+g.statusText+"'. Error code: "+g.status}}else{if(B.isFunction(A[N.p.id].afterSubmit)){b=A[N.p.id].afterSubmit.call(N,g,T)}}if(b[0]===false){B("#DelError>td","#"+H).html(b[1]);B("#DelError","#"+H).show()}else{if(A[N.p.id].reloadAfterSubmit&&N.p.datatype!=="local"){B(N).trigger("reloadGrid")}else{if(N.p.treeGrid===true){try{B(N).jqGrid("delTreeNode",N.p.idPrefix+Y[0])}catch(d){}}else{for(c=0;c<Y.length;c++){B(N).jqGrid("delRowData",N.p.idPrefix+Y[c])}}N.p.selrow=null;N.p.selarrrow=[]}if(B.isFunction(A[N.p.id].afterComplete)){setTimeout(function(){A[N.p.id].afterComplete.call(N,g,Y)},500)}}A[N.p.id].processing=false;B("#dData","#"+H+"_2").removeClass("ui-state-active");if(b[0]){B.jgrid.hideModal("#"+B.jgrid.jqID(G.themodal),{gb:"#gbox_"+B.jgrid.jqID(K),jqm:D.jqModal,onClose:A[N.p.id].onClose})}}},B.jgrid.ajaxOptions,A[N.p.id].ajaxDelOptions);if(!a.url&&!A[N.p.id].useDataProxy){if(B.isFunction(N.p.dataProxy)){A[N.p.id].useDataProxy=true}else{b[0]=false;b[1]+=" "+B.jgrid.errors.nourl}}if(b[0]){if(A[N.p.id].useDataProxy){var Z=N.p.dataProxy.call(N,a,"del_"+N.p.id);if(Z===undefined){Z=[true,""]}if(Z[0]===false){b[0]=false;b[1]=Z[1]||"Error deleting the selected row!"}else{B.jgrid.hideModal("#"+B.jgrid.jqID(G.themodal),{gb:"#gbox_"+B.jgrid.jqID(K),jqm:D.jqModal,onClose:A[N.p.id].onClose})}}else{B.ajax(a)}}}if(b[0]===false){B("#DelError>td","#"+H).html(b[1]);B("#DelError","#"+H).show()}return false});B("#eData","#"+H+"_2").click(function(){B.jgrid.hideModal("#"+B.jgrid.jqID(G.themodal),{gb:"#gbox_"+B.jgrid.jqID(K),jqm:A[N.p.id].jqModal,onClose:A[N.p.id].onClose});return false});if(P){A[N.p.id].beforeShowForm.call(N,B("#"+H))}B.jgrid.viewModal("#"+B.jgrid.jqID(G.themodal),{gbox:"#gbox_"+B.jgrid.jqID(K),jqm:A[N.p.id].jqModal,overlay:A[N.p.id].overlay,modal:A[N.p.id].modal});if(Q){A[N.p.id].afterShowForm.call(N,B("#"+H))}}if(A[N.p.id].closeOnEscape===true){setTimeout(function(){B(".ui-jqdialog-titlebar-close","#"+B.jgrid.jqID(G.modalhead)).focus()},0)}})},navGrid:function(F,D,G,I,C,H,E){D=B.extend({edit:true,editicon:"ui-icon-pencil",add:true,addicon:"ui-icon-plus",del:true,delicon:"ui-icon-trash",search:true,searchicon:"ui-icon-search",refresh:true,refreshicon:"ui-icon-refresh",refreshstate:"firstpage",view:false,viewicon:"ui-icon-document",position:"left",closeOnEscape:true,beforeRefresh:null,afterRefresh:null,cloneToTop:false,alertwidth:200,alertheight:"auto",alerttop:null,alertleft:null,alertzIndex:null},B.jgrid.nav,D||{});return this.each(function(){if(this.nav){return}var Q={themodal:"alertmod_"+this.p.id,modalhead:"alerthd_"+this.p.id,modalcontent:"alertcnt_"+this.p.id},T=this,J,U;if(!T.grid||typeof F!=="string"){return}if(B("#"+Q.themodal)[0]===undefined){if(!D.alerttop&&!D.alertleft){if(window.innerWidth!==undefined){D.alertleft=window.innerWidth;D.alerttop=window.innerHeight}else{if(document.documentElement!==undefined&&document.documentElement.clientWidth!==undefined&&document.documentElement.clientWidth!==0){D.alertleft=document.documentElement.clientWidth;D.alerttop=document.documentElement.clientHeight}else{D.alertleft=1024;D.alerttop=768}}D.alertleft=D.alertleft/2-parseInt(D.alertwidth,10)/2;D.alerttop=D.alerttop/2-25}B.jgrid.createModal(Q,"<div>"+D.alerttext+"</div><span tabindex='0'><span tabindex='-1' id='jqg_alrt'></span></span>",{gbox:"#gbox_"+B.jgrid.jqID(T.p.id),jqModal:true,drag:true,resize:true,caption:D.alertcap,top:D.alerttop,left:D.alertleft,width:D.alertwidth,height:D.alertheight,closeOnEscape:D.closeOnEscape,zIndex:D.alertzIndex},"#gview_"+B.jgrid.jqID(T.p.id),B("#gbox_"+B.jgrid.jqID(T.p.id))[0],true)}var K=1,S,R=function(){if(!B(this).hasClass("ui-state-disabled")){B(this).addClass("ui-state-hover")}},P=function(){B(this).removeClass("ui-state-hover")};if(D.cloneToTop&&T.p.toppager){K=2}for(S=0;S<K;S++){var O,M=B("<table cellspacing='0' cellpadding='0' border='0' class='ui-pg-table navtable' style='float:left;table-layout:auto;'><tbody><tr></tr></tbody></table>"),N="<td class='ui-pg-button ui-state-disabled' style='width:4px;'><span class='ui-separator'></span></td>",V,L;if(S===0){V=F;L=T.p.id;if(V===T.p.toppager){L+="_top";K=1}}else{V=T.p.toppager;L=T.p.id+"_top"}if(T.p.direction==="rtl"){B(M).attr("dir","rtl").css("float","right")}if(D.add){I=I||{};O=B("<td class='ui-pg-button ui-corner-all'></td>");B(O).append("<div class='ui-pg-div'><span class='ui-icon "+D.addicon+"'></span>"+D.addtext+"</div>");B("tr",M).append(O);B(O,M).attr({"title":D.addtitle||"",id:I.id||"add_"+L}).click(function(){if(!B(this).hasClass("ui-state-disabled")){if(B.isFunction(D.addfunc)){D.addfunc.call(T)}else{B(T).jqGrid("editGridRow","new",I)}}return false}).hover(R,P);O=null}if(D.edit){O=B("<td class='ui-pg-button ui-corner-all'></td>");G=G||{};B(O).append("<div class='ui-pg-div'><span class='ui-icon "+D.editicon+"'></span>"+D.edittext+"</div>");B("tr",M).append(O);B(O,M).attr({"title":D.edittitle||"",id:G.id||"edit_"+L}).click(function(){if(!B(this).hasClass("ui-state-disabled")){var W=T.p.selrow;if(W){if(B.isFunction(D.editfunc)){D.editfunc.call(T,W)}else{B(T).jqGrid("editGridRow",W,G)}}else{B.jgrid.viewModal("#"+Q.themodal,{gbox:"#gbox_"+B.jgrid.jqID(T.p.id),jqm:true});B("#jqg_alrt").focus()}}return false}).hover(R,P);O=null}if(D.view){O=B("<td class='ui-pg-button ui-corner-all'></td>");E=E||{};B(O).append("<div class='ui-pg-div'><span class='ui-icon "+D.viewicon+"'></span>"+D.viewtext+"</div>");B("tr",M).append(O);B(O,M).attr({"title":D.viewtitle||"",id:E.id||"view_"+L}).click(function(){if(!B(this).hasClass("ui-state-disabled")){var W=T.p.selrow;if(W){if(B.isFunction(D.viewfunc)){D.viewfunc.call(T,W)}else{B(T).jqGrid("viewGridRow",W,E)}}else{B.jgrid.viewModal("#"+Q.themodal,{gbox:"#gbox_"+B.jgrid.jqID(T.p.id),jqm:true});B("#jqg_alrt").focus()}}return false}).hover(R,P);O=null}if(D.del){O=B("<td class='ui-pg-button ui-corner-all'></td>");C=C||{};B(O).append("<div class='ui-pg-div'><span class='ui-icon "+D.delicon+"'></span>"+D.deltext+"</div>");B("tr",M).append(O);B(O,M).attr({"title":D.deltitle||"",id:C.id||"del_"+L}).click(function(){if(!B(this).hasClass("ui-state-disabled")){var W;if(T.p.multiselect){W=T.p.selarrrow;if(W.length===0){W=null}}else{W=T.p.selrow}if(W){if(B.isFunction(D.delfunc)){D.delfunc.call(T,W)}else{B(T).jqGrid("delGridRow",W,C)}}else{B.jgrid.viewModal("#"+Q.themodal,{gbox:"#gbox_"+B.jgrid.jqID(T.p.id),jqm:true});B("#jqg_alrt").focus()}}return false}).hover(R,P);O=null}if(D.add||D.edit||D.del||D.view){B("tr",M).append(N)}if(D.search){O=B("<td class='ui-pg-button ui-corner-all'></td>");H=H||{};B(O).append("<div class='ui-pg-div'><span class='ui-icon "+D.searchicon+"'></span>"+D.searchtext+"</div>");B("tr",M).append(O);B(O,M).attr({"title":D.searchtitle||"",id:H.id||"search_"+L}).click(function(){if(!B(this).hasClass("ui-state-disabled")){if(B.isFunction(D.searchfunc)){D.searchfunc.call(T,H)}else{B(T).jqGrid("searchGrid",H)}}return false}).hover(R,P);if(H.showOnLoad&&H.showOnLoad===true){B(O,M).click()}O=null}if(D.refresh){O=B("<td class='ui-pg-button ui-corner-all'></td>");B(O).append("<div class='ui-pg-div'><span class='ui-icon "+D.refreshicon+"'></span>"+D.refreshtext+"</div>");B("tr",M).append(O);B(O,M).attr({"title":D.refreshtitle||"",id:"refresh_"+L}).click(function(){if(!B(this).hasClass("ui-state-disabled")){if(B.isFunction(D.beforeRefresh)){D.beforeRefresh.call(T)}T.p.search=false;T.p.resetsearch=true;try{var X=T.p.id;T.p.postData.filters="";try{B("#fbox_"+B.jgrid.jqID(X)).jqFilter("resetFilter")}catch(Y){}if(B.isFunction(T.clearToolbar)){T.clearToolbar.call(T,false)}}catch(W){}switch(D.refreshstate){case"firstpage":B(T).trigger("reloadGrid",[{page:1}]);break;case"current":B(T).trigger("reloadGrid",[{current:true}]);break}if(B.isFunction(D.afterRefresh)){D.afterRefresh.call(T)}}return false}).hover(R,P);O=null}U=B(".ui-jqgrid").css("font-size")||"11px";B("body").append("<div id='testpg2' class='ui-jqgrid ui-widget ui-widget-content' style='font-size:"+U+";visibility:hidden;' ></div>");J=B(M).clone().appendTo("#testpg2").width();B("#testpg2").remove();B(V+"_"+D.position,V).append(M);if(T.p._nvtd){if(J>T.p._nvtd[0]){B(V+"_"+D.position,V).width(J);T.p._nvtd[0]=J}T.p._nvtd[1]=J}U=null;J=null;M=null;this.nav=true}})},navButtonAdd:function(D,C){C=B.extend({caption:"newButton",title:"",buttonicon:"ui-icon-newwin",onClickButton:null,position:"last",cursor:"pointer"},C||{});return this.each(function(){if(!this.grid){return}if(typeof D==="string"&&D.indexOf("#")!==0){D="#"+B.jgrid.jqID(D)}var E=B(".navtable",D)[0],G=this;if(E){if(C.id&&B("#"+B.jgrid.jqID(C.id),E)[0]!==undefined){return}var F=B("<td></td>");if(C.buttonicon.toString().toUpperCase()==="NONE"){B(F).addClass("ui-pg-button ui-corner-all").append("<div class='ui-pg-div'>"+C.caption+"</div>")}else{B(F).addClass("ui-pg-button ui-corner-all").append("<div class='ui-pg-div'><span class='ui-icon "+C.buttonicon+"'></span>"+C.caption+"</div>")}if(C.id){B(F).attr("id",C.id)}if(C.position==="first"){if(E.rows[0].cells.length===0){B("tr",E).append(F)}else{B("tr td:eq(0)",E).before(F)}}else{B("tr",E).append(F)}B(F,E).attr("title",C.title||"").click(function(H){if(!B(this).hasClass("ui-state-disabled")){if(B.isFunction(C.onClickButton)){C.onClickButton.call(G,H)}}return false}).hover(function(){if(!B(this).hasClass("ui-state-disabled")){B(this).addClass("ui-state-hover")}},function(){B(this).removeClass("ui-state-hover")})}})},navSeparatorAdd:function(D,C){C=B.extend({sepclass:"ui-separator",sepcontent:"",position:"last"},C||{});return this.each(function(){if(!this.grid){return}if(typeof D==="string"&&D.indexOf("#")!==0){D="#"+B.jgrid.jqID(D)}var E=B(".navtable",D)[0];if(E){var F="<td class='ui-pg-button ui-state-disabled' style='width:4px;'><span class='"+C.sepclass+"'></span>"+C.sepcontent+"</td>";if(C.position==="first"){if(E.rows[0].cells.length===0){B("tr",E).append(F)}else{B("tr td:eq(0)",E).before(F)}}else{B("tr",E).append(F)}}})},GridToForm:function(D,C){return this.each(function(){var G=this,E;if(!G.grid){return}var F=B(G).jqGrid("getRowData",D);if(F){for(E in F){if(F.hasOwnProperty(E)){if(B("[name="+B.jgrid.jqID(E)+"]",C).is("input:radio")||B("[name="+B.jgrid.jqID(E)+"]",C).is("input:checkbox")){B("[name="+B.jgrid.jqID(E)+"]",C).each(function(){if(B(this).val()==F[E]){B(this)[G.p.useProp?"prop":"attr"]("checked",true)}else{B(this)[G.p.useProp?"prop":"attr"]("checked",false)}})}else{B("[name="+B.jgrid.jqID(E)+"]",C).val(F[E])}}}}})},FormToGrid:function(F,C,D,E){return this.each(function(){var I=this;if(!I.grid){return}if(!D){D="set"}if(!E){E="first"}var H=B(C).serializeArray();var G={};B.each(H,function(J,K){G[K.name]=K.value});if(D==="add"){B(I).jqGrid("addRowData",F,G,E)}else{if(D==="set"){B(I).jqGrid("setRowData",F,G)}}})}})})(jQuery);(function(A){A.jgrid.inlineEdit=A.jgrid.inlineEdit||{};A.jgrid.extend({editRow:function(K,H,E,B,I,J,C,F,L){var G={},D=A.makeArray(arguments).slice(1);if(A.type(D[0])==="object"){G=D[0]}else{if(H!==undefined){G.keys=H}if(A.isFunction(E)){G.oneditfunc=E}if(A.isFunction(B)){G.successfunc=B}if(I!==undefined){G.url=I}if(J!==undefined){G.extraparam=J}if(A.isFunction(C)){G.aftersavefunc=C}if(A.isFunction(F)){G.errorfunc=F}if(A.isFunction(L)){G.afterrestorefunc=L}}G=A.extend(true,{keys:false,oneditfunc:null,successfunc:null,url:null,extraparam:{},aftersavefunc:null,errorfunc:null,afterrestorefunc:null,restoreAfterError:true,mtype:"POST"},A.jgrid.inlineEdit,G);return this.each(function(){var S=this,M,Q,N,T=0,P=null,R={},V,O,U;if(!S.grid){return}V=A(S).jqGrid("getInd",K,true);if(V===false){return}U=A.isFunction(G.beforeEditRow)?G.beforeEditRow.call(S,G,K):undefined;if(U===undefined){U=true}if(!U){return}N=A(V).attr("editable")||"0";if(N==="0"&&!A(V).hasClass("not-editable-row")){O=S.p.colModel;A('td[role="gridcell"]',V).each(function(X){M=O[X].name;var W=S.p.treeGrid===true&&M===S.p.ExpandColumn;if(W){Q=A("span:first",this).html()}else{try{Q=A.unformat.call(S,this,{rowId:K,colModel:O[X]},X)}catch(a){Q=(O[X].edittype&&O[X].edittype==="textarea")?A(this).text():A(this).html()}}if(M!=="cb"&&M!=="subgrid"&&M!=="rn"){if(S.p.autoencode){Q=A.jgrid.htmlDecode(Q)}R[M]=Q;if(O[X].editable===true){if(P===null){P=X}if(W){A("span:first",this).html("")}else{A(this).html("")}var Y=A.extend({},O[X].editoptions||{},{id:K+"_"+M,name:M});if(!O[X].edittype){O[X].edittype="text"}if(Q==="&nbsp;"||Q==="&#160;"||(Q.length===1&&Q.charCodeAt(0)===160)){Q=""}var Z=A.jgrid.createEl.call(S,O[X].edittype,Y,Q,true,A.extend({},A.jgrid.ajaxOptions,S.p.ajaxSelectOptions||{}));A(Z).addClass("editable");if(W){A("span:first",this).append(Z)}else{A(this).append(Z)}A.jgrid.bindEv.call(S,Z,Y);if(O[X].edittype==="select"&&O[X].editoptions!==undefined&&O[X].editoptions.multiple===true&&O[X].editoptions.dataUrl===undefined&&A.jgrid.msie){A(Z).width(A(Z).width())}T++}}});if(T>0){R.id=K;S.p.savedRow.push(R);A(V).attr("editable","1");setTimeout(function(){A("td:eq("+P+") input",V).focus()},0);if(G.keys===true){A(V).bind("keydown",function(W){if(W.keyCode===27){A(S).jqGrid("restoreRow",K,G.afterrestorefunc);if(S.p._inlinenav){try{A(S).jqGrid("showAddEditButtons")}catch(X){}}return false}if(W.keyCode===13){var Y=W.target;if(Y.tagName==="TEXTAREA"){return true}if(A(S).jqGrid("saveRow",K,G)){if(S.p._inlinenav){try{A(S).jqGrid("showAddEditButtons")}catch(Z){}}}return false}})}A(S).triggerHandler("jqGridInlineEditRow",[K,G]);if(A.isFunction(G.oneditfunc)){G.oneditfunc.call(S,K)}}}})},saveRow:function(U,c,C,T,b,d,O){var B=A.makeArray(arguments).slice(1),L={};if(A.type(B[0])==="object"){L=B[0]}else{if(A.isFunction(c)){L.successfunc=c}if(C!==undefined){L.url=C}if(T!==undefined){L.extraparam=T}if(A.isFunction(b)){L.aftersavefunc=b}if(A.isFunction(d)){L.errorfunc=d}if(A.isFunction(O)){L.afterrestorefunc=O}}L=A.extend(true,{successfunc:null,url:null,extraparam:{},aftersavefunc:null,errorfunc:null,afterrestorefunc:null,restoreAfterError:true,mtype:"POST"},A.jgrid.inlineEdit,L);var Y=false;var M=this[0],S,F={},Z={},K={},g,G,H,W;if(!M.grid){return Y}W=A(M).jqGrid("getInd",U,true);if(W===false){return Y}var D=A.isFunction(L.beforeSaveRow)?L.beforeSaveRow.call(M,L,U):undefined;if(D===undefined){D=true}if(!D){return}g=A(W).attr("editable");L.url=L.url||M.p.editurl;if(g==="1"){var X;A('td[role="gridcell"]',W).each(function(j){X=M.p.colModel[j];S=X.name;if(S!=="cb"&&S!=="subgrid"&&X.editable===true&&S!=="rn"&&!A(this).hasClass("not-editable-cell")){switch(X.edittype){case"checkbox":var l=["Yes","No"];if(X.editoptions){l=X.editoptions.value.split(":")}F[S]=A("input",this).is(":checked")?l[0]:l[1];break;case"text":case"password":case"textarea":case"button":F[S]=A("input, textarea",this).val();break;case"select":if(!X.editoptions.multiple){F[S]=A("select option:selected",this).val();Z[S]=A("select option:selected",this).text()}else{var m=A("select",this),n=[];F[S]=A(m).val();if(F[S]){F[S]=F[S].join(",")}else{F[S]=""}A("select option:selected",this).each(function(e,o){n[e]=A(o).text()});Z[S]=n.join(",")}if(X.formatter&&X.formatter==="select"){Z={}}break;case"custom":try{if(X.editoptions&&A.isFunction(X.editoptions.custom_value)){F[S]=X.editoptions.custom_value.call(M,A(".customelement",this),"get");if(F[S]===undefined){throw"e2"}}else{throw"e1"}}catch(k){if(k==="e1"){A.jgrid.info_dialog(A.jgrid.errors.errcap,"function 'custom_value' "+A.jgrid.edit.msg.nodefined,A.jgrid.edit.bClose)}if(k==="e2"){A.jgrid.info_dialog(A.jgrid.errors.errcap,"function 'custom_value' "+A.jgrid.edit.msg.novalue,A.jgrid.edit.bClose)}else{A.jgrid.info_dialog(A.jgrid.errors.errcap,k.message,A.jgrid.edit.bClose)}}break}H=A.jgrid.checkValues.call(M,F[S],j);if(H[0]===false){return false}if(M.p.autoencode){F[S]=A.jgrid.htmlEncode(F[S])}if(L.url!=="clientArray"&&X.editoptions&&X.editoptions.NullIfEmpty===true){if(F[S]===""){K[S]="null"}}}});if(H[0]===false){try{var V=A(M).jqGrid("getGridRowById",U),a=A.jgrid.findPos(V);A.jgrid.info_dialog(A.jgrid.errors.errcap,H[1],A.jgrid.edit.bClose,{left:a[0],top:a[1]+A(V).outerHeight()})}catch(Q){alert(H[1])}return Y}var f,I=M.p.prmNames,N=U;if(M.p.keyIndex===false){f=I.id}else{f=M.p.colModel[M.p.keyIndex+(M.p.rownumbers===true?1:0)+(M.p.multiselect===true?1:0)+(M.p.subGrid===true?1:0)].name}if(F){F[I.oper]=I.editoper;if(F[f]===undefined||F[f]===""){F[f]=U}else{if(W.id!==M.p.idPrefix+F[f]){var h=A.jgrid.stripPref(M.p.idPrefix,U);if(M.p._index[h]!==undefined){M.p._index[F[f]]=M.p._index[h];delete M.p._index[h]}U=M.p.idPrefix+F[f];A(W).attr("id",U);if(M.p.selrow===N){M.p.selrow=U}if(A.isArray(M.p.selarrrow)){var J=A.inArray(N,M.p.selarrrow);if(J>=0){M.p.selarrrow[J]=U}}if(M.p.multiselect){var R="jqg_"+M.p.id+"_"+U;A("input.cbox",W).attr("id",R).attr("name",R)}}}if(M.p.inlineData===undefined){M.p.inlineData={}}F=A.extend({},F,M.p.inlineData,L.extraparam)}if(L.url==="clientArray"){F=A.extend({},F,Z);if(M.p.autoencode){A.each(F,function(e,i){F[e]=A.jgrid.htmlDecode(i)})}var P,E=A(M).jqGrid("setRowData",U,F);A(W).attr("editable","0");for(P=0;P<M.p.savedRow.length;P++){if(String(M.p.savedRow[P].id)===String(N)){G=P;break}}if(G>=0){M.p.savedRow.splice(G,1)}A(M).triggerHandler("jqGridInlineAfterSaveRow",[U,E,F,L]);if(A.isFunction(L.aftersavefunc)){L.aftersavefunc.call(M,U,E,L)}Y=true;A(W).removeClass("jqgrid-new-row").unbind("keydown")}else{A("#lui_"+A.jgrid.jqID(M.p.id)).show();K=A.extend({},F,K);K[f]=A.jgrid.stripPref(M.p.idPrefix,K[f]);A.ajax(A.extend({url:L.url,data:A.isFunction(M.p.serializeRowData)?M.p.serializeRowData.call(M,K):K,type:L.mtype,async:false,complete:function(i,l){A("#lui_"+A.jgrid.jqID(M.p.id)).hide();if(l==="success"){var j=true,e,m;e=A(M).triggerHandler("jqGridInlineSuccessSaveRow",[i,U,L]);if(!A.isArray(e)){e=[true,F]}if(e[0]&&A.isFunction(L.successfunc)){e=L.successfunc.call(M,i)}if(A.isArray(e)){j=e[0];F=e[1]||F}else{j=e}if(j===true){if(M.p.autoencode){A.each(F,function(k,o){F[k]=A.jgrid.htmlDecode(o)})}F=A.extend({},F,Z);A(M).jqGrid("setRowData",U,F);A(W).attr("editable","0");for(m=0;m<M.p.savedRow.length;m++){if(String(M.p.savedRow[m].id)===String(U)){G=m;break}}if(G>=0){M.p.savedRow.splice(G,1)}A(M).triggerHandler("jqGridInlineAfterSaveRow",[U,i,F,L]);if(A.isFunction(L.aftersavefunc)){L.aftersavefunc.call(M,U,i)}Y=true;A(W).removeClass("jqgrid-new-row").unbind("keydown")}else{A(M).triggerHandler("jqGridInlineErrorSaveRow",[U,i,l,null,L]);if(A.isFunction(L.errorfunc)){L.errorfunc.call(M,U,i,l,null)}if(L.restoreAfterError===true){A(M).jqGrid("restoreRow",U,L.afterrestorefunc)}}}},error:function(j,l,k){A("#lui_"+A.jgrid.jqID(M.p.id)).hide();A(M).triggerHandler("jqGridInlineErrorSaveRow",[U,j,l,k,L]);if(A.isFunction(L.errorfunc)){L.errorfunc.call(M,U,j,l,k)}else{var m=j.responseText||j.statusText;try{A.jgrid.info_dialog(A.jgrid.errors.errcap,'<div class="ui-state-error">'+m+"</div>",A.jgrid.edit.bClose,{buttonalign:"right"})}catch(i){alert(m)}}if(L.restoreAfterError===true){A(M).jqGrid("restoreRow",U,L.afterrestorefunc)}}},A.jgrid.ajaxOptions,M.p.ajaxRowOptions||{}))}}return Y},restoreRow:function(E,B){var D=A.makeArray(arguments).slice(1),C={};if(A.type(D[0])==="object"){C=D[0]}else{if(A.isFunction(B)){C.afterrestorefunc=B}}C=A.extend(true,{},A.jgrid.inlineEdit,C);return this.each(function(){var L=this,K=-1,I,G={},F;if(!L.grid){return}I=A(L).jqGrid("getInd",E,true);if(I===false){return}var H=A.isFunction(C.beforeCancelRow)?C.beforeCancelRow.call(L,C,sr):undefined;if(H===undefined){H=true}if(!H){return}for(F=0;F<L.p.savedRow.length;F++){if(String(L.p.savedRow[F].id)===String(E)){K=F;break}}if(K>=0){if(A.isFunction(A.fn.datepicker)){try{A("input.hasDatepicker","#"+A.jgrid.jqID(I.id)).datepicker("hide")}catch(J){}}A.each(L.p.colModel,function(){if(this.editable===true&&L.p.savedRow[K].hasOwnProperty(this.name)){G[this.name]=L.p.savedRow[K][this.name]}});A(L).jqGrid("setRowData",E,G);A(I).attr("editable","0").unbind("keydown");L.p.savedRow.splice(K,1);if(A("#"+A.jgrid.jqID(E),"#"+A.jgrid.jqID(L.p.id)).hasClass("jqgrid-new-row")){setTimeout(function(){A(L).jqGrid("delRowData",E);A(L).jqGrid("showAddEditButtons")},0)}}A(L).triggerHandler("jqGridInlineAfterRestoreRow",[E]);if(A.isFunction(C.afterrestorefunc)){C.afterrestorefunc.call(L,E)}})},addRow:function(B){B=A.extend(true,{rowID:null,initdata:{},position:"first",useDefValues:true,useFormatter:false,addRowParams:{extraparam:{}}},B||{});return this.each(function(){if(!this.grid){return}var F=this;var C=A.isFunction(B.beforeAddRow)?B.beforeAddRow.call(F,B.addRowParams):undefined;if(C===undefined){C=true}if(!C){return}B.rowID=A.isFunction(B.rowID)?B.rowID.call(F,B):((B.rowID!=null)?B.rowID:A.jgrid.randId());if(B.useDefValues===true){A(F.p.colModel).each(function(){if(this.editoptions&&this.editoptions.defaultValue){var H=this.editoptions.defaultValue,G=A.isFunction(H)?H.call(F):H;B.initdata[this.name]=G}})}A(F).jqGrid("addRowData",B.rowID,B.initdata,B.position);B.rowID=F.p.idPrefix+B.rowID;A("#"+A.jgrid.jqID(B.rowID),"#"+A.jgrid.jqID(F.p.id)).addClass("jqgrid-new-row");if(B.useFormatter){A("#"+A.jgrid.jqID(B.rowID)+" .ui-inline-edit","#"+A.jgrid.jqID(F.p.id)).click()}else{var D=F.p.prmNames,E=D.oper;B.addRowParams.extraparam[E]=D.addoper;A(F).jqGrid("editRow",B.rowID,B.addRowParams);A(F).jqGrid("setSelection",B.rowID)}})},inlineNav:function(C,B){B=A.extend(true,{edit:true,editicon:"ui-icon-pencil",add:true,addicon:"ui-icon-plus",save:true,saveicon:"ui-icon-disk",cancel:true,cancelicon:"ui-icon-cancel",addParams:{addRowParams:{extraparam:{}}},editParams:{},restoreAfterSelect:true},A.jgrid.nav,B||{});return this.each(function(){if(!this.grid){return}var J=this,I,G=A.jgrid.jqID(J.p.id);J.p._inlinenav=true;if(B.addParams.useFormatter===true){var H=J.p.colModel,E;for(E=0;E<H.length;E++){if(H[E].formatter&&H[E].formatter==="actions"){if(H[E].formatoptions){var D={keys:false,onEdit:null,onSuccess:null,afterSave:null,onError:null,afterRestore:null,extraparam:{},url:null},F=A.extend(D,H[E].formatoptions);B.addParams.addRowParams={"keys":F.keys,"oneditfunc":F.onEdit,"successfunc":F.onSuccess,"url":F.url,"extraparam":F.extraparam,"aftersavefunc":F.afterSave,"errorfunc":F.onError,"afterrestorefunc":F.afterRestore}}break}}}if(B.add){A(J).jqGrid("navButtonAdd",C,{caption:B.addtext,title:B.addtitle,buttonicon:B.addicon,id:J.p.id+"_iladd",onClickButton:function(){A(J).jqGrid("addRow",B.addParams);if(!B.addParams.useFormatter){A("#"+G+"_ilsave").removeClass("ui-state-disabled");A("#"+G+"_ilcancel").removeClass("ui-state-disabled");A("#"+G+"_iladd").addClass("ui-state-disabled");A("#"+G+"_iledit").addClass("ui-state-disabled")}}})}if(B.edit){A(J).jqGrid("navButtonAdd",C,{caption:B.edittext,title:B.edittitle,buttonicon:B.editicon,id:J.p.id+"_iledit",onClickButton:function(){var K=A(J).jqGrid("getGridParam","selrow");if(K){A(J).jqGrid("editRow",K,B.editParams);A("#"+G+"_ilsave").removeClass("ui-state-disabled");A("#"+G+"_ilcancel").removeClass("ui-state-disabled");A("#"+G+"_iladd").addClass("ui-state-disabled");A("#"+G+"_iledit").addClass("ui-state-disabled")}else{A.jgrid.viewModal("#alertmod",{gbox:"#gbox_"+G,jqm:true});A("#jqg_alrt").focus()}}})}if(B.save){A(J).jqGrid("navButtonAdd",C,{caption:B.savetext||"",title:B.savetitle||"Save row",buttonicon:B.saveicon,id:J.p.id+"_ilsave",onClickButton:function(){var N=J.p.savedRow[0].id;if(N){var L=J.p.prmNames,M=L.oper,K=B.editParams;if(A("#"+A.jgrid.jqID(N),"#"+G).hasClass("jqgrid-new-row")){B.addParams.addRowParams.extraparam[M]=L.addoper;K=B.addParams.addRowParams}else{if(!B.editParams.extraparam){B.editParams.extraparam={}}B.editParams.extraparam[M]=L.editoper}if(A(J).jqGrid("saveRow",N,K)){A(J).jqGrid("showAddEditButtons")}}else{A.jgrid.viewModal("#alertmod",{gbox:"#gbox_"+G,jqm:true});A("#jqg_alrt").focus()}}});A("#"+G+"_ilsave").addClass("ui-state-disabled")}if(B.cancel){A(J).jqGrid("navButtonAdd",C,{caption:B.canceltext||"",title:B.canceltitle||"Cancel row editing",buttonicon:B.cancelicon,id:J.p.id+"_ilcancel",onClickButton:function(){var L=J.p.savedRow[0].id,K=B.editParams;if(L){if(A("#"+A.jgrid.jqID(L),"#"+G).hasClass("jqgrid-new-row")){K=B.addParams.addRowParams}A(J).jqGrid("restoreRow",L,K);A(J).jqGrid("showAddEditButtons")}else{A.jgrid.viewModal("#alertmod",{gbox:"#gbox_"+G,jqm:true});A("#jqg_alrt").focus()}}});A("#"+G+"_ilcancel").addClass("ui-state-disabled")}if(B.restoreAfterSelect===true){if(A.isFunction(J.p.beforeSelectRow)){I=J.p.beforeSelectRow}else{I=false}J.p.beforeSelectRow=function(M,L){var K=true;if(J.p.savedRow.length>0&&J.p._inlinenav===true&&(M!==J.p.selrow&&J.p.selrow!==null)){if(J.p.selrow===B.addParams.rowID){A(J).jqGrid("delRowData",J.p.selrow)}else{A(J).jqGrid("restoreRow",J.p.selrow,B.editParams)}A(J).jqGrid("showAddEditButtons")}if(I){K=I.call(J,M,L)}return K}}})},showAddEditButtons:function(){return this.each(function(){if(!this.grid){return}var B=A.jgrid.jqID(this.p.id);A("#"+B+"_ilsave").addClass("ui-state-disabled");A("#"+B+"_ilcancel").addClass("ui-state-disabled");A("#"+B+"_iladd").removeClass("ui-state-disabled");A("#"+B+"_iledit").removeClass("ui-state-disabled")})}})})(jQuery);(function(A){A.jgrid.extend({editCell:function(C,B,D){return this.each(function(){var M=this,F,J,E,I;if(!M.grid||M.p.cellEdit!==true){return}B=parseInt(B,10);M.p.selrow=M.rows[C].id;if(!M.p.knv){A(M).jqGrid("GridNav")}if(M.p.savedRow.length>0){if(D===true){if(C==M.p.iRow&&B==M.p.iCol){return}}A(M).jqGrid("saveCell",M.p.savedRow[0].id,M.p.savedRow[0].ic)}else{window.setTimeout(function(){A("#"+A.jgrid.jqID(M.p.knv)).attr("tabindex","-1").focus()},0)}I=M.p.colModel[B];F=I.name;if(F==="subgrid"||F==="cb"||F==="rn"){return}E=A("td:eq("+B+")",M.rows[C]);if(I.editable===true&&D===true&&!E.hasClass("not-editable-cell")){if(parseInt(M.p.iCol,10)>=0&&parseInt(M.p.iRow,10)>=0){A("td:eq("+M.p.iCol+")",M.rows[M.p.iRow]).removeClass("edit-cell ui-state-highlight");A(M.rows[M.p.iRow]).removeClass("selected-row ui-state-hover")}A(E).addClass("edit-cell ui-state-highlight");A(M.rows[C]).addClass("selected-row ui-state-hover");try{J=A.unformat.call(M,E,{rowId:M.rows[C].id,colModel:I},B)}catch(K){J=(I.edittype&&I.edittype==="textarea")?A(E).text():A(E).html()}if(M.p.autoencode){J=A.jgrid.htmlDecode(J)}if(!I.edittype){I.edittype="text"}M.p.savedRow.push({id:C,ic:B,name:F,v:J});if(J==="&nbsp;"||J==="&#160;"||(J.length===1&&J.charCodeAt(0)===160)){J=""}if(A.isFunction(M.p.formatCell)){var H=M.p.formatCell.call(M,M.rows[C].id,F,J,C,B);if(H!==undefined){J=H}}A(M).triggerHandler("jqGridBeforeEditCell",[M.rows[C].id,F,J,C,B]);if(A.isFunction(M.p.beforeEditCell)){M.p.beforeEditCell.call(M,M.rows[C].id,F,J,C,B)}var G=A.extend({},I.editoptions||{},{id:C+"_"+F,name:F});var L=A.jgrid.createEl.call(M,I.edittype,G,J,true,A.extend({},A.jgrid.ajaxOptions,M.p.ajaxSelectOptions||{}));A(E).html("").append(L).attr("tabindex","0");A.jgrid.bindEv.call(M,L,G);window.setTimeout(function(){A(L).focus()},0);A("input, select, textarea",E).bind("keydown",function(N){if(N.keyCode===27){if(A("input.hasDatepicker",E).length>0){if(A(".ui-datepicker").is(":hidden")){A(M).jqGrid("restoreCell",C,B)}else{A("input.hasDatepicker",E).datepicker("hide")}}else{A(M).jqGrid("restoreCell",C,B)}}if(N.keyCode===13){A(M).jqGrid("saveCell",C,B);return false}if(N.keyCode===9){if(!M.grid.hDiv.loading){if(N.shiftKey){A(M).jqGrid("prevCell",C,B)}else{A(M).jqGrid("nextCell",C,B)}}else{return false}}N.stopPropagation()});A(M).triggerHandler("jqGridAfterEditCell",[M.rows[C].id,F,J,C,B]);if(A.isFunction(M.p.afterEditCell)){M.p.afterEditCell.call(M,M.rows[C].id,F,J,C,B)}}else{if(parseInt(M.p.iCol,10)>=0&&parseInt(M.p.iRow,10)>=0){A("td:eq("+M.p.iCol+")",M.rows[M.p.iRow]).removeClass("edit-cell ui-state-highlight");A(M.rows[M.p.iRow]).removeClass("selected-row ui-state-hover")}E.addClass("edit-cell ui-state-highlight");A(M.rows[C]).addClass("selected-row ui-state-hover");J=E.html().replace(/\&#160\;/ig,"");A(M).triggerHandler("jqGridSelectCell",[M.rows[C].id,F,J,C,B]);if(A.isFunction(M.p.onSelectCell)){M.p.onSelectCell.call(M,M.rows[C].id,F,J,C,B)}}M.p.iCol=B;M.p.iRow=C})},saveCell:function(C,B){return this.each(function(){var O=this,W;if(!O.grid||O.p.cellEdit!==true){return}if(O.p.savedRow.length>=1){W=0}else{W=null}if(W!==null){var P=A("td:eq("+B+")",O.rows[C]),E,G,R=O.p.colModel[B],N=R.name,F=A.jgrid.jqID(N);switch(R.edittype){case"select":if(!R.editoptions.multiple){E=A("#"+C+"_"+F+" option:selected",O.rows[C]).val();G=A("#"+C+"_"+F+" option:selected",O.rows[C]).text()}else{var S=A("#"+C+"_"+F,O.rows[C]),V=[];E=A(S).val();if(E){E.join(",")}else{E=""}A("option:selected",S).each(function(X,Y){V[X]=A(Y).text()});G=V.join(",")}if(R.formatter){G=E}break;case"checkbox":var Q=["Yes","No"];if(R.editoptions){Q=R.editoptions.value.split(":")}E=A("#"+C+"_"+F,O.rows[C]).is(":checked")?Q[0]:Q[1];G=E;break;case"password":case"text":case"textarea":case"button":E=A("#"+C+"_"+F,O.rows[C]).val();G=E;break;case"custom":try{if(R.editoptions&&A.isFunction(R.editoptions.custom_value)){E=R.editoptions.custom_value.call(O,A(".customelement",P),"get");if(E===undefined){throw"e2"}else{G=E}}else{throw"e1"}}catch(L){if(L==="e1"){A.jgrid.info_dialog(A.jgrid.errors.errcap,"function 'custom_value' "+A.jgrid.edit.msg.nodefined,A.jgrid.edit.bClose)}if(L==="e2"){A.jgrid.info_dialog(A.jgrid.errors.errcap,"function 'custom_value' "+A.jgrid.edit.msg.novalue,A.jgrid.edit.bClose)}else{A.jgrid.info_dialog(A.jgrid.errors.errcap,L.message,A.jgrid.edit.bClose)}}break}if(G!==O.p.savedRow[W].v){var K=A(O).triggerHandler("jqGridBeforeSaveCell",[O.rows[C].id,N,E,C,B]);if(K){E=K;G=K}if(A.isFunction(O.p.beforeSaveCell)){var U=O.p.beforeSaveCell.call(O,O.rows[C].id,N,E,C,B);if(U){E=U;G=U}}var H=A.jgrid.checkValues.call(O,E,B);if(H[0]===true){var J=A(O).triggerHandler("jqGridBeforeSubmitCell",[O.rows[C].id,N,E,C,B])||{};if(A.isFunction(O.p.beforeSubmitCell)){J=O.p.beforeSubmitCell.call(O,O.rows[C].id,N,E,C,B);if(!J){J={}}}if(A("input.hasDatepicker",P).length>0){A("input.hasDatepicker",P).datepicker("hide")}if(O.p.cellsubmit==="remote"){if(O.p.cellurl){var D={};if(O.p.autoencode){E=A.jgrid.htmlEncode(E)}D[N]=E;var M,I,T;T=O.p.prmNames;M=T.id;I=T.oper;D[M]=A.jgrid.stripPref(O.p.idPrefix,O.rows[C].id);D[I]=T.editoper;D=A.extend(J,D);A("#lui_"+A.jgrid.jqID(O.p.id)).show();O.grid.hDiv.loading=true;A.ajax(A.extend({url:O.p.cellurl,data:A.isFunction(O.p.serializeCellData)?O.p.serializeCellData.call(O,D):D,type:"POST",complete:function(X,Z){A("#lui_"+O.p.id).hide();O.grid.hDiv.loading=false;if(Z==="success"){var Y=A(O).triggerHandler("jqGridAfterSubmitCell",[O,X,D.id,N,E,C,B])||[true,""];if(Y[0]===true&&A.isFunction(O.p.afterSubmitCell)){Y=O.p.afterSubmitCell.call(O,X,D.id,N,E,C,B)}if(Y[0]===true){A(P).empty();A(O).jqGrid("setCell",O.rows[C].id,B,G,false,false,true);A(P).addClass("dirty-cell");A(O.rows[C]).addClass("edited");A(O).triggerHandler("jqGridAfterSaveCell",[O.rows[C].id,N,E,C,B]);if(A.isFunction(O.p.afterSaveCell)){O.p.afterSaveCell.call(O,O.rows[C].id,N,E,C,B)}O.p.savedRow.splice(0,1)}else{A.jgrid.info_dialog(A.jgrid.errors.errcap,Y[1],A.jgrid.edit.bClose);A(O).jqGrid("restoreCell",C,B)}}},error:function(X,Z,Y){A("#lui_"+A.jgrid.jqID(O.p.id)).hide();O.grid.hDiv.loading=false;A(O).triggerHandler("jqGridErrorCell",[X,Z,Y]);if(A.isFunction(O.p.errorCell)){O.p.errorCell.call(O,X,Z,Y);A(O).jqGrid("restoreCell",C,B)}else{A.jgrid.info_dialog(A.jgrid.errors.errcap,X.status+" : "+X.statusText+"<br/>"+Z,A.jgrid.edit.bClose);A(O).jqGrid("restoreCell",C,B)}}},A.jgrid.ajaxOptions,O.p.ajaxCellOptions||{}))}else{try{A.jgrid.info_dialog(A.jgrid.errors.errcap,A.jgrid.errors.nourl,A.jgrid.edit.bClose);A(O).jqGrid("restoreCell",C,B)}catch(L){}}}if(O.p.cellsubmit==="clientArray"){A(P).empty();A(O).jqGrid("setCell",O.rows[C].id,B,G,false,false,true);A(P).addClass("dirty-cell");A(O.rows[C]).addClass("edited");A(O).triggerHandler("jqGridAfterSaveCell",[O.rows[C].id,N,E,C,B]);if(A.isFunction(O.p.afterSaveCell)){O.p.afterSaveCell.call(O,O.rows[C].id,N,E,C,B)}O.p.savedRow.splice(0,1)}}else{try{window.setTimeout(function(){A.jgrid.info_dialog(A.jgrid.errors.errcap,E+" "+H[1],A.jgrid.edit.bClose)},100);A(O).jqGrid("restoreCell",C,B)}catch(L){}}}else{A(O).jqGrid("restoreCell",C,B)}}window.setTimeout(function(){A("#"+A.jgrid.jqID(O.p.knv)).attr("tabindex","-1").focus()},0)})},restoreCell:function(C,B){return this.each(function(){var G=this,F;if(!G.grid||G.p.cellEdit!==true){return}if(G.p.savedRow.length>=1){F=0}else{F=null}if(F!==null){var D=A("td:eq("+B+")",G.rows[C]);if(A.isFunction(A.fn.datepicker)){try{A("input.hasDatepicker",D).datepicker("hide")}catch(E){}}A(D).empty().attr("tabindex","-1");A(G).jqGrid("setCell",G.rows[C].id,B,G.p.savedRow[F].v,false,false,true);A(G).triggerHandler("jqGridAfterRestoreCell",[G.rows[C].id,G.p.savedRow[F].v,C,B]);if(A.isFunction(G.p.afterRestoreCell)){G.p.afterRestoreCell.call(G,G.rows[C].id,G.p.savedRow[F].v,C,B)}G.p.savedRow.splice(0,1)}window.setTimeout(function(){A("#"+G.p.knv).attr("tabindex","-1").focus()},0)})},nextCell:function(C,B){return this.each(function(){var F=this,D=false,E;if(!F.grid||F.p.cellEdit!==true){return}for(E=B+1;E<F.p.colModel.length;E++){if(F.p.colModel[E].editable===true){D=E;break}}if(D!==false){A(F).jqGrid("editCell",C,D,true)}else{if(F.p.savedRow.length>0){A(F).jqGrid("saveCell",C,B)}}})},prevCell:function(C,B){return this.each(function(){var F=this,D=false,E;if(!F.grid||F.p.cellEdit!==true){return}for(E=B-1;E>=0;E--){if(F.p.colModel[E].editable===true){D=E;break}}if(D!==false){A(F).jqGrid("editCell",C,D,true)}else{if(F.p.savedRow.length>0){A(F).jqGrid("saveCell",C,B)}}})},GridNav:function(){return this.each(function(){var G=this;if(!G.grid||G.p.cellEdit!==true){return}G.p.knv=G.p.id+"_kn";var F=A("<div style='position:fixed;top:0px;width:1px;height:1px;' tabindex='0'><div tabindex='-1' style='width:1px;height:1px;' id='"+G.p.knv+"'></div></div>"),C,E;function D(P,R,M){if(M.substr(0,1)==="v"){var L=A(G.grid.bDiv)[0].clientHeight,H=A(G.grid.bDiv)[0].scrollTop,K=G.rows[P].offsetTop+G.rows[P].clientHeight,I=G.rows[P].offsetTop;if(M==="vd"){if(K>=L){A(G.grid.bDiv)[0].scrollTop=A(G.grid.bDiv)[0].scrollTop+G.rows[P].clientHeight}}if(M==="vu"){if(I<H){A(G.grid.bDiv)[0].scrollTop=A(G.grid.bDiv)[0].scrollTop-G.rows[P].clientHeight}}}if(M==="h"){var Q=A(G.grid.bDiv)[0].clientWidth,O=A(G.grid.bDiv)[0].scrollLeft,J=G.rows[P].cells[R].offsetLeft+G.rows[P].cells[R].clientWidth,N=G.rows[P].cells[R].offsetLeft;if(J>=Q+parseInt(O,10)){A(G.grid.bDiv)[0].scrollLeft=A(G.grid.bDiv)[0].scrollLeft+G.rows[P].cells[R].clientWidth}else{if(N<O){A(G.grid.bDiv)[0].scrollLeft=A(G.grid.bDiv)[0].scrollLeft-G.rows[P].cells[R].clientWidth}}}}function B(J,H){var K,I;if(H==="lft"){K=J+1;for(I=J;I>=0;I--){if(G.p.colModel[I].hidden!==true){K=I;break}}}if(H==="rgt"){K=J-1;for(I=J;I<G.p.colModel.length;I++){if(G.p.colModel[I].hidden!==true){K=I;break}}}return K}A(F).insertBefore(G.grid.cDiv);A("#"+G.p.knv).focus().keydown(function(H){E=H.keyCode;if(G.p.direction==="rtl"){if(E===37){E=39}else{if(E===39){E=37}}}switch(E){case 38:if(G.p.iRow-1>0){D(G.p.iRow-1,G.p.iCol,"vu");A(G).jqGrid("editCell",G.p.iRow-1,G.p.iCol,false)}break;case 40:if(G.p.iRow+1<=G.rows.length-1){D(G.p.iRow+1,G.p.iCol,"vd");A(G).jqGrid("editCell",G.p.iRow+1,G.p.iCol,false)}break;case 37:if(G.p.iCol-1>=0){C=B(G.p.iCol-1,"lft");D(G.p.iRow,C,"h");A(G).jqGrid("editCell",G.p.iRow,C,false)}break;case 39:if(G.p.iCol+1<=G.p.colModel.length-1){C=B(G.p.iCol+1,"rgt");D(G.p.iRow,C,"h");A(G).jqGrid("editCell",G.p.iRow,C,false)}break;case 13:if(parseInt(G.p.iCol,10)>=0&&parseInt(G.p.iRow,10)>=0){A(G).jqGrid("editCell",G.p.iRow,G.p.iCol,true)}break;default:return true}return false})})},getChangedCells:function(B){var C=[];if(!B){B="all"}this.each(function(){var E=this,D;if(!E.grid||E.p.cellEdit!==true){return}A(E.rows).each(function(G){var F={};if(A(this).hasClass("edited")){A("td",this).each(function(H){D=E.p.colModel[H].name;if(D!=="cb"&&D!=="subgrid"){if(B==="dirty"){if(A(this).hasClass("dirty-cell")){try{F[D]=A.unformat.call(E,this,{rowId:E.rows[G].id,colModel:E.p.colModel[H]},H)}catch(I){F[D]=A.jgrid.htmlDecode(A(this).html())}}}else{try{F[D]=A.unformat.call(E,this,{rowId:E.rows[G].id,colModel:E.p.colModel[H]},H)}catch(I){F[D]=A.jgrid.htmlDecode(A(this).html())}}}});F.id=this.id;C.push(F)}})});return C}})})(jQuery);(function(A){A.jgrid.extend({setSubGrid:function(){return this.each(function(){var E=this,D,B,C={plusicon:"ui-icon-plus",minusicon:"ui-icon-minus",openicon:"ui-icon-carat-1-sw",expandOnLoad:false,delayOnLoad:50,selectOnExpand:false,selectOnCollapse:false,reloadOnExpand:true};E.p.subGridOptions=A.extend(C,E.p.subGridOptions||{});E.p.colNames.unshift("");E.p.colModel.unshift({name:"subgrid",width:A.jgrid.cell_width?E.p.subGridWidth+E.p.cellLayout:E.p.subGridWidth,sortable:false,resizable:false,hidedlg:true,search:false,fixed:true});D=E.p.subGridModel;if(D[0]){D[0].align=A.extend([],D[0].align||[]);for(B=0;B<D[0].name.length;B++){D[0].align[B]=D[0].align[B]||"left"}}})},addSubGridCell:function(C,B){var E="",F,D;this.each(function(){E=this.formatCol(C,B);D=this.p.id;F=this.p.subGridOptions.plusicon});return'<td role="gridcell" aria-describedby="'+D+'_subgrid" class="ui-sgcollapsed sgcollapsed" '+E+"><a style='cursor:pointer;'><span class='ui-icon "+F+"'></span></a></td>"},addSubGrid:function(C,B){return this.each(function(){var L=this;if(!L.grid){return}var M=function(R,S,Q){var T=A("<td align='"+L.p.subGridModel[0].align[Q]+"'></td>").html(S);A(R).append(T)};var G=function(U,T){var V,S,Q,R=A("<table cellspacing='0' cellpadding='0' border='0'><tbody></tbody></table>"),X=A("<tr></tr>");for(S=0;S<L.p.subGridModel[0].name.length;S++){V=A("<th class='ui-state-default ui-th-subgrid ui-th-column ui-th-"+L.p.direction+"'></th>");A(V).html(L.p.subGridModel[0].name[S]);A(V).width(L.p.subGridModel[0].width[S]);A(X).append(V)}A(R).append(X);if(U){Q=L.p.xmlReader.subgrid;A(Q.root+" "+Q.row,U).each(function(){X=A("<tr class='ui-widget-content ui-subtblcell'></tr>");if(Q.repeatitems===true){A(Q.cell,this).each(function(Z){M(X,A(this).text()||"&#160;",Z)})}else{var Y=L.p.subGridModel[0].mapping||L.p.subGridModel[0].name;if(Y){for(S=0;S<Y.length;S++){M(X,A(Y[S],this).text()||"&#160;",S)}}}A(R).append(X)})}var W=A("table:first",L.grid.bDiv).attr("id")+"_";A("#"+A.jgrid.jqID(W+T)).append(R);L.grid.hDiv.loading=false;A("#load_"+A.jgrid.jqID(L.p.id)).hide();return false};var K=function(X,W){var Y,S,V,R,Q,U,T=A("<table cellspacing='0' cellpadding='0' border='0'><tbody></tbody></table>"),b=A("<tr></tr>");for(V=0;V<L.p.subGridModel[0].name.length;V++){Y=A("<th class='ui-state-default ui-th-subgrid ui-th-column ui-th-"+L.p.direction+"'></th>");A(Y).html(L.p.subGridModel[0].name[V]);A(Y).width(L.p.subGridModel[0].width[V]);A(b).append(Y)}A(T).append(b);if(X){Q=L.p.jsonReader.subgrid;S=A.jgrid.getAccessor(X,Q.root);if(S!==undefined){for(V=0;V<S.length;V++){R=S[V];b=A("<tr class='ui-widget-content ui-subtblcell'></tr>");if(Q.repeatitems===true){if(Q.cell){R=R[Q.cell]}for(U=0;U<R.length;U++){M(b,R[U]||"&#160;",U)}}else{var a=L.p.subGridModel[0].mapping||L.p.subGridModel[0].name;if(a.length){for(U=0;U<a.length;U++){M(b,R[a[U]]||"&#160;",U)}}}A(T).append(b)}}}var Z=A("table:first",L.grid.bDiv).attr("id")+"_";A("#"+A.jgrid.jqID(Z+W)).append(T);L.grid.hDiv.loading=false;A("#load_"+A.jgrid.jqID(L.p.id)).hide();return false};var F=function(U){var S,R,Q,T;S=A(U).attr("id");R={nd_:(new Date().getTime())};R[L.p.prmNames.subgridid]=S;if(!L.p.subGridModel[0]){return false}if(L.p.subGridModel[0].params){for(T=0;T<L.p.subGridModel[0].params.length;T++){for(Q=0;Q<L.p.colModel.length;Q++){if(L.p.colModel[Q].name===L.p.subGridModel[0].params[T]){R[L.p.colModel[Q].name]=A("td:eq("+Q+")",U).text().replace(/\&#160\;/ig,"")}}}}if(!L.grid.hDiv.loading){L.grid.hDiv.loading=true;A("#load_"+A.jgrid.jqID(L.p.id)).show();if(!L.p.subgridtype){L.p.subgridtype=L.p.datatype}if(A.isFunction(L.p.subgridtype)){L.p.subgridtype.call(L,R)}else{L.p.subgridtype=L.p.subgridtype.toLowerCase()}switch(L.p.subgridtype){case"xml":case"json":A.ajax(A.extend({type:L.p.mtype,url:L.p.subGridUrl,dataType:L.p.subgridtype,data:A.isFunction(L.p.serializeSubGridData)?L.p.serializeSubGridData.call(L,R):R,complete:function(V){if(L.p.subgridtype==="xml"){G(V.responseXML,S)}else{K(A.jgrid.parse(V.responseText),S)}V=null}},A.jgrid.ajaxOptions,L.p.ajaxSubgridOptions||{}));break}}return false};var H,P,E,D=0,O,J;A.each(L.p.colModel,function(){if(this.hidden===true||this.name==="rn"||this.name==="cb"){D++}});var N=L.rows.length,I=1;if(B!==undefined&&B>0){I=B;N=B+1}while(I<N){if(A(L.rows[I]).hasClass("jqgrow")){A(L.rows[I].cells[C]).bind("click",function(){var Q=A(this).parent("tr")[0];J=Q.nextSibling;if(A(this).hasClass("sgcollapsed")){P=L.p.id;H=Q.id;if(L.p.subGridOptions.reloadOnExpand===true||(L.p.subGridOptions.reloadOnExpand===false&&!A(J).hasClass("ui-subgrid"))){E=C>=1?"<td colspan='"+C+"'>&#160;</td>":"";O=A(L).triggerHandler("jqGridSubGridBeforeExpand",[P+"_"+H,H]);O=(O===false||O==="stop")?false:true;if(O&&A.isFunction(L.p.subGridBeforeExpand)){O=L.p.subGridBeforeExpand.call(L,P+"_"+H,H)}if(O===false){return false}A(Q).after("<tr role='row' class='ui-subgrid'>"+E+"<td class='ui-widget-content subgrid-cell'><span class='ui-icon "+L.p.subGridOptions.openicon+"'></span></td><td colspan='"+parseInt(L.p.colNames.length-1-D,10)+"' class='ui-widget-content subgrid-data'><div id="+P+"_"+H+" class='tablediv'></div></td></tr>");A(L).triggerHandler("jqGridSubGridRowExpanded",[P+"_"+H,H]);if(A.isFunction(L.p.subGridRowExpanded)){L.p.subGridRowExpanded.call(L,P+"_"+H,H)}else{F(Q)}}else{A(J).show()}A(this).html("<a style='cursor:pointer;'><span class='ui-icon "+L.p.subGridOptions.minusicon+"'></span></a>").removeClass("sgcollapsed").addClass("sgexpanded");if(L.p.subGridOptions.selectOnExpand){A(L).jqGrid("setSelection",H)}}else{if(A(this).hasClass("sgexpanded")){O=A(L).triggerHandler("jqGridSubGridRowColapsed",[P+"_"+H,H]);O=(O===false||O==="stop")?false:true;H=Q.id;if(O&&A.isFunction(L.p.subGridRowColapsed)){O=L.p.subGridRowColapsed.call(L,P+"_"+H,H)}if(O===false){return false}if(L.p.subGridOptions.reloadOnExpand===true){A(J).remove(".ui-subgrid")}else{if(A(J).hasClass("ui-subgrid")){A(J).hide()}}A(this).html("<a style='cursor:pointer;'><span class='ui-icon "+L.p.subGridOptions.plusicon+"'></span></a>").removeClass("sgexpanded").addClass("sgcollapsed");if(L.p.subGridOptions.selectOnCollapse){A(L).jqGrid("setSelection",H)}}}return false})}I++}if(L.p.subGridOptions.expandOnLoad===true){A(L.rows).filter(".jqgrow").each(function(R,Q){A(Q.cells[0]).click()})}L.subGridXml=function(R,Q){G(R,Q)};L.subGridJson=function(Q,R){K(Q,R)}})},expandSubGridRow:function(B){return this.each(function(){var E=this;if(!E.grid&&!B){return}if(E.p.subGrid===true){var D=A(this).jqGrid("getInd",B,true);if(D){var C=A("td.sgcollapsed",D)[0];if(C){A(C).trigger("click")}}}})},collapseSubGridRow:function(B){return this.each(function(){var E=this;if(!E.grid&&!B){return}if(E.p.subGrid===true){var D=A(this).jqGrid("getInd",B,true);if(D){var C=A("td.sgexpanded",D)[0];if(C){A(C).trigger("click")}}}})},toggleSubGridRow:function(B){return this.each(function(){var E=this;if(!E.grid&&!B){return}if(E.p.subGrid===true){var D=A(this).jqGrid("getInd",B,true);if(D){var C=A("td.sgcollapsed",D)[0];if(C){A(C).trigger("click")}else{C=A("td.sgexpanded",D)[0];if(C){A(C).trigger("click")}}}}})}})})(jQuery);(function(A){A.jgrid.extend({setTreeNode:function(B,C){return this.each(function(){var N=this;if(!N.grid||!N.p.treeGrid){return}var O=N.p.expColInd,I=N.p.treeReader.expanded_field,U=N.p.treeReader.leaf_field,Q=N.p.treeReader.level_field,J=N.p.treeReader.icon_field,T=N.p.treeReader.loaded,P,M,H,R,L,G,D,K;while(B<C){var F=A.jgrid.stripPref(N.p.idPrefix,N.rows[B].id),V=N.p._index[F],S;D=N.p.data[V];if(N.p.treeGridModel==="nested"){if(!D[U]){P=parseInt(D[N.p.treeReader.left_field],10);M=parseInt(D[N.p.treeReader.right_field],10);D[U]=(M===P+1)?"true":"false";N.rows[B].cells[N.p._treeleafpos].innerHTML=D[U]}}H=parseInt(D[Q],10);if(N.p.tree_root_level===0){R=H+1;L=H}else{R=H;L=H-1}G="<div class='tree-wrap tree-wrap-"+N.p.direction+"' style='width:"+(R*18)+"px;'>";G+="<div style='"+(N.p.direction==="rtl"?"right:":"left:")+(L*18)+"px;' class='ui-icon ";if(D[T]!==undefined){if(D[T]==="true"||D[T]===true){D[T]=true}else{D[T]=false}}if(D[U]==="true"||D[U]===true){G+=((D[J]!==undefined&&D[J]!=="")?D[J]:N.p.treeIcons.leaf)+" tree-leaf treeclick";D[U]=true;K="leaf"}else{D[U]=false;K=""}D[I]=((D[I]==="true"||D[I]===true)?true:false)&&(D[T]||D[T]===undefined);if(D[I]===false){G+=((D[U]===true)?"'":N.p.treeIcons.plus+" tree-plus treeclick'")}else{G+=((D[U]===true)?"'":N.p.treeIcons.minus+" tree-minus treeclick'")}G+="></div></div>";A(N.rows[B].cells[O]).wrapInner("<span class='cell-wrapper"+K+"'></span>").prepend(G);if(H!==parseInt(N.p.tree_root_level,10)){var E=A(N).jqGrid("getNodeParent",D);S=E&&E.hasOwnProperty(I)?E[I]:true;if(!S){A(N.rows[B]).css("display","none")}}A(N.rows[B].cells[O]).find("div.treeclick").bind("click",function(W){var Z=W.target||W.srcElement,Y=A.jgrid.stripPref(N.p.idPrefix,A(Z,N.rows).closest("tr.jqgrow")[0].id),X=N.p._index[Y];if(!N.p.data[X][U]){if(N.p.data[X][I]){A(N).jqGrid("collapseRow",N.p.data[X]);A(N).jqGrid("collapseNode",N.p.data[X])}else{A(N).jqGrid("expandRow",N.p.data[X]);A(N).jqGrid("expandNode",N.p.data[X])}}return false});if(N.p.ExpandColClick===true){A(N.rows[B].cells[O]).find("span.cell-wrapper").css("cursor","pointer").bind("click",function(W){var Z=W.target||W.srcElement,Y=A.jgrid.stripPref(N.p.idPrefix,A(Z,N.rows).closest("tr.jqgrow")[0].id),X=N.p._index[Y];if(!N.p.data[X][U]){if(N.p.data[X][I]){A(N).jqGrid("collapseRow",N.p.data[X]);A(N).jqGrid("collapseNode",N.p.data[X])}else{A(N).jqGrid("expandRow",N.p.data[X]);A(N).jqGrid("expandNode",N.p.data[X])}}A(N).jqGrid("setSelection",Y);return false})}B++}})},setTreeGrid:function(){return this.each(function(){var G=this,E=0,C,I=false,B,H,F,D=[];if(!G.p.treeGrid){return}if(!G.p.treedatatype){A.extend(G.p,{treedatatype:G.p.datatype})}G.p.subGrid=false;G.p.altRows=false;G.p.pgbuttons=false;G.p.pginput=false;G.p.gridview=true;if(G.p.rowTotal===null){G.p.rowNum=10000}G.p.multiselect=false;G.p.rowList=[];G.p.expColInd=0;C="ui-icon-triangle-1-"+(G.p.direction==="rtl"?"w":"e");G.p.treeIcons=A.extend({plus:C,minus:"ui-icon-triangle-1-s",leaf:"ui-icon-radio-off"},G.p.treeIcons||{});if(G.p.treeGridModel==="nested"){G.p.treeReader=A.extend({level_field:"level",left_field:"lft",right_field:"rgt",leaf_field:"isLeaf",expanded_field:"expanded",loaded:"loaded",icon_field:"icon"},G.p.treeReader)}else{if(G.p.treeGridModel==="adjacency"){G.p.treeReader=A.extend({level_field:"level",parent_id_field:"parent",leaf_field:"isLeaf",expanded_field:"expanded",loaded:"loaded",icon_field:"icon"},G.p.treeReader)}}for(H in G.p.colModel){if(G.p.colModel.hasOwnProperty(H)){B=G.p.colModel[H].name;if(B===G.p.ExpandColumn&&!I){I=true;G.p.expColInd=E}E++;for(F in G.p.treeReader){if(G.p.treeReader.hasOwnProperty(F)&&G.p.treeReader[F]===B){D.push(B)}}}}A.each(G.p.treeReader,function(K,J){if(J&&A.inArray(J,D)===-1){if(K==="leaf_field"){G.p._treeleafpos=E}E++;G.p.colNames.push(J);G.p.colModel.push({name:J,width:1,hidden:true,sortable:false,resizable:false,hidedlg:true,editable:true,search:false})}})})},expandRow:function(B){this.each(function(){var E=this;if(!E.grid||!E.p.treeGrid){return}var C=A(E).jqGrid("getNodeChildren",B),D=E.p.treeReader.expanded_field;A(C).each(function(){var F=E.p.idPrefix+A.jgrid.getAccessor(this,E.p.localReader.id);A(A(E).jqGrid("getGridRowById",F)).css("display","");if(this[D]){A(E).jqGrid("expandRow",this)}})})},collapseRow:function(B){this.each(function(){var E=this;if(!E.grid||!E.p.treeGrid){return}var C=A(E).jqGrid("getNodeChildren",B),D=E.p.treeReader.expanded_field;A(C).each(function(){var F=E.p.idPrefix+A.jgrid.getAccessor(this,E.p.localReader.id);A(A(E).jqGrid("getGridRowById",F)).css("display","none");if(this[D]){A(E).jqGrid("collapseRow",this)}})})},getRootNodes:function(){var B=[];this.each(function(){var E=this;if(!E.grid||!E.p.treeGrid){return}switch(E.p.treeGridModel){case"nested":var C=E.p.treeReader.level_field;A(E.p.data).each(function(){if(parseInt(this[C],10)===parseInt(E.p.tree_root_level,10)){B.push(this)}});break;case"adjacency":var D=E.p.treeReader.parent_id_field;A(E.p.data).each(function(){if(this[D]===null||String(this[D]).toLowerCase()==="null"){B.push(this)}});break}});return B},getNodeDepth:function(C){var B=null;this.each(function(){if(!this.grid||!this.p.treeGrid){return}var E=this;switch(E.p.treeGridModel){case"nested":var D=E.p.treeReader.level_field;B=parseInt(C[D],10)-parseInt(E.p.tree_root_level,10);break;case"adjacency":B=A(E).jqGrid("getNodeAncestors",C).length;break}});return B},getNodeParent:function(C){var B=null;this.each(function(){var H=this;if(!H.grid||!H.p.treeGrid){return}switch(H.p.treeGridModel){case"nested":var E=H.p.treeReader.left_field,F=H.p.treeReader.right_field,D=H.p.treeReader.level_field,L=parseInt(C[E],10),K=parseInt(C[F],10),I=parseInt(C[D],10);A(this.p.data).each(function(){if(parseInt(this[D],10)===I-1&&parseInt(this[E],10)<L&&parseInt(this[F],10)>K){B=this;return false}});break;case"adjacency":var G=H.p.treeReader.parent_id_field,J=H.p.localReader.id;A(this.p.data).each(function(){if(this[J]===A.jgrid.stripPref(H.p.idPrefix,C[G])){B=this;return false}});break}});return B},getNodeChildren:function(C){var B=[];this.each(function(){var H=this;if(!H.grid||!H.p.treeGrid){return}switch(H.p.treeGridModel){case"nested":var E=H.p.treeReader.left_field,F=H.p.treeReader.right_field,D=H.p.treeReader.level_field,L=parseInt(C[E],10),K=parseInt(C[F],10),I=parseInt(C[D],10);A(this.p.data).each(function(){if(parseInt(this[D],10)===I+1&&parseInt(this[E],10)>L&&parseInt(this[F],10)<K){B.push(this)}});break;case"adjacency":var G=H.p.treeReader.parent_id_field,J=H.p.localReader.id;A(this.p.data).each(function(){if(this[G]==A.jgrid.stripPref(H.p.idPrefix,C[J])){B.push(this)}});break}});return B},getFullTreeNode:function(C){var B=[];this.each(function(){var I=this,J;if(!I.grid||!I.p.treeGrid){return}switch(I.p.treeGridModel){case"nested":var E=I.p.treeReader.left_field,F=I.p.treeReader.right_field,D=I.p.treeReader.level_field,M=parseInt(C[E],10),L=parseInt(C[F],10),H=parseInt(C[D],10);A(this.p.data).each(function(){if(parseInt(this[D],10)>=H&&parseInt(this[E],10)>=M&&parseInt(this[E],10)<=L){B.push(this)}});break;case"adjacency":if(C){B.push(C);var G=I.p.treeReader.parent_id_field,K=I.p.localReader.id;A(this.p.data).each(function(N){J=B.length;for(N=0;N<J;N++){if(A.jgrid.stripPref(I.p.idPrefix,B[N][K])===this[G]){B.push(this);break}}})}break}});return B},getNodeAncestors:function(C){var B=[];this.each(function(){if(!this.grid||!this.p.treeGrid){return}var D=A(this).jqGrid("getNodeParent",C);while(D){B.push(D);D=A(this).jqGrid("getNodeParent",D)}});return B},isVisibleNode:function(C){var B=true;this.each(function(){var F=this;if(!F.grid||!F.p.treeGrid){return}var D=A(F).jqGrid("getNodeAncestors",C),E=F.p.treeReader.expanded_field;A(D).each(function(){B=B&&this[E];if(!B){return false}})});return B},isNodeLoaded:function(C){var B;this.each(function(){var F=this;if(!F.grid||!F.p.treeGrid){return}var D=F.p.treeReader.leaf_field,E=F.p.treeReader.loaded;if(C!==undefined){if(C[E]!==undefined){B=C[E]}else{if(C[D]||A(F).jqGrid("getNodeChildren",C).length>0){B=true}else{B=false}}}else{B=false}});return B},expandNode:function(B){return this.each(function(){if(!this.grid||!this.p.treeGrid){return}var D=this.p.treeReader.expanded_field,J=this.p.treeReader.parent_id_field,E=this.p.treeReader.loaded,H=this.p.treeReader.level_field,K=this.p.treeReader.left_field,C=this.p.treeReader.right_field;if(!B[D]){var G=A.jgrid.getAccessor(B,this.p.localReader.id);var I=A("#"+this.p.idPrefix+A.jgrid.jqID(G),this.grid.bDiv)[0];var F=this.p._index[G];if(A(this).jqGrid("isNodeLoaded",this.p.data[F])){B[D]=true;A("div.treeclick",I).removeClass(this.p.treeIcons.plus+" tree-plus").addClass(this.p.treeIcons.minus+" tree-minus")}else{if(!this.grid.hDiv.loading){B[D]=true;A("div.treeclick",I).removeClass(this.p.treeIcons.plus+" tree-plus").addClass(this.p.treeIcons.minus+" tree-minus");this.p.treeANode=I.rowIndex;this.p.datatype=this.p.treedatatype;if(this.p.treeGridModel==="nested"){A(this).jqGrid("setGridParam",{postData:{nodeid:G,n_left:B[K],n_right:B[C],n_level:B[H]}})}else{A(this).jqGrid("setGridParam",{postData:{nodeid:G,parentid:B[J],n_level:B[H]}})}A(this).trigger("reloadGrid");B[E]=true;if(this.p.treeGridModel==="nested"){A(this).jqGrid("setGridParam",{postData:{nodeid:"",n_left:"",n_right:"",n_level:""}})}else{A(this).jqGrid("setGridParam",{postData:{nodeid:"",parentid:"",n_level:""}})}}}}})},collapseNode:function(B){return this.each(function(){if(!this.grid||!this.p.treeGrid){return}var D=this.p.treeReader.expanded_field;if(B[D]){B[D]=false;var E=A.jgrid.getAccessor(B,this.p.localReader.id);var C=A("#"+this.p.idPrefix+A.jgrid.jqID(E),this.grid.bDiv)[0];A("div.treeclick",C).removeClass(this.p.treeIcons.minus+" tree-minus").addClass(this.p.treeIcons.plus+" tree-plus")}})},SortTree:function(C,D,B,E){return this.each(function(){if(!this.grid||!this.p.treeGrid){return}var I,K,F,G=[],J=this,H,L,M=A(this).jqGrid("getRootNodes");H=A.jgrid.from(M);H.orderBy(C,D,B,E);L=H.select();for(I=0,K=L.length;I<K;I++){F=L[I];G.push(F);A(this).jqGrid("collectChildrenSortTree",G,F,C,D,B,E)}A.each(G,function(N){var O=A.jgrid.getAccessor(this,J.p.localReader.id);A("#"+A.jgrid.jqID(J.p.id)+" tbody tr:eq("+N+")").after(A("tr#"+A.jgrid.jqID(O),J.grid.bDiv))});H=null;L=null;G=null})},collectChildrenSortTree:function(C,B,E,F,D,G){return this.each(function(){if(!this.grid||!this.p.treeGrid){return}var I,K,J,L,H,M;L=A(this).jqGrid("getNodeChildren",B);H=A.jgrid.from(L);H.orderBy(E,F,D,G);M=H.select();for(I=0,K=M.length;I<K;I++){J=M[I];C.push(J);A(this).jqGrid("collectChildrenSortTree",C,J,E,F,D,G)}})},setTreeRow:function(D,C){var B=false;this.each(function(){var E=this;if(!E.grid||!E.p.treeGrid){return}B=A(E).jqGrid("setRowData",D,C)});return B},delTreeNode:function(B){return this.each(function(){var J=this,M=J.p.localReader.id,H,G=J.p.treeReader.left_field,E=J.p.treeReader.right_field,D,I,L,K;if(!J.grid||!J.p.treeGrid){return}var F=J.p._index[B];if(F!==undefined){D=parseInt(J.p.data[F][E],10);I=D-parseInt(J.p.data[F][G],10)+1;var C=A(J).jqGrid("getFullTreeNode",J.p.data[F]);if(C.length>0){for(H=0;H<C.length;H++){A(J).jqGrid("delRowData",C[H][M])}}if(J.p.treeGridModel==="nested"){L=A.jgrid.from(J.p.data).greater(G,D,{stype:"integer"}).select();if(L.length){for(K in L){if(L.hasOwnProperty(K)){L[K][G]=parseInt(L[K][G],10)-I}}}L=A.jgrid.from(J.p.data).greater(E,D,{stype:"integer"}).select();if(L.length){for(K in L){if(L.hasOwnProperty(K)){L[K][E]=parseInt(L[K][E],10)-I}}}}}})},addChildNode:function(U,I,W,R){var P=this[0];if(W){var X=P.p.treeReader.expanded_field,b=P.p.treeReader.leaf_field,C=P.p.treeReader.level_field,Q=P.p.treeReader.parent_id_field,V=P.p.treeReader.left_field,H=P.p.treeReader.right_field,T=P.p.treeReader.loaded,M,F,N,O,G,D,Z=0,E=I,a,L;if(R===undefined){R=false}if(U===undefined||U===null){G=P.p.data.length-1;if(G>=0){while(G>=0){Z=Math.max(Z,parseInt(P.p.data[G][P.p.localReader.id],10));G--}}U=Z+1}var B=A(P).jqGrid("getInd",I);a=false;if(I===undefined||I===null||I===""){I=null;E=null;M="last";O=P.p.tree_root_level;G=P.p.data.length+1}else{M="after";F=P.p._index[I];N=P.p.data[F];I=N[P.p.localReader.id];O=parseInt(N[C],10)+1;var S=A(P).jqGrid("getFullTreeNode",N);if(S.length){G=S[S.length-1][P.p.localReader.id];E=G;G=A(P).jqGrid("getInd",E)+1}else{G=A(P).jqGrid("getInd",I)+1}if(N[b]){a=true;N[X]=true;A(P.rows[B]).find("span.cell-wrapperleaf").removeClass("cell-wrapperleaf").addClass("cell-wrapper").end().find("div.tree-leaf").removeClass(P.p.treeIcons.leaf+" tree-leaf").addClass(P.p.treeIcons.minus+" tree-minus");P.p.data[F][b]=false;N[T]=true}}D=G+1;if(W[X]===undefined){W[X]=false}if(W[T]===undefined){W[T]=false}W[C]=O;if(W[b]===undefined){W[b]=true}if(P.p.treeGridModel==="adjacency"){W[Q]=I}if(P.p.treeGridModel==="nested"){var Y,K,J;if(I!==null){L=parseInt(N[H],10);Y=A.jgrid.from(P.p.data);Y=Y.greaterOrEquals(H,L,{stype:"integer"});K=Y.select();if(K.length){for(J in K){if(K.hasOwnProperty(J)){K[J][V]=K[J][V]>L?parseInt(K[J][V],10)+2:K[J][V];K[J][H]=K[J][H]>=L?parseInt(K[J][H],10)+2:K[J][H]}}}W[V]=L;W[H]=L+1}else{L=parseInt(A(P).jqGrid("getCol",H,false,"max"),10);K=A.jgrid.from(P.p.data).greater(V,L,{stype:"integer"}).select();if(K.length){for(J in K){if(K.hasOwnProperty(J)){K[J][V]=parseInt(K[J][V],10)+2}}}K=A.jgrid.from(P.p.data).greater(H,L,{stype:"integer"}).select();if(K.length){for(J in K){if(K.hasOwnProperty(J)){K[J][H]=parseInt(K[J][H],10)+2}}}W[V]=L+1;W[H]=L+2}}if(I===null||A(P).jqGrid("isNodeLoaded",N)||a){A(P).jqGrid("addRowData",U,W,M,E);A(P).jqGrid("setTreeNode",G,D)}if(N&&!N[X]&&R){A(P.rows[B]).find("div.treeclick").click()}}}})})(jQuery);(function(A){A.extend(A.jgrid,{template:function(B){var E=A.makeArray(arguments).slice(1),D,C=E.length;if(B==null){B=""}return B.replace(/\{([\w\-]+)(?:\:([\w\.]*)(?:\((.*?)?\))?)?\}/g,function(H,G){if(!isNaN(parseInt(G,10))){return E[parseInt(G,10)]}for(D=0;D<C;D++){if(A.isArray(E[D])){var I=E[D],F=I.length;while(F--){if(G===I[F].nm){return I[F].v}}}}})}});A.jgrid.extend({groupingSetup:function(){return this.each(function(){var G=this,B,F,E,D=G.p.colModel,C=G.p.groupingView;if(C!==null&&((typeof C==="object")||A.isFunction(C))){if(!C.groupField.length){G.p.grouping=false}else{if(C.visibiltyOnNextGrouping===undefined){C.visibiltyOnNextGrouping=[]}C.lastvalues=[];if(!C._locgr){C.groups=[]}C.counters=[];for(B=0;B<C.groupField.length;B++){if(!C.groupOrder[B]){C.groupOrder[B]="asc"}if(!C.groupText[B]){C.groupText[B]="{0}"}if(typeof C.groupColumnShow[B]!=="boolean"){C.groupColumnShow[B]=true}if(typeof C.groupSummary[B]!=="boolean"){C.groupSummary[B]=false}if(!C.groupSummaryPos[B]){C.groupSummaryPos[B]="footer"}if(C.groupColumnShow[B]===true){C.visibiltyOnNextGrouping[B]=true;A(G).jqGrid("showCol",C.groupField[B])}else{C.visibiltyOnNextGrouping[B]=A("#"+A.jgrid.jqID(G.p.id+"_"+C.groupField[B])).is(":visible");A(G).jqGrid("hideCol",C.groupField[B])}}C.summary=[];if(C.hideFirstGroupCol){C.formatDisplayField[0]=function(H){return H}}for(F=0,E=D.length;F<E;F++){if(C.hideFirstGroupCol){if(!D[F].hidden&&C.groupField[0]===D[F].name){D[F].formatter=function(){return""}}}if(D[F].summaryType){if(D[F].summaryDivider){C.summary.push({nm:D[F].name,st:D[F].summaryType,v:"",sd:D[F].summaryDivider,vd:"",sr:D[F].summaryRound,srt:D[F].summaryRoundType||"round"})}else{C.summary.push({nm:D[F].name,st:D[F].summaryType,v:"",sr:D[F].summaryRound,srt:D[F].summaryRoundType||"round"})}}}}}else{G.p.grouping=false}})},groupingPrepare:function(B,C){this.each(function(){var E=this.p.groupingView,L=this,J,H=E.groupField.length,F,G,D,I,K=0;for(J=0;J<H;J++){F=E.groupField[J];D=E.displayField[J];G=B[F];I=D==null?null:B[D];if(I==null){I=G}if(G!==undefined){if(C===0){E.groups.push({idx:J,dataIndex:F,value:G,displayValue:I,startRow:C,cnt:1,summary:[]});E.lastvalues[J]=G;E.counters[J]={cnt:1,pos:E.groups.length-1,summary:A.extend(true,[],E.summary)};A.each(E.counters[J].summary,function(){if(A.isFunction(this.st)){this.v=this.st.call(L,this.v,this.nm,B)}else{this.v=A(L).jqGrid("groupingCalculations.handler",this.st,this.v,this.nm,this.sr,this.srt,B);if(this.st.toLowerCase()==="avg"&&this.sd){this.vd=A(L).jqGrid("groupingCalculations.handler",this.st,this.vd,this.sd,this.sr,this.srt,B)}}});E.groups[E.counters[J].pos].summary=E.counters[J].summary}else{if(typeof G!=="object"&&(A.isArray(E.isInTheSameGroup)&&A.isFunction(E.isInTheSameGroup[J])?!E.isInTheSameGroup[J].call(L,E.lastvalues[J],G,J,E):E.lastvalues[J]!==G)){E.groups.push({idx:J,dataIndex:F,value:G,displayValue:I,startRow:C,cnt:1,summary:[]});E.lastvalues[J]=G;K=1;E.counters[J]={cnt:1,pos:E.groups.length-1,summary:A.extend(true,[],E.summary)};A.each(E.counters[J].summary,function(){if(A.isFunction(this.st)){this.v=this.st.call(L,this.v,this.nm,B)}else{this.v=A(L).jqGrid("groupingCalculations.handler",this.st,this.v,this.nm,this.sr,this.srt,B);if(this.st.toLowerCase()==="avg"&&this.sd){this.vd=A(L).jqGrid("groupingCalculations.handler",this.st,this.vd,this.sd,this.sr,this.srt,B)}}});E.groups[E.counters[J].pos].summary=E.counters[J].summary}else{if(K===1){E.groups.push({idx:J,dataIndex:F,value:G,displayValue:I,startRow:C,cnt:1,summary:[]});E.lastvalues[J]=G;E.counters[J]={cnt:1,pos:E.groups.length-1,summary:A.extend(true,[],E.summary)};A.each(E.counters[J].summary,function(){if(A.isFunction(this.st)){this.v=this.st.call(L,this.v,this.nm,B)}else{this.v=A(L).jqGrid("groupingCalculations.handler",this.st,this.v,this.nm,this.sr,this.srt,B);if(this.st.toLowerCase()==="avg"&&this.sd){this.vd=A(L).jqGrid("groupingCalculations.handler",this.st,this.vd,this.sd,this.sr,this.srt,B)}}});E.groups[E.counters[J].pos].summary=E.counters[J].summary}else{E.counters[J].cnt+=1;E.groups[E.counters[J].pos].cnt=E.counters[J].cnt;A.each(E.counters[J].summary,function(){if(A.isFunction(this.st)){this.v=this.st.call(L,this.v,this.nm,B)}else{this.v=A(L).jqGrid("groupingCalculations.handler",this.st,this.v,this.nm,this.sr,this.srt,B);if(this.st.toLowerCase()==="avg"&&this.sd){this.vd=A(L).jqGrid("groupingCalculations.handler",this.st,this.vd,this.sd,this.sr,this.srt,B)}}});E.groups[E.counters[J].pos].summary=E.counters[J].summary}}}}}});return this},groupingToggle:function(B){this.each(function(){var M=this,G=M.p.groupingView,P=B.split("_"),O=parseInt(P[P.length-2],10);P.splice(P.length-2,2);var N=P.join("_"),J=G.minusicon,L=G.plusicon,H=A("#"+A.jgrid.jqID(B)),C=H.length?H[0].nextSibling:null,I=A("#"+A.jgrid.jqID(B)+" span.tree-wrap-"+M.p.direction),Q=function(U){var V=A.map(U.split(" "),function(W){if(W.substring(0,N.length+1)===N+"_"){return parseInt(W.substring(N.length+1),10)}});return V.length>0?V[0]:undefined},T,D,E=false,R=M.p.frozenColumns?M.p.id+"_frozen":false,K=R?A("#"+A.jgrid.jqID(B),"#"+A.jgrid.jqID(R)):false,S=(K&&K.length)?K[0].nextSibling:null;if(I.hasClass(J)){if(G.showSummaryOnHide){if(C){while(C){if(A(C).hasClass("jqfoot")){var F=parseInt(A(C).attr("jqfootlevel"),10);if(F<=O){break}}A(C).hide();C=C.nextSibling;if(R){A(S).hide();S=S.nextSibling}}}}else{if(C){while(C){T=Q(C.className);if(T!==undefined&&T<=O){break}A(C).hide();C=C.nextSibling;if(R){A(S).hide();S=S.nextSibling}}}}I.removeClass(J).addClass(L);E=true}else{if(C){D=undefined;while(C){T=Q(C.className);if(D===undefined){D=T===undefined}if(T!==undefined){if(T<=O){break}if(T===O+1){A(C).show().find(">td>span.tree-wrap-"+M.p.direction).removeClass(J).addClass(L);if(R){A(S).show().find(">td>span.tree-wrap-"+M.p.direction).removeClass(J).addClass(L)}}}else{if(D){A(C).show();if(R){A(S).show()}}}C=C.nextSibling;if(R){S=S.nextSibling}}}I.removeClass(L).addClass(J)}A(M).triggerHandler("jqGridGroupingClickGroup",[B,E]);if(A.isFunction(M.p.onClickGroup)){M.p.onClickGroup.call(M,B,E)}});return false},groupingRender:function(B,D,E,C){return this.each(function(){var M=this,F=M.p.groupingView,J="",K="",Q,H,S=F.groupCollapse?F.plusicon:F.minusicon,G,O=[],N=F.groupField.length;S+=" tree-wrap-"+M.p.direction;A.each(M.p.colModel,function(T,U){var V;for(V=0;V<N;V++){if(F.groupField[V]===U.name){O[V]=T;break}}});var R=0;function L(W,T,U){var X=false,V;if(T===0){X=U[W]}else{var Y=U[W].idx;if(Y===0){X=U[W]}else{for(V=W;V>=0;V--){if(U[V].idx===Y-T){X=U[V];break}}}}return X}function P(c,e,U,V){var a=L(c,e,U),X=M.p.colModel,Z,W=a.cnt,Y="",b;for(b=V;b<D;b++){var d="<td "+M.formatCol(b,1,"")+">&#160;</td>",T="{0}";A.each(a.summary,function(){if(this.nm===X[b].name){if(X[b].summaryTpl){T=X[b].summaryTpl}if(typeof this.st==="string"&&this.st.toLowerCase()==="avg"){if(this.sd&&this.vd){this.v=(this.v/this.vd)}else{if(this.v&&W>0){this.v=(this.v/W)}}}try{this.groupCount=a.cnt;this.groupIndex=a.dataIndex;this.groupValue=a.value;Z=M.formatter("",this.v,b,this)}catch(f){Z=this.v}d="<td "+M.formatCol(b,1,"")+">"+A.jgrid.format(T,Z)+"</td>";return false}});Y+=d}return Y}var I=A.makeArray(F.groupSummary);I.reverse();A.each(F.groups,function(a,b){if(F._locgr){if(!(b.startRow+b.cnt>(E-1)*C&&b.startRow<E*C)){return true}}R++;H=M.p.id+"ghead_"+b.idx;Q=H+"_"+a;K="<span style='cursor:pointer;' class='ui-icon "+S+"' onclick=\"jQuery('#"+A.jgrid.jqID(M.p.id)+"').jqGrid('groupingToggle','"+Q+"');return false;\"></span>";try{if(A.isArray(F.formatDisplayField)&&A.isFunction(F.formatDisplayField[b.idx])){b.displayValue=F.formatDisplayField[b.idx].call(M,b.displayValue,b.value,M.p.colModel[O[b.idx]],b.idx,F);G=b.displayValue}else{G=M.formatter(Q,b.displayValue,O[b.idx],b.value)}}catch(c){G=b.displayValue}if(F.groupSummaryPos[b.idx]==="header"){J+='<tr id="'+Q+'"'+(F.groupCollapse&&b.idx>0?' style="display:none;" ':" ")+'role="row" class= "ui-widget-content jqgroup ui-row-'+M.p.direction+" "+H+'"><td style="padding-left:'+(b.idx*12)+'px;">'+K+A.jgrid.template(F.groupText[b.idx],G,b.cnt,b.summary)+"</td>";J+=P(a,b.idx-1,F.groups,1);J+="</tr>"}else{J+='<tr id="'+Q+'"'+(F.groupCollapse&&b.idx>0?' style="display:none;" ':" ")+'role="row" class= "ui-widget-content jqgroup ui-row-'+M.p.direction+" "+H+'"><td style="padding-left:'+(b.idx*12)+'px;" colspan="'+D+'">'+K+A.jgrid.template(F.groupText[b.idx],G,b.cnt,b.summary)+"</td></tr>"}var e=N-1===b.idx;if(e){var T=F.groups[a+1],V,X,U=0,Z=b.startRow,W=T!==undefined?F.groups[a+1].startRow:B.length;if(F._locgr){U=(E-1)*C;if(U>b.startRow){Z=U}}for(V=Z;V<W;V++){if(!B[V-U]){break}J+=B[V-U].join("")}if(F.groupSummaryPos[b.idx]!=="header"){var Y;if(T!==undefined){for(Y=0;Y<F.groupField.length;Y++){if(T.dataIndex===F.groupField[Y]){break}}R=F.groupField.length-Y}for(X=0;X<R;X++){if(!I[X]){continue}var d="";if(F.groupCollapse&&!F.showSummaryOnHide){d=' style="display:none;"'}J+="<tr"+d+' jqfootlevel="'+(b.idx-X)+'" role="row" class="ui-widget-content jqfoot ui-row-'+M.p.direction+'">';J+=P(a,X,F.groups,0);J+="</tr>"}R=Y}}});A("#"+A.jgrid.jqID(M.p.id)+" tbody:first").append(J);J=null})},groupingGroupBy:function(C,B){return this.each(function(){var F=this;if(typeof C==="string"){C=[C]}var E=F.p.groupingView;F.p.grouping=true;if(E.visibiltyOnNextGrouping===undefined){E.visibiltyOnNextGrouping=[]}var D;for(D=0;D<E.groupField.length;D++){if(!E.groupColumnShow[D]&&E.visibiltyOnNextGrouping[D]){A(F).jqGrid("showCol",E.groupField[D])}}for(D=0;D<C.length;D++){E.visibiltyOnNextGrouping[D]=A("#"+A.jgrid.jqID(F.p.id)+"_"+A.jgrid.jqID(C[D])).is(":visible")}F.p.groupingView=A.extend(F.p.groupingView,B||{});E.groupField=C;A(F).trigger("reloadGrid")})},groupingRemove:function(B){return this.each(function(){var E=this;if(B===undefined){B=true}E.p.grouping=false;if(B===true){var D=E.p.groupingView,C;for(C=0;C<D.groupField.length;C++){if(!D.groupColumnShow[C]&&D.visibiltyOnNextGrouping[C]){A(E).jqGrid("showCol",D.groupField)}}A("tr.jqgroup, tr.jqfoot","#"+A.jgrid.jqID(E.p.id)+" tbody:first").remove();A("tr.jqgrow:hidden","#"+A.jgrid.jqID(E.p.id)+" tbody:first").show()}else{A(E).trigger("reloadGrid")}})},groupingCalculations:{handler:function(H,C,G,D,B,E){var I={sum:function(){return parseFloat(C||0)+parseFloat((E[G]||0))},min:function(){if(C===""){return parseFloat(E[G]||0)}return Math.min(parseFloat(C),parseFloat(E[G]||0))},max:function(){if(C===""){return parseFloat(E[G]||0)}return Math.max(parseFloat(C),parseFloat(E[G]||0))},count:function(){if(C===""){C=0}if(E.hasOwnProperty(G)){return C+1}return 0},avg:function(){return I.sum()}};if(!I[H]){throw ("jqGrid Grouping No such method: "+H)}var J=I[H]();if(D!=null){if(B==="fixed"){J=J.toFixed(D)}else{var F=Math.pow(10,D);J=Math.round(J*F)/F}}return J}}})})(jQuery);(function(A){A.jgrid.extend({jqGridImport:function(B){B=A.extend({imptype:"xml",impstring:"",impurl:"",mtype:"GET",impData:{},xmlGrid:{config:"roots>grid",data:"roots>rows"},jsonGrid:{config:"grid",data:"data"},ajaxOptions:{}},B||{});return this.each(function(){var F=this;var C=function(G,K){var I=A(K.xmlGrid.config,G)[0];var N=A(K.xmlGrid.data,G)[0],M,J,L;if(xmlJsonClass.xml2json&&A.jgrid.parse){M=xmlJsonClass.xml2json(I," ");M=A.jgrid.parse(M);for(L in M){if(M.hasOwnProperty(L)){J=M[L]}}if(N){var H=M.grid.datatype;M.grid.datatype="xmlstring";M.grid.datastr=G;A(F).jqGrid(J).jqGrid("setGridParam",{datatype:H})}else{A(F).jqGrid(J)}M=null;J=null}else{alert("xml2json or parse are not present")}};var D=function(L,G){if(L&&typeof L==="string"){var M=false;if(A.jgrid.useJSON){A.jgrid.useJSON=false;M=true}var H=A.jgrid.parse(L);if(M){A.jgrid.useJSON=true}var J=H[G.jsonGrid.config];var I=H[G.jsonGrid.data];if(I){var K=J.datatype;J.datatype="jsonstring";J.datastr=I;A(F).jqGrid(J).jqGrid("setGridParam",{datatype:K})}else{A(F).jqGrid(J)}}};switch(B.imptype){case"xml":A.ajax(A.extend({url:B.impurl,type:B.mtype,data:B.impData,dataType:"xml",complete:function(G,H){if(H==="success"){C(G.responseXML,B);A(F).triggerHandler("jqGridImportComplete",[G,B]);if(A.isFunction(B.importComplete)){B.importComplete(G)}}G=null}},B.ajaxOptions));break;case"xmlstring":if(B.impstring&&typeof B.impstring==="string"){var E=A.parseXML(B.impstring);if(E){C(E,B);A(F).triggerHandler("jqGridImportComplete",[E,B]);if(A.isFunction(B.importComplete)){B.importComplete(E)}B.impstring=null}E=null}break;case"json":A.ajax(A.extend({url:B.impurl,type:B.mtype,data:B.impData,dataType:"json",complete:function(H){try{D(H.responseText,B);A(F).triggerHandler("jqGridImportComplete",[H,B]);if(A.isFunction(B.importComplete)){B.importComplete(H)}}catch(G){}H=null}},B.ajaxOptions));break;case"jsonstring":if(B.impstring&&typeof B.impstring==="string"){D(B.impstring,B);A(F).triggerHandler("jqGridImportComplete",[B.impstring,B]);if(A.isFunction(B.importComplete)){B.importComplete(B.impstring)}B.impstring=null}break}})},jqGridExport:function(B){B=A.extend({exptype:"xmlstring",root:"grid",ident:"\t"},B||{});var C=null;this.each(function(){if(!this.grid){return}var D,E=A.extend(true,{},A(this).jqGrid("getGridParam"));if(E.rownumbers){E.colNames.splice(0,1);E.colModel.splice(0,1)}if(E.multiselect){E.colNames.splice(0,1);E.colModel.splice(0,1)}if(E.subGrid){E.colNames.splice(0,1);E.colModel.splice(0,1)}E.knv=null;if(E.treeGrid){for(D in E.treeReader){if(E.treeReader.hasOwnProperty(D)){E.colNames.splice(E.colNames.length-1);E.colModel.splice(E.colModel.length-1)}}}switch(B.exptype){case"xmlstring":C="<"+B.root+">"+xmlJsonClass.json2xml(E,B.ident)+"</"+B.root+">";break;case"jsonstring":C="{"+xmlJsonClass.toJson(E,B.root,B.ident,false)+"}";if(E.postData.filters!==undefined){C=C.replace(/filters":"/,'filters":');C=C.replace(/}]}"/,"}]}")}break}});return C},excelExport:function(B){B=A.extend({exptype:"remote",url:null,oper:"oper",tag:"excel",exportOptions:{}},B||{});return this.each(function(){if(!this.grid){return}var C;if(B.exptype==="remote"){var D=A.extend({},this.p.postData);D[B.oper]=B.tag;var E=jQuery.param(D);if(B.url.indexOf("?")!==-1){C=B.url+"&"+E}else{C=B.url+"?"+E}window.location=C}})}})})(jQuery);(function($){if($.jgrid.msie&&$.jgrid.msiever()===8){$.expr[":"].hidden=function(elem){return elem.offsetWidth===0||elem.offsetHeight===0||elem.style.display==="none"}}$.jgrid._multiselect=false;if($.ui){if($.ui.multiselect){if($.ui.multiselect.prototype._setSelected){var setSelected=$.ui.multiselect.prototype._setSelected;$.ui.multiselect.prototype._setSelected=function(item,selected){var ret=setSelected.call(this,item,selected);if(selected&&this.selectedList){var elt=this.element;this.selectedList.find("li").each(function(){if($(this).data("optionLink")){$(this).data("optionLink").remove().appendTo(elt)}})}return ret}}if($.ui.multiselect.prototype.destroy){$.ui.multiselect.prototype.destroy=function(){this.element.show();this.container.remove();if($.Widget===undefined){$.widget.prototype.destroy.apply(this,arguments)}else{$.Widget.prototype.destroy.apply(this,arguments)}}}$.jgrid._multiselect=true}}$.jgrid.extend({sortableColumns:function(tblrow){return this.each(function(){var ts=this,tid=$.jgrid.jqID(ts.p.id);function start(){ts.p.disableClick=true}var sortable_opts={"tolerance":"pointer","axis":"x","scrollSensitivity":"1","items":">th:not(:has(#jqgh_"+tid+"_cb,#jqgh_"+tid+"_rn,#jqgh_"+tid+"_subgrid),:hidden)","placeholder":{element:function(item){var el=$(document.createElement(item[0].nodeName)).addClass(item[0].className+" ui-sortable-placeholder ui-state-highlight").removeClass("ui-sortable-helper")[0];return el},update:function(self,p){p.height(self.currentItem.innerHeight()-parseInt(self.currentItem.css("paddingTop")||0,10)-parseInt(self.currentItem.css("paddingBottom")||0,10));p.width(self.currentItem.innerWidth()-parseInt(self.currentItem.css("paddingLeft")||0,10)-parseInt(self.currentItem.css("paddingRight")||0,10))}},"update":function(event,ui){var p=$(ui.item).parent(),th=$(">th",p),colModel=ts.p.colModel,cmMap={},tid=ts.p.id+"_";$.each(colModel,function(i){cmMap[this.name]=i});var permutation=[];th.each(function(){var id=$(">div",this).get(0).id.replace(/^jqgh_/,"").replace(tid,"");if(cmMap.hasOwnProperty(id)){permutation.push(cmMap[id])}});$(ts).jqGrid("remapColumns",permutation,true,true);if($.isFunction(ts.p.sortable.update)){ts.p.sortable.update(permutation)}setTimeout(function(){ts.p.disableClick=false},50)}};if(ts.p.sortable.options){$.extend(sortable_opts,ts.p.sortable.options)}else{if($.isFunction(ts.p.sortable)){ts.p.sortable={"update":ts.p.sortable}}}if(sortable_opts.start){var s=sortable_opts.start;sortable_opts.start=function(e,ui){start();s.call(this,e,ui)}}else{sortable_opts.start=start}if(ts.p.sortable.exclude){sortable_opts.items+=":not("+ts.p.sortable.exclude+")"}tblrow.sortable(sortable_opts).data("sortable").floating=true})},columnChooser:function(opts){var self=this;if($("#colchooser_"+$.jgrid.jqID(self[0].p.id)).length){return}var selector=$('<div id="colchooser_'+self[0].p.id+'" style="position:relative;overflow:hidden"><div><select multiple="multiple"></select></div></div>');var select=$("select",selector);function insert(perm,i,v){if(i>=0){var a=perm.slice();var b=a.splice(i,Math.max(perm.length-i,i));if(i>perm.length){i=perm.length}a[i]=v;return a.concat(b)}}opts=$.extend({"width":420,"height":240,"classname":null,"done":function(perm){if(perm){self.jqGrid("remapColumns",perm,true)}},"msel":"multiselect","dlog":"dialog","dialog_opts":{"minWidth":470},"dlog_opts":function(opts){var buttons={};buttons[opts.bSubmit]=function(){opts.apply_perm();opts.cleanup(false)};buttons[opts.bCancel]=function(){opts.cleanup(true)};return $.extend(true,{"buttons":buttons,"close":function(){opts.cleanup(true)},"modal":opts.modal||false,"resizable":opts.resizable||true,"width":opts.width+20},opts.dialog_opts||{})},"apply_perm":function(){$("option",select).each(function(){if(this.selected){self.jqGrid("showCol",colModel[this.value].name)}else{self.jqGrid("hideCol",colModel[this.value].name)}});var perm=[];$("option:selected",select).each(function(){perm.push(parseInt(this.value,10))});$.each(perm,function(){delete colMap[colModel[parseInt(this,10)].name]});$.each(colMap,function(){var ti=parseInt(this,10);perm=insert(perm,ti,ti)});if(opts.done){opts.done.call(self,perm)}},"cleanup":function(calldone){call(opts.dlog,selector,"destroy");call(opts.msel,select,"destroy");selector.remove();if(calldone&&opts.done){opts.done.call(self)}},"msel_opts":{}},$.jgrid.col,opts||{});if($.ui){if($.ui.multiselect){if(opts.msel==="multiselect"){if(!$.jgrid._multiselect){alert("Multiselect plugin loaded after jqGrid. Please load the plugin before the jqGrid!");return}opts.msel_opts=$.extend($.ui.multiselect.defaults,opts.msel_opts)}}}if(opts.caption){selector.attr("title",opts.caption)}if(opts.classname){selector.addClass(opts.classname);select.addClass(opts.classname)}if(opts.width){$(">div",selector).css({"width":opts.width,"margin":"0 auto"});select.css("width",opts.width)}if(opts.height){$(">div",selector).css("height",opts.height);select.css("height",opts.height-10)}var colModel=self.jqGrid("getGridParam","colModel");var colNames=self.jqGrid("getGridParam","colNames");var colMap={},fixedCols=[];select.empty();$.each(colModel,function(i){colMap[this.name]=i;if(this.hidedlg){if(!this.hidden){fixedCols.push(i)}return}select.append("<option value='"+i+"' "+(this.hidden?"":"selected='selected'")+">"+$.jgrid.stripHtml(colNames[i])+"</option>")});function call(fn,obj){if(!fn){return}if(typeof fn==="string"){if($.fn[fn]){$.fn[fn].apply(obj,$.makeArray(arguments).slice(2))}}else{if($.isFunction(fn)){fn.apply(obj,$.makeArray(arguments).slice(2))}}}var dopts=$.isFunction(opts.dlog_opts)?opts.dlog_opts.call(self,opts):opts.dlog_opts;call(opts.dlog,selector,dopts);var mopts=$.isFunction(opts.msel_opts)?opts.msel_opts.call(self,opts):opts.msel_opts;call(opts.msel,select,mopts)},sortableRows:function(opts){return this.each(function(){var $t=this;if(!$t.grid){return}if($t.p.treeGrid){return}if($.fn.sortable){opts=$.extend({"cursor":"move","axis":"y","items":".jqgrow"},opts||{});if(opts.start&&$.isFunction(opts.start)){opts._start_=opts.start;delete opts.start}else{opts._start_=false}if(opts.update&&$.isFunction(opts.update)){opts._update_=opts.update;delete opts.update}else{opts._update_=false}opts.start=function(ev,ui){$(ui.item).css("border-width","0");$("td",ui.item).each(function(i){this.style.width=$t.grid.cols[i].style.width});if($t.p.subGrid){var subgid=$(ui.item).attr("id");try{$($t).jqGrid("collapseSubGridRow",subgid)}catch(e){}}if(opts._start_){opts._start_.apply(this,[ev,ui])}};opts.update=function(ev,ui){$(ui.item).css("border-width","");if($t.p.rownumbers===true){$("td.jqgrid-rownum",$t.rows).each(function(i){$(this).html(i+1+(parseInt($t.p.page,10)-1)*parseInt($t.p.rowNum,10))})}if(opts._update_){opts._update_.apply(this,[ev,ui])}};$("tbody:first",$t).sortable(opts);$("tbody:first",$t).disableSelection()}})},gridDnD:function(opts){return this.each(function(){var $t=this,i,cn;if(!$t.grid){return}if($t.p.treeGrid){return}if(!$.fn.draggable||!$.fn.droppable){return}function updateDnD(){var datadnd=$.data($t,"dnd");$("tr.jqgrow:not(.ui-draggable)",$t).draggable($.isFunction(datadnd.drag)?datadnd.drag.call($($t),datadnd):datadnd.drag)}var appender="<table id='jqgrid_dnd' class='ui-jqgrid-dnd'></table>";if($("#jqgrid_dnd")[0]===undefined){$("body").append(appender)}if(typeof opts==="string"&&opts==="updateDnD"&&$t.p.jqgdnd===true){updateDnD();return}opts=$.extend({"drag":function(opts){return $.extend({start:function(ev,ui){var i,subgid;if($t.p.subGrid){subgid=$(ui.helper).attr("id");try{$($t).jqGrid("collapseSubGridRow",subgid)}catch(e){}}for(i=0;i<$.data($t,"dnd").connectWith.length;i++){if($($.data($t,"dnd").connectWith[i]).jqGrid("getGridParam","reccount")===0){$($.data($t,"dnd").connectWith[i]).jqGrid("addRowData","jqg_empty_row",{})}}ui.helper.addClass("ui-state-highlight");$("td",ui.helper).each(function(i){this.style.width=$t.grid.headers[i].width+"px"});if(opts.onstart&&$.isFunction(opts.onstart)){opts.onstart.call($($t),ev,ui)}},stop:function(ev,ui){var i,ids;if(ui.helper.dropped&&!opts.dragcopy){ids=$(ui.helper).attr("id");if(ids===undefined){ids=$(this).attr("id")}$($t).jqGrid("delRowData",ids)}for(i=0;i<$.data($t,"dnd").connectWith.length;i++){$($.data($t,"dnd").connectWith[i]).jqGrid("delRowData","jqg_empty_row")}if(opts.onstop&&$.isFunction(opts.onstop)){opts.onstop.call($($t),ev,ui)}}},opts.drag_opts||{})},"drop":function(opts){return $.extend({accept:function(d){if(!$(d).hasClass("jqgrow")){return d}var tid=$(d).closest("table.ui-jqgrid-btable");if(tid.length>0&&$.data(tid[0],"dnd")!==undefined){var cn=$.data(tid[0],"dnd").connectWith;return $.inArray("#"+$.jgrid.jqID(this.id),cn)!==-1?true:false}return false},drop:function(ev,ui){if(!$(ui.draggable).hasClass("jqgrow")){return}var accept=$(ui.draggable).attr("id");var getdata=ui.draggable.parent().parent().jqGrid("getRowData",accept);if(!opts.dropbyname){var j=0,tmpdata={},nm,key;var dropmodel=$("#"+$.jgrid.jqID(this.id)).jqGrid("getGridParam","colModel");try{for(key in getdata){if(getdata.hasOwnProperty(key)){nm=dropmodel[j].name;if(!(nm==="cb"||nm==="rn"||nm==="subgrid")){if(getdata.hasOwnProperty(key)&&dropmodel[j]){tmpdata[nm]=getdata[key]}}j++}}getdata=tmpdata}catch(e){}}ui.helper.dropped=true;if(opts.beforedrop&&$.isFunction(opts.beforedrop)){var datatoinsert=opts.beforedrop.call(this,ev,ui,getdata,$("#"+$.jgrid.jqID($t.p.id)),$(this));if(datatoinsert!==undefined&&datatoinsert!==null&&typeof datatoinsert==="object"){getdata=datatoinsert}}if(ui.helper.dropped){var grid;if(opts.autoid){if($.isFunction(opts.autoid)){grid=opts.autoid.call(this,getdata)}else{grid=Math.ceil(Math.random()*1000);grid=opts.autoidprefix+grid}}$("#"+$.jgrid.jqID(this.id)).jqGrid("addRowData",grid,getdata,opts.droppos)}if(opts.ondrop&&$.isFunction(opts.ondrop)){opts.ondrop.call(this,ev,ui,getdata)}}},opts.drop_opts||{})},"onstart":null,"onstop":null,"beforedrop":null,"ondrop":null,"drop_opts":{"activeClass":"ui-state-active","hoverClass":"ui-state-hover"},"drag_opts":{"revert":"invalid","helper":"clone","cursor":"move","appendTo":"#jqgrid_dnd","zIndex":5000},"dragcopy":false,"dropbyname":false,"droppos":"first","autoid":true,"autoidprefix":"dnd_"},opts||{});if(!opts.connectWith){return}opts.connectWith=opts.connectWith.split(",");opts.connectWith=$.map(opts.connectWith,function(n){return $.trim(n)});$.data($t,"dnd",opts);if($t.p.reccount!==0&&!$t.p.jqgdnd){updateDnD()}$t.p.jqgdnd=true;for(i=0;i<opts.connectWith.length;i++){cn=opts.connectWith[i];$(cn).droppable($.isFunction(opts.drop)?opts.drop.call($($t),opts):opts.drop)}})},gridResize:function(opts){return this.each(function(){var $t=this,gID=$.jgrid.jqID($t.p.id);if(!$t.grid||!$.fn.resizable){return}opts=$.extend({},opts||{});if(opts.alsoResize){opts._alsoResize_=opts.alsoResize;delete opts.alsoResize}else{opts._alsoResize_=false}if(opts.stop&&$.isFunction(opts.stop)){opts._stop_=opts.stop;delete opts.stop}else{opts._stop_=false}opts.stop=function(ev,ui){$($t).jqGrid("setGridParam",{height:$("#gview_"+gID+" .ui-jqgrid-bdiv").height()});$($t).jqGrid("setGridWidth",ui.size.width,opts.shrinkToFit);if(opts._stop_){opts._stop_.call($t,ev,ui)}};if(opts._alsoResize_){var optstest="{'#gview_"+gID+" .ui-jqgrid-bdiv':true,'"+opts._alsoResize_+"':true}";opts.alsoResize=eval("("+optstest+")")}else{opts.alsoResize=$(".ui-jqgrid-bdiv","#gview_"+gID)}delete opts._alsoResize_;$("#gbox_"+gID).resizable(opts)})}})})(jQuery);function tableToGrid(B,A){jQuery(B).each(function(){if(this.grid){return}jQuery(this).width("99%");var F=jQuery(this).width();var L=jQuery("tr td:first-child input[type=checkbox]:first",jQuery(this));var N=jQuery("tr td:first-child input[type=radio]:first",jQuery(this));var C=L.length>0;var O=!C&&N.length>0;var D=C||O;var G=[];var H=[];jQuery("th",jQuery(this)).each(function(){if(G.length===0&&D){G.push({name:"__selection__",index:"__selection__",width:0,hidden:true});H.push("__selection__")}else{G.push({name:jQuery(this).attr("id")||jQuery.trim(jQuery.jgrid.stripHtml(jQuery(this).html())).split(" ").join("_"),index:jQuery(this).attr("id")||jQuery.trim(jQuery.jgrid.stripHtml(jQuery(this).html())).split(" ").join("_"),width:jQuery(this).width()||150});H.push(jQuery(this).html())}});var M=[];var E=[];var J=[];jQuery("tbody > tr",jQuery(this)).each(function(){var Q={};var P=0;jQuery("td",jQuery(this)).each(function(){if(P===0&&D){var S=jQuery("input",jQuery(this));var R=S.attr("value");E.push(R||M.length);if(S.is(":checked")){J.push(R)}Q[G[P].name]=S.attr("value")}else{Q[G[P].name]=jQuery(this).html()}P++});if(P>0){M.push(Q)}});jQuery(this).empty();jQuery(this).addClass("scroll");jQuery(this).jqGrid(jQuery.extend({datatype:"local",width:F,colNames:H,colModel:G,multiselect:C},A||{}));var I;for(I=0;I<M.length;I++){var K=null;if(E.length>0){K=E[I];if(K&&K.replace){K=encodeURIComponent(K).replace(/[.\-%]/g,"_")}}if(K===null){K=I+1}jQuery(this).jqGrid("addRowData",K,M[I])}for(I=0;I<J.length;I++){jQuery(this).jqGrid("setSelection",J[I])}})}(function(B){function A(F,C){var D,H,E=[],G;if(!this||typeof F!=="function"||(F instanceof RegExp)){throw new TypeError()}G=this.length;for(D=0;D<G;D++){if(this.hasOwnProperty(D)){H=this[D];if(F.call(C,H,D,this)){E.push(H);break}}}return E}B.assocArraySize=function(E){var C=0,D;for(D in E){if(E.hasOwnProperty(D)){C++}}return C};B.jgrid.extend({pivotSetup:function(E,H){var G=[],I=[],K=[],C=[],D={grouping:true,groupingView:{groupField:[],groupSummary:[],groupSummaryPos:[]}},F=[],J=B.extend({rowTotals:false,rowTotalsText:"Total",colTotals:false,groupSummary:true,groupSummaryPos:"header",frozenStaticCols:false},H||{});this.each(function(){var c,U,S,T=E.length,j,l,O,P,X,L=0;function m(r,i,t){var k;k=A.call(r,i,t);return k.length>0?k[0]:null}function Y(r,u){var v=0,t=true,k;for(k in r){if(r[k]!=this[v]){t=false;break}v++;if(v>=this.length){break}}if(t){U=u}return t}function R(k,r,u,i){var t;switch(k){case"sum":t=parseFloat(r||0)+parseFloat((i[u]||0));break;case"count":if(r===""||r==null){r=0}if(i.hasOwnProperty(u)){t=r+1}else{t=0}break;case"min":if(r===""||r==null){t=parseFloat(i[u]||0)}else{t=Math.min(parseFloat(r),parseFloat(i[u]||0))}break;case"max":if(r===""||r==null){t=parseFloat(i[u]||0)}else{t=Math.max(parseFloat(r),parseFloat(i[u]||0))}break}return t}function N(Ac,u,w,y){var r=u.length,x,Ab,Aa,z;if(B.isArray(w)){z=w.length}else{z=1}C=[];C.root=0;for(Aa=0;Aa<z;Aa++){var v=[],t;for(x=0;x<r;x++){if(w==null){Ab=B.trim(u[x].member)+"_"+u[x].aggregator;t=Ab}else{t=w[Aa].replace(/\s+/g,"");try{Ab=(r===1?t:t+"_"+u[x].aggregator+"_"+x)}catch(k){}}y[Ab]=v[Ab]=R(u[x].aggregator,y[Ab],u[x].member,Ac)}C[t]=v}return y}if(J.rowTotals&&J.yDimension.length>0){var q=J.yDimension[0].dataName;J.yDimension.splice(0,0,{dataName:q});J.yDimension[0].converter=function(){return"_r_Totals"}}j=B.isArray(J.xDimension)?J.xDimension.length:0;l=J.yDimension.length;O=B.isArray(J.aggregates)?J.aggregates.length:0;if(j===0||O===0){throw ("xDimension or aggregates optiona are not set!")}var d;for(S=0;S<j;S++){d={name:J.xDimension[S].dataName,frozen:J.frozenStaticCols};d=B.extend(true,d,J.xDimension[S]);G.push(d)}var h=j-1,Q={};while(L<T){c=E[L];var g=[];var e=[];P={};S=0;do{g[S]=B.trim(c[J.xDimension[S].dataName]);P[J.xDimension[S].dataName]=g[S];S++}while(S<j);var f=0;U=-1;X=m(I,Y,g);if(!X){f=0;if(l>=1){for(f=0;f<l;f++){e[f]=B.trim(c[J.yDimension[f].dataName]);if(J.yDimension[f].converter&&B.isFunction(J.yDimension[f].converter)){e[f]=J.yDimension[f].converter.call(this,e[f],g,e)}}P=N(c,J.aggregates,e,P)}else{if(l===0){P=N(c,J.aggregates,null,P)}}I.push(P)}else{if(U>=0){f=0;if(l>=1){for(f=0;f<l;f++){e[f]=B.trim(c[J.yDimension[f].dataName]);if(J.yDimension[f].converter&&B.isFunction(J.yDimension[f].converter)){e[f]=J.yDimension[f].converter.call(this,e[f],g,e)}}X=N(c,J.aggregates,e,X)}else{if(l===0){X=N(c,J.aggregates,null,X)}}I[U]=X}}var V=0,Z=null,o=null,s;for(s in C){if(V===0){if(!Q.children||Q.children===undefined){Q={text:s,level:0,children:[]}}Z=Q.children}else{o=null;for(S=0;S<Z.length;S++){if(Z[S].text===s){o=Z[S];break}}if(o){Z=o.children}else{Z.push({children:[],text:s,level:V,fields:C[s]});Z=Z[Z.length-1].children}}V++}L++}var a=[],p=G.length,b=p;if(l>0){F[l-1]={useColSpanStyle:false,groupHeaders:[]}}function M(y){var v,r,x,t,z;for(x in y){if(y.hasOwnProperty(x)){if(typeof y[x]!=="object"){if(x==="level"){if(a[y.level]===undefined){a[y.level]="";if(y.level>0&&y.text!=="_r_Totals"){F[y.level-1]={useColSpanStyle:false,groupHeaders:[]}}}if(a[y.level]!==y.text&&y.children.length&&y.text!=="_r_Totals"){if(y.level>0){F[y.level-1].groupHeaders.push({titleText:y.text});var i=F[y.level-1].groupHeaders.length,u=i===1?b:p+(i-1)*O;F[y.level-1].groupHeaders[i-1].startColumnName=G[u].name;F[y.level-1].groupHeaders[i-1].numberOfColumns=G.length-u;p=G.length}}a[y.level]=y.text}if(y.level===l&&x==="level"&&l>0){if(O>1){var w=1;for(v in y.fields){if(w===1){F[l-1].groupHeaders.push({startColumnName:v,numberOfColumns:1,titleText:y.text})}w++}F[l-1].groupHeaders[F[l-1].groupHeaders.length-1].numberOfColumns=w-1}else{F.splice(l-1,1)}}}if(y[x]!=null&&typeof y[x]==="object"){M(y[x])}if(x==="level"){if(y.level>0){r=0;for(v in y.fields){z={};for(t in J.aggregates[r]){if(J.aggregates[r].hasOwnProperty(t)){switch(t){case"member":case"label":case"aggregator":break;default:z[t]=J.aggregates[r][t]}}}if(O>1){z.name=v;z.label=J.aggregates[r].label||v}else{z.name=y.text;z.label=y.text==="_r_Totals"?J.rowTotalsText:y.text}G.push(z);r++}}}}}}M(Q,0);var n;if(J.colTotals){var W=I.length;while(W--){for(S=j;S<G.length;S++){n=G[S].name;if(!K[n]){K[n]=parseFloat(I[W][n]||0)}else{K[n]+=parseFloat(I[W][n]||0)}}}}if(h>0){for(S=0;S<h;S++){D.groupingView.groupField[S]=G[S].name;D.groupingView.groupSummary[S]=J.groupSummary;D.groupingView.groupSummaryPos[S]=J.groupSummaryPos}}else{D.grouping=false}D.sortname=G[h].name;D.groupingView.hideFirstGroupCol=true});return{"colModel":G,"rows":I,"groupOptions":D,"groupHeaders":F,summary:K}},jqPivot:function(F,D,C,E){return this.each(function(){var H=this;function G(N){var L=jQuery(H).jqGrid("pivotSetup",N,D),M=B.assocArraySize(L.summary)>0?true:false,K=B.jgrid.from(L.rows),I;for(I=0;I<L.groupOptions.groupingView.groupField.length;I++){K.orderBy(L.groupOptions.groupingView.groupField[I],"a","text","")}jQuery(H).jqGrid(B.extend({datastr:B.extend(K.select(),M?{userdata:L.summary}:{}),datatype:"jsonstring",footerrow:M,userDataOnFooter:M,colModel:L.colModel,viewrecords:true,sortname:D.xDimension[0].dataName},C||{},L.groupOptions));var J=L.groupHeaders;if(J.length){for(I=0;I<J.length;I++){if(J[I]&&J[I].groupHeaders.length){jQuery(H).jqGrid("setGroupHeaders",J[I])}}}if(D.frozenStaticCols){jQuery(H).jqGrid("setFrozenColumns")}}if(typeof F==="string"){B.ajax(B.extend({url:F,dataType:"json",success:function(I){G(B.jgrid.getAccessor(I,E&&E.reader?E.reader:"rows"))}},E||{}))}else{G(F)}})}})})(jQuery);