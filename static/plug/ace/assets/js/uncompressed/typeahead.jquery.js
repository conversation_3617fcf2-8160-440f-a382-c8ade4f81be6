/*
 * typeahead.js 0.10.2
 * https://github.com/twitter/typeahead.js
 * Copyright 2013-2014 Twitter, Inc. and other contributors; Licensed MIT
 */
(function(I){var F={isMsie:function(){return/(msie|trident)/i.test(navigator.userAgent)?navigator.userAgent.match(/(msie |rv:)(\d+(.\d+)?)/i)[2]:false},isBlankString:function(M){return !M||/^\s*$/.test(M)},escapeRegExChars:function(M){return M.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&")},isString:function(M){return typeof M==="string"},isNumber:function(M){return typeof M==="number"},isArray:I.isArray,isFunction:I.isFunction,isObject:I.isPlainObject,isUndefined:function(M){return typeof M==="undefined"},bind:I.proxy,each:function(N,M){I.each(N,O);function O(Q,P){return M(P,Q)}},map:I.map,filter:I.grep,every:function(N,M){var O=true;if(!N){return O}I.each(N,function(P,Q){if(!(O=M.call(null,Q,P,N))){return false}});return !!O},some:function(N,M){var O=false;if(!N){return O}I.each(N,function(P,Q){if(O=M.call(null,Q,P,N)){return false}});return !!O},mixin:I.extend,getUniqueId:function(){var M=0;return function(){return M++}}(),templatify:function E(M){return I.isFunction(M)?M:N;function N(){return String(M)}},defer:function(M){setTimeout(M,0)},debounce:function(N,Q,P){var M,O;return function(){var R=this,U=arguments,S,T;S=function(){M=null;if(!P){O=N.apply(R,U)}};T=P&&!M;clearTimeout(M);M=setTimeout(S,Q);if(T){O=N.apply(R,U)}return O}},throttle:function(T,Q){var P,M,N,O,S,R;S=0;R=function(){S=new Date();N=null;O=T.apply(P,M)};return function(){var V=new Date(),U=Q-(V-S);P=this;M=arguments;if(U<=0){clearTimeout(N);N=null;S=V;O=T.apply(P,M)}else{if(!N){N=setTimeout(R,U)}}return O}},noop:function(){}};var K={wrapper:'<span class="twitter-typeahead"></span>',dropdown:'<span class="tt-dropdown-menu"></span>',dataset:'<div class="tt-dataset-%CLASS%"></div>',suggestions:'<span class="tt-suggestions"></span>',suggestion:'<div class="tt-suggestion"></div>'};var C={wrapper:{position:"relative",display:"inline-block"},hint:{position:"absolute",top:"0",left:"0",borderColor:"transparent",boxShadow:"none"},input:{position:"relative",verticalAlign:"top",backgroundColor:"transparent"},inputWithNoHint:{position:"relative",verticalAlign:"top"},dropdown:{position:"absolute",top:"100%",left:"0",zIndex:"100",display:"none"},suggestions:{display:"block"},suggestion:{whiteSpace:"nowrap",cursor:"pointer"},suggestionChild:{whiteSpace:"normal"},ltr:{left:"0",right:"auto"},rtl:{left:"auto",right:" 0"}};if(F.isMsie()){F.mixin(C.input,{backgroundImage:"url(data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7)"})}if(F.isMsie()&&F.isMsie()<=7){F.mixin(C.input,{marginTop:"-1px"})}var L=function(){var M="typeahead:";function N(O){if(!O||!O.el){I.error("EventBus initialized without el")}this.$el=I(O.el)}F.mixin(N.prototype,{trigger:function(O){var P=[].slice.call(arguments,1);this.$el.trigger(M+O,P)}});return N}();var J=function(){var S=/\s+/,V=R();return{onSync:N,onAsync:U,off:T,trigger:M};function Q(Z,Y,W,X){var a;if(!W){return this}Y=Y.split(S);W=X?P(W,X):W;this._callbacks=this._callbacks||{};while(a=Y.shift()){this._callbacks[a]=this._callbacks[a]||{sync:[],async:[]};this._callbacks[a][Z].push(W)}return this}function U(Y,W,X){return Q.call(this,"async",Y,W,X)}function N(Y,W,X){return Q.call(this,"sync",Y,W,X)}function T(X){var W;if(!this._callbacks){return this}X=X.split(S);while(W=X.shift()){delete this._callbacks[W]}return this}function M(Y){var W,b,a,Z,X;if(!this._callbacks){return this}Y=Y.split(S);a=[].slice.call(arguments,1);while((W=Y.shift())&&(b=this._callbacks[W])){Z=O(b.sync,this,[W].concat(a));X=O(b.async,this,[W].concat(a));Z()&&V(X)}return this}function O(Z,W,Y){return X;function X(){var b;for(var a=0;!b&&a<Z.length;a+=1){b=Z[a].apply(W,Y)===false}return !b}}function R(){var Y;if(window.setImmediate){Y=function X(Z){setImmediate(function(){Z()})}}else{Y=function W(Z){setTimeout(function(){Z()},0)}}return Y}function P(X,W){return X.bind?X.bind(W):function(){X.apply(W,[].slice.call(arguments,0))}}}();var D=function(P){var M={node:null,pattern:null,tagName:"strong",className:null,wordsOnly:false,caseSensitive:false};return function O(R){var Q;R=F.mixin({},M,R);if(!R.node||!R.pattern){return}R.pattern=F.isArray(R.pattern)?R.pattern:[R.pattern];Q=N(R.pattern,R.caseSensitive,R.wordsOnly);S(R.node,T);function T(V){var U,W;if(U=Q.exec(V.data)){wrapperNode=P.createElement(R.tagName);R.className&&(wrapperNode.className=R.className);W=V.splitText(U.index);W.splitText(U[0].length);wrapperNode.appendChild(W.cloneNode(true));V.parentNode.replaceChild(wrapperNode,W)}return !!U}function S(U,V){var X,Y=3;for(var W=0;W<U.childNodes.length;W++){X=U.childNodes[W];if(X.nodeType===Y){W+=V(X)?1:0}else{S(X,V)}}}};function N(V,T,R){var S=[],U;for(var Q=0;Q<V.length;Q++){S.push(F.escapeRegExChars(V[Q]))}U=R?"\\b("+S.join("|")+")\\b":"("+S.join("|")+")";return T?new RegExp(U):new RegExp(U,"i")}}(window.document);var G=function(){var g;g={9:"tab",27:"esc",37:"left",39:"right",13:"enter",38:"up",40:"down"};function b(n){var r=this,m,q,s,p;n=n||{};if(!n.input){I.error("input is missing")}m=F.bind(this._onBlur,this);q=F.bind(this._onFocus,this);s=F.bind(this._onKeydown,this);p=F.bind(this._onInput,this);this.$hint=I(n.hint);this.$input=I(n.input).on("blur.tt",m).on("focus.tt",q).on("keydown.tt",s);if(this.$hint.length===0){this.setHint=this.getHint=this.clearHint=this.clearHintIfInvalid=F.noop}if(!F.isMsie()){this.$input.on("input.tt",p)}else{this.$input.on("keydown.tt keypress.tt cut.tt paste.tt",function(o){if(g[o.which||o.keyCode]){return}F.defer(F.bind(r._onInput,r,o))})}this.query=this.$input.val();this.$overflowHelper=T(this.$input)}b.normalizeQuery=function(m){return(m||"").replace(/^\s*/g,"").replace(/\s{2,}/g," ")};F.mixin(b.prototype,J,{_onBlur:function c(){this.resetInputValue();this.trigger("blurred")},_onFocus:function i(){this.trigger("focused")},_onKeydown:function S(n){var m=g[n.which||n.keyCode];this._managePreventDefault(m,n);if(m&&this._shouldTrigger(m,n)){this.trigger(m+"Keyed",n)}},_onInput:function e(){this._checkInputValue()},_managePreventDefault:function N(o,q){var p,m,n;switch(o){case"tab":m=this.getHint();n=this.getInputValue();p=m&&m!==n&&!k(q);break;case"up":case"down":p=!k(q);break;default:p=false}p&&q.preventDefault()},_shouldTrigger:function j(m,o){var n;switch(m){case"tab":n=!k(o);break;default:n=true}return n},_checkInputValue:function Z(){var m,n,o;m=this.getInputValue();n=W(m,this.query);o=n?this.query.length!==m.length:false;if(!n){this.trigger("queryChanged",this.query=m)}else{if(o){this.trigger("whitespaceChanged",this.query)}}},focus:function X(){this.$input.focus()},blur:function a(){this.$input.blur()},getQuery:function M(){return this.query},setQuery:function O(m){this.query=m},getInputValue:function h(){return this.$input.val()},setInputValue:function Q(m,n){this.$input.val(m);n?this.clearHint():this._checkInputValue()},resetInputValue:function V(){this.setInputValue(this.query,true)},getHint:function f(){return this.$hint.val()},setHint:function U(m){this.$hint.val(m)},clearHint:function P(){this.setHint("")},clearHintIfInvalid:function d(){var p,m,n,o;p=this.getInputValue();m=this.getHint();n=p!==m&&m.indexOf(p)===0;o=p!==""&&n&&!this.hasOverflow();!o&&this.clearHint()},getLanguageDirection:function l(){return(this.$input.css("direction")||"ltr").toLowerCase()},hasOverflow:function Y(){var m=this.$input.width()-2;this.$overflowHelper.text(this.getInputValue());return this.$overflowHelper.width()>=m},isCursorAtEnd:function(){var n,m,o;n=this.$input.val().length;m=this.$input[0].selectionStart;if(F.isNumber(m)){return m===n}else{if(document.selection){o=document.selection.createRange();o.moveStart("character",-n);return n===o.text.length}}return true},destroy:function R(){this.$hint.off(".tt");this.$input.off(".tt");this.$hint=this.$input=this.$overflowHelper=null}});return b;function T(m){return I('<pre aria-hidden="true"></pre>').css({position:"absolute",visibility:"hidden",whiteSpace:"pre",fontFamily:m.css("font-family"),fontSize:m.css("font-size"),fontStyle:m.css("font-style"),fontVariant:m.css("font-variant"),fontWeight:m.css("font-weight"),wordSpacing:m.css("word-spacing"),letterSpacing:m.css("letter-spacing"),textIndent:m.css("text-indent"),textRendering:m.css("text-rendering"),textTransform:m.css("text-transform")}).insertAfter(m)}function W(n,m){return b.normalizeQuery(n)===b.normalizeQuery(m)}function k(m){return m.altKey||m.ctrlKey||m.metaKey||m.shiftKey}}();var H=function(){var R="ttDataset",T="ttValue",a="ttDatum";function V(c){c=c||{};c.templates=c.templates||{};if(!c.source){I.error("missing source")}if(c.name&&!M(c.name)){I.error("invalid dataset name: "+c.name)}this.query=null;this.highlight=!!c.highlight;this.name=c.name||F.getUniqueId();this.source=c.source;this.displayFn=S(c.display||c.displayKey);this.templates=Z(c.templates,this.displayFn);this.$el=I(K.dataset.replace("%CLASS%",this.name))}V.extractDatasetName=function O(c){return I(c).data(R)};V.extractValue=function Y(c){return I(c).data(T)};V.extractDatum=function Y(c){return I(c).data(a)};F.mixin(V.prototype,J,{_render:function U(f,j){if(!this.$el){return}var h=this,c;this.$el.empty();c=j&&j.length;if(!c&&this.templates.empty){this.$el.html(d()).prepend(h.templates.header?g():null).append(h.templates.footer?i():null)}else{if(c){this.$el.html(e()).prepend(h.templates.header?g():null).append(h.templates.footer?i():null)}}this.trigger("rendered");function d(){return h.templates.empty({query:f,isEmpty:true})}function e(){var m,l;m=I(K.suggestions).css(C.suggestions);l=F.map(j,k);m.append.apply(m,l);h.highlight&&D({node:m[0],pattern:f});return m;function k(n){var o;o=I(K.suggestion).append(h.templates.suggestion(n)).data(R,h.name).data(T,h.displayFn(n)).data(a,n);o.children().each(function(){I(this).css(C.suggestionChild)});return o}}function g(){return h.templates.header({query:f,isEmpty:!c})}function i(){return h.templates.footer({query:f,isEmpty:!c})}},getRoot:function b(){return this.$el},update:function P(e){var c=this;this.query=e;this.canceled=false;this.source(e,d);function d(f){if(!c.canceled&&e===c.query){c._render(e,f)}}},cancel:function N(){this.canceled=true},clear:function Q(){this.cancel();this.$el.empty();this.trigger("rendered")},isEmpty:function X(){return this.$el.is(":empty")},destroy:function W(){this.$el=null}});return V;function S(c){c=c||"value";return F.isFunction(c)?c:d;function d(e){return e[c]}}function Z(e,c){return{empty:e.empty&&F.templatify(e.empty),header:e.header&&F.templatify(e.header),footer:e.footer&&F.templatify(e.footer),suggestion:e.suggestion||d};function d(f){return"<p>"+c(f)+"</p>"}}function M(c){return/^[_a-zA-Z0-9-]+$/.test(c)}}();var B=function(){function S(m){var p=this,l,n,k;m=m||{};if(!m.menu){I.error("menu is required")}this.isOpen=false;this.isEmpty=true;this.datasets=F.map(m.datasets,a);l=F.bind(this._onSuggestionClick,this);n=F.bind(this._onSuggestionMouseEnter,this);k=F.bind(this._onSuggestionMouseLeave,this);this.$menu=I(m.menu).on("click.tt",".tt-suggestion",l).on("mouseenter.tt",".tt-suggestion",n).on("mouseleave.tt",".tt-suggestion",k);F.each(this.datasets,function(o){p.$menu.append(o.getRoot());o.onSync("rendered",p._onRendered,p)})}F.mixin(S.prototype,J,{_onSuggestionClick:function P(k){this.trigger("suggestionClicked",I(k.currentTarget))},_onSuggestionMouseEnter:function Y(k){this._removeCursor();this._setCursor(I(k.currentTarget),true)},_onSuggestionMouseLeave:function e(){this._removeCursor()},_onRendered:function O(){this.isEmpty=F.every(this.datasets,k);this.isEmpty?this._hide():this.isOpen&&this._show();this.trigger("datasetRendered");function k(l){return l.isEmpty()}},_hide:function(){this.$menu.hide()},_show:function(){this.$menu.css("display","block")},_getSuggestions:function c(){return this.$menu.find(".tt-suggestion")},_getCursor:function d(){return this.$menu.find(".tt-cursor").first()},_setCursor:function M(k,l){k.first().addClass("tt-cursor");!l&&this.trigger("cursorMoved")},_removeCursor:function f(){this._getCursor().removeClass("tt-cursor")},_moveCursor:function U(k){var m,o,n,l;if(!this.isOpen){return}o=this._getCursor();m=this._getSuggestions();this._removeCursor();n=m.index(o)+k;n=(n+1)%(m.length+1)-1;if(n===-1){this.trigger("cursorRemoved");return}else{if(n<-1){n=m.length-1}}this._setCursor(l=m.eq(n));this._ensureVisible(l)},_ensureVisible:function T(n){var k,l,o,m;k=n.position().top;l=k+n.outerHeight(true);o=this.$menu.scrollTop();m=this.$menu.height()+parseInt(this.$menu.css("paddingTop"),10)+parseInt(this.$menu.css("paddingBottom"),10);if(k<0){this.$menu.scrollTop(o+k)}else{if(m<l){this.$menu.scrollTop(o+(l-m))}}},close:function W(){if(this.isOpen){this.isOpen=false;this._removeCursor();this._hide();this.trigger("closed")}},open:function j(){if(!this.isOpen){this.isOpen=true;!this.isEmpty&&this._show();this.trigger("opened")}},setLanguageDirection:function b(k){this.$menu.css(k==="ltr"?C.ltr:C.rtl)},moveCursorUp:function Z(){this._moveCursor(-1)},moveCursorDown:function Q(){this._moveCursor(+1)},getDatumForSuggestion:function X(k){var l=null;if(k.length){l={raw:H.extractDatum(k),value:H.extractValue(k),datasetName:H.extractDatasetName(k)}}return l},getDatumForCursor:function h(){return this.getDatumForSuggestion(this._getCursor().first())},getDatumForTopSuggestion:function R(){return this.getDatumForSuggestion(this._getSuggestions().first())},update:function g(k){F.each(this.datasets,l);function l(m){m.update(k)}},empty:function N(){F.each(this.datasets,k);this.isEmpty=true;function k(l){l.clear()}},isVisible:function i(){return this.isOpen&&!this.isEmpty},destroy:function V(){this.$menu.off(".tt");this.$menu=null;F.each(this.datasets,k);function k(l){l.destroy()}}});return S;function a(k){return new H(k)}}();var A=function(){var M="ttAttrs";function h(s){var u,r,t;s=s||{};if(!s.input){I.error("missing input")}this.isActivated=false;this.autoselect=!!s.autoselect;this.minLength=F.isNumber(s.minLength)?s.minLength:1;this.$node=f(s.input,s.withHint);u=this.$node.find(".tt-dropdown-menu");r=this.$node.find(".tt-input");t=this.$node.find(".tt-hint");r.on("blur.tt",function(y){var x,w,v;x=document.activeElement;w=u.is(x);v=u.has(x).length>0;if(F.isMsie()&&(w||v)){y.preventDefault();y.stopImmediatePropagation();F.defer(function(){r.focus()})}});u.on("mousedown.tt",function(v){v.preventDefault()});this.eventBus=s.eventBus||new L({el:r});this.dropdown=new B({menu:u,datasets:s.datasets}).onSync("suggestionClicked",this._onSuggestionClicked,this).onSync("cursorMoved",this._onCursorMoved,this).onSync("cursorRemoved",this._onCursorRemoved,this).onSync("opened",this._onOpened,this).onSync("closed",this._onClosed,this).onAsync("datasetRendered",this._onDatasetRendered,this);this.input=new G({input:r,hint:t}).onSync("focused",this._onFocused,this).onSync("blurred",this._onBlurred,this).onSync("enterKeyed",this._onEnterKeyed,this).onSync("tabKeyed",this._onTabKeyed,this).onSync("escKeyed",this._onEscKeyed,this).onSync("upKeyed",this._onUpKeyed,this).onSync("downKeyed",this._onDownKeyed,this).onSync("leftKeyed",this._onLeftKeyed,this).onSync("rightKeyed",this._onRightKeyed,this).onSync("queryChanged",this._onQueryChanged,this).onSync("whitespaceChanged",this._onWhitespaceChanged,this);this._setLanguageDirection()}F.mixin(h.prototype,{_onSuggestionClicked:function P(r,s){var t;if(t=this.dropdown.getDatumForSuggestion(s)){this._select(t)}},_onCursorMoved:function g(){var r=this.dropdown.getDatumForCursor();this.input.setInputValue(r.value,true);this.eventBus.trigger("cursorchanged",r.raw,r.datasetName)},_onCursorRemoved:function U(){this.input.resetInputValue();this._updateHint()},_onDatasetRendered:function k(){this._updateHint()},_onOpened:function e(){this._updateHint();this.eventBus.trigger("opened")},_onClosed:function Q(){this.input.clearHint();this.eventBus.trigger("closed")},_onFocused:function X(){this.isActivated=true;this.dropdown.open()},_onBlurred:function l(){this.isActivated=false;this.dropdown.empty();this.dropdown.close()},_onEnterKeyed:function n(r,u){var t,s;t=this.dropdown.getDatumForCursor();s=this.dropdown.getDatumForTopSuggestion();if(t){this._select(t);u.preventDefault()}else{if(this.autoselect&&s){this._select(s);u.preventDefault()}}},_onTabKeyed:function W(r,t){var s;if(s=this.dropdown.getDatumForCursor()){this._select(s);t.preventDefault()}else{this._autocomplete(true)}},_onEscKeyed:function R(){this.dropdown.close();this.input.resetInputValue()},_onUpKeyed:function c(){var r=this.input.getQuery();this.dropdown.isEmpty&&r.length>=this.minLength?this.dropdown.update(r):this.dropdown.moveCursorUp();this.dropdown.open()},_onDownKeyed:function p(){var r=this.input.getQuery();this.dropdown.isEmpty&&r.length>=this.minLength?this.dropdown.update(r):this.dropdown.moveCursorDown();this.dropdown.open()},_onLeftKeyed:function d(){this.dir==="rtl"&&this._autocomplete()},_onRightKeyed:function m(){this.dir==="ltr"&&this._autocomplete()},_onQueryChanged:function N(r,s){this.input.clearHintIfInvalid();s.length>=this.minLength?this.dropdown.update(s):this.dropdown.empty();this.dropdown.open();this._setLanguageDirection()},_onWhitespaceChanged:function o(){this._updateHint();this.dropdown.open()},_setLanguageDirection:function a(){var r;if(this.dir!==(r=this.input.getLanguageDirection())){this.dir=r;this.$node.css("direction",r);this.dropdown.setLanguageDirection(r)}},_updateHint:function S(){var w,u,t,v,s,r;w=this.dropdown.getDatumForTopSuggestion();if(w&&this.dropdown.isVisible()&&!this.input.hasOverflow()){u=this.input.getInputValue();t=G.normalizeQuery(u);v=F.escapeRegExChars(t);s=new RegExp("^(?:"+v+")(.+$)","i");r=s.exec(w.value);r?this.input.setHint(u+r[1]):this.input.clearHint()}else{this.input.clearHint()}},_autocomplete:function j(r){var s,t,u,v;s=this.input.getHint();t=this.input.getQuery();u=r||this.input.isCursorAtEnd();if(s&&t!==s&&u){v=this.dropdown.getDatumForTopSuggestion();v&&this.input.setInputValue(v.value);this.eventBus.trigger("autocompleted",v.raw,v.datasetName)}},_select:function b(r){this.input.setQuery(r.value);this.input.setInputValue(r.value,true);this._setLanguageDirection();this.eventBus.trigger("selected",r.raw,r.datasetName);this.dropdown.close();F.defer(F.bind(this.dropdown.empty,this.dropdown))},open:function q(){this.dropdown.open()},close:function V(){this.dropdown.close()},setVal:function Y(r){if(this.isActivated){this.input.setInputValue(r)}else{this.input.setQuery(r);this.input.setInputValue(r,true)}this._setLanguageDirection()},getVal:function O(){return this.input.getQuery()},destroy:function T(){this.input.destroy();this.dropdown.destroy();i(this.$node);this.$node=null}});return h;function f(u,w){var r,s,x,v;r=I(u);s=I(K.wrapper).css(C.wrapper);x=I(K.dropdown).css(C.dropdown);v=r.clone().css(C.hint).css(Z(r));v.val("").removeData().addClass("tt-hint").removeAttr("id name placeholder").prop("disabled",true).attr({autocomplete:"off",spellcheck:"false"});r.data(M,{dir:r.attr("dir"),autocomplete:r.attr("autocomplete"),spellcheck:r.attr("spellcheck"),style:r.attr("style")});r.addClass("tt-input").attr({autocomplete:"off",spellcheck:false}).css(w?C.input:C.inputWithNoHint);try{!r.attr("dir")&&r.attr("dir","auto")}catch(t){}return r.wrap(s).parent().prepend(w?v:null).append(x)}function Z(r){return{backgroundAttachment:r.css("background-attachment"),backgroundClip:r.css("background-clip"),backgroundColor:r.css("background-color"),backgroundImage:r.css("background-image"),backgroundOrigin:r.css("background-origin"),backgroundPosition:r.css("background-position"),backgroundRepeat:r.css("background-repeat"),backgroundSize:r.css("background-size")}}function i(s){var r=s.find(".tt-input");F.each(r.data(M),function(u,t){F.isUndefined(u)?r.removeAttr(t):r.attr(t,u)});r.detach().removeData(M).removeClass("tt-input").insertAfter(s);s.remove()}}();(function(){var M,O,Q;M=I.fn.typeahead;O="ttTypeahead";Q={initialize:function U(V,W){W=F.isArray(W)?W:[].slice.call(arguments,1);V=V||{};return this.each(X);function X(){var Y=I(this),Z,a;F.each(W,function(b){b.highlight=!!V.highlight});a=new A({input:Y,eventBus:Z=new L({el:Y}),withHint:F.isUndefined(V.hint)?true:!!V.hint,minLength:V.minLength,autoselect:V.autoselect,datasets:W});Y.data(O,a)}},open:function S(){return this.each(V);function V(){var W=I(this),X;if(X=W.data(O)){X.open()}}},close:function P(){return this.each(V);function V(){var W=I(this),X;if(X=W.data(O)){X.close()}}},val:function R(V){return !arguments.length?X(this.first()):this.each(W);function W(){var Y=I(this),Z;if(Z=Y.data(O)){Z.setVal(V)}}function X(Y){var Z,a;if(Z=Y.data(O)){a=Z.getVal()}return a}},destroy:function T(){return this.each(V);function V(){var W=I(this),X;if(X=W.data(O)){X.destroy();W.removeData(O)}}}};I.fn.typeahead=function(V){if(Q[V]){return Q[V].apply(this,[].slice.call(arguments,1))}else{return Q.initialize.apply(this,arguments)}};I.fn.typeahead.noConflict=function N(){I.fn.typeahead=M;return this}})()})(window.jQuery);