/*
 * jQuery Validation Plugin v1.12.0
 *
 * http://jqueryvalidation.org/
 *
 * Copyright (c) 2014 <PERSON><PERSON><PERSON>
 * Released under the MIT license
 */
(function(B){B.extend(B.fn,{validate:function(C){if(!this.length){if(C&&C.debug&&window.console){console.warn("Nothing selected, can't validate, returning nothing.")}return}var D=B.data(this[0],"validator");if(D){return D}this.attr("novalidate","novalidate");D=new B.validator(C,this[0]);B.data(this[0],"validator",D);if(D.settings.onsubmit){this.validateDelegate(":submit","click",function(E){if(D.settings.submitHandler){D.submitButton=E.target}if(B(E.target).hasClass("cancel")){D.cancelSubmit=true}if(B(E.target).attr("formnovalidate")!==undefined){D.cancelSubmit=true}});this.submit(function(F){if(D.settings.debug){F.preventDefault()}function E(){var G;if(D.settings.submitHandler){if(D.submitButton){G=B("<input type='hidden'/>").attr("name",D.submitButton.name).val(B(D.submitButton).val()).appendTo(D.currentForm)}D.settings.submitHandler.call(D,D.currentForm,F);if(D.submitButton){G.remove()}return false}return true}if(D.cancelSubmit){D.cancelSubmit=false;return E()}if(D.form()){if(D.pendingRequest){D.formSubmitted=true;return false}return E()}else{D.focusInvalid();return false}})}return D},valid:function(){var C,D;if(B(this[0]).is("form")){C=this.validate().form()}else{C=true;D=B(this[0].form).validate();this.each(function(){C=D.element(this)&&C})}return C},removeAttrs:function(C){var D={},E=this;B.each(C.split(/\s/),function(G,F){D[F]=E.attr(F);E.removeAttr(F)});return D},rules:function(H,G){var E=this[0],D,I,C,F,K,J;if(H){D=B.data(E.form,"validator").settings;I=D.rules;C=B.validator.staticRules(E);switch(H){case"add":B.extend(C,B.validator.normalizeRule(G));delete C.messages;I[E.name]=C;if(G.messages){D.messages[E.name]=B.extend(D.messages[E.name],G.messages)}break;case"remove":if(!G){delete I[E.name];return C}J={};B.each(G.split(/\s/),function(M,L){J[L]=C[L];delete C[L];if(L==="required"){B(E).removeAttr("aria-required")}});return J}}F=B.validator.normalizeRules(B.extend({},B.validator.classRules(E),B.validator.attributeRules(E),B.validator.dataRules(E),B.validator.staticRules(E)),E);if(F.required){K=F.required;delete F.required;F=B.extend({required:K},F);B(E).attr("aria-required","true")}if(F.remote){K=F.remote;delete F.remote;F=B.extend(F,{remote:K})}return F}});B.extend(B.expr[":"],{blank:function(C){return !B.trim(""+B(C).val())},filled:function(C){return !!B.trim(""+B(C).val())},unchecked:function(C){return !B(C).prop("checked")}});B.validator=function(D,C){this.settings=B.extend(true,{},B.validator.defaults,D);this.currentForm=C;this.init()};B.validator.format=function(C,D){if(arguments.length===1){return function(){var E=B.makeArray(arguments);E.unshift(C);return B.validator.format.apply(this,E)}}if(arguments.length>2&&D.constructor!==Array){D=B.makeArray(arguments).slice(1)}if(D.constructor!==Array){D=[D]}B.each(D,function(E,F){C=C.replace(new RegExp("\\{"+E+"\\}","g"),function(){return F})});return C};B.extend(B.validator,{defaults:{messages:{},groups:{},rules:{},errorClass:"error",validClass:"valid",errorElement:"label",focusInvalid:true,errorContainer:B([]),errorLabelContainer:B([]),onsubmit:true,ignore:":hidden",ignoreTitle:false,onfocusin:function(C){this.lastActive=C;if(this.settings.focusCleanup&&!this.blockFocusCleanup){if(this.settings.unhighlight){this.settings.unhighlight.call(this,C,this.settings.errorClass,this.settings.validClass)}this.addWrapper(this.errorsFor(C)).hide()}},onfocusout:function(C){if(!this.checkable(C)&&(C.name in this.submitted||!this.optional(C))){this.element(C)}},onkeyup:function(D,C){if(C.which===9&&this.elementValue(D)===""){return}else{if(D.name in this.submitted||D===this.lastElement){this.element(D)}}},onclick:function(C){if(C.name in this.submitted){this.element(C)}else{if(C.parentNode.name in this.submitted){this.element(C.parentNode)}}},highlight:function(E,D,C){if(E.type==="radio"){this.findByName(E.name).addClass(D).removeClass(C)}else{B(E).addClass(D).removeClass(C)}},unhighlight:function(E,D,C){if(E.type==="radio"){this.findByName(E.name).removeClass(D).addClass(C)}else{B(E).removeClass(D).addClass(C)}}},setDefaults:function(C){B.extend(B.validator.defaults,C)},messages:{required:"This field is required.",remote:"Please fix this field.",email:"Please enter a valid email address.",url:"Please enter a valid URL.",date:"Please enter a valid date.",dateISO:"Please enter a valid date (ISO).",number:"Please enter a valid number.",digits:"Please enter only digits.",creditcard:"Please enter a valid credit card number.",equalTo:"Please enter the same value again.",maxlength:B.validator.format("Please enter no more than {0} characters."),minlength:B.validator.format("Please enter at least {0} characters."),rangelength:B.validator.format("Please enter a value between {0} and {1} characters long."),range:B.validator.format("Please enter a value between {0} and {1}."),max:B.validator.format("Please enter a value less than or equal to {0}."),min:B.validator.format("Please enter a value greater than or equal to {0}.")},autoCreateRanges:false,prototype:{init:function(){this.labelContainer=B(this.settings.errorLabelContainer);this.errorContext=this.labelContainer.length&&this.labelContainer||B(this.currentForm);this.containers=B(this.settings.errorContainer).add(this.settings.errorLabelContainer);this.submitted={};this.valueCache={};this.pendingRequest=0;this.pending={};this.invalid={};this.reset();var C=(this.groups={}),D;B.each(this.settings.groups,function(F,G){if(typeof G==="string"){G=G.split(/\s/)}B.each(G,function(I,H){C[H]=F})});D=this.settings.rules;B.each(D,function(F,G){D[F]=B.validator.normalizeRule(G)});function E(H){var I=B.data(this[0].form,"validator"),F="on"+H.type.replace(/^validate/,""),G=I.settings;if(G[F]&&!this.is(G.ignore)){G[F].call(I,this[0],H)}}B(this.currentForm).validateDelegate(":text, [type='password'], [type='file'], select, textarea, [type='number'], [type='search'] ,[type='tel'], [type='url'], [type='email'], [type='datetime'], [type='date'], [type='month'], [type='week'], [type='time'], [type='datetime-local'], [type='range'], [type='color'] ","focusin focusout keyup",E).validateDelegate("[type='radio'], [type='checkbox'], select, option","click",E);if(this.settings.invalidHandler){B(this.currentForm).bind("invalid-form.validate",this.settings.invalidHandler)}B(this.currentForm).find("[required], [data-rule-required], .required").attr("aria-required","true")},form:function(){this.checkForm();B.extend(this.submitted,this.errorMap);this.invalid=B.extend({},this.errorMap);if(!this.valid()){B(this.currentForm).triggerHandler("invalid-form",[this])}this.showErrors();return this.valid()},checkForm:function(){this.prepareForm();for(var C=0,D=(this.currentElements=this.elements());D[C];C++){this.check(D[C])}return this.valid()},element:function(F){var D=this.clean(F),C=this.validationTargetFor(D),E=true;this.lastElement=C;if(C===undefined){delete this.invalid[D.name]}else{this.prepareElement(C);this.currentElements=B(C);E=this.check(C)!==false;if(E){delete this.invalid[C.name]}else{this.invalid[C.name]=true}}B(F).attr("aria-invalid",!E);if(!this.numberOfInvalids()){this.toHide=this.toHide.add(this.containers)}this.showErrors();return E},showErrors:function(C){if(C){B.extend(this.errorMap,C);this.errorList=[];for(var D in C){this.errorList.push({message:C[D],element:this.findByName(D)[0]})}this.successList=B.grep(this.successList,function(E){return !(E.name in C)})}if(this.settings.showErrors){this.settings.showErrors.call(this,this.errorMap,this.errorList)}else{this.defaultShowErrors()}},resetForm:function(){if(B.fn.resetForm){B(this.currentForm).resetForm()}this.submitted={};this.lastElement=null;this.prepareForm();this.hideErrors();this.elements().removeClass(this.settings.errorClass).removeData("previousValue").removeAttr("aria-invalid")},numberOfInvalids:function(){return this.objectLength(this.invalid)},objectLength:function(E){var C=0,D;for(D in E){C++}return C},hideErrors:function(){this.addWrapper(this.toHide).hide()},valid:function(){return this.size()===0},size:function(){return this.errorList.length},focusInvalid:function(){if(this.settings.focusInvalid){try{B(this.findLastActive()||this.errorList.length&&this.errorList[0].element||[]).filter(":visible").focus().trigger("focusin")}catch(C){}}},findLastActive:function(){var C=this.lastActive;return C&&B.grep(this.errorList,function(D){return D.element.name===C.name}).length===1&&C},elements:function(){var C=this,D={};return B(this.currentForm).find("input, select, textarea").not(":submit, :reset, :image, [disabled]").not(this.settings.ignore).filter(function(){if(!this.name&&C.settings.debug&&window.console){console.error("%o has no name assigned",this)}if(this.name in D||!C.objectLength(B(this).rules())){return false}D[this.name]=true;return true})},clean:function(C){return B(C)[0]},errors:function(){var C=this.settings.errorClass.split(" ").join(".");return B(this.settings.errorElement+"."+C,this.errorContext)},reset:function(){this.successList=[];this.errorList=[];this.errorMap={};this.toShow=B([]);this.toHide=B([]);this.currentElements=B([])},prepareForm:function(){this.reset();this.toHide=this.errors().add(this.containers)},prepareElement:function(C){this.reset();this.toHide=this.errorsFor(C)},elementValue:function(F){var D,C=B(F),E=C.attr("type");if(E==="radio"||E==="checkbox"){return B("input[name='"+C.attr("name")+"']:checked").val()}D=C.val();if(typeof D==="string"){return D.replace(/\r/g,"")}return D},check:function(D){D=this.validationTargetFor(this.clean(D));var F=B(D).rules(),I=B.map(F,function(M,L){return L}).length,J=false,G=this.elementValue(D),E,K,H;for(K in F){H={method:K,parameters:F[K]};try{E=B.validator.methods[K].call(this,G,D,H.parameters);if(E==="dependency-mismatch"&&I===1){J=true;continue}J=false;if(E==="pending"){this.toHide=this.toHide.not(this.errorsFor(D));return}if(!E){this.formatAndAdd(D,H);return false}}catch(C){if(this.settings.debug&&window.console){console.log("Exception occurred when checking element "+D.id+", check the '"+H.method+"' method.",C)}throw C}}if(J){return}if(this.objectLength(F)){this.successList.push(D)}return true},customDataMessage:function(D,C){return B(D).data("msg"+C[0].toUpperCase()+C.substring(1).toLowerCase())||B(D).data("msg")},customMessage:function(C,D){var E=this.settings.messages[C];return E&&(E.constructor===String?E:E[D])},findDefined:function(){for(var C=0;C<arguments.length;C++){if(arguments[C]!==undefined){return arguments[C]}}return undefined},defaultMessage:function(D,C){return this.findDefined(this.customMessage(D.name,C),this.customDataMessage(D,C),!this.settings.ignoreTitle&&D.title||undefined,B.validator.messages[C],"<strong>Warning: No message defined for "+D.name+"</strong>")},formatAndAdd:function(E,C){var F=this.defaultMessage(E,C.method),D=/\$?\{(\d+)\}/g;if(typeof F==="function"){F=F.call(this,C.parameters,E)}else{if(D.test(F)){F=B.validator.format(F.replace(D,"{$1}"),C.parameters)}}this.errorList.push({message:F,element:E,method:C.method});this.errorMap[E.name]=F;this.submitted[E.name]=F},addWrapper:function(C){if(this.settings.wrapper){C=C.add(C.parent(this.settings.wrapper))}return C},defaultShowErrors:function(){var C,D,E;for(C=0;this.errorList[C];C++){E=this.errorList[C];if(this.settings.highlight){this.settings.highlight.call(this,E.element,this.settings.errorClass,this.settings.validClass)}this.showLabel(E.element,E.message)}if(this.errorList.length){this.toShow=this.toShow.add(this.containers)}if(this.settings.success){for(C=0;this.successList[C];C++){this.showLabel(this.successList[C])}}if(this.settings.unhighlight){for(C=0,D=this.validElements();D[C];C++){this.settings.unhighlight.call(this,D[C],this.settings.errorClass,this.settings.validClass)}}this.toHide=this.toHide.not(this.toShow);this.hideErrors();this.addWrapper(this.toShow).show()},validElements:function(){return this.currentElements.not(this.invalidElements())},invalidElements:function(){return B(this.errorList).map(function(){return this.element})},showLabel:function(D,E){var C=this.errorsFor(D);if(C.length){C.removeClass(this.settings.validClass).addClass(this.settings.errorClass);C.html(E)}else{C=B("<"+this.settings.errorElement+">").attr("for",this.idOrName(D)).addClass(this.settings.errorClass).html(E||"");if(this.settings.wrapper){C=C.hide().show().wrap("<"+this.settings.wrapper+"/>").parent()}if(!this.labelContainer.append(C).length){if(this.settings.errorPlacement){this.settings.errorPlacement(C,B(D))}else{C.insertAfter(D)}}}if(!E&&this.settings.success){C.text("");if(typeof this.settings.success==="string"){C.addClass(this.settings.success)}else{this.settings.success(C,D)}}this.toShow=this.toShow.add(C)},errorsFor:function(D){var C=this.idOrName(D);return this.errors().filter(function(){return B(this).attr("for")===C})},idOrName:function(C){return this.groups[C.name]||(this.checkable(C)?C.name:C.id||C.name)},validationTargetFor:function(C){if(this.checkable(C)){C=this.findByName(C.name).not(this.settings.ignore)[0]}return C},checkable:function(C){return(/radio|checkbox/i).test(C.type)},findByName:function(C){return B(this.currentForm).find("[name='"+C+"']")},getLength:function(C,D){switch(D.nodeName.toLowerCase()){case"select":return B("option:selected",D).length;case"input":if(this.checkable(D)){return this.findByName(D.name).filter(":checked").length}}return C.length},depend:function(C,D){return this.dependTypes[typeof C]?this.dependTypes[typeof C](C,D):true},dependTypes:{"boolean":function(C){return C},"string":function(C,D){return !!B(C,D.form).length},"function":function(C,D){return C(D)}},optional:function(D){var C=this.elementValue(D);return !B.validator.methods.required.call(this,C,D)&&"dependency-mismatch"},startRequest:function(C){if(!this.pending[C.name]){this.pendingRequest++;this.pending[C.name]=true}},stopRequest:function(D,C){this.pendingRequest--;if(this.pendingRequest<0){this.pendingRequest=0}delete this.pending[D.name];if(C&&this.pendingRequest===0&&this.formSubmitted&&this.form()){B(this.currentForm).submit();this.formSubmitted=false}else{if(!C&&this.pendingRequest===0&&this.formSubmitted){B(this.currentForm).triggerHandler("invalid-form",[this]);this.formSubmitted=false}}},previousValue:function(C){return B.data(C,"previousValue")||B.data(C,"previousValue",{old:null,valid:true,message:this.defaultMessage(C,"remote")})}},classRuleSettings:{required:{required:true},email:{email:true},url:{url:true},date:{date:true},dateISO:{dateISO:true},number:{number:true},digits:{digits:true},creditcard:{creditcard:true}},addClassRules:function(C,D){if(C.constructor===String){this.classRuleSettings[C]=D}else{B.extend(this.classRuleSettings,C)}},classRules:function(D){var C={},E=B(D).attr("class");if(E){B.each(E.split(" "),function(){if(this in B.validator.classRuleSettings){B.extend(C,B.validator.classRuleSettings[this])}})}return C},attributeRules:function(H){var D={},C=B(H),E=H.getAttribute("type"),F,G;for(F in B.validator.methods){if(F==="required"){G=H.getAttribute(F);if(G===""){G=true}G=!!G}else{G=C.attr(F)}if(/min|max/.test(F)&&(E===null||/number|range|text/.test(E))){G=Number(G)}if(G||G===0){D[F]=G}else{if(E===F&&E!=="range"){D[F]=true}}}if(D.maxlength&&/-1|2147483647|524288/.test(D.maxlength)){delete D.maxlength}return D},dataRules:function(G){var E,F,D={},C=B(G);for(E in B.validator.methods){F=C.data("rule"+E[0].toUpperCase()+E.substring(1).toLowerCase());if(F!==undefined){D[E]=F}}return D},staticRules:function(E){var C={},D=B.data(E.form,"validator");if(D.settings.rules){C=B.validator.normalizeRule(D.settings.rules[E.name])||{}}return C},normalizeRules:function(C,D){B.each(C,function(E,G){if(G===false){delete C[E];return}if(G.param||G.depends){var F=true;switch(typeof G.depends){case"string":F=!!B(G.depends,D.form).length;break;case"function":F=G.depends.call(D,D);break}if(F){C[E]=G.param!==undefined?G.param:true}else{delete C[E]}}});B.each(C,function(E,F){C[E]=B.isFunction(F)?F(D):F});B.each(["minlength","maxlength"],function(){if(C[this]){C[this]=Number(C[this])}});B.each(["rangelength","range"],function(){var E;if(C[this]){if(B.isArray(C[this])){C[this]=[Number(C[this][0]),Number(C[this][1])]}else{if(typeof C[this]==="string"){E=C[this].split(/[\s,]+/);C[this]=[Number(E[0]),Number(E[1])]}}}});if(B.validator.autoCreateRanges){if(C.min&&C.max){C.range=[C.min,C.max];delete C.min;delete C.max}if(C.minlength&&C.maxlength){C.rangelength=[C.minlength,C.maxlength];delete C.minlength;delete C.maxlength}}return C},normalizeRule:function(D){if(typeof D==="string"){var C={};B.each(D.split(/\s/),function(){C[this]=true});D=C}return D},addMethod:function(C,D,E){B.validator.methods[C]=D;B.validator.messages[C]=E!==undefined?E:B.validator.messages[C];if(D.length<3){B.validator.addClassRules(C,B.validator.normalizeRule(C))}},methods:{required:function(E,F,D){if(!this.depend(D,F)){return"dependency-mismatch"}if(F.nodeName.toLowerCase()==="select"){var C=B(F).val();return C&&C.length>0}if(this.checkable(F)){return this.getLength(E,F)>0}return B.trim(E).length>0},email:function(C,D){return this.optional(D)||/^[a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(C)},url:function(C,D){return this.optional(D)||/^(https?|s?ftp):\/\/(((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:)*@)?(((\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5]))|((([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.)+(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.?)(:\d*)?)(\/((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)+(\/(([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)*)*)?)?(\?((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)|[\uE000-\uF8FF]|\/|\?)*)?(#((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)|\/|\?)*)?$/i.test(C)},date:function(C,D){return this.optional(D)||!/Invalid|NaN/.test(new Date(C).toString())},dateISO:function(C,D){return this.optional(D)||/^\d{4}[\/\-]\d{1,2}[\/\-]\d{1,2}$/.test(C)},number:function(C,D){return this.optional(D)||/^-?(?:\d+|\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/.test(C)},digits:function(C,D){return this.optional(D)||/^\d+$/.test(C)},creditcard:function(G,H){if(this.optional(H)){return"dependency-mismatch"}if(/[^0-9 \-]+/.test(G)){return false}var F=0,C=0,I=false,D,E;G=G.replace(/\D/g,"");if(G.length<13||G.length>19){return false}for(D=G.length-1;D>=0;D--){E=G.charAt(D);C=parseInt(E,10);if(I){if((C*=2)>9){C-=9}}F+=C;I=!I}return(F%10)===0},minlength:function(E,F,D){var C=B.isArray(E)?E.length:this.getLength(B.trim(E),F);return this.optional(F)||C>=D},maxlength:function(E,F,D){var C=B.isArray(E)?E.length:this.getLength(B.trim(E),F);return this.optional(F)||C<=D},rangelength:function(E,F,D){var C=B.isArray(E)?E.length:this.getLength(B.trim(E),F);return this.optional(F)||(C>=D[0]&&C<=D[1])},min:function(D,E,C){return this.optional(E)||D>=C},max:function(D,E,C){return this.optional(E)||D<=C},range:function(D,E,C){return this.optional(E)||(D>=C[0]&&D<=C[1])},equalTo:function(E,F,C){var D=B(C);if(this.settings.onfocusout){D.unbind(".validate-equalTo").bind("blur.validate-equalTo",function(){B(F).valid()})}return E===D.val()},remote:function(E,F,C){if(this.optional(F)){return"dependency-mismatch"}var D=this.previousValue(F),G,H;if(!this.settings.messages[F.name]){this.settings.messages[F.name]={}}D.originalMessage=this.settings.messages[F.name].remote;this.settings.messages[F.name].remote=D.message;C=typeof C==="string"&&{url:C}||C;if(D.old===E){return D.valid}D.old=E;G=this;this.startRequest(F);H={};H[F.name]=E;B.ajax(B.extend(true,{url:C,mode:"abort",port:"validate"+F.name,dataType:"json",data:H,context:G.currentForm,success:function(M){var K=M===true||M==="true",J,I,L;G.settings.messages[F.name].remote=D.originalMessage;if(K){L=G.formSubmitted;G.prepareElement(F);G.formSubmitted=L;G.successList.push(F);delete G.invalid[F.name];G.showErrors()}else{J={};I=M||G.defaultMessage(F,"remote");J[F.name]=D.message=B.isFunction(I)?I(E):I;G.invalid[F.name]=true;G.showErrors(J)}D.valid=K;G.stopRequest(F,K)}},C));return"pending"}}});B.format=function A(){throw"$.format has been deprecated. Please use $.validator.format instead."}}(jQuery));(function(C){var A={},B;if(C.ajaxPrefilter){C.ajaxPrefilter(function(F,G,D){var E=F.port;if(F.mode==="abort"){if(A[E]){A[E].abort()}A[E]=D}})}else{B=C.ajax;C.ajax=function(F){var E=("mode" in F?F:C.ajaxSettings).mode,D=("port" in F?F:C.ajaxSettings).port;if(E==="abort"){if(A[D]){A[D].abort()}A[D]=B.apply(this,arguments);return A[D]}return B.apply(this,arguments)}}}(jQuery));(function(A){A.extend(A.fn,{validateDelegate:function(D,B,C){return this.bind(B,function(E){var F=A(E.target);if(F.is(D)){return C.apply(F,arguments)}})}})}(jQuery));