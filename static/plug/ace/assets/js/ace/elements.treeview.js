(function(C,B){var A={"open-icon":ace.vars["icon"]+"fa fa-folder-open","close-icon":ace.vars["icon"]+"fa fa-folder","selectable":true,"selected-icon":ace.vars["icon"]+"fa fa-check","unselected-icon":ace.vars["icon"]+"fa fa-times"};C.fn.ace_tree=function(D){A=C.extend({},A,D);this.each(function(){var E=C(this);E.html('<div class="tree-folder" style="display:none;">				<div class="tree-folder-header">					<i class="'+ace.vars["icon"]+A["close-icon"]+'"></i>					<div class="tree-folder-name"></div>				</div>				<div class="tree-folder-content"></div>				<div class="tree-loader" style="display:none"></div>			</div>			<div class="tree-item" style="display:none;">				'+(A["unselected-icon"]==null?"":'<i class="'+ace.vars["icon"]+A["unselected-icon"]+'"></i>')+'				<div class="tree-item-name"></div>			</div>');E.addClass(A["selectable"]==true?"tree-selectable":"tree-unselectable");E.tree(A)});return this}})(window.jQuery);