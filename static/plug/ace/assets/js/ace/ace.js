if(!("ace" in window)){window["ace"]={}}if(!("helper" in window["ace"])){window["ace"].helper={}}if(!("options" in window["ace"])){window["ace"].options={}}if(!("vars" in window["ace"])){window["ace"].vars={"icon":" ace-icon ",".icon":".ace-icon"}}ace.vars["touch"]="ontouchstart" in document.documentElement;jQuery(function(F){ace.click_event=ace.vars["touch"]&&F.fn.tap?"tap":"click";var C=navigator.userAgent;ace.vars["webkit"]=!!C.match(/AppleWebKit/i);ace.vars["safari"]=!!C.match(/Safari/i)&&!C.match(/Chrome/i);ace.vars["android"]=ace.vars["safari"]&&!!C.match(/Android/i);ace.vars["ios_safari"]=!!C.match(/OS ([4-9])(_\d)+ like Mac OS X/i)&&!C.match(/CriOS/i);ace.vars["old_ie"]=document.all&&!document.addEventListener;ace.vars["non_auto_fixed"]=ace.vars["android"]||ace.vars["ios_safari"];if(ace.vars["non_auto_fixed"]){F("body").addClass("mob-safari")}var A=document.documentElement.style;ace.vars["transition"]="transition" in A||"WebkitTransition" in A||"MozTransition" in A||"OTransition" in A;var B={"general_vars":null,"add_touch_drag":null,"general_things":null,"handle_side_menu":null,"sidebar_scrollable":{"scroll_to_active":true,"include_shortcuts":true,"include_toggle":false||ace.vars["safari"]||ace.vars["ios_safari"],"smooth_scroll":200,"outside":false},"sidebar_hoverable":{"sub_scroll":false},"widget_boxes":null,"widget_reload_handler":null,"settings_box":null,"settings_rtl":null,"settings_skin":null,"enable_searchbox_autocomplete":null,"auto_hide_sidebar":false,"auto_padding":false,"auto_container":false};for(var E in B){if(!(E in ace)){continue}var D=B[E];if(D===false){continue}else{if(D===null){D=[jQuery]}else{if(D instanceof Array){D.unshift(jQuery)}else{D=[jQuery,D]}}}ace[E].apply(null,D)}});ace.general_vars=function(E){var A="menu-min";var C="responsive-min";var D="h-sidebar";var B=E("#sidebar").eq(0);ace.vars["mobile_style"]=1;if(B.hasClass("responsive")&&!E("#menu-toggler").hasClass("navbar-toggle")){ace.vars["mobile_style"]=2}else{if(B.hasClass(C)){ace.vars["mobile_style"]=3}else{if(B.hasClass("navbar-collapse")){ace.vars["mobile_style"]=4}}}E(window).on("resize.ace.vars",function(){ace.vars["window"]={width:parseInt(E(this).width()),height:parseInt(E(this).height())};ace.vars["mobile_view"]=ace.vars["mobile_style"]<4&&ace.helper.mobile_view();ace.vars["collapsible"]=!ace.vars["mobile_view"]&&ace.helper.collapsible();ace.vars["nav_collapse"]=(ace.vars["collapsible"]||ace.vars["mobile_view"])&&E("#navbar").hasClass("navbar-collapse");var F=E(document.getElementById("sidebar"));ace.vars["minimized"]=(!ace.vars["collapsible"]&&F.hasClass(A))||(ace.vars["mobile_style"]==3&&ace.vars["mobile_view"]&&F.hasClass(C));ace.vars["horizontal"]=!(ace.vars["mobile_view"]||ace.vars["collapsible"])&&F.hasClass(D)}).triggerHandler("resize.ace.vars")};ace.general_things=function(E){var A=!!E.fn.ace_scroll;if(A){E(".dropdown-content").ace_scroll({reset:false,mouseWheelLock:true})}E(window).on("resize.reset_scroll",function(){if(!A){return}E(".ace-scroll").ace_scroll("reset")});if(A){E(document).on("settings.ace.reset_scroll",function(F,G){if(G=="sidebar_collapsed"){E(".ace-scroll").ace_scroll("reset")}})}E(document).on("click.dropdown.pos",'.dropdown-toggle[data-position="auto"]',function(){var F=E(this).offset();var G=E(this.parentNode);if(parseInt(F.top+E(this).height())+50>(ace.helper.scrollTop()+ace.helper.winHeight()-G.find(".dropdown-menu").eq(0).height())){G.addClass("dropup")}else{G.removeClass("dropup")}});E(document).on("click",".dropdown-navbar .nav-tabs",function(G){G.stopPropagation();var I,F;var H=G.target;if((I=E(G.target).closest("[data-toggle=tab]"))&&I.length>0){I.tab("show");G.preventDefault()}});E('.ace-nav [class*="icon-animated-"]').closest("a").one("click",function(){var G=E(this).find('[class*="icon-animated-"]').eq(0);var F=G.attr("class").match(/icon\-animated\-([\d\w]+)/);G.removeClass(F[0])});E(".sidebar .nav-list .badge[title],.sidebar .nav-list .badge[title]").each(function(){var F=E(this).attr("class").match(/tooltip\-(?:\w+)/);F=F?F[0]:"tooltip-error";E(this).tooltip({"placement":function(G,H){var I=E(H).offset();if(parseInt(I.left)<parseInt(document.body.scrollWidth/2)){return"right"}return"left"},container:"body",template:'<div class="tooltip '+F+'"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>'})});var C=E(".btn-scroll-up");if(C.length>0){var B=false;E(window).on("scroll.scroll_btn",function(){var H=ace.helper.scrollTop();var F=ace.helper.winHeight();var G=document.body.scrollHeight;if(H>parseInt(F/4)||(H>0&&G>=F&&F+H>=G-1)){if(!B){C.addClass("display");B=true}}else{if(B){C.removeClass("display");B=false}}}).triggerHandler("scroll.scroll_btn");C.on(ace.click_event,function(){var F=Math.min(500,Math.max(100,parseInt(ace.helper.scrollTop()/3)));E("html,body").animate({scrollTop:0},F);return false})}if(ace.vars["webkit"]){var D=E(".ace-nav").get(0);if(D){E(window).on("resize.webkit",function(){ace.helper.redraw(D)})}}if(ace.vars["ios_safari"]){E(document).on("ace.settings.ios_fix",function(G,H,F){if(H!="navbar_fixed"){return}E(document).off("focus.ios_fix blur.ios_fix","input,textarea,.wysiwyg-editor");if(F==true){E(document).on("focus.ios_fix","input,textarea,.wysiwyg-editor",function(){E(window).on("scroll.ios_fix",function(){var I=E("#navbar").get(0);if(I){ace.helper.redraw(I)}})}).on("blur.ios_fix","input,textarea,.wysiwyg-editor",function(){E(window).off("scroll.ios_fix")})}}).triggerHandler("ace.settings.ios_fix",["navbar_fixed",E("#navbar").css("position")=="fixed"])}};ace.helper.collapsible=function(){var A;return(document.querySelector("#sidebar.navbar-collapse")!=null)&&((A=document.querySelector('.navbar-toggle[data-target*=".sidebar"]'))!=null)&&A.scrollHeight>0};ace.helper.mobile_view=function(){var A;return((A=document.getElementById("menu-toggler"))!=null&&A.scrollHeight>0)};ace.helper.redraw=function(A){var B=A.style["display"];A.style.display="none";A.offsetHeight;A.style.display=B};ace.helper.scrollTop=function(){return document.scrollTop||document.documentElement.scrollTop||document.body.scrollTop};ace.helper.winHeight=function(){return window.innerHeight||document.documentElement.clientHeight};ace.helper.camelCase=function(A){return A.replace(/-([\da-z])/gi,function(B,C){return C?C.toUpperCase():""})};ace.helper.removeStyle="removeProperty" in document.documentElement.style?function(A,B){A.style.removeProperty(B)}:function(A,B){A.style[ace.helper.camelCase(B)]=""};ace.helper.hasClass="classList" in document.documentElement?function(B,A){return B.classList.contains(A)}:function(B,A){return B.className.indexOf(A)>-1};