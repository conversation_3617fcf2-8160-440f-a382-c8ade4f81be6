ace.settings_box=function(A){A("#ace-settings-btn").on(ace.click_event,function(B){B.preventDefault();A(this).toggleClass("open");A("#ace-settings-box").toggleClass("open")});A("#ace-settings-navbar").on("click",function(){ace.settings.navbar_fixed(this.checked)}).each(function(){this.checked=ace.settings.is("navbar","fixed")});A("#ace-settings-sidebar").on("click",function(){ace.settings.sidebar_fixed(this.checked)}).each(function(){this.checked=ace.settings.is("sidebar","fixed")});A("#ace-settings-breadcrumbs").on("click",function(){ace.settings.breadcrumbs_fixed(this.checked)}).each(function(){this.checked=ace.settings.is("breadcrumbs","fixed")});A("#ace-settings-add-container").on("click",function(){ace.settings.main_container_fixed(this.checked)}).each(function(){this.checked=ace.settings.is("main-container","fixed")});A("#ace-settings-compact").removeAttr("checked").on("click",function(){if(this.checked){A("#sidebar").addClass("compact");var B=A("#ace-settings-hover");if(B.length>0&&!B.get(0).checked){B.removeAttr("checked").trigger("click")}}else{A("#sidebar").removeClass("compact");if("sidebar_scroll" in ace.helper){ace.helper.sidebar_scroll.reset()}}});A("#ace-settings-highlight").removeAttr("checked").on("click",function(){if(this.checked){A("#sidebar .nav-list > li").addClass("highlight")}else{A("#sidebar .nav-list > li").removeClass("highlight")}});A("#ace-settings-hover").removeAttr("checked").on("click",function(){if(A(".sidebar").hasClass("h-sidebar")){return}if(this.checked){A("#sidebar li").addClass("hover").filter(".open").removeClass("open").find("> .submenu").css("display","none")}else{A("#sidebar li.hover").removeClass("hover");var B=A("#ace-settings-compact");if(B.length>0&&B.get(0).checked){B.trigger("click")}if("sidebar_hover" in ace.helper){ace.helper.sidebar_hover.reset()}}if("sidebar_scroll" in ace.helper){ace.helper.sidebar_scroll.reset()}})};