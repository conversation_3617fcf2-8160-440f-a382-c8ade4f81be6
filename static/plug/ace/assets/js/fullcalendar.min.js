/*
 * FullCalendar v2.0.2
 * Docs & License: http://arshaw.com/fullcalendar/
 * (c) 2013 <PERSON>
 */
(function(A){"function"==typeof define&&define.amd?define(["jquery","moment"],A):A(jQuery,moment)})(function(Bj,Bz){function Bp(B,A){return A.longDateFormat("LT").replace(":mm","(:mm)").replace(/(\Wmm)$/,"($1)").replace(/\s*a$/i,"t")}function Bd(C,A){var B=A.longDateFormat("L");return B=B.replace(/^Y+[^\w\s]*|[^\w\s]*Y+$/g,""),C.isRTL?B+=" ddd":B="ddd "+B,B}function Bv(A){Bq(Am,A)}function Bq(B){function C(E,D){Bj.isPlainObject(D)&&Bj.isPlainObject(B[E])&&!Bo(E)?B[E]=Bq({},B[E],D):void 0!==D&&(B[E]=D)}for(var A=1;arguments.length>A;A++){Bj.each(arguments[A],C)}return B}function Bo(A){return/(Time|Duration)$/.test(A)}function Be(Cr,Ci){function Cu(A){T?Cv()&&(Ct(),Cs(A)):Cq()}function Cq(){BH=u.theme?"ui":"fc",Cr.addClass("fc"),u.isRTL?Cr.addClass("fc-rtl"):Cr.addClass("fc-ltr"),u.theme&&Cr.addClass("ui-widget"),T=Bj("<div class='fc-content' />").prependTo(Cr),Ae=new Br(k,u),t=Ae.render(),t&&Cr.prepend(t),Cp(u.defaultView),u.handleWindowResize&&Bj(window).resize(Cn),Cm()||Cj()}function Cj(){setTimeout(function(){!BK.start&&Cm()&&Cw()},0)}function Cx(){BK&&(BX("viewDestroy",BK,BK,BK.element),BK.triggerEventDestroy()),Bj(window).unbind("resize",Cn),u.droppable&&Bj(document).off("dragstart",B1).off("dragstop",B2),BK.selectionManagerDestroy&&BK.selectionManagerDestroy(),Ae.destroy(),T.remove(),Cr.removeClass("fc fc-ltr fc-rtl ui-widget")}function Cv(){return Cr.is(":visible")}function Cm(){return Bj("body").is(":visible")}function Cp(A){BK&&A==BK.name||Ck(A)}function Ck(A){B++,BK&&(BX("viewDestroy",BK,BK,BK.element),B5(),BK.triggerEventDestroy(),B4(),BK.element.remove(),Ae.deactivateButton(BK.name)),Ae.activateButton(A),BK=new BC[A](Bj("<div class='fc-view fc-view-"+A+"' />").appendTo(T),k),Cw(),BY(),B--}function Cw(A){BK.start&&!A&&BM.isWithin(BK.intervalStart,BK.intervalEnd)||Cv()&&Cs(A)}function Cs(A){B++,BK.start&&(BX("viewDestroy",BK,BK,BK.element),B5(),Cg()),B4(),A&&(BM=BK.incrementDate(BM,A)),BK.render(BM.clone()),Cd(),BY(),(BK.afterRender||Bm)(),B3(),Cb(),BX("viewRender",BK,BK,BK.element),B--,B8()}function Ch(){Cv()&&(B5(),Cg(),Ct(),Cd(),BV())}function Ct(){l=u.contentHeight?u.contentHeight:u.height?u.height-(t?t.height():0)-AT(T):Math.round(T.width()/Math.max(u.aspectRatio,0.5))}function Cd(){void 0===l&&Ct(),B++,BK.setHeight(l),BK.setWidth(T.width()),B--,BO=Cr.outerWidth()}function Cn(C){if(!B&&C.target===window){if(BK.start){var A=++BI;setTimeout(function(){A==BI&&!B&&Cv()&&BO!=(BO=Cr.outerWidth())&&(B++,Ch(),BK.trigger("windowResize",BN),B--)},u.windowResizeDelay)}else{Cj()}}}function B9(){Cg(),Cf()}function Ce(A){Cg(),BV(A)}function BV(A){Cv()&&(BK.renderEvents(BJ,A),BK.trigger("eventAfterAllRender"))}function Cg(){BK.triggerEventDestroy(),BK.clearEvents(),BK.clearEventData()}function B8(){!u.lazyFetching||o(BK.start,BK.end)?Cf():BV()}function Cf(){c(BK.start,BK.end)}function BU(A){BJ=A,BV()}function BT(A){Ce(A)}function B3(){Ae.updateTitle(BK.title)}function Cb(){var A=k.getNow();A.isWithin(BK.intervalStart,BK.intervalEnd)?Ae.disableButton("today"):Ae.enableButton("today")}function Ca(C,A){BK.select(C,A)}function B5(){BK&&BK.unselect()}function BS(){Cw(-1)}function B6(){Cw(1)}function BZ(){BM.add("years",-1),Cw()}function B7(){BM.add("years",1),Cw()}function BQ(){BM=k.getNow(),Cw()}function BW(A){BM=k.moment(A),Cw()}function Co(A){BM.add(Bz.duration(A)),Cw()}function Cl(){return BM.clone()}function B4(){T.css({width:"100%",height:T.height(),overflow:"hidden"})}function BY(){T.css({width:"",height:"",overflow:""})}function BR(){return k}function B0(){return BK}function Cc(C,A){return void 0===A?u[C]:(("height"==C||"contentHeight"==C||"aspectRatio"==C)&&(u[C]=A,Ch()),void 0)}function BX(C,A){return u[C]?u[C].apply(A||BN,Array.prototype.slice.call(arguments,2)):void 0}function B1(C,D){var A=C.target,F=Bj(A);if(!F.parents(".fc").length){var E=u.dropAccept;(Bj.isFunction(E)?E.call(A,F):F.is(E))&&(e=A,BK.dragStart(e,C,D))}}function B2(C,A){e&&(BK.dragStop(e,C,A),e=null)}var k=this;Ci=Ci||{};var BP,u=Bq({},Am,Ci);BP=u.lang in Af?Af[u.lang]:Af[Am.lang],BP&&(u=Bq({},Am,BP,Ci)),u.isRTL&&(u=Bq({},Am,Al,BP||{},Ci)),k.options=u,k.render=Cu,k.destroy=Cx,k.refetchEvents=B9,k.reportEvents=BU,k.reportEventChange=BT,k.rerenderEvents=Ce,k.changeView=Cp,k.select=Ca,k.unselect=B5,k.prev=BS,k.next=B6,k.prevYear=BZ,k.nextYear=B7,k.today=BQ,k.gotoDate=BW,k.incrementDate=Co,k.getDate=Cl,k.getCalendar=BR,k.getView=B0,k.option=Cc,k.trigger=BX;var I=Bk(Bz.langData(u.lang));if(u.monthNames&&(I._months=u.monthNames),u.monthNamesShort&&(I._monthsShort=u.monthNamesShort),u.dayNames&&(I._weekdays=u.dayNames),u.dayNamesShort&&(I._weekdaysShort=u.dayNamesShort),null!=u.firstDay){var BL=Bk(I._week);BL.dow=u.firstDay,I._week=BL}k.defaultAllDayEventDuration=Bz.duration(u.defaultAllDayEventDuration),k.defaultTimedEventDuration=Bz.duration(u.defaultTimedEventDuration),k.moment=function(){var A;return"local"===u.timezone?(A=BD.moment.apply(null,arguments),A.hasTime()&&A.local()):A="UTC"===u.timezone?BD.moment.utc.apply(null,arguments):BD.moment.parseZone.apply(null,arguments),A._lang=I,A},k.getIsAmbigTimezone=function(){return"local"!==u.timezone&&"UTC"!==u.timezone},k.rezoneDate=function(A){return k.moment(A.toArray())},k.getNow=function(){var A=u.now;return"function"==typeof A&&(A=A()),k.moment(A)},k.calculateWeekNumber=function(C){var A=u.weekNumberCalculation;return"function"==typeof A?A(C):"local"===A?C.week():"ISO"===A.toUpperCase()?C.isoWeek():void 0},k.getEventEnd=function(A){return A.end?A.end.clone():k.getDefaultEventEnd(A.allDay,A.start)},k.getDefaultEventEnd=function(D,A){var C=A.clone();return D?C.stripTime().add(k.defaultAllDayEventDuration):C.add(k.defaultTimedEventDuration),k.getIsAmbigTimezone()&&C.stripZone(),C},k.formatRange=function(D,A,C){return"function"==typeof C&&(C=C.call(k,u,I)),AY(D,A,C,null,u.isRTL)},k.formatDate=function(C,A){return"function"==typeof A&&(A=A.call(k,u,I)),A3(C,A)},Bu.call(k,u);var Ae,t,T,BH,BK,BO,l,BM,e,o=k.isFetchNeeded,c=k.fetchEvents,BN=Cr[0],BI=0,B=0,BJ=[];BM=null!=u.defaultDate?k.moment(u.defaultDate):k.getNow(),u.droppable&&Bj(document).on("dragstart",B1).on("dragstop",B2)}function Br(A,G){function C(){L=G.theme?"ui":"fc";var N=G.header;return N?D=Bj("<table class='fc-header' style='width:100%'/>").append(Bj("<tr/>").append(H("left")).append(H("center")).append(H("right"))):void 0}function K(){D.remove()}function H(N){var P=Bj("<td class='fc-header-"+N+"'/>"),O=G.header[N];return O&&Bj.each(O.split(" "),function(Q){Q>0&&P.append("<span class='fc-header-space'/>");var R;Bj.each(this.split(","),function(S,X){if("title"==X){P.append("<span class='fc-header-title'><h2>&nbsp;</h2></span>"),R&&R.addClass(L+"-corner-right"),R=null}else{var T;if(A[X]?T=A[X]:BC[X]&&(T=function(){W.removeClass(L+"-state-hover"),A.changeView(X)}),T){var Y,Z=Ba(G.themeButtonIcons,X),a=Ba(G.buttonIcons,X),V=Ba(G.defaultButtonText,X),U=Ba(G.buttonText,X);Y=U?AN(U):Z&&G.theme?"<span class='ui-icon ui-icon-"+Z+"'></span>":a&&!G.theme?"<span class='fc-icon fc-icon-"+a+"'></span>":AN(V||X);var W=Bj("<span class='fc-button fc-button-"+X+" "+L+"-state-default'>"+Y+"</span>").click(function(){W.hasClass(L+"-state-disabled")||T()}).mousedown(function(){W.not("."+L+"-state-active").not("."+L+"-state-disabled").addClass(L+"-state-down")}).mouseup(function(){W.removeClass(L+"-state-down")}).hover(function(){W.not("."+L+"-state-active").not("."+L+"-state-disabled").addClass(L+"-state-hover")},function(){W.removeClass(L+"-state-hover").removeClass(L+"-state-down")}).appendTo(P);AX(W),R||W.addClass(L+"-corner-left"),R=W}}}),R&&R.addClass(L+"-corner-right")}),P}function F(N){D.find("h2").html(N)}function B(N){D.find("span.fc-button-"+N).addClass(L+"-state-active")}function I(N){D.find("span.fc-button-"+N).removeClass(L+"-state-active")}function J(N){D.find("span.fc-button-"+N).addClass(L+"-state-disabled")}function M(N){D.find("span.fc-button-"+N).removeClass(L+"-state-disabled")}var E=this;E.render=C,E.destroy=K,E.updateTitle=F,E.activateButton=B,E.deactivateButton=I,E.disableButton=J,E.enableButton=M;var L,D=Bj([])}function Bu(B){function BM(C,A){return !Z||C.clone().stripZone()<Z.clone().stripZone()||A.clone().stripZone()>J.clone().stripZone()}function t(F,C){Z=F,J=C,P=[];var D=++N,A=G.length;X=A;for(var E=0;A>E;E++){BS(G[E],D)}}function BS(A,C){BN(A,function(D){var H,F,E=Bj.isArray(A.events);if(C==N){if(D){for(H=0;D.length>H;H++){F=D[H],E||(F=Y(F,A)),F&&P.push(F)}}X--,X||Q(P)}})}function BN(O,A){var W,M,C=BD.sourceFetchers;for(W=0;C.length>W;W++){if(M=C[W].call(U,O,Z.clone(),J.clone(),B.timezone,A),M===!0){return}if("object"==typeof M){return BN(M,A),void 0}}var R=O.events;if(R){Bj.isFunction(R)?(q(),R.call(U,Z.clone(),J.clone(),B.timezone,function(a){A(a),BQ()})):Bj.isArray(R)?A(R):A()}else{var T=O.url;if(T){var k,F=O.success,b=O.error,E=O.complete;k=Bj.isFunction(O.data)?O.data():O.data;var H=Bj.extend({},k||{}),D=AL(O.startParam,B.startParam),e=AL(O.endParam,B.endParam),S=AL(O.timezoneParam,B.timezoneParam);D&&(H[D]=Z.format()),e&&(H[e]=J.format()),B.timezone&&"local"!=B.timezone&&(H[S]=B.timezone),q(),Bj.ajax(Bj.extend({},Aa,O,{data:H,success:function(a){a=a||[];var c=AZ(F,this,arguments);Bj.isArray(c)&&(a=c),A(a)},error:function(){AZ(b,this,arguments),A()},complete:function(){AZ(E,this,arguments),BQ()}}))}else{A()}}}function BL(C){var A=v(C);A&&(G.push(A),X++,BS(A,N))}function v(C){var D,A,E=BD.sourceNormalizers;if(Bj.isFunction(C)||Bj.isArray(C)?D={events:C}:"string"==typeof C?D={url:C}:"object"==typeof C&&(D=Bj.extend({},C),"string"==typeof D.className&&(D.className=D.className.split(/\s+/))),D){for(Bj.isArray(D.events)&&(D.events=Bj.map(D.events,function(F){return Y(F,D)})),A=0;E.length>A;A++){E[A].call(U,D)}return D}}function BO(A){G=Bj.grep(G,function(C){return !BR(C,A)}),P=Bj.grep(P,function(C){return !BR(C.source,A)}),Q(P)}function BR(C,A){return C&&A&&BI(C)==BI(A)}function BI(A){return("object"==typeof A?A.events||A.url:"")||A}function BT(A){A.start=U.moment(A.start),A.end&&(A.end=U.moment(A.end)),BH(A),BK(A),Q(P)}function BK(E){var C,D,A,F;for(C=0;P.length>C;C++){if(D=P[C],D._id==E._id&&D!==E){for(A=0;K.length>A;A++){F=K[A],void 0!==E[F]&&(D[F]=E[F])}}}}function Ae(D,A){var C=Y(D);C&&(C.source||(A&&(I.events.push(C),C.source=I),P.push(C)),Q(P))}function BU(C){var D,A;for(null==C?C=function(){return !0}:Bj.isFunction(C)||(D=C+"",C=function(E){return E._id==D}),P=Bj.grep(P,C,!0),A=0;G.length>A;A++){Bj.isArray(G[A].events)&&(G[A].events=Bj.grep(G[A].events,C,!0))}Q(P)}function BP(A){return Bj.isFunction(A)?Bj.grep(P,A):null!=A?(A+="",Bj.grep(P,function(C){return C._id==A})):P}function q(){V++||BJ("loading",null,!0,j())}function BQ(){--V||BJ("loading",null,!1,j())}function Y(C,A){var M,D,E,F,H={};return B.eventDataTransform&&(C=B.eventDataTransform(C)),A&&A.eventDataTransform&&(C=A.eventDataTransform(C)),M=U.moment(C.start||C.date),M.isValid()&&(D=null,!C.end||(D=U.moment(C.end),D.isValid()))?(E=C.allDay,void 0===E&&(F=AL(A?A.allDayDefault:void 0,B.allDayDefault),E=void 0!==F?F:!(M.hasTime()||D&&D.hasTime())),E?(M.hasTime()&&M.stripTime(),D&&D.hasTime()&&D.stripTime()):(M.hasTime()||(M=U.rezoneDate(M)),D&&!D.hasTime()&&(D=U.rezoneDate(D))),Bj.extend(H,C),A&&(H.source=A),H._id=C._id||(void 0===C.id?"_fc"+Ai++:C.id+""),H.className=C.className?"string"==typeof C.className?C.className.split(/\s+/):C.className:[],H.allDay=E,H.start=M,H.end=D,B.forceEventDuration&&!H.end&&(H.end=d(H)),By(H),H):void 0}function BH(E,A,H){var D,S,M,F,C=E._allDay,O=E._start,R=E._end,T=!1;return A||H||(A=E.start,H=E.end),D=E.allDay!=C?E.allDay:!(A||H).hasTime(),D&&(A&&(A=A.clone().stripTime()),H&&(H=H.clone().stripTime())),A&&(S=D?Bh(A,O.clone().stripTime()):Bh(A,O)),D!=C?T=!0:H&&(M=Bh(H||U.getDefaultEventEnd(D,A||O),A||O).subtract(Bh(R||U.getDefaultEventEnd(C,O),O))),F=L(BP(E._id),T,D,S,M),{dateDelta:S,durationDelta:M,undo:F}}function L(C,A,M,D,E){var F=U.getIsAmbigTimezone(),H=[];return Bj.each(C,function(S,a){var b=a._allDay,T=a._start,e=a._end,R=null!=M?M:b,W=T.clone(),O=!A&&e?e.clone():null;R?(W.stripTime(),O&&O.stripTime()):(W.hasTime()||(W=U.rezoneDate(W)),O&&!O.hasTime()&&(O=U.rezoneDate(O))),O||!B.forceEventDuration&&!+E||(O=U.getDefaultEventEnd(R,W)),W.add(D),O&&O.add(D).add(E),F&&(+D||+E)&&(W.stripZone(),O&&O.stripZone()),a.allDay=R,a.start=W,a.end=O,By(a),H.push(function(){a.allDay=b,a.start=T,a.end=e,By(a)})}),function(){for(var O=0;H.length>O;O++){H[O]()}}}var U=this;U.isFetchNeeded=BM,U.fetchEvents=t,U.addEventSource=BL,U.removeEventSource=BO,U.updateEvent=BT,U.renderEvent=Ae,U.removeEvents=BU,U.clientEvents=BP,U.mutateEvent=BH;var Z,J,BJ=U.trigger,j=U.getView,Q=U.reportEvents,d=U.getEventEnd,I={events:[]},G=[I],N=0,X=0,V=0,P=[];Bj.each((B.events?[B.events]:[]).concat(B.eventSources||[]),function(D,A){var C=v(A);C&&G.push(C)});var K=["title","url","allDay","className","editable","color","backgroundColor","borderColor","textColor"]}function By(A){A._allDay=A.allDay,A._start=A.start.clone(),A._end=A.end?A.end.clone():null}function Bk(B){var A=function(){};return A.prototype=B,new A}function Bw(C,A){for(var B in A){A.hasOwnProperty(B)&&(C[B]=A[B])}}function Bh(B,A){return Bz.duration({days:B.clone().stripTime().diff(A.clone().stripTime(),"days"),ms:B.time()-A.time()})}function Bn(A){return"[object Date]"===Object.prototype.toString.call(A)||A instanceof Date}function Bf(B,C,A){B.unbind("mouseover").mouseover(function(E){for(var H,F,D,G=E.target;G!=this;){H=G,G=G.parentNode}void 0!==(F=H._fci)&&(H._fci=void 0,D=C[F],A(D.event,D.element,D),Bj(E.target).trigger(E)),E.stopPropagation()})}function Bx(B,C,A){for(var E,D=0;B.length>D;D++){E=Bj(B[D]),E.width(Math.max(0,C-Bc(E,A)))}}function Bs(B,C,A){for(var E,D=0;B.length>D;D++){E=Bj(B[D]),E.height(Math.max(0,C-AT(E,A)))}}function Bc(B,A){return Bt(B)+Bi(B)+(A?A8(B):0)}function Bt(A){return(parseFloat(Bj.css(A[0],"paddingLeft",!0))||0)+(parseFloat(Bj.css(A[0],"paddingRight",!0))||0)}function A8(A){return(parseFloat(Bj.css(A[0],"marginLeft",!0))||0)+(parseFloat(Bj.css(A[0],"marginRight",!0))||0)}function Bi(A){return(parseFloat(Bj.css(A[0],"borderLeftWidth",!0))||0)+(parseFloat(Bj.css(A[0],"borderRightWidth",!0))||0)}function AT(B,A){return A4(B)+AO(B)+(A?A9(B):0)}function A4(A){return(parseFloat(Bj.css(A[0],"paddingTop",!0))||0)+(parseFloat(Bj.css(A[0],"paddingBottom",!0))||0)}function A9(A){return(parseFloat(Bj.css(A[0],"marginTop",!0))||0)+(parseFloat(Bj.css(A[0],"marginBottom",!0))||0)}function AO(A){return(parseFloat(Bj.css(A[0],"borderTopWidth",!0))||0)+(parseFloat(Bj.css(A[0],"borderBottomWidth",!0))||0)}function Bm(){}function Bb(B,A){return B-A}function A2(A){return Math.max.apply(Math,A)}function Ba(D,B){if(D=D||{},void 0!==D[B]){return D[B]}for(var C,A=B.split(/(?=[A-Z])/),E=A.length-1;E>=0;E--){if(C=D[A[E].toLowerCase()],void 0!==C){return C}}return D["default"]}function AN(A){return(A+"").replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/'/g,"&#039;").replace(/"/g,"&quot;").replace(/\n/g,"<br />")}function AM(A){return A.replace(/&.*?;/g,"")}function AX(A){A.attr("unselectable","on").css("MozUserSelect","none").bind("selectstart.ui",function(){return !1})}function A6(A){A.children().removeClass("fc-first fc-last").filter(":first-child").addClass("fc-first").end().filter(":last-child").addClass("fc-last")}function A5(D,A){var F=D.source||{},C=D.color,J=F.color,G=A("eventColor"),E=D.backgroundColor||C||F.backgroundColor||J||A("eventBackgroundColor")||G,B=D.borderColor||C||F.borderColor||J||A("eventBorderColor")||G,H=D.textColor||F.textColor||A("eventTextColor"),I=[];return E&&I.push("background-color:"+E),B&&I.push("border-color:"+B),H&&I.push("color:"+H),I.join(";")}function AZ(B,C,A){if(Bj.isFunction(B)&&(B=[B]),B){var E,D;for(E=0;B.length>E;E++){D=B[E].apply(C,A)||D}return D}}function AL(){for(var A=0;arguments.length>A;A++){if(void 0!==arguments[A]){return arguments[A]}}}function A0(D,A,H){var E,C,B,F,G=D[0],I=1==D.length&&"string"==typeof G;return Bz.isMoment(G)?(F=Bz.apply(null,D),G._ambigTime&&(F._ambigTime=!0),G._ambigZone&&(F._ambigZone=!0)):Bn(G)||void 0===G?F=Bz.apply(null,D):(E=!1,C=!1,I?Ab.test(G)?(G+="-01",D=[G],E=!0,C=!0):(B=BB.exec(G))&&(E=!B[5],C=!0):Bj.isArray(G)&&(C=!0),F=A?Bz.utc.apply(Bz,D):Bz.apply(null,D),E?(F._ambigTime=!0,F._ambigZone=!0):H&&(C?F._ambigZone=!0:I&&F.zone(G))),new AS(F)}function AS(A){Bw(this,A)}function A1(D){var B,C=[],A=!1,E=!1;for(B=0;D.length>B;B++){C.push(BD.moment(D[B])),A=A||C[B]._ambigTime,E=E||C[B]._ambigZone}for(B=0;C.length>B;B++){A?C[B].stripTime():E&&C[B].stripZone()}return C}function AJ(B,A){return Bz.fn.format.call(B,A)}function A3(B,A){return AP(B,AR(A))}function AP(D,B){var C,A="";for(C=0;B.length>C;C++){A+=Bl(D,B[C])}return A}function Bl(D,B){var C,A;return"string"==typeof B?B:(C=B.token)?Ac[C]?Ac[C](D):AJ(D,C):B.maybe&&(A=AP(D,B.maybe),A.match(/[1-9]/))?A:""}function AY(D,B,C,A,E){return D=BD.moment.parseZone(D),B=BD.moment.parseZone(B),C=D.lang().longDateFormat(C)||C,A=A||" - ",Bg(D,B,AR(C),A,E)}function Bg(E,A,H,C,L){var I,G,B,J,K="",N="",F="",M="",D="";for(G=0;H.length>G&&(I=Ad(E,A,H[G]),I!==!1);G++){K+=I}for(B=H.length-1;B>G&&(I=Ad(E,A,H[B]),I!==!1);B--){N=I+N}for(J=G;B>=J;J++){F+=Bl(E,H[J]),M+=Bl(A,H[J])}return(F||M)&&(D=L?M+C+F:F+C+M),K+D+N}function Ad(D,B,C){var A,E;return"string"==typeof C?C:(A=C.token)&&(E=BF[A.charAt(0)],E&&D.isSame(B,E))?AJ(D,A):!1}function AR(A){return A in BA?BA[A]:BA[A]=AK(A)}function AK(D){for(var B,C=[],A=/\[([^\]]*)\]|\(([^\)]*)\)|(LT|(\w)\4*o?)|([^\w\[\(]+)/g;B=A.exec(D);){B[1]?C.push(B[1]):B[2]?C.push({maybe:AK(B[2])}):B[3]?C.push({token:B[3]}):B[5]&&C.push(B[5])}return C}function AU(D,B){function C(G,F){return G.clone().stripTime().add("months",F).startOf("month")}function A(G){E.intervalStart=G.clone().stripTime().startOf("month"),E.intervalEnd=E.intervalStart.clone().add("months",1),E.start=E.intervalStart.clone(),E.start=E.skipHiddenDays(E.start),E.start.startOf("week"),E.start=E.skipHiddenDays(E.start),E.end=E.intervalEnd.clone(),E.end=E.skipHiddenDays(E.end,-1,!0),E.end.add("days",(7-E.end.weekday())%7),E.end=E.skipHiddenDays(E.end,-1,!0);var F=Math.ceil(E.end.diff(E.start,"weeks",!0));"fixed"==E.opt("weekMode")&&(E.end.add("weeks",6-F),F=6),E.title=B.formatDate(E.intervalStart,E.opt("titleFormat")),E.renderBasic(F,E.getCellsPerWeek(),!0)}var E=this;E.incrementDate=C,E.render=A,AV.call(E,D,B,"month")}function A7(D,B){function C(G,F){return G.clone().stripTime().add("weeks",F).startOf("week")}function A(F){E.intervalStart=F.clone().stripTime().startOf("week"),E.intervalEnd=E.intervalStart.clone().add("weeks",1),E.start=E.skipHiddenDays(E.intervalStart),E.end=E.skipHiddenDays(E.intervalEnd,-1,!0),E.title=B.formatRange(E.start,E.end.clone().subtract(1),E.opt("titleFormat")," — "),E.renderBasic(1,E.getCellsPerWeek(),!1)}var E=this;E.incrementDate=C,E.render=A,AV.call(E,D,B,"basicWeek")}function AQ(D,B){function C(H,F){var G=H.clone().stripTime().add("days",F);return G=E.skipHiddenDays(G,0>F?-1:1)}function A(F){E.start=E.intervalStart=F.clone().stripTime(),E.end=E.intervalEnd=E.start.clone().add("days",1),E.title=B.formatDate(E.start,E.opt("titleFormat")),E.renderBasic(1,1,!1)}var E=this;E.incrementDate=C,E.render=A,AV.call(E,D,B,"basicDay")}function AV(F,Cr,Cg){function Cx(C,A,B){BZ=C,B9=A,BW=B,Cs(),BY||Cq(),Ch()}function Cs(){H=BH("theme")?"ui":"fc",BM=BH("columnFormat"),BJ=BH("weekNumbers")}function Cq(){B1=Bj("<div class='fc-event-container' style='position:absolute;z-index:8;top:0;left:0'/>").appendTo(F)}function Ch(){var A=Ct();B2&&B2.remove(),B2=Bj(A).appendTo(F),BS=B2.find("thead"),B3=BS.find(".fc-day-header"),BY=B2.find("tbody"),B4=BY.find("tr"),BQ=BY.find(".fc-day"),B6=B4.find("td:first-child"),BV=B4.eq(0).find(".fc-day > div"),Cn=B4.eq(0).find(".fc-day-content > div"),A6(BS.add(BS.find("tr"))),A6(B4),B4.eq(0).addClass("fc-first"),B4.filter(":last").addClass("fc-last"),BQ.each(function(C,D){var B=t(Math.floor(C/B9),C%B9);K("dayRender",B8,B,Bj(D))}),Cp(BQ)}function Ct(){var A="<table class='fc-border-separate' style='width:100%' cellspacing='0'>"+Cw()+Cz()+"</table>";return A}function Cw(){var D,B,C=H+"-widget-header",A="";for(A+="<thead><tr>",BJ&&(A+="<th class='fc-week-number "+C+"'>"+AN(BH("weekNumberTitle"))+"</th>"),D=0;B9>D;D++){B=t(0,D),A+="<th class='fc-day-header fc-"+Ah[B.day()]+" "+C+"'>"+AN(Ae(B,BM))+"</th>"}return A+="</tr></thead>"}function Cz(){var D,B,C,A=H+"-widget-content",E="";for(E+="<tbody>",D=0;BZ>D;D++){for(E+="<tr class='fc-week'>",BJ&&(C=t(D,0),E+="<td class='fc-week-number "+A+"'><div>"+AN(Cd(C))+"</div></td>"),B=0;B9>B;B++){C=t(D,B),E+=Cm(C)}E+="</tr>"}return E+="</tbody>"}function Cm(E){var B=B8.intervalStart.month(),A=Cr.getNow().stripTime(),G="",C=H+"-widget-content",D=["fc-day","fc-"+Ah[E.day()],C];return E.month()!=B&&D.push("fc-other-month"),E.isSame(A,"day")?D.push("fc-today",H+"-state-highlight"):A>E?D.push("fc-past"):D.push("fc-future"),G+="<td class='"+D.join(" ")+"' data-date='"+E.format()+"'><div>",BW&&(G+="<div class='fc-day-number'>"+E.date()+"</div>"),G+="<div class='fc-day-content'><div style='position:relative'>&nbsp;</div></div></div></td>"}function Cy(B){R=B;var C,A,E,D=Math.max(R-BS.height(),0);"variable"==BH("weekMode")?C=A=Math.floor(D/(1==BZ?2:6)):(C=Math.floor(D/BZ),A=D-C*(BZ-1)),B6.each(function(G,I){BZ>G&&(E=Bj(I),E.find("> div").css("min-height",(G==BZ-1?A:C)-AT(E)))})}function Ck(A){Cj=A,BP.clear(),BI.clear(),BR=0,BJ&&(BR=BS.find("th.fc-week-number").outerWidth()),BX=Math.floor((Cj-BR)/B9),Bx(B3.slice(0,-1),BX)}function Cp(A){A.click(Ci).mousedown(BO)}function Ci(B){if(!BH("selectable")){var A=Cr.moment(Bj(this).data("date"));K("dayClick",this,A,B)}}function Cu(E,B,C){C&&B0.build();for(var A=T(E,B),G=0;A.length>G;G++){var D=A[G];Cp(Cf(D.row,D.leftCol,D.row,D.rightCol))}}function Cf(D,B,A,E){var C=B0.rect(D,B,A,E,F);return BK(C,F)}function Cv(A){return A.clone().stripTime().add("days",1)}function Ca(B,A){Cu(B,A,!0)}function Cl(){BL()}function B7(D,B){var C=BN(D),A=BQ[C.row*B9+C.col];K("dayClick",A,D,B)}function Cb(B,A){g.start(function(E){if(BL(),E){var D=t(E),C=D.clone().add(Cr.defaultAllDayEventDuration);Cu(D,C)}},A)}function BU(D,B,C){var A=g.stop();BL(),A&&K("drop",D,t(A),B,C)}function Co(A){return BP.left(A)}function Ce(A){return BP.right(A)}function B5(A){return BI.left(A)}function Cc(A){return BI.right(A)}function BT(A){return B4.eq(A)}var B8=this;B8.renderBasic=Cx,B8.setHeight=Cy,B8.setWidth=Ck,B8.renderDayOverlay=Cu,B8.defaultSelectionEnd=Cv,B8.renderSelection=Ca,B8.clearSelection=Cl,B8.reportDayClick=B7,B8.dragStart=Cb,B8.dragStop=BU,B8.getHoverListener=function(){return g},B8.colLeft=Co,B8.colRight=Ce,B8.colContentLeft=B5,B8.colContentRight=Cc,B8.getIsCellAllDay=function(){return !0},B8.allDayRow=BT,B8.getRowCnt=function(){return BZ},B8.getColCnt=function(){return B9},B8.getColWidth=function(){return BX},B8.getDaySegmentContainer=function(){return B1},AG.call(B8,F,Cr,Cg),BG.call(B8),As.call(B8),AW.call(B8);var B2,BS,B3,BY,B4,BQ,B6,BV,Cn,B1,Cj,R,BX,BR,BZ,B9,BW,B0,g,BP,BI,H,BM,BJ,BH=B8.opt,K=B8.trigger,BK=B8.renderOverlay,BL=B8.clearOverlays,BO=B8.daySelectionMousedown,t=B8.cellToDate,BN=B8.dateToCell,T=B8.rangeToSegments,Ae=Cr.formatDate,Cd=Cr.calculateWeekNumber;AX(F.addClass("fc-grid")),B0=new Ag(function(B,C){var A,E,D;B3.each(function(I,G){A=Bj(G),E=A.offset().left,I&&(D[1]=E),D=[E],C[I]=D}),D[1]=E+A.outerWidth(),B4.each(function(I,G){BZ>I&&(A=Bj(G),E=A.offset().top,I&&(D[1]=E),D=[E],B[I]=D)}),D[1]=E+A.outerHeight()}),g=new Ak(B0),BP=new Av(function(A){return BV.eq(A)}),BI=new Av(function(A){return Cn.eq(A)})}function AW(){function C(E,D){B.renderDayEvents(E,D)}function A(){B.getDaySegmentContainer().empty()}var B=this;B.renderEvents=C,B.clearEvents=A,AB.call(B)}function At(D,B){function C(G,F){return G.clone().stripTime().add("weeks",F).startOf("week")}function A(F){E.intervalStart=F.clone().stripTime().startOf("week"),E.intervalEnd=E.intervalStart.clone().add("weeks",1),E.start=E.skipHiddenDays(E.intervalStart),E.end=E.skipHiddenDays(E.intervalEnd,-1,!0),E.title=B.formatRange(E.start,E.end.clone().subtract(1),E.opt("titleFormat")," — "),E.renderAgenda(E.getCellsPerWeek())}var E=this;E.incrementDate=C,E.render=A,AE.call(E,D,B,"agendaWeek")}function AI(D,B){function C(H,F){var G=H.clone().stripTime().add("days",F);return G=E.skipHiddenDays(G,0>F?-1:1)}function A(F){E.start=E.intervalStart=F.clone().stripTime(),E.end=E.intervalEnd=E.start.clone().add("days",1),E.title=B.formatDate(E.start,E.opt("titleFormat")),E.renderAgenda(1)}var E=this;E.incrementDate=C,E.render=A,AE.call(E,D,B,"agendaDay")}function Ay(B,A){return A.longDateFormat("LT").replace(":mm","(:mm)").replace(/(\Wmm)$/,"($1)").replace(/\s*a$/i,"a")}function Ao(B,A){return A.longDateFormat("LT").replace(/\s*a$/i,"")}function AE(CF,Cu,CK){function CG(A){BI=A,CE(),Cs?CH():Cv()}function CE(){t=e("theme")?"ui":"fc",R=e("isRTL"),CW=e("columnFormat"),CP=Bz.duration(e("minTime")),T=Bz.duration(e("maxTime")),BW=Bz.duration(e("slotDuration")),BX=e("snapDuration"),BX=BX?Bz.duration(BX):BW}function Cv(){var A,I,C,B,D=t+"-widget-header",G=t+"-widget-content",E=0===BW.asMinutes()%15;for(CH(),B2=Bj("<div style='position:absolute;z-index:2;left:0;width:100%'/>").appendTo(CF),e("allDaySlot")?(BU=Bj("<div class='fc-event-container' style='position:absolute;z-index:8;top:0;left:0'/>").appendTo(B2),A="<table style='width:100%' class='fc-agenda-allday' cellspacing='0'><tr><th class='"+D+" fc-agenda-axis'>"+(e("allDayHTML")||AN(e("allDayText")))+"</th><td><div class='fc-day-content'><div style='position:relative'/></div></td><th class='"+D+" fc-agenda-gutter'>&nbsp;</th></tr></table>",BK=Bj(A).appendTo(B2),BZ=BK.find("tr"),Ct(BZ.find("td")),B2.append("<div class='fc-agenda-divider "+D+"'><div class='fc-agenda-divider-inner'/></div>")):BU=Bj([]),BT=Bj("<div style='position:absolute;width:100%;overflow-x:hidden;overflow-y:auto'/>").appendTo(B2),BL=Bj("<div style='position:relative;width:100%;overflow:hidden'/>").appendTo(BT),BV=Bj("<div class='fc-event-container' style='position:absolute;z-index:8;top:0;left:0'/>").appendTo(BL),A="<table class='fc-agenda-slots' style='width:100%' cellspacing='0'><tbody>",I=Bz.duration(+CP),g=0;T>I;){C=Cx.start.clone().time(I),B=C.minutes(),A+="<tr class='fc-slot"+g+" "+(B?"fc-minor":"")+"'><th class='fc-agenda-axis "+D+"'>"+(E&&B?"&nbsp;":AN(x(C,e("axisFormat"))))+"</th><td class='"+G+"'><div style='position:relative'>&nbsp;</div></td></tr>",I.add(BW),g++}A+="</tbody></table>",BY=Bj(A).appendTo(BL),CI(BY.find("td"))}function CH(){var A=CJ();Cs&&Cs.remove(),Cs=Bj(A).appendTo(CF),Cb=Cs.find("thead"),B5=Cb.find("th").slice(1,-1),Cd=Cs.find("tbody"),Co=Cd.find("td").slice(0,-1),Ca=Co.find("> div"),Ce=Co.find(".fc-day-content > div"),Cf=Co.eq(0),BP=Ca.eq(0),A6(Cb.add(Cb.find("tr"))),A6(Cd.add(Cd.find("tr")))}function CJ(){var A="<table style='width:100%' class='fc-agenda-days fc-border-separate' cellspacing='0'>"+CM()+CA()+"</table>";return A}function CM(){var D,B,C,A=t+"-widget-header",E="";for(E+="<thead><tr>",e("weekNumbers")?(D=CV(0,0),B=CU(D),R?B+=e("weekNumberTitle"):B=e("weekNumberTitle")+B,E+="<th class='fc-agenda-axis fc-week-number "+A+"'>"+AN(B)+"</th>"):E+="<th class='fc-agenda-axis "+A+"'>&nbsp;</th>",C=0;BI>C;C++){D=CV(0,C),E+="<th class='fc-"+Ah[D.day()]+" fc-col"+C+" "+A+"'>"+AN(x(D,CW))+"</th>"}return E+="<th class='fc-agenda-gutter "+A+"'>&nbsp;</th></tr></thead>"}function CA(){var C,A,E,K,G,D=t+"-widget-header",B=t+"-widget-content",I=Cu.getNow().stripTime(),J="";for(J+="<tbody><tr><th class='fc-agenda-axis "+D+"'>&nbsp;</th>",E="",A=0;BI>A;A++){C=CV(0,A),G=["fc-col"+A,"fc-"+Ah[C.day()],B],C.isSame(I,"day")?G.push(t+"-state-highlight","fc-today"):I>C?G.push("fc-past"):G.push("fc-future"),K="<td class='"+G.join(" ")+"'><div><div class='fc-day-content'><div style='position:relative'>&nbsp;</div></div></div></td>",E+=K}return J+=E,J+="<td class='fc-agenda-gutter "+B+"'>&nbsp;</td></tr></tbody>"}function CL(E){void 0===E&&(E=B0),B0=E,CY={};var B=Cd.position().top,C=BT.position().top,A=Math.min(E-B,BY.height()+C+1);BP.height(A-AT(Cf)),B2.css("top",B),BT.height(A-C-1);var G=BY.find("tr:first").height()+1,D=BY.find("tr:eq(1)").height();BJ=(G+D)/2,Ae=BW/BX,CS=BJ/Ae}function Cy(B){BQ=B,CQ.clear(),F.clear();var C=Cb.find("th:first");BK&&(C=C.add(BK.find("th:first"))),C=C.add(BY.find("th:first")),BO=0,Bx(C.width("").each(function(E,G){BO=Math.max(BO,Bj(G).outerWidth())}),BO);var A=Cs.find(".fc-agenda-gutter");BK&&(A=A.add(BK.find("th.fc-agenda-gutter")));var D=BT[0].clientWidth;BM=BT.width()-D,BM?(Bx(A,BM),A.show().prev().removeClass("fc-last")):A.hide().prev().addClass("fc-last"),BS=Math.floor((D-BO)/BI),Bx(B5.slice(0,-1),BS)}function CD(){function B(){BT.scrollTop(A)}var A=B6(Bz.duration(e("scrollTime")))+1;B(),setTimeout(B,0)}function Cw(){CD()}function Ct(A){A.click(Cp).mousedown(BN)}function CI(A){A.click(Cp).mousedown(Cl)}function Cp(D){if(!e("selectable")){var A=Math.min(BI-1,Math.floor((D.pageX-Cs.offset().left-BO)/BS)),B=CV(0,A),E=this.parentNode.className.match(/fc-slot(\d+)/);if(E){var C=parseInt(E[1],10);B.add(CP+C*BW),B=Cu.rezoneDate(B),CN("dayClick",Co[A],B,D)}else{CN("dayClick",Co[A],B,D)}}}function Cz(E,B,C){C&&BH.build();for(var A=CX(E,B),G=0;A.length>G;G++){var D=A[G];Ct(Cm(D.row,D.leftCol,D.row,D.rightCol))}}function Cm(D,B,C,A){var E=BH.rect(D,B,C,A,B2);return m(E,B2)}function Cq(D,A){D=D.clone().stripZone(),A=A.clone().stripZone();for(var G=0;BI>G;G++){var C=CV(0,G),L=C.clone().add("days",1),I=D>C?D:C,E=A>L?L:A;if(E>I){var B=BH.rect(0,G,0,G,BL),J=Ch(I,C),K=Ch(E,C);B.top=J,B.height=K-J,CI(m(B,BL))}}}function B8(A){return CQ.left(A)}function CC(A){return F.left(A)}function Ck(A){return CQ.right(A)}function Cr(A){return F.right(A)}function B7(A){return e("allDaySlot")&&!A.row}function Cn(B){var A=CV(0,B.col),C=B.row;return e("allDaySlot")&&C--,C>=0&&(A.time(Bz.duration(CP+C*BX)),A=Cu.rezoneDate(A)),A}function Ch(B,A){return B6(Bz.duration(B.clone().stripZone()-A.clone().stripTime()))}function B6(E){if(CP>E){return 0}if(E>=T){return BY.height()}var B=(E-CP)/BW,C=Math.floor(B),A=B-C,G=CY[C];void 0===G&&(G=CY[C]=BY.find("tr").eq(C).find("td div")[0].offsetTop);var D=G-1+A*BJ;return D=Math.max(D,0)}function Ci(A){return A.hasTime()?A.clone().add(BW):A.clone().add("days",1)}function Cc(B,A){B.hasTime()||A.hasTime()?Cj(B,A):e("allDaySlot")&&Cz(B,A,!0)}function Cj(A,E){var C=e("selectHelper");if(BH.build(),C){var J=CO(A).col;if(J>=0&&BI>J){var G=BH.rect(0,J,0,J,BL),D=Ch(A,A),B=Ch(E,A);if(B>D){if(G.top=D,G.height=B-D,G.left+=2,G.width-=5,Bj.isFunction(C)){var I=C(A,E);I&&(G.position="absolute",B1=Bj(I).css(G).appendTo(BL))}else{G.isStart=!0,G.isEnd=!0,B1=Bj(B3({title:"",start:A,end:E,className:["fc-select-helper"],editable:!1},G)),B1.css("opacity",e("dragOpacity"))}B1&&(CI(B1),BL.append(B1),Bx(B1,G.width,!0),Bs(B1,G.height,!0))}}}else{Cq(A,E)}}function B4(){CT(),B1&&(B1.remove(),B1=null)}function Cl(A){if(1==A.which&&e("selectable")){H(A);var B;CR.start(function(E,D){if(B4(),E&&E.col==D.col&&!B7(E)){var C=Cn(D),G=Cn(E);B=[C,C.clone().add(BX),G,G.clone().add(BX)].sort(Bb),Cj(B[0],B[3])}else{B=null}},A),Bj(document).one("mouseup",function(C){CR.stop(),B&&(+B[0]==+B[1]&&B9(B[0],C),BR(B[0],B[3],C))})}}function B9(B,A){CN("dayClick",Co[CO(B).col],B,A)}function CB(B,A){CR.start(function(E){if(CT(),E){var C=Cn(E),D=C.clone();C.hasTime()?(D.add(Cu.defaultTimedEventDuration),Cq(C,D)):(D.add(Cu.defaultAllDayEventDuration),Cz(C,D))}},A)}function Cg(D,B,C){var A=CR.stop();CT(),A&&CN("drop",D,Cn(A),B,C)}var Cx=this;Cx.renderAgenda=CG,Cx.setWidth=Cy,Cx.setHeight=CL,Cx.afterRender=Cw,Cx.computeDateTop=Ch,Cx.getIsCellAllDay=B7,Cx.allDayRow=function(){return BZ},Cx.getCoordinateGrid=function(){return BH},Cx.getHoverListener=function(){return CR},Cx.colLeft=B8,Cx.colRight=Ck,Cx.colContentLeft=CC,Cx.colContentRight=Cr,Cx.getDaySegmentContainer=function(){return BU},Cx.getSlotSegmentContainer=function(){return BV},Cx.getSlotContainer=function(){return BL},Cx.getRowCnt=function(){return 1},Cx.getColCnt=function(){return BI},Cx.getColWidth=function(){return BS},Cx.getSnapHeight=function(){return CS},Cx.getSnapDuration=function(){return BX},Cx.getSlotHeight=function(){return BJ},Cx.getSlotDuration=function(){return BW},Cx.getMinTime=function(){return CP},Cx.getMaxTime=function(){return T},Cx.defaultSelectionEnd=Ci,Cx.renderDayOverlay=Cz,Cx.renderSelection=Cc,Cx.clearSelection=B4,Cx.reportDayClick=B9,Cx.dragStart=CB,Cx.dragStop=Cg,AG.call(Cx,CF,Cu,CK),BG.call(Cx),As.call(Cx),Az.call(Cx);var Cs,Cb,B5,Cd,Co,Ca,Ce,Cf,BP,B2,BU,BK,BZ,BT,BL,BV,BY,B1,BQ,B0,BO,BS,BM,BW,BJ,BX,Ae,CS,BI,g,BH,CR,CQ,F,t,R,CP,T,CW,e=Cx.opt,CN=Cx.trigger,m=Cx.renderOverlay,CT=Cx.clearOverlays,BR=Cx.reportSelection,H=Cx.unselect,BN=Cx.daySelectionMousedown,B3=Cx.slotSegHtml,CV=Cx.cellToDate,CO=Cx.dateToCell,CX=Cx.rangeToSegments,x=Cu.formatDate,CU=Cu.calculateWeekNumber,CY={};AX(CF.addClass("fc-agenda")),BH=new Ag(function(A,E){function C(M){return Math.max(I,Math.min(J,M))}var K,G,D;B5.each(function(N,M){K=Bj(M),G=K.offset().left,N&&(D[1]=G),D=[G],E[N]=D}),D[1]=G+K.outerWidth(),e("allDaySlot")&&(K=BZ,G=K.offset().top,A[0]=[G,G+K.outerHeight()]);for(var B=BL.offset().top,I=BT.offset().top,J=I+BT.outerHeight(),L=0;g*Ae>L;L++){A.push([C(B+CS*L),C(B+CS*(L+1))])}}),CR=new Ak(BH),CQ=new Av(function(A){return Ca.eq(A)}),F=new Av(function(A){return Ce.eq(A)})}function Az(){function B7(G,C){var D,B=G.length,E=[],F=[];for(D=0;B>D;D++){G[D].allDay?E.push(G[D]):F.push(G[D])}B0("allDaySlot")&&(y(E,C),B1()),B6(Cc(F),C)}function BX(){BQ().empty(),BU().empty()}function Cc(E){var B,G,D,J,F,C=BJ(),H=R(),I=BH(),L=[];for(G=0;C>G;G++){for(B=e(0,G),F=B8(E,B.clone().time(H),B.clone().time(I)),F=Ax(F),D=0;F.length>D;D++){J=F[D],J.col=G,L.push(J)}}return L}function B8(E,B,H){B=B.clone().stripZone(),H=H.clone().stripZone();var D,M,I,G,C,J,L,O,F=[],N=E.length;for(D=0;N>D;D++){M=E[D],I=M.start.clone().stripZone(),G=BI(M).stripZone(),G>B&&H>I&&(B>I?(C=B.clone(),L=!1):(C=I,L=!0),G>H?(J=H.clone(),O=!1):(J=G,O=!0),F.push({event:M,start:C,end:J,isStart:L,isEnd:O}))}return F.sort(Aq)}function B6(W,J){var B,P,L,I,O,V,G,Q,U,M,N,h,E,Y,q,k,l=W.length,j="",X=BU(),Z=B0("isRTL");for(B=0;l>B;B++){P=W[B],L=P.event,I=B4(P.start,P.start),O=B4(P.end,P.start),V=BO(P.col),G=BV(P.col),Q=G-V,G-=0.025*Q,Q=G-V,U=Q*(P.forwardCoord-P.backwardCoord),B0("slotEventOverlap")&&(U=Math.max(2*(U-10),U)),Z?(N=G-P.backwardCoord*Q,M=N-U):(M=V+P.backwardCoord*Q,N=M+U),M=Math.max(M,V),N=Math.min(N,G),U=N-M,P.top=I,P.left=M,P.outerWidth=U,P.outerHeight=O-I,j+=BY(L,P)}for(X[0].innerHTML=j,h=X.children(),B=0;l>B;B++){P=W[B],L=P.event,E=Bj(h[B]),Y=B5("eventRender",L,L,E),Y===!1?E.remove():(Y&&Y!==!0&&(E.remove(),E=Bj(Y).css({position:"absolute",top:P.top,left:P.left}).appendTo(X)),P.element=E,L._id===J?B9(L,E,P):E[0]._fci=B,K(L,E))}for(Bf(X,W,B9),B=0;l>B;B++){P=W[B],(E=P.element)&&(P.vsides=AT(E,!0),P.hsides=Bc(E,!0),q=E.find(".fc-event-title"),q.length&&(P.contentTop=q[0].offsetTop))}for(B=0;l>B;B++){P=W[B],(E=P.element)&&(E[0].style.width=Math.max(0,P.outerWidth-P.hsides)+"px",k=Math.max(0,P.outerHeight-P.vsides),E[0].style.height=k+"px",L=P.event,void 0!==P.contentTop&&10>k-P.contentTop&&(E.find("div.fc-event-time").text(t(L.start,B0("timeFormat"))+" - "+L.title),E.find("div.fc-event-title").remove()),B5("eventAfterRender",L,L,E))}}function BY(F,C){var D="<",B=F.url,G=A5(F,B0),E=["fc-event","fc-event-vert"];return Ce(F)&&E.push("fc-event-draggable"),C.isStart&&E.push("fc-event-start"),C.isEnd&&E.push("fc-event-end"),E=E.concat(F.className),F.source&&(E=E.concat(F.source.className||[])),D+=B?"a href='"+AN(F.url)+"'":"div",D+=" class='"+E.join(" ")+"' style='position:absolute;top:"+C.top+"px;left:"+C.left+"px;"+G+"'><div class='fc-event-inner'><div class='fc-event-time'>"+AN(Cd.getEventTimeText(F))+"</div><div class='fc-event-title'>"+AN(F.title||"")+"</div></div><div class='fc-event-bg'></div>",C.isEnd&&Ca(F)&&(D+="<div class='ui-resizable-handle ui-resizable-s'>=</div>"),D+="</"+(B?"a":"div")+">"}function B9(E,C,D){var B=C.find("div.fc-event-time");Ce(E)&&Cf(E,C,B),D.isEnd&&Ca(E)&&B2(E,C,B),BT(E,C)}function Cb(E,H,B){function O(){M||(H.width(I).height("").draggable("option","grid",null),M=!0)}var I,G,C,J=B.isStart,M=!0,S=m(),F=BR(),P=R(),D=Ae(),Q=BM(),N=T(),L=BL();H.draggable({opacity:B0("dragOpacity","month"),revertDuration:B0("dragRevertDuration"),start:function(V,U){B5("eventDragStart",H[0],E,V,U),p(E,H),I=H.width(),S.start(function(X,W){if(A(),X){G=!1;var Y=e(0,W.col),Z=e(0,X.col);C=Z.diff(Y,"days"),X.row?J?M&&(H.width(F-10),Bs(H,BS.defaultTimedEventDuration/D*Q),H.draggable("option","grid",[F,1]),M=!1):G=!0:(BZ(E.start.clone().add("days",C),BI(E).add("days",C)),O()),G=G||M&&!C}else{O(),G=!0}H.draggable("option","revert",G)},V,"drag")},stop:function(U,V){if(S.stop(),A(),B5("eventDragStop",H[0],E,U,V),G){O(),H.css("filter",""),BP(E,H)}else{var W,Y,X=E.start.clone().add("days",C);M||(Y=Math.round((H.offset().top-BN().offset().top)/L),W=Bz.duration(P+Y*N),X=BS.rezoneDate(X.clone().time(W))),B3(H[0],E,X,U,V)}}})}function Cf(I,Y,M){function B(){A(),F&&(U?(M.hide(),Y.draggable("option","grid",null),BZ(Q,h)):(V(),M.css("display",""),Y.draggable("option","grid",[f,j])))}function V(){Q&&M.text(Cd.getEventTimeText(Q,I.end?h:null))}var N,L,F,O,U,X,J,G,W,P,k,Q,h,H=Cd.getCoordinateGrid(),Z=BJ(),f=BR(),j=BL(),q=T();Y.draggable({scroll:!1,grid:[f,j],axis:1==Z?"y":!1,opacity:B0("dragOpacity"),revertDuration:B0("dragRevertDuration"),start:function(D,C){B5("eventDragStart",Y[0],I,D,C),p(I,Y),H.build(),N=Y.position(),L=H.cell(D.pageX,D.pageY),F=O=!0,U=X=BW(L),J=G=0,W=0,P=k=0,Q=null,h=null},drag:function(E,b){var c=H.cell(E.pageX,E.pageY);if(F=!!c){if(U=BW(c),J=Math.round((b.position.left-N.left)/f),J!=G){var S=e(0,L.col),C=L.col+J;C=Math.max(0,C),C=Math.min(Z-1,C);var D=e(0,C);W=D.diff(S,"days")}U||(P=Math.round((b.position.top-N.top)/j))}(F!=O||U!=X||J!=G||P!=k)&&(U?(Q=I.start.clone().stripTime().add("days",W),h=Q.clone().add(BS.defaultAllDayEventDuration)):(Q=I.start.clone().add(P*q).add("days",W),h=BI(I).add(P*q).add("days",W)),B(),O=F,X=U,G=J,k=P),Y.draggable("option","revert",!F)},stop:function(C,D){A(),B5("eventDragStop",Y[0],I,C,D),F&&(U||W||P)?B3(Y[0],I,Q,C,D):(F=!0,U=!1,J=0,W=0,P=0,B(),Y.css("filter",""),Y.css(N),BP(I,Y))}})}function B2(E,B,G){var D,I,H,F=BL(),C=T();B.resizable({handles:{s:".ui-resizable-handle"},grid:F,start:function(J,L){D=I=0,p(E,B),B5("eventResizeStart",B[0],E,J,L)},resize:function(L,M){if(D=Math.round((Math.max(F,B.height())-M.originalSize.height)/F),D!=I){H=BI(E).add(C*D);var J;J=D?Cd.getEventTimeText(E.start,H):Cd.getEventTimeText(E),G.text(J),I=D}},stop:function(J,L){B5("eventResizeStop",B[0],E,J,L),D?BK(B[0],E,H,J,L):BP(E,B)}})}var Cd=this;Cd.renderEvents=B7,Cd.clearEvents=BX,Cd.slotSegHtml=BY,AB.call(Cd);var B0=Cd.opt,B5=Cd.trigger,Ce=Cd.isEventDraggable,Ca=Cd.isEventResizable,BT=Cd.eventElementHandlers,B1=Cd.setHeight,BQ=Cd.getDaySegmentContainer,BU=Cd.getSlotSegmentContainer,m=Cd.getHoverListener,B4=Cd.computeDateTop,BW=Cd.getIsCellAllDay,BO=Cd.colContentLeft,BV=Cd.colContentRight,e=Cd.cellToDate,BJ=Cd.getColCnt,BR=Cd.getColWidth,BL=Cd.getSnapHeight,T=Cd.getSnapDuration,BM=Cd.getSlotHeight,Ae=Cd.getSlotDuration,BN=Cd.getSlotContainer,K=Cd.reportEventElement,BP=Cd.showEvents,p=Cd.hideEvents,B3=Cd.eventDrop,BK=Cd.eventResize,BZ=Cd.renderDayOverlay,A=Cd.clearOverlays,y=Cd.renderDayEvents,R=Cd.getMinTime,BH=Cd.getMaxTime,BS=Cd.calendar,t=BS.formatDate,BI=BS.getEventEnd;Cd.draggableDayEvent=Cb}function Ax(D){var B,C=Ap(D),A=C[0];if(AA(C),A){for(B=0;A.length>B;B++){AD(A[B])}for(B=0;A.length>B;B++){AH(A[B],0,0)}}return Au(C)}function Ap(D){var B,C,A,E=[];for(B=0;D.length>B;B++){for(C=D[B],A=0;E.length>A&&AF(C,E[A]).length;A++){}(E[A]||(E[A]=[])).push(C)}return E}function AA(E){var B,C,A,F,D;for(B=0;E.length>B;B++){for(C=E[B],A=0;C.length>A;A++){for(F=C[A],F.forwardSegs=[],D=B+1;E.length>D;D++){AF(F,E[D],F.forwardSegs)}}}}function AD(D){var B,C,A=D.forwardSegs,E=0;if(void 0===D.forwardPressure){for(B=0;A.length>B;B++){C=A[B],AD(C),E=Math.max(E,1+C.forwardPressure)}D.forwardPressure=E}}function AH(D,B,C){var A,E=D.forwardSegs;if(void 0===D.forwardCoord){for(E.length?(E.sort(Aw),AH(E[0],B+1,C),D.forwardCoord=E[0].backwardCoord):D.forwardCoord=1,D.backwardCoord=D.forwardCoord-(D.forwardCoord-C)/(B+1),A=0;E.length>A;A++){AH(E[A],0,D.forwardCoord)}}}function Au(D){var B,C,A,E=[];for(B=0;D.length>B;B++){for(C=D[B],A=0;C.length>A;A++){E.push(C[A])}}return E}function AF(D,B,C){C=C||[];for(var A=0;B.length>A;A++){Ar(D,B[A])&&C.push(B[A])}return C}function Ar(B,A){return B.end>A.start&&B.start<A.end}function Aw(B,A){return A.forwardPressure-B.forwardPressure||(B.backwardCoord||0)-(A.backwardCoord||0)||Aq(B,A)}function Aq(B,A){return B.start-A.start||A.end-A.start-(B.end-B.start)||(B.event.title||"").localeCompare(A.event.title)}function AG(BX,BO,B3){function BY(B,C){var A=q[B];return Bj.isPlainObject(A)&&!Bo(B)?Ba(A,C||B3):A}function BP(B,A){return BO.trigger.apply(BO,[B,A||Y].concat(Array.prototype.slice.call(arguments,2),[Y]))}function BZ(B){var A=B.source||{};return AL(B.startEditable,A.startEditable,BY("eventStartEditable"),B.editable,A.editable,BY("editable"))}function B2(B){var A=B.source||{};return AL(B.durationEditable,A.durationEditable,BY("eventDurationEditable"),B.editable,A.editable,BY("editable"))}function B6(){BI={},i=[]}function BT(B,A){i.push({event:B,element:A}),BI[B._id]?BI[B._id].push(A):BI[B._id]=[A]}function B4(){Bj.each(i,function(B,A){Y.trigger("eventDestroy",A.event,A.event,A.element)})}function BR(B,A){A.click(function(C){return A.hasClass("ui-draggable-dragging")||A.hasClass("ui-resizable-resizing")?void 0:BP("eventClick",this,B,C)}).hover(function(C){BP("eventMouseover",this,B,C)},function(C){BP("eventMouseout",this,B,C)})}function BW(B,A){B5(B,A,"show")}function BQ(B,A){B5(B,A,"hide")}function B5(E,B,C){var A,F=BI[E._id],D=F.length;for(A=0;D>A;A++){B&&F[A][0]==B[0]||F[A][C]()}}function B0(E,B,C,F,D){var A=BO.mutateEvent(B,C,null);BP("eventDrop",E,B,A.dateDelta,function(){A.undo(),BJ(B._id)},F,D),BJ(B._id)}function BN(E,B,C,F,D){var A=BO.mutateEvent(B,null,C);BP("eventResize",E,B,A.durationDelta,function(){A.undo(),BJ(B._id)},F,D),BJ(B._id)}function B1(A){return Bz.isMoment(A)&&(A=A.day()),Ae[A]}function BK(){return t}function BS(D,B,C){var A=D.clone();for(B=B||1;Ae[(A.day()+(C?B:0)+7)%7];){A.add("days",B)}return A}function X(){var C=BH.apply(null,arguments),A=BL(C),B=Q(A);return B}function BH(E,B){var C=Y.getColCnt(),A=e?-1:1,F=e?C-1:0;"object"==typeof E&&(B=E.col,E=E.row);var D=E*C+(B*A+F);return D}function BL(B){var A=Y.start.day();return B+=U[A],7*Math.floor(B/t)+BU[(B%t+t)%t]-A}function Q(A){return Y.start.clone().add("days",A)}function BV(D){var B=BM(D),C=z(B),A=K(C);return A}function BM(A){return A.clone().stripTime().diff(Y.start,"days")}function z(B){var A=Y.start.day();return B+=A,Math.floor(B/7)*t+U[(B%7+7)%7]-U[A]}function K(E){var B=Y.getColCnt(),C=e?-1:1,A=e?B-1:0,F=Math.floor(E/B),D=(E%B+B)%B*C+A;return{row:F,col:D}}function J(E,j){var L=Y.getRowCnt(),A=Y.getColCnt(),S=[],M=BM(E),I=BM(j),B=+j.time();B&&B>=V&&I++,I=Math.max(I,M+1);for(var N=z(M),R=z(I)-1,Z=0;L>Z;Z++){var F=Z*A,T=F+A-1,D=Math.max(N,F),H=Math.min(R,T);if(H>=D){var C=K(D),W=K(H),O=[C.col,W.col].sort(),k=BL(D)==M,P=BL(H)+1==I;S.push({row:Z,leftCol:O[0],rightCol:O[1],isStart:k,isEnd:P})}}return S}var Y=this;Y.element=BX,Y.calendar=BO,Y.name=B3,Y.opt=BY,Y.trigger=BP,Y.isEventDraggable=BZ,Y.isEventResizable=B2,Y.clearEventData=B6,Y.reportEventElement=BT,Y.triggerEventDestroy=B4,Y.eventElementHandlers=BR,Y.showEvents=BW,Y.hideEvents=BQ,Y.eventDrop=B0,Y.eventResize=BN;var BJ=BO.reportEventChange,BI={},i=[],q=BO.options,V=Bz.duration(q.nextDayThreshold);Y.getEventTimeText=function(C){var A,B;return 2===arguments.length?(A=arguments[0],B=arguments[1]):(A=C.start,B=C.end),B&&BY("displayEventEnd")?BO.formatRange(A,B,BY("timeFormat")):BO.formatDate(A,BY("timeFormat"))},Y.isHiddenDay=B1,Y.skipHiddenDays=BS,Y.getCellsPerWeek=BK,Y.dateToCell=BV,Y.dateToDayOffset=BM,Y.dayOffsetToCellOffset=z,Y.cellOffsetToCell=K,Y.cellToDate=X,Y.cellToCellOffset=BH,Y.cellOffsetToDayOffset=BL,Y.dayOffsetToDate=Q,Y.rangeToSegments=J;var t,G=BY("hiddenDays")||[],Ae=[],U=[],BU=[],e=BY("isRTL");(function(){BY("weekends")===!1&&G.push(0,6);for(var A=0,B=0;7>A;A++){U[A]=B,Ae[A]=-1!=Bj.inArray(A,G),Ae[A]||(BU[B]=A,B++)}if(t=B,!t){throw"invalid hiddenDays"}})()}function AB(){function A(D,B){var C=B3(D,!1,!0);AC(C,function(F,E){B2(F.event,E)}),Cg(C,B),AC(C,function(F,E){B0("eventAfterRender",F.event,F.event,E)})}function Cd(E,B,C){var F=B3([E],!0,!1),D=[];return AC(F,function(I,G){I.row===B&&G.css("top",C),D.push(G[0])}),D}function B3(B,F,C){var G,I,E=BR(),J=F?Bj("<div/>"):E,D=Cj(B);return Cc(D),G=B4(D),J[0].innerHTML=G,I=J.children(),F&&E.append(I),Ci(D,I),AC(D,function(L,K){L.hsides=Bc(K,!0)}),AC(D,function(L,K){K.width(Math.max(0,L.outerWidth-L.hsides))}),AC(D,function(L,K){L.outerHeight=K.outerHeight(!0)}),Cm(D,C),D}function Cj(E){for(var C=[],D=0;E.length>D;D++){var B=Ce(E[D]);C.push.apply(C,B)}return C}function Ce(D){for(var B=BO(D.start,R(D)),C=0;B.length>C;C++){B[C].event=D}return B}function Cc(E){for(var B=BW("isRTL"),G=0;E.length>G;G++){var D=E[G],J=(B?D.isEnd:D.isStart)?BJ:t,I=(B?D.isStart:D.isEnd)?B9:BV,F=J(D.leftCol),C=I(D.rightCol);D.left=F,D.outerWidth=C-F}}function B4(D){for(var B="",C=0;D.length>C;C++){B+=Cf(D[C])}return B}function Cf(G){var C="",D=BW("isRTL"),B=G.event,I=B.url,E=["fc-event","fc-event-hori"];BI(B)&&E.push("fc-event-draggable"),G.isStart&&E.push("fc-event-start"),G.isEnd&&E.push("fc-event-end"),E=E.concat(B.className),B.source&&(E=E.concat(B.source.className||[]));var F=A5(B,BW);return C+=I?"<a href='"+AN(I)+"'":"<div",C+=" class='"+E.join(" ")+"' style='position:absolute;left:"+G.left+"px;"+F+"'><div class='fc-event-inner'>",!B.allDay&&G.isStart&&(C+="<span class='fc-event-time'>"+AN(BN.getEventTimeText(B))+"</span>"),C+="<span class='fc-event-title'>"+AN(B.title||"")+"</span></div>",B.allDay&&G.isEnd&&Ca(B)&&(C+="<div class='ui-resizable-handle ui-resizable-"+(D?"w":"e")+"'>&nbsp;&nbsp;&nbsp;</div>"),C+="</"+(I?"a":"div")+">"}function Ci(C,D){for(var B=0;C.length>B;B++){var I=C[B],E=I.event,F=D.eq(B),G=B0("eventRender",E,E,F);G===!1?F.remove():(G&&G!==!0&&(G=Bj(G).css({position:"absolute",left:I.left}),F.replaceWith(G),F=G),I.element=F)}}function Cm(F,C){var D,B=B8(F),G=Cl(),E=[];if(C){for(D=0;G.length>D;D++){G[D].height(B[D])}}for(D=0;G.length>D;D++){E.push(G[D].position().top)}AC(F,function(J,I){I.css("top",E[J.row]+J.top)})}function B8(E){for(var B,G=BT(),D=BM(),L=[],I=Ck(E),F=0;G>F;F++){var C=I[F],J=[];for(B=0;D>B;B++){J.push(0)}for(var K=0;C.length>K;K++){var N=C[K];for(N.top=A2(J.slice(N.leftCol,N.rightCol+1)),B=N.leftCol;N.rightCol>=B;B++){J[B]=N.top+N.outerHeight}}L.push(A2(J))}return L}function Ck(F){var C,D,B,G=BT(),E=[];for(C=0;F.length>C;C++){D=F[C],B=D.row,D.element&&(E[B]?E[B].push(D):E[B]=[D])}for(B=0;G>B;B++){E[B]=B6(E[B]||[])}return E}function B6(E){for(var C=[],D=Cb(E),B=0;D.length>B;B++){C.push.apply(C,D[B])}return C}function Cb(E){E.sort(Aj);for(var C=[],D=0;E.length>D;D++){for(var B=E[D],F=0;C.length>F&&An(B,C[F]);F++){}C[F]?C[F].push(B):C[F]=[B]}return C}function Cl(){var D,B=BT(),C=[];for(D=0;B>D;D++){C[D]=BU(D).find("div.fc-day-content > div")}return C}function Cg(D,B){var C=BR();AC(D,function(G,F,E){var I=G.event;I._id===B?Ch(I,F,G):F[0]._fci=E}),Bf(C,D,Ch)}function Ch(D,B,C){BI(D)&&BN.draggableDayEvent(D,B,C),D.allDay&&C.isEnd&&Ca(D)&&BN.resizableDayEvent(D,B,C),B1(D,B)}function BZ(E,C){var D,B,F=y();C.draggable({delay:50,opacity:BW("dragOpacity"),revertDuration:BW("dragRevertDuration"),start:function(I,G){B0("eventDragStart",C[0],E,I,G),BX(E,C),F.start(function(P,L,J,K){if(C.draggable("option","revert",!P||!J&&!K),H(),P){var N=BY(L),O=BY(P);D=O.diff(N,"days"),B=E.start.clone().add("days",D),B5(B,R(E).add("days",D))}else{D=0}},I,"drag")},stop:function(I,G){F.stop(),H(),B0("eventDragStop",C[0],E,I,G),D?BS(C[0],E,B,I,G):(C.css("filter",""),BH(E,C))}})}function B7(C,B,I){var D=BW("isRTL"),E=D?"w":"e",F=B.find(".ui-resizable-"+E),G=!1;AX(B),B.mousedown(function(J){J.preventDefault()}).click(function(J){G&&(J.preventDefault(),J.stopImmediatePropagation())}),F.mousedown(function(P){function J(U){B0("eventResizeStop",B[0],C,U,{}),Bj("body").css("cursor",""),S.stop(),H(),Q&&Ae(B[0],C,T,U,{}),setTimeout(function(){G=!1},0)}if(1==P.which){G=!0;var Q,T,N,S=y(),L=B.css("top"),O=Bj.extend({},C),K=M(BQ(C.start));BL(),Bj("body").css("cursor",E+"-resize").one("mouseup",J),B0("eventResizeStart",B[0],C,P,{}),S.start(function(U,W){if(U){var V=BK(W),X=BK(U);if(X=Math.max(X,K),Q=BP(X)-BP(V),T=R(C).add("days",Q),Q){O.end=T;var Y=N;N=Cd(O,I.row,L),N=Bj(N),N.find("*").css("cursor",E+"-resize"),Y&&Y.remove(),BX(C)}else{N&&(BH(C),N.remove(),N=null)}H(),B5(C.start,T)}},P)}})}var BN=this;BN.renderDayEvents=A,BN.draggableDayEvent=BZ,BN.resizableDayEvent=B7;var BW=BN.opt,B0=BN.trigger,BI=BN.isEventDraggable,Ca=BN.isEventResizable,B2=BN.reportEventElement,B1=BN.eventElementHandlers,BH=BN.showEvents,BX=BN.hideEvents,BS=BN.eventDrop,Ae=BN.eventResize,BT=BN.getRowCnt,BM=BN.getColCnt,BU=BN.allDayRow,t=BN.colLeft,BV=BN.colRight,BJ=BN.colContentLeft,B9=BN.colContentRight,BR=BN.getDaySegmentContainer,B5=BN.renderDayOverlay,H=BN.clearOverlays,BL=BN.clearSelection,y=BN.getHoverListener,BO=BN.rangeToSegments,BY=BN.cellToDate,BK=BN.cellToCellOffset,BP=BN.cellOffsetToDayOffset,BQ=BN.dateToDayOffset,M=BN.dayOffsetToCellOffset,p=BN.calendar,R=p.getEventEnd}function An(D,B){for(var C=0;B.length>C;C++){var A=B[C];if(A.leftCol<=D.rightCol&&A.rightCol>=D.leftCol){return !0}}return !1}function AC(D,B){for(var C=0;D.length>C;C++){var A=D[C],E=A.element;E&&B(A,E,C)}}function Aj(B,A){return A.rightCol-A.leftCol-(B.rightCol-B.leftCol)||A.event.allDay-B.event.allDay||B.event.start-A.event.start||(B.event.title||"").localeCompare(A.event.title)}function As(){function A(O){var P=K("unselectCancel");P&&Bj(O.target).parents(P).length||C(O)}function H(P,O){C(),P=J.moment(P),O=O?J.moment(O):E(P),M(P,O),L(P,O)}function C(O){F&&(F=!1,D(),N("unselect",null,O))}function L(Q,O,P){F=!0,N("select",null,Q,O,P)}function I(P){var Q=B.cellToDate,R=B.getIsCellAllDay,O=B.getHoverListener(),S=B.reportDayClick;if(1==P.which&&K("selectable")){C(P);var T;O.start(function(V,U){D(),V&&R(V)?(T=[Q(U),Q(V)].sort(Bb),M(T[0],T[1].clone().add("days",1))):T=null},P),Bj(document).one("mouseup",function(U){O.stop(),T&&(+T[0]==+T[1]&&S(T[0],U),L(T[0],T[1].clone().add("days",1),U))})}}function G(){Bj(document).off("mousedown",A)}var B=this;B.select=H,B.unselect=C,B.reportSelection=L,B.daySelectionMousedown=I,B.selectionManagerDestroy=G;var J=B.calendar,K=B.opt,N=B.trigger,E=B.defaultSelectionEnd,M=B.renderSelection,D=B.clearSelection,F=!1;K("selectable")&&K("unselectAuto")&&Bj(document).on("mousedown",A)}function BG(){function B(G,H){var F=D.shift();return F||(F=Bj("<div class='fc-cell-overlay' style='position:absolute;z-index:3'/>")),F[0].parentNode!=H[0]&&F.appendTo(H),E.push(F.css(G).show()),F}function C(){for(var F;F=E.shift();){D.push(F.hide().unbind())}}var A=this;A.renderOverlay=B,A.clearOverlays=C;var E=[],D=[]}function Ag(D){var B,C,A=this;A.build=function(){B=[],C=[],D(B,C)},A.cell=function(J,E){var K,G=B.length,F=C.length,H=-1,I=-1;for(K=0;G>K;K++){if(E>=B[K][0]&&B[K][1]>E){H=K;break}}for(K=0;F>K;K++){if(J>=C[K][0]&&C[K][1]>J){I=K;break}}return H>=0&&I>=0?{row:H,col:I}:null},A.rect=function(I,E,J,G,F){var H=F.offset();return{top:B[I][0]-H.top,left:C[E][0]-H.left,width:C[G][1]-C[E][0],height:B[J][1]-B[I][0]}}}function Ak(B){function C(I){BE(I);var H=B.cell(I.pageX,I.pageY);(Boolean(H)!==Boolean(E)||H&&(H.row!=E.row||H.col!=E.col))&&(H?(D||(D=H),G(H,D,H.row-D.row,H.col-D.col)):G(H,D),E=H)}var A,G,D,E,F=this;F.start=function(H,I,J){G=H,D=E=null,B.build(),C(I),A=J||"mousemove",Bj(document).bind(A,C)},F.stop=function(){return Bj(document).unbind(A,C),E}}function BE(A){void 0===A.pageX&&(A.pageX=A.originalEvent.pageX,A.pageY=A.originalEvent.pageY)}function Av(E){function B(G){return A[G]=A[G]||E(G)}var C=this,A={},F={},D={};C.left=function(G){return F[G]=void 0===F[G]?B(G).position().left:F[G]},C.right=function(G){return D[G]=void 0===D[G]?C.left(G)+B(G).width():D[G]},C.clear=function(){A={},F={},D={}}}var Am={lang:"en",defaultTimedEventDuration:"02:00:00",defaultAllDayEventDuration:{days:1},forceEventDuration:!1,nextDayThreshold:"09:00:00",defaultView:"month",aspectRatio:1.35,header:{left:"title",center:"",right:"today prev,next"},weekends:!0,weekNumbers:!1,weekNumberTitle:"W",weekNumberCalculation:"local",lazyFetching:!0,startParam:"start",endParam:"end",timezoneParam:"timezone",timezone:!1,titleFormat:{month:"MMMM YYYY",week:"ll",day:"LL"},columnFormat:{month:"ddd",week:Bd,day:"dddd"},timeFormat:{"default":Bp},displayEventEnd:{month:!1,basicWeek:!1,"default":!0},isRTL:!1,defaultButtonText:{prev:"prev",next:"next",prevYear:"prev year",nextYear:"next year",today:"today",month:"month",week:"week",day:"day"},buttonIcons:{prev:"left-single-arrow",next:"right-single-arrow",prevYear:"left-double-arrow",nextYear:"right-double-arrow"},theme:!1,themeButtonIcons:{prev:"circle-triangle-w",next:"circle-triangle-e",prevYear:"seek-prev",nextYear:"seek-next"},unselectAuto:!0,dropAccept:"*",handleWindowResize:!0,windowResizeDelay:200},Af={en:{columnFormat:{week:"ddd M/D"}}},Al={header:{left:"next,prev today",center:"",right:"title"},buttonIcons:{prev:"right-single-arrow",next:"left-single-arrow",prevYear:"right-double-arrow",nextYear:"left-double-arrow"},themeButtonIcons:{prev:"circle-triangle-e",next:"circle-triangle-w",nextYear:"seek-prev",prevYear:"seek-next"}},BD=Bj.fullCalendar={version:"2.0.2"},BC=BD.views={};Bj.fn.fullCalendar=function(B){var C=Array.prototype.slice.call(arguments,1),A=this;return this.each(function(H,E){var D,F=Bj(E),G=F.data("fullCalendar");"string"==typeof B?G&&Bj.isFunction(G[B])&&(D=G[B].apply(G,C),H||(A=D),"destroy"===B&&F.removeData("fullCalendar")):G||(G=new Be(F,B),F.data("fullCalendar",G),G.render())}),A},BD.langs=Af,BD.datepickerLang=function(B,C,A){var D=Af[B];D||(D=Af[B]={}),Bq(D,{isRTL:A.isRTL,weekNumberTitle:A.weekHeader,titleFormat:{month:A.showMonthAfterYear?"YYYY["+A.yearSuffix+"] MMMM":"MMMM YYYY["+A.yearSuffix+"]"},defaultButtonText:{prev:AM(A.prevText),next:AM(A.nextText),today:AM(A.currentText)}}),Bj.datepicker&&(Bj.datepicker.regional[C]=Bj.datepicker.regional[B]=A,Bj.datepicker.regional.en=Bj.datepicker.regional[""],Bj.datepicker.setDefaults(A))},BD.lang=function(C,A){var B;A&&(B=Af[C],B||(B=Af[C]={}),Bq(B,A||{})),Am.lang=C},BD.sourceNormalizers=[],BD.sourceFetchers=[];var Aa={dataType:"json",cache:!1},Ai=1;BD.applyAll=AZ;var Ah=["sun","mon","tue","wed","thu","fri","sat"],Ab=/^\s*\d{4}-\d\d$/,BB=/^\s*\d{4}-(?:(\d\d-\d\d)|(W\d\d$)|(W\d\d-\d)|(\d\d\d))((T| )(\d\d(:\d\d(:\d\d(\.\d+)?)?)?)?)?$/;BD.moment=function(){return A0(arguments)},BD.moment.utc=function(){var A=A0(arguments,!0);return A.hasTime()&&A.utc(),A},BD.moment.parseZone=function(){return A0(arguments,!0,!0)},AS.prototype=Bk(Bz.fn),AS.prototype.clone=function(){return A0([this])},AS.prototype.time=function(B){if(null==B){return Bz.duration({hours:this.hours(),minutes:this.minutes(),seconds:this.seconds(),milliseconds:this.milliseconds()})}delete this._ambigTime,Bz.isDuration(B)||Bz.isMoment(B)||(B=Bz.duration(B));var A=0;return Bz.isDuration(B)&&(A=24*Math.floor(B.asDays())),this.hours(A+B.hours()).minutes(B.minutes()).seconds(B.seconds()).milliseconds(B.milliseconds())},AS.prototype.stripTime=function(){var A=this.toArray();return Bz.fn.utc.call(this),this.year(A[0]).month(A[1]).date(A[2]).hours(0).minutes(0).seconds(0).milliseconds(0),this._ambigTime=!0,this._ambigZone=!0,this},AS.prototype.hasTime=function(){return !this._ambigTime},AS.prototype.stripZone=function(){var B=this.toArray(),A=this._ambigTime;return Bz.fn.utc.call(this),this.year(B[0]).month(B[1]).date(B[2]).hours(B[3]).minutes(B[4]).seconds(B[5]).milliseconds(B[6]),A&&(this._ambigTime=!0),this._ambigZone=!0,this},AS.prototype.hasZone=function(){return !this._ambigZone},AS.prototype.zone=function(A){return null!=A&&(delete this._ambigTime,delete this._ambigZone),Bz.fn.zone.apply(this,arguments)},AS.prototype.local=function(){var B=this.toArray(),A=this._ambigZone;return delete this._ambigTime,delete this._ambigZone,Bz.fn.local.apply(this,arguments),A&&this.year(B[0]).month(B[1]).date(B[2]).hours(B[3]).minutes(B[4]).seconds(B[5]).milliseconds(B[6]),this},AS.prototype.utc=function(){return delete this._ambigTime,delete this._ambigZone,Bz.fn.utc.apply(this,arguments)},AS.prototype.format=function(){return arguments[0]?A3(this,arguments[0]):this._ambigTime?AJ(this,"YYYY-MM-DD"):this._ambigZone?AJ(this,"YYYY-MM-DD[T]HH:mm:ss"):AJ(this)},AS.prototype.toISOString=function(){return this._ambigTime?AJ(this,"YYYY-MM-DD"):this._ambigZone?AJ(this,"YYYY-MM-DD[T]HH:mm:ss"):Bz.fn.toISOString.apply(this,arguments)},AS.prototype.isWithin=function(C,A){var B=A1([this,C,A]);return B[0]>=B[1]&&B[0]<B[2]},Bj.each(["isBefore","isAfter","isSame"],function(B,A){AS.prototype[A]=function(D,C){var E=A1([this,D]);return Bz.fn[A].call(E[0],E[1],C)}});var Ac={t:function(A){return AJ(A,"a").charAt(0)},T:function(A){return AJ(A,"A").charAt(0)}};BD.formatRange=AY;var BF={Y:"year",M:"month",D:"day",d:"day",A:"second",a:"second",T:"second",t:"second",H:"second",h:"second",m:"second",s:"second"},BA={};BC.month=AU,BC.basicWeek=A7,BC.basicDay=AQ,Bv({weekMode:"fixed"}),BC.agendaWeek=At,BC.agendaDay=AI,Bv({allDaySlot:!0,allDayText:"all-day",scrollTime:"06:00:00",slotDuration:"00:30:00",axisFormat:Ay,timeFormat:{agenda:Ao},dragOpacity:{agenda:0.5},minTime:"00:00:00",maxTime:"24:00:00",slotEventOverlap:!0})});