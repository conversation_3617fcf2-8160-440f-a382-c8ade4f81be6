<section>
    <h1 class="hidden"  data-id="#plugins/bootstrap">Bootstrap</h1>
	<!-- #section:plugins/bootstrap -->
	<div class="help-content">
		<h3 class="info-title smaller">Bootstrap</h3>
		<div class="info-section">
		 <ul class="info-list list-unstyled">
			<li>
				Ace admin template is based on the latest version of Bootstrap
			</li>
			
			<li>
				You may want to have on overview of it
				and keep its documentation at hand.
			</li>
			
			<li>
				Please use the following link for more info:
				<br />
				<a href="http://getbootstrap.com/">getbootstrap.com</a>
			</li>
		 </ul>
		</div>
		
		
        <h3 class="info-title smaller" data-id="#plugins/bootstrap.typeahead">Bootstrap Typeahead</h3>
		<!-- #section:plugins/bootstrap.typeahead -->
		<div class="info-section">
		 <ul class="info-list list-unstyled">
			<li>
				There is also a dropped plugin of Bootstrap 2 which is still available in Ace admin template.
				<br />
				It's <code>typeahead</code> plugin and is used by <a href="#plugins/input.tag-input" class="help-more">Tag input plugin</a> and 
				search box demo.
				<br />
				For more info about it, please see:
				<br />
				<a href="http://getbootstrap.com/2.3.2/javascript.html#typeahead">http://getbootstrap.com/2.3.2/javascript.html#typeahead</a>
			</li>
			
			<li>
				To avoid conflicts with the new Typeahead.js plugin, its been renamed to <code>bs_typeahead</code>.
				<br />
				Of course the plugins have methods to resolve conflicts by 
				calling <code>$.fn.typeahead.noConflict();</code>
				but I found it easier to rename it to something else especially when using
				both plugins inside an ajax page and coming back and forth to that page multiple times!
			</li>
		 </ul>
		</div>
		<!-- /section:plugins/bootstrap.typeahead -->
		
		<h3 class="info-title smaller" data-id="#plugins/bootstrap.typeahead-js">Typeahead.js</h3>
		<!-- #section:plugins/bootstrap.typeahead-js -->
		<div class="info-section">
		 <ul class="info-list list-unstyled">	
			<li>
				It's the updated version of Bootstrap Typeahead with more features.
				<br />
				For more info and documentation about it, please see its page at:
				<br />
				<a href="https://twitter.github.io/typeahead.js/">https://twitter.github.io/typeahead.js/</a>
			</li>
			<li>
				To use it, you should include:
				<br />
				<code>assets/js/typeahead.jquery.min.js</code> 
			</li>
		 </ul>
		</div>
		<!-- /section:plugins/bootstrap.typeahead-js -->
		
	</div>
	<!-- /section:plugins/bootstrap -->
</section>