<section>
	<h1 class="blue" data-id="#custom/inline-editable"><i class="ace-icon fa fa-desktop grey"></i> Inline Editable Addons</h1>

	<div class="hr hr-double hr32"></div>


	<!-- #section:custom/inline-editable -->
	<div class="help-content">
		<h3 class="info-title smaller">Overview</h3>
		<div class="info-section">
		 <ul class="info-list list-unstyled">
			<li>
				Based on <a href="#plugins/misc.inline-editable">Inline editable</a> plugin,
				five additional editables have been defined which you can use by including
				the following scripts:
<pre data-language="html">
 &lt;script src="assets/js/x-editable/bootstrap-editable.min.js" /&gt;
 &lt;script src="assets/js/x-editable/ace-editable.min.js" /&gt;
</pre>
			</li>
 		 </ul>
		</div>
		
		
		<h3 class="info-title smaller" data-id="#custom/inline-editable.image">Image editable</h3>
		<!-- #section:custom/inline-editable.image -->
		<div class="info-section">
		 <ul class="info-list list-unstyled">
			<li>
				Image editable is based on <a href="#custom/file-input" class="help-more">custom file input</a>
			</li>
		 
			<li>
				You should do some extra work for uploading the image.
				<br />
				A working example is available at <code>examples/profile-update.html</code>.
				<br />
				The backend (server side) code is written in PHP at <code>examples/file-upload.php</code>.
			</li>
			
			<li>
				Inside <code>image</code> parapmeter which takes custom file input options,
				there is also <code>on_error</code> and <code>on_success</code> callbacks that are called
				when an invalid file is selected or if files are successfully selected.
			</li>
			
			<li>
				An example of image editable's usage:
<pre data-language="javascript">
//first let's add a fake appendChild for Image element for browsers that have a problem with this
//because it seems that editable plugin calls appendChild, and it causes errors on IE at unpredicted points
try {
   document.createElement('IMG').appendChild(document.createElement('B'));
} catch(e) {
   Image.prototype.appendChild = function(el){}
}

$('#avatar').editable({
  type: 'image',
  name: 'avatar',
  value: null,
  image: {
    name: 'avatar',//name should be here as well
	
    //custom file input options
    btn_choose: 'Change Avatar',
    droppable: true,
    maxSize: 110000,//~100kb
	
    //inline editable callback option
    on_error : function(error_type) {
	  //invalid file selected, for example display an error message
      if(error_type == 1) {
        //file format error
      } else if(error_type == 2) {
        //file size rror
      }
      else {
        //other error
      }
    },
    on_success : function() {
      //valid file selected, for example remove error messages
    }
  },
  url: function(params) {
    //actual file upload happens here
    //see "examples/profile-avatar-update.js"
  }
});
</pre>
			</li>
 		 </ul>
		</div>
		<!-- /section:custom/inline-editable.image -->

		
		<h3 class="info-title smaller" data-id="#custom/inline-editable.slider">Slider</h3>
		<!-- #section:custom/inline-editable.slider -->
		<div class="info-section">
		 <ul class="info-list list-unstyled">
			<li>
				For this addon 
				<a href="#plugins/jquery.slider" class="help-more">jQuery UI</a>
				is required and should be included first
			</li>
			<li>
<pre data-language="javascript">
$('#element').editable({
   type : 'slider',
   name : 'element-name',

   //jQuery UI slider options
   slider : {
       min: 1,
       max: 50,

	 //and slider's width (default is 200)
	 width: 100
	 
	 //,nativeUI: true //this will use browser's built-in range control if available
   },
   
   success: function(response, newValue) {
      alert(parseInt(newValue));
   }
});
</pre>
			</li>
			
			<li>
				Using <code>nativeUI</code> option will switch to browser's built-in range control if available
			</li>
 		 </ul>
		</div>
		<!-- /section:custom/inline-editable.slider -->
		
		
		<h3 class="info-title smaller" data-id="#custom/inline-editable.spinner">Spinner</h3>
		<!-- #section:custom/inline-editable.spinner -->
		<div class="info-section">
		 <ul class="info-list list-unstyled">
			<li>
				For this addon,
				<a href="#plugins/fuelux.spinner" class="help-more">FuelUX spinner</a>
				is required and should be included first
			</li>
			<li>
<pre data-language="javascript">
$('#element').editable({
   type: 'spinner',
   name: 'element-name',
   
   //spinner options
   spinner : {
      min: 16,
      max: 99,
     step: 1
	 
	 //,nativeUI: true //this will use browser's built-in number input if available
   }
});
</pre>
			</li>
			
			<li>
				Using <code>nativeUI</code> option will switch to browser's built-in number input if available
			</li>
 		 </ul>
		</div>
		<!-- /section:custom/inline-editable.spinner -->
		
		
		
		<h3 class="info-title smaller" data-id="#custom/inline-editable.wysiwyg">Wysiwyg</h3>
		<!-- #section:custom/inline-editable.wysiwyg -->
		<div class="info-section">
		 <ul class="info-list list-unstyled">
			<li>
				For this addon <a href="#plugins/editor.wysiwyg" class="help-more">Wysiwyg plugin</a>
				is required and should be included first
			</li>
			<li>
<pre data-language="javascript">
$('#element').editable({
   mode: 'inline',
   type: 'wysiwyg',
   name: 'element-name',

  //wysiwyg plugin options, such as buttons, etc
  wysiwyg : {
  
    //optional max-width
    //css : {'max-width':'300px'}
  },
  success: function(response, newValue) {
  }
});
</pre>
			</li>
 		 </ul>
		</div>
		<!-- /section:custom/inline-editable.wysiwyg -->
		
		
		
		
		<h3 class="info-title smaller" data-id="#custom/inline-editable.date">Date</h3>
		<!-- #section:custom/inline-editable.date -->
		<div class="info-section">
		 <ul class="info-list list-unstyled">
			<li>
				Latest version of inline editable plugin does not support date picker
			</li>
			<li>
				I added a custom one which requires
				<a href="#plugins/date-time.datepicker" class="help-more">date picker</a>
				and it should be included first.
				<br />
				The type name is <code>adate</code>
			</li>
			<li>
<pre data-language="javascript">
$('#element').editable({
  type: 'adate',
  
  //datepicker plugin options
  date: {
        format: 'dd/mm/yyyy',
    viewformat: 'dd/mm/yyyy',
     weekStart: 1
	 
     //,nativeUI: true //this will use browser's built-in date picker if available
  }
})
</pre>
			</li>
			
			<li>
				Using <code>nativeUI</code> option will switch to browser's built-in date picker if available
			</li>
 		 </ul>
		</div>
		<!-- /section:custom/inline-editable.date -->
		

	</div>
	<!-- /section:custom/inline-editable -->
	
</section>