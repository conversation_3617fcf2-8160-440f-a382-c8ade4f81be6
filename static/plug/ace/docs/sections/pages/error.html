<section>
	<div class="help-content">
		<h3 class="info-title smaller" data-id="#pages/error">Error Page</h3>
		<!-- #section:pages/error -->
		<div class="info-section">
		 <ul class="info-list list-unstyled">
			<li>			
				Error page doesn't have any special elements
			</li>
			<li>
				There is only <code>.error-container</code> which adds some margin:
<pre data-language="html">
<div class="error-container">
 <!-- error content, for example a .well -->
</div>
</pre>
			</li>
		 </ul>
		</div>
		<!-- /section:pages/error -->
	</div>	
</section>