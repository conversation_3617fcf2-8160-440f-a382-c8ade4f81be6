KindEditor.plugin("autoheight",function(A){var B=this;if(!B.autoHeightMode){return}var E;function D(){var G=B.edit;var H=G.doc.body;G.iframe[0].scroll="no";H.style.overflowY="hidden"}function C(){var G=B.edit;var H=G.doc.body;G.iframe.height(E);<PERSON><PERSON>resize(null,Math.max((A.IE?H.scrollHeight:H.offsetHeight)+76,E))}function F(){E=A.removeUnit(B.height);B.edit.afterChange(C);D();C()}if(B.isCreated){F()}else{B.afterCreate(F)}});