KindEditor.plugin("media",function(A){var J=this,B="media",E=<PERSON><PERSON>lang(B+"."),I=<PERSON>.undef(J.allowMediaUpload,true),D=A.undef(J.allowFileManager,false),C=A.undef(J.formatUploadUrl,true),G=A.undef(J.extraFileUploadParams,{}),H=A.undef(J.filePostName,"imgFile"),F=A.undef(J.upload<PERSON>son,J.basePath+"php/upload_json.php");J.plugin.media={edit:function(){var Q=['<div style="padding:20px;">','<div class="ke-dialog-row">','<label for="keUrl" style="width:60px;">'+E.url+"</label>",'<input class="ke-input-text" type="text" id="keUrl" name="url" value="" style="width:160px;" /> &nbsp;','<input type="button" class="ke-upload-button" value="'+E.upload+'" /> &nbsp;','<span class="ke-button-common ke-button-outer">','<input type="button" class="ke-button-common ke-button" name="viewServer" value="'+E.viewServer+'" />',"</span>","</div>",'<div class="ke-dialog-row">','<label for="keWidth" style="width:60px;">'+E.width+"</label>",'<input type="text" id="keWidth" class="ke-input-text ke-input-number" name="width" value="550" maxlength="4" />',"</div>",'<div class="ke-dialog-row">','<label for="keHeight" style="width:60px;">'+E.height+"</label>",'<input type="text" id="keHeight" class="ke-input-text ke-input-number" name="height" value="400" maxlength="4" />',"</div>",'<div class="ke-dialog-row">','<label for="keAutostart">'+E.autostart+"</label>",'<input type="checkbox" id="keAutostart" name="autostart" value="" /> ',"</div>","</div>"].join("");var P=J.createDialog({name:B,width:450,height:230,title:J.lang(B),body:Q,yesBtn:{name:J.lang("yes"),click:function(X){var Z=A.trim(N.val()),Y=R.val(),W=S.val();if(Z=="http://"||A.invalidUrl(Z)){alert(J.lang("invalidUrl"));N[0].focus();return}if(!/^\d*$/.test(Y)){alert(J.lang("invalidWidth"));R[0].focus();return}if(!/^\d*$/.test(W)){alert(J.lang("invalidHeight"));S[0].focus();return}var V=A.mediaImg(J.themesPath+"common/blank.gif",{src:Z,type:A.mediaType(Z),width:Y,height:W,autostart:T[0].checked?"true":"false",loop:"true"});J.insertHtml(V).hideDialog().focus()}}}),U=P.div,N=A('[name="url"]',U),O=A('[name="viewServer"]',U),R=A('[name="width"]',U),S=A('[name="height"]',U),T=A('[name="autostart"]',U);N.val("http://");if(I){var K=A.uploadbutton({button:A(".ke-upload-button",U)[0],fieldName:H,extraParams:G,url:A.addParam(F,"dir=media"),afterUpload:function(W){P.hideLoading();if(W.error===0){var V=W.url;if(C){V=A.formatUrl(V,"absolute")}N.val(V);if(J.afterUpload){J.afterUpload.call(J,V,W,B)}alert(J.lang("uploadSuccess"))}else{alert(W.message)}},afterError:function(V){P.hideLoading();J.errorDialog(V)}});K.fileBox.change(function(V){P.showLoading(J.lang("uploadLoading"));K.submit()})}else{A(".ke-upload-button",U).hide()}if(D){O.click(function(V){J.loadPlugin("filemanager",function(){J.plugin.filemanagerDialog({viewType:"LIST",dirName:"media",clickFn:function(W,X){if(J.dialogs.length>1){A('[name="url"]',U).val(W);if(J.afterSelectFile){J.afterSelectFile.call(J,W)}J.hideDialog()}}})})})}else{O.hide()}var M=J.plugin.getSelectedMedia();if(M){var L=A.mediaAttrs(M.attr("data-ke-tag"));N.val(L.src);R.val(A.removeUnit(M.css("width"))||L.width||0);S.val(A.removeUnit(M.css("height"))||L.height||0);T[0].checked=(L.autostart==="true")}N[0].focus();N[0].select()},"delete":function(){J.plugin.getSelectedMedia().remove();J.addBookmark()}};J.clickToolbar(B,J.plugin.media.edit)});