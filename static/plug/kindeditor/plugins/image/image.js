KindEditor.plugin("image",function(A){var N=this,C="image",J=A.undef(N.allowImageUpload,true),B=A.undef(N.allowImageRemote,true),D=A.undef(N.formatUploadUrl,true),E=A.undef(N.allowFileManager,false),H=A.undef(N.uploadJson,N.basePath+"php/upload_json.php"),F=A.undef(N.imageTabIndex,0),L=N.pluginsPath+"image/images/",I=A.undef(N.extraFileUploadParams,{}),G=A.undef(N.filePostName,"imgFile"),M=A.undef(N.fillDescAfterUploadImage,false),O=N.lang(C+".");N.plugin.imageDialog=function(m){var X=m.imageUrl,Z=A.undef(m.imageWidth,""),c=A.undef(m.imageHeight,""),Y=A.undef(m.imageTitle,""),t=A.undef(m.imageAlign,""),i=A.undef(m.showRemote,true),g=A.undef(m.showLocal,true),n=A.undef(m.tabIndex,0),R=m.clickFn;var o="kindeditor_upload_iframe_"+new Date().getTime();var p=[];for(var S in I){p.push('<input type="hidden" name="'+S+'" value="'+I[S]+'" />')}var d=['<div style="padding:20px;">','<div class="tabs"></div>','<div class="tab1" style="display:none;">','<div class="ke-dialog-row">','<label for="remoteUrl" style="width:60px;">'+O.remoteUrl+"</label>",'<input type="text" id="remoteUrl" class="ke-input-text" name="url" value="" style="width:200px;" /> &nbsp;','<span class="ke-button-common ke-button-outer">','<input type="button" class="ke-button-common ke-button" name="viewServer" value="'+O.viewServer+'" />',"</span>","</div>",'<div class="ke-dialog-row">','<label for="remoteWidth" style="width:60px;">'+O.size+"</label>",O.width+' <input type="text" id="remoteWidth" class="ke-input-text ke-input-number" name="width" value="" maxlength="4" /> ',O.height+' <input type="text" class="ke-input-text ke-input-number" name="height" value="" maxlength="4" /> ','<img class="ke-refresh-btn" src="'+L+'refresh.png" width="16" height="16" alt="" style="cursor:pointer;" title="'+O.resetSize+'" />',"</div>",'<div class="ke-dialog-row">','<label style="width:60px;">'+O.align+"</label>",'<input type="radio" name="align" class="ke-inline-block" value="" checked="checked" /> <img name="defaultImg" src="'+L+'align_top.gif" width="23" height="25" alt="" />',' <input type="radio" name="align" class="ke-inline-block" value="left" /> <img name="leftImg" src="'+L+'align_left.gif" width="23" height="25" alt="" />',' <input type="radio" name="align" class="ke-inline-block" value="right" /> <img name="rightImg" src="'+L+'align_right.gif" width="23" height="25" alt="" />',"</div>",'<div class="ke-dialog-row">','<label for="remoteTitle" style="width:60px;">'+O.imgTitle+"</label>",'<input type="text" id="remoteTitle" class="ke-input-text" name="title" value="" style="width:200px;" />',"</div>","</div>",'<div class="tab2" style="display:none;">','<iframe name="'+o+'" style="display:none;"></iframe>','<form class="ke-upload-area ke-form" method="post" enctype="multipart/form-data" target="'+o+'" action="'+A.addParam(H,"dir=image")+'">','<div class="ke-dialog-row">',p.join(""),'<label style="width:60px;">'+O.localUrl+"</label>",'<input type="text" name="localUrl" class="ke-input-text" tabindex="-1" style="width:200px;" readonly="true" /> &nbsp;','<input type="button" class="ke-upload-button" value="'+O.upload+'" />',"</div>","</form>","</div>","</div>"].join("");var f=g||E?450:400,W=g&&i?300:250;var U=N.createDialog({name:C,width:f,height:W,title:N.lang(C),body:d,yesBtn:{name:N.lang("yes"),click:function(u){if(U.isLoading){return}if(g&&i&&V&&V.selectedIndex===1||!i){if(r.fileBox.val()==""){alert(N.lang("pleaseSelectFile"));return}U.showLoading(N.lang("uploadLoading"));r.submit();j.val("");return}var y=A.trim(Q.val()),x=q.val(),k=K.val(),w=s.val(),v="";e.each(function(){if(this.checked){v=this.value;return false}});if(y=="http://"||A.invalidUrl(y)){alert(N.lang("invalidUrl"));Q[0].focus();return}if(!/^\d*$/.test(x)){alert(N.lang("invalidWidth"));q[0].focus();return}if(!/^\d*$/.test(k)){alert(N.lang("invalidHeight"));K[0].focus();return}R.call(N,y,w,x,k,0,v)}},beforeRemove:function(){b.unbind();q.unbind();K.unbind();P.unbind()}}),T=U.div;var Q=A('[name="url"]',T),j=A('[name="localUrl"]',T),b=A('[name="viewServer"]',T),q=A('.tab1 [name="width"]',T),K=A('.tab1 [name="height"]',T),P=A(".ke-refresh-btn",T),s=A('.tab1 [name="title"]',T),e=A('.tab1 [name="align"]',T);var V;if(i&&g){V=A.tabs({src:A(".tabs",T),afterSelect:function(k){}});V.add({title:O.remoteImage,panel:A(".tab1",T)});V.add({title:O.localImage,panel:A(".tab2",T)});V.select(n)}else{if(i){A(".tab1",T).show()}else{if(g){A(".tab2",T).show()}}}var r=A.uploadbutton({button:A(".ke-upload-button",T)[0],fieldName:G,form:A(".ke-form",T),target:o,width:60,afterUpload:function(u){U.hideLoading();if(u.error===0){var k=u.url;if(D){k=A.formatUrl(k,"absolute")}if(N.afterUpload){N.afterUpload.call(N,k,u,C)}if(!M){R.call(N,k,u.title,u.width,u.height,u.border,u.align)}else{A(".ke-dialog-row #remoteUrl",T).val(k);A(".ke-tabs-li",T)[0].click();A(".ke-refresh-btn",T).click()}}else{alert(u.message)}},afterError:function(k){U.hideLoading();N.errorDialog(k)}});r.fileBox.change(function(k){j.val(r.fileBox.val())});if(E){b.click(function(k){N.loadPlugin("filemanager",function(){N.plugin.filemanagerDialog({viewType:"VIEW",dirName:"image",clickFn:function(u,v){if(N.dialogs.length>1){A('[name="url"]',T).val(u);if(N.afterSelectFile){N.afterSelectFile.call(N,u)}N.hideDialog()}}})})})}else{b.hide()}var a=0,h=0;function l(u,k){q.val(u);K.val(k);a=u;h=k}P.click(function(u){var k=A('<img src="'+Q.val()+'" />',document).css({position:"absolute",visibility:"hidden",top:0,left:"-1000px"});k.bind("load",function(){l(k.width(),k.height());k.remove()});A(document.body).append(k)});q.change(function(k){if(a>0){K.val(Math.round(h/a*parseInt(this.value,10)))}});K.change(function(k){if(h>0){q.val(Math.round(a/h*parseInt(this.value,10)))}});Q.val(m.imageUrl);l(m.imageWidth,m.imageHeight);s.val(m.imageTitle);e.each(function(){if(this.value===m.imageAlign){this.checked=true;return false}});if(i&&n===0){Q[0].focus();Q[0].select()}return U};N.plugin.image={edit:function(){var K=N.plugin.getSelectedImage();N.plugin.imageDialog({imageUrl:K?K.attr("data-ke-src"):"http://",imageWidth:K?K.width():"",imageHeight:K?K.height():"",imageTitle:K?K.attr("title"):"",imageAlign:K?K.attr("align"):"",showRemote:B,showLocal:J,tabIndex:K?0:F,clickFn:function(R,T,U,Q,P,S){if(K){K.attr("src",R);K.attr("data-ke-src",R);K.attr("width",U);K.attr("height",Q);K.attr("title",T);K.attr("align",S);K.attr("alt",T)}else{N.exec("insertimage",R,T,U,Q,P,S)}setTimeout(function(){N.hideDialog().focus()},0)}})},"delete":function(){var K=N.plugin.getSelectedImage();if(K.parent().name=="a"){K=K.parent()}K.remove();N.addBookmark()}};N.clickToolbar(C,N.plugin.image.edit)});