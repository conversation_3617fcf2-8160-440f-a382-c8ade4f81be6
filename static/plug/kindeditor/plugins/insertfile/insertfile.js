KindEditor.plugin("insertfile",function(A){var H=this,B="insertfile",I=A.undef(H.allowFileUpload,true),D=A.undef(H.allowFileManager,false),C=A.undef(H.formatUploadUrl,true),F=A.undef(<PERSON><PERSON>upload<PERSON><PERSON>,H.basePath+"php/upload_json.php"),G=A.undef(H.extraFileUploadParams,{}),E=A.undef(H.filePostName,"imgFile"),J=<PERSON>.lang(B+".");H.plugin.fileDialog=function(Q){var U=A.undef(Q.fileUrl,"http://"),K=A.undef(Q.fileTitle,""),R=Q.clickFn;var T=['<div style="padding:20px;">','<div class="ke-dialog-row">','<label for="keUrl" style="width:60px;">'+J.url+"</label>",'<input type="text" id="keUrl" name="url" class="ke-input-text" style="width:160px;" /> &nbsp;','<input type="button" class="ke-upload-button" value="'+J.upload+'" /> &nbsp;','<span class="ke-button-common ke-button-outer">','<input type="button" class="ke-button-common ke-button" name="viewServer" value="'+J.viewServer+'" />',"</span>","</div>",'<div class="ke-dialog-row">','<label for="keTitle" style="width:60px;">'+J.title+"</label>",'<input type="text" id="keTitle" class="ke-input-text" name="title" value="" style="width:160px;" /></div>',"</div>","</form>","</div>"].join("");var O=H.createDialog({name:B,width:450,title:H.lang(B),body:T,yesBtn:{name:H.lang("yes"),click:function(V){var X=A.trim(S.val()),W=L.val();if(X=="http://"||A.invalidUrl(X)){alert(H.lang("invalidUrl"));S[0].focus();return}if(A.trim(W)===""){W=X}R.call(H,X,W)}}}),M=O.div;var S=A('[name="url"]',M),N=A('[name="viewServer"]',M),L=A('[name="title"]',M);if(I){var P=A.uploadbutton({button:A(".ke-upload-button",M)[0],fieldName:E,url:A.addParam(F,"dir=file"),extraParams:G,afterUpload:function(W){O.hideLoading();if(W.error===0){var V=W.url;if(C){V=A.formatUrl(V,"absolute")}S.val(V);if(H.afterUpload){H.afterUpload.call(H,V,W,B)}alert(H.lang("uploadSuccess"))}else{alert(W.message)}},afterError:function(V){O.hideLoading();H.errorDialog(V)}});P.fileBox.change(function(V){O.showLoading(H.lang("uploadLoading"));P.submit()})}else{A(".ke-upload-button",M).hide()}if(D){N.click(function(V){H.loadPlugin("filemanager",function(){H.plugin.filemanagerDialog({viewType:"LIST",dirName:"file",clickFn:function(W,X){if(H.dialogs.length>1){A('[name="url"]',M).val(W);if(H.afterSelectFile){H.afterSelectFile.call(H,W)}H.hideDialog()}}})})})}else{N.hide()}S.val(U);L.val(K);S[0].focus();S[0].select()};H.clickToolbar(B,function(){H.plugin.fileDialog({clickFn:function(L,M){var K='<a class="ke-insertfile" href="'+L+'" data-ke-src="'+L+'" target="_blank">'+M+"</a>";H.insertHtml(K).hideDialog().focus()}})})});