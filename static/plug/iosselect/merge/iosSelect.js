(function(){var A=(function(){var D=window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.oRequestAnimationFrame||window.msRequestAnimationFrame||function(I){window.setTimeout(I,1000/60)};var E=(function(){var L={};var K=document.createElement("div").style;var I=(function(){var Q=["t","webkitT","MozT","msT","OT"],R,O=0,P=Q.length;for(;O<P;O++){R=Q[O]+"ransform";if(R in K){return Q[O].substr(0,Q[O].length-1)}}return false})();function N(O){if(I===false){return false}if(I===""){return O}return I+O.charAt(0).toUpperCase()+O.substr(1)}L.getTime=Date.now||function J(){return new Date().getTime()};L.extend=function(Q,P){for(var O in P){Q[O]=P[O]}};L.addEvent=function(O,R,Q,P){O.addEventListener(R,Q,!!P)};L.removeEvent=function(O,R,Q,P){O.removeEventListener(R,Q,!!P)};L.prefixPointerEvent=function(O){return window.MSPointerEvent?"MSPointer"+O.charAt(9).toUpperCase()+O.substr(10):O};L.momentum=function(R,Q,P,T,X,W){var U=R-Q,S=Math.abs(U)/P,V,O;W=W===undefined?0.0006:W;V=R+(S*S)/(2*W)*(U<0?-1:1);O=S/W;if(V<T){V=X?T-(X/2.5*(S/8)):T;U=Math.abs(V-R);O=U/S}else{if(V>0){V=X?X/2.5*(S/8):0;U=Math.abs(R)+V;O=U/S}}return{destination:Math.round(V),duration:O}};var M=N("transform");L.extend(L,{hasTransform:M!==false,hasPerspective:N("perspective") in K,hasTouch:"ontouchstart" in window,hasPointer:window.PointerEvent||window.MSPointerEvent,hasTransition:N("transition") in K});L.isBadAndroid=/Android /.test(window.navigator.appVersion)&&!(/Chrome\/\d/.test(window.navigator.appVersion));L.extend(L.style={},{transform:M,transitionTimingFunction:N("transitionTimingFunction"),transitionDuration:N("transitionDuration"),transitionDelay:N("transitionDelay"),transformOrigin:N("transformOrigin")});L.hasClass=function(P,Q){var O=new RegExp("(^|\\s)"+Q+"(\\s|$)");return O.test(P.className)};L.addClass=function(O,P){if(L.hasClass(O,P)){return}var Q=O.className.split(" ");Q.push(P);O.className=Q.join(" ")};L.removeClass=function(P,Q){if(!L.hasClass(P,Q)){return}var O=new RegExp("(^|\\s)"+Q+"(\\s|$)","g");P.className=P.className.replace(O," ")};L.offset=function(O){var P=-O.offsetLeft,Q=-O.offsetTop;while(O=O.offsetParent){P-=O.offsetLeft;Q-=O.offsetTop}return{left:P,top:Q}};L.preventDefaultException=function(O,Q){for(var P in Q){if(Q[P].test(O[P])){return true}}return false};L.extend(L.eventType={},{touchstart:1,touchmove:1,touchend:1,mousedown:2,mousemove:2,mouseup:2,pointerdown:3,pointermove:3,pointerup:3,MSPointerDown:3,MSPointerMove:3,MSPointerUp:3});L.extend(L.ease={},{quadratic:{style:"cubic-bezier(0.25, 0.46, 0.45, 0.94)",fn:function(O){return O*(2-O)}},circular:{style:"cubic-bezier(0.1, 0.57, 0.1, 1)",fn:function(O){return Math.sqrt(1-(--O*O))}},back:{style:"cubic-bezier(0.175, 0.885, 0.32, 1.275)",fn:function(P){var O=4;return(P=P-1)*P*((O+1)*P+O)+1}},bounce:{style:"",fn:function(O){if((O/=1)<(1/2.75)){return 7.5625*O*O}else{if(O<(2/2.75)){return 7.5625*(O-=(1.5/2.75))*O+0.75}else{if(O<(2.5/2.75)){return 7.5625*(O-=(2.25/2.75))*O+0.9375}else{return 7.5625*(O-=(2.625/2.75))*O+0.984375}}}}},elastic:{style:"",fn:function(Q){var O=0.22,P=0.4;if(Q===0){return 0}if(Q==1){return 1}return(P*Math.pow(2,-10*Q)*Math.sin((Q-O/4)*(2*Math.PI)/O)+1)}}});L.tap=function(O,P){var Q=document.createEvent("Event");Q.initEvent(P,true,true);Q.pageX=O.pageX;Q.pageY=O.pageY;O.target.dispatchEvent(Q)};L.click=function(O){var P=O.target,Q;if(!(/(SELECT|INPUT|TEXTAREA)/i).test(P.tagName)){Q=document.createEvent("MouseEvents");Q.initMouseEvent("click",true,true,O.view,1,P.screenX,P.screenY,P.clientX,P.clientY,O.ctrlKey,O.altKey,O.shiftKey,O.metaKey,0,null);Q._constructed=true;P.dispatchEvent(Q)}};return L})();function G(I,K){this.wrapper=typeof I=="string"?document.querySelector(I):I;this.scroller=this.wrapper.children[0];this.scrollerStyle=this.scroller.style;this.options={resizeScrollbars:true,mouseWheelSpeed:20,snapThreshold:0.334,startX:0,startY:0,scrollY:true,directionLockThreshold:5,momentum:true,bounce:true,bounceTime:600,bounceEasing:"",preventDefault:true,preventDefaultException:{tagName:/^(INPUT|TEXTAREA|BUTTON|SELECT)$/},HWCompositing:true,useTransition:true,useTransform:true};for(var J in K){this.options[J]=K[J]}this.translateZ=this.options.HWCompositing&&E.hasPerspective?" translateZ(0)":"";this.options.useTransition=E.hasTransition&&this.options.useTransition;this.options.useTransform=E.hasTransform&&this.options.useTransform;this.options.eventPassthrough=this.options.eventPassthrough===true?"vertical":this.options.eventPassthrough;this.options.preventDefault=!this.options.eventPassthrough&&this.options.preventDefault;this.options.scrollY=this.options.eventPassthrough=="vertical"?false:this.options.scrollY;this.options.scrollX=this.options.eventPassthrough=="horizontal"?false:this.options.scrollX;this.options.freeScroll=this.options.freeScroll&&!this.options.eventPassthrough;this.options.directionLockThreshold=this.options.eventPassthrough?0:this.options.directionLockThreshold;this.options.bounceEasing=typeof this.options.bounceEasing=="string"?E.ease[this.options.bounceEasing]||E.ease.circular:this.options.bounceEasing;this.options.resizePolling=this.options.resizePolling===undefined?60:this.options.resizePolling;if(this.options.tap===true){this.options.tap="tap"}if(this.options.shrinkScrollbars=="scale"){this.options.useTransition=false}this.options.invertWheelDirection=this.options.invertWheelDirection?-1:1;if(this.options.probeType==3){this.options.useTransition=false}this.x=0;this.y=0;this.directionX=0;this.directionY=0;this._events={};this._init();this.refresh();this.scrollTo(this.options.startX,this.options.startY);this.enable()}G.prototype={version:"5.1.3",_init:function(){this._initEvents();if(this.options.scrollbars||this.options.indicators){this._initIndicators()}if(this.options.mouseWheel){this._initWheel()}if(this.options.snap){this._initSnap()}if(this.options.keyBindings){this._initKeys()}},destroy:function(){this._initEvents(true);this._execEvent("destroy")},_transitionEnd:function(I){if(I.target!=this.scroller||!this.isInTransition){return}this._transitionTime();if(!this.resetPosition(this.options.bounceTime)){this.isInTransition=false;this._execEvent("scrollEnd")}},_start:function(I){if(E.eventType[I.type]!=1){if(I.button!==0){return}}if(!this.enabled||(this.initiated&&E.eventType[I.type]!==this.initiated)){return}if(this.options.preventDefault&&!E.isBadAndroid&&!E.preventDefaultException(I.target,this.options.preventDefaultException)){I.preventDefault()}var J=I.touches?I.touches[0]:I,K;this.initiated=E.eventType[I.type];this.moved=false;this.distX=0;this.distY=0;this.directionX=0;this.directionY=0;this.directionLocked=0;this._transitionTime();this.startTime=E.getTime();if(this.options.useTransition&&this.isInTransition){this.isInTransition=false;K=this.getComputedPosition();this._translate(Math.round(K.x),Math.round(K.y));this._execEvent("scrollEnd")}else{if(!this.options.useTransition&&this.isAnimating){this.isAnimating=false;this._execEvent("scrollEnd")}}this.startX=this.x;this.startY=this.y;this.absStartX=this.x;this.absStartY=this.y;this.pointX=J.pageX;this.pointY=J.pageY;this._execEvent("beforeScrollStart")},_move:function(I){if(!this.enabled||E.eventType[I.type]!==this.initiated){return}if(this.options.preventDefault){I.preventDefault()}var K=I.touches?I.touches[0]:I,P=K.pageX-this.pointX,J=K.pageY-this.pointY,O=E.getTime(),Q,N,M,L;this.pointX=K.pageX;this.pointY=K.pageY;this.distX+=P;this.distY+=J;M=Math.abs(this.distX);L=Math.abs(this.distY);if(O-this.endTime>300&&(M<10&&L<10)){return}if(!this.directionLocked&&!this.options.freeScroll){if(M>L+this.options.directionLockThreshold){this.directionLocked="h"}else{if(L>=M+this.options.directionLockThreshold){this.directionLocked="v"}else{this.directionLocked="n"}}}if(this.directionLocked=="h"){if(this.options.eventPassthrough=="vertical"){I.preventDefault()}else{if(this.options.eventPassthrough=="horizontal"){this.initiated=false;return}}J=0}else{if(this.directionLocked=="v"){if(this.options.eventPassthrough=="horizontal"){I.preventDefault()}else{if(this.options.eventPassthrough=="vertical"){this.initiated=false;return}}P=0}}P=this.hasHorizontalScroll?P:0;J=this.hasVerticalScroll?J:0;Q=this.x+P;N=this.y+J;if(Q>0||Q<this.maxScrollX){Q=this.options.bounce?this.x+P/3:Q>0?0:this.maxScrollX}if(N>0||N<this.maxScrollY){N=this.options.bounce?this.y+J/3:N>0?0:this.maxScrollY}this.directionX=P>0?-1:P<0?1:0;this.directionY=J>0?-1:J<0?1:0;if(!this.moved){this._execEvent("scrollStart")}this.moved=true;this._translate(Q,N);if(O-this.startTime>300){this.startTime=O;this.startX=this.x;this.startY=this.y;if(this.options.probeType==1){this._execEvent("scroll")}}if(this.options.probeType>1){this._execEvent("scroll")}},_end:function(M){if(!this.enabled||E.eventType[M.type]!==this.initiated){return}if(this.options.preventDefault&&!E.preventDefaultException(M.target,this.options.preventDefaultException)){M.preventDefault()}var O=M.changedTouches?M.changedTouches[0]:M,I,R,P=E.getTime()-this.startTime,T=Math.round(this.x),S=Math.round(this.y),N=Math.abs(T-this.startX),L=Math.abs(S-this.startY),J=0,Q="";this.isInTransition=0;this.initiated=0;this.endTime=E.getTime();if(this.resetPosition(this.options.bounceTime)){return}this.scrollTo(T,S);if(!this.moved){if(this.options.tap){E.tap(M,this.options.tap)}if(this.options.click){E.click(M)}this._execEvent("scrollCancel");return}if(this._events.flick&&P<200&&N<100&&L<100){this._execEvent("flick");return}if(this.options.momentum&&P<300){I=this.hasHorizontalScroll?E.momentum(this.x,this.startX,P,this.maxScrollX,this.options.bounce?this.wrapperWidth:0,this.options.deceleration):{destination:T,duration:0};R=this.hasVerticalScroll?E.momentum(this.y,this.startY,P,this.maxScrollY,this.options.bounce?this.wrapperHeight:0,this.options.deceleration):{destination:S,duration:0};T=I.destination;S=R.destination;J=Math.max(I.duration,R.duration);this.isInTransition=1}if(this.options.snap){var K=this._nearestSnap(T,S);this.currentPage=K;J=this.options.snapSpeed||Math.max(Math.max(Math.min(Math.abs(T-K.x),1000),Math.min(Math.abs(S-K.y),1000)),300);T=K.x;S=K.y;this.directionX=0;this.directionY=0;Q=this.options.bounceEasing}if(T!=this.x||S!=this.y){if(T>0||T<this.maxScrollX||S>0||S<this.maxScrollY){Q=E.ease.quadratic}this.scrollTo(T,S,J,Q);return}this._execEvent("scrollEnd")},_resize:function(){var I=this;clearTimeout(this.resizeTimeout);this.resizeTimeout=setTimeout(function(){I.refresh()},this.options.resizePolling)},resetPosition:function(I){var J=this.x,K=this.y;I=I||0;if(!this.hasHorizontalScroll||this.x>0){J=0}else{if(this.x<this.maxScrollX){J=this.maxScrollX}}if(!this.hasVerticalScroll||this.y>0){K=0}else{if(this.y<this.maxScrollY){K=this.maxScrollY}}if(J==this.x&&K==this.y){return false}this.scrollTo(J,K,I,this.options.bounceEasing);return true},disable:function(){this.enabled=false},enable:function(){this.enabled=true},refresh:function(){var I=this.wrapper.offsetHeight;this.wrapperWidth=this.wrapper.clientWidth;this.wrapperHeight=this.wrapper.clientHeight;this.scrollerWidth=this.scroller.offsetWidth;this.scrollerHeight=this.scroller.offsetHeight;this.maxScrollX=this.wrapperWidth-this.scrollerWidth;this.maxScrollY=this.wrapperHeight-this.scrollerHeight;this.hasHorizontalScroll=this.options.scrollX&&this.maxScrollX<0;this.hasVerticalScroll=this.options.scrollY&&this.maxScrollY<0;if(!this.hasHorizontalScroll){this.maxScrollX=0;this.scrollerWidth=this.wrapperWidth}if(!this.hasVerticalScroll){this.maxScrollY=0;this.scrollerHeight=this.wrapperHeight}this.endTime=0;this.directionX=0;this.directionY=0;this.wrapperOffset=E.offset(this.wrapper);this._execEvent("refresh");this.resetPosition()},on:function(I,J){if(!this._events[I]){this._events[I]=[]}this._events[I].push(J)},off:function(I,J){if(!this._events[I]){return}var K=this._events[I].indexOf(J);if(K>-1){this._events[I].splice(K,1)}},_execEvent:function(I){if(!this._events[I]){return}var J=0,K=this._events[I].length;if(!K){return}for(;J<K;J++){this._events[I][J].apply(this,[].slice.call(arguments,1))}},scrollBy:function(J,K,L,I){J=this.x+J;K=this.y+K;L=L||0;this.scrollTo(J,K,L,I)},scrollTo:function(J,K,L,I){I=I||E.ease.circular;this.isInTransition=this.options.useTransition&&L>0;if(!L||(this.options.useTransition&&I.style)){this._transitionTimingFunction(I.style);this._transitionTime(L);this._translate(J,K)}else{this._animate(J,K,L,I.fn)}},scrollToElement:function(I,M,J,K,L){I=I.nodeType?I:this.scroller.querySelector(I);if(!I){return}var N=E.offset(I);N.left-=this.wrapperOffset.left;N.top-=this.wrapperOffset.top;if(J===true){J=Math.round(I.offsetWidth/2-this.wrapper.offsetWidth/2)}if(K===true){K=Math.round(I.offsetHeight/2-this.wrapper.offsetHeight/2)}N.left-=J||0;N.top-=K||0;N.left=N.left>0?0:N.left<this.maxScrollX?this.maxScrollX:N.left;N.top=N.top>0?0:N.top<this.maxScrollY?this.maxScrollY:N.top;M=M===undefined||M===null||M==="auto"?Math.max(Math.abs(this.x-N.left),Math.abs(this.y-N.top)):M;this.scrollTo(N.left,N.top,M,L)},_transitionTime:function(J){J=J||0;this.scrollerStyle[E.style.transitionDuration]=J+"ms";if(!J&&E.isBadAndroid){this.scrollerStyle[E.style.transitionDuration]="0.001s"}if(this.indicators){for(var I=this.indicators.length;I--;){this.indicators[I].transitionTime(J)}}},_transitionTimingFunction:function(J){this.scrollerStyle[E.style.transitionTimingFunction]=J;if(this.indicators){for(var I=this.indicators.length;I--;){this.indicators[I].transitionTimingFunction(J)}}},_translate:function(J,K){if(this.options.useTransform){this.scrollerStyle[E.style.transform]="translate("+J+"px,"+K+"px)"+this.translateZ}else{J=Math.round(J);K=Math.round(K);this.scrollerStyle.left=J+"px";this.scrollerStyle.top=K+"px"}this.x=J;this.y=K;if(this.indicators){for(var I=this.indicators.length;I--;){this.indicators[I].updatePosition()}}},_initEvents:function(I){var J=I?E.removeEvent:E.addEvent,K=this.options.bindToWrapper?this.wrapper:window;J(window,"orientationchange",this);J(window,"resize",this);if(this.options.click){J(this.wrapper,"click",this,true)}if(!this.options.disableMouse){J(this.wrapper,"mousedown",this);J(K,"mousemove",this);J(K,"mousecancel",this);J(K,"mouseup",this)}if(E.hasPointer&&!this.options.disablePointer){J(this.wrapper,E.prefixPointerEvent("pointerdown"),this);J(K,E.prefixPointerEvent("pointermove"),this);J(K,E.prefixPointerEvent("pointercancel"),this);J(K,E.prefixPointerEvent("pointerup"),this)}if(E.hasTouch&&!this.options.disableTouch){J(this.wrapper,"touchstart",this);J(K,"touchmove",this);J(K,"touchcancel",this);J(K,"touchend",this)}J(this.scroller,"transitionend",this);J(this.scroller,"webkitTransitionEnd",this);J(this.scroller,"oTransitionEnd",this);J(this.scroller,"MSTransitionEnd",this)},getComputedPosition:function(){var I=window.getComputedStyle(this.scroller,null),J,K;if(this.options.useTransform){I=I[E.style.transform].split(")")[0].split(", ");J=+(I[12]||I[4]);K=+(I[13]||I[5])}else{J=+I.left.replace(/[^-\d.]/g,"");K=+I.top.replace(/[^-\d.]/g,"")}return{x:J,y:K}},_initIndicators:function(){var I=this.options.interactiveScrollbars,J=typeof this.options.scrollbars!="string",M=[],L;var N=this;this.indicators=[];if(this.options.scrollbars){if(this.options.scrollY){L={el:H("v",I,this.options.scrollbars),interactive:I,defaultScrollbars:true,customStyle:J,resize:this.options.resizeScrollbars,shrink:this.options.shrinkScrollbars,fade:this.options.fadeScrollbars,listenX:false};this.wrapper.appendChild(L.el);M.push(L)}if(this.options.scrollX){L={el:H("h",I,this.options.scrollbars),interactive:I,defaultScrollbars:true,customStyle:J,resize:this.options.resizeScrollbars,shrink:this.options.shrinkScrollbars,fade:this.options.fadeScrollbars,listenY:false};this.wrapper.appendChild(L.el);M.push(L)}}if(this.options.indicators){M=M.concat(this.options.indicators)}for(var K=M.length;K--;){this.indicators.push(new F(this,M[K]))}function O(Q){for(var P=N.indicators.length;P--;){Q.call(N.indicators[P])}}if(this.options.fadeScrollbars){this.on("scrollEnd",function(){O(function(){this.fade()})});this.on("scrollCancel",function(){O(function(){this.fade()})});this.on("scrollStart",function(){O(function(){this.fade(1)})});this.on("beforeScrollStart",function(){O(function(){this.fade(1,true)})})}this.on("refresh",function(){O(function(){this.refresh()})});this.on("destroy",function(){O(function(){this.destroy()});delete this.indicators})},_initWheel:function(){E.addEvent(this.wrapper,"wheel",this);E.addEvent(this.wrapper,"mousewheel",this);E.addEvent(this.wrapper,"DOMMouseScroll",this);this.on("destroy",function(){E.removeEvent(this.wrapper,"wheel",this);E.removeEvent(this.wrapper,"mousewheel",this);E.removeEvent(this.wrapper,"DOMMouseScroll",this)})},_wheel:function(J){if(!this.enabled){return}J.preventDefault();J.stopPropagation();var N,I,L,M,K=this;if(this.wheelTimeout===undefined){K._execEvent("scrollStart")}clearTimeout(this.wheelTimeout);this.wheelTimeout=setTimeout(function(){K._execEvent("scrollEnd");K.wheelTimeout=undefined},400);if("deltaX" in J){if(J.deltaMode===1){N=-J.deltaX*this.options.mouseWheelSpeed;I=-J.deltaY*this.options.mouseWheelSpeed}else{N=-J.deltaX;I=-J.deltaY}}else{if("wheelDeltaX" in J){N=J.wheelDeltaX/120*this.options.mouseWheelSpeed;I=J.wheelDeltaY/120*this.options.mouseWheelSpeed}else{if("wheelDelta" in J){N=I=J.wheelDelta/120*this.options.mouseWheelSpeed}else{if("detail" in J){N=I=-J.detail/3*this.options.mouseWheelSpeed}else{return}}}}N*=this.options.invertWheelDirection;I*=this.options.invertWheelDirection;if(!this.hasVerticalScroll){N=I;I=0}if(this.options.snap){L=this.currentPage.pageX;M=this.currentPage.pageY;if(N>0){L--}else{if(N<0){L++}}if(I>0){M--}else{if(I<0){M++}}this.goToPage(L,M);return}L=this.x+Math.round(this.hasHorizontalScroll?N:0);M=this.y+Math.round(this.hasVerticalScroll?I:0);if(L>0){L=0}else{if(L<this.maxScrollX){L=this.maxScrollX}}if(M>0){M=0}else{if(M<this.maxScrollY){M=this.maxScrollY}}this.scrollTo(L,M,0);if(this.options.probeType>1){this._execEvent("scroll")}},_initSnap:function(){this.currentPage={};if(typeof this.options.snap=="string"){this.options.snap=this.scroller.querySelectorAll(this.options.snap)}this.on("refresh",function(){var S=0,M,N=0,L,K,I,P=0,Q,R=this.options.snapStepX||this.wrapperWidth,O=this.options.snapStepY||this.wrapperHeight,J;this.pages=[];if(!this.wrapperWidth||!this.wrapperHeight||!this.scrollerWidth||!this.scrollerHeight){return}if(this.options.snap===true){K=Math.round(R/2);I=Math.round(O/2);while(P>-this.scrollerWidth){this.pages[S]=[];M=0;Q=0;while(Q>-this.scrollerHeight){this.pages[S][M]={x:Math.max(P,this.maxScrollX),y:Math.max(Q,this.maxScrollY),width:R,height:O,cx:P-K,cy:Q-I};Q-=O;M++}P-=R;S++}}else{J=this.options.snap;M=J.length;L=-1;for(;S<M;S++){if(S===0||J[S].offsetLeft<=J[S-1].offsetLeft){N=0;L++}if(!this.pages[N]){this.pages[N]=[]}P=Math.max(-J[S].offsetLeft,this.maxScrollX);Q=Math.max(-J[S].offsetTop,this.maxScrollY);K=P-Math.round(J[S].offsetWidth/2);I=Q-Math.round(J[S].offsetHeight/2);this.pages[N][L]={x:P,y:Q,width:J[S].offsetWidth,height:J[S].offsetHeight,cx:K,cy:I};if(P>this.maxScrollX){N++}}}this.goToPage(this.currentPage.pageX||0,this.currentPage.pageY||0,0);if(this.options.snapThreshold%1===0){this.snapThresholdX=this.options.snapThreshold;this.snapThresholdY=this.options.snapThreshold}else{this.snapThresholdX=Math.round(this.pages[this.currentPage.pageX][this.currentPage.pageY].width*this.options.snapThreshold);this.snapThresholdY=Math.round(this.pages[this.currentPage.pageX][this.currentPage.pageY].height*this.options.snapThreshold)}});this.on("flick",function(){var I=this.options.snapSpeed||Math.max(Math.max(Math.min(Math.abs(this.x-this.startX),1000),Math.min(Math.abs(this.y-this.startY),1000)),300);this.goToPage(this.currentPage.pageX+this.directionX,this.currentPage.pageY+this.directionY,I)})},_nearestSnap:function(L,M){if(!this.pages.length){return{x:0,y:0,pageX:0,pageY:0}}var I=0,J=this.pages.length,K=0;if(Math.abs(L-this.absStartX)<this.snapThresholdX&&Math.abs(M-this.absStartY)<this.snapThresholdY){return this.currentPage}if(L>0){L=0}else{if(L<this.maxScrollX){L=this.maxScrollX}}if(M>0){M=0}else{if(M<this.maxScrollY){M=this.maxScrollY}}for(;I<J;I++){if(L>=this.pages[I][0].cx){L=this.pages[I][0].x;break}}J=this.pages[I].length;for(;K<J;K++){if(M>=this.pages[0][K].cy){M=this.pages[0][K].y;break}}if(I==this.currentPage.pageX){I+=this.directionX;if(I<0){I=0}else{if(I>=this.pages.length){I=this.pages.length-1}}L=this.pages[I][0].x}if(K==this.currentPage.pageY){K+=this.directionY;if(K<0){K=0}else{if(K>=this.pages[0].length){K=this.pages[0].length-1}}M=this.pages[0][K].y}return{x:L,y:M,pageX:I,pageY:K}},goToPage:function(L,M,N,J){J=J||this.options.bounceEasing;if(L>=this.pages.length){L=this.pages.length-1}else{if(L<0){L=0}}if(M>=this.pages[L].length){M=this.pages[L].length-1}else{if(M<0){M=0}}var K=this.pages[L][M].x,I=this.pages[L][M].y;N=N===undefined?this.options.snapSpeed||Math.max(Math.max(Math.min(Math.abs(K-this.x),1000),Math.min(Math.abs(I-this.y),1000)),300):N;this.currentPage={x:K,y:I,pageX:L,pageY:M};this.scrollTo(K,I,N,J)},next:function(J,I){var K=this.currentPage.pageX,L=this.currentPage.pageY;K++;if(K>=this.pages.length&&this.hasVerticalScroll){K=0;L++}this.goToPage(K,L,J,I)},prev:function(J,I){var K=this.currentPage.pageX,L=this.currentPage.pageY;K--;if(K<0&&this.hasVerticalScroll){K=0;L--}this.goToPage(K,L,J,I)},_initKeys:function(J){var K={pageUp:33,pageDown:34,end:35,home:36,left:37,up:38,right:39,down:40};var I;if(typeof this.options.keyBindings=="object"){for(I in this.options.keyBindings){if(typeof this.options.keyBindings[I]=="string"){this.options.keyBindings[I]=this.options.keyBindings[I].toUpperCase().charCodeAt(0)}}}else{this.options.keyBindings={}}for(I in K){this.options.keyBindings[I]=this.options.keyBindings[I]||K[I]}E.addEvent(window,"keydown",this);this.on("destroy",function(){E.removeEvent(window,"keydown",this)})},_key:function(K){if(!this.enabled){return}var J=this.options.snap,P=J?this.currentPage.pageX:this.x,O=J?this.currentPage.pageY:this.y,I=E.getTime(),N=this.keyTime||0,L=0.25,M;if(this.options.useTransition&&this.isInTransition){M=this.getComputedPosition();this._translate(Math.round(M.x),Math.round(M.y));this.isInTransition=false}this.keyAcceleration=I-N<200?Math.min(this.keyAcceleration+L,50):0;switch(K.keyCode){case this.options.keyBindings.pageUp:if(this.hasHorizontalScroll&&!this.hasVerticalScroll){P+=J?1:this.wrapperWidth}else{O+=J?1:this.wrapperHeight}break;case this.options.keyBindings.pageDown:if(this.hasHorizontalScroll&&!this.hasVerticalScroll){P-=J?1:this.wrapperWidth}else{O-=J?1:this.wrapperHeight}break;case this.options.keyBindings.end:P=J?this.pages.length-1:this.maxScrollX;O=J?this.pages[0].length-1:this.maxScrollY;break;case this.options.keyBindings.home:P=0;O=0;break;case this.options.keyBindings.left:P+=J?-1:5+this.keyAcceleration>>0;break;case this.options.keyBindings.up:O+=J?1:5+this.keyAcceleration>>0;break;case this.options.keyBindings.right:P-=J?-1:5+this.keyAcceleration>>0;break;case this.options.keyBindings.down:O-=J?1:5+this.keyAcceleration>>0;break;default:return}if(J){this.goToPage(P,O);return}if(P>0){P=0;this.keyAcceleration=0}else{if(P<this.maxScrollX){P=this.maxScrollX;this.keyAcceleration=0}}if(O>0){O=0;this.keyAcceleration=0}else{if(O<this.maxScrollY){O=this.maxScrollY;this.keyAcceleration=0}}this.scrollTo(P,O,0);this.keyTime=I},_animate:function(J,I,L,Q){var N=this,M=this.x,O=this.y,R=E.getTime(),P=R+L;function K(){var V=E.getTime(),S,U,T;if(V>=P){N.isAnimating=false;N._translate(J,I);if(!N.resetPosition(N.options.bounceTime)){N._execEvent("scrollEnd")}return}V=(V-R)/L;T=Q(V);S=(J-M)*T+M;U=(I-O)*T+O;N._translate(S,U);if(N.isAnimating){D(K)}if(N.options.probeType==3){N._execEvent("scroll")}}this.isAnimating=true;K()},handleEvent:function(I){switch(I.type){case"touchstart":case"pointerdown":case"MSPointerDown":case"mousedown":this._start(I);break;case"touchmove":case"pointermove":case"MSPointerMove":case"mousemove":this._move(I);break;case"touchend":case"pointerup":case"MSPointerUp":case"mouseup":case"touchcancel":case"pointercancel":case"MSPointerCancel":case"mousecancel":this._end(I);break;case"orientationchange":case"resize":this._resize();break;case"transitionend":case"webkitTransitionEnd":case"oTransitionEnd":case"MSTransitionEnd":this._transitionEnd(I);break;case"wheel":case"DOMMouseScroll":case"mousewheel":this._wheel(I);break;case"keydown":this._key(I);break;case"click":if(!I._constructed){I.preventDefault();I.stopPropagation()}break}}};function H(M,I,L){var K=document.createElement("div"),J=document.createElement("div");if(L===true){K.style.cssText="position:absolute;z-index:9999";J.style.cssText="-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;position:absolute;background:rgba(0,0,0,0.5);border:1px solid rgba(255,255,255,0.9);border-radius:3px"}J.className="iScrollIndicator";if(M=="h"){if(L===true){K.style.cssText+=";height:7px;left:2px;right:2px;bottom:0";J.style.height="100%"}K.className="iScrollHorizontalScrollbar"}else{if(L===true){K.style.cssText+=";width:7px;bottom:2px;top:2px;right:1px";J.style.width="100%"}K.className="iScrollVerticalScrollbar"}K.style.cssText+=";overflow:hidden";if(!I){K.style.pointerEvents="none"}K.appendChild(J);return K}function F(J,K){this.wrapper=typeof K.el=="string"?document.querySelector(K.el):K.el;this.wrapperStyle=this.wrapper.style;this.indicator=this.wrapper.children[0];this.indicatorStyle=this.indicator.style;this.scroller=J;this.options={listenX:true,listenY:true,interactive:false,resize:true,defaultScrollbars:false,shrink:false,fade:false,speedRatioX:0,speedRatioY:0};for(var I in K){this.options[I]=K[I]}this.sizeRatioX=1;this.sizeRatioY=1;this.maxPosX=0;this.maxPosY=0;if(this.options.interactive){if(!this.options.disableTouch){E.addEvent(this.indicator,"touchstart",this);E.addEvent(window,"touchend",this)}if(!this.options.disablePointer){E.addEvent(this.indicator,E.prefixPointerEvent("pointerdown"),this);E.addEvent(window,E.prefixPointerEvent("pointerup"),this)}if(!this.options.disableMouse){E.addEvent(this.indicator,"mousedown",this);E.addEvent(window,"mouseup",this)}}if(this.options.fade){this.wrapperStyle[E.style.transform]=this.scroller.translateZ;this.wrapperStyle[E.style.transitionDuration]=E.isBadAndroid?"0.001s":"0ms";this.wrapperStyle.opacity="0"}}F.prototype={handleEvent:function(I){switch(I.type){case"touchstart":case"pointerdown":case"MSPointerDown":case"mousedown":this._start(I);break;case"touchmove":case"pointermove":case"MSPointerMove":case"mousemove":this._move(I);break;case"touchend":case"pointerup":case"MSPointerUp":case"mouseup":case"touchcancel":case"pointercancel":case"MSPointerCancel":case"mousecancel":this._end(I);break}},destroy:function(){if(this.options.interactive){E.removeEvent(this.indicator,"touchstart",this);E.removeEvent(this.indicator,E.prefixPointerEvent("pointerdown"),this);E.removeEvent(this.indicator,"mousedown",this);E.removeEvent(window,"touchmove",this);E.removeEvent(window,E.prefixPointerEvent("pointermove"),this);E.removeEvent(window,"mousemove",this);E.removeEvent(window,"touchend",this);E.removeEvent(window,E.prefixPointerEvent("pointerup"),this);E.removeEvent(window,"mouseup",this)}if(this.options.defaultScrollbars){this.wrapper.parentNode.removeChild(this.wrapper)}},_start:function(I){var J=I.touches?I.touches[0]:I;I.preventDefault();I.stopPropagation();this.transitionTime();this.initiated=true;this.moved=false;this.lastPointX=J.pageX;this.lastPointY=J.pageY;this.startTime=E.getTime();if(!this.options.disableTouch){E.addEvent(window,"touchmove",this)}if(!this.options.disablePointer){E.addEvent(window,E.prefixPointerEvent("pointermove"),this)}if(!this.options.disableMouse){E.addEvent(window,"mousemove",this)}this.scroller._execEvent("beforeScrollStart")},_move:function(K){var L=K.touches?K.touches[0]:K,N,M,J,O,I=E.getTime();if(!this.moved){this.scroller._execEvent("scrollStart")}this.moved=true;N=L.pageX-this.lastPointX;this.lastPointX=L.pageX;M=L.pageY-this.lastPointY;this.lastPointY=L.pageY;J=this.x+N;O=this.y+M;this._pos(J,O);if(this.scroller.options.probeType==1&&I-this.startTime>300){this.startTime=I;this.scroller._execEvent("scroll")}else{if(this.scroller.options.probeType>1){this.scroller._execEvent("scroll")}}K.preventDefault();K.stopPropagation()},_end:function(I){if(!this.initiated){return}this.initiated=false;I.preventDefault();I.stopPropagation();E.removeEvent(window,"touchmove",this);E.removeEvent(window,E.prefixPointerEvent("pointermove"),this);E.removeEvent(window,"mousemove",this);if(this.scroller.options.snap){var J=this.scroller._nearestSnap(this.scroller.x,this.scroller.y);var K=this.options.snapSpeed||Math.max(Math.max(Math.min(Math.abs(this.scroller.x-J.x),1000),Math.min(Math.abs(this.scroller.y-J.y),1000)),300);if(this.scroller.x!=J.x||this.scroller.y!=J.y){this.scroller.directionX=0;this.scroller.directionY=0;this.scroller.currentPage=J;this.scroller.scrollTo(J.x,J.y,K,this.scroller.options.bounceEasing)}}if(this.moved){this.scroller._execEvent("scrollEnd")}},transitionTime:function(I){I=I||0;this.indicatorStyle[E.style.transitionDuration]=I+"ms";if(!I&&E.isBadAndroid){this.indicatorStyle[E.style.transitionDuration]="0.001s"}},transitionTimingFunction:function(I){this.indicatorStyle[E.style.transitionTimingFunction]=I},refresh:function(){this.transitionTime();if(this.options.listenX&&!this.options.listenY){this.indicatorStyle.display=this.scroller.hasHorizontalScroll?"block":"none"}else{if(this.options.listenY&&!this.options.listenX){this.indicatorStyle.display=this.scroller.hasVerticalScroll?"block":"none"}else{this.indicatorStyle.display=this.scroller.hasHorizontalScroll||this.scroller.hasVerticalScroll?"block":"none"}}if(this.scroller.hasHorizontalScroll&&this.scroller.hasVerticalScroll){E.addClass(this.wrapper,"iScrollBothScrollbars");E.removeClass(this.wrapper,"iScrollLoneScrollbar");if(this.options.defaultScrollbars&&this.options.customStyle){if(this.options.listenX){this.wrapper.style.right="8px"}else{this.wrapper.style.bottom="8px"}}}else{E.removeClass(this.wrapper,"iScrollBothScrollbars");E.addClass(this.wrapper,"iScrollLoneScrollbar");if(this.options.defaultScrollbars&&this.options.customStyle){if(this.options.listenX){this.wrapper.style.right="2px"}else{this.wrapper.style.bottom="2px"}}}var I=this.wrapper.offsetHeight;if(this.options.listenX){this.wrapperWidth=this.wrapper.clientWidth;if(this.options.resize){this.indicatorWidth=Math.max(Math.round(this.wrapperWidth*this.wrapperWidth/(this.scroller.scrollerWidth||this.wrapperWidth||1)),8);this.indicatorStyle.width=this.indicatorWidth+"px"}else{this.indicatorWidth=this.indicator.clientWidth}this.maxPosX=this.wrapperWidth-this.indicatorWidth;if(this.options.shrink=="clip"){this.minBoundaryX=-this.indicatorWidth+8;this.maxBoundaryX=this.wrapperWidth-8}else{this.minBoundaryX=0;this.maxBoundaryX=this.maxPosX}this.sizeRatioX=this.options.speedRatioX||(this.scroller.maxScrollX&&(this.maxPosX/this.scroller.maxScrollX))}if(this.options.listenY){this.wrapperHeight=this.wrapper.clientHeight;if(this.options.resize){this.indicatorHeight=Math.max(Math.round(this.wrapperHeight*this.wrapperHeight/(this.scroller.scrollerHeight||this.wrapperHeight||1)),8);this.indicatorStyle.height=this.indicatorHeight+"px"}else{this.indicatorHeight=this.indicator.clientHeight}this.maxPosY=this.wrapperHeight-this.indicatorHeight;if(this.options.shrink=="clip"){this.minBoundaryY=-this.indicatorHeight+8;this.maxBoundaryY=this.wrapperHeight-8}else{this.minBoundaryY=0;this.maxBoundaryY=this.maxPosY}this.maxPosY=this.wrapperHeight-this.indicatorHeight;this.sizeRatioY=this.options.speedRatioY||(this.scroller.maxScrollY&&(this.maxPosY/this.scroller.maxScrollY))}this.updatePosition()},updatePosition:function(){var I=this.options.listenX&&Math.round(this.sizeRatioX*this.scroller.x)||0,J=this.options.listenY&&Math.round(this.sizeRatioY*this.scroller.y)||0;if(!this.options.ignoreBoundaries){if(I<this.minBoundaryX){if(this.options.shrink=="scale"){this.width=Math.max(this.indicatorWidth+I,8);this.indicatorStyle.width=this.width+"px"}I=this.minBoundaryX}else{if(I>this.maxBoundaryX){if(this.options.shrink=="scale"){this.width=Math.max(this.indicatorWidth-(I-this.maxPosX),8);this.indicatorStyle.width=this.width+"px";I=this.maxPosX+this.indicatorWidth-this.width}else{I=this.maxBoundaryX}}else{if(this.options.shrink=="scale"&&this.width!=this.indicatorWidth){this.width=this.indicatorWidth;this.indicatorStyle.width=this.width+"px"}}}if(J<this.minBoundaryY){if(this.options.shrink=="scale"){this.height=Math.max(this.indicatorHeight+J*3,8);this.indicatorStyle.height=this.height+"px"}J=this.minBoundaryY}else{if(J>this.maxBoundaryY){if(this.options.shrink=="scale"){this.height=Math.max(this.indicatorHeight-(J-this.maxPosY)*3,8);this.indicatorStyle.height=this.height+"px";J=this.maxPosY+this.indicatorHeight-this.height}else{J=this.maxBoundaryY}}else{if(this.options.shrink=="scale"&&this.height!=this.indicatorHeight){this.height=this.indicatorHeight;this.indicatorStyle.height=this.height+"px"}}}}this.x=I;this.y=J;if(this.scroller.options.useTransform){this.indicatorStyle[E.style.transform]="translate("+I+"px,"+J+"px)"+this.scroller.translateZ}else{this.indicatorStyle.left=I+"px";this.indicatorStyle.top=J+"px"}},_pos:function(I,J){if(I<0){I=0}else{if(I>this.maxPosX){I=this.maxPosX}}if(J<0){J=0}else{if(J>this.maxPosY){J=this.maxPosY}}I=this.options.listenX?Math.round(I/this.sizeRatioX):this.scroller.x;J=this.options.listenY?Math.round(J/this.sizeRatioY):this.scroller.y;this.scroller.scrollTo(I,J)},fade:function(K,J){if(J&&!this.visible){return}clearTimeout(this.fadeTimeout);this.fadeTimeout=null;var I=K?250:500,L=K?0:300;K=K?"1":"0";this.wrapperStyle[E.style.transitionDuration]=I+"ms";this.fadeTimeout=setTimeout((function(M){this.wrapperStyle.opacity=M;this.visible=+M}).bind(this,K),L)}};G.utils=E;return G})();iosSelectUtil={isArray:function(D){return Object.prototype.toString.call(D)==="[object Array]"},attrToData:function(D,G){var E={};for(var F in D.dataset){E[F]=D.dataset[F]}E["dom"]=D;E["atindex"]=G;return E},attrToHtml:function(E){var D="";for(var F in E){D+="data-"+F+'="'+E[F]+'"'}return D}};function C(D,E){if(!(this instanceof C)){return new C(D,E)}this.html=D;this.opts=E;var F=document.createElement("div");F.className="olay";var G=document.createElement("div");G.className="layer";this.el=F;this.layer_el=G;this.init()}C.prototype={init:function(){this.layer_el.innerHTML=this.html;document.body.appendChild(this.el);this.el.appendChild(this.layer_el);this.el.style.height=Math.max(document.documentElement.getBoundingClientRect().height,window.innerHeight);if(this.opts.className){this.el.className+=" "+this.opts.className}this.bindEvent()},bindEvent:function(){var H=this.el.querySelectorAll(".sure");var D=this.el.querySelectorAll(".close");var E=this;for(var F=0,G=H.length;F<G;F++){H[F].addEventListener("click",function(I){E.close()})}for(var F=0,G=D.length;F<G;F++){D[F].addEventListener("click",function(I){E.close()})}},close:function(){if(this.el){this.el.parentNode.removeChild(this.el);this.el=null}}};function B(D,F,E){if(!iosSelectUtil.isArray(F)||F.length===0||!iosSelectUtil.isArray(F[0])){return}this.data=F;this.level=D||1;this.options=E;this.typeBox=this.level===1?"one-level-box":(this.level===2?"two-level-box":"three-level-box");this.callback=E.callback;this.title=E.title||"";this.itemHeight=E.itemHeight||35;this.headerHeight=E.headerHeight||44;this.init()}B.prototype={init:function(){this.initLayer();this.selectOneObj={};this.selectTwoObj={};this.selectThreeObj={};this.setOneLevel(this.options.oneLevelId,this.options.twoLevelId,this.options.threeLevelId)},initLayer:function(){var E=this;var D=['<header class="iosselect-header">','<h2 id="iosSelectTitle"></h2>','<a href="javascript:void(0)" class="close">取消</a>','<a href="javascript:void(0)" class="sure">确定</a>',"</header>",'<section class="iosselect-box">','<div class="one-level-contain" id="oneLevelContain">','<ul class="select-one-level">',"</ul>","</div>",'<div class="two-level-contain" id="twoLevelContain">','<ul class="select-two-level">',"</ul>","</div>",'<div class="three-level-contain" id="threeLevelContain">','<ul class="select-three-level">',"</ul>","</div>","</section>",'<hr class="cover-area1"/>','<hr class="cover-area2"/>'].join("\r\n");this.iosSelectLayer=new C(D,{className:"ios-select-widget-box "+this.typeBox+(this.options.addClassName?" "+this.options.addClassName:"")});this.iosSelectTitleDom=document.querySelector("#iosSelectTitle");if(this.options.title){this.iosSelectTitleDom.innerHTML=this.options.title}if(this.options.headerHeight&&this.options.itemHeight){this.coverArea1Dom=document.querySelector(".cover-area1");this.coverArea1Dom.style.top=this.headerHeight+this.itemHeight*3+"px";this.coverArea2Dom=document.querySelector(".cover-area2");this.coverArea2Dom.style.top=this.headerHeight+this.itemHeight*4+"px"}this.oneLevelContainDom=document.querySelector("#oneLevelContain");this.twoLevelContainDom=document.querySelector("#twoLevelContain");this.threeLevelContainDom=document.querySelector("#threeLevelContain");this.oneLevelUlContainDom=document.querySelector(".select-one-level");this.twoLevelUlContainDom=document.querySelector(".select-two-level");this.threeLevelUlContainDom=document.querySelector(".select-three-level");this.iosSelectLayer.el.querySelector(".layer").style.height=this.itemHeight*7+this.headerHeight+"px";this.oneLevelContainDom.style.height=this.itemHeight*7+"px";this.scrollOne=new A("#oneLevelContain",{probeType:3,bounce:false});this.scrollOne.on("scrollStart",function(){Array.prototype.slice.call(E.oneLevelContainDom.querySelectorAll("li")).forEach(function(H,F,G){if(H.classList.contains("at")){H.classList.remove("at")}else{if(H.classList.contains("side1")){H.classList.remove("side1")}else{if(H.classList.contains("side2")){H.classList.remove("side2")}}}})});this.scrollOne.on("scroll",function(){var G=Math.abs(this.y)/E.itemHeight;var F=1;F=Math.round(G)+1;Array.prototype.slice.call(E.oneLevelContainDom.querySelectorAll("li")).forEach(function(K,I,J){if(K.classList.contains("at")){K.classList.remove("at")}else{if(K.classList.contains("side1")){K.classList.remove("side1")}else{if(K.classList.contains("side2")){K.classList.remove("side2")}}}});var H=E.oneLevelContainDom.querySelector("li:nth-child("+(F+3)+")");H.classList.add("at");E.oneLevelContainDom.querySelector("li:nth-child("+(F+2)+")").classList.add("side1");E.oneLevelContainDom.querySelector("li:nth-child("+(F+1)+")").classList.add("side2");E.oneLevelContainDom.querySelector("li:nth-child("+(F+4)+")").classList.add("side1");E.oneLevelContainDom.querySelector("li:nth-child("+(F+5)+")").classList.add("side2")});this.scrollOne.on("scrollEnd",function(){var H=Math.abs(this.y)/E.itemHeight;var F=1;var G=0;if(Math.ceil(H)===Math.round(H)){G=Math.ceil(H)*E.itemHeight;F=Math.ceil(H)+1}else{G=Math.floor(H)*E.itemHeight;F=Math.floor(H)+1}E.scrollOne.scrollTo(0,-G,0);var I=E.oneLevelContainDom.querySelector("li:nth-child("+(F+3)+")");Array.prototype.slice.call(E.oneLevelContainDom.querySelectorAll("li")).forEach(function(L,J,K){if(L.classList.contains("at")){L.classList.remove("at")}else{if(L.classList.contains("side1")){L.classList.remove("side1")}else{if(L.classList.contains("side2")){L.classList.remove("side2")}}}});I.classList.add("at");E.oneLevelContainDom.querySelector("li:nth-child("+(F+2)+")").classList.add("side1");E.oneLevelContainDom.querySelector("li:nth-child("+(F+1)+")").classList.add("side2");E.oneLevelContainDom.querySelector("li:nth-child("+(F+4)+")").classList.add("side1");E.oneLevelContainDom.querySelector("li:nth-child("+(F+5)+")").classList.add("side2");E.selectOneObj=iosSelectUtil.attrToData(I,F);if(E.level>1&&E.options.oneTwoRelation===1){E.setTwoLevel(E.selectOneObj.id)}});if(this.level>=2){this.twoLevelContainDom.style.height=this.itemHeight*7+"px";this.scrollTwo=new A("#twoLevelContain",{probeType:3,bounce:false});this.scrollTwo.on("scrollStart",function(){Array.prototype.slice.call(E.twoLevelContainDom.querySelectorAll("li")).forEach(function(H,F,G){if(H.classList.contains("at")){H.classList.remove("at")}else{if(H.classList.contains("side1")){H.classList.remove("side1")}else{if(H.classList.contains("side2")){H.classList.remove("side2")}}}})});this.scrollTwo.on("scroll",function(){var H=Math.abs(this.y)/E.itemHeight;var F=0;F=Math.round(H)+1;var G=E.twoLevelContainDom.querySelector("li:nth-child("+(F+3)+")");Array.prototype.slice.call(E.twoLevelContainDom.querySelectorAll("li")).forEach(function(K,I,J){if(K.classList.contains("at")){K.classList.remove("at")}else{if(K.classList.contains("side1")){K.classList.remove("side1")}else{if(K.classList.contains("side2")){K.classList.remove("side2")}}}});G.classList.add("at");E.twoLevelContainDom.querySelector("li:nth-child("+(F+2)+")").classList.add("side1");E.twoLevelContainDom.querySelector("li:nth-child("+(F+1)+")").classList.add("side2");E.twoLevelContainDom.querySelector("li:nth-child("+(F+4)+")").classList.add("side1");E.twoLevelContainDom.querySelector("li:nth-child("+(F+5)+")").classList.add("side2")});this.scrollTwo.on("scrollEnd",function(){var H=Math.abs(this.y)/E.itemHeight;var F=1;var G=0;if(Math.ceil(H)===Math.round(H)){G=Math.ceil(H)*E.itemHeight;F=Math.ceil(H)+1}else{G=Math.floor(H)*E.itemHeight;F=Math.floor(H)+1}E.scrollTwo.scrollTo(0,-G,0);var I=E.twoLevelContainDom.querySelector("li:nth-child("+(F+3)+")");Array.prototype.slice.call(E.twoLevelContainDom.querySelectorAll("li")).forEach(function(L,J,K){if(L.classList.contains("at")){L.classList.remove("at")}else{if(L.classList.contains("side1")){L.classList.remove("side1")}else{if(L.classList.contains("side2")){L.classList.remove("side2")}}}});I.classList.add("at");E.twoLevelContainDom.querySelector("li:nth-child("+(F+2)+")").classList.add("side1");E.twoLevelContainDom.querySelector("li:nth-child("+(F+1)+")").classList.add("side2");E.twoLevelContainDom.querySelector("li:nth-child("+(F+4)+")").classList.add("side1");E.twoLevelContainDom.querySelector("li:nth-child("+(F+5)+")").classList.add("side2");E.selectTwoObj=iosSelectUtil.attrToData(I,F);if(E.level===3&&E.options.twoThreeRelation===1){E.setThreeLevel(E.selectOneObj.id,E.selectTwoObj.id)}})}if(this.level===3){this.threeLevelContainDom.style.height=this.itemHeight*7+"px";this.scrollThree=new A("#threeLevelContain",{probeType:3,bounce:false});this.scrollThree.on("scrollStart",function(){Array.prototype.slice.call(E.threeLevelContainDom.querySelectorAll("li")).forEach(function(H,F,G){if(H.classList.contains("at")){H.classList.remove("at")}else{if(H.classList.contains("side1")){H.classList.remove("side1")}else{if(H.classList.contains("side2")){H.classList.remove("side2")}}}})});this.scrollThree.on("scroll",function(){var G=Math.abs(this.y)/E.itemHeight;var F=0;F=Math.round(G)+1;var H=E.threeLevelContainDom.querySelector("li:nth-child("+(F+3)+")");Array.prototype.slice.call(E.threeLevelContainDom.querySelectorAll("li")).forEach(function(K,I,J){if(K.classList.contains("at")){K.classList.remove("at")}else{if(K.classList.contains("side1")){K.classList.remove("side1")}else{if(K.classList.contains("side2")){K.classList.remove("side2")}}}});H.classList.add("at");E.threeLevelContainDom.querySelector("li:nth-child("+(F+2)+")").classList.add("side1");E.threeLevelContainDom.querySelector("li:nth-child("+(F+1)+")").classList.add("side2");E.threeLevelContainDom.querySelector("li:nth-child("+(F+4)+")").classList.add("side1");E.threeLevelContainDom.querySelector("li:nth-child("+(F+5)+")").classList.add("side2")});this.scrollThree.on("scrollEnd",function(){var H=Math.abs(this.y)/E.itemHeight;var F=1;var G=0;if(Math.ceil(H)===Math.round(H)){G=Math.ceil(H)*E.itemHeight;F=Math.ceil(H)+1}else{G=Math.floor(H)*E.itemHeight;F=Math.floor(H)+1}E.scrollThree.scrollTo(0,-G,0);var I=E.threeLevelContainDom.querySelector("li:nth-child("+(F+3)+")");Array.prototype.slice.call(E.threeLevelContainDom.querySelectorAll("li")).forEach(function(L,J,K){if(L.classList.contains("at")){L.classList.remove("at")}else{if(L.classList.contains("side1")){L.classList.remove("side1")}else{if(L.classList.contains("side2")){L.classList.remove("side2")}}}});I.classList.add("at");E.threeLevelContainDom.querySelector("li:nth-child("+(F+2)+")").classList.add("side1");E.threeLevelContainDom.querySelector("li:nth-child("+(F+1)+")").classList.add("side2");E.threeLevelContainDom.querySelector("li:nth-child("+(F+4)+")").classList.add("side1");E.threeLevelContainDom.querySelector("li:nth-child("+(F+5)+")").classList.add("side2");E.selectThreeObj=iosSelectUtil.attrToData(I,F)})}this.selectBtnDom=this.iosSelectLayer.el.querySelector(".sure");this.selectBtnDom.addEventListener("click",function(F){E.callback&&E.callback(E.selectOneObj,E.selectTwoObj,E.selectThreeObj);E.iosSelectLayer.close()})},getOneLevel:function(){return this.data[0]},setOneLevel:function(I,D,H){var F=this.getOneLevel();if(!I){I=F[0]["id"]}var G="";var J=this;var E=0;G+="<li></li>";G+="<li></li>";G+="<li></li>";F.forEach(function(N,L,M){if(N.id===I){G+="<li "+iosSelectUtil.attrToHtml(N)+' class="at">'+N.value+"</li>";E=L+1+3}else{G+="<li "+iosSelectUtil.attrToHtml(N)+">"+N.value+"</li>"}});G+="<li></li>";G+="<li></li>";G+="<li></li>";this.oneLevelUlContainDom.innerHTML=G;this.scrollOne.refresh();this.scrollOne.scrollToElement("li:nth-child("+(E-3)+")",0);if(this.level>=2){this.setTwoLevel(I,D,H)}var K=this.oneLevelContainDom.querySelector(".at");this.oneLevelContainDom.querySelector("li:nth-child("+(E-1)+")").classList.add("side1");this.oneLevelContainDom.querySelector("li:nth-child("+(E-2)+")").classList.add("side2");this.oneLevelContainDom.querySelector("li:nth-child("+(E+1)+")").classList.add("side1");this.oneLevelContainDom.querySelector("li:nth-child("+(E+2)+")").classList.add("side2");this.selectOneObj=iosSelectUtil.attrToData(K,E)},getTwoLevel:function(D){if(!iosSelectUtil.isArray(this.data[1])){throw new Error("data format error")}var E=[];if(this.options.oneTwoRelation===1){this.data[1].forEach(function(H,F,G){if(H["parentId"]===D){E.push(H)}})}else{E=this.data[1]}return E},setTwoLevel:function(H,D,G){var E=this.getTwoLevel(H);var F=0;if(!D){D=E[0]["id"]}var K="";var J=this;K+="<li></li>";K+="<li></li>";K+="<li></li>";E.forEach(function(N,L,M){if(N.id===D){K+="<li "+iosSelectUtil.attrToHtml(N)+' class="at">'+N.value+"</li>";F=L+1+3}else{K+="<li "+iosSelectUtil.attrToHtml(N)+">"+N.value+"</li>"}});K+="<li></li>";K+="<li></li>";K+="<li></li>";this.twoLevelUlContainDom.innerHTML=K;this.scrollTwo.refresh();this.scrollTwo.scrollToElement(":nth-child("+(F-3)+")",0);if(this.level===3){this.setThreeLevel(H,D,G)}var I=J.twoLevelContainDom.querySelector("li:nth-child("+F+")");I.classList.add("at");J.twoLevelContainDom.querySelector("li:nth-child("+(F-1)+")").classList.add("side1");J.twoLevelContainDom.querySelector("li:nth-child("+(F-2)+")").classList.add("side2");J.twoLevelContainDom.querySelector("li:nth-child("+(F+1)+")").classList.add("side1");J.twoLevelContainDom.querySelector("li:nth-child("+(F+2)+")").classList.add("side2");J.selectTwoObj=iosSelectUtil.attrToData(I,F)},getThreeLevel:function(E){if(!iosSelectUtil.isArray(this.data[2])){throw new Error("data format error")}var D=[];if(this.options.twoThreeRelation===1){this.data[2].forEach(function(H,F,G){if(H["parentId"]===E){D.push(H)}})}else{D=this.data[2]}return D},setThreeLevel:function(J,F,I){var E=this.getThreeLevel(F);var G=0;if(!I){I=E[0]["id"]}var D="";var K=this;D+="<li></li>";D+="<li></li>";D+="<li></li>";E.forEach(function(N,L,M){if(N.id===I){D+="<li "+iosSelectUtil.attrToHtml(N)+' class="at">'+N.value+"</li>";G=L+1+3}else{D+="<li "+iosSelectUtil.attrToHtml(N)+">"+N.value+"</li>"}});D+="<li></li>";D+="<li></li>";D+="<li></li>";this.threeLevelUlContainDom.innerHTML=D;this.scrollThree.refresh();this.scrollThree.scrollToElement(":nth-child("+(G-3)+")",0);var H=K.threeLevelContainDom.querySelector("li:nth-child("+G+")");H.classList.add("at");K.threeLevelContainDom.querySelector("li:nth-child("+(G-1)+")").classList.add("side1");K.threeLevelContainDom.querySelector("li:nth-child("+(G-2)+")").classList.add("side2");K.threeLevelContainDom.querySelector("li:nth-child("+(G+1)+")").classList.add("side1");K.threeLevelContainDom.querySelector("li:nth-child("+(G+2)+")").classList.add("side2");K.selectThreeObj=iosSelectUtil.attrToData(H,G)}};if(typeof module!="undefined"&&module.exports){module.exports=B}else{if(typeof define=="function"&&define.amd){define(function(){return B})}else{window.IosSelect=B}}})();