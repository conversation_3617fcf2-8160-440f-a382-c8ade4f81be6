(function(G){G.extend(!0,G.fn.zTree._z,{view:{clearOldFirstNode:function(O,P){for(var N=P.getNextNode();N;){if(N.isFirstNode){N.isFirstNode=!1;M.setNodeLineIcos(O,N);break}if(N.isLastNode){break}N=N.getNextNode()}},clearOldLastNode:function(O,P,N){for(P=P.getPreNode();P;){if(P.isLastNode){P.isLastNode=!1;N&&M.setNodeLineIcos(O,P);break}if(P.isFirstNode){break}P=P.getPreNode()}},makeDOMNodeMainBefore:function(O,P,N){O.push("<li ",N.isHidden?"style='display:none;' ":"","id='",N.tId,"' class='",J.className.LEVEL,N.level,"' tabindex='0' hidefocus='true' treenode>")},showNode:function(N,O){O.isHidden=!1;L.initShowForExCheck(N,O);F(O,N).show()},showNodes:function(T,U,S){if(U&&U.length!=0){var N={},V,P;for(V=0,P=U.length;V<P;V++){var Q=U[V];if(!N[Q.parentTId]){var R=Q.getParentNode();N[Q.parentTId]=R===null?L.getRoot(T):Q.getParentNode()}M.showNode(T,Q,S)}for(var O in N){U=N[O][T.data.key.children],M.setFirstNodeForShow(T,U),M.setLastNodeForShow(T,U)}}},hideNode:function(N,O){O.isHidden=!0;O.isFirstNode=!1;O.isLastNode=!1;L.initHideForExCheck(N,O);M.cancelPreSelectedNode(N,O);F(O,N).hide()},hideNodes:function(T,U,S){if(U&&U.length!=0){var N={},V,P;for(V=0,P=U.length;V<P;V++){var Q=U[V];if((Q.isFirstNode||Q.isLastNode)&&!N[Q.parentTId]){var R=Q.getParentNode();N[Q.parentTId]=R===null?L.getRoot(T):Q.getParentNode()}M.hideNode(T,Q,S)}for(var O in N){U=N[O][T.data.key.children],M.setFirstNodeForHide(T,U),M.setLastNodeForHide(T,U)}}},setFirstNode:function(P,Q){var O=P.data.key.children,N=Q[O].length;N>0&&!Q[O][0].isHidden?Q[O][0].isFirstNode=!0:N>0&&M.setFirstNodeForHide(P,Q[O])},setLastNode:function(P,Q){var O=P.data.key.children,N=Q[O].length;N>0&&!Q[O][0].isHidden?Q[O][N-1].isLastNode=!0:N>0&&M.setLastNodeForHide(P,Q[O])},setFirstNodeForHide:function(Q,R){var P,O,N;for(O=0,N=R.length;O<N;O++){P=R[O];if(P.isFirstNode){break}if(!P.isHidden&&!P.isFirstNode){P.isFirstNode=!0;M.setNodeLineIcos(Q,P);break}else{P=null}}return P},setFirstNodeForShow:function(R,S){var Q,P,N,T,O;for(P=0,N=S.length;P<N;P++){if(Q=S[P],!T&&!Q.isHidden&&Q.isFirstNode){T=Q;break}else{if(!T&&!Q.isHidden&&!Q.isFirstNode){Q.isFirstNode=!0,T=Q,M.setNodeLineIcos(R,Q)}else{if(T&&Q.isFirstNode){Q.isFirstNode=!1;O=Q;M.setNodeLineIcos(R,Q);break}}}}return{"new":T,old:O}},setLastNodeForHide:function(P,Q){var O,N;for(N=Q.length-1;N>=0;N--){O=Q[N];if(O.isLastNode){break}if(!O.isHidden&&!O.isLastNode){O.isLastNode=!0;M.setNodeLineIcos(P,O);break}else{O=null}}return O},setLastNodeForShow:function(Q,R){var P,O,N,S;for(O=R.length-1;O>=0;O--){if(P=R[O],!N&&!P.isHidden&&P.isLastNode){N=P;break}else{if(!N&&!P.isHidden&&!P.isLastNode){P.isLastNode=!0,N=P,M.setNodeLineIcos(Q,P)}else{if(N&&P.isLastNode){P.isLastNode=!1;S=P;M.setNodeLineIcos(Q,P);break}}}}return{"new":N,old:S}}},data:{initHideForExCheck:function(N,O){if(O.isHidden&&N.check&&N.check.enable){if(typeof O._nocheck=="undefined"){O._nocheck=!!O.nocheck,O.nocheck=!0}O.check_Child_State=-1;M.repairParentChkClassWithSelf&&M.repairParentChkClassWithSelf(N,O)}},initShowForExCheck:function(O,P){if(!P.isHidden&&O.check&&O.check.enable){if(typeof P._nocheck!="undefined"){P.nocheck=P._nocheck,delete P._nocheck}if(M.setChkClass){var N=F(P,J.id.CHECK,O);M.setChkClass(O,N,P)}M.repairParentChkClassWithSelf&&M.repairParentChkClassWithSelf(O,P)}}}});var G=G.fn.zTree,K=G._z.tools,J=G.consts,M=G._z.view,L=G._z.data,F=K.$;L.addInitNode(function(O,P,N){if(typeof N.isHidden=="string"){N.isHidden=K.eqs(N.isHidden,"true")}N.isHidden=!!N.isHidden;L.initHideForExCheck(O,N)});L.addBeforeA(function(){});L.addZTreeTools(function(O,P){P.showNodes=function(R,Q){M.showNodes(O,R,Q)};P.showNode=function(R,Q){R&&M.showNodes(O,[R],Q)};P.hideNodes=function(R,Q){M.hideNodes(O,R,Q)};P.hideNode=function(R,Q){R&&M.hideNodes(O,[R],Q)};var N=P.checkNode;if(N){P.checkNode=function(S,R,T,Q){(!S||!S.isHidden)&&N.apply(P,arguments)}}});var H=L.initNode;L.initNode=function(S,T,R,N,U,Q,P){var O=(N?N:L.getRoot(S))[S.data.key.children];L.tmpHideFirstNode=M.setFirstNodeForHide(S,O);L.tmpHideLastNode=M.setLastNodeForHide(S,O);P&&(M.setNodeLineIcos(S,L.tmpHideFirstNode),M.setNodeLineIcos(S,L.tmpHideLastNode));U=L.tmpHideFirstNode===R;Q=L.tmpHideLastNode===R;H&&H.apply(L,arguments);P&&Q&&M.clearOldLastNode(S,R,P)};var I=L.makeChkFlag;if(I){L.makeChkFlag=function(N,O){(!O||!O.isHidden)&&I.apply(L,arguments)}}var C=L.getTreeCheckedNodes;if(C){L.getTreeCheckedNodes=function(Q,R,P,O){if(R&&R.length>0){var N=R[0].getParentNode();if(N&&N.isHidden){return[]}}return C.apply(L,arguments)}}var D=L.getTreeChangeCheckedNodes;if(D){L.getTreeChangeCheckedNodes=function(P,Q,O){if(Q&&Q.length>0){var N=Q[0].getParentNode();if(N&&N.isHidden){return[]}}return D.apply(L,arguments)}}var A=M.expandCollapseSonNode;if(A){M.expandCollapseSonNode=function(P,Q,O,N,R){(!Q||!Q.isHidden)&&A.apply(M,arguments)}}var B=M.setSonNodeCheckBox;if(B){M.setSonNodeCheckBox=function(P,Q,O,N){(!Q||!Q.isHidden)&&B.apply(M,arguments)}}var E=M.repairParentChkClassWithSelf;if(E){M.repairParentChkClassWithSelf=function(N,O){(!O||!O.isHidden)&&E.apply(M,arguments)}}})(jQuery);