(function(L){var C,D,A,K={event:{CHECK:"ztree_check"},id:{CHECK:"_check"},checkbox:{STYLE:"checkbox",DEFAULT:"chk",DISABLED:"disable",FALSE:"false",TRUE:"true",FULL:"full",PART:"part",FOCUS:"focus"},radio:{STYLE:"radio",TYPE_ALL:"all",TYPE_LEVEL:"level"}},E={check:{enable:!1,autoCheckTrigger:!1,chkStyle:K.checkbox.STYLE,nocheckInherit:!1,chkDisabledInherit:!1,radioType:K.radio.TYPE_LEVEL,chkboxType:{Y:"ps",N:"ps"}},data:{key:{checked:"checked"}},callback:{beforeCheck:null,onCheck:null}};C=function(Q,R){if(R.chkDisabled===!0){return !1}var P=N.getSetting(Q.data.treeId),O=P.data.key.checked;if(I.apply(P.callback.beforeCheck,[P.treeId,R],!0)==!1){return !0}R[O]=!R[O];J.checkNodeRelation(P,R);O=M(R,H.id.CHECK,P);J.setChkClass(P,O,R);J.repairParentChkClassWithSelf(P,R);P.treeObj.trigger(H.event.CHECK,[Q,P.treeId,R]);return !0};D=function(Q,R){if(R.chkDisabled===!0){return !1}var P=N.getSetting(Q.data.treeId),O=M(R,H.id.CHECK,P);R.check_Focus=!0;J.setChkClass(P,O,R);return !0};A=function(Q,R){if(R.chkDisabled===!0){return !1}var P=N.getSetting(Q.data.treeId),O=M(R,H.id.CHECK,P);R.check_Focus=!1;J.setChkClass(P,O,R);return !0};L.extend(!0,L.fn.zTree.consts,K);L.extend(!0,L.fn.zTree._z,{tools:{},view:{checkNodeRelation:function(S,T){var R,V,O,P=S.data.key.children,Q=S.data.key.checked;R=H.radio;if(S.check.chkStyle==R.STYLE){var U=N.getRadioCheckedList(S);if(T[Q]){if(S.check.radioType==R.TYPE_ALL){for(V=U.length-1;V>=0;V--){R=U[V],R[Q]&&R!=T&&(R[Q]=!1,U.splice(V,1),J.setChkClass(S,M(R,H.id.CHECK,S),R),R.parentTId!=T.parentTId&&J.repairParentChkClassWithSelf(S,R))}U.push(T)}else{U=T.parentTId?T.getParentNode():N.getRoot(S);for(V=0,O=U[P].length;V<O;V++){R=U[P][V],R[Q]&&R!=T&&(R[Q]=!1,J.setChkClass(S,M(R,H.id.CHECK,S),R))}}}else{if(S.check.radioType==R.TYPE_ALL){for(V=0,O=U.length;V<O;V++){if(T==U[V]){U.splice(V,1);break}}}}}else{T[Q]&&(!T[P]||T[P].length==0||S.check.chkboxType.Y.indexOf("s")>-1)&&J.setSonNodeCheckBox(S,T,!0),!T[Q]&&(!T[P]||T[P].length==0||S.check.chkboxType.N.indexOf("s")>-1)&&J.setSonNodeCheckBox(S,T,!1),T[Q]&&S.check.chkboxType.Y.indexOf("p")>-1&&J.setParentNodeCheckBox(S,T,!0),!T[Q]&&S.check.chkboxType.N.indexOf("p")>-1&&J.setParentNodeCheckBox(S,T,!1)}},makeChkClass:function(S,T){var R=S.data.key.checked,P=H.checkbox,O=H.radio,Q="",Q=T.chkDisabled===!0?P.DISABLED:T.halfCheck?P.PART:S.check.chkStyle==O.STYLE?T.check_Child_State<1?P.FULL:P.PART:T[R]?T.check_Child_State===2||T.check_Child_State===-1?P.FULL:P.PART:T.check_Child_State<1?P.FULL:P.PART,R=S.check.chkStyle+"_"+(T[R]?P.TRUE:P.FALSE)+"_"+Q,R=T.check_Focus&&T.chkDisabled!==!0?R+"_"+P.FOCUS:R;return H.className.BUTTON+" "+P.DEFAULT+" "+R},repairAllChk:function(S,T){if(S.check.enable&&S.check.chkStyle===H.checkbox.STYLE){for(var R=S.data.key.checked,V=S.data.key.children,O=N.getRoot(S),P=0,Q=O[V].length;P<Q;P++){var U=O[V][P];U.nocheck!==!0&&U.chkDisabled!==!0&&(U[R]=T);J.setSonNodeCheckBox(S,U,T)}}},repairChkClass:function(P,Q){if(Q&&(N.makeChkFlag(P,Q),Q.nocheck!==!0)){var O=M(Q,H.id.CHECK,P);J.setChkClass(P,O,Q)}},repairParentChkClass:function(P,Q){if(Q&&Q.parentTId){var O=Q.getParentNode();J.repairChkClass(P,O);J.repairParentChkClass(P,O)}},repairParentChkClassWithSelf:function(P,Q){if(Q){var O=P.data.key.children;Q[O]&&Q[O].length>0?J.repairParentChkClass(P,Q[O][0]):J.repairParentChkClass(P,Q)}},repairSonChkDisabled:function(T,U,S,P){if(U){var O=T.data.key.children;if(U.chkDisabled!=S){U.chkDisabled=S}J.repairChkClass(T,U);if(U[O]&&P){for(var Q=0,R=U[O].length;Q<R;Q++){J.repairSonChkDisabled(T,U[O][Q],S,P)}}}},repairParentChkDisabled:function(Q,R,P,O){if(R){if(R.chkDisabled!=P&&O){R.chkDisabled=P}J.repairChkClass(Q,R);J.repairParentChkDisabled(Q,R.getParentNode(),P,O)}},setChkClass:function(P,Q,O){Q&&(O.nocheck===!0?Q.hide():Q.show(),Q.attr("class",J.makeChkClass(P,O)))},setParentNodeCheckBox:function(T,U,S,W){var P=T.data.key.children,Q=T.data.key.checked,R=M(U,H.id.CHECK,T);W||(W=U);N.makeChkFlag(T,U);U.nocheck!==!0&&U.chkDisabled!==!0&&(U[Q]=S,J.setChkClass(T,R,U),T.check.autoCheckTrigger&&U!=W&&T.treeObj.trigger(H.event.CHECK,[null,T.treeId,U]));if(U.parentTId){R=!0;if(!S){for(var P=U.getParentNode()[P],V=0,O=P.length;V<O;V++){if(P[V].nocheck!==!0&&P[V].chkDisabled!==!0&&P[V][Q]||(P[V].nocheck===!0||P[V].chkDisabled===!0)&&P[V].check_Child_State>0){R=!1;break}}}R&&J.setParentNodeCheckBox(T,U.getParentNode(),S,W)}},setSonNodeCheckBox:function(V,W,U,Y){if(W){var P=V.data.key.children,Q=V.data.key.checked,S=M(W,H.id.CHECK,V);Y||(Y=W);var X=!1;if(W[P]){for(var O=0,T=W[P].length;O<T&&W.chkDisabled!==!0;O++){var R=W[P][O];J.setSonNodeCheckBox(V,R,U,Y);R.chkDisabled===!0&&(X=!0)}}if(W!=N.getRoot(V)&&W.chkDisabled!==!0){X&&W.nocheck!==!0&&N.makeChkFlag(V,W);if(W.nocheck!==!0&&W.chkDisabled!==!0){if(W[Q]=U,!X){W.check_Child_State=W[P]&&W[P].length>0?U?2:0:-1}}else{W.check_Child_State=-1}J.setChkClass(V,S,W);V.check.autoCheckTrigger&&W!=Y&&W.nocheck!==!0&&W.chkDisabled!==!0&&V.treeObj.trigger(H.event.CHECK,[null,V.treeId,W])}}}},event:{},data:{getRadioCheckedList:function(Q){for(var R=N.getRoot(Q).radioCheckedList,P=0,O=R.length;P<O;P++){N.getNodeCache(Q,R[P].tId)||(R.splice(P,1),P--,O--)}return R},getCheckStatus:function(P,Q){if(!P.check.enable||Q.nocheck||Q.chkDisabled){return null}var O=P.data.key.checked;return{checked:Q[O],half:Q.halfCheck?Q.halfCheck:P.check.chkStyle==H.radio.STYLE?Q.check_Child_State===2:Q[O]?Q.check_Child_State>-1&&Q.check_Child_State<2:Q.check_Child_State>0}},getTreeCheckedNodes:function(T,U,S,W){if(!U){return[]}for(var Q=T.data.key.children,R=T.data.key.checked,O=S&&T.check.chkStyle==H.radio.STYLE&&T.check.radioType==H.radio.TYPE_ALL,W=!W?[]:W,V=0,P=U.length;V<P;V++){if(U[V].nocheck!==!0&&U[V].chkDisabled!==!0&&U[V][R]==S&&(W.push(U[V]),O)){break}N.getTreeCheckedNodes(T,U[V][Q],S,W);if(O&&W.length>0){break}}return W},getTreeChangeCheckedNodes:function(T,U,S){if(!U){return[]}for(var P=T.data.key.children,O=T.data.key.checked,S=!S?[]:S,Q=0,R=U.length;Q<R;Q++){U[Q].nocheck!==!0&&U[Q].chkDisabled!==!0&&U[Q][O]!=U[Q].checkedOld&&S.push(U[Q]),N.getTreeChangeCheckedNodes(T,U[Q][P],S)}return S},makeChkFlag:function(S,T){if(T){var R=S.data.key.children,W=S.data.key.checked,P=-1;if(T[R]){for(var Q=0,O=T[R].length;Q<O;Q++){var V=T[R][Q],U=-1;if(S.check.chkStyle==H.radio.STYLE){if(U=V.nocheck===!0||V.chkDisabled===!0?V.check_Child_State:V.halfCheck===!0?2:V[W]?2:V.check_Child_State>0?2:0,U==2){P=2;break}else{U==0&&(P=0)}}else{if(S.check.chkStyle==H.checkbox.STYLE){if(U=V.nocheck===!0||V.chkDisabled===!0?V.check_Child_State:V.halfCheck===!0?1:V[W]?V.check_Child_State===-1||V.check_Child_State===2?2:1:V.check_Child_State>0?1:0,U===1){P=1;break}else{if(U===2&&P>-1&&Q>0&&U!==P){P=1;break}else{if(P===2&&U>-1&&U<2){P=1;break}else{U>-1&&(P=U)}}}}}}}T.check_Child_State=P}}}});var L=L.fn.zTree,I=L._z.tools,H=L.consts,J=L._z.view,N=L._z.data,M=I.$;N.exSetting(E);N.addInitBind(function(O){O.treeObj.bind(H.event.CHECK,function(S,R,Q,P){S.srcEvent=R;I.apply(O.callback.onCheck,[S,Q,P])})});N.addInitUnBind(function(O){O.treeObj.unbind(H.event.CHECK)});N.addInitCache(function(){});N.addInitNode(function(Q,R,P,O){if(P){R=Q.data.key.checked;typeof P[R]=="string"&&(P[R]=I.eqs(P[R],"true"));P[R]=!!P[R];P.checkedOld=P[R];if(typeof P.nocheck=="string"){P.nocheck=I.eqs(P.nocheck,"true")}P.nocheck=!!P.nocheck||Q.check.nocheckInherit&&O&&!!O.nocheck;if(typeof P.chkDisabled=="string"){P.chkDisabled=I.eqs(P.chkDisabled,"true")}P.chkDisabled=!!P.chkDisabled||Q.check.chkDisabledInherit&&O&&!!O.chkDisabled;if(typeof P.halfCheck=="string"){P.halfCheck=I.eqs(P.halfCheck,"true")}P.halfCheck=!!P.halfCheck;P.check_Child_State=-1;P.check_Focus=!1;P.getCheckStatus=function(){return N.getCheckStatus(Q,P)};Q.check.chkStyle==H.radio.STYLE&&Q.check.radioType==H.radio.TYPE_ALL&&P[R]&&N.getRoot(Q).radioCheckedList.push(P)}});N.addInitProxy(function(T){var U=T.target,S=N.getSetting(T.data.treeId),P="",O=null,Q="",R=null;if(I.eqs(T.type,"mouseover")){if(S.check.enable&&I.eqs(U.tagName,"span")&&U.getAttribute("treeNode"+H.id.CHECK)!==null){P=I.getNodeMainDom(U).id,Q="mouseoverCheck"}}else{if(I.eqs(T.type,"mouseout")){if(S.check.enable&&I.eqs(U.tagName,"span")&&U.getAttribute("treeNode"+H.id.CHECK)!==null){P=I.getNodeMainDom(U).id,Q="mouseoutCheck"}}else{if(I.eqs(T.type,"click")&&S.check.enable&&I.eqs(U.tagName,"span")&&U.getAttribute("treeNode"+H.id.CHECK)!==null){P=I.getNodeMainDom(U).id,Q="checkNode"}}}if(P.length>0){switch(O=N.getNodeCache(S,P),Q){case"checkNode":R=C;break;case"mouseoverCheck":R=D;break;case"mouseoutCheck":R=A}}return{stop:Q==="checkNode",node:O,nodeEventType:Q,nodeEventCallback:R,treeEventType:"",treeEventCallback:null}},!0);N.addInitRoot(function(O){N.getRoot(O).radioCheckedList=[]});N.addBeforeA(function(P,Q,O){P.check.enable&&(N.makeChkFlag(P,Q),O.push("<span ID='",Q.tId,H.id.CHECK,"' class='",J.makeChkClass(P,Q),"' treeNode",H.id.CHECK,Q.nocheck===!0?" style='display:none;'":"","></span>"))});N.addZTreeTools(function(P,Q){Q.checkNode=function(U,S,T,V){var R=this.setting.data.key.checked;if(U.chkDisabled!==!0&&(S!==!0&&S!==!1&&(S=!U[R]),V=!!V,(U[R]!==S||T)&&!(V&&I.apply(this.setting.callback.beforeCheck,[this.setting.treeId,U],!0)==!1)&&I.uCanDo(this.setting)&&this.setting.check.enable&&U.nocheck!==!0)){U[R]=S,S=M(U,H.id.CHECK,this.setting),(T||this.setting.check.chkStyle===H.radio.STYLE)&&J.checkNodeRelation(this.setting,U),J.setChkClass(this.setting,S,U),J.repairParentChkClassWithSelf(this.setting,U),V&&this.setting.treeObj.trigger(H.event.CHECK,[null,this.setting.treeId,U])}};Q.checkAllNodes=function(R){J.repairAllChk(this.setting,!!R)};Q.getCheckedNodes=function(S){var R=this.setting.data.key.children;return N.getTreeCheckedNodes(this.setting,N.getRoot(this.setting)[R],S!==!1)};Q.getChangeCheckedNodes=function(){var R=this.setting.data.key.children;return N.getTreeChangeCheckedNodes(this.setting,N.getRoot(this.setting)[R])};Q.setChkDisabled=function(T,R,S,U){R=!!R;S=!!S;J.repairSonChkDisabled(this.setting,T,R,!!U);J.repairParentChkDisabled(this.setting,T.getParentNode(),R,S)};var O=Q.updateNode;Q.updateNode=function(S,T){O&&O.apply(Q,arguments);if(S&&this.setting.check.enable&&M(S,this.setting).get(0)&&I.uCanDo(this.setting)){var R=M(S,H.id.CHECK,this.setting);(T==!0||this.setting.check.chkStyle===H.radio.STYLE)&&J.checkNodeRelation(this.setting,S);J.setChkClass(this.setting,R,S);J.repairParentChkClassWithSelf(this.setting,S)}}});var B=J.createNodes;J.createNodes=function(Q,R,P,O){B&&B.apply(J,arguments);P&&J.repairParentChkClassWithSelf(Q,O)};var F=J.removeNode;J.removeNode=function(P,Q){var O=Q.getParentNode();F&&F.apply(J,arguments);Q&&O&&(J.repairChkClass(P,O),J.repairParentChkClass(P,O))};var G=J.appendNodes;J.appendNodes=function(S,T,R,P,O,Q){var U="";G&&(U=G.apply(J,arguments));P&&N.makeChkFlag(S,P);return U}})(jQuery);