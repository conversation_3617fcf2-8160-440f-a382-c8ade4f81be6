(function(A){A.fn.bootstrapTable.locales["tr-TR"]={formatLoadingMessage:function(){return"<PERSON><PERSON><PERSON><PERSON>yo<PERSON>, lütfen bekleyin..."},formatRecordsPerPage:function(B){return"<PERSON><PERSON> başına "+B+" kayıt."},formatShowingRows:function(B,C,D){return D+" kayıttan "+B+"-"+C+" arası gösteriliyor."},formatSearch:function(){return"Ara"},formatNoMatches:function(){return"Eşleşen kayıt bulunamadı."},formatRefresh:function(){return"Yenile"},formatToggle:function(){return"Değiştir"},formatColumns:function(){return"Sütunlar"},formatAllRows:function(){return"Tüm Satırlar"}};A.extend(A.fn.bootstrapTable.defaults,A.fn.bootstrapTable.locales["tr-TR"])})(jQuery);