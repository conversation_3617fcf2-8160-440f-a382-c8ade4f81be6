!function(A){A.fn.bootstrapTable.locales["it-IT"]={formatLoadingMessage:function(){return"Caricamento in corso..."},formatRecordsPerPage:function(B){return B+" elementi per pagina"},formatShowingRows:function(D,B,C){return"Pagina "+D+" di "+B+" ("+C+" elementi)"},formatSearch:function(){return"Cerca"},formatNoMatches:function(){return"Nessun elemento trovato"},formatRefresh:function(){return"Aggiorna"},formatToggle:function(){return"Alterna"},formatColumns:function(){return"Colonne"},formatAllRows:function(){return"Tutto"}},A.extend(A.fn.bootstrapTable.defaults,A.fn.bootstrapTable.locales["it-IT"])}(jQuery);