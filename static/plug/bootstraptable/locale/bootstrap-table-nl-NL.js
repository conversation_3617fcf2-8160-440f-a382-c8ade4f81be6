(function(A){A.fn.bootstrapTable.locales["nl-NL"]={formatLoadingMessage:function(){return"Laden, even geduld..."},formatRecordsPerPage:function(B){return B+" records per pagina"},formatShowingRows:function(B,C,D){return"Toon "+B+" tot "+C+" van "+D+" record"+((D>1)?"s":"")},formatDetailPagination:function(B){return"Toon "+B+" record"+((B>1)?"s":"")},formatSearch:function(){return"Zoeken"},formatNoMatches:function(){return"Geen resultaten gevonden"},formatRefresh:function(){return"Vernieuwen"},formatToggle:function(){return"Omschakelen"},formatColumns:function(){return"Kolommen"},formatAllRows:function(){return"Alle"},formatPaginationSwitch:function(){return"Verberg/Toon paginatie"},formatExport:function(){return"Exporteer data"},formatClearFilters:function(){return"Verwijder filters"}};A.extend(A.fn.bootstrapTable.defaults,A.fn.bootstrapTable.locales["nl-NL"])})(jQuery);