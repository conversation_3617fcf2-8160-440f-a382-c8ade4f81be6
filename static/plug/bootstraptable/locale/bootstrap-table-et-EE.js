(function(A){A.fn.bootstrapTable.locales["et-EE"]={formatLoadingMessage:function(){return"Päring käib, palun oota..."},formatRecordsPerPage:function(B){return B+" rida lehe kohta"},formatShowingRows:function(B,C,D){return"Näitan tulemusi "+B+" kuni "+C+" - kokku "+D+" tulemust"},formatSearch:function(){return"Otsi"},formatNoMatches:function(){return"Päringu tingimustele ei vastanud ühtegi tulemust"},formatPaginationSwitch:function(){return"Näita/Peida lehtedeks jagamine"},formatRefresh:function(){return"Värskenda"},formatToggle:function(){return"<PERSON>ü<PERSON>a"},formatColumns:function(){return"Veerud"},formatAllRows:function(){return"Kõik"}};A.extend(A.fn.bootstrapTable.defaults,A.fn.bootstrapTable.locales["et-EE"])})(jQuery);