(function(A){A.fn.bootstrapTable.locales["ko-KR"]={formatLoadingMessage:function(){return"데이터를 불러오는 중입니다..."},formatRecordsPerPage:function(B){return"페이지 당 "+B+"개 데이터 출력"},formatShowingRows:function(B,C,D){return"전체 "+D+"개 중 "+B+"~"+C+"번째 데이터 출력,"},formatSearch:function(){return"검색"},formatNoMatches:function(){return"조회된 데이터가 없습니다."},formatRefresh:function(){return"새로 고침"},formatToggle:function(){return"전환"},formatColumns:function(){return"컬럼 필터링"}};A.extend(A.fn.bootstrapTable.defaults,A.fn.bootstrapTable.locales["ko-KR"])})(jQuery);