(function(A){A.fn.bootstrapTable.locales["de-DE"]={formatLoadingMessage:function(){return"Lade, bitte warten..."},formatRecordsPerPage:function(B){return B+" Einträge pro Seite"},formatShowingRows:function(B,C,D){return"Zeige "+B+" bis "+C+" von "+D+" Zeile"+((D>1)?"n":"")},formatSearch:function(){return"Suchen"},formatNoMatches:function(){return"Keine passenden Ergebnisse gefunden"},formatRefresh:function(){return"Neu laden"},formatToggle:function(){return"Umschalten"},formatColumns:function(){return"Spalten"}};A.extend(A.fn.bootstrapTable.defaults,A.fn.bootstrapTable.locales["de-DE"])})(jQuery);