!function(E){var C=E.fn.bootstrapTable.utils.calculateObjectValue,D=(E.fn.bootstrapTable.utils.sprintf,function(G){var H=document.createElement("textarea");E(H).html(G),document.body.appendChild(H),H.select();try{document.execCommand("copy")}catch(F){console.log("Oops, unable to copy")}E(H).remove()});E.extend(E.fn.bootstrapTable.defaults,{copyBtn:!1,copyWHiddenBtn:!1,copyDelemeter:", "}),E.fn.bootstrapTable.methods.push("copyColumnsToClipboard","copyColumnsToClipboardWithHidden");var A=E.fn.bootstrapTable.Constructor,B=A.prototype.initToolbar;A.prototype.initToolbar=function(){B.apply(this,Array.prototype.slice.apply(arguments));var I=this,G=this.$toolbar.find(">.btn-group");if(this.options.clickToSelect||this.options.singleSelect){if(this.options.copyBtn){var H="<button class='btn btn-default' id='copyBtn'><span class='glyphicon glyphicon-copy icon-pencil'></span></button>";G.append(H),G.find("#copyBtn").click(function(){I.copyColumnsToClipboard()})}if(this.options.copyWHiddenBtn){var F="<button class='btn btn-default' id='copyWHiddenBtn'><span class='badge'><span class='glyphicon glyphicon-copy icon-pencil'></span></span></button>";G.append(F),G.find("#copyWHiddenBtn").click(function(){I.copyColumnsToClipboardWithHidden()})}}},A.prototype.copyColumnsToClipboard=function(){var F=this,G="",H=this.options.copyDelemeter;E.each(F.getSelections(),function(J,I){E.each(F.options.columns[0],function(L,K){"state"!==K.field&&"RowNumber"!==K.field&&K.visible&&(null!==I[K.field]&&(G+=C(K,F.header.formatters[L],[I[K.field],I,J],I[K.field])),G+=H)}),G+="\r\n"}),D(G)},A.prototype.copyColumnsToClipboardWithHidden=function(){var F=this,G="",H=this.options.copyDelemeter;E.each(F.getSelections(),function(J,I){E.each(F.options.columns[0],function(L,K){"state"!=K.field&&"RowNumber"!==K.field&&(null!==I[K.field]&&(G+=C(K,F.header.formatters[L],[I[K.field],I,J],I[K.field])),G+=H)}),G+="\r\n"}),D(G)}}(jQuery);