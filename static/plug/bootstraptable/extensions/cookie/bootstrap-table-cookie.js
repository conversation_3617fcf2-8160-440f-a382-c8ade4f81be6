(function(U){var F={sortOrder:"bs.table.sortOrder",sortName:"bs.table.sortName",pageNumber:"bs.table.pageNumber",pageList:"bs.table.pageList",columns:"bs.table.columns",searchText:"bs.table.searchText",filterControl:"bs.table.filterControl"};var Q=function(a){var b=a.$header;if(a.options.height){b=a.$tableHeader}return b};var Y=function(a){var b="select, input";if(a.options.height){b="table select, table input"}return b};var G=function(){return !!(navigator.cookieEnabled)};var T=function(c,d){var b=-1;for(var a=0;a<d.length;a++){if(c.toLowerCase()===d[a].toLowerCase()){b=a;break}}return b};var Z=function(b,c,a){if((!b.options.cookie)||(!G())||(b.options.cookieIdTable==="")){return}if(T(c,b.options.cookiesEnabled)===-1){return}c=b.options.cookieIdTable+"."+c;if(!c||/^(?:expires|max\-age|path|domain|secure)$/i.test(c)){return false}document.cookie=encodeURIComponent(c)+"="+encodeURIComponent(a)+X(b.options.cookieExpire)+(b.options.cookieDomain?"; domain="+b.options.cookieDomain:"")+(b.options.cookiePath?"; path="+b.options.cookiePath:"")+(b.cookieSecure?"; secure":"");return true};var M=function(b,a,c){if(!c){return null}if(T(c,b.options.cookiesEnabled)===-1){return null}c=a+"."+c;return decodeURIComponent(document.cookie.replace(new RegExp("(?:(?:^|.*;)\\s*"+encodeURIComponent(c).replace(/[\-\.\+\*]/g,"\\$&")+"\\s*\\=\\s*([^;]*).*$)|^.*$"),"$1"))||null};var K=function(a){if(!a){return false}return(new RegExp("(?:^|;\\s*)"+encodeURIComponent(a).replace(/[\-\.\+\*]/g,"\\$&")+"\\s*\\=")).test(document.cookie)};var H=function(a,c,d,b){c=a+"."+c;if(!K(c)){return false}document.cookie=encodeURIComponent(c)+"=; expires=Thu, 01 Jan 1970 00:00:00 GMT"+(b?"; domain="+b:"")+(d?"; path="+d:"");return true};var X=function(a){var b=a.replace(/[0-9]*/,"");a=a.replace(/[A-Za-z]/,"");switch(b.toLowerCase()){case"s":a=+a;break;case"mi":a=a*60;break;case"h":a=a*60*60;break;case"d":a=a*24*60*60;break;case"m":a=a*30*24*60*60;break;case"y":a=a*365*24*60*60;break;default:a=undefined;break}return a===undefined?"":"; max-age="+a};var S=function(a){setTimeout(function(){var f=JSON.parse(M(a,a.options.cookieIdTable,F.filterControl));if(!a.options.filterControlValuesLoaded&&f){a.options.filterControlValuesLoaded=true;var d={},c=Q(a),e=Y(a),b=function(h,g){U(g).each(function(j,k){U(h).val(k.text);d[k.field]=k.text})};c.find(e).each(function(){var h=U(this).closest("[data-field]").data("field"),g=U.grep(f,function(i){return i.field===h});b(this,g)});a.initColumnSearch(d)}},250)};U.extend(U.fn.bootstrapTable.defaults,{cookie:false,cookieExpire:"2h",cookiePath:null,cookieDomain:null,cookieSecure:null,cookieIdTable:"",cookiesEnabled:["bs.table.sortOrder","bs.table.sortName","bs.table.pageNumber","bs.table.pageList","bs.table.columns","bs.table.searchText","bs.table.filterControl"],filterControls:[],filterControlValuesLoaded:false});U.fn.bootstrapTable.methods.push("deleteCookie");var N=U.fn.bootstrapTable.Constructor,V=N.prototype.init,A=N.prototype.initTable,R=N.prototype.initServer,B=N.prototype.onSort,D=N.prototype.onPageNumber,O=N.prototype.onPageListChange,C=N.prototype.onPageFirst,L=N.prototype.onPagePre,E=N.prototype.onPageNext,I=N.prototype.onPageLast,J=N.prototype.toggleColumn,W=N.prototype.selectPage,P=N.prototype.onSearch;N.prototype.init=function(){var b=0;this.options.filterControls=[];this.options.filterControlValuesLoaded=false;this.options.cookiesEnabled=typeof this.options.cookiesEnabled==="string"?this.options.cookiesEnabled.replace("[","").replace("]","").replace(/ /g,"").toLowerCase().split(","):this.options.cookiesEnabled;if(this.options.filterControl){var a=this;this.$el.on("column-search.bs.table",function(d,g,f){var h=true;for(var c=0;c<a.options.filterControls.length;c++){if(a.options.filterControls[c].field===g){a.options.filterControls[c].text=f;h=false;break}}if(h){a.options.filterControls.push({field:g,text:f})}Z(a,F.filterControl,JSON.stringify(a.options.filterControls))}).on("post-body.bs.table",S(a))}V.apply(this,Array.prototype.slice.apply(arguments));U.extend(U.fn.bootstrapTable.utils,{setCookie:Z,getCookie:M})};N.prototype.initServer=function(){var a=this,d=[],c=function(f){return f.filterControl&&f.filterControl==="select"},b=function(f){return f.filterData&&f.filterData!=="column"},e=function(){return a.options.cookie&&a.getCookies(a)};d=U.grep(a.columns,function(f){return c(f)&&!b(f)});N.prototype.initServer=R;if(e()&&d.length===0){return}R.apply(this,Array.prototype.slice.apply(arguments))};N.prototype.initTable=function(){A.apply(this,Array.prototype.slice.apply(arguments));this.initCookie()};N.prototype.initCookie=function(){if(!this.options.cookie){return}if((this.options.cookieIdTable==="")||(this.options.cookieExpire==="")||(!G())){throw new Error("Configuration error. Please review the cookieIdTable, cookieExpire properties, if those properties are ok, then this browser does not support the cookies");return}var b=M(this,this.options.cookieIdTable,F.sortOrder),a=M(this,this.options.cookieIdTable,F.sortName),d=M(this,this.options.cookieIdTable,F.pageNumber),c=M(this,this.options.cookieIdTable,F.pageList),e=JSON.parse(M(this,this.options.cookieIdTable,F.columns)),f=M(this,this.options.cookieIdTable,F.searchText);this.options.sortOrder=b?b:this.options.sortOrder;this.options.sortName=a?a:this.options.sortName;this.options.pageNumber=d?+d:this.options.pageNumber;this.options.pageSize=c?c===this.options.formatAllRows()?c:+c:this.options.pageSize;this.options.searchText=f?f:"";if(e){U.each(this.columns,function(g,h){h.visible=U.inArray(h.field,e)!==-1})}};N.prototype.onSort=function(){B.apply(this,Array.prototype.slice.apply(arguments));Z(this,F.sortOrder,this.options.sortOrder);Z(this,F.sortName,this.options.sortName)};N.prototype.onPageNumber=function(){D.apply(this,Array.prototype.slice.apply(arguments));Z(this,F.pageNumber,this.options.pageNumber)};N.prototype.onPageListChange=function(){O.apply(this,Array.prototype.slice.apply(arguments));Z(this,F.pageList,this.options.pageSize)};N.prototype.onPageFirst=function(){C.apply(this,Array.prototype.slice.apply(arguments));Z(this,F.pageNumber,this.options.pageNumber)};N.prototype.onPagePre=function(){L.apply(this,Array.prototype.slice.apply(arguments));Z(this,F.pageNumber,this.options.pageNumber)};N.prototype.onPageNext=function(){E.apply(this,Array.prototype.slice.apply(arguments));Z(this,F.pageNumber,this.options.pageNumber)};N.prototype.onPageLast=function(){I.apply(this,Array.prototype.slice.apply(arguments));Z(this,F.pageNumber,this.options.pageNumber)};N.prototype.toggleColumn=function(){J.apply(this,Array.prototype.slice.apply(arguments));var a=[];U.each(this.columns,function(b,c){if(c.visible){a.push(c.field)}});Z(this,F.columns,JSON.stringify(a))};N.prototype.selectPage=function(a){W.apply(this,Array.prototype.slice.apply(arguments));Z(this,F.pageNumber,a)};N.prototype.onSearch=function(){var a=Array.prototype.slice.apply(arguments);P.apply(this,a);if(U(a[0].currentTarget).parent().hasClass("search")){Z(this,F.searchText,this.searchText)}};N.prototype.getCookies=function(a){var b=[];U.each(F,function(c,d){var e=JSON.parse(M(a,a.options.cookieIdTable,F.filterControl));b.concat(e)});return b};N.prototype.deleteCookie=function(a){if((a==="")||(!G())){return}H(this.options.cookieIdTable,F[a],this.options.cookiePath,this.options.cookieDomain)}})(jQuery);