!function(S){var Q={sortOrder:"bs.table.sortOrder",sortName:"bs.table.sortName",pageNumber:"bs.table.pageNumber",pageList:"bs.table.pageList",columns:"bs.table.columns",searchText:"bs.table.searchText",filterControl:"bs.table.filterControl"},R=function(d){var c=d.$header;return d.options.height&&(c=d.$tableHeader),c},V=function(d){var c="select, input";return d.options.height&&(c="table select, table input"),c},W=function(){return !!navigator.cookieEnabled},T=function(h,f){for(var g=-1,e=0;e<f.length;e++){if(h.toLowerCase()===f[e].toLowerCase()){g=e;break}}return g},U=function(f,d,e){return f.options.cookie&&W()&&""!==f.options.cookieIdTable&&-1!==T(d,f.options.cookiesEnabled)?(d=f.options.cookieIdTable+"."+d,!d||/^(?:expires|max\-age|path|domain|secure)$/i.test(d)?!1:(document.cookie=encodeURIComponent(d)+"="+encodeURIComponent(e)+J(f.options.cookieExpire)+(f.options.cookieDomain?"; domain="+f.options.cookieDomain:"")+(f.options.cookiePath?"; path="+f.options.cookiePath:"")+(f.cookieSecure?"; secure":""),!0)):void 0},K=function(f,d,e){return e?-1===T(e,f.options.cookiesEnabled)?null:(e=d+"."+e,decodeURIComponent(document.cookie.replace(new RegExp("(?:(?:^|.*;)\\s*"+encodeURIComponent(e).replace(/[\-\.\+\*]/g,"\\$&")+"\\s*\\=\\s*([^;]*).*$)|^.*$"),"$1"))||null):null},L=function(b){return b?new RegExp("(?:^|;\\s*)"+encodeURIComponent(b).replace(/[\-\.\+\*]/g,"\\$&")+"\\s*\\=").test(document.cookie):!1},I=function(h,f,g,e){return f=h+"."+f,L(f)?(document.cookie=encodeURIComponent(f)+"=; expires=Thu, 01 Jan 1970 00:00:00 GMT"+(e?"; domain="+e:"")+(g?"; path="+g:""),!0):!1},J=function(d){var c=d.replace(/[0-9]*/,"");switch(d=d.replace(/[A-Za-z]/,""),c.toLowerCase()){case"s":d=+d;break;case"mi":d=60*d;break;case"h":d=60*d*60;break;case"d":d=24*d*60*60;break;case"m":d=30*d*24*60*60;break;case"y":d=365*d*24*60*60;break;default:d=void 0}return void 0===d?"":"; max-age="+d},O=function(a){setTimeout(function(){var h=JSON.parse(K(a,a.options.cookieIdTable,Q.filterControl));if(!a.options.filterControlValuesLoaded&&h){a.options.filterControlValuesLoaded=!0;var b={},c=R(a),e=V(a),d=function(f,g){S(g).each(function(j,i){S(f).val(i.text),b[i.field]=i.text})};c.find(e).each(function(){var f=S(this).closest("[data-field]").data("field"),g=S.grep(h,function(i){return i.field===f});d(this,g)}),a.initColumnSearch(b)}},250)};S.extend(S.fn.bootstrapTable.defaults,{cookie:!1,cookieExpire:"2h",cookiePath:null,cookieDomain:null,cookieSecure:null,cookieIdTable:"",cookiesEnabled:["bs.table.sortOrder","bs.table.sortName","bs.table.pageNumber","bs.table.pageList","bs.table.columns","bs.table.searchText","bs.table.filterControl"],filterControls:[],filterControlValuesLoaded:!1}),S.fn.bootstrapTable.methods.push("deleteCookie");var P=S.fn.bootstrapTable.Constructor,M=P.prototype.init,N=P.prototype.initTable,C=P.prototype.initServer,D=P.prototype.onSort,A=P.prototype.onPageNumber,B=P.prototype.onPageListChange,G=P.prototype.onPageFirst,H=P.prototype.onPagePre,E=P.prototype.onPageNext,F=P.prototype.onPageLast,Y=P.prototype.toggleColumn,Z=P.prototype.selectPage,X=P.prototype.onSearch;P.prototype.init=function(){if(this.options.filterControls=[],this.options.filterControlValuesLoaded=!1,this.options.cookiesEnabled="string"==typeof this.options.cookiesEnabled?this.options.cookiesEnabled.replace("[","").replace("]","").replace(/ /g,"").toLowerCase().split(","):this.options.cookiesEnabled,this.options.filterControl){var a=this;this.$el.on("column-search.bs.table",function(i,c,g){for(var j=!0,b=0;b<a.options.filterControls.length;b++){if(a.options.filterControls[b].field===c){a.options.filterControls[b].text=g,j=!1;break}}j&&a.options.filterControls.push({field:c,text:g}),U(a,Q.filterControl,JSON.stringify(a.options.filterControls))}).on("post-body.bs.table",O(a))}M.apply(this,Array.prototype.slice.apply(arguments)),S.extend(S.fn.bootstrapTable.utils,{setCookie:U,getCookie:K})},P.prototype.initServer=function(){var h=this,i=[],a=function(b){return b.filterControl&&"select"===b.filterControl},g=function(b){return b.filterData&&"column"!==b.filterData},j=function(){return h.options.cookie&&h.getCookies(h)};i=S.grep(h.columns,function(b){return a(b)&&!g(b)}),P.prototype.initServer=C,j()&&0===i.length||C.apply(this,Array.prototype.slice.apply(arguments))},P.prototype.initTable=function(){N.apply(this,Array.prototype.slice.apply(arguments)),this.initCookie()},P.prototype.initCookie=function(){if(this.options.cookie){if(""===this.options.cookieIdTable||""===this.options.cookieExpire||!W()){throw new Error("Configuration error. Please review the cookieIdTable, cookieExpire properties, if those properties are ok, then this browser does not support the cookies")}var h=K(this,this.options.cookieIdTable,Q.sortOrder),b=K(this,this.options.cookieIdTable,Q.sortName),l=K(this,this.options.cookieIdTable,Q.pageNumber),a=K(this,this.options.cookieIdTable,Q.pageList),e=JSON.parse(K(this,this.options.cookieIdTable,Q.columns)),k=K(this,this.options.cookieIdTable,Q.searchText);this.options.sortOrder=h?h:this.options.sortOrder,this.options.sortName=b?b:this.options.sortName,this.options.pageNumber=l?+l:this.options.pageNumber,this.options.pageSize=a?a===this.options.formatAllRows()?a:+a:this.options.pageSize,this.options.searchText=k?k:"",e&&S.each(this.columns,function(d,f){f.visible=-1!==S.inArray(f.field,e)})}},P.prototype.onSort=function(){D.apply(this,Array.prototype.slice.apply(arguments)),U(this,Q.sortOrder,this.options.sortOrder),U(this,Q.sortName,this.options.sortName)},P.prototype.onPageNumber=function(){A.apply(this,Array.prototype.slice.apply(arguments)),U(this,Q.pageNumber,this.options.pageNumber)},P.prototype.onPageListChange=function(){B.apply(this,Array.prototype.slice.apply(arguments)),U(this,Q.pageList,this.options.pageSize)},P.prototype.onPageFirst=function(){G.apply(this,Array.prototype.slice.apply(arguments)),U(this,Q.pageNumber,this.options.pageNumber)},P.prototype.onPagePre=function(){H.apply(this,Array.prototype.slice.apply(arguments)),U(this,Q.pageNumber,this.options.pageNumber)},P.prototype.onPageNext=function(){E.apply(this,Array.prototype.slice.apply(arguments)),U(this,Q.pageNumber,this.options.pageNumber)},P.prototype.onPageLast=function(){F.apply(this,Array.prototype.slice.apply(arguments)),U(this,Q.pageNumber,this.options.pageNumber)},P.prototype.toggleColumn=function(){Y.apply(this,Array.prototype.slice.apply(arguments));var a=[];S.each(this.columns,function(d,c){c.visible&&a.push(c.field)}),U(this,Q.columns,JSON.stringify(a))},P.prototype.selectPage=function(b){Z.apply(this,Array.prototype.slice.apply(arguments)),U(this,Q.pageNumber,b)},P.prototype.onSearch=function(){var a=Array.prototype.slice.apply(arguments);X.apply(this,a),S(a[0].currentTarget).parent().hasClass("search")&&U(this,Q.searchText,this.searchText)},P.prototype.getCookies=function(b){var a=[];return S.each(Q,function(){var c=JSON.parse(K(b,b.options.cookieIdTable,Q.filterControl));a.concat(c)}),a},P.prototype.deleteCookie=function(b){""!==b&&W()&&I(this.options.cookieIdTable,Q[b],this.options.cookiePath,this.options.cookieDomain)}}(jQuery);