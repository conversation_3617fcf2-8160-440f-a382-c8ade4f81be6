!function(E){var C=E.fn.bootstrapTable.utils.sprintf;var A=function(F){F.initHeader();F.initSearch();F.initPagination();F.initBody()};E.extend(E.fn.bootstrapTable.defaults,{showToggleBtn:false,multiToggleDefaults:[],});E.fn.bootstrapTable.methods.push("hideAllColumns","showAllColumns");var D=E.fn.bootstrapTable.Constructor,B=D.prototype.initToolbar;D.prototype.initToolbar=function(){B.apply(this,Array.prototype.slice.apply(arguments));var F=this,H=this.$toolbar.find(">.btn-group");if(typeof this.options.multiToggleDefaults==="string"){this.options.multiToggleDefaults=JSON.parse(this.options.multiToggleDefaults)}if(this.options.showToggleBtn&&this.options.showColumns){var I="<button class='btn btn-default hidden' id='showAllBtn'><span class='glyphicon glyphicon-resize-full icon-zoom-in'></span></button>",G="<button class='btn btn-default' id='hideAllBtn'><span class='glyphicon glyphicon-resize-small icon-zoom-out'></span></button>";H.append(I+G);H.find("#showAllBtn").click(function(){F.showAllColumns();H.find("#hideAllBtn").toggleClass("hidden");H.find("#showAllBtn").toggleClass("hidden")});H.find("#hideAllBtn").click(function(){F.hideAllColumns();H.find("#hideAllBtn").toggleClass("hidden");H.find("#showAllBtn").toggleClass("hidden")})}};D.prototype.hideAllColumns=function(){var F=this,G=F.options.multiToggleDefaults;E.each(this.columns,function(I,J){if(G.indexOf(J.field)==-1&&J.switchable){J.visible=false;var H=F.$toolbar.find(".keep-open input").prop("disabled",false);H.filter(C('[value="%s"]',I)).prop("checked",false)}});A(F)};D.prototype.showAllColumns=function(){var F=this;E.each(this.columns,function(H,I){if(I.switchable){I.visible=true}var G=F.$toolbar.find(".keep-open input").prop("disabled",false);G.filter(C('[value="%s"]',H)).prop("checked",true)});A(F);F.toggleColumn(0,F.columns[0].visible,false)}}(jQuery);