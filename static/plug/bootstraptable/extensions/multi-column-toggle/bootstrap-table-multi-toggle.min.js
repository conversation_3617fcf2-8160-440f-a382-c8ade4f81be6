!function(E){var C=E.fn.bootstrapTable.utils.sprintf,D=function(F){F.init<PERSON>ead<PERSON>(),<PERSON><PERSON>init<PERSON>earch(),F.initPagination(),F.initBody()};E.extend(E.fn.bootstrapTable.defaults,{showToggleBtn:!1,multiToggleDefaults:[]}),E.fn.bootstrapTable.methods.push("hideAllColumns","showAllColumns");var A=E.fn.bootstrapTable.Constructor,B=A.prototype.initToolbar;A.prototype.initToolbar=function(){B.apply(this,Array.prototype.slice.apply(arguments));var I=this,G=this.$toolbar.find(">.btn-group");if("string"==typeof this.options.multiToggleDefaults&&(this.options.multiToggleDefaults=JSON.parse(this.options.multiToggleDefaults)),this.options.showToggleBtn&&this.options.showColumns){var H="<button class='btn btn-default hidden' id='showAllBtn'><span class='glyphicon glyphicon-resize-full icon-zoom-in'></span></button>",F="<button class='btn btn-default' id='hideAllBtn'><span class='glyphicon glyphicon-resize-small icon-zoom-out'></span></button>";G.append(H+F),G.find("#showAllBtn").click(function(){I.showAllColumns(),G.find("#hideAllBtn").toggleClass("hidden"),G.find("#showAllBtn").toggleClass("hidden")}),G.find("#hideAllBtn").click(function(){I.hideAllColumns(),G.find("#hideAllBtn").toggleClass("hidden"),G.find("#showAllBtn").toggleClass("hidden")})}},A.prototype.hideAllColumns=function(){var F=this,G=F.options.multiToggleDefaults;E.each(this.columns,function(I,H){if(-1==G.indexOf(H.field)&&H.switchable){H.visible=!1;var J=F.$toolbar.find(".keep-open input").prop("disabled",!1);J.filter(C('[value="%s"]',I)).prop("checked",!1)}}),D(F)},A.prototype.showAllColumns=function(){var F=this;E.each(this.columns,function(I,H){H.switchable&&(H.visible=!0);var G=F.$toolbar.find(".keep-open input").prop("disabled",!1);G.filter(C('[value="%s"]',I)).prop("checked",!0)}),D(F),F.toggleColumn(0,F.columns[0].visible,!1)}}(jQuery);