!function(S){var Q=S.fn.bootstrapTable.utils.sprintf,R=S.fn.bootstrapTable.utils.objectKeys,V=function(e,f,a){f=S.trim(f),e=S(e.get(e.length-1)),T(e,f)||e.append(S("<option></option>").attr("value",f).text(S("<div />").html(a).text()))},W=function(a){var d=a.find("option:gt(0)");d.sort(function(e,f){return e=S(e).text().toLowerCase(),f=S(f).text().toLowerCase(),S.isNumeric(e)&&S.isNumeric(f)&&(e=parseFloat(e),f=parseFloat(f)),e>f?1:f>e?-1:0}),a.find("option:gt(0)").remove(),a.append(d)},T=function(h,f){for(var g=h.get(h.length-1).options,e=0;e<g.length;e++){if(g[e].value===f.toString()){return !0}}return !1},U=function(b){b.$tableHeader.css("height","77px")},K=function(d){var c=d.$header;return d.options.height&&(c=d.$tableHeader),c},L=function(d){var c="select, input";return d.options.height&&(c="table select, table input"),c},I=function(g){if(S.fn.bootstrapTable.utils.isIEBrowser()){if(S(g).is("input")){var h=0;if("selectionStart" in g){h=g.selectionStart}else{if("selection" in document){g.focus();var a=document.selection.createRange(),f=document.selection.createRange().text.length;a.moveStart("character",-g.value.length),h=a.text.length-f}}return h}return -1}return -1},J=function(a,d){S.fn.bootstrapTable.utils.isIEBrowser()&&(void 0!==a.setSelectionRange?a.setSelectionRange(d,d):S(a).val(a.value))},O=function(e){var f=K(e),a=L(e);e.options.valuesFilterControl=[],f.find(a).each(function(){e.options.valuesFilterControl.push({field:S(this).closest("[data-field]").data("field"),value:S(this).val(),position:I(S(this).get(0))})})},P=function(h){var i=null,a=[],g=K(h),j=L(h);h.options.valuesFilterControl.length>0&&g.find(j).each(function(){i=S(this).closest("[data-field]").data("field"),a=S.grep(h.options.valuesFilterControl,function(b){return b.field===i}),a.length>0&&(S(this).val(a[0].value),J(S(this).get(0),a[0].position))})},M=function(){var a=[],d=document.cookie.match(/(?:bs.table.)(\w*)/g);return d?(S.each(d,function(e,b){/./.test(b)&&(b=b.split(".").pop()),-1===S.inArray(b,a)&&a.push(b)}),a):void 0},N=function(j){var k=j.options.data,l=(j.pageTo<j.options.data.length?j.options.data.length:j.pageTo,function(b){return b.filterControl&&"select"===b.filterControl.toLowerCase()&&b.searchable}),a=function(b){return void 0===b.filterData||"column"===b.filterData.toLowerCase()},d=function(b){return b&&b.length>0},e=j.options.pagination?"server"===j.options.sidePagination?j.pageTo:j.options.totalRows:j.pageTo;S.each(j.header.fields,function(g,h){var t=j.columns[S.fn.bootstrapTable.utils.getFieldIndex(j.columns,h)],u=S(".bootstrap-table-filter-control-"+C(t.field));if(l(t)&&a(t)&&d(u)){0===u.get(u.length-1).options.length&&V(u,"","");for(var i={},p=0;e>p;p++){var f=k[p][h],b=S.fn.bootstrapTable.utils.calculateObjectValue(j.header,j.header.formatters[g],[f,k[p],p],f);i[b]=f}for(var c in i){V(u,i[c],c)}W(u)}})},C=function(b){return String(b).replace(/(:|\.|\[|\]|,)/g,"\\$1")},D=function(j,k){var l,a,d=!1,e=0;S.each(j.columns,function(g,c){if(l="hidden",a=[],c.visible){if(c.filterControl){a.push('<div style="margin: 0 2px 2px 2px;" class="filterControl">');var f=c.filterControl.toLowerCase();c.searchable&&j.options.filterTemplate[f]&&(d=!0,l="visible",a.push(j.options.filterTemplate[f](j,c.field,l)))}else{a.push('<div style="height: 34px;"></div>')}if(S.each(k.children().children(),function(i,m){return m=S(m),m.data("field")===c.field?(m.find(".fht-cell").append(a.join("")),!1):void 0}),void 0!==c.filterData&&"column"!==c.filterData.toLowerCase()){var r,s,h=G(B,c.filterData.substring(0,c.filterData.indexOf(":")));if(null===h){throw new SyntaxError('Error. You should use any of these allowed filter data methods: var, json, url. Use like this: var: {key: "value"}')}r=c.filterData.substring(c.filterData.indexOf(":")+1,c.filterData.length),s=S(".bootstrap-table-filter-control-"+C(c.field)),V(s,"",""),h(r,s);var p,b;switch(h){case"url":S.ajax({url:r,dataType:"json",success:function(m){for(var i in m){V(s,i,m[i])}W(s)}});break;case"var":p=window[r];for(b in p){V(s,b,p[b])}W(s);break;case"jso":p=JSON.parse(r);for(b in p){V(s,b,p[b])}W(s)}}}}),d?(k.off("keyup","input").on("keyup","input",function(b){clearTimeout(e),e=setTimeout(function(){j.onColumnSearch(b)},j.options.searchTimeOut)}),k.off("change","select").on("change","select",function(b){clearTimeout(e),e=setTimeout(function(){j.onColumnSearch(b)},j.options.searchTimeOut)}),k.off("mouseup","input").on("mouseup","input",function(g){var b=S(this),f=b.val();""!==f&&setTimeout(function(){var c=b.val();""===c&&(clearTimeout(e),e=setTimeout(function(){j.onColumnSearch(g)},j.options.searchTimeOut))},1)}),k.find(".date-filter-control").length>0&&S.each(j.columns,function(f,c){void 0!==c.filterControl&&"datepicker"===c.filterControl.toLowerCase()&&k.find(".date-filter-control.bootstrap-table-filter-control-"+c.field).datepicker(c.filterDatepickerOptions).on("changeDate",function(g){S(g.currentTarget).keyup()})})):k.find(".filterControl").hide()},A=function(b){switch(b=void 0===b?"left":b.toLowerCase()){case"left":return"ltr";case"right":return"rtl";case"auto":return"auto";default:return"ltr"}},B={"var":function(g,d){var e=window[g];for(var h in e){V(d,h,e[h])}W(d)},url:function(a,d){S.ajax({url:a,dataType:"json",success:function(e){for(var c in e){V(d,c,e[c])}W(d)}})},json:function(g,d){var e=JSON.parse(g);for(var h in e){V(d,h,e[h])}W(d)}},G=function(h,f){for(var g=Object.keys(h),e=0;e<g.length;e++){if(g[e]===f){return h[f]}}return null};S.extend(S.fn.bootstrapTable.defaults,{filterControl:!1,onColumnSearch:function(){return !1},filterShowClear:!1,alignmentSelectControlOptions:void 0,filterTemplate:{input:function(f,e,b){return Q('<input type="text" class="form-control bootstrap-table-filter-control-%s" style="width: 100%; visibility: %s">',e,b)},select:function(f,e,b){return Q('<select class="form-control bootstrap-table-filter-control-%s" style="width: 100%; visibility: %s" dir="%s"></select>',e,b,A(f.options.alignmentSelectControlOptions))},datepicker:function(f,e,b){return Q('<input type="text" class="form-control date-filter-control bootstrap-table-filter-control-%s" style="width: 100%; visibility: %s">',e,b)}},valuesFilterControl:[]}),S.extend(S.fn.bootstrapTable.COLUMN_DEFAULTS,{filterControl:void 0,filterData:void 0,filterDatepickerOptions:void 0,filterStrictSearch:!1,filterStartsWithSearch:!1}),S.extend(S.fn.bootstrapTable.Constructor.EVENTS,{"column-search.bs.table":"onColumnSearch"}),S.extend(S.fn.bootstrapTable.defaults.icons,{clear:"glyphicon-trash icon-clear"}),S.extend(S.fn.bootstrapTable.locales,{formatClearFilters:function(){return"Clear Filters"}}),S.extend(S.fn.bootstrapTable.defaults,S.fn.bootstrapTable.locales);var H=S.fn.bootstrapTable.Constructor,E=H.prototype.init,F=H.prototype.initToolbar,Y=H.prototype.initHeader,Z=H.prototype.initBody,X=H.prototype.initSearch;H.prototype.init=function(){if(this.options.filterControl){var b=this;Object.keys||R(),this.options.valuesFilterControl=[],this.$el.on("reset-view.bs.table",function(){b.options.height&&(b.$tableHeader.find("select").length>0||b.$tableHeader.find("input").length>0||D(b,b.$tableHeader))}).on("post-header.bs.table",function(){P(b)}).on("post-body.bs.table",function(){b.options.height&&U(b)}).on("column-switch.bs.table",function(){P(b)})}E.apply(this,Array.prototype.slice.apply(arguments))},H.prototype.initToolbar=function(){if(this.showToolbar=this.options.filterControl&&this.options.filterShowClear,F.apply(this,Array.prototype.slice.apply(arguments)),this.options.filterControl&&this.options.filterShowClear){var b=this.$toolbar.find(">.btn-group"),a=b.find(".filter-show-clear");a.length||(a=S(['<button class="btn btn-default filter-show-clear" ',Q('type="button" title="%s">',this.options.formatClearFilters()),Q('<i class="%s %s"></i> ',this.options.iconsPrefix,this.options.icons.clear),"</button>"].join("")).appendTo(b),a.off("click").on("click",S.proxy(this.clearFilterControl,this)))}},H.prototype.initHeader=function(){Y.apply(this,Array.prototype.slice.apply(arguments)),this.options.filterControl&&D(this,this.$header)},H.prototype.initBody=function(){Z.apply(this,Array.prototype.slice.apply(arguments)),N(this)},H.prototype.initSearch=function(){if(X.apply(this,Array.prototype.slice.apply(arguments)),"server"!==this.options.sidePagination){var a=this,d=S.isEmptyObject(this.filterColumnsPartial)?null:this.filterColumnsPartial;this.data=d?S.grep(this.data,function(c,j){for(var m in d){var b=a.columns[S.fn.bootstrapTable.utils.getFieldIndex(a.columns,m)],k=d[m].toLowerCase(),l=c[m];if(b&&b.searchFormatter&&(l=S.fn.bootstrapTable.utils.calculateObjectValue(a.header,a.header.formatters[S.inArray(m,a.header.fields)],[l,c,j],l)),b.filterStrictSearch){if(-1===S.inArray(m,a.header.fields)||"string"!=typeof l&&"number"!=typeof l||l.toString().toLowerCase()!==k.toString().toLowerCase()){return !1}}else{if(b.filterStartsWithSearch){if(-1===S.inArray(m,a.header.fields)||"string"!=typeof l&&"number"!=typeof l||0!==(l+"").toLowerCase().indexOf(k)){return !1}}else{if(-1===S.inArray(m,a.header.fields)||"string"!=typeof l&&"number"!=typeof l||-1===(l+"").toLowerCase().indexOf(k)){return !1}}}}return !0}):this.data}},H.prototype.initColumnSearch=function(d){if(O(this),d){this.filterColumnsPartial=d,this.updatePagination();for(var c in d){this.trigger("column-search",c,d[c])}}},H.prototype.onColumnSearch=function(e){if(!(S.inArray(e.keyCode,[37,38,39,40])>-1)){O(this);var f=S.trim(S(e.currentTarget).val()),a=S(e.currentTarget).closest("[data-field]").data("field");S.isEmptyObject(this.filterColumnsPartial)&&(this.filterColumnsPartial={}),f?this.filterColumnsPartial[a]=f:delete this.filterColumnsPartial[a],this.searchText+="randomText",this.options.pageNumber=1,this.onSearch(e),this.trigger("column-search",a,f)}},H.prototype.clearFilterControl=function(){if(this.options.filterControl&&this.options.filterShowClear){var m=this,p=M(),a=K(m),n=a.closest("table"),o=a.find(L(m)),b=m.$toolbar.find(".search input"),h=0;if(S.each(m.options.valuesFilterControl,function(d,c){c.value=""}),P(m),!(o.length>0)){return}if(this.filterColumnsPartial={},S(o[0]).trigger("INPUT"===o[0].tagName?"keyup":"change"),b.length>0&&m.resetSearch(),m.options.sortName!==n.data("sortName")||m.options.sortOrder!==n.data("sortOrder")){var i=a.find(Q('[data-field="%s"]',S(o[0]).closest("table").data("sortName")));i.length>0&&(m.onSort(n.data("sortName"),n.data("sortName")),S(i).find(".sortable").trigger("click"))}clearTimeout(h),h=setTimeout(function(){p&&p.length>0&&S.each(p,function(d,c){void 0!==m.deleteCookie&&m.deleteCookie(c)})},m.options.searchTimeOut)}}}(jQuery);