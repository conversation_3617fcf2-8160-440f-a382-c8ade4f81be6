!function(H){var D=function(K,L){if(K.options.columnsHidden.length>0){H.each(K.columns,function(M,N){if(K.options.columnsHidden.indexOf(N.field)!==-1){if(N.visible!==L){K.toggleColumn(H.fn.bootstrapTable.utils.getFieldIndex(K.columns,N.field),L,true)}}})}};var C=function(K){if(K.options.height||K.options.showFooter){setTimeout(function(){K.resetView.call(K)},1)}};var E=function(L,M,K){if(L.options.minHeight){if((M<=L.options.minWidth)&&(K<=L.options.minHeight)){F(L)}else{if((M>L.options.minWidth)&&(K>L.options.minHeight)){B(L)}}}else{if(M<=L.options.minWidth){F(L)}else{if(M>L.options.minWidth){B(L)}}}C(L)};var F=function(K){J(K,false);D(K,false)};var B=function(K){J(K,true);D(K,true)};var J=function(K,L){K.options.cardView=L;K.toggleView()};var G=function(L,M){var K;return function(){var N=this,P=arguments;var O=function(){K=null;L.apply(N,P)};clearTimeout(K);K=setTimeout(O,M)}};H.extend(H.fn.bootstrapTable.defaults,{mobileResponsive:false,minWidth:562,minHeight:undefined,heightThreshold:100,checkOnInit:true,columnsHidden:[]});var A=H.fn.bootstrapTable.Constructor,I=A.prototype.init;A.prototype.init=function(){I.apply(this,Array.prototype.slice.apply(arguments));if(!this.options.mobileResponsive){return}if(!this.options.minWidth){return}if(this.options.minWidth<100&&this.options.resizable){console.log("The minWidth when the resizable extension is active should be greater or equal than 100");this.options.minWidth=100}var L=this,M={width:H(window).width(),height:H(window).height()};H(window).on("resize orientationchange",G(function(P){var O=H(this).height(),Q=H(this).width();if(Math.abs(M.height-O)>L.options.heightThreshold||M.width!=Q){E(L,Q,O);M={width:Q,height:O}}},200));if(this.options.checkOnInit){var K=H(window).height(),N=H(window).width();E(this,N,K);M={width:N,height:K}}}}(jQuery);