!function(F){var D,E,I=function(M){var K=arguments,L=!0,J=1;return M=M.replace(/%s/g,function(){var N=K[J++];return"undefined"==typeof N?(L=!1,""):N}),L?M:""},A=function(L,J){var K={};return L.forEach(function(N){var M=J(N);K[M]=K[M]||[],K[M].push(N)}),K};F.extend(F.fn.bootstrapTable.defaults,{groupBy:!1,groupByField:""});var G=F.fn.bootstrapTable.Constructor,H=G.prototype.initSort,B=G.prototype.initBody,C=G.prototype.updateSelected;G.prototype.initSort=function(){H.apply(this,Array.prototype.slice.apply(arguments));var K=this;if(E=[],this.options.groupBy&&""!==this.options.groupByField){this.options.sortName!=this.options.groupByField&&this.data.sort(function(N,M){return N[K.options.groupByField].localeCompare(M[K.options.groupByField])});var K=this,J=A(K.data,function(M){return[M[K.options.groupByField]]}),L=0;F.each(J,function(N,M){E.push({id:L,name:N}),M.forEach(function(O){O._data||(O._data={}),O._data["parent-index"]=L}),L++})}},G.prototype.initBody=function(){if(D=!0,B.apply(this,Array.prototype.slice.apply(arguments)),this.options.groupBy&&""!==this.options.groupByField){var K=this,L=!1,J=0;this.columns.forEach(function(M){M.checkbox?L=!0:M.visible&&(J+=1)}),this.options.detailView&&!this.options.cardView&&(J+=1),E.forEach(function(M){var N=[];N.push(I('<tr class="info groupBy expanded" data-group-index="%s">',M.id)),K.options.detailView&&!K.options.cardView&&N.push('<td class="detail"></td>'),L&&N.push('<td class="bs-checkbox">','<input name="btSelectGroup" type="checkbox" />',"</td>"),N.push("<td",I(' colspan="%s"',J),">",M.name,"</td>"),N.push("</tr>"),K.$body.find("tr[data-parent-index="+M.id+"]:first").before(F(N.join("")))}),this.$selectGroup=[],this.$body.find('[name="btSelectGroup"]').each(function(){var M=F(this);K.$selectGroup.push({group:M,item:K.$selectItem.filter(function(){return F(this).closest("tr").data("parent-index")===M.closest("tr").data("group-index")})})}),this.$container.off("click",".groupBy").on("click",".groupBy",function(){F(this).toggleClass("expanded"),K.$body.find("tr[data-parent-index="+F(this).closest("tr").data("group-index")+"]").toggleClass("hidden")}),this.$container.off("click",'[name="btSelectGroup"]').on("click",'[name="btSelectGroup"]',function(N){N.stopImmediatePropagation();var O=F(this),M=O.prop("checked");K[M?"checkGroup":"uncheckGroup"](F(this).closest("tr").data("group-index"))})}D=!1,this.updateSelected()},G.prototype.updateSelected=function(){D||(C.apply(this,Array.prototype.slice.apply(arguments)),this.options.groupBy&&""!==this.options.groupByField&&this.$selectGroup.forEach(function(K){var J=K.item.filter(":enabled").length===K.item.filter(":enabled").filter(":checked").length;K.group.prop("checked",J)}))},G.prototype.getGroupSelections=function(J){var K=this;return F.grep(this.data,function(L){return L[K.header.stateField]&&L._data["parent-index"]===J})},G.prototype.checkGroup=function(J){this.checkGroup_(J,!0)},G.prototype.uncheckGroup=function(J){this.checkGroup_(J,!1)},G.prototype.checkGroup_=function(L,M){var J,K=function(){return F(this).closest("tr").data("parent-index")===L};M||(J=this.getGroupSelections(L)),this.$selectItem.filter(K).prop("checked",M),this.updateRows(),this.updateSelected(),M&&(J=this.getGroupSelections(L)),this.trigger(M?"check-all":"uncheck-all",J)}}(jQuery);