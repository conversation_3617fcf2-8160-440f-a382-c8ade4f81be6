(function(A){A.fn.bootstrapTable.locales["af-ZA"]={formatLoadingMessage:function(){return"Besig om te laai, wag asseblief ..."},formatRecordsPerPage:function(B){return B+" rekords per bladsy"},formatShowingRows:function(B,C,D){return"Resultate "+B+" tot "+C+" van "+D+" rye"},formatSearch:function(){return"Soek"},formatNoMatches:function(){return"Geen rekords gevind nie"},formatPaginationSwitch:function(){return"Wys/verberg bladsy nummering"},formatRefresh:function(){return"Herlaai"},formatToggle:function(){return"Wissel"},formatColumns:function(){return"Kolomme"}};A.extend(A.fn.bootstrapTable.defaults,A.fn.bootstrapTable.locales["af-ZA"])})(jQuery);(function(A){A.fn.bootstrapTable.locales["ar-SA"]={formatLoadingMessage:function(){return"جاري التحميل, يرجى الإنتظار..."},formatRecordsPerPage:function(B){return B+" سجل لكل صفحة"},formatShowingRows:function(B,C,D){return"الظاهر "+B+" إلى "+C+" من "+D+" سجل"},formatSearch:function(){return"بحث"},formatNoMatches:function(){return"لا توجد نتائج مطابقة للبحث"},formatPaginationSwitch:function(){return"إخفاءإظهار ترقيم الصفحات"},formatRefresh:function(){return"تحديث"},formatToggle:function(){return"تغيير"},formatColumns:function(){return"أعمدة"}};A.extend(A.fn.bootstrapTable.defaults,A.fn.bootstrapTable.locales["ar-SA"])})(jQuery);(function(A){A.fn.bootstrapTable.locales["ca-ES"]={formatLoadingMessage:function(){return"Espereu, si us plau..."},formatRecordsPerPage:function(B){return B+" resultats per pàgina"},formatShowingRows:function(B,C,D){return"Mostrant de "+B+" fins "+C+" - total "+D+" resultats"},formatSearch:function(){return"Cerca"},formatNoMatches:function(){return"No s'han trobat resultats"},formatPaginationSwitch:function(){return"Amaga/Mostra paginació"},formatRefresh:function(){return"Refresca"},formatToggle:function(){return"Alterna formatació"},formatColumns:function(){return"Columnes"},formatAllRows:function(){return"Tots"}};A.extend(A.fn.bootstrapTable.defaults,A.fn.bootstrapTable.locales["ca-ES"])})(jQuery);(function(A){A.fn.bootstrapTable.locales["cs-CZ"]={formatLoadingMessage:function(){return"Čekejte, prosím..."},formatRecordsPerPage:function(B){return B+" položek na stránku"},formatShowingRows:function(B,C,D){return"Zobrazena "+B+". - "+C+". položka z celkových "+D},formatSearch:function(){return"Vyhledávání"},formatNoMatches:function(){return"Nenalezena žádná vyhovující položka"},formatPaginationSwitch:function(){return"Skrýt/Zobrazit stránkování"},formatRefresh:function(){return"Aktualizovat"},formatToggle:function(){return"Přepni"},formatColumns:function(){return"Sloupce"},formatAllRows:function(){return"Vše"}};A.extend(A.fn.bootstrapTable.defaults,A.fn.bootstrapTable.locales["cs-CZ"])})(jQuery);(function(A){A.fn.bootstrapTable.locales["da-DK"]={formatLoadingMessage:function(){return"Indlæser, vent venligst..."},formatRecordsPerPage:function(B){return B+" poster pr side"},formatShowingRows:function(B,C,D){return"Viser "+B+" til "+C+" af "+D+" rækker"},formatSearch:function(){return"Søg"},formatNoMatches:function(){return"Ingen poster fundet"},formatRefresh:function(){return"Opdater"},formatToggle:function(){return"Skift"},formatColumns:function(){return"Kolonner"}};A.extend(A.fn.bootstrapTable.defaults,A.fn.bootstrapTable.locales["da-DK"])})(jQuery);(function(A){A.fn.bootstrapTable.locales["de-DE"]={formatLoadingMessage:function(){return"Lade, bitte warten..."},formatRecordsPerPage:function(B){return B+" Einträge pro Seite"},formatShowingRows:function(B,C,D){return"Zeige "+B+" bis "+C+" von "+D+" Zeile"+((D>1)?"n":"")},formatSearch:function(){return"Suchen"},formatNoMatches:function(){return"Keine passenden Ergebnisse gefunden"},formatRefresh:function(){return"Neu laden"},formatToggle:function(){return"Umschalten"},formatColumns:function(){return"Spalten"}};A.extend(A.fn.bootstrapTable.defaults,A.fn.bootstrapTable.locales["de-DE"])})(jQuery);(function(A){A.fn.bootstrapTable.locales["el-GR"]={formatLoadingMessage:function(){return"Φορτώνει, παρακαλώ περιμένετε..."},formatRecordsPerPage:function(B){return B+" αποτελέσματα ανά σελίδα"},formatShowingRows:function(B,C,D){return"Εμφανίζονται από την "+B+" ως την "+C+" από σύνολο "+D+" σειρών"},formatSearch:function(){return"Αναζητήστε"},formatNoMatches:function(){return"Δεν βρέθηκαν αποτελέσματα"}};A.extend(A.fn.bootstrapTable.defaults,A.fn.bootstrapTable.locales["el-GR"])})(jQuery);(function(A){A.fn.bootstrapTable.locales["en-US"]={formatLoadingMessage:function(){return"Loading, please wait..."},formatRecordsPerPage:function(B){return B+" rows per page"},formatShowingRows:function(B,C,D){return"Showing "+B+" to "+C+" of "+D+" rows"},formatSearch:function(){return"Search"},formatNoMatches:function(){return"No matching records found"},formatPaginationSwitch:function(){return"Hide/Show pagination"},formatRefresh:function(){return"Refresh"},formatToggle:function(){return"Toggle"},formatColumns:function(){return"Columns"},formatAllRows:function(){return"All"},formatExport:function(){return"Export data"},formatClearFilters:function(){return"Clear filters"}};A.extend(A.fn.bootstrapTable.defaults,A.fn.bootstrapTable.locales["en-US"])})(jQuery);(function(A){A.fn.bootstrapTable.locales["es-AR"]={formatLoadingMessage:function(){return"Cargando, espere por favor..."},formatRecordsPerPage:function(B){return B+" registros por página"},formatShowingRows:function(B,C,D){return"Mostrando "+B+" a "+C+" de "+D+" filas"},formatSearch:function(){return"Buscar"},formatNoMatches:function(){return"No se encontraron registros"},formatAllRows:function(){return"Todo"}};A.extend(A.fn.bootstrapTable.defaults,A.fn.bootstrapTable.locales["es-AR"])})(jQuery);(function(A){A.fn.bootstrapTable.locales["es-CR"]={formatLoadingMessage:function(){return"Cargando, por favor espere..."},formatRecordsPerPage:function(B){return B+" registros por página"},formatShowingRows:function(B,C,D){return"Mostrando de "+B+" a "+C+" registros de "+D+" registros en total"},formatSearch:function(){return"Buscar"},formatNoMatches:function(){return"No se encontraron registros"},formatRefresh:function(){return"Refrescar"},formatToggle:function(){return"Alternar"},formatColumns:function(){return"Columnas"},formatAllRows:function(){return"Todo"}};A.extend(A.fn.bootstrapTable.defaults,A.fn.bootstrapTable.locales["es-CR"])})(jQuery);(function(A){A.fn.bootstrapTable.locales["es-ES"]={formatLoadingMessage:function(){return"Por favor espere..."},formatRecordsPerPage:function(B){return B+" resultados por página"},formatShowingRows:function(B,C,D){return"Mostrando desde "+B+" hasta "+C+" - En total "+D+" resultados"},formatSearch:function(){return"Buscar"},formatNoMatches:function(){return"No se encontraron resultados"},formatPaginationSwitch:function(){return"Ocultar/Mostrar paginación"},formatRefresh:function(){return"Refrescar"},formatToggle:function(){return"Ocultar/Mostrar"},formatColumns:function(){return"Columnas"},formatAllRows:function(){return"Todos"}};A.extend(A.fn.bootstrapTable.defaults,A.fn.bootstrapTable.locales["es-ES"])})(jQuery);(function(A){A.fn.bootstrapTable.locales["es-MX"]={formatLoadingMessage:function(){return"Cargando, espere por favor..."},formatRecordsPerPage:function(B){return B+" registros por página"},formatShowingRows:function(B,C,D){return"Mostrando "+B+" a "+C+" de "+D+" filas"},formatSearch:function(){return"Buscar"},formatNoMatches:function(){return"No se encontraron registros"},formatAllRows:function(){return"Todo"}};A.extend(A.fn.bootstrapTable.defaults,A.fn.bootstrapTable.locales["es-MX"])})(jQuery);(function(A){A.fn.bootstrapTable.locales["es-NI"]={formatLoadingMessage:function(){return"Cargando, por favor espere..."},formatRecordsPerPage:function(B){return B+" registros por página"},formatShowingRows:function(B,C,D){return"Mostrando de "+B+" a "+C+" registros de "+D+" registros en total"},formatSearch:function(){return"Buscar"},formatNoMatches:function(){return"No se encontraron registros"},formatRefresh:function(){return"Refrescar"},formatToggle:function(){return"Alternar"},formatColumns:function(){return"Columnas"},formatAllRows:function(){return"Todo"}};A.extend(A.fn.bootstrapTable.defaults,A.fn.bootstrapTable.locales["es-NI"])})(jQuery);(function(A){A.fn.bootstrapTable.locales["es-SP"]={formatLoadingMessage:function(){return"Cargando, por favor espera..."},formatRecordsPerPage:function(B){return B+" registros por p&#225;gina."},formatShowingRows:function(B,C,D){return B+" - "+C+" de "+D+" registros."},formatSearch:function(){return"Buscar"},formatNoMatches:function(){return"No se han encontrado registros."},formatRefresh:function(){return"Actualizar"},formatToggle:function(){return"Alternar"},formatColumns:function(){return"Columnas"},formatAllRows:function(){return"Todo"}};A.extend(A.fn.bootstrapTable.defaults,A.fn.bootstrapTable.locales["es-SP"])})(jQuery);(function(A){A.fn.bootstrapTable.locales["et-EE"]={formatLoadingMessage:function(){return"Päring käib, palun oota..."},formatRecordsPerPage:function(B){return B+" rida lehe kohta"},formatShowingRows:function(B,C,D){return"Näitan tulemusi "+B+" kuni "+C+" - kokku "+D+" tulemust"},formatSearch:function(){return"Otsi"},formatNoMatches:function(){return"Päringu tingimustele ei vastanud ühtegi tulemust"},formatPaginationSwitch:function(){return"Näita/Peida lehtedeks jagamine"},formatRefresh:function(){return"Värskenda"},formatToggle:function(){return"Lülita"},formatColumns:function(){return"Veerud"},formatAllRows:function(){return"Kõik"}};A.extend(A.fn.bootstrapTable.defaults,A.fn.bootstrapTable.locales["et-EE"])})(jQuery);(function(A){A.fn.bootstrapTable.locales["fa-IR"]={formatLoadingMessage:function(){return"در حال بارگذاری, لطفا صبر کنید..."},formatRecordsPerPage:function(B){return B+" رکورد در صفحه"},formatShowingRows:function(B,C,D){return"نمایش "+B+" تا "+C+" از "+D+" ردیف"},formatSearch:function(){return"جستجو"},formatNoMatches:function(){return"رکوردی یافت نشد."},formatPaginationSwitch:function(){return"نمایش/مخفی صفحه بندی"},formatRefresh:function(){return"به روز رسانی"},formatToggle:function(){return"تغییر نمایش"},formatColumns:function(){return"سطر ها"},formatAllRows:function(){return"همه"}};A.extend(A.fn.bootstrapTable.defaults,A.fn.bootstrapTable.locales["fa-IR"])})(jQuery);(function(A){A.fn.bootstrapTable.locales["fr-BE"]={formatLoadingMessage:function(){return"Chargement en cours..."},formatRecordsPerPage:function(B){return B+" entrées par page"},formatShowingRows:function(B,C,D){return"Affiche de"+B+" à "+C+" sur "+D+" lignes"},formatSearch:function(){return"Recherche"},formatNoMatches:function(){return"Pas de fichiers trouvés"}};A.extend(A.fn.bootstrapTable.defaults,A.fn.bootstrapTable.locales["fr-BE"])})(jQuery);(function(A){A.fn.bootstrapTable.locales["fr-FR"]={formatLoadingMessage:function(){return"Chargement en cours, patientez, s´il vous plaît ..."},formatRecordsPerPage:function(B){return B+" lignes par page"},formatShowingRows:function(B,C,D){return"Affichage des lignes "+B+" à "+C+" sur "+D+" lignes au total"},formatSearch:function(){return"Rechercher"},formatNoMatches:function(){return"Aucun résultat trouvé"},formatRefresh:function(){return"Rafraîchir"},formatToggle:function(){return"Alterner"},formatColumns:function(){return"Colonnes"},formatAllRows:function(){return"Tous"}};A.extend(A.fn.bootstrapTable.defaults,A.fn.bootstrapTable.locales["fr-FR"])})(jQuery);(function(A){A.fn.bootstrapTable.locales["he-IL"]={formatLoadingMessage:function(){return"טוען, נא להמתין..."},formatRecordsPerPage:function(B){return B+" שורות בעמוד"},formatShowingRows:function(B,C,D){return"מציג "+B+" עד "+C+" מ-"+D+" שורות"},formatSearch:function(){return"חיפוש"},formatNoMatches:function(){return"לא נמצאו רשומות תואמות"},formatPaginationSwitch:function(){return"הסתר/הצג מספור דפים"},formatRefresh:function(){return"רענן"},formatToggle:function(){return"החלף תצוגה"},formatColumns:function(){return"עמודות"},formatAllRows:function(){return"הכל"}};A.extend(A.fn.bootstrapTable.defaults,A.fn.bootstrapTable.locales["he-IL"])})(jQuery);(function(A){A.fn.bootstrapTable.locales["hr-HR"]={formatLoadingMessage:function(){return"Molimo pričekajte ..."},formatRecordsPerPage:function(B){return B+" broj zapisa po stranici"},formatShowingRows:function(B,C,D){return"Prikazujem "+B+". - "+C+". od ukupnog broja zapisa "+D},formatSearch:function(){return"Pretraži"},formatNoMatches:function(){return"Nije pronađen niti jedan zapis"},formatPaginationSwitch:function(){return"Prikaži/sakrij stranice"},formatRefresh:function(){return"Osvježi"},formatToggle:function(){return"Promijeni prikaz"},formatColumns:function(){return"Kolone"},formatAllRows:function(){return"Sve"}};A.extend(A.fn.bootstrapTable.defaults,A.fn.bootstrapTable.locales["hr-HR"])})(jQuery);(function(A){A.fn.bootstrapTable.locales["hu-HU"]={formatLoadingMessage:function(){return"Betöltés, kérem várjon..."},formatRecordsPerPage:function(B){return B+" rekord per oldal"},formatShowingRows:function(B,C,D){return"Megjelenítve "+B+" - "+C+" / "+D+" összesen"},formatSearch:function(){return"Keresés"},formatNoMatches:function(){return"Nincs találat"},formatPaginationSwitch:function(){return"Lapozó elrejtése/megjelenítése"},formatRefresh:function(){return"Frissítés"},formatToggle:function(){return"Összecsuk/Kinyit"},formatColumns:function(){return"Oszlopok"},formatAllRows:function(){return"Összes"}};A.extend(A.fn.bootstrapTable.defaults,A.fn.bootstrapTable.locales["hu-HU"])})(jQuery);(function(A){A.fn.bootstrapTable.locales["it-IT"]={formatLoadingMessage:function(){return"Caricamento in corso..."},formatRecordsPerPage:function(B){return B+" elementi per pagina"},formatShowingRows:function(B,C,D){return"Pagina "+B+" di "+C+" ("+D+" elementi)"},formatSearch:function(){return"Cerca"},formatNoMatches:function(){return"Nessun elemento trovato"},formatRefresh:function(){return"Aggiorna"},formatToggle:function(){return"Alterna"},formatColumns:function(){return"Colonne"},formatAllRows:function(){return"Tutto"}};A.extend(A.fn.bootstrapTable.defaults,A.fn.bootstrapTable.locales["it-IT"])})(jQuery);(function(A){A.fn.bootstrapTable.locales["ja-JP"]={formatLoadingMessage:function(){return"読み込み中です。少々お待ちください。"},formatRecordsPerPage:function(B){return"ページ当たり最大"+B+"件"},formatShowingRows:function(B,C,D){return"全"+D+"件から、"+B+"から"+C+"件目まで表示しています"},formatSearch:function(){return"検索"},formatNoMatches:function(){return"該当するレコードが見つかりません"},formatPaginationSwitch:function(){return"ページ数を表示・非表示"},formatRefresh:function(){return"更新"},formatToggle:function(){return"トグル"},formatColumns:function(){return"列"},formatAllRows:function(){return"すべて"}};A.extend(A.fn.bootstrapTable.defaults,A.fn.bootstrapTable.locales["ja-JP"])})(jQuery);(function(A){A.fn.bootstrapTable.locales["ka-GE"]={formatLoadingMessage:function(){return"იტვირთება, გთხოვთ მოიცადოთ..."},formatRecordsPerPage:function(B){return B+" ჩანაწერი თითო გვერდზე"},formatShowingRows:function(B,C,D){return"ნაჩვენებია "+B+"-დან "+C+"-მდე ჩანაწერი ჯამური "+D+"-დან"},formatSearch:function(){return"ძებნა"},formatNoMatches:function(){return"მონაცემები არ არის"},formatPaginationSwitch:function(){return"გვერდების გადამრთველის დამალვა/გამოჩენა"},formatRefresh:function(){return"განახლება"},formatToggle:function(){return"ჩართვა/გამორთვა"},formatColumns:function(){return"სვეტები"}};A.extend(A.fn.bootstrapTable.defaults,A.fn.bootstrapTable.locales["ka-GE"])})(jQuery);(function(A){A.fn.bootstrapTable.locales["ko-KR"]={formatLoadingMessage:function(){return"데이터를 불러오는 중입니다..."},formatRecordsPerPage:function(B){return"페이지 당 "+B+"개 데이터 출력"},formatShowingRows:function(B,C,D){return"전체 "+D+"개 중 "+B+"~"+C+"번째 데이터 출력,"},formatSearch:function(){return"검색"},formatNoMatches:function(){return"조회된 데이터가 없습니다."},formatRefresh:function(){return"새로 고침"},formatToggle:function(){return"전환"},formatColumns:function(){return"컬럼 필터링"}};A.extend(A.fn.bootstrapTable.defaults,A.fn.bootstrapTable.locales["ko-KR"])})(jQuery);(function(A){A.fn.bootstrapTable.locales["ms-MY"]={formatLoadingMessage:function(){return"Permintaan sedang dimuatkan. Sila tunggu sebentar..."},formatRecordsPerPage:function(B){return B+" rekod setiap muka surat"},formatShowingRows:function(B,C,D){return"Sedang memaparkan rekod "+B+" hingga "+C+" daripada jumlah "+D+" rekod"},formatSearch:function(){return"Cari"},formatNoMatches:function(){return"Tiada rekod yang menyamai permintaan"},formatPaginationSwitch:function(){return"Tunjuk/sembunyi muka surat"},formatRefresh:function(){return"Muatsemula"},formatToggle:function(){return"Tukar"},formatColumns:function(){return"Lajur"},formatAllRows:function(){return"Semua"}};A.extend(A.fn.bootstrapTable.defaults,A.fn.bootstrapTable.locales["ms-MY"])})(jQuery);(function(A){A.fn.bootstrapTable.locales["nb-NO"]={formatLoadingMessage:function(){return"Oppdaterer, vennligst vent..."},formatRecordsPerPage:function(B){return B+" poster pr side"},formatShowingRows:function(B,C,D){return"Viser "+B+" til "+C+" av "+D+" rekker"},formatSearch:function(){return"Søk"},formatNoMatches:function(){return"Ingen poster funnet"},formatRefresh:function(){return"Oppdater"},formatToggle:function(){return"Endre"},formatColumns:function(){return"Kolonner"}};A.extend(A.fn.bootstrapTable.defaults,A.fn.bootstrapTable.locales["nb-NO"])})(jQuery);(function(A){A.fn.bootstrapTable.locales["nl-NL"]={formatLoadingMessage:function(){return"Laden, even geduld..."},formatRecordsPerPage:function(B){return B+" records per pagina"},formatShowingRows:function(B,C,D){return"Toon "+B+" tot "+C+" van "+D+" record"+((D>1)?"s":"")},formatDetailPagination:function(B){return"Toon "+B+" record"+((B>1)?"s":"")},formatSearch:function(){return"Zoeken"},formatNoMatches:function(){return"Geen resultaten gevonden"},formatRefresh:function(){return"Vernieuwen"},formatToggle:function(){return"Omschakelen"},formatColumns:function(){return"Kolommen"},formatAllRows:function(){return"Alle"},formatPaginationSwitch:function(){return"Verberg/Toon paginatie"},formatExport:function(){return"Exporteer data"},formatClearFilters:function(){return"Verwijder filters"}};A.extend(A.fn.bootstrapTable.defaults,A.fn.bootstrapTable.locales["nl-NL"])})(jQuery);(function(A){A.fn.bootstrapTable.locales["pl-PL"]={formatLoadingMessage:function(){return"Ładowanie, proszę czekać..."},formatRecordsPerPage:function(B){return B+" rekordów na stronę"},formatShowingRows:function(B,C,D){return"Wyświetlanie rekordów od "+B+" do "+C+" z "+D},formatSearch:function(){return"Szukaj"},formatNoMatches:function(){return"Niestety, nic nie znaleziono"},formatRefresh:function(){return"Odśwież"},formatToggle:function(){return"Przełącz"},formatColumns:function(){return"Kolumny"}};A.extend(A.fn.bootstrapTable.defaults,A.fn.bootstrapTable.locales["pl-PL"])})(jQuery);(function(A){A.fn.bootstrapTable.locales["pt-BR"]={formatLoadingMessage:function(){return"Carregando, aguarde..."},formatRecordsPerPage:function(B){return B+" registros por página"},formatShowingRows:function(B,C,D){return"Exibindo "+B+" até "+C+" de "+D+" linhas"},formatSearch:function(){return"Pesquisar"},formatRefresh:function(){return"Recarregar"},formatToggle:function(){return"Alternar"},formatColumns:function(){return"Colunas"},formatPaginationSwitch:function(){return"Ocultar/Exibir paginação"},formatNoMatches:function(){return"Nenhum registro encontrado"}};A.extend(A.fn.bootstrapTable.defaults,A.fn.bootstrapTable.locales["pt-BR"])})(jQuery);(function(A){A.fn.bootstrapTable.locales["pt-PT"]={formatLoadingMessage:function(){return"A carregar, por favor aguarde..."},formatRecordsPerPage:function(B){return B+" registos por p&aacute;gina"},formatShowingRows:function(B,C,D){return"A mostrar "+B+" at&eacute; "+C+" de "+D+" linhas"},formatSearch:function(){return"Pesquisa"},formatNoMatches:function(){return"Nenhum registo encontrado"},formatPaginationSwitch:function(){return"Esconder/Mostrar pagina&ccedil&atilde;o"},formatRefresh:function(){return"Atualizar"},formatToggle:function(){return"Alternar"},formatColumns:function(){return"Colunas"},formatAllRows:function(){return"Tudo"}};A.extend(A.fn.bootstrapTable.defaults,A.fn.bootstrapTable.locales["pt-PT"])})(jQuery);(function(A){A.fn.bootstrapTable.locales["ro-RO"]={formatLoadingMessage:function(){return"Se incarca, va rugam asteptati..."},formatRecordsPerPage:function(B){return B+" inregistrari pe pagina"},formatShowingRows:function(B,C,D){return"Arata de la "+B+" pana la "+C+" din "+D+" randuri"},formatSearch:function(){return"Cauta"},formatNoMatches:function(){return"Nu au fost gasite inregistrari"},formatPaginationSwitch:function(){return"Ascunde/Arata paginatia"},formatRefresh:function(){return"Reincarca"},formatToggle:function(){return"Comuta"},formatColumns:function(){return"Coloane"},formatAllRows:function(){return"Toate"}};A.extend(A.fn.bootstrapTable.defaults,A.fn.bootstrapTable.locales["ro-RO"])})(jQuery);(function(A){A.fn.bootstrapTable.locales["ru-RU"]={formatLoadingMessage:function(){return"Пожалуйста, подождите, идёт загрузка..."},formatRecordsPerPage:function(B){return B+" записей на страницу"},formatShowingRows:function(B,C,D){return"Записи с "+B+" по "+C+" из "+D},formatSearch:function(){return"Поиск"},formatNoMatches:function(){return"Ничего не найдено"},formatRefresh:function(){return"Обновить"},formatToggle:function(){return"Переключить"},formatColumns:function(){return"Колонки"},formatClearFilters:function(){return"Очистить фильтры"}};A.extend(A.fn.bootstrapTable.defaults,A.fn.bootstrapTable.locales["ru-RU"])})(jQuery);(function(A){A.fn.bootstrapTable.locales["sk-SK"]={formatLoadingMessage:function(){return"Prosím čakajte ..."},formatRecordsPerPage:function(B){return B+" záznamov na stranu"},formatShowingRows:function(B,C,D){return"Zobrazená "+B+". - "+C+". položka z celkových "+D},formatSearch:function(){return"Vyhľadávanie"},formatNoMatches:function(){return"Nenájdená žiadna vyhovujúca položka"},formatRefresh:function(){return"Obnoviť"},formatToggle:function(){return"Prepni"},formatColumns:function(){return"Stĺpce"}};A.extend(A.fn.bootstrapTable.defaults,A.fn.bootstrapTable.locales["sk-SK"])})(jQuery);(function(A){A.fn.bootstrapTable.locales["sv-SE"]={formatLoadingMessage:function(){return"Laddar, vänligen vänta..."},formatRecordsPerPage:function(B){return B+" rader per sida"},formatShowingRows:function(B,C,D){return"Visa "+B+" till "+C+" av "+D+" rader"},formatSearch:function(){return"Sök"},formatNoMatches:function(){return"Inga matchande resultat funna."},formatRefresh:function(){return"Uppdatera"},formatToggle:function(){return"Skifta"},formatColumns:function(){return"kolumn"}};A.extend(A.fn.bootstrapTable.defaults,A.fn.bootstrapTable.locales["sv-SE"])})(jQuery);(function(A){A.fn.bootstrapTable.locales["th-TH"]={formatLoadingMessage:function(){return"กำลังโหลดข้อมูล, กรุณารอสักครู่..."},formatRecordsPerPage:function(B){return B+" รายการต่อหน้า"},formatShowingRows:function(B,C,D){return"รายการที่ "+B+" ถึง "+C+" จากทั้งหมด "+D+" รายการ"},formatSearch:function(){return"ค้นหา"},formatNoMatches:function(){return"ไม่พบรายการที่ค้นหา !"},formatRefresh:function(){return"รีเฟรส"},formatToggle:function(){return"สลับมุมมอง"},formatColumns:function(){return"คอลัมน์"}};A.extend(A.fn.bootstrapTable.defaults,A.fn.bootstrapTable.locales["th-TH"])})(jQuery);(function(A){A.fn.bootstrapTable.locales["tr-TR"]={formatLoadingMessage:function(){return"Yükleniyor, lütfen bekleyin..."},formatRecordsPerPage:function(B){return"Sayfa başına "+B+" kayıt."},formatShowingRows:function(B,C,D){return D+" kayıttan "+B+"-"+C+" arası gösteriliyor."},formatSearch:function(){return"Ara"},formatNoMatches:function(){return"Eşleşen kayıt bulunamadı."},formatRefresh:function(){return"Yenile"},formatToggle:function(){return"Değiştir"},formatColumns:function(){return"Sütunlar"},formatAllRows:function(){return"Tüm Satırlar"}};A.extend(A.fn.bootstrapTable.defaults,A.fn.bootstrapTable.locales["tr-TR"])})(jQuery);(function(A){A.fn.bootstrapTable.locales["uk-UA"]={formatLoadingMessage:function(){return"Завантаження, будь ласка, зачекайте..."},formatRecordsPerPage:function(B){return B+" записів на сторінку"},formatShowingRows:function(B,C,D){return"Показано з "+B+" по "+C+". Всього: "+D},formatSearch:function(){return"Пошук"},formatNoMatches:function(){return"Не знайдено жодного запису"},formatRefresh:function(){return"Оновити"},formatToggle:function(){return"Змінити"},formatColumns:function(){return"Стовпці"}};A.extend(A.fn.bootstrapTable.defaults,A.fn.bootstrapTable.locales["uk-UA"])})(jQuery);(function(A){A.fn.bootstrapTable.locales["ur-PK"]={formatLoadingMessage:function(){return"براۓ مہربانی انتظار کیجئے"},formatRecordsPerPage:function(B){return B+" ریکارڈز فی صفہ "},formatShowingRows:function(B,C,D){return"دیکھیں "+B+" سے "+C+" کے "+D+"ریکارڈز"},formatSearch:function(){return"تلاش"},formatNoMatches:function(){return"کوئی ریکارڈ نہیں ملا"},formatRefresh:function(){return"تازہ کریں"},formatToggle:function(){return"تبدیل کریں"},formatColumns:function(){return"کالم"}};A.extend(A.fn.bootstrapTable.defaults,A.fn.bootstrapTable.locales["ur-PK"])})(jQuery);(function(A){A.fn.bootstrapTable.locales["vi-VN"]={formatLoadingMessage:function(){return"Đang tải..."},formatRecordsPerPage:function(B){return B+" bản ghi mỗi trang"},formatShowingRows:function(B,C,D){return"Hiển thị từ trang "+B+" đến "+C+" của "+D+" bảng ghi"},formatSearch:function(){return"Tìm kiếm"},formatNoMatches:function(){return"Không có dữ liệu"}};A.extend(A.fn.bootstrapTable.defaults,A.fn.bootstrapTable.locales["vi-VN"])})(jQuery);(function(A){A.fn.bootstrapTable.locales["zh-CN"]={formatLoadingMessage:function(){return"正在努力地加载数据中，请稍候……"},formatRecordsPerPage:function(B){return"每页显示 "+B+" 条记录"},formatShowingRows:function(B,C,D){return"显示第 "+B+" 到第 "+C+" 条记录，总共 "+D+" 条记录"},formatSearch:function(){return"搜索"},formatNoMatches:function(){return"没有找到匹配的记录"},formatPaginationSwitch:function(){return"隐藏/显示分页"},formatRefresh:function(){return"刷新"},formatToggle:function(){return"切换"},formatColumns:function(){return"列"},formatExport:function(){return"导出数据"},formatClearFilters:function(){return"清空过滤"}};A.extend(A.fn.bootstrapTable.defaults,A.fn.bootstrapTable.locales["zh-CN"])})(jQuery);(function(A){A.fn.bootstrapTable.locales["zh-TW"]={formatLoadingMessage:function(){return"正在努力地載入資料，請稍候……"},formatRecordsPerPage:function(B){return"每頁顯示 "+B+" 項記錄"},formatShowingRows:function(B,C,D){return"顯示第 "+B+" 到第 "+C+" 項記錄，總共 "+D+" 項記錄"},formatSearch:function(){return"搜尋"},formatNoMatches:function(){return"沒有找到符合的結果"},formatPaginationSwitch:function(){return"隱藏/顯示分頁"},formatRefresh:function(){return"重新整理"},formatToggle:function(){return"切換"},formatColumns:function(){return"列"}};A.extend(A.fn.bootstrapTable.defaults,A.fn.bootstrapTable.locales["zh-TW"])})(jQuery);