(function(K){function G(Q,P){var M=K(Q).combo("panel");var O=M.find('div.combobox-item[value="'+P+'"]');if(O.length){if(O.position().top<=0){var N=M.scrollTop()+O.position().top;M.scrollTop(N)}else{if(O.position().top+O.outerHeight()>M.height()){var N=M.scrollTop()+O.position().top+O.outerHeight()-M.height();M.scrollTop(N)}}}}function F(Q){var M=K(Q).combo("panel");var N=K(Q).combo("getValues");var O=M.find('div.combobox-item[value="'+N.pop()+'"]');if(O.length){var R=O.prev(":visible");if(R.length){O=R}}else{O=M.find("div.combobox-item:visible:last")}var P=O.attr("value");I(Q,P);G(Q,P)}function J(Q){var M=K(Q).combo("panel");var N=K(Q).combo("getValues");var O=M.find('div.combobox-item[value="'+N.pop()+'"]');if(O.length){var R=O.next(":visible");if(R.length){O=R}}else{O=M.find("div.combobox-item:visible:first")}var P=O.attr("value");I(Q,P);G(Q,P)}function I(Q,P){var N=K.data(Q,"combobox").options;var R=K.data(Q,"combobox").data;if(N.multiple){var O=K(Q).combo("getValues");for(var M=0;M<O.length;M++){if(O[M]==P){return}}O.push(P);L(Q,O)}else{L(Q,[P])}for(var M=0;M<R.length;M++){if(R[M][N.valueField]==P){N.onSelect.call(Q,R[M]);return}}}function H(Q,P){var N=K.data(Q,"combobox").options;var R=K.data(Q,"combobox").data;var O=K(Q).combo("getValues");for(var M=0;M<O.length;M++){if(O[M]==P){O.splice(M,1);L(Q,O);break}}for(var M=0;M<R.length;M++){if(R[M][N.valueField]==P){N.onUnselect.call(Q,R[M]);return}}}function L(W,U,P){var V=K.data(W,"combobox").options;var O=K.data(W,"combobox").data;var X=K(W).combo("panel");X.find("div.combobox-item-selected").removeClass("combobox-item-selected");var R=[],M=[];for(var T=0;T<U.length;T++){var N=U[T];var S=N;for(var Q=0;Q<O.length;Q++){if(O[Q][V.valueField]==N){S=O[Q][V.textField];break}}R.push(N);M.push(S);X.find('div.combobox-item[value="'+N+'"]').addClass("combobox-item-selected")}K(W).combo("setValues",R);if(!P){K(W).combo("setText",M.join(V.separator))}}function C(N){var M=K.data(N,"combobox").options;var O=[];K(">option",N).each(function(){var P={};P[M.valueField]=K(this).attr("value")!=undefined?K(this).attr("value"):K(this).html();P[M.textField]=K(this).html();P["selected"]=K(this).attr("selected");O.push(P)});return O}function A(T,O,P){var S=K.data(T,"combobox").options;var U=K(T).combo("panel");K.data(T,"combobox").data=O;var R=K(T).combobox("getValues");U.empty();for(var Q=0;Q<O.length;Q++){var N=O[Q][S.valueField];var M=O[Q][S.textField];var V=K('<div class="combobox-item"></div>').appendTo(U);V.attr("value",N);if(S.formatter){V.html(S.formatter.call(T,O[Q]))}else{V.html(M)}if(O[Q]["selected"]){(function(){for(var W=0;W<R.length;W++){if(N==R[W]){return}}R.push(N)})()}}if(S.multiple){L(T,R,P)}else{if(R.length){L(T,[R[R.length-1]],P)}else{L(T,[],P)}}S.onLoadSuccess.call(T,O);K(".combobox-item",U).hover(function(){K(this).addClass("combobox-item-hover")},function(){K(this).removeClass("combobox-item-hover")}).click(function(){var W=K(this);if(S.multiple){if(W.hasClass("combobox-item-selected")){H(T,W.attr("value"))}else{I(T,W.attr("value"))}}else{I(T,W.attr("value"));K(T).combo("hidePanel")}})}function B(Q,M,P,O){var N=K.data(Q,"combobox").options;if(M){N.url=M}P=P||{};if(N.onBeforeLoad.call(Q,P)==false){return}N.loader.call(Q,P,function(R){A(Q,R,O)},function(){N.onLoadError.apply(this,arguments)})}function E(S,N){var R=K.data(S,"combobox").options;if(R.multiple&&!N){L(S,[],true)}else{L(S,[N],true)}if(R.mode=="remote"){B(S,null,{q:N},true)}else{var T=K(S).combo("panel");T.find("div.combobox-item").hide();var P=K.data(S,"combobox").data;for(var Q=0;Q<P.length;Q++){if(R.filter.call(S,N,P[Q])){var O=P[Q][R.valueField];var M=P[Q][R.textField];var U=T.find('div.combobox-item[value="'+O+'"]');U.show();if(M==N){L(S,[O],true);U.addClass("combobox-item-selected")}}}}}function D(N){var M=K.data(N,"combobox").options;K(N).addClass("combobox-f");K(N).combo(K.extend({},M,{onShowPanel:function(){K(N).combo("panel").find("div.combobox-item").show();G(N,K(N).combobox("getValue"));M.onShowPanel.call(N)}}))}K.fn.combobox=function(M,O){if(typeof M=="string"){var N=K.fn.combobox.methods[M];if(N){return N(this,O)}else{return this.combo(M,O)}}M=M||{};return this.each(function(){var P=K.data(this,"combobox");if(P){K.extend(P.options,M);D(this)}else{P=K.data(this,"combobox",{options:K.extend({},K.fn.combobox.defaults,K.fn.combobox.parseOptions(this),M)});D(this);A(this,C(this))}if(P.options.data){A(this,P.options.data)}B(this)})};K.fn.combobox.methods={options:function(M){var N=K.data(M[0],"combobox").options;N.originalValue=M.combo("options").originalValue;return N},getData:function(M){return K.data(M[0],"combobox").data},setValues:function(M,N){return M.each(function(){L(this,N)})},setValue:function(M,N){return M.each(function(){L(this,[N])})},clear:function(M){return M.each(function(){K(this).combo("clear");var N=K(this).combo("panel");N.find("div.combobox-item-selected").removeClass("combobox-item-selected")})},reset:function(M){return M.each(function(){var N=K(this).combobox("options");if(N.multiple){K(this).combobox("setValues",N.originalValue)}else{K(this).combobox("setValue",N.originalValue)}})},loadData:function(M,N){return M.each(function(){A(this,N)})},reload:function(M,N){return M.each(function(){B(this,N)})},select:function(M,N){return M.each(function(){I(this,N)})},unselect:function(M,N){return M.each(function(){H(this,N)})}};K.fn.combobox.parseOptions=function(N){var M=K(N);return K.extend({},K.fn.combo.parseOptions(N),K.parser.parseOptions(N,["valueField","textField","mode","method","url"]))};K.fn.combobox.defaults=K.extend({},K.fn.combo.defaults,{valueField:"value",textField:"text",mode:"local",method:"post",url:null,data:null,keyHandler:{up:function(){F(this)},down:function(){J(this)},enter:function(){var M=K(this).combobox("getValues");K(this).combobox("setValues",M);K(this).combobox("hidePanel")},query:function(M){E(this,M)}},filter:function(N,O){var M=K(this).combobox("options");return O[M.textField].indexOf(N)==0},formatter:function(N){var M=K(this).combobox("options");return N[M.textField]},loader:function(O,M,P){var N=K(this).combobox("options");if(!N.url){return false}K.ajax({type:N.method,url:N.url,data:O,dataType:"json",success:function(Q){M(Q)},error:function(){P.apply(this,arguments)}})},onBeforeLoad:function(M){},onLoadSuccess:function(){},onLoadError:function(){},onSelect:function(M){},onUnselect:function(M){}})})(jQuery);