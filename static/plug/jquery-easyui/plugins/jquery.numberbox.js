(function(F){function B(I){F(I).addClass("numberbox-f");var J=F('<input type="hidden">').insertAfter(I);var K=F(I).attr("name");if(K){J.attr("name",K);F(I).removeAttr("name").attr("numberboxName",K)}return J}function G(I){var J=F.data(I,"numberbox").options;var K=J.onChange;<PERSON><PERSON>onC<PERSON>=function(){};E(I,J.parser.call(I,J.value));J.onChange=K;J.originalValue=A(I)}function A(I){return F.data(I,"numberbox").field.val()}function E(I,J){var K=F.data(I,"numberbox");var L=K.options;var M=A(I);J=L.parser.call(I,J);L.value=J;K.field.val(J);F(I).val(L.formatter.call(I,J));if(M!=J){L.onChange.call(I,J,M)}}function D(J){var I=F.data(J,"numberbox").options;F(J).unbind(".numberbox").bind("keypress.numberbox",function(K){if(K.which==45){if(F(this).val().indexOf("-")==-1){return true}else{return false}}if(K.which==46){if(F(this).val().indexOf(".")==-1){return true}else{return false}}else{if((K.which>=48&&K.which<=57&&K.ctrlKey==false&&K.shiftKey==false)||K.which==0||K.which==8){return true}else{if(K.ctrlKey==true&&(K.which==99||K.which==118)){return true}else{return false}}}}).bind("blur.numberbox",function(){E(J,F(this).val());F(this).val(I.formatter.call(J,A(J)))}).bind("focus.numberbox",function(){var K=A(J);if(F(this).val()!=K){F(this).val(K)}})}function H(I){if(F.fn.validatebox){var J=F.data(I,"numberbox").options;F(I).validatebox(J)}}function C(I,K){var J=F.data(I,"numberbox").options;if(K){J.disabled=true;F(I).attr("disabled",true)}else{J.disabled=false;F(I).removeAttr("disabled")}}F.fn.numberbox=function(K,J){if(typeof K=="string"){var I=F.fn.numberbox.methods[K];if(I){return I(this,J)}else{return this.validatebox(K,J)}}K=K||{};return this.each(function(){var L=F.data(this,"numberbox");if(L){F.extend(L.options,K)}else{L=F.data(this,"numberbox",{options:F.extend({},F.fn.numberbox.defaults,F.fn.numberbox.parseOptions(this),K),field:B(this)});F(this).removeAttr("disabled");F(this).css({imeMode:"disabled"})}C(this,L.options.disabled);D(this);H(this);G(this)})};F.fn.numberbox.methods={options:function(I){return F.data(I[0],"numberbox").options},destroy:function(I){return I.each(function(){F.data(this,"numberbox").field.remove();F(this).validatebox("destroy");F(this).remove()})},disable:function(I){return I.each(function(){C(this,true)})},enable:function(I){return I.each(function(){C(this,false)})},fix:function(I){return I.each(function(){E(this,F(this).val())})},setValue:function(I,J){return I.each(function(){E(this,J)})},getValue:function(I){return A(I[0])},clear:function(I){return I.each(function(){var J=F.data(this,"numberbox");J.field.val("");F(this).val("")})},reset:function(I){return I.each(function(){var J=F(this).numberbox("options");F(this).numberbox("setValue",J.originalValue)})}};F.fn.numberbox.parseOptions=function(I){var J=F(I);return F.extend({},F.fn.validatebox.parseOptions(I),F.parser.parseOptions(I,["decimalSeparator","groupSeparator","suffix",{min:"number",max:"number",precision:"number"}]),{prefix:(J.attr("prefix")?J.attr("prefix"):undefined),disabled:(J.attr("disabled")?true:undefined),value:(J.val()||undefined)})};F.fn.numberbox.defaults=F.extend({},F.fn.validatebox.defaults,{disabled:false,value:"",min:null,max:null,precision:0,decimalSeparator:".",groupSeparator:"",prefix:"",suffix:"",formatter:function(L){if(!L){return L}L=L+"";var N=F(this).numberbox("options");var I=L,J="";var M=L.indexOf(".");if(M>=0){I=L.substring(0,M);J=L.substring(M+1,L.length)}if(N.groupSeparator){var K=/(\d+)(\d{3})/;while(K.test(I)){I=I.replace(K,"$1"+N.groupSeparator+"$2")}}if(J){return N.prefix+I+N.decimalSeparator+J+N.suffix}else{return N.prefix+I+N.suffix}},parser:function(J){J=J+"";var I=F(this).numberbox("options");if(I.groupSeparator){J=J.replace(new RegExp("\\"+I.groupSeparator,"g"),"")}if(I.decimalSeparator){J=J.replace(new RegExp("\\"+I.decimalSeparator,"g"),".")}if(I.prefix){J=J.replace(new RegExp("\\"+F.trim(I.prefix),"g"),"")}if(I.suffix){J=J.replace(new RegExp("\\"+F.trim(I.suffix),"g"),"")}J=J.replace(/\s/g,"");var K=parseFloat(J).toFixed(I.precision);if(isNaN(K)){K=""}else{if(typeof(I.min)=="number"&&K<I.min){K=I.min.toFixed(I.precision)}else{if(typeof(I.max)=="number"&&K>I.max){K=I.max.toFixed(I.precision)}}}return K},onChange:function(I,J){}})})(jQuery);