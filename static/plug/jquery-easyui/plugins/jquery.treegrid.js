(function(V){function D(f,d){for(var c=0,e=f.length;c<e;c++){if(f[c]==d){return c}}return -1}function U(e,c){var d=D(e,c);if(d!=-1){e.splice(d,1)}}function M(c){var d=V.data(c,"treegrid").options;V(c).datagrid(V.extend({},d,{url:null,data:null,loader:function(){return false},onLoadSuccess:function(){},onResizeColumn:function(g,f){S(c);d.onResizeColumn.call(c,g,f)},onSortColumn:function(f,g){d.sortName=f;d.sortOrder=g;if(d.remoteSort){T(c)}else{var h=V(c).treegrid("getData");K(c,0,h)}d.onSortColumn.call(c,f,g)},onBeforeEdit:function(f,g){if(d.onBeforeEdit.call(c,g)==false){return false}},onAfterEdit:function(g,h,f){d.onAfterEdit.call(c,h,f)},onCancelEdit:function(f,g){d.onCancelEdit.call(c,g)},onSelect:function(f){d.onSelect.call(c,J(c,f))},onUnselect:function(f){d.onUnselect.call(c,J(c,f))},onSelectAll:function(){d.onSelectAll.call(c,V.data(c,"treegrid").data)},onUnselectAll:function(){d.onUnselectAll.call(c,V.data(c,"treegrid").data)},onCheck:function(f){d.onCheck.call(c,J(c,f))},onUncheck:function(f){d.onUncheck.call(c,J(c,f))},onCheckAll:function(){d.onCheckAll.call(c,V.data(c,"treegrid").data)},onUncheckAll:function(){d.onUncheckAll.call(c,V.data(c,"treegrid").data)},onClickRow:function(f){d.onClickRow.call(c,J(c,f))},onDblClickRow:function(f){d.onDblClickRow.call(c,J(c,f))},onClickCell:function(g,f){d.onClickCell.call(c,f,J(c,g))},onDblClickCell:function(g,f){d.onDblClickCell.call(c,f,J(c,g))},onRowContextMenu:function(g,f){d.onContextMenu.call(c,g,J(c,f))}}));if(d.pagination){var e=V(c).datagrid("getPager");e.pagination({pageNumber:d.pageNumber,pageSize:d.pageSize,pageList:d.pageList,onSelectPage:function(f,g){d.pageNumber=f;d.pageSize=g;T(c)}});d.pageSize=e.pagination("options").pageSize}}function S(g,f){var d=V.data(g,"datagrid").options;var c=V.data(g,"datagrid").dc;if(!c.body1.is(":empty")&&(!d.nowrap||d.autoRowHeight)){if(f!=undefined){var h=R(g,f);for(var e=0;e<h.length;e++){j(h[e][d.idField])}}}V(g).datagrid("fixRowHeight",f);function j(k){var m=d.finder.getTr(g,k,"body",1);var l=d.finder.getTr(g,k,"body",2);m.css("height","");l.css("height","");var i=Math.max(m.height(),l.height());m.css("height",i);l.css("height",i)}}function I(d){var e=V.data(d,"datagrid").dc;var c=V.data(d,"treegrid").options;if(!c.rownumbers){return}e.body1.find("div.datagrid-cell-rownumber").each(function(f){V(this).html(f+1)})}function P(f){var e=V.data(f,"datagrid").dc;var c=e.body1.add(e.body2);var d=(V.data(c[0],"events")||V._data(c[0],"events")).click[0].handler;e.body1.add(e.body2).bind("mouseover",function(g){var i=V(g.target);var h=i.closest("tr.datagrid-row");if(!h.length){return}if(i.hasClass("tree-hit")){i.hasClass("tree-expanded")?i.addClass("tree-expanded-hover"):i.addClass("tree-collapsed-hover")}g.stopPropagation()}).bind("mouseout",function(g){var i=V(g.target);var h=i.closest("tr.datagrid-row");if(!h.length){return}if(i.hasClass("tree-hit")){i.hasClass("tree-expanded")?i.removeClass("tree-expanded-hover"):i.removeClass("tree-collapsed-hover")}g.stopPropagation()}).unbind("click").bind("click",function(g){var i=V(g.target);var h=i.closest("tr.datagrid-row");if(!h.length){return}if(i.hasClass("tree-hit")){C(f,h.attr("node-id"))}else{d(g)}g.stopPropagation()})}function E(j,e){var d=V.data(j,"treegrid").options;var i=d.finder.getTr(j,e,"body",1);var f=d.finder.getTr(j,e,"body",2);var h=V(j).datagrid("getColumnFields",true).length+(d.rownumbers?1:0);var g=V(j).datagrid("getColumnFields",false).length;c(i,h);c(f,g);function c(l,k){V('<tr class="treegrid-tr-tree"><td style="border:0px" colspan="'+k+'"><div></div></td></tr>').insertAfter(l)}}function K(e,f,c,o){var n=V.data(e,"treegrid").options;var g=V.data(e,"datagrid").dc;c=n.loadFilter.call(e,c,f);var k=J(e,f);if(k){var m=n.finder.getTr(e,f,"body",1);var l=n.finder.getTr(e,f,"body",2);var j=m.next("tr.treegrid-tr-tree").children("td").children("div");var d=l.next("tr.treegrid-tr-tree").children("td").children("div")}else{var j=g.body1;var d=g.body2}if(!o){V.data(e,"treegrid").data=[];j.empty();d.empty()}if(n.view.onBeforeRender){n.view.onBeforeRender.call(n.view,e,f,c)}n.view.render.call(n.view,e,j,true);n.view.render.call(n.view,e,d,false);if(n.showFooter){n.view.renderFooter.call(n.view,e,g.footer1,true);n.view.renderFooter.call(n.view,e,g.footer2,false)}if(n.view.onAfterRender){n.view.onAfterRender.call(n.view,e)}n.onLoadSuccess.call(e,k,c);if(!f&&n.pagination){var i=V.data(e,"treegrid").total;var h=V(e).datagrid("getPager");if(h.pagination("options").total!=i){h.pagination({total:i})}}S(e);I(e);V(e).treegrid("autoSizeColumn")}function T(i,h,l,k,c){var e=V.data(i,"treegrid").options;var d=V(i).datagrid("getPanel").find("div.datagrid-body");if(l){e.queryParams=l}var g=V.extend({},e.queryParams);if(e.pagination){V.extend(g,{page:e.pageNumber,rows:e.pageSize})}if(e.sortName){V.extend(g,{sort:e.sortName,order:e.sortOrder})}var j=J(i,h);if(e.onBeforeLoad.call(i,j,g)==false){return}var f=d.find("tr[node-id="+h+"] span.tree-folder");f.addClass("tree-loading");V(i).treegrid("loading");var m=e.loader.call(i,g,function(n){f.removeClass("tree-loading");V(i).treegrid("loaded");K(i,h,n,k);if(c){c()}},function(){f.removeClass("tree-loading");V(i).treegrid("loaded");e.onLoadError.apply(i,arguments);if(c){c()}});if(m==false){f.removeClass("tree-loading");V(i).treegrid("loaded")}}function W(d){var c=Z(d);if(c.length){return c[0]}else{return null}}function Z(c){return V.data(c,"treegrid").data}function Y(d,e){var c=J(d,e);if(c._parentId){return J(d,c._parentId)}else{return null}}function R(f,j){var c=V.data(f,"treegrid").options;var k=V(f).datagrid("getPanel").find("div.datagrid-view2 div.datagrid-body");var g=[];if(j){d(j)}else{var h=Z(f);for(var e=0;e<h.length;e++){g.push(h[e]);d(h[e][c.idField])}}function d(o){var n=J(f,o);if(n&&n.children){for(var l=0,p=n.children.length;l<p;l++){var m=n.children[l];g.push(m);d(m[c.idField])}}}return g}function A(c){var d=b(c);if(d.length){return d[0]}else{return null}}function b(d){var e=[];var c=V(d).datagrid("getPanel");c.find("div.datagrid-view2 div.datagrid-body tr.datagrid-row-selected").each(function(){var f=V(this).attr("node-id");e.push(J(d,f))});return e}function G(d,c){if(!c){return 0}var f=V.data(d,"treegrid").options;var e=V(d).datagrid("getPanel").children("div.datagrid-view");var g=e.find("div.datagrid-body tr[node-id="+c+"]").children("td[field="+f.treeField+"]");return g.find("span.tree-indent,span.tree-hit").length}function J(g,f){var j=V.data(g,"treegrid").options;var h=V.data(g,"treegrid").data;var d=[h];while(d.length){var l=d.shift();for(var k=0;k<l.length;k++){var e=l[k];if(e[j.idField]==f){return e}else{if(e["children"]){d.push(e["children"])}}}}return null}function H(g,f){var d=V.data(g,"treegrid").options;var h=J(g,f);var i=d.finder.getTr(g,f);var c=i.find("span.tree-hit");if(c.length==0){return}if(c.hasClass("tree-collapsed")){return}if(d.onBeforeCollapse.call(g,h)==false){return}c.removeClass("tree-expanded tree-expanded-hover").addClass("tree-collapsed");c.next().removeClass("tree-folder-open");h.state="closed";i=i.next("tr.treegrid-tr-tree");var e=i.children("td").children("div");if(d.animate){e.slideUp("normal",function(){V(g).treegrid("autoSizeColumn");S(g,f);d.onCollapse.call(g,h)})}else{e.hide();V(g).treegrid("autoSizeColumn");S(g,f);d.onCollapse.call(g,h)}}function Q(j,c){var k=V.data(j,"treegrid").options;var f=k.finder.getTr(j,c);var e=f.find("span.tree-hit");var g=J(j,c);if(e.length==0){return}if(e.hasClass("tree-expanded")){return}if(k.onBeforeExpand.call(j,g)==false){return}e.removeClass("tree-collapsed tree-collapsed-hover").addClass("tree-expanded");e.next().addClass("tree-folder-open");var i=f.next("tr.treegrid-tr-tree");if(i.length){var d=i.children("td").children("div");h(d)}else{E(j,g[k.idField]);var i=f.next("tr.treegrid-tr-tree");var d=i.children("td").children("div");d.hide();T(j,g[k.idField],{id:g[k.idField]},true,function(){if(d.is(":empty")){i.remove()}else{h(d)}})}function h(l){g.state="open";if(k.animate){l.slideDown("normal",function(){V(j).treegrid("autoSizeColumn");S(j,c);k.onExpand.call(j,g)})}else{l.show();V(j).treegrid("autoSizeColumn");S(j,c);k.onExpand.call(j,g)}}}function C(d,c){var g=V.data(d,"treegrid").options;var e=g.finder.getTr(d,c);var f=e.find("span.tree-hit");if(f.hasClass("tree-expanded")){H(d,c)}else{Q(d,c)}}function L(c,f){var e=V.data(c,"treegrid").options;var g=R(c,f);if(f){g.unshift(J(c,f))}for(var d=0;d<g.length;d++){H(c,g[d][e.idField])}}function a(f,e){var g=V.data(f,"treegrid").options;var d=R(f,e);if(e){d.unshift(J(f,e))}for(var c=0;c<d.length;c++){Q(f,d[c][g.idField])}}function B(e,d){var g=V.data(e,"treegrid").options;var h=[];var f=Y(e,d);while(f){var j=f[g.idField];h.unshift(j);f=Y(e,j)}for(var c=0;c<h.length;c++){Q(e,h[c])}}function N(d,g){var f=V.data(d,"treegrid").options;if(g.parent){var e=f.finder.getTr(d,g.parent);if(e.next("tr.treegrid-tr-tree").length==0){E(d,g.parent)}var c=e.children("td[field="+f.treeField+"]").children("div.datagrid-cell");var i=c.children("span.tree-icon");if(i.hasClass("tree-file")){i.removeClass("tree-file").addClass("tree-folder");var h=V('<span class="tree-hit tree-expanded"></span>').insertBefore(i);if(h.prev().length){h.prev().remove()}}}K(d,g.parent,g.data,true)}function X(d,h){var e=h.before||h.after;var c=V.data(d,"treegrid").options;var f=Y(d,e);N(d,{parent:(f?f[c.idField]:null),data:[h.data]});g(true);g(false);I(d);function g(i){var m=i?1:2;var j=c.finder.getTr(d,h.data[c.idField],"body",m);var l=j.closest("table.datagrid-btable");j=j.parent().children();var n=c.finder.getTr(d,e,"body",m);if(h.before){j.insertBefore(n)}else{var k=n.next("tr.treegrid-tr-tree");j.insertAfter(k.length?k:n)}l.remove()}}function F(c,f){var e=V.data(c,"treegrid").options;var g=e.finder.getTr(c,f);g.next("tr.treegrid-tr-tree").remove();g.remove();var i=d(f);if(i){if(i.children.length==0){g=e.finder.getTr(c,i[e.idField]);g.next("tr.treegrid-tr-tree").remove();var h=g.children("td[field="+e.treeField+"]").children("div.datagrid-cell");h.find(".tree-icon").removeClass("tree-folder").addClass("tree-file");h.find(".tree-hit").remove();V('<span class="tree-indent"></span>').prependTo(h)}}I(c);function d(m){var k;var l=Y(c,f);if(l){k=l.children}else{k=V(c).treegrid("getData")}for(var j=0;j<k.length;j++){if(k[j][e.idField]==m){k.splice(j,1);break}}return l}}V.fn.treegrid=function(e,c){if(typeof e=="string"){var d=V.fn.treegrid.methods[e];if(d){return d(this,c)}else{return this.datagrid(e,c)}}e=e||{};return this.each(function(){var f=V.data(this,"treegrid");if(f){V.extend(f.options,e)}else{f=V.data(this,"treegrid",{options:V.extend({},V.fn.treegrid.defaults,V.fn.treegrid.parseOptions(this),e),data:[]})}M(this);if(f.options.data){V(this).treegrid("loadData",f.options.data)}T(this);P(this)})};V.fn.treegrid.methods={options:function(c){return V.data(c[0],"treegrid").options},resize:function(d,c){return d.each(function(){V(this).datagrid("resize",c)})},fixRowHeight:function(c,d){return c.each(function(){S(this,d)})},loadData:function(c,d){return c.each(function(){K(this,null,d)})},reload:function(c,d){return c.each(function(){if(d){var f=V(this).treegrid("find",d);if(f.children){f.children.splice(0,f.children.length)}var h=V(this).datagrid("getPanel").find("div.datagrid-body");var g=h.find("tr[node-id="+d+"]");g.next("tr.treegrid-tr-tree").remove();var e=g.find("span.tree-hit");e.removeClass("tree-expanded tree-expanded-hover").addClass("tree-collapsed");Q(this,d)}else{T(this,null,{})}})},reloadFooter:function(c,d){return c.each(function(){var e=V.data(this,"treegrid").options;var f=V.data(this,"datagrid").dc;if(d){V.data(this,"treegrid").footer=d}if(e.showFooter){e.view.renderFooter.call(e.view,this,f.footer1,true);e.view.renderFooter.call(e.view,this,f.footer2,false);if(e.view.onAfterRender){e.view.onAfterRender.call(e.view,this)}V(this).treegrid("fixRowHeight")}})},getData:function(c){return V.data(c[0],"treegrid").data},getFooterRows:function(c){return V.data(c[0],"treegrid").footer},getRoot:function(c){return W(c[0])},getRoots:function(c){return Z(c[0])},getParent:function(c,d){return Y(c[0],d)},getChildren:function(c,d){return R(c[0],d)},getSelected:function(c){return A(c[0])},getSelections:function(c){return b(c[0])},getLevel:function(c,d){return G(c[0],d)},find:function(c,d){return J(c[0],d)},isLeaf:function(d,g){var f=V.data(d[0],"treegrid").options;var e=f.finder.getTr(d[0],g);var c=e.find("span.tree-hit");return c.length==0},select:function(c,d){return c.each(function(){V(this).datagrid("selectRow",d)})},unselect:function(c,d){return c.each(function(){V(this).datagrid("unselectRow",d)})},collapse:function(c,d){return c.each(function(){H(this,d)})},expand:function(c,d){return c.each(function(){Q(this,d)})},toggle:function(c,d){return c.each(function(){C(this,d)})},collapseAll:function(c,d){return c.each(function(){L(this,d)})},expandAll:function(c,d){return c.each(function(){a(this,d)})},expandTo:function(c,d){return c.each(function(){B(this,d)})},append:function(c,d){return c.each(function(){N(this,d)})},insert:function(d,c){return d.each(function(){X(this,c)})},remove:function(c,d){return c.each(function(){F(this,d)})},pop:function(c,e){var d=c.treegrid("find",e);c.treegrid("remove",e);return d},refresh:function(c,d){return c.each(function(){var e=V.data(this,"treegrid").options;e.view.refreshRow.call(e.view,this,d)})},update:function(c,d){return c.each(function(){var e=V.data(this,"treegrid").options;e.view.updateRow.call(e.view,this,d.id,d.row)})},beginEdit:function(c,d){return c.each(function(){V(this).datagrid("beginEdit",d);V(this).treegrid("fixRowHeight",d)})},endEdit:function(c,d){return c.each(function(){V(this).datagrid("endEdit",d)})},cancelEdit:function(c,d){return c.each(function(){V(this).datagrid("cancelEdit",d)})}};V.fn.treegrid.parseOptions=function(c){return V.extend({},V.fn.datagrid.parseOptions(c),V.parser.parseOptions(c,["treeField",{animate:"boolean"}]))};var O=V.extend({},V.fn.datagrid.defaults.view,{render:function(e,g,f){var c=V.data(e,"treegrid").options;var k=V(e).datagrid("getColumnFields",f);var d=V.data(e,"datagrid").rowIdPrefix;if(f){if(!(c.rownumbers||(c.frozenColumns&&c.frozenColumns.length))){return}}var i=this;var h=j(f,this.treeLevel,this.treeNodes);V(g).append(h.join(""));function j(t,o,n){var q=['<table class="datagrid-btable" cellspacing="0" cellpadding="0" border="0"><tbody>'];for(var r=0;r<n.length;r++){var s=n[r];if(s.state!="open"&&s.state!="closed"){s.state="open"}var p=c.rowStyler?c.rowStyler.call(e,s):"";var w=p?'style="'+p+'"':"";var u=d+"-"+(t?1:2)+"-"+s[c.idField];q.push('<tr id="'+u+'" class="datagrid-row" node-id='+s[c.idField]+" "+w+">");q=q.concat(i.renderRow.call(i,e,k,t,o,s));q.push("</tr>");if(s.children&&s.children.length){var m=j(t,o+1,s.children);var l=s.state=="closed"?"none":"block";q.push('<tr class="treegrid-tr-tree"><td style="border:0px" colspan='+(k.length+(c.rownumbers?1:0))+'><div style="display:'+l+'">');q=q.concat(m);q.push("</div></td></tr>")}}q.push("</tbody></table>");return q}},renderFooter:function(c,e,d){var k=V.data(c,"treegrid").options;var j=V.data(c,"treegrid").footer||[];var l=V(c).datagrid("getColumnFields",d);var f=['<table class="datagrid-ftable" cellspacing="0" cellpadding="0" border="0"><tbody>'];for(var g=0;g<j.length;g++){var h=j[g];h[k.idField]=h[k.idField]||("foot-row-id"+g);f.push('<tr class="datagrid-row" node-id='+h[k.idField]+">");f.push(this.renderRow.call(this,c,l,d,0,h));f.push("</tr>")}f.push("</tbody></table>");V(e).html(f.join(""))},renderRow:function(h,l,k,e,m){var d=V.data(h,"treegrid").options;var c=[];if(k&&d.rownumbers){c.push('<td class="datagrid-td-rownumber"><div class="datagrid-cell-rownumber">0</div></td>')}for(var n=0;n<l.length;n++){var g=l[n];var r=V(h).datagrid("getColumnOption",g);if(r){var f=r.styler?(r.styler(m[g],m)||""):"";var p=r.hidden?'style="display:none;'+f+'"':(f?'style="'+f+'"':"");c.push('<td field="'+g+'" '+p+">");if(r.checkbox){var p=""}else{var p="";if(r.align){p+="text-align:"+r.align+";"}if(!d.nowrap){p+="white-space:normal;height:auto;"}else{if(d.autoRowHeight){p+="height:auto;"}}}c.push('<div style="'+p+'" ');if(r.checkbox){c.push('class="datagrid-cell-check ')}else{c.push('class="datagrid-cell '+r.cellClass)}c.push('">');if(r.checkbox){if(m.checked){c.push('<input type="checkbox" checked="checked"')}else{c.push('<input type="checkbox"')}c.push(' name="'+g+'" value="'+(m[g]!=undefined?m[g]:"")+'"/>')}else{var o=null;if(r.formatter){o=r.formatter(m[g],m)}else{o=m[g]}if(g==d.treeField){for(var q=0;q<e;q++){c.push('<span class="tree-indent"></span>')}if(m.state=="closed"){c.push('<span class="tree-hit tree-collapsed"></span>');c.push('<span class="tree-icon tree-folder '+(m.iconCls?m.iconCls:"")+'"></span>')}else{if(m.children&&m.children.length){c.push('<span class="tree-hit tree-expanded"></span>');c.push('<span class="tree-icon tree-folder tree-folder-open '+(m.iconCls?m.iconCls:"")+'"></span>')}else{c.push('<span class="tree-indent"></span>');c.push('<span class="tree-icon tree-file '+(m.iconCls?m.iconCls:"")+'"></span>')}}c.push('<span class="tree-title">'+o+"</span>")}else{c.push(o)}}c.push("</div>");c.push("</td>")}}return c.join("")},refreshRow:function(c,d){this.updateRow.call(this,c,d,{})},updateRow:function(i,e,d){var c=V.data(i,"treegrid").options;var j=V(i).treegrid("find",e);V.extend(j,d);var g=V(i).treegrid("getLevel",e)-1;var f=c.rowStyler?c.rowStyler.call(i,j):"";function h(k){var o=V(i).treegrid("getColumnFields",k);var n=c.finder.getTr(i,e,"body",(k?1:2));var m=n.find("div.datagrid-cell-rownumber").html();var l=n.find("div.datagrid-cell-check input[type=checkbox]").is(":checked");n.html(this.renderRow(i,o,k,g,j));n.attr("style",f||"");n.find("div.datagrid-cell-rownumber").html(m);if(l){n.find("div.datagrid-cell-check input[type=checkbox]")._propAttr("checked",true)}}h.call(this,true);h.call(this,false);V(i).treegrid("fixRowHeight",e)},onBeforeRender:function(e,d,g){if(!g){return false}var f=V.data(e,"treegrid").options;if(g.length==undefined){if(g.footer){V.data(e,"treegrid").footer=g.footer}if(g.total){V.data(e,"treegrid").total=g.total}g=this.transfer(e,d,g.rows)}else{function c(m,k){for(var j=0;j<m.length;j++){var l=m[j];l._parentId=k;if(l.children&&l.children.length){c(l.children,l[f.idField])}}}c(g,d)}var h=J(e,d);if(h){if(h.children){h.children=h.children.concat(g)}else{h.children=g}}else{V.data(e,"treegrid").data=V.data(e,"treegrid").data.concat(g)}if(!f.remoteSort){this.sort(e,g)}this.treeNodes=g;this.treeLevel=V(e).treegrid("getLevel",d)},sort:function(h,f){var e=V.data(h,"treegrid").options;var d=V(h).treegrid("getColumnOption",e.sortName);if(d){var g=d.sorter||function(j,i){return(j>i?1:-1)};c(f)}function c(k){k.sort(function(i,m){return g(i[e.sortName],m[e.sortName])*(e.sortOrder=="asc"?1:-1)});for(var j=0;j<k.length;j++){var l=k[j].children;if(l&&l.length){c(l)}}}},transfer:function(g,d,c){var f=V.data(g,"treegrid").options;var e=[];for(var h=0;h<c.length;h++){e.push(c[h])}var l=[];for(var h=0;h<e.length;h++){var j=e[h];if(!d){if(!j._parentId){l.push(j);U(e,j);h--}}else{if(j._parentId==d){l.push(j);U(e,j);h--}}}var k=[];for(var h=0;h<l.length;h++){k.push(l[h])}while(k.length){var m=k.shift();for(var h=0;h<e.length;h++){var j=e[h];if(j._parentId==m[f.idField]){if(m.children){m.children.push(j)}else{m.children=[j]}k.push(j);U(e,j);h--}}}return l}});V.fn.treegrid.defaults=V.extend({},V.fn.datagrid.defaults,{treeField:null,animate:false,singleSelect:true,view:O,loader:function(d,c,f){var e=V(this).treegrid("options");if(!e.url){return false}V.ajax({type:e.method,url:e.url,data:d,dataType:"json",success:function(g){c(g)},error:function(){f.apply(this,arguments)}})},loadFilter:function(d,c){return d},finder:{getTr:function(c,h,j,d){j=j||"body";d=d||0;var k=V.data(c,"datagrid").dc;if(d==0){var f=V.data(c,"treegrid").options;var g=f.finder.getTr(c,h,j,1);var e=f.finder.getTr(c,h,j,2);return g.add(e)}else{if(j=="body"){var i=V("#"+V.data(c,"datagrid").rowIdPrefix+"-"+d+"-"+h);if(!i.length){i=(d==1?k.body1:k.body2).find("tr[node-id="+h+"]")}return i}else{if(j=="footer"){return(d==1?k.footer1:k.footer2).find("tr[node-id="+h+"]")}else{if(j=="selected"){return(d==1?k.body1:k.body2).find("tr.datagrid-row-selected")}else{if(j=="last"){return(d==1?k.body1:k.body2).find("tr:last[node-id]")}else{if(j=="allbody"){return(d==1?k.body1:k.body2).find("tr[node-id]")}else{if(j=="allfooter"){return(d==1?k.footer1:k.footer2).find("tr[node-id]")}}}}}}}},getRow:function(d,c){var e=(typeof c=="object")?c.attr("node-id"):c;return V(d).treegrid("find",e)}},onBeforeLoad:function(d,c){},onLoadSuccess:function(c,d){},onLoadError:function(){},onBeforeCollapse:function(c){},onCollapse:function(c){},onBeforeExpand:function(c){},onExpand:function(c){},onClickRow:function(c){},onDblClickRow:function(c){},onClickCell:function(c,d){},onDblClickCell:function(d,c){},onContextMenu:function(c,d){},onBeforeEdit:function(c){},onAfterEdit:function(c,d){},onCancelEdit:function(c){}})})(jQuery);