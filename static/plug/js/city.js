var province_arr=[];jQuery.get("/city/findAllProvince",{},function(A){province_arr=A});function doInitCitySelect(G,F,I,E,D,J){$("#"+G).unbind("change");$("#"+F).unbind("change");$("#"+I).unbind("change");removAlleSelect(G);removAlleSelect(F);removAlleSelect(I);var A=document.getElementById(G);A.options[A.length]=new Option("省","");for(var H=0;H<province_arr.length;H++){A.options[A.length]=new Option(province_arr[H].name,province_arr[H].id)}var C=document.getElementById(F);C.options[C.length]=new Option("市","");var B=document.getElementById(I);B.options[B.length]=new Option("区县","");$("#"+G).change(function(){removAlleSelect(F);C.options[C.length]=new Option("市","");if($(this).val()!=""){jQuery.get("/city/findAllCity",{provinceId:$(this).val()},function(L){for(var K=0;K<L.length;K++){C.options[C.length]=new Option(L[K].name,L[K].id)}removAlleSelect(I);B.options[B.length]=new Option("区县","")})}});$("#"+F).change(function(){removAlleSelect(I);if($(this).val()!=""){B.options[B.length]=new Option("区县","");jQuery.get("/city/findAllcounty",{cityId:$(this).val()},function(L){for(var K=0;K<L.length;K++){B.options[B.length]=new Option(L[K].name,L[K].id)}})}});if(typeof E!="undefined"){A.value=E;jQuery.get("/city/findAllCity",{provinceId:E},function(L){for(var K=0;K<L.length;K++){C.options[C.length]=new Option(L[K].name,L[K].id)}if(typeof D!="undefined"){C.value=D;jQuery.get("/city/findAllcounty",{cityId:D},function(N){for(var M=0;M<N.length;M++){B.options[B.length]=new Option(N[M].name,N[M].id)}if(typeof J!="undefined"){B.value=J}})}})}}function doInitCitySelectTarento(I,H,L,D,E,G,F,P,J,O){$("#"+I).unbind("change");$("#"+H).unbind("change");$("#"+L).unbind("change");$("#"+D).unbind("change");$("#"+E).unbind("change");removAlleSelect(I);var A=document.getElementById(I);A.options[A.length]=new Option("-选择省份-","");for(var K=0;K<province_arr.length;K++){A.options[A.length]=new Option(province_arr[K].value,province_arr[K].id)}var M=document.getElementById(H);M.options[M.length]=new Option("-选择地区-","");var B=document.getElementById(L);B.options[B.length]=new Option("-选择县市区-","");var C=document.getElementById(D);C.options[C.length]=new Option("-选择街道-","");var N=document.getElementById(E);N.options[N.length]=new Option("-选择社区-","");$("#"+I).change(function(){removAlleSelect(H);M.options[M.length]=new Option("-选择地区-","");if($(this).val()!=""){jQuery.get("/city/street/getDistricts",{provinceId:$(this).val()},function(R){for(var Q=0;Q<R.length;Q++){M.options[M.length]=new Option(R[Q].value,R[Q].id)}removAlleSelect(L);B.options[B.length]=new Option("-选择县市区-","")})}});$("#"+H).change(function(){removAlleSelect(L);B.options[B.length]=new Option("-选择县市区-","");if($(this).val()!=""){jQuery.get("/city/street/getCitys",{districtId:$(this).val()},function(R){for(var Q=0;Q<R.length;Q++){B.options[B.length]=new Option(R[Q].value,R[Q].id)}})}});$("#"+L).change(function(){removAlleSelect(D);C.options[C.length]=new Option("-选择街道-","");if($(this).val()!=""){jQuery.get("/city/street/getStreets",{cityId:$(this).val()},function(R){for(var Q=0;Q<R.length;Q++){C.options[C.length]=new Option(R[Q].value,R[Q].id)}})}});$("#"+D).change(function(){removAlleSelect(E);N.options[N.length]=new Option("-选择社区-","");if($(this).val()!=""){jQuery.get("/city/street/getCommunities",{streetId:$(this).val()},function(R){for(var Q=0;Q<R.length;Q++){N.options[N.length]=new Option(R[Q].value,R[Q].id)}})}});if(typeof G!="undefined"&&G.length>0){A.value=G;jQuery.get("/city/street/getDistricts",{provinceId:G},function(R){for(var Q=0;Q<R.length;Q++){M.options[M.length]=new Option(R[Q].value,R[Q].id)}if(typeof F!="undefined"){M.value=F;jQuery.get("/city/street/getCitys",{districtId:F},function(T){for(var S=0;S<T.length;S++){B.options[B.length]=new Option(T[S].value,T[S].id)}if(typeof P!="undefined"){B.value=P;jQuery.get("/city/street/getStreets",{cityId:P},function(V){for(var U=0;U<V.length;U++){C.options[C.length]=new Option(V[U].value,V[U].id)}if(typeof J!="undefined"){C.value=J;jQuery.get("/city/street/getCommunities",{streetId:J},function(X){for(var W=0;W<X.length;W++){N.options[N.length]=new Option(X[W].value,X[W].id)}if(typeof O!="undefined"){N.value=O}})}})}})}})}};