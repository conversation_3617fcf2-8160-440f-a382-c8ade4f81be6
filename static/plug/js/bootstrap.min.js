/*
 * Bootstrap v3.3.7 (http://getbootstrap.com)
 * Copyright 2011-2016 Twitter, Inc.
 * Licensed under the MIT license
 */
if("undefined"==typeof jQuery){throw new Error("Bootstrap's JavaScript requires jQuery")}+function(B){var A=B.fn.jquery.split(" ")[0].split(".");if(A[0]<2&&A[1]<9||1==A[0]&&9==A[1]&&A[2]<1||A[0]>3){throw new Error("Bootstrap's JavaScript requires jQuery version 1.9.1 or higher, but lower than version 4")}}(jQuery),+function(B){function A(){var E=document.createElement("bootstrap"),C={WebkitTransition:"webkitTransitionEnd",MozTransition:"transitionend",OTransition:"oTransitionEnd otransitionend",transition:"transitionend"};for(var D in C){if(void 0!==E.style[D]){return{end:C[D]}}}return !1}B.fn.emulateTransitionEnd=function(E){var F=!1,C=this;B(this).one("bsTransitionEnd",function(){F=!0});var D=function(){F||B(C).trigger(B.support.transition.end)};return setTimeout(D,E),this},B(function(){B.support.transition=A(),B.support.transition&&(B.event.special.bsTransitionEnd={bindType:B.support.transition.end,delegateType:B.support.transition.end,handle:function(C){if(B(C.target).is(this)){return C.handleObj.handler.apply(this,arguments)}}})})}(jQuery),+function(E){function C(F){return this.each(function(){var H=E(this),G=H.data("bs.alert");G||H.data("bs.alert",G=new A(this)),"string"==typeof F&&G[F].call(H)})}var D='[data-dismiss="alert"]',A=function(F){E(F).on("click",D,this.close)};A.VERSION="3.3.7",A.TRANSITION_DURATION=150,A.prototype.close=function(H){function I(){F.detach().trigger("closed.bs.alert").remove()}var G=E(this),J=G.attr("data-target");J||(J=G.attr("href"),J=J&&J.replace(/.*(?=#[^\s]*$)/,""));var F=E("#"===J?[]:J);H&&H.preventDefault(),F.length||(F=G.closest(".alert")),F.trigger(H=E.Event("close.bs.alert")),H.isDefaultPrevented()||(F.removeClass("in"),E.support.transition&&F.hasClass("fade")?F.one("bsTransitionEnd",I).emulateTransitionEnd(A.TRANSITION_DURATION):I())};var B=E.fn.alert;E.fn.alert=C,E.fn.alert.Constructor=A,E.fn.alert.noConflict=function(){return E.fn.alert=B,this},E(document).on("click.bs.alert.data-api",D,A.prototype.close)}(jQuery),+function(D){function B(E){return this.each(function(){var F=D(this),G=F.data("bs.button"),H="object"==typeof E&&E;G||F.data("bs.button",G=new C(this,H)),"toggle"==E?G.toggle():E&&G.setState(E)})}var C=function(F,E){this.$element=D(F),this.options=D.extend({},C.DEFAULTS,E),this.isLoading=!1};C.VERSION="3.3.7",C.DEFAULTS={loadingText:"loading..."},C.prototype.setState=function(G){var H="disabled",E=this.$element,F=E.is("input")?"val":"html",I=E.data();G+="Text",null==I.resetText&&E.data("resetText",E[F]()),setTimeout(D.proxy(function(){E[F](null==I[G]?this.options[G]:I[G]),"loadingText"==G?(this.isLoading=!0,E.addClass(H).attr(H,H).prop(H,!0)):this.isLoading&&(this.isLoading=!1,E.removeClass(H).removeAttr(H).prop(H,!1))},this),0)},C.prototype.toggle=function(){var G=!0,E=this.$element.closest('[data-toggle="buttons"]');if(E.length){var F=this.$element.find("input");"radio"==F.prop("type")?(F.prop("checked")&&(G=!1),E.find(".active").removeClass("active"),this.$element.addClass("active")):"checkbox"==F.prop("type")&&(F.prop("checked")!==this.$element.hasClass("active")&&(G=!1),this.$element.toggleClass("active")),F.prop("checked",this.$element.hasClass("active")),G&&F.trigger("change")}else{this.$element.attr("aria-pressed",!this.$element.hasClass("active")),this.$element.toggleClass("active")}};var A=D.fn.button;D.fn.button=B,D.fn.button.Constructor=C,D.fn.button.noConflict=function(){return D.fn.button=A,this},D(document).on("click.bs.button.data-api",'[data-toggle^="button"]',function(F){var E=D(F.target).closest(".btn");B.call(E,"toggle"),D(F.target).is('input[type="radio"], input[type="checkbox"]')||(F.preventDefault(),E.is("input,button")?E.trigger("focus"):E.find("input:visible,button:visible").first().trigger("focus"))}).on("focus.bs.button.data-api blur.bs.button.data-api",'[data-toggle^="button"]',function(E){D(E.target).closest(".btn").toggleClass("focus",/^focus(in)?$/.test(E.type))})}(jQuery),+function(E){function C(F){return this.each(function(){var H=E(this),I=H.data("bs.carousel"),J=E.extend({},D.DEFAULTS,H.data(),"object"==typeof F&&F),G="string"==typeof F?F:J.slide;I||H.data("bs.carousel",I=new D(this,J)),"number"==typeof F?I.to(F):G?I[G]():J.interval&&I.pause().cycle()})}var D=function(F,G){this.$element=E(F),this.$indicators=this.$element.find(".carousel-indicators"),this.options=G,this.paused=null,this.sliding=null,this.interval=null,this.$active=null,this.$items=null,this.options.keyboard&&this.$element.on("keydown.bs.carousel",E.proxy(this.keydown,this)),"hover"==this.options.pause&&!("ontouchstart" in document.documentElement)&&this.$element.on("mouseenter.bs.carousel",E.proxy(this.pause,this)).on("mouseleave.bs.carousel",E.proxy(this.cycle,this))};D.VERSION="3.3.7",D.TRANSITION_DURATION=600,D.DEFAULTS={interval:5000,pause:"hover",wrap:!0,keyboard:!0},D.prototype.keydown=function(F){if(!/input|textarea/i.test(F.target.tagName)){switch(F.which){case 37:this.prev();break;case 39:this.next();break;default:return}F.preventDefault()}},D.prototype.cycle=function(F){return F||(this.paused=!1),this.interval&&clearInterval(this.interval),this.options.interval&&!this.paused&&(this.interval=setInterval(E.proxy(this.next,this),this.options.interval)),this},D.prototype.getItemIndex=function(F){return this.$items=F.parent().children(".item"),this.$items.index(F||this.$active)},D.prototype.getItemForDirection=function(J,H){var I=this.getItemIndex(H),F="prev"==J&&0===I||"next"==J&&I==this.$items.length-1;if(F&&!this.options.wrap){return H}var G="prev"==J?-1:1,K=(I+G)%this.$items.length;return this.$items.eq(K)},D.prototype.to=function(H){var F=this,G=this.getItemIndex(this.$active=this.$element.find(".item.active"));if(!(H>this.$items.length-1||H<0)){return this.sliding?this.$element.one("slid.bs.carousel",function(){F.to(H)}):G==H?this.pause().cycle():this.slide(H>G?"next":"prev",this.$items.eq(H))}},D.prototype.pause=function(F){return F||(this.paused=!0),this.$element.find(".next, .prev").length&&E.support.transition&&(this.$element.trigger(E.support.transition.end),this.cycle(!0)),this.interval=clearInterval(this.interval),this},D.prototype.next=function(){if(!this.sliding){return this.slide("next")}},D.prototype.prev=function(){if(!this.sliding){return this.slide("prev")}},D.prototype.slide=function(M,P){var F=this.$element.find(".item.active"),N=P||this.getItemForDirection(M,F),O=this.interval,I="next"==M?"left":"right",J=this;if(N.hasClass("active")){return this.sliding=!1}var G=N[0],H=E.Event("slide.bs.carousel",{relatedTarget:G,direction:I});if(this.$element.trigger(H),!H.isDefaultPrevented()){if(this.sliding=!0,O&&this.pause(),this.$indicators.length){this.$indicators.find(".active").removeClass("active");var K=E(this.$indicators.children()[this.getItemIndex(N)]);K&&K.addClass("active")}var L=E.Event("slid.bs.carousel",{relatedTarget:G,direction:I});return E.support.transition&&this.$element.hasClass("slide")?(N.addClass(M),N[0].offsetWidth,F.addClass(I),N.addClass(I),F.one("bsTransitionEnd",function(){N.removeClass([M,I].join(" ")).addClass("active"),F.removeClass(["active",I].join(" ")),J.sliding=!1,setTimeout(function(){J.$element.trigger(L)},0)}).emulateTransitionEnd(D.TRANSITION_DURATION)):(F.removeClass("active"),N.addClass("active"),this.sliding=!1,this.$element.trigger(L)),O&&this.cycle(),this}};var A=E.fn.carousel;E.fn.carousel=C,E.fn.carousel.Constructor=D,E.fn.carousel.noConflict=function(){return E.fn.carousel=A,this};var B=function(J){var G,H=E(this),K=E(H.attr("data-target")||(G=H.attr("href"))&&G.replace(/.*(?=#[^\s]+$)/,""));if(K.hasClass("carousel")){var F=E.extend({},K.data(),H.data()),I=H.attr("data-slide-to");I&&(F.interval=!1),C.call(K,F),I&&K.data("bs.carousel").to(I),J.preventDefault()}};E(document).on("click.bs.carousel.data-api","[data-slide]",B).on("click.bs.carousel.data-api","[data-slide-to]",B),E(window).on("load",function(){E('[data-ride="carousel"]').each(function(){var F=E(this);C.call(F,F.data())})})}(jQuery),+function(E){function C(G){var H,F=G.attr("data-target")||(H=G.attr("href"))&&H.replace(/.*(?=#[^\s]+$)/,"");return E(F)}function D(F){return this.each(function(){var H=E(this),G=H.data("bs.collapse"),I=E.extend({},A.DEFAULTS,H.data(),"object"==typeof F&&F);!G&&I.toggle&&/show|hide/.test(F)&&(I.toggle=!1),G||H.data("bs.collapse",G=new A(this,I)),"string"==typeof F&&G[F]()})}var A=function(F,G){this.$element=E(F),this.options=E.extend({},A.DEFAULTS,G),this.$trigger=E('[data-toggle="collapse"][href="#'+F.id+'"],[data-toggle="collapse"][data-target="#'+F.id+'"]'),this.transitioning=null,this.options.parent?this.$parent=this.getParent():this.addAriaAndCollapsedClass(this.$element,this.$trigger),this.options.toggle&&this.toggle()};A.VERSION="3.3.7",A.TRANSITION_DURATION=350,A.DEFAULTS={toggle:!0},A.prototype.dimension=function(){var F=this.$element.hasClass("width");return F?"width":"height"},A.prototype.show=function(){if(!this.transitioning&&!this.$element.hasClass("in")){var J,H=this.$parent&&this.$parent.children(".panel").children(".in, .collapsing");if(!(H&&H.length&&(J=H.data("bs.collapse"),J&&J.transitioning))){var K=E.Event("show.bs.collapse");if(this.$element.trigger(K),!K.isDefaultPrevented()){H&&H.length&&(D.call(H,"hide"),J||H.data("bs.collapse",null));var F=this.dimension();this.$element.removeClass("collapse").addClass("collapsing")[F](0).attr("aria-expanded",!0),this.$trigger.removeClass("collapsed").attr("aria-expanded",!0),this.transitioning=1;var G=function(){this.$element.removeClass("collapsing").addClass("collapse in")[F](""),this.transitioning=0,this.$element.trigger("shown.bs.collapse")};if(!E.support.transition){return G.call(this)}var I=E.camelCase(["scroll",F].join("-"));this.$element.one("bsTransitionEnd",E.proxy(G,this)).emulateTransitionEnd(A.TRANSITION_DURATION)[F](this.$element[0][I])}}}},A.prototype.hide=function(){if(!this.transitioning&&this.$element.hasClass("in")){var G=E.Event("hide.bs.collapse");if(this.$element.trigger(G),!G.isDefaultPrevented()){var H=this.dimension();this.$element[H](this.$element[H]())[0].offsetHeight,this.$element.addClass("collapsing").removeClass("collapse in").attr("aria-expanded",!1),this.$trigger.addClass("collapsed").attr("aria-expanded",!1),this.transitioning=1;var F=function(){this.transitioning=0,this.$element.removeClass("collapsing").addClass("collapse").trigger("hidden.bs.collapse")};return E.support.transition?void this.$element[H](0).one("bsTransitionEnd",E.proxy(F,this)).emulateTransitionEnd(A.TRANSITION_DURATION):F.call(this)}}},A.prototype.toggle=function(){this[this.$element.hasClass("in")?"hide":"show"]()},A.prototype.getParent=function(){return E(this.options.parent).find('[data-toggle="collapse"][data-parent="'+this.options.parent+'"]').each(E.proxy(function(H,F){var G=E(F);this.addAriaAndCollapsedClass(C(G),G)},this)).end()},A.prototype.addAriaAndCollapsedClass=function(H,F){var G=H.hasClass("in");H.attr("aria-expanded",G),F.toggleClass("collapsed",!G).attr("aria-expanded",G)};var B=E.fn.collapse;E.fn.collapse=D,E.fn.collapse.Constructor=A,E.fn.collapse.noConflict=function(){return E.fn.collapse=B,this},E(document).on("click.bs.collapse.data-api",'[data-toggle="collapse"]',function(G){var H=E(this);H.attr("data-target")||G.preventDefault();var J=C(H),F=J.data("bs.collapse"),I=F?"toggle":H.data();D.call(J,I)})}(jQuery),+function(E){function C(J){var K=J.attr("data-target");K||(K=J.attr("href"),K=K&&/#[A-Za-z]/.test(K)&&K.replace(/.*(?=#[^\s]*$)/,""));var I=K&&E(K);return I&&I.length?I:J.parent()}function D(I){I&&3===I.which||(E(A).remove(),E(F).each(function(){var J=E(this),K=C(J),L={relatedTarget:this};K.hasClass("open")&&(I&&"click"==I.type&&/input|textarea/i.test(I.target.tagName)&&E.contains(K[0],I.target)||(K.trigger(I=E.Event("hide.bs.dropdown",L)),I.isDefaultPrevented()||(J.attr("aria-expanded","false"),K.removeClass("open").trigger(E.Event("hidden.bs.dropdown",L)))))}))}function H(I){return this.each(function(){var K=E(this),J=K.data("bs.dropdown");J||K.data("bs.dropdown",J=new G(this)),"string"==typeof I&&J[I].call(K)})}var A=".dropdown-backdrop",F='[data-toggle="dropdown"]',G=function(I){E(I).on("click.bs.dropdown",this.toggle)};G.VERSION="3.3.7",G.prototype.toggle=function(J){var K=E(this);if(!K.is(".disabled, :disabled")){var M=C(K),I=M.hasClass("open");if(D(),!I){"ontouchstart" in document.documentElement&&!M.closest(".navbar-nav").length&&E(document.createElement("div")).addClass("dropdown-backdrop").insertAfter(E(this)).on("click",D);var L={relatedTarget:this};if(M.trigger(J=E.Event("show.bs.dropdown",L)),J.isDefaultPrevented()){return}K.trigger("focus").attr("aria-expanded","true"),M.toggleClass("open").trigger(E.Event("shown.bs.dropdown",L))}return !1}},G.prototype.keydown=function(N){if(/(38|40|27|32)/.test(N.which)&&!/input|textarea/i.test(N.target.tagName)){var J=E(this);if(N.preventDefault(),N.stopPropagation(),!J.is(".disabled, :disabled")){var K=C(J),I=K.hasClass("open");if(!I&&27!=N.which||I&&27==N.which){return 27==N.which&&K.find(F).trigger("focus"),J.trigger("click")}var L=" li:not(.disabled):visible a",M=K.find(".dropdown-menu"+L);if(M.length){var O=M.index(N.target);38==N.which&&O>0&&O--,40==N.which&&O<M.length-1&&O++,~O||(O=0),M.eq(O).trigger("focus")}}}};var B=E.fn.dropdown;E.fn.dropdown=H,E.fn.dropdown.Constructor=G,E.fn.dropdown.noConflict=function(){return E.fn.dropdown=B,this},E(document).on("click.bs.dropdown.data-api",D).on("click.bs.dropdown.data-api",".dropdown form",function(I){I.stopPropagation()}).on("click.bs.dropdown.data-api",F,G.prototype.toggle).on("keydown.bs.dropdown.data-api",F,G.prototype.keydown).on("keydown.bs.dropdown.data-api",".dropdown-menu",G.prototype.keydown)}(jQuery),+function(D){function B(F,E){return this.each(function(){var H=D(this),I=H.data("bs.modal"),G=D.extend({},C.DEFAULTS,H.data(),"object"==typeof F&&F);I||H.data("bs.modal",I=new C(this,G)),"string"==typeof F?I[F](E):G.show&&I.show(E)})}var C=function(E,F){this.options=F,this.$body=D(document.body),this.$element=D(E),this.$dialog=this.$element.find(".modal-dialog"),this.$backdrop=null,this.isShown=null,this.originalBodyPad=null,this.scrollbarWidth=0,this.ignoreBackdropClick=!1,this.options.remote&&this.$element.find(".modal-content").load(this.options.remote,D.proxy(function(){this.$element.trigger("loaded.bs.modal")},this))};C.VERSION="3.3.7",C.TRANSITION_DURATION=300,C.BACKDROP_TRANSITION_DURATION=150,C.DEFAULTS={backdrop:!0,keyboard:!0,show:!0},C.prototype.toggle=function(E){return this.isShown?this.hide():this.show(E)},C.prototype.show=function(G){var E=this,F=D.Event("show.bs.modal",{relatedTarget:G});this.$element.trigger(F),this.isShown||F.isDefaultPrevented()||(this.isShown=!0,this.checkScrollbar(),this.setScrollbar(),this.$body.addClass("modal-open"),this.escape(),this.resize(),this.$element.on("click.dismiss.bs.modal",'[data-dismiss="modal"]',D.proxy(this.hide,this)),this.$dialog.on("mousedown.dismiss.bs.modal",function(){E.$element.one("mouseup.dismiss.bs.modal",function(H){D(H.target).is(E.$element)&&(E.ignoreBackdropClick=!0)})}),this.backdrop(function(){var H=D.support.transition&&E.$element.hasClass("fade");E.$element.parent().length||E.$element.appendTo(E.$body),E.$element.show().scrollTop(0),E.adjustDialog(),H&&E.$element[0].offsetWidth,E.$element.addClass("in"),E.enforceFocus();var I=D.Event("shown.bs.modal",{relatedTarget:G});H?E.$dialog.one("bsTransitionEnd",function(){E.$element.trigger("focus").trigger(I)}).emulateTransitionEnd(C.TRANSITION_DURATION):E.$element.trigger("focus").trigger(I)}))},C.prototype.hide=function(E){E&&E.preventDefault(),E=D.Event("hide.bs.modal"),this.$element.trigger(E),this.isShown&&!E.isDefaultPrevented()&&(this.isShown=!1,this.escape(),this.resize(),D(document).off("focusin.bs.modal"),this.$element.removeClass("in").off("click.dismiss.bs.modal").off("mouseup.dismiss.bs.modal"),this.$dialog.off("mousedown.dismiss.bs.modal"),D.support.transition&&this.$element.hasClass("fade")?this.$element.one("bsTransitionEnd",D.proxy(this.hideModal,this)).emulateTransitionEnd(C.TRANSITION_DURATION):this.hideModal())},C.prototype.enforceFocus=function(){D(document).off("focusin.bs.modal").on("focusin.bs.modal",D.proxy(function(E){document===E.target||this.$element[0]===E.target||this.$element.has(E.target).length||this.$element.trigger("focus")},this))},C.prototype.escape=function(){this.isShown&&this.options.keyboard?this.$element.on("keydown.dismiss.bs.modal",D.proxy(function(E){27==E.which&&this.hide()},this)):this.isShown||this.$element.off("keydown.dismiss.bs.modal")},C.prototype.resize=function(){this.isShown?D(window).on("resize.bs.modal",D.proxy(this.handleUpdate,this)):D(window).off("resize.bs.modal")},C.prototype.hideModal=function(){var E=this;this.$element.hide(),this.backdrop(function(){E.$body.removeClass("modal-open"),E.resetAdjustments(),E.resetScrollbar(),E.$element.trigger("hidden.bs.modal")})},C.prototype.removeBackdrop=function(){this.$backdrop&&this.$backdrop.remove(),this.$backdrop=null},C.prototype.backdrop=function(H){var F=this,G=this.$element.hasClass("fade")?"fade":"";if(this.isShown&&this.options.backdrop){var I=D.support.transition&&G;if(this.$backdrop=D(document.createElement("div")).addClass("modal-backdrop "+G).appendTo(this.$body),this.$element.on("click.dismiss.bs.modal",D.proxy(function(J){return this.ignoreBackdropClick?void (this.ignoreBackdropClick=!1):void (J.target===J.currentTarget&&("static"==this.options.backdrop?this.$element[0].focus():this.hide()))},this)),I&&this.$backdrop[0].offsetWidth,this.$backdrop.addClass("in"),!H){return}I?this.$backdrop.one("bsTransitionEnd",H).emulateTransitionEnd(C.BACKDROP_TRANSITION_DURATION):H()}else{if(!this.isShown&&this.$backdrop){this.$backdrop.removeClass("in");var E=function(){F.removeBackdrop(),H&&H()};D.support.transition&&this.$element.hasClass("fade")?this.$backdrop.one("bsTransitionEnd",E).emulateTransitionEnd(C.BACKDROP_TRANSITION_DURATION):E()}else{H&&H()}}},C.prototype.handleUpdate=function(){this.adjustDialog()},C.prototype.adjustDialog=function(){var E=this.$element[0].scrollHeight>document.documentElement.clientHeight;this.$element.css({paddingLeft:!this.bodyIsOverflowing&&E?this.scrollbarWidth:"",paddingRight:this.bodyIsOverflowing&&!E?this.scrollbarWidth:""})},C.prototype.resetAdjustments=function(){this.$element.css({paddingLeft:"",paddingRight:""})},C.prototype.checkScrollbar=function(){var F=window.innerWidth;if(!F){var E=document.documentElement.getBoundingClientRect();F=E.right-Math.abs(E.left)}this.bodyIsOverflowing=document.body.clientWidth<F,this.scrollbarWidth=this.measureScrollbar()},C.prototype.setScrollbar=function(){var E=parseInt(this.$body.css("padding-right")||0,10);this.originalBodyPad=document.body.style.paddingRight||"",this.bodyIsOverflowing&&this.$body.css("padding-right",E+this.scrollbarWidth)},C.prototype.resetScrollbar=function(){this.$body.css("padding-right",this.originalBodyPad)},C.prototype.measureScrollbar=function(){var F=document.createElement("div");F.className="modal-scrollbar-measure",this.$body.append(F);var E=F.offsetWidth-F.clientWidth;return this.$body[0].removeChild(F),E};var A=D.fn.modal;D.fn.modal=B,D.fn.modal.Constructor=C,D.fn.modal.noConflict=function(){return D.fn.modal=A,this},D(document).on("click.bs.modal.data-api",'[data-toggle="modal"]',function(H){var F=D(this),G=F.attr("href"),I=D(F.attr("data-target")||G&&G.replace(/.*(?=#[^\s]+$)/,"")),E=I.data("bs.modal")?"toggle":D.extend({remote:!/#/.test(G)&&G},I.data(),F.data());F.is("a")&&H.preventDefault(),I.one("show.bs.modal",function(J){J.isDefaultPrevented()||I.one("hidden.bs.modal",function(){F.is(":visible")&&F.trigger("focus")})}),B.call(I,E,this)})}(jQuery),+function(D){function B(E){return this.each(function(){var F=D(this),G=F.data("bs.tooltip"),H="object"==typeof E&&E;!G&&/destroy|hide/.test(E)||(G||F.data("bs.tooltip",G=new C(this,H)),"string"==typeof E&&G[E]())})}var C=function(F,E){this.type=null,this.options=null,this.enabled=null,this.timeout=null,this.hoverState=null,this.$element=null,this.inState=null,this.init("tooltip",F,E)};C.VERSION="3.3.7",C.TRANSITION_DURATION=150,C.DEFAULTS={animation:!0,placement:"top",selector:!1,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',trigger:"hover focus",title:"",delay:0,html:!1,container:!1,viewport:{selector:"body",padding:0}},C.prototype.init=function(H,I,L){if(this.enabled=!0,this.type=H,this.$element=D(I),this.options=this.getOptions(L),this.$viewport=this.options.viewport&&D(D.isFunction(this.options.viewport)?this.options.viewport.call(this,this.$element):this.options.viewport.selector||this.options.viewport),this.inState={click:!1,hover:!1,focus:!1},this.$element[0] instanceof document.constructor&&!this.options.selector){throw new Error("`selector` option must be specified when initializing "+this.type+" on the window.document object!")}for(var E=this.options.trigger.split(" "),J=E.length;J--;){var K=E[J];if("click"==K){this.$element.on("click."+this.type,this.options.selector,D.proxy(this.toggle,this))}else{if("manual"!=K){var F="hover"==K?"mouseenter":"focusin",G="hover"==K?"mouseleave":"focusout";this.$element.on(F+"."+this.type,this.options.selector,D.proxy(this.enter,this)),this.$element.on(G+"."+this.type,this.options.selector,D.proxy(this.leave,this))}}}this.options.selector?this._options=D.extend({},this.options,{trigger:"manual",selector:""}):this.fixTitle()},C.prototype.getDefaults=function(){return C.DEFAULTS},C.prototype.getOptions=function(E){return E=D.extend({},this.getDefaults(),this.$element.data(),E),E.delay&&"number"==typeof E.delay&&(E.delay={show:E.delay,hide:E.delay}),E},C.prototype.getDelegateOptions=function(){var E={},F=this.getDefaults();return this._options&&D.each(this._options,function(H,G){F[H]!=G&&(E[H]=G)}),E},C.prototype.enter=function(E){var F=E instanceof this.constructor?E:D(E.currentTarget).data("bs."+this.type);return F||(F=new this.constructor(E.currentTarget,this.getDelegateOptions()),D(E.currentTarget).data("bs."+this.type,F)),E instanceof D.Event&&(F.inState["focusin"==E.type?"focus":"hover"]=!0),F.tip().hasClass("in")||"in"==F.hoverState?void (F.hoverState="in"):(clearTimeout(F.timeout),F.hoverState="in",F.options.delay&&F.options.delay.show?void (F.timeout=setTimeout(function(){"in"==F.hoverState&&F.show()},F.options.delay.show)):F.show())},C.prototype.isInStateTrue=function(){for(var E in this.inState){if(this.inState[E]){return !0}}return !1},C.prototype.leave=function(E){var F=E instanceof this.constructor?E:D(E.currentTarget).data("bs."+this.type);if(F||(F=new this.constructor(E.currentTarget,this.getDelegateOptions()),D(E.currentTarget).data("bs."+this.type,F)),E instanceof D.Event&&(F.inState["focusout"==E.type?"focus":"hover"]=!1),!F.isInStateTrue()){return clearTimeout(F.timeout),F.hoverState="out",F.options.delay&&F.options.delay.hide?void (F.timeout=setTimeout(function(){"out"==F.hoverState&&F.hide()},F.options.delay.hide)):F.hide()}},C.prototype.show=function(){var P=D.Event("show.bs."+this.type);if(this.hasContent()&&this.enabled){this.$element.trigger(P);var S=D.contains(this.$element[0].ownerDocument.documentElement,this.$element[0]);if(P.isDefaultPrevented()||!S){return}var E=this,Q=this.tip(),R=this.getUID(this.type);this.setContent(),Q.attr("id",R),this.$element.attr("aria-describedby",R),this.options.animation&&Q.addClass("fade");var J="function"==typeof this.options.placement?this.options.placement.call(this,Q[0],this.$element[0]):this.options.placement,K=/\s?auto?\s?/i,H=K.test(J);H&&(J=J.replace(K,"")||"top"),Q.detach().css({top:0,left:0,display:"block"}).addClass(J).data("bs."+this.type,this),this.options.container?Q.appendTo(this.options.container):Q.insertAfter(this.$element),this.$element.trigger("inserted.bs."+this.type);var I=this.getPosition(),N=Q[0].offsetWidth,O=Q[0].offsetHeight;if(H){var L=J,M=this.getPosition(this.$viewport);J="bottom"==J&&I.bottom+O>M.bottom?"top":"top"==J&&I.top-O<M.top?"bottom":"right"==J&&I.right+N>M.width?"left":"left"==J&&I.left-N<M.left?"right":J,Q.removeClass(L).addClass(J)}var F=this.getCalculatedOffset(J,I,N,O);this.applyPlacement(F,J);var G=function(){var T=E.hoverState;E.$element.trigger("shown.bs."+E.type),E.hoverState=null,"out"==T&&E.leave(E)};D.support.transition&&this.$tip.hasClass("fade")?Q.one("bsTransitionEnd",G).emulateTransitionEnd(C.TRANSITION_DURATION):G()}},C.prototype.applyPlacement=function(M,N){var Q=this.tip(),E=Q[0].offsetWidth,O=Q[0].offsetHeight,P=parseInt(Q.css("margin-top"),10),H=parseInt(Q.css("margin-left"),10);isNaN(P)&&(P=0),isNaN(H)&&(H=0),M.top+=P,M.left+=H,D.offset.setOffset(Q[0],D.extend({using:function(R){Q.css({top:Math.round(R.top),left:Math.round(R.left)})}},M),0),Q.addClass("in");var I=Q[0].offsetWidth,F=Q[0].offsetHeight;"top"==N&&F!=O&&(M.top=M.top+O-F);var G=this.getViewportAdjustedDelta(N,M,I,F);G.left?M.left+=G.left:M.top+=G.top;var K=/top|bottom/.test(N),L=K?2*G.left-E+I:2*G.top-O+F,J=K?"offsetWidth":"offsetHeight";Q.offset(M),this.replaceArrow(L,Q[0][J],K)},C.prototype.replaceArrow=function(G,E,F){this.arrow().css(F?"left":"top",50*(1-G/E)+"%").css(F?"top":"left","")},C.prototype.setContent=function(){var F=this.tip(),E=this.getTitle();F.find(".tooltip-inner")[this.options.html?"html":"text"](E),F.removeClass("fade in top bottom left right")},C.prototype.hide=function(H){function F(){"in"!=G.hoverState&&I.detach(),G.$element&&G.$element.removeAttr("aria-describedby").trigger("hidden.bs."+G.type),H&&H()}var G=this,I=D(this.$tip),E=D.Event("hide.bs."+this.type);if(this.$element.trigger(E),!E.isDefaultPrevented()){return I.removeClass("in"),D.support.transition&&I.hasClass("fade")?I.one("bsTransitionEnd",F).emulateTransitionEnd(C.TRANSITION_DURATION):F(),this.hoverState=null,this}},C.prototype.fixTitle=function(){var E=this.$element;(E.attr("title")||"string"!=typeof E.attr("data-original-title"))&&E.attr("data-original-title",E.attr("title")||"").attr("title","")},C.prototype.hasContent=function(){return this.getTitle()},C.prototype.getPosition=function(H){H=H||this.$element;var I=H[0],L="BODY"==I.tagName,E=I.getBoundingClientRect();null==E.width&&(E=D.extend({},E,{width:E.right-E.left,height:E.bottom-E.top}));var J=window.SVGElement&&I instanceof window.SVGElement,K=L?{top:0,left:0}:J?null:H.offset(),F={scroll:L?document.documentElement.scrollTop||document.body.scrollTop:H.scrollTop()},G=L?{width:D(window).width(),height:D(window).height()}:null;return D.extend({},E,F,G,K)},C.prototype.getCalculatedOffset=function(H,F,G,E){return"bottom"==H?{top:F.top+F.height,left:F.left+F.width/2-G/2}:"top"==H?{top:F.top-E,left:F.left+F.width/2-G/2}:"left"==H?{top:F.top+F.height/2-E/2,left:F.left-G}:{top:F.top+F.height/2-E/2,left:F.left+F.width}},C.prototype.getViewportAdjustedDelta=function(L,J,K,O){var E={top:0,left:0};if(!this.$viewport){return E}var M=this.options.viewport&&this.options.viewport.padding||0,N=this.getPosition(this.$viewport);if(/right|left/.test(L)){var H=J.top-M-N.scroll,I=J.top+M-N.scroll+O;H<N.top?E.top=N.top-H:I>N.top+N.height&&(E.top=N.top+N.height-I)}else{var F=J.left-M,G=J.left+M+K;F<N.left?E.left=N.left-F:G>N.right&&(E.left=N.left+N.width-G)}return E},C.prototype.getTitle=function(){var G,E=this.$element,F=this.options;return G=E.attr("data-original-title")||("function"==typeof F.title?F.title.call(E[0]):F.title)},C.prototype.getUID=function(E){do{E+=~~(1000000*Math.random())}while(document.getElementById(E));return E},C.prototype.tip=function(){if(!this.$tip&&(this.$tip=D(this.options.template),1!=this.$tip.length)){throw new Error(this.type+" `template` option must consist of exactly 1 top-level element!")}return this.$tip},C.prototype.arrow=function(){return this.$arrow=this.$arrow||this.tip().find(".tooltip-arrow")},C.prototype.enable=function(){this.enabled=!0},C.prototype.disable=function(){this.enabled=!1},C.prototype.toggleEnabled=function(){this.enabled=!this.enabled},C.prototype.toggle=function(E){var F=this;E&&(F=D(E.currentTarget).data("bs."+this.type),F||(F=new this.constructor(E.currentTarget,this.getDelegateOptions()),D(E.currentTarget).data("bs."+this.type,F))),E?(F.inState.click=!F.inState.click,F.isInStateTrue()?F.enter(F):F.leave(F)):F.tip().hasClass("in")?F.leave(F):F.enter(F)},C.prototype.destroy=function(){var E=this;clearTimeout(this.timeout),this.hide(function(){E.$element.off("."+E.type).removeData("bs."+E.type),E.$tip&&E.$tip.detach(),E.$tip=null,E.$arrow=null,E.$viewport=null,E.$element=null})};var A=D.fn.tooltip;D.fn.tooltip=B,D.fn.tooltip.Constructor=C,D.fn.tooltip.noConflict=function(){return D.fn.tooltip=A,this}}(jQuery),+function(D){function B(E){return this.each(function(){var F=D(this),G=F.data("bs.popover"),H="object"==typeof E&&E;!G&&/destroy|hide/.test(E)||(G||F.data("bs.popover",G=new C(this,H)),"string"==typeof E&&G[E]())})}var C=function(F,E){this.init("popover",F,E)};if(!D.fn.tooltip){throw new Error("Popover requires tooltip.js")}C.VERSION="3.3.7",C.DEFAULTS=D.extend({},D.fn.tooltip.Constructor.DEFAULTS,{placement:"right",trigger:"click",content:"",template:'<div class="popover" role="tooltip"><div class="arrow"></div><h3 class="popover-title"></h3><div class="popover-content"></div></div>'}),C.prototype=D.extend({},D.fn.tooltip.Constructor.prototype),C.prototype.constructor=C,C.prototype.getDefaults=function(){return C.DEFAULTS},C.prototype.setContent=function(){var G=this.tip(),E=this.getTitle(),F=this.getContent();G.find(".popover-title")[this.options.html?"html":"text"](E),G.find(".popover-content").children().detach().end()[this.options.html?"string"==typeof F?"html":"append":"text"](F),G.removeClass("fade top bottom left right in"),G.find(".popover-title").html()||G.find(".popover-title").hide()},C.prototype.hasContent=function(){return this.getTitle()||this.getContent()},C.prototype.getContent=function(){var F=this.$element,E=this.options;return F.attr("data-content")||("function"==typeof E.content?E.content.call(F[0]):E.content)},C.prototype.arrow=function(){return this.$arrow=this.$arrow||this.tip().find(".arrow")};var A=D.fn.popover;D.fn.popover=B,D.fn.popover.Constructor=C,D.fn.popover.noConflict=function(){return D.fn.popover=A,this}}(jQuery),+function(D){function B(F,E){this.$body=D(document.body),this.$scrollElement=D(D(F).is(document.body)?window:F),this.options=D.extend({},B.DEFAULTS,E),this.selector=(this.options.target||"")+" .nav li > a",this.offsets=[],this.targets=[],this.activeTarget=null,this.scrollHeight=0,this.$scrollElement.on("scroll.bs.scrollspy",D.proxy(this.process,this)),this.refresh(),this.process()}function C(E){return this.each(function(){var F=D(this),G=F.data("bs.scrollspy"),H="object"==typeof E&&E;G||F.data("bs.scrollspy",G=new B(this,H)),"string"==typeof E&&G[E]()})}B.VERSION="3.3.7",B.DEFAULTS={offset:10},B.prototype.getScrollHeight=function(){return this.$scrollElement[0].scrollHeight||Math.max(this.$body[0].scrollHeight,document.documentElement.scrollHeight)},B.prototype.refresh=function(){var F=this,G="offset",E=0;this.offsets=[],this.targets=[],this.scrollHeight=this.getScrollHeight(),D.isWindow(this.$scrollElement[0])||(G="position",E=this.$scrollElement.scrollTop()),this.$body.find(this.selector).map(function(){var I=D(this),H=I.data("target")||I.attr("href"),J=/^#./.test(H)&&D(H);return J&&J.length&&J.is(":visible")&&[[J[G]().top+E,H]]||null}).sort(function(I,H){return I[0]-H[0]}).each(function(){F.offsets.push(this[0]),F.targets.push(this[1])})},B.prototype.process=function(){var J,H=this.$scrollElement.scrollTop()+this.options.offset,I=this.getScrollHeight(),F=this.options.offset+I-this.$scrollElement.height(),G=this.offsets,K=this.targets,E=this.activeTarget;if(this.scrollHeight!=I&&this.refresh(),H>=F){return E!=(J=K[K.length-1])&&this.activate(J)}if(E&&H<G[0]){return this.activeTarget=null,this.clear()}for(J=G.length;J--;){E!=K[J]&&H>=G[J]&&(void 0===G[J+1]||H<G[J+1])&&this.activate(K[J])}},B.prototype.activate=function(F){this.activeTarget=F,this.clear();var G=this.selector+'[data-target="'+F+'"],'+this.selector+'[href="'+F+'"]',E=D(G).parents("li").addClass("active");E.parent(".dropdown-menu").length&&(E=E.closest("li.dropdown").addClass("active")),E.trigger("activate.bs.scrollspy")},B.prototype.clear=function(){D(this.selector).parentsUntil(this.options.target,".active").removeClass("active")};var A=D.fn.scrollspy;D.fn.scrollspy=C,D.fn.scrollspy.Constructor=B,D.fn.scrollspy.noConflict=function(){return D.fn.scrollspy=A,this},D(window).on("load.bs.scrollspy.data-api",function(){D('[data-spy="scroll"]').each(function(){var E=D(this);C.call(E,E.data())})})}(jQuery),+function(E){function C(F){return this.each(function(){var G=E(this),H=G.data("bs.tab");H||G.data("bs.tab",H=new D(this)),"string"==typeof F&&H[F]()})}var D=function(F){this.element=E(F)};D.VERSION="3.3.7",D.TRANSITION_DURATION=150,D.prototype.show=function(){var J=this.element,K=J.closest("ul:not(.dropdown-menu)"),G=J.data("target");if(G||(G=J.attr("href"),G=G&&G.replace(/.*(?=#[^\s]*$)/,"")),!J.parent("li").hasClass("active")){var H=K.find(".active:last a"),L=E.Event("hide.bs.tab",{relatedTarget:J[0]}),F=E.Event("show.bs.tab",{relatedTarget:H[0]});if(H.trigger(L),J.trigger(F),!F.isDefaultPrevented()&&!L.isDefaultPrevented()){var I=E(G);this.activate(J.closest("li"),K),this.activate(I,I.parent(),function(){H.trigger({type:"hidden.bs.tab",relatedTarget:J[0]}),J.trigger({type:"shown.bs.tab",relatedTarget:H[0]})})}}},D.prototype.activate=function(J,G,H){function K(){F.removeClass("active").find("> .dropdown-menu > .active").removeClass("active").end().find('[data-toggle="tab"]').attr("aria-expanded",!1),J.addClass("active").find('[data-toggle="tab"]').attr("aria-expanded",!0),I?(J[0].offsetWidth,J.addClass("in")):J.removeClass("fade"),J.parent(".dropdown-menu").length&&J.closest("li.dropdown").addClass("active").end().find('[data-toggle="tab"]').attr("aria-expanded",!0),H&&H()}var F=G.find("> .active"),I=H&&E.support.transition&&(F.length&&F.hasClass("fade")||!!G.find("> .fade").length);F.length&&I?F.one("bsTransitionEnd",K).emulateTransitionEnd(D.TRANSITION_DURATION):K(),F.removeClass("in")};var A=E.fn.tab;E.fn.tab=C,E.fn.tab.Constructor=D,E.fn.tab.noConflict=function(){return E.fn.tab=A,this};var B=function(F){F.preventDefault(),C.call(E(this),"show")};E(document).on("click.bs.tab.data-api",'[data-toggle="tab"]',B).on("click.bs.tab.data-api",'[data-toggle="pill"]',B)}(jQuery),+function(D){function B(E){return this.each(function(){var F=D(this),G=F.data("bs.affix"),H="object"==typeof E&&E;G||F.data("bs.affix",G=new C(this,H)),"string"==typeof E&&G[E]()})}var C=function(F,E){this.options=D.extend({},C.DEFAULTS,E),this.$target=D(this.options.target).on("scroll.bs.affix.data-api",D.proxy(this.checkPosition,this)).on("click.bs.affix.data-api",D.proxy(this.checkPositionWithEventLoop,this)),this.$element=D(F),this.affixed=null,this.unpin=null,this.pinnedOffset=null,this.checkPosition()};C.VERSION="3.3.7",C.RESET="affix affix-top affix-bottom",C.DEFAULTS={offset:0,target:window},C.prototype.getState=function(K,I,J,N){var E=this.$target.scrollTop(),L=this.$element.offset(),M=this.$target.height();if(null!=J&&"top"==this.affixed){return E<J&&"top"}if("bottom"==this.affixed){return null!=J?!(E+this.unpin<=L.top)&&"bottom":!(E+M<=K-N)&&"bottom"}var G=null==this.affixed,H=G?E:L.top,F=G?M:I;return null!=J&&E<=J?"top":null!=N&&H+F>=K-N&&"bottom"},C.prototype.getPinnedOffset=function(){if(this.pinnedOffset){return this.pinnedOffset}this.$element.removeClass(C.RESET).addClass("affix");var F=this.$target.scrollTop(),E=this.$element.offset();return this.pinnedOffset=E.top-F},C.prototype.checkPositionWithEventLoop=function(){setTimeout(D.proxy(this.checkPosition,this),1)},C.prototype.checkPosition=function(){if(this.$element.is(":visible")){var I=this.$element.height(),L=this.options.offset,E=L.top,J=L.bottom,K=Math.max(D(document).height(),D(document.body).height());"object"!=typeof L&&(J=E=L),"function"==typeof E&&(E=L.top(this.$element)),"function"==typeof J&&(J=L.bottom(this.$element));var G=this.getState(K,I,E,J);if(this.affixed!=G){null!=this.unpin&&this.$element.css("top","");var H="affix"+(G?"-"+G:""),F=D.Event(H+".bs.affix");if(this.$element.trigger(F),F.isDefaultPrevented()){return}this.affixed=G,this.unpin="bottom"==G?this.getPinnedOffset():null,this.$element.removeClass(C.RESET).addClass(H).trigger(H.replace("affix","affixed")+".bs.affix")}"bottom"==G&&this.$element.offset({top:K-I-J})}};var A=D.fn.affix;D.fn.affix=B,D.fn.affix.Constructor=C,D.fn.affix.noConflict=function(){return D.fn.affix=A,this},D(window).on("load",function(){D('[data-spy="affix"]').each(function(){var F=D(this),E=F.data();E.offset=E.offset||{},null!=E.offsetBottom&&(E.offset.bottom=E.offsetBottom),null!=E.offsetTop&&(E.offset.top=E.offsetTop),B.call(F,E)})})}(jQuery);