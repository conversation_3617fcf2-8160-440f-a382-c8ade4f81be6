(function(A){if(typeof define==="function"&&define.amd){define(["jquery"],A)}else{if(typeof exports==="object"){A(require("jquery"))}else{A(jQuery)}}}(function(G,E){if(!("indexOf" in Array.prototype)){Array.prototype.indexOf=function(L,K){if(K===E){K=0}if(K<0){K+=this.length}if(K<0){K=0}for(var J=this.length;K<J;K++){if(K in this&&this[K]===L){return K}}return -1}}function A(J){var M=G(J);var L=M.add(M.parents());var K=false;L.each(function(){if(G(this).css("position")==="fixed"){K=true;return false}});return K}function B(){return new Date(Date.UTC.apply(Date,arguments))}function I(){var J=new Date();return B(J.getUTCFullYear(),<PERSON>.getUT<PERSON><PERSON>(),<PERSON>.getUT<PERSON>(),<PERSON><PERSON>getUT<PERSON>(),<PERSON><PERSON>get<PERSON>(),<PERSON>.getUTCSecond<PERSON>(),0)}var C=function(K,N){var J=this;this.element=G(K);this.container=N.container||"body";this.language=N.language||this.element.data("date-language")||"en";this.language=this.language in F?this.language:this.language.split("-")[0];this.language=this.language in F?this.language:"en";this.isRTL=F[this.language].rtl||false;this.formatType=N.formatType||this.element.data("format-type")||"standard";this.format=H.parseFormat(N.format||this.element.data("date-format")||F[this.language].format||H.getDefaultFormat(this.formatType,"input"),this.formatType);this.isInline=false;this.isVisible=false;this.isInput=this.element.is("input");this.fontAwesome=N.fontAwesome||this.element.data("font-awesome")||false;this.bootcssVer=N.bootcssVer||(this.isInput?(this.element.is(".form-control")?3:2):(this.bootcssVer=this.element.is(".input-group")?3:2));this.component=this.element.is(".date")?(this.bootcssVer==3?this.element.find(".input-group-addon .glyphicon-th, .input-group-addon .glyphicon-time, .input-group-addon .glyphicon-remove, .input-group-addon .glyphicon-calendar, .input-group-addon .fa-calendar, .input-group-addon .fa-clock-o").parent():this.element.find(".add-on .icon-th, .add-on .icon-time, .add-on .icon-calendar, .add-on .fa-calendar, .add-on .fa-clock-o").parent()):false;this.componentReset=this.element.is(".date")?(this.bootcssVer==3?this.element.find(".input-group-addon .glyphicon-remove, .input-group-addon .fa-times").parent():this.element.find(".add-on .icon-remove, .add-on .fa-times").parent()):false;this.hasInput=this.component&&this.element.find("input").length;if(this.component&&this.component.length===0){this.component=false}this.linkField=N.linkField||this.element.data("link-field")||false;this.linkFormat=H.parseFormat(N.linkFormat||this.element.data("link-format")||H.getDefaultFormat(this.formatType,"link"),this.formatType);this.minuteStep=N.minuteStep||this.element.data("minute-step")||5;this.pickerPosition=N.pickerPosition||this.element.data("picker-position")||"bottom-right";this.showMeridian=N.showMeridian||this.element.data("show-meridian")||false;this.initialDate=N.initialDate||new Date();this.zIndex=N.zIndex||this.element.data("z-index")||E;this.title=typeof N.title==="undefined"?false:N.title;this.defaultTimeZone=(new Date).toString().split("(")[1].slice(0,-1);this.timezone=N.timezone||this.defaultTimeZone;this.icons={leftArrow:this.fontAwesome?"fa-arrow-left":(this.bootcssVer===3?"glyphicon-arrow-left":"icon-arrow-left"),rightArrow:this.fontAwesome?"fa-arrow-right":(this.bootcssVer===3?"glyphicon-arrow-right":"icon-arrow-right")};this.icontype=this.fontAwesome?"fa":"glyphicon";this._attachEvents();this.clickedOutside=function(O){if(G(O.target).closest(".datetimepicker").length===0){J.hide()}};this.formatViewType="datetime";if("formatViewType" in N){this.formatViewType=N.formatViewType}else{if("formatViewType" in this.element.data()){this.formatViewType=this.element.data("formatViewType")}}this.minView=0;if("minView" in N){this.minView=N.minView}else{if("minView" in this.element.data()){this.minView=this.element.data("min-view")}}this.minView=H.convertViewMode(this.minView);this.maxView=H.modes.length-1;if("maxView" in N){this.maxView=N.maxView}else{if("maxView" in this.element.data()){this.maxView=this.element.data("max-view")}}this.maxView=H.convertViewMode(this.maxView);this.wheelViewModeNavigation=false;if("wheelViewModeNavigation" in N){this.wheelViewModeNavigation=N.wheelViewModeNavigation}else{if("wheelViewModeNavigation" in this.element.data()){this.wheelViewModeNavigation=this.element.data("view-mode-wheel-navigation")}}this.wheelViewModeNavigationInverseDirection=false;if("wheelViewModeNavigationInverseDirection" in N){this.wheelViewModeNavigationInverseDirection=N.wheelViewModeNavigationInverseDirection}else{if("wheelViewModeNavigationInverseDirection" in this.element.data()){this.wheelViewModeNavigationInverseDirection=this.element.data("view-mode-wheel-navigation-inverse-dir")}}this.wheelViewModeNavigationDelay=100;if("wheelViewModeNavigationDelay" in N){this.wheelViewModeNavigationDelay=N.wheelViewModeNavigationDelay}else{if("wheelViewModeNavigationDelay" in this.element.data()){this.wheelViewModeNavigationDelay=this.element.data("view-mode-wheel-navigation-delay")}}this.startViewMode=2;if("startView" in N){this.startViewMode=N.startView}else{if("startView" in this.element.data()){this.startViewMode=this.element.data("start-view")}}this.startViewMode=H.convertViewMode(this.startViewMode);this.viewMode=this.startViewMode;this.viewSelect=this.minView;if("viewSelect" in N){this.viewSelect=N.viewSelect}else{if("viewSelect" in this.element.data()){this.viewSelect=this.element.data("view-select")}}this.viewSelect=H.convertViewMode(this.viewSelect);this.forceParse=true;if("forceParse" in N){this.forceParse=N.forceParse}else{if("dateForceParse" in this.element.data()){this.forceParse=this.element.data("date-force-parse")}}var L=this.bootcssVer===3?H.templateV3:H.template;while(L.indexOf("{iconType}")!==-1){L=L.replace("{iconType}",this.icontype)}while(L.indexOf("{leftArrow}")!==-1){L=L.replace("{leftArrow}",this.icons.leftArrow)}while(L.indexOf("{rightArrow}")!==-1){L=L.replace("{rightArrow}",this.icons.rightArrow)}this.picker=G(L).appendTo(this.isInline?this.element:this.container).on({click:G.proxy(this.click,this),mousedown:G.proxy(this.mousedown,this)});if(this.wheelViewModeNavigation){if(G.fn.mousewheel){this.picker.on({mousewheel:G.proxy(this.mousewheel,this)})}else{console.log("Mouse Wheel event is not supported. Please include the jQuery Mouse Wheel plugin before enabling this option")}}if(this.isInline){this.picker.addClass("datetimepicker-inline")}else{this.picker.addClass("datetimepicker-dropdown-"+this.pickerPosition+" dropdown-menu")}if(this.isRTL){this.picker.addClass("datetimepicker-rtl");var M=this.bootcssVer===3?".prev span, .next span":".prev i, .next i";this.picker.find(M).toggleClass(this.icons.leftArrow+" "+this.icons.rightArrow)}G(document).on("mousedown",this.clickedOutside);this.autoclose=false;if("autoclose" in N){this.autoclose=N.autoclose}else{if("dateAutoclose" in this.element.data()){this.autoclose=this.element.data("date-autoclose")}}this.keyboardNavigation=true;if("keyboardNavigation" in N){this.keyboardNavigation=N.keyboardNavigation}else{if("dateKeyboardNavigation" in this.element.data()){this.keyboardNavigation=this.element.data("date-keyboard-navigation")}}this.todayBtn=(N.todayBtn||this.element.data("date-today-btn")||false);this.clearBtn=(N.clearBtn||this.element.data("date-clear-btn")||false);this.todayHighlight=(N.todayHighlight||this.element.data("date-today-highlight")||false);this.weekStart=((N.weekStart||this.element.data("date-weekstart")||F[this.language].weekStart||0)%7);this.weekEnd=((this.weekStart+6)%7);this.startDate=-Infinity;this.endDate=Infinity;this.datesDisabled=[];this.daysOfWeekDisabled=[];this.setStartDate(N.startDate||this.element.data("date-startdate"));this.setEndDate(N.endDate||this.element.data("date-enddate"));this.setDatesDisabled(N.datesDisabled||this.element.data("date-dates-disabled"));this.setDaysOfWeekDisabled(N.daysOfWeekDisabled||this.element.data("date-days-of-week-disabled"));this.setMinutesDisabled(N.minutesDisabled||this.element.data("date-minute-disabled"));this.setHoursDisabled(N.hoursDisabled||this.element.data("date-hour-disabled"));this.fillDow();this.fillMonths();this.update();this.showMode();if(this.isInline){this.show()}};C.prototype={constructor:C,_events:[],_attachEvents:function(){this._detachEvents();if(this.isInput){this._events=[[this.element,{focus:G.proxy(this.show,this),keyup:G.proxy(this.update,this),keydown:G.proxy(this.keydown,this)}]]}else{if(this.component&&this.hasInput){this._events=[[this.element.find("input"),{focus:G.proxy(this.show,this),keyup:G.proxy(this.update,this),keydown:G.proxy(this.keydown,this)}],[this.component,{click:G.proxy(this.show,this)}]];if(this.componentReset){this._events.push([this.componentReset,{click:G.proxy(this.reset,this)}])}}else{if(this.element.is("div")){this.isInline=true}else{this._events=[[this.element,{click:G.proxy(this.show,this)}]]}}}for(var K=0,L,J;K<this._events.length;K++){L=this._events[K][0];J=this._events[K][1];L.on(J)}},_detachEvents:function(){for(var K=0,L,J;K<this._events.length;K++){L=this._events[K][0];J=this._events[K][1];L.off(J)}this._events=[]},show:function(J){this.picker.show();this.height=this.component?this.component.outerHeight():this.element.outerHeight();if(this.forceParse){this.update()}this.place();G(window).on("resize",G.proxy(this.place,this));if(J){J.stopPropagation();J.preventDefault()}this.isVisible=true;this.element.trigger({type:"show",date:this.date})},hide:function(J){if(!this.isVisible){return}if(this.isInline){return}this.picker.hide();G(window).off("resize",this.place);this.viewMode=this.startViewMode;this.showMode();if(!this.isInput){G(document).off("mousedown",this.hide)}if(this.forceParse&&(this.isInput&&this.element.val()||this.hasInput&&this.element.find("input").val())){this.setValue()}this.isVisible=false;this.element.trigger({type:"hide",date:this.date})},remove:function(){this._detachEvents();G(document).off("mousedown",this.clickedOutside);this.picker.remove();delete this.picker;delete this.element.data().datetimepicker},getDate:function(){var J=this.getUTCDate();return new Date(J.getTime()+(J.getTimezoneOffset()*60000))},getUTCDate:function(){return this.date},getInitialDate:function(){return this.initialDate},setInitialDate:function(J){this.initialDate=J},setDate:function(J){this.setUTCDate(new Date(J.getTime()-(J.getTimezoneOffset()*60000)))},setUTCDate:function(J){if(J>=this.startDate&&J<=this.endDate){this.date=J;this.setValue();this.viewDate=this.date;this.fill()}else{this.element.trigger({type:"outOfRange",date:J,startDate:this.startDate,endDate:this.endDate})}},setFormat:function(K){this.format=H.parseFormat(K,this.formatType);var J;if(this.isInput){J=this.element}else{if(this.component){J=this.element.find("input")}}if(J&&J.val()){this.setValue()}},setValue:function(){var J=this.getFormattedDate();if(!this.isInput){if(this.component){this.element.find("input").val(J)}this.element.data("date",J)}else{this.element.val(J)}if(this.linkField){G("#"+this.linkField).val(this.getFormattedDate(this.linkFormat))}},getFormattedDate:function(J){if(J==E){J=this.format}return H.formatDate(this.date,J,this.language,this.formatType,this.timezone)},setStartDate:function(J){this.startDate=J||-Infinity;if(this.startDate!==-Infinity){this.startDate=H.parseDate(this.startDate,this.format,this.language,this.formatType,this.timezone)}this.update();this.updateNavArrows()},setEndDate:function(J){this.endDate=J||Infinity;if(this.endDate!==Infinity){this.endDate=H.parseDate(this.endDate,this.format,this.language,this.formatType,this.timezone)}this.update();this.updateNavArrows()},setDatesDisabled:function(J){this.datesDisabled=J||[];if(!G.isArray(this.datesDisabled)){this.datesDisabled=this.datesDisabled.split(/,\s*/)}this.datesDisabled=G.map(this.datesDisabled,function(K){return H.parseDate(K,this.format,this.language,this.formatType,this.timezone).toDateString()});this.update();this.updateNavArrows()},setTitle:function(J,K){return this.picker.find(J).find("th:eq(1)").text(this.title===false?K:this.title)},setDaysOfWeekDisabled:function(J){this.daysOfWeekDisabled=J||[];if(!G.isArray(this.daysOfWeekDisabled)){this.daysOfWeekDisabled=this.daysOfWeekDisabled.split(/,\s*/)}this.daysOfWeekDisabled=G.map(this.daysOfWeekDisabled,function(K){return parseInt(K,10)});this.update();this.updateNavArrows()},setMinutesDisabled:function(J){this.minutesDisabled=J||[];if(!G.isArray(this.minutesDisabled)){this.minutesDisabled=this.minutesDisabled.split(/,\s*/)}this.minutesDisabled=G.map(this.minutesDisabled,function(K){return parseInt(K,10)});this.update();this.updateNavArrows()},setHoursDisabled:function(J){this.hoursDisabled=J||[];if(!G.isArray(this.hoursDisabled)){this.hoursDisabled=this.hoursDisabled.split(/,\s*/)}this.hoursDisabled=G.map(this.hoursDisabled,function(K){return parseInt(K,10)});this.update();this.updateNavArrows()},place:function(){if(this.isInline){return}if(!this.zIndex){var O=0;G("div").each(function(){var P=parseInt(G(this).css("zIndex"),10);if(P>O){O=P}});this.zIndex=O+10}var K,J,M,L;if(this.container instanceof G){L=this.container.offset()}else{L=G(this.container).offset()}if(this.component){K=this.component.offset();M=K.left;if(this.pickerPosition=="bottom-left"||this.pickerPosition=="top-left"){M+=this.component.outerWidth()-this.picker.outerWidth()}}else{K=this.element.offset();M=K.left;if(this.pickerPosition=="bottom-left"||this.pickerPosition=="top-left"){M+=this.element.outerWidth()-this.picker.outerWidth()}}var N=document.body.clientWidth||window.innerWidth;if(M+220>N){M=N-220}if(this.pickerPosition=="top-left"||this.pickerPosition=="top-right"){J=K.top-this.picker.outerHeight()}else{J=K.top+this.height}J=J-L.top;M=M-L.left;this.picker.css({top:J,left:M,zIndex:this.zIndex})},update:function(){var J,K=false;if(arguments&&arguments.length&&(typeof arguments[0]==="string"||arguments[0] instanceof Date)){J=arguments[0];K=true}else{J=(this.isInput?this.element.val():this.element.find("input").val())||this.element.data("date")||this.initialDate;if(typeof J=="string"||J instanceof String){J=J.replace(/^\s+|\s+$/g,"")}}if(!J){J=new Date();K=false}this.date=H.parseDate(J,this.format,this.language,this.formatType,this.timezone);if(K){this.setValue()}if(this.date<this.startDate){this.viewDate=new Date(this.startDate)}else{if(this.date>this.endDate){this.viewDate=new Date(this.endDate)}else{this.viewDate=new Date(this.date)}}this.fill()},fillDow:function(){var J=this.weekStart,K="<tr>";while(J<this.weekStart+7){K+='<th class="dow">'+F[this.language].daysMin[(J++)%7]+"</th>"}K+="</tr>";this.picker.find(".datetimepicker-days thead").append(K)},fillMonths:function(){var K="",J=0;while(J<12){K+='<span class="month">'+F[this.language].monthsShort[J++]+"</span>"}this.picker.find(".datetimepicker-months td").html(K)},fill:function(){if(this.date==null||this.viewDate==null){return}var c=new Date(this.viewDate),T=c.getUTCFullYear(),d=c.getUTCMonth(),W=c.getUTCDate(),Ab=c.getUTCHours(),Af=c.getUTCMinutes(),Ad=this.startDate!==-Infinity?this.startDate.getUTCFullYear():-Infinity,Ac=this.startDate!==-Infinity?this.startDate.getUTCMonth():-Infinity,P=this.endDate!==Infinity?this.endDate.getUTCFullYear():Infinity,h=this.endDate!==Infinity?this.endDate.getUTCMonth()+1:Infinity,M=(new B(this.date.getUTCFullYear(),this.date.getUTCMonth(),this.date.getUTCDate())).valueOf(),Aa=new Date();this.setTitle(".datetimepicker-days",F[this.language].months[d]+" "+T);if(this.formatViewType=="time"){var V=this.getFormattedDate();this.setTitle(".datetimepicker-hours",V);this.setTitle(".datetimepicker-minutes",V)}else{this.setTitle(".datetimepicker-hours",W+" "+F[this.language].months[d]+" "+T);this.setTitle(".datetimepicker-minutes",W+" "+F[this.language].months[d]+" "+T)}this.picker.find("tfoot th.today").text(F[this.language].today||F.en.today).toggle(this.todayBtn!==false);this.picker.find("tfoot th.clear").text(F[this.language].clear||F.en.clear).toggle(this.clearBtn!==false);this.updateNavArrows();this.fillMonths();var b=B(T,d-1,28,0,0,0,0),g=H.getDaysInMonth(b.getUTCFullYear(),b.getUTCMonth());b.setUTCDate(g);b.setUTCDate(g-(b.getUTCDay()-this.weekStart+7)%7);var U=new Date(b);U.setUTCDate(U.getUTCDate()+42);U=U.valueOf();var N=[];var Q;while(b.valueOf()<U){if(b.getUTCDay()==this.weekStart){N.push("<tr>")}Q="";if(b.getUTCFullYear()<T||(b.getUTCFullYear()==T&&b.getUTCMonth()<d)){Q+=" old"}else{if(b.getUTCFullYear()>T||(b.getUTCFullYear()==T&&b.getUTCMonth()>d)){Q+=" new"}}if(this.todayHighlight&&b.getUTCFullYear()==Aa.getFullYear()&&b.getUTCMonth()==Aa.getMonth()&&b.getUTCDate()==Aa.getDate()){Q+=" today"}if(b.valueOf()==M){Q+=" active"}if((b.valueOf()+86400000)<=this.startDate||b.valueOf()>this.endDate||G.inArray(b.getUTCDay(),this.daysOfWeekDisabled)!==-1||G.inArray(b.toDateString(),this.datesDisabled)!==-1){Q+=" disabled"}N.push('<td class="day'+Q+'">'+b.getUTCDate()+"</td>");if(b.getUTCDay()==this.weekEnd){N.push("</tr>")}b.setUTCDate(b.getUTCDate()+1)}this.picker.find(".datetimepicker-days tbody").empty().append(N.join(""));N=[];var R="",i="",S="";var Y=this.hoursDisabled||[];for(var f=0;f<24;f++){if(Y.indexOf(f)!==-1){continue}var Ae=B(T,d,W,f);Q="";if((Ae.valueOf()+3600000)<=this.startDate||Ae.valueOf()>this.endDate){Q+=" disabled"}else{if(Ab==f){Q+=" active"}}if(this.showMeridian&&F[this.language].meridiem.length==2){i=(f<12?F[this.language].meridiem[0]:F[this.language].meridiem[1]);if(i!=S){if(S!=""){N.push("</fieldset>")}N.push('<fieldset class="hour"><legend>'+i.toUpperCase()+"</legend>")}S=i;R=(f%12?f%12:12);N.push('<span class="hour'+Q+" hour_"+(f<12?"am":"pm")+'">'+R+"</span>");if(f==23){N.push("</fieldset>")}}else{R=f+":00";N.push('<span class="hour'+Q+'">'+R+"</span>")}}this.picker.find(".datetimepicker-hours td").html(N.join(""));N=[];R="",i="",S="";var Z=this.minutesDisabled||[];for(var f=0;f<60;f+=this.minuteStep){if(Z.indexOf(f)!==-1){continue}var Ae=B(T,d,W,Ab,f,0);Q="";if(Ae.valueOf()<this.startDate||Ae.valueOf()>this.endDate){Q+=" disabled"}else{if(Math.floor(Af/this.minuteStep)==Math.floor(f/this.minuteStep)){Q+=" active"}}if(this.showMeridian&&F[this.language].meridiem.length==2){i=(Ab<12?F[this.language].meridiem[0]:F[this.language].meridiem[1]);if(i!=S){if(S!=""){N.push("</fieldset>")}N.push('<fieldset class="minute"><legend>'+i.toUpperCase()+"</legend>")}S=i;R=(Ab%12?Ab%12:12);N.push('<span class="minute'+Q+'">'+R+":"+(f<10?"0"+f:f)+"</span>");if(f==59){N.push("</fieldset>")}}else{R=f+":00";N.push('<span class="minute'+Q+'">'+Ab+":"+(f<10?"0"+f:f)+"</span>")}}this.picker.find(".datetimepicker-minutes td").html(N.join(""));var e=this.date.getUTCFullYear();var O=this.setTitle(".datetimepicker-months",T).end().find("span").removeClass("active");if(e==T){var X=O.length-12;O.eq(this.date.getUTCMonth()+X).addClass("active")}if(T<Ad||T>P){O.addClass("disabled")}if(T==Ad){O.slice(0,Ac).addClass("disabled")}if(T==P){O.slice(h).addClass("disabled")}N="";T=parseInt(T/10,10)*10;var a=this.setTitle(".datetimepicker-years",T+"-"+(T+9)).end().find("td");T-=1;for(var f=-1;f<11;f++){N+='<span class="year'+(f==-1||f==10?" old":"")+(e==T?" active":"")+(T<Ad||T>P?" disabled":"")+'">'+T+"</span>";T+=1}a.html(N);this.place()},updateNavArrows:function(){var J=new Date(this.viewDate),K=J.getUTCFullYear(),L=J.getUTCMonth(),N=J.getUTCDate(),M=J.getUTCHours();switch(this.viewMode){case 0:if(this.startDate!==-Infinity&&K<=this.startDate.getUTCFullYear()&&L<=this.startDate.getUTCMonth()&&N<=this.startDate.getUTCDate()&&M<=this.startDate.getUTCHours()){this.picker.find(".prev").css({visibility:"hidden"})}else{this.picker.find(".prev").css({visibility:"visible"})}if(this.endDate!==Infinity&&K>=this.endDate.getUTCFullYear()&&L>=this.endDate.getUTCMonth()&&N>=this.endDate.getUTCDate()&&M>=this.endDate.getUTCHours()){this.picker.find(".next").css({visibility:"hidden"})}else{this.picker.find(".next").css({visibility:"visible"})}break;case 1:if(this.startDate!==-Infinity&&K<=this.startDate.getUTCFullYear()&&L<=this.startDate.getUTCMonth()&&N<=this.startDate.getUTCDate()){this.picker.find(".prev").css({visibility:"hidden"})}else{this.picker.find(".prev").css({visibility:"visible"})}if(this.endDate!==Infinity&&K>=this.endDate.getUTCFullYear()&&L>=this.endDate.getUTCMonth()&&N>=this.endDate.getUTCDate()){this.picker.find(".next").css({visibility:"hidden"})}else{this.picker.find(".next").css({visibility:"visible"})}break;case 2:if(this.startDate!==-Infinity&&K<=this.startDate.getUTCFullYear()&&L<=this.startDate.getUTCMonth()){this.picker.find(".prev").css({visibility:"hidden"})}else{this.picker.find(".prev").css({visibility:"visible"})}if(this.endDate!==Infinity&&K>=this.endDate.getUTCFullYear()&&L>=this.endDate.getUTCMonth()){this.picker.find(".next").css({visibility:"hidden"})}else{this.picker.find(".next").css({visibility:"visible"})}break;case 3:case 4:if(this.startDate!==-Infinity&&K<=this.startDate.getUTCFullYear()){this.picker.find(".prev").css({visibility:"hidden"})}else{this.picker.find(".prev").css({visibility:"visible"})}if(this.endDate!==Infinity&&K>=this.endDate.getUTCFullYear()){this.picker.find(".next").css({visibility:"hidden"})}else{this.picker.find(".next").css({visibility:"visible"})}break}},mousewheel:function(M){M.preventDefault();M.stopPropagation();if(this.wheelPause){return}this.wheelPause=true;var L=M.originalEvent;var K=L.wheelDelta;var J=K>0?1:(K===0)?0:-1;if(this.wheelViewModeNavigationInverseDirection){J=-J}this.showMode(J);setTimeout(G.proxy(function(){this.wheelPause=false},this),this.wheelViewModeNavigationDelay)},click:function(Q){Q.stopPropagation();Q.preventDefault();var R=G(Q.target).closest("span, td, th, legend");if(R.is("."+this.icontype)){R=G(R).parent().closest("span, td, th, legend")}if(R.length==1){if(R.is(".disabled")){this.element.trigger({type:"outOfRange",date:this.viewDate,startDate:this.startDate,endDate:this.endDate});return}switch(R[0].nodeName.toLowerCase()){case"th":switch(R[0].className){case"switch":this.showMode(1);break;case"prev":case"next":var O=H.modes[this.viewMode].navStep*(R[0].className=="prev"?-1:1);switch(this.viewMode){case 0:this.viewDate=this.moveHour(this.viewDate,O);break;case 1:this.viewDate=this.moveDate(this.viewDate,O);break;case 2:this.viewDate=this.moveMonth(this.viewDate,O);break;case 3:case 4:this.viewDate=this.moveYear(this.viewDate,O);break}this.fill();this.element.trigger({type:R[0].className+":"+this.convertViewModeText(this.viewMode),date:this.viewDate,startDate:this.startDate,endDate:this.endDate});break;case"clear":this.reset();if(this.autoclose){this.hide()}break;case"today":var P=new Date();P=B(P.getFullYear(),P.getMonth(),P.getDate(),P.getHours(),P.getMinutes(),P.getSeconds(),0);if(P<this.startDate){P=this.startDate}else{if(P>this.endDate){P=this.endDate}}this.viewMode=this.startViewMode;this.showMode(0);this._setDate(P);this.fill();if(this.autoclose){this.hide()}break}break;case"span":if(!R.is(".disabled")){var M=this.viewDate.getUTCFullYear(),L=this.viewDate.getUTCMonth(),J=this.viewDate.getUTCDate(),K=this.viewDate.getUTCHours(),S=this.viewDate.getUTCMinutes(),N=this.viewDate.getUTCSeconds();if(R.is(".month")){this.viewDate.setUTCDate(1);L=R.parent().find("span").index(R);J=this.viewDate.getUTCDate();this.viewDate.setUTCMonth(L);this.element.trigger({type:"changeMonth",date:this.viewDate});if(this.viewSelect>=3){this._setDate(B(M,L,J,K,S,N,0))}}else{if(R.is(".year")){this.viewDate.setUTCDate(1);M=parseInt(R.text(),10)||0;this.viewDate.setUTCFullYear(M);this.element.trigger({type:"changeYear",date:this.viewDate});if(this.viewSelect>=4){this._setDate(B(M,L,J,K,S,N,0))}}else{if(R.is(".hour")){K=parseInt(R.text(),10)||0;if(R.hasClass("hour_am")||R.hasClass("hour_pm")){if(K==12&&R.hasClass("hour_am")){K=0}else{if(K!=12&&R.hasClass("hour_pm")){K+=12}}}this.viewDate.setUTCHours(K);this.element.trigger({type:"changeHour",date:this.viewDate});if(this.viewSelect>=1){this._setDate(B(M,L,J,K,S,N,0))}}else{if(R.is(".minute")){S=parseInt(R.text().substr(R.text().indexOf(":")+1),10)||0;this.viewDate.setUTCMinutes(S);this.element.trigger({type:"changeMinute",date:this.viewDate});if(this.viewSelect>=0){this._setDate(B(M,L,J,K,S,N,0))}}}}}if(this.viewMode!=0){var T=this.viewMode;this.showMode(-1);this.fill();if(T==this.viewMode&&this.autoclose){this.hide()}}else{this.fill();if(this.autoclose){this.hide()}}}break;case"td":if(R.is(".day")&&!R.is(".disabled")){var J=parseInt(R.text(),10)||1;var M=this.viewDate.getUTCFullYear(),L=this.viewDate.getUTCMonth(),K=this.viewDate.getUTCHours(),S=this.viewDate.getUTCMinutes(),N=this.viewDate.getUTCSeconds();if(R.is(".old")){if(L===0){L=11;M-=1}else{L-=1}}else{if(R.is(".new")){if(L==11){L=0;M+=1}else{L+=1}}}this.viewDate.setUTCFullYear(M);this.viewDate.setUTCMonth(L,J);this.element.trigger({type:"changeDay",date:this.viewDate});if(this.viewSelect>=2){this._setDate(B(M,L,J,K,S,N,0))}}var T=this.viewMode;this.showMode(-1);this.fill();if(T==this.viewMode&&this.autoclose){this.hide()}break}}},_setDate:function(K,J){if(!J||J=="date"){this.date=K}if(!J||J=="view"){this.viewDate=K}this.fill();this.setValue();var L;if(this.isInput){L=this.element}else{if(this.component){L=this.element.find("input")}}if(L){L.change();if(this.autoclose&&(!J||J=="date")){}}this.element.trigger({type:"changeDate",date:this.getDate()});if(K==null){this.date=this.viewDate}},moveMinute:function(L,K){if(!K){return L}var J=new Date(L.valueOf());J.setUTCMinutes(J.getUTCMinutes()+(K*this.minuteStep));return J},moveHour:function(L,K){if(!K){return L}var J=new Date(L.valueOf());J.setUTCHours(J.getUTCHours()+K);return J},moveDate:function(L,K){if(!K){return L}var J=new Date(L.valueOf());J.setUTCDate(J.getUTCDate()+K);return J},moveMonth:function(M,N){if(!N){return M}var O=new Date(M.valueOf()),J=O.getUTCDate(),P=O.getUTCMonth(),R=Math.abs(N),L,K;N=N>0?1:-1;if(R==1){K=N==-1?function(){return O.getUTCMonth()==P}:function(){return O.getUTCMonth()!=L};L=P+N;O.setUTCMonth(L);if(L<0||L>11){L=(L+12)%12}}else{for(var Q=0;Q<R;Q++){O=this.moveMonth(O,N)}L=O.getUTCMonth();O.setUTCDate(J);K=function(){return L!=O.getUTCMonth()}}while(K()){O.setUTCDate(--J);O.setUTCMonth(L)}return O},moveYear:function(K,J){return this.moveMonth(K,J*12)},dateWithinRange:function(J){return J>=this.startDate&&J<=this.endDate},keydown:function(O){if(this.picker.is(":not(:visible)")){if(O.keyCode==27){this.show()}return}var K=false,N,L,P,J,M;switch(O.keyCode){case 27:this.hide();O.preventDefault();break;case 37:case 39:if(!this.keyboardNavigation){break}N=O.keyCode==37?-1:1;viewMode=this.viewMode;if(O.ctrlKey){viewMode+=2}else{if(O.shiftKey){viewMode+=1}}if(viewMode==4){J=this.moveYear(this.date,N);M=this.moveYear(this.viewDate,N)}else{if(viewMode==3){J=this.moveMonth(this.date,N);M=this.moveMonth(this.viewDate,N)}else{if(viewMode==2){J=this.moveDate(this.date,N);M=this.moveDate(this.viewDate,N)}else{if(viewMode==1){J=this.moveHour(this.date,N);M=this.moveHour(this.viewDate,N)}else{if(viewMode==0){J=this.moveMinute(this.date,N);M=this.moveMinute(this.viewDate,N)}}}}}if(this.dateWithinRange(J)){this.date=J;this.viewDate=M;this.setValue();this.update();O.preventDefault();K=true}break;case 38:case 40:if(!this.keyboardNavigation){break}N=O.keyCode==38?-1:1;viewMode=this.viewMode;if(O.ctrlKey){viewMode+=2}else{if(O.shiftKey){viewMode+=1}}if(viewMode==4){J=this.moveYear(this.date,N);M=this.moveYear(this.viewDate,N)}else{if(viewMode==3){J=this.moveMonth(this.date,N);M=this.moveMonth(this.viewDate,N)}else{if(viewMode==2){J=this.moveDate(this.date,N*7);M=this.moveDate(this.viewDate,N*7)}else{if(viewMode==1){if(this.showMeridian){J=this.moveHour(this.date,N*6);M=this.moveHour(this.viewDate,N*6)}else{J=this.moveHour(this.date,N*4);M=this.moveHour(this.viewDate,N*4)}}else{if(viewMode==0){J=this.moveMinute(this.date,N*4);M=this.moveMinute(this.viewDate,N*4)}}}}}if(this.dateWithinRange(J)){this.date=J;this.viewDate=M;this.setValue();this.update();O.preventDefault();K=true}break;case 13:if(this.viewMode!=0){var R=this.viewMode;this.showMode(-1);this.fill();if(R==this.viewMode&&this.autoclose){this.hide()}}else{this.fill();if(this.autoclose){this.hide()}}O.preventDefault();break;case 9:this.hide();break}if(K){var Q;if(this.isInput){Q=this.element}else{if(this.component){Q=this.element.find("input")}}if(Q){Q.change()}this.element.trigger({type:"changeDate",date:this.getDate()})}},showMode:function(J){if(J){var K=Math.max(0,Math.min(H.modes.length-1,this.viewMode+J));if(K>=this.minView&&K<=this.maxView){this.element.trigger({type:"changeMode",date:this.viewDate,oldViewMode:this.viewMode,newViewMode:K});this.viewMode=K}}this.picker.find(">div").hide().filter(".datetimepicker-"+H.modes[this.viewMode].clsName).css("display","block");this.updateNavArrows()},reset:function(J){this._setDate(null,"date")},convertViewModeText:function(J){switch(J){case 4:return"decade";case 3:return"year";case 2:return"month";case 1:return"day";case 0:return"hour"}}};var D=G.fn.datetimepicker;G.fn.datetimepicker=function(J){var K=Array.apply(null,arguments);K.shift();var L;this.each(function(){var N=G(this),M=N.data("datetimepicker"),O=typeof J=="object"&&J;if(!M){N.data("datetimepicker",(M=new C(this,G.extend({},G.fn.datetimepicker.defaults,O))))}if(typeof J=="string"&&typeof M[J]=="function"){L=M[J].apply(M,K);if(L!==E){return false}}});if(L!==E){return L}else{return this}};G.fn.datetimepicker.defaults={};G.fn.datetimepicker.Constructor=C;var F=G.fn.datetimepicker.dates={en:{days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"],daysShort:["Sun","Mon","Tue","Wed","Thu","Fri","Sat","Sun"],daysMin:["Su","Mo","Tu","We","Th","Fr","Sa","Su"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],monthsShort:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],meridiem:["am","pm"],suffix:["st","nd","rd","th"],today:"Today",clear:"Clear"}};var H={modes:[{clsName:"minutes",navFnc:"Hours",navStep:1},{clsName:"hours",navFnc:"Date",navStep:1},{clsName:"days",navFnc:"Month",navStep:1},{clsName:"months",navFnc:"FullYear",navStep:1},{clsName:"years",navFnc:"FullYear",navStep:10}],isLeapYear:function(J){return(((J%4===0)&&(J%100!==0))||(J%400===0))},getDaysInMonth:function(J,K){return[31,(H.isLeapYear(J)?29:28),31,30,31,30,31,31,30,31,30,31][K]},getDefaultFormat:function(J,K){if(J=="standard"){if(K=="input"){return"yyyy-mm-dd hh:ii"}else{return"yyyy-mm-dd hh:ii:ss"}}else{if(J=="php"){if(K=="input"){return"Y-m-d H:i"}else{return"Y-m-d H:i:s"}}else{throw new Error("Invalid format type.")}}},validParts:function(J){if(J=="standard"){return/t|hh?|HH?|p|P|z|Z|ii?|ss?|dd?|DD?|mm?|MM?|yy(?:yy)?/g}else{if(J=="php"){return/[dDjlNwzFmMnStyYaABgGhHis]/g}else{throw new Error("Invalid format type.")}}},nonpunctuation:/[^ -\/:-@\[-`{-~\t\n\rTZ]+/g,parseFormat:function(K,M){var L=K.replace(this.validParts(M),"\0").split("\0"),J=K.match(this.validParts(M));if(!L||!L.length||!J||J.length==0){throw new Error("Invalid date format.")}return{separators:L,parts:J}},parseDate:function(X,a,M,Q,J){if(X instanceof Date){var P=new Date(X.valueOf()-X.getTimezoneOffset()*60000);P.setMilliseconds(0);return P}if(/^\d{4}\-\d{1,2}\-\d{1,2}$/.test(X)){a=this.parseFormat("yyyy-mm-dd",Q)}if(/^\d{4}\-\d{1,2}\-\d{1,2}[T ]\d{1,2}\:\d{1,2}$/.test(X)){a=this.parseFormat("yyyy-mm-dd hh:ii",Q)}if(/^\d{4}\-\d{1,2}\-\d{1,2}[T ]\d{1,2}\:\d{1,2}\:\d{1,2}[Z]{0,1}$/.test(X)){a=this.parseFormat("yyyy-mm-dd hh:ii:ss",Q)}if(/^[-+]\d+[dmwy]([\s,]+[-+]\d+[dmwy])*$/.test(X)){var U=/([-+]\d+)([dmwy])/,L=X.match(/([-+]\d+)([dmwy])/g),O,K;X=new Date();for(var Z=0;Z<L.length;Z++){O=U.exec(L[Z]);K=parseInt(O[1]);switch(O[2]){case"d":X.setUTCDate(X.getUTCDate()+K);break;case"m":X=C.prototype.moveMonth.call(C.prototype,X,K);break;case"w":X.setUTCDate(X.getUTCDate()+K*7);break;case"y":X=C.prototype.moveYear.call(C.prototype,X,K);break}}return B(X.getUTCFullYear(),X.getUTCMonth(),X.getUTCDate(),X.getUTCHours(),X.getUTCMinutes(),X.getUTCSeconds(),0)}var L=X&&X.toString().match(this.nonpunctuation)||[],X=new Date(0,0,0,0,0,0,0),V={},Y=["hh","h","ii","i","ss","s","yyyy","yy","M","MM","m","mm","D","DD","d","dd","H","HH","p","P","z","Z"],T={hh:function(c,b){return c.setUTCHours(b)},h:function(c,b){return c.setUTCHours(b)},HH:function(c,b){return c.setUTCHours(b==12?0:b)},H:function(c,b){return c.setUTCHours(b==12?0:b)},ii:function(c,b){return c.setUTCMinutes(b)},i:function(c,b){return c.setUTCMinutes(b)},ss:function(c,b){return c.setUTCSeconds(b)},s:function(c,b){return c.setUTCSeconds(b)},yyyy:function(c,b){return c.setUTCFullYear(b)},yy:function(c,b){return c.setUTCFullYear(2000+b)},m:function(c,b){b-=1;while(b<0){b+=12}b%=12;c.setUTCMonth(b);while(c.getUTCMonth()!=b){if(isNaN(c.getUTCMonth())){return c}else{c.setUTCDate(c.getUTCDate()-1)}}return c},d:function(c,b){return c.setUTCDate(b)},p:function(c,b){return c.setUTCHours(b==1?c.getUTCHours()+12:c.getUTCHours())},z:function(){return J}},W,R,O;T.M=T.MM=T.mm=T.m;T.dd=T.d;T.P=T.p;T.Z=T.z;X=B(X.getFullYear(),X.getMonth(),X.getDate(),X.getHours(),X.getMinutes(),X.getSeconds());if(L.length==a.parts.length){for(var Z=0,N=a.parts.length;Z<N;Z++){W=parseInt(L[Z],10);O=a.parts[Z];if(isNaN(W)){switch(O){case"MM":R=G(F[M].months).filter(function(){var b=this.slice(0,L[Z].length),c=L[Z].slice(0,b.length);return b==c});W=G.inArray(R[0],F[M].months)+1;break;case"M":R=G(F[M].monthsShort).filter(function(){var b=this.slice(0,L[Z].length),c=L[Z].slice(0,b.length);return b.toLowerCase()==c.toLowerCase()});W=G.inArray(R[0],F[M].monthsShort)+1;break;case"p":case"P":W=G.inArray(L[Z].toLowerCase(),F[M].meridiem);break;case"z":case"Z":J;break}}V[O]=W}for(var Z=0,S;Z<Y.length;Z++){S=Y[Z];if(S in V&&!isNaN(V[S])){T[S](X,V[S])}}}return X},formatDate:function(Q,L,R,K,P){if(Q==null){return""}var N;if(K=="standard"){N={t:Q.getTime(),yy:Q.getUTCFullYear().toString().substring(2),yyyy:Q.getUTCFullYear(),m:Q.getUTCMonth()+1,M:F[R].monthsShort[Q.getUTCMonth()],MM:F[R].months[Q.getUTCMonth()],d:Q.getUTCDate(),D:F[R].daysShort[Q.getUTCDay()],DD:F[R].days[Q.getUTCDay()],p:(F[R].meridiem.length==2?F[R].meridiem[Q.getUTCHours()<12?0:1]:""),h:Q.getUTCHours(),i:Q.getUTCMinutes(),s:Q.getUTCSeconds(),z:P};if(F[R].meridiem.length==2){N.H=(N.h%12==0?12:N.h%12)}else{N.H=N.h}N.HH=(N.H<10?"0":"")+N.H;N.P=N.p.toUpperCase();N.Z=N.z;N.hh=(N.h<10?"0":"")+N.h;N.ii=(N.i<10?"0":"")+N.i;N.ss=(N.s<10?"0":"")+N.s;N.dd=(N.d<10?"0":"")+N.d;N.mm=(N.m<10?"0":"")+N.m}else{if(K=="php"){N={y:Q.getUTCFullYear().toString().substring(2),Y:Q.getUTCFullYear(),F:F[R].months[Q.getUTCMonth()],M:F[R].monthsShort[Q.getUTCMonth()],n:Q.getUTCMonth()+1,t:H.getDaysInMonth(Q.getUTCFullYear(),Q.getUTCMonth()),j:Q.getUTCDate(),l:F[R].days[Q.getUTCDay()],D:F[R].daysShort[Q.getUTCDay()],w:Q.getUTCDay(),N:(Q.getUTCDay()==0?7:Q.getUTCDay()),S:(Q.getUTCDate()%10<=F[R].suffix.length?F[R].suffix[Q.getUTCDate()%10-1]:""),a:(F[R].meridiem.length==2?F[R].meridiem[Q.getUTCHours()<12?0:1]:""),g:(Q.getUTCHours()%12==0?12:Q.getUTCHours()%12),G:Q.getUTCHours(),i:Q.getUTCMinutes(),s:Q.getUTCSeconds()};N.m=(N.n<10?"0":"")+N.n;N.d=(N.j<10?"0":"")+N.j;N.A=N.a.toString().toUpperCase();N.h=(N.g<10?"0":"")+N.g;N.H=(N.G<10?"0":"")+N.G;N.i=(N.i<10?"0":"")+N.i;N.s=(N.s<10?"0":"")+N.s}else{throw new Error("Invalid format type.")}}var Q=[],J=G.extend([],L.separators);for(var O=0,M=L.parts.length;O<M;O++){if(J.length){Q.push(J.shift())}Q.push(N[L.parts[O]])}if(J.length){Q.push(J.shift())}return Q.join("")},convertViewMode:function(J){switch(J){case 4:case"decade":J=4;break;case 3:case"year":J=3;break;case 2:case"month":J=2;break;case 1:case"day":J=1;break;case 0:case"hour":J=0;break}return J},headTemplate:'<thead><tr><th class="prev"><i class="{iconType} {leftArrow}"/></th><th colspan="5" class="switch"></th><th class="next"><i class="{iconType} {rightArrow}"/></th></tr></thead>',headTemplateV3:'<thead><tr><th class="prev"><span class="{iconType} {leftArrow}"></span> </th><th colspan="5" class="switch"></th><th class="next"><span class="{iconType} {rightArrow}"></span> </th></tr></thead>',contTemplate:'<tbody><tr><td colspan="7"></td></tr></tbody>',footTemplate:'<tfoot><tr><th colspan="7" class="today"></th></tr><tr><th colspan="7" class="clear"></th></tr></tfoot>'};H.template='<div class="datetimepicker"><div class="datetimepicker-minutes"><table class=" table-condensed">'+H.headTemplate+H.contTemplate+H.footTemplate+'</table></div><div class="datetimepicker-hours"><table class=" table-condensed">'+H.headTemplate+H.contTemplate+H.footTemplate+'</table></div><div class="datetimepicker-days"><table class=" table-condensed">'+H.headTemplate+"<tbody></tbody>"+H.footTemplate+'</table></div><div class="datetimepicker-months"><table class="table-condensed">'+H.headTemplate+H.contTemplate+H.footTemplate+'</table></div><div class="datetimepicker-years"><table class="table-condensed">'+H.headTemplate+H.contTemplate+H.footTemplate+"</table></div></div>";H.templateV3='<div class="datetimepicker"><div class="datetimepicker-minutes"><table class=" table-condensed">'+H.headTemplateV3+H.contTemplate+H.footTemplate+'</table></div><div class="datetimepicker-hours"><table class=" table-condensed">'+H.headTemplateV3+H.contTemplate+H.footTemplate+'</table></div><div class="datetimepicker-days"><table class=" table-condensed">'+H.headTemplateV3+"<tbody></tbody>"+H.footTemplate+'</table></div><div class="datetimepicker-months"><table class="table-condensed">'+H.headTemplateV3+H.contTemplate+H.footTemplate+'</table></div><div class="datetimepicker-years"><table class="table-condensed">'+H.headTemplateV3+H.contTemplate+H.footTemplate+"</table></div></div>";G.fn.datetimepicker.DPGlobal=H;G.fn.datetimepicker.noConflict=function(){G.fn.datetimepicker=D;return this};G(document).on("focus.datetimepicker.data-api click.datetimepicker.data-api",'[data-provide="datetimepicker"]',function(K){var J=G(this);if(J.data("datetimepicker")){return}K.preventDefault();J.datetimepicker("show")});G(function(){G('[data-provide="datetimepicker-inline"]').datetimepicker()})}));