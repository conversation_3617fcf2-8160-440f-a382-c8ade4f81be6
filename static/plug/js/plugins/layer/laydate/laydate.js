!function(E){var C={path:"",defSkin:"default",format:"YYYY-MM-DD",min:"1900-01-01 00:00:00",max:"2099-12-31 23:59:59",isv:!1},D={},H=document,A="createElement",F="getElementById",G="getElementsByTagName",B=["laydate_box","laydate_void","laydate_click","LayDateSkin","skins/","/laydate.css"];E.laydate=function(J){J=J||{};try{B.event=E.event?E.event:laydate.caller.arguments[0]}catch(I){}return D.run(J),laydate},laydate.v="1.1",D.getPath=function(){var J=document.scripts,I=J[J.length-1].src;return C.path?C.path:I.substring(0,I.lastIndexOf("/")+1)}(),D.use=function(J,I){var K=H[A]("link");K.type="text/css",K.rel="stylesheet",K.href=D.getPath+J+B[5],I&&(K.id=I),H[G]("head")[0].appendChild(K),K=null},D.trim=function(I){return I=I||"",I.replace(/^\s|\s$/g,"").replace(/\s+/g," ")},D.digit=function(I){return 10>I?"0"+(0|I):I},D.stopmp=function(I){return I=I||E.event,I.stopPropagation?I.stopPropagation():I.cancelBubble=!0,this},D.each=function(L,J){for(var K=0,I=L.length;I>K&&J(K,L[K])!==!1;K++){}},D.hasClass=function(J,I){return J=J||{},new RegExp("\\b"+I+"\\b").test(J.className)},D.addClass=function(J,I){return J=J||{},D.hasClass(J,I)||(J.className+=" "+I),J.className=D.trim(J.className),this},D.removeClass=function(K,J){if(K=K||{},D.hasClass(K,J)){var I=new RegExp("\\b"+J+"\\b");K.className=K.className.replace(I,"")}return this},D.removeCssAttr=function(K,I){var J=K.style;J.removeProperty?J.removeProperty(I):J.removeAttribute(I)},D.shde=function(J,I){J.style.display=I?"none":"block"},D.query=function(M){var K,L,I,J,N;return M=D.trim(M).split(" "),L=H[F](M[0].substr(1)),L?M[1]?/^\./.test(M[1])?(J=M[1].substr(1),N=new RegExp("\\b"+J+"\\b"),K=[],I=H.getElementsByClassName?L.getElementsByClassName(J):L[G]("*"),D.each(I,function(P,O){N.test(O.className)&&K.push(O)}),K[0]?K:""):(K=L[G](M[1]),K[0]?L[G](M[1]):""):L:void 0},D.on=function(K,I,J){return K.attachEvent?K.attachEvent("on"+I,function(){J.call(K,E.even)}):K.addEventListener(I,J,!1),D},D.stopMosup=function(J,I){"mouseup"!==J&&D.on(I,"mouseup",function(K){D.stopmp(K)})},D.run=function(N){var J,K,I,M=D.query,O=B.event;try{I=O.target||O.srcElement||{}}catch(L){I={}}if(J=N.elem?M(N.elem):I,O&&I.tagName){if(!J||J===D.elem){return}D.stopMosup(O.type,J),D.stopmp(O),D.view(J,N),D.reshow()}else{K=N.event||"click",D.each((0|J.length)>0?J:[J],function(Q,P){D.stopMosup(K,P),D.on(P,K,function(R){D.stopmp(R),P!==D.elem&&(D.view(P,N),D.reshow())})})}},D.scroll=function(I){return I=I?"scrollLeft":"scrollTop",H.body[I]|H.documentElement[I]},D.winarea=function(I){return document.documentElement[I?"clientWidth":"clientHeight"]},D.isleap=function(I){return 0===I%4&&0!==I%100||0===I%400},D.checkVoid=function(L,K,I){var J=[];return L=0|L,K=0|K,I=0|I,L<D.mins[0]?J=["y"]:L>D.maxs[0]?J=["y",1]:L>=D.mins[0]&&L<=D.maxs[0]&&(L==D.mins[0]&&(K<D.mins[1]?J=["m"]:K==D.mins[1]&&I<D.mins[2]&&(J=["d"])),L==D.maxs[0]&&(K>D.maxs[1]?J=["m",1]:K==D.maxs[1]&&I>D.maxs[2]&&(J=["d",1]))),J},D.timeVoid=function(J,I){if(D.ymd[1]+1==D.mins[1]&&D.ymd[2]==D.mins[2]){if(0===I&&J<D.mins[3]){return 1}if(1===I&&J<D.mins[4]){return 1}if(2===I&&J<D.mins[5]){return 1}}else{if(D.ymd[1]+1==D.maxs[1]&&D.ymd[2]==D.maxs[2]){if(0===I&&J>D.maxs[3]){return 1}if(1===I&&J>D.maxs[4]){return 1}if(2===I&&J>D.maxs[5]){return 1}}}return J>(I?59:23)?1:void 0},D.check=function(){var L=D.options.format.replace(/YYYY|MM|DD|hh|mm|ss/g,"\\d+\\").replace(/\\$/g,""),K=new RegExp(L),I=D.elem[B.elemv],J=I.match(/\d+/g)||[],M=D.checkVoid(J[0],J[1],J[2]);if(""!==I.replace(/\s/g,"")){if(!K.test(I)){return D.elem[B.elemv]="",D.msg("日期不符合格式，请重新选择。"),1}if(M[0]){return D.elem[B.elemv]="",D.msg("日期不在有效期内，请重新选择。"),1}M.value=D.elem[B.elemv].match(K).join(),J=M.value.match(/\d+/g),J[1]<1?(J[1]=1,M.auto=1):J[1]>12?(J[1]=12,M.auto=1):J[1].length<2&&(M.auto=1),J[2]<1?(J[2]=1,M.auto=1):J[2]>D.months[(0|J[1])-1]?(J[2]=31,M.auto=1):J[2].length<2&&(M.auto=1),J.length>3&&(D.timeVoid(J[3],0)&&(M.auto=1),D.timeVoid(J[4],1)&&(M.auto=1),D.timeVoid(J[5],2)&&(M.auto=1)),M.auto?D.creation([J[0],0|J[1],0|J[2]],1):M.value!==D.elem[B.elemv]&&(D.elem[B.elemv]=M.value)}},D.months=[31,null,31,30,31,30,31,31,30,31,30,31],D.viewDate=function(L,K,J){var M=(D.query,{}),I=new Date;L<(0|D.mins[0])&&(L=0|D.mins[0]),L>(0|D.maxs[0])&&(L=0|D.maxs[0]),I.setFullYear(L,K,J),M.ymd=[I.getFullYear(),I.getMonth(),I.getDate()],D.months[1]=D.isleap(M.ymd[0])?29:28,I.setFullYear(M.ymd[0],M.ymd[1],1),M.FDay=I.getDay(),M.PDay=D.months[0===K?11:K-1]-M.FDay+1,M.NDay=1,D.each(B.tds,function(R,Q){var N,O=M.ymd[0],P=M.ymd[1]+1;Q.className="",R<M.FDay?(Q.innerHTML=N=R+M.PDay,D.addClass(Q,"laydate_nothis"),1===P&&(O-=1),P=1===P?12:P-1):R>=M.FDay&&R<M.FDay+D.months[M.ymd[1]]?(Q.innerHTML=N=R-M.FDay+1,R-M.FDay+1===M.ymd[2]&&(D.addClass(Q,B[2]),M.thisDay=Q)):(Q.innerHTML=N=M.NDay++,D.addClass(Q,"laydate_nothis"),12===P&&(O+=1),P=12===P?1:P+1),D.checkVoid(O,P,N)[0]&&D.addClass(Q,B[1]),D.options.festival&&D.festival(Q,P+"."+N),Q.setAttribute("y",O),Q.setAttribute("m",P),Q.setAttribute("d",N),O=P=N=null}),D.valid=!D.hasClass(M.thisDay,B[1]),D.ymd=M.ymd,B.year.value=D.ymd[0]+"年",B.month.value=D.digit(D.ymd[1]+1)+"月",D.each(B.mms,function(P,O){var N=D.checkVoid(D.ymd[0],(0|O.getAttribute("m"))+1);"y"===N[0]||"m"===N[0]?D.addClass(O,B[1]):D.removeClass(O,B[1]),D.removeClass(O,B[2]),N=null}),D.addClass(B.mms[D.ymd[1]],B[2]),M.times=[0|D.inymd[3]||0,0|D.inymd[4]||0,0|D.inymd[5]||0],D.each(new Array(3),function(N){D.hmsin[N].value=D.digit(D.timeVoid(M.times[N],N)?0|D.mins[N+3]:0|M.times[N])}),D[D.valid?"removeClass":"addClass"](B.ok,B[1])},D.festival=function(K,I){var J;switch(I){case"1.1":J="元旦";break;case"3.8":J="妇女";break;case"4.5":J="清明";break;case"5.1":J="劳动";break;case"6.1":J="儿童";break;case"9.10":J="教师";break;case"10.1":J="国庆"}J&&(K.innerHTML=J),J=null},D.viewYears=function(K){var J=D.query,I="";D.each(new Array(14),function(L){I+=7===L?"<li "+(parseInt(B.year.value)===K?'class="'+B[2]+'"':"")+' y="'+K+'">'+K+"年</li>":'<li y="'+(K-7+L)+'">'+(K-7+L)+"年</li>"}),J("#laydate_ys").innerHTML=I,D.each(J("#laydate_ys li"),function(M,L){"y"===D.checkVoid(L.getAttribute("y"))[0]?D.addClass(L,B[1]):D.on(L,"click",function(N){D.stopmp(N).reshow(),D.viewDate(0|this.getAttribute("y"),D.ymd[1],D.ymd[2])})})},D.initDate=function(){var I=(D.query,new Date),J=D.elem[B.elemv].match(/\d+/g)||[];J.length<3&&(J=D.options.start.match(/\d+/g)||[],J.length<3&&(J=[I.getFullYear(),I.getMonth()+1,I.getDate()])),D.inymd=J,D.viewDate(J[0],J[1]-1,J[2])},D.iswrite=function(){var J=D.query,I={time:J("#laydate_hms")};D.shde(I.time,!D.options.istime),D.shde(B.oclear,!("isclear" in D.options?D.options.isclear:1)),D.shde(B.otoday,!("istoday" in D.options?D.options.istoday:1)),D.shde(B.ok,!("issure" in D.options?D.options.issure:1))},D.orien=function(L,K){var I,J=D.elem.getBoundingClientRect();L.style.left=J.left+(K?0:D.scroll(1))+"px",I=J.bottom+L.offsetHeight/1.5<=D.winarea()?J.bottom-1:J.top>L.offsetHeight/1.5?J.top-L.offsetHeight+1:D.winarea()-L.offsetHeight,L.style.top=I+(K?0:D.scroll())+"px"},D.follow=function(I){D.options.fixed?(I.style.position="fixed",D.orien(I,1)):(I.style.position="absolute",D.orien(I))},D.viewtb=function(){var M,L=[],N=["日","一","二","三","四","五","六"],I={},J=H[A]("table"),K=H[A]("thead");return K.appendChild(H[A]("tr")),I.creath=function(P){var O=H[A]("th");O.innerHTML=N[P],K[G]("tr")[0].appendChild(O),O=null},D.each(new Array(6),function(O){L.push([]),M=J.insertRow(0),D.each(new Array(7),function(P){L[O][P]=0,0===O&&I.creath(P),M.insertCell(P)})}),J.insertBefore(K,J.children[0]),J.id=J.className="laydate_table",M=L=null,J.outerHTML.toLowerCase()}(),D.view=function(L,M){var J,I=D.query,K={};M=M||L,D.elem=L,D.options=M,D.options.format||(D.options.format=C.format),D.options.start=D.options.start||"",D.mm=K.mm=[D.options.min||C.min,D.options.max||C.max],D.mins=K.mm[0].match(/\d+/g),D.maxs=K.mm[1].match(/\d+/g),B.elemv=/textarea|input/.test(D.elem.tagName.toLocaleLowerCase())?"value":"innerHTML",D.box?D.shde(D.box):(J=H[A]("div"),J.id=B[0],J.className=B[0],J.style.cssText="position: absolute;",J.setAttribute("name","laydate-v"+laydate.v),J.innerHTML=K.html='<div class="laydate_top"><div class="laydate_ym laydate_y" id="laydate_YY"><a class="laydate_choose laydate_chprev laydate_tab"><cite></cite></a><input id="laydate_y" readonly><label></label><a class="laydate_choose laydate_chnext laydate_tab"><cite></cite></a><div class="laydate_yms"><a class="laydate_tab laydate_chtop"><cite></cite></a><ul id="laydate_ys"></ul><a class="laydate_tab laydate_chdown"><cite></cite></a></div></div><div class="laydate_ym laydate_m" id="laydate_MM"><a class="laydate_choose laydate_chprev laydate_tab"><cite></cite></a><input id="laydate_m" readonly><label></label><a class="laydate_choose laydate_chnext laydate_tab"><cite></cite></a><div class="laydate_yms" id="laydate_ms">'+function(){var N="";return D.each(new Array(12),function(O){N+='<span m="'+O+'">'+D.digit(O+1)+"月</span>"}),N}()+"</div></div></div>"+D.viewtb+'<div class="laydate_bottom"><ul id="laydate_hms"><li class="laydate_sj">时间</li><li><input readonly>:</li><li><input readonly>:</li><li><input readonly></li></ul><div class="laydate_time" id="laydate_time"></div><div class="laydate_btn"><a id="laydate_clear">清空</a><a id="laydate_today">今天</a><a id="laydate_ok">确认</a></div>'+(C.isv?'<a href="http://sentsin.com/layui/laydate/" class="laydate_v" target="_blank">laydate-v'+laydate.v+"</a>":"")+"</div>",H.body.appendChild(J),D.box=I("#"+B[0]),D.events(),J=null),D.follow(D.box),M.zIndex?D.box.style.zIndex=M.zIndex:D.removeCssAttr(D.box,"z-index"),D.stopMosup("click",D.box),D.initDate(),D.iswrite(),D.check()},D.reshow=function(){return D.each(D.query("#"+B[0]+" .laydate_show"),function(J,I){D.removeClass(I,"laydate_show")}),this},D.close=function(){D.reshow(),D.shde(D.query("#"+B[0]),1),D.elem=null},D.parse=function(K,I,J){return K=K.concat(I),J=J||(D.options?D.options.format:C.format),J.replace(/YYYY|MM|DD|hh|mm|ss/g,function(){return K.index=0|++K.index,D.digit(K[K.index])})},D.creation=function(K,J){var I=(D.query,D.hmsin),L=D.parse(K,[I[0].value,I[1].value,I[2].value]);D.elem[B.elemv]=L,J||(D.close(),"function"==typeof D.options.choose&&D.options.choose(L))},D.events=function(){var J=D.query,I={box:"#"+B[0]};D.addClass(H.body,"laydate_body"),B.tds=J("#laydate_table td"),B.mms=J("#laydate_ms span"),B.year=J("#laydate_y"),B.month=J("#laydate_m"),D.each(J(I.box+" .laydate_ym"),function(L,K){D.on(K,"click",function(M){D.stopmp(M).reshow(),D.addClass(this[G]("div")[0],"laydate_show"),L||(I.YY=parseInt(B.year.value),D.viewYears(I.YY))})}),D.on(J(I.box),"click",function(){D.reshow()}),I.tabYear=function(K){0===K?D.ymd[0]--:1===K?D.ymd[0]++:2===K?I.YY-=14:I.YY+=14,2>K?(D.viewDate(D.ymd[0],D.ymd[1],D.ymd[2]),D.reshow()):D.viewYears(I.YY)},D.each(J("#laydate_YY .laydate_tab"),function(L,K){D.on(K,"click",function(M){D.stopmp(M),I.tabYear(L)})}),I.tabMonth=function(K){K?(D.ymd[1]++,12===D.ymd[1]&&(D.ymd[0]++,D.ymd[1]=0)):(D.ymd[1]--,-1===D.ymd[1]&&(D.ymd[0]--,D.ymd[1]=11)),D.viewDate(D.ymd[0],D.ymd[1],D.ymd[2])},D.each(J("#laydate_MM .laydate_tab"),function(L,K){D.on(K,"click",function(M){D.stopmp(M).reshow(),I.tabMonth(L)})}),D.each(J("#laydate_ms span"),function(L,K){D.on(K,"click",function(M){D.stopmp(M).reshow(),D.hasClass(this,B[1])||D.viewDate(D.ymd[0],0|this.getAttribute("m"),D.ymd[2])})}),D.each(J("#laydate_table td"),function(L,K){D.on(K,"click",function(M){D.hasClass(this,B[1])||(D.stopmp(M),D.creation([0|this.getAttribute("y"),0|this.getAttribute("m"),0|this.getAttribute("d")]))})}),B.oclear=J("#laydate_clear"),D.on(B.oclear,"click",function(){D.elem[B.elemv]="",D.close()}),B.otoday=J("#laydate_today"),D.on(B.otoday,"click",function(){D.elem[B.elemv]=laydate.now(0,D.options.format),D.close()}),B.ok=J("#laydate_ok"),D.on(B.ok,"click",function(){D.valid&&D.creation([D.ymd[0],D.ymd[1]+1,D.ymd[2]])}),I.times=J("#laydate_time"),D.hmsin=I.hmsin=J("#laydate_hms input"),I.hmss=["小时","分钟","秒数"],I.hmsarr=[],D.msg=function(L,K){var M='<div class="laydte_hsmtex">'+(K||"提示")+"<span>×</span></div>";"string"==typeof L?(M+="<p>"+L+"</p>",D.shde(J("#"+B[0])),D.removeClass(I.times,"laydate_time1").addClass(I.times,"laydate_msg")):(I.hmsarr[L]?M=I.hmsarr[L]:(M+='<div id="laydate_hmsno" class="laydate_hmsno">',D.each(new Array(0===L?24:60),function(N){M+="<span>"+N+"</span>"}),M+="</div>",I.hmsarr[L]=M),D.removeClass(I.times,"laydate_msg"),D[0===L?"removeClass":"addClass"](I.times,"laydate_time1")),D.addClass(I.times,"laydate_show"),I.times.innerHTML=M},I.hmson=function(M,K){var L=J("#laydate_hmsno span"),N=D.valid?null:1;D.each(L,function(P,O){N?D.addClass(O,B[1]):D.timeVoid(P,K)?D.addClass(O,B[1]):D.on(O,"click",function(){D.hasClass(this,B[1])||(M.value=D.digit(0|this.innerHTML))})}),D.addClass(L[0|M.value],"laydate_click")},D.each(I.hmsin,function(L,K){D.on(K,"click",function(M){D.stopmp(M).reshow(),D.msg(L,I.hmss[L]),I.hmson(this,L)})}),D.on(H,"mouseup",function(){var K=J("#"+B[0]);K&&"none"!==K.style.display&&(D.check()||D.close())}).on(H,"keydown",function(L){L=L||E.event;var K=L.keyCode;13===K&&D.creation([D.ymd[0],D.ymd[1]+1,D.ymd[2]])})},D.init=function(){D.use("need"),D.use(B[4]+C.defSkin,B[3]),D.skinLink=D.query("#"+B[3])}(),laydate.reset=function(){D.box&&D.elem&&D.follow(D.box)},laydate.now=function(K,J){var I=new Date(0|K?function(L){return 86400000>L?+new Date+86400000*L:L}(parseInt(K)):+new Date);return D.parse([I.getFullYear(),I.getMonth()+1,I.getDate()],[I.getHours(),I.getMinutes(),I.getSeconds()],J)},laydate.skin=function(I){D.skinLink.href=D.getPath+B[4]+I+B[5]}}(window);