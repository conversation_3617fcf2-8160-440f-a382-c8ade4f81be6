(function(A){A.extend(A.jqx._jqxChart.prototype,{_moduleAnnotations:true,_renderAnnotation:function(S,T,R){var J=this.seriesGroups[S];var E=this.renderer;if(isNaN(S)){return}var O=this._get([this.getXAxisDataPointOffset(T.xValue,S),T.x]);var K=this._get([this.getValueAxisDataPointOffset(T.yValue,S),T.y]);var F=this._get([this.getXAxisDataPointOffset(T.xValue2,S),T.x2]);var V=this._get([this.getValueAxisDataPointOffset(T.yValue2,S),T.y2]);if(J.polar||J.spider){var B=this.getPolarDataPointOffset(T.xValue,T.yValue,S);if(B&&!isNaN(B.x)&&!isNaN(B.y)){O=B.x;K=B.y}else{O=T.x;K=T.y}}if(isNaN(K)||isNaN(O)){return false}if(J.orientation=="horizontal"){var G=O;O=K;K=G;G=F;F=V;V=G}if(T.offset){if(!isNaN(T.offset.x)){O+=T.offset.x;F+=T.offset.x}if(!isNaN(T.offset.y)){K+=T.offset.y;V+=T.offset.y}}var D=this._get([T.width,F-O]);var N=this._get([T.height,V-K]);var U;switch(T.type){case"rect":U=E.rect(O,K,D,N);break;case"circle":U=E.rect(O,K,T.radius);break;case"line":U=E.rect(O,K,F,V);break;case"path":U=E.path(T.path);break}E.attr(U,{fill:T.fillColor,stroke:T.lineColor,opacity:this._get([T.fillOpacity,T.opacity]),"stroke-opacity":this._get([T.lineOpacity,T.opacity]),"stroke-width":T.lineWidth,"stroke-dasharray":T.dashStyle||"none"});var I;if(T.text){var P=T.text;var W=0,L=0;if(P.offset){if(!isNaN(P.offset.x)){W+=P.offset.x}if(!isNaN(P.offset.y)){L+=P.offset.y}}I=E.text(P.value,O+W,K+L,NaN,NaN,P.angle,{},P.clip===true,P.horizontalAlignment||"center",P.verticalAlignment||"center",P.rotationPoint||"centermiddle");E.attr(I,{fill:P.fillColor,stroke:P.lineColor,"class":P["class"]})}var Q=["click","mouseenter","mouseleave"];var M=this;for(var H=0;H<Q.length;H++){var C=this._getEvent(Q[H])||Q[H];if(U){this.renderer.addHandler(U,C,function(){M._raiseAnnotationEvent(T,C)})}if(I){this.renderer.addHandler(I,C,function(){M._raiseAnnotationEvent(T,C)})}}},_raiseAnnotationEvent:function(B,C){this._raiseEvent("annotation_"+C,{annotation:B})}})})(jqxBaseFramework);