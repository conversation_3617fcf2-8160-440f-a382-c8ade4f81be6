(function(A){A.jqx.jqxWidget("jqxTree","",{});A.extend(A.jqx._jqxTree.prototype,{defineInstance:function(){var B={items:new Array(),width:null,height:null,easing:"easeInOutCirc",animationShowDuration:"fast",animationHideDuration:"fast",treeElements:new Array(),disabled:false,itemsMember:"",displayMember:"",valueMember:"",enableHover:true,keyboardNavigation:true,enableKeyboardNavigation:true,toggleMode:"dblclick",source:null,checkboxes:false,checkSize:13,toggleIndicatorSize:16,hasThreeStates:false,selectedItem:null,touchMode:"auto",allowDrag:true,allowDrop:true,searchMode:"startswithignorecase",incrementalSearch:true,incrementalSearchDelay:700,animationHideDelay:0,submitCheckedItems:false,dragStart:null,dragEnd:null,rtl:false,dropAction:"default",events:["expand","collapse","select","initialized","added","removed","checkChange","dragEnd","dragStart","itemClick"],aria:{"aria-activedescendant":{name:"getActiveDescendant",type:"string"},"aria-disabled":{name:"disabled",type:"boolean"}}};A.extend(true,this,B);return B},createInstance:function(E){var D=this;this.host.attr("role","tree");this.host.attr("data-role","treeview");this.enableKeyboardNavigation=this.keyboardNavigation;this.propertyChangeMap.disabled=function(I,F,G,H){if(D.disabled){D.host.addClass(D.toThemeProperty("jqx-tree-disabled"))}else{D.host.removeClass(D.toThemeProperty("jqx-tree-disabled"))}A.jqx.aria(D,"aria-disabled",H)};if(this.width!=null&&this.width.toString().indexOf("px")!=-1){this.host.width(this.width)}else{if(this.width!=undefined&&!isNaN(this.width)){this.host.width(this.width)}}if(this.height!=null&&this.height.toString().indexOf("px")!=-1){this.host.height(this.height)}else{if(this.height!=undefined&&!isNaN(this.height)){this.host.height(this.height)}}if(this.width!=null&&this.width.toString().indexOf("%")!=-1){this.host.width(this.width)}if(this.height!=null&&this.height.toString().indexOf("%")!=-1){this.host.height(this.height)}if(!this.host.attr("tabindex")){this.host.attr("tabIndex",1)}if(this.disabled){this.host.addClass(this.toThemeProperty("jqx-tree-disabled"));A.jqx.aria(this,"aria-disabled",true)}if(this.host.jqxDragDrop){jqxTreeDragDrop()}this.originalInnerHTML=this.element.innerHTML;this.createdTree=false;if(this.element.innerHTML.indexOf("UL")){var C=this.host.find("ul:first");if(C.length>0){this.createTree(C[0]);this.createdTree=true}}if(this.source!=null){var B=this.loadItems(this.source);this.element.innerHTML=B;var C=this.host.find("ul:first");if(C.length>0){this.createTree(C[0]);this.createdTree=true}}this._itemslength=this.items.length;if(!this.createdTree){if(this.host.find("ul").length==0){this.host.append(A("<ul></ul>"));var C=this.host.find("ul:first");if(C.length>0){this.createTree(C[0]);this.createdTree=true}this.createdTree=true}}if(this.createdTree==true){this._render();this._handleKeys()}this._updateCheckLayout()},checkItems:function(G,D){var B=this;if(G!=null){var I=0;var H=false;var E=0;var C=A(G.element).find("li");E=C.length;A.each(C,function(K){var J=B.itemMapping["id"+this.id].item;if(J.checked!=false){if(J.checked==null){H=true}I++}});if(G!=D){if(I==E){this.checkItem(G.element,true,"tree")}else{if(I>0){this.checkItem(G.element,null,"tree")}else{this.checkItem(G.element,false,"tree")}}}else{var F=D.checked;var C=A(D.element).find("li");A.each(C,function(){var J=B.itemMapping["id"+this.id].item;B.checkItem(this,F,"tree")})}this.checkItems(this._parentItem(G),D)}else{var F=D.checked;var C=A(D.element).find("li");A.each(C,function(){var J=B.itemMapping["id"+this.id].item;B.checkItem(this,F,"tree")})}},_getMatches:function(D,G){if(D==undefined||D.length==0){return -1}var F=this.items;var E=new Array();for(var C=0;C<F.length;C++){if(this._isVisible(F[C])&&!F[C].disabled){E.push(F[C])}}F=E;if(G!=undefined){F=F.slice(G)}var B=new Array();A.each(F,function(I){var J=this.label;if(!J){J=""}var H=A.jqx.string.startsWithIgnoreCase(J.toString(),D);if(H){B.push({id:this.id,element:this.element})}});return B},_handleKeys:function(){var B=this;this.addHandler(this.host,"keydown",function(R){var D=R.keyCode;if(B.keyboardNavigation||B.enableKeyboardNavigation){if(B.selectedItem!=null){var M=B.selectedItem.element;if(B.incrementalSearch&&(!(D>=33&&D<=40))){var G=-1;if(!B._searchString){B._searchString=""}if((D==8||D==46)&&B._searchString.length>=1){B._searchString=B._searchString.substr(0,B._searchString.length-1)}var J=String.fromCharCode(D);var L=(!isNaN(parseInt(J)));var K=false;if((D>=65&&D<=97)||L||D==8||D==32||D==46){if(!R.shiftKey){J=J.toLocaleLowerCase()}if(D!=8&&D!=32&&D!=46){if(!(B._searchString.length>0&&B._searchString.substr(0,1)==J)){B._searchString+=J}}if(D==32){B._searchString+=" "}B._searchTime=new Date();var C=B.selectedItem;if(C){var Q=C.id;var N=-1;for(var I=0;I<B.items.length;I++){if(B.items[I].id==Q){N=I+1;break}}var P=B._getMatches(B._searchString,N);if(P.length==0||(P.length>0&&P[0].id==Q)){var P=B._getMatches(B._searchString)}}else{var P=B._getMatches(B._searchString)}if(P.length>0){var C=B.selectedItem;if(B.selectedItem&&B.selectedItem.id!=P[0].id){B.clearSelection();B.selectItem(P[0].element,"keyboard")}B._lastSearchString=B._searchString}}if(B._searchTimer!=undefined){clearTimeout(B._searchTimer)}if(D==27||D==13){B._searchString="";B._lastSearchString=""}B._searchTimer=setTimeout(function(){B._searchString="";B._lastSearchString=""},500);if(G>=0){return}if(K){return false}}switch(D){case 32:if(B.checkboxes){B.fromKey=true;var F=A(B.selectedItem.checkBoxElement).jqxCheckBox("checked");B.checkItem(B.selectedItem.element,!F,"tree");if(B.hasThreeStates){B.checkItems(B.selectedItem,B.selectedItem)}return false}return true;case 33:var H=B._getItemsOnPage();var E=B.selectedItem;for(var I=0;I<H;I++){E=B._prevVisibleItem(E)}if(E!=null){B.selectItem(E.element,"keyboard");B.ensureVisible(E.element)}else{B.selectItem(B._firstItem().element,"keyboard");B.ensureVisible(B._firstItem().element)}return false;case 34:var H=B._getItemsOnPage();var O=B.selectedItem;for(var I=0;I<H;I++){O=B._nextVisibleItem(O)}if(O!=null){B.selectItem(O.element,"keyboard");B.ensureVisible(O.element)}else{B.selectItem(B._lastItem().element,"keyboard");B.ensureVisible(B._lastItem().element)}return false;case 37:case 39:if((D==37&&!B.rtl)||(D==39&&B.rtl)){if(B.selectedItem.hasItems&&B.selectedItem.isExpanded){B.collapseItem(M)}else{var S=B._parentItem(B.selectedItem);if(S!=null){B.selectItem(S.element,"keyboard");B.ensureVisible(S.element)}}}if((D==39&&!B.rtl)||(D==37&&B.rtl)){if(B.selectedItem.hasItems){if(!B.selectedItem.isExpanded){B.expandItem(M)}else{var O=B._nextVisibleItem(B.selectedItem);if(O!=null){B.selectItem(O.element,"keyboard");B.ensureVisible(O.element)}}}}return false;case 13:if(B.selectedItem.hasItems){if(B.selectedItem.isExpanded){B.collapseItem(M)}else{B.expandItem(M)}}return false;case 36:B.selectItem(B._firstItem().element,"keyboard");B.ensureVisible(B._firstItem().element);return false;case 35:B.selectItem(B._lastItem().element,"keyboard");B.ensureVisible(B._lastItem().element);return false;case 38:var E=B._prevVisibleItem(B.selectedItem);if(E!=null){B.selectItem(E.element,"keyboard");B.ensureVisible(E.element)}return false;case 40:var O=B._nextVisibleItem(B.selectedItem);if(O!=null){B.selectItem(O.element,"keyboard");B.ensureVisible(O.element)}return false}}}})},_firstItem:function(){var B=null;var E=this;var F=this.host.find("ul:first");var C=A(F).find("li");for(i=0;i<=C.length-1;i++){var D=C[i];B=this.itemMapping["id"+D.id].item;if(E._isVisible(B)){return B}}return null},_lastItem:function(){var B=null;var E=this;var F=this.host.find("ul:first");var C=A(F).find("li");for(i=C.length-1;i>=0;i--){var D=C[i];B=this.itemMapping["id"+D.id].item;if(E._isVisible(B)){return B}}return null},_parentItem:function(B){if(B==null||B==undefined){return null}var D=B.parentElement;if(!D){return null}var C=null;A.each(this.items,function(){if(this.element==D){C=this;return false}});return C},_nextVisibleItem:function(C){if(C==null||C==undefined){return null}var B=C;while(B!=null){B=B.nextItem;if(this._isVisible(B)&&!B.disabled){return B}}return null},_prevVisibleItem:function(C){if(C==null||C==undefined){return null}var B=C;while(B!=null){B=B.prevItem;if(this._isVisible(B)&&!B.disabled){return B}}return null},_isVisible:function(C){if(C==null||C==undefined){return false}if(!this._isElementVisible(C.element)){return false}var B=this._parentItem(C);if(B==null){return true}if(B!=null){if(!this._isElementVisible(B.element)){return false}if(B.isExpanded){while(B!=null){B=this._parentItem(B);if(B!=null&&!this._isElementVisible(B.element)){return false}if(B!=null&&!B.isExpanded){return false}}}else{return false}}return true},_getItemsOnPage:function(){var B=0;var E=this.panel.jqxPanel("getVScrollPosition");var D=parseInt(this.host.height());var F=0;var C=this._firstItem();if(parseInt(A(C.element).height())>0){while(F<=D){F+=parseInt(A(C.element).outerHeight());B++}}return B},_isElementVisible:function(B){if(B==null){return false}if(A(B).css("display")!="none"&&A(B).css("visibility")!="hidden"){return true}return false},refresh:function(C){if(this.width!=null&&this.width.toString().indexOf("px")!=-1){this.host.width(this.width)}else{if(this.width!=undefined&&!isNaN(this.width)){this.host.width(this.width)}}if(this.height!=null&&this.height.toString().indexOf("px")!=-1){this.host.height(this.height)}else{if(this.height!=undefined&&!isNaN(this.height)){this.host.height(this.height)}}if(this.panel){if(this.width!=null&&this.width.toString().indexOf("%")!=-1){var B=this;this.panel.jqxPanel("width","100%");B.removeHandler(A(window),"resize.jqxtree"+B.element.id);B.addHandler(A(window),"resize.jqxtree"+B.element.id,function(){B._calculateWidth()})}else{this.panel.jqxPanel("width",this.host.width())}this.panel.jqxPanel("_arrange")}this._calculateWidth();if(A.jqx.isHidden(this.host)){var B=this;if(this._hiddenTimer){clearInterval(this._hiddenTimer)}this._hiddenTimer=setInterval(function(){if(!A.jqx.isHidden(B.host)){clearInterval(B._hiddenTimer);B._calculateWidth()}},100)}if(C!=true){if(this.checkboxes){this._updateCheckLayout(null)}}},resize:function(C,B){this.width=C;this.height=B;this.refresh()},loadItems:function(D){if(D==null){return}var C=this;this.items=new Array();var B="<ul>";A.map(D,function(E){if(E==undefined){return null}B+=C._parseItem(E)});B+="</ul>";return B},_parseItem:function(F){var L="";if(F==undefined){return null}var D=F.label;var H=F.value;if(!F.label&&F.html){D=F.html}if(this.displayMember!=undefined&&this.displayMember!=""){D=F[this.displayMember]}if(this.valueMember!=undefined&&this.valueMember!=""){H=F[this.valueMember]}if(!D){D="Item"}if(typeof F==="string"){D=F}var E=false;if(F.expanded!=undefined&&F.expanded){E=true}var K=false;if(F.locked!=undefined&&F.locked){K=true}var M=false;if(F.selected!=undefined&&F.selected){M=true}var B=false;if(F.disabled!=undefined&&F.disabled){B=true}var G=false;if(F.checked!=undefined&&F.checked){G=true}var C=F.icon;var J=F.iconsize;L+="<li";if(E){L+=' item-expanded="true" '}if(K){L+=' item-locked="true" '}if(B){L+=' item-disabled="true" '}if(M){L+=' item-selected="true" '}if(J){L+=' item-iconsize="'+F.iconsize+'" '}if(C!=null&&C!=undefined){L+=' item-icon="'+C+'" '}if(F.label&&!F.html){L+=' item-label="'+D+'" '}if(H!=null){L+=' item-value="'+H+'" '}if(F.checked!=undefined){L+=' item-checked="'+G+'" '}var I="";if(F.id!=undefined){I=F.id;L+=' id="'+I+'" '}else{I=this.createID();L+=' id="'+I+'" '}L+=">"+D;if(F.items){L+=this.loadItems(F.items)}else{if(this.itemsMember!=undefined&&this.itemsMember!=""){if(F[this.itemsMember]){L+=this.loadItems(F[this.itemsMember])}}}if(!this._valueList){this._valueList=new Array()}this._valueList[I]=F.value;L+="</li>";return L},ensureVisible:function(B){if(B==null||B==undefined){return}if(this.panel){var E=this.panel.jqxPanel("getVScrollPosition");var C=this.panel.jqxPanel("getHScrollPosition");var D=parseInt(this.host.height());var F=A(B).position().top;if(F<=E||F>=D+E){this.panel.jqxPanel("scrollTo",C,F-D+A(B).outerHeight())}}},_syncItems:function(C){this._visibleItems=new Array();var B=this;A.each(C,function(){var E=A(this);if(E.css("display")!="none"){var D=E.outerHeight();if(E.height()>0){var F=parseInt(E.offset().top);B._visibleItems[B._visibleItems.length]={element:this,top:F,height:D,bottom:F+D}}}})},hitTest:function(D,H){var I=this;var E=this;var G=null;var B=this.host.find(".jqx-item");this._syncItems(B);if(E._visibleItems){var F=parseInt(E.host.offset().left);var C=E.host.outerWidth();A.each(E._visibleItems,function(J){if(D>=F&&D<F+C){if(this.top+5<H&&H<this.top+this.height){var K=A(this.element).parents("li:first");if(K.length>0){G=E.getItem(K[0]);if(G!=null){G.height=this.height;G.top=this.top;return false}}}}})}return G},addBefore:function(C,B,D){return this.addBeforeAfter(C,B,true,D)},addAfter:function(C,B,D){return this.addBeforeAfter(C,B,false,D)},addBeforeAfter:function(I,B,D,H){var J=this;var K=new Array();if(B&&B.treeInstance!=undefined){B=B.element}if(!A.isArray(I)){K[0]=I}else{K=I}var O="";var C=this;A.each(K,function(){O+=C._parseItem(this)});var L=A(O);if(J.element.innerHTML.indexOf("UL")){var G=J.host.find("ul:first")}if(B==undefined&&B==null){G.append(L)}else{if(D){A(B).before(L)}else{A(B).after(L)}}var P=L;for(var F=0;F<P.length;F++){this._createItem(P[F]);var M=A(P[F]).find("li");if(M.length>0){for(var N=0;N<M.length;N++){this._createItem(M[N])}}}var E=function(Q){C._refreshMapping(false);C._updateItemsNavigation();if(Q&&C.allowDrag&&C._enableDragDrop){C._enableDragDrop()}if(C.selectedItem!=null){A(C.selectedItem.titleElement).addClass(C.toThemeProperty("jqx-fill-state-pressed"));A(C.selectedItem.titleElement).addClass(C.toThemeProperty("jqx-tree-item-selected"))}};if(H==false){E(true);this._raiseEvent("4",{items:this.getItems()});return}E(false);C._render();this._raiseEvent("4",{items:this.getItems()});if(C.checkboxes){C._updateCheckLayout(null)}},addTo:function(E,C,D){var I=this;var J=new Array();if(C&&C.treeInstance!=undefined){C=C.element}if(!A.isArray(E)){J[0]=E}else{J=E}var G="";var B=this;A.each(J,function(){G+=B._parseItem(this)});var M=A(G);if(I.element.innerHTML.indexOf("UL")){var K=I.host.find("ul:first")}if(C==undefined&&C==null){K.append(M)}else{C=A(C);var R=C.find("ul:first");if(R.length==0){ulElement=A("<ul></ul>");A(C).append(ulElement);R=C.find("ul:first");var F=I.itemMapping["id"+C[0].id].item;F.subtreeElement=R[0];F.hasItems=true;R.addClass(I.toThemeProperty("jqx-tree-dropdown"));if(B.rtl){R.addClass(I.toThemeProperty("jqx-tree-dropdown-rtl"))}R.append(M);var H=R.find("li:first");F.parentElement=H}else{R.append(M)}}var Q=M;for(var L=0;L<Q.length;L++){this._createItem(Q[L]);var N=A(Q[L]).find("li");if(N.length>0){for(var P=0;P<N.length;P++){this._createItem(N[P])}}}var O=function(S){B._refreshMapping(false);B._updateItemsNavigation();if(S&&B.allowDrag&&B._enableDragDrop){B._enableDragDrop()}if(B.selectedItem!=null){A(B.selectedItem.titleElement).addClass(B.toThemeProperty("jqx-fill-state-pressed"));A(B.selectedItem.titleElement).addClass(B.toThemeProperty("jqx-tree-item-selected"))}};if(D==false){O(true);this._raiseEvent("4",{items:this.getItems()});return}O(false);B._render();if(B.checkboxes){B._updateCheckLayout(null)}this._raiseEvent("4",{items:this.getItems()})},updateItem:function(B,C){var D=B.treeInstance!=undefined?B:this.getItem(B);if(!D){var I=B;B=C;C=I;var D=B.treeInstance!=undefined?B:this.getItem(B)}if(D){if(typeof(C)==="string"){C={label:C}}if(C.value){D.value=C.value}if(C.label){D.label=C.label;A.jqx.utilities.html(A(D.titleElement),C.label);var E=A.jqx.browser.msie&&A.jqx.browser.version<8;if(E){A(document.body).append(this._measureItem);this._measureItem.html(A(D.titleElement).text());var H=this._measureItem.width();if(D.icon){H+=20}if(A(A(D.titleElement).find("img")).length>0){H+=20}A(D.titleElement).css("max-width",H+"px");this._measureItem.remove()}}if(C.icon){if(A(D.element).children(".itemicon").length>0){A(D.element).find(".itemicon")[0].src=C.icon}else{var F=C.iconsize;if(!F){F=16}var G=A('<img width="'+F+'" height="'+F+'" style="float: left;" class="itemicon" src="'+C.icon+'"/>');A(D.titleElement).prepend(G);G.css("margin-right","4px");if(this.rtl){G.css("margin-right","0px");G.css("margin-left","4px");G.css("float","right")}}}if(C.expanded){this.expandItem(D)}if(C.disabled){this.disableItem(D)}if(C.selected){this.selectItem(D)}return true}return false},removeItem:function(F,C){if(F==undefined||F==null){return}if(F.treeInstance!=undefined){F=F.element}var D=this;var B=F.id;var G=-1;var H=this.getItem(F);if(H){G=this.items.indexOf(H);if(G!=-1){(function E(J){var I=-1;I=this.items.indexOf(J);if(I!=-1){this.items.splice(I,1)}var O=A(J.element).find("li");var N=O.length;var K=this;var L=new Array();if(N>0){A.each(O,function(Q){var P=K.itemMapping["id"+this.id].item;L.push(P)});for(var M=0;M<L.length;M++){E.apply(this,[L[M]])}}}).apply(this,[H])}}if(this.host.find("#"+F.id).length>0){A(F).remove()}if(C==false){this._raiseEvent("5");return}D._updateItemsNavigation();if(D.allowDrag&&D._enableDragDrop){D._render(true,false)}else{D._render()}if(D.selectedItem!=null){if(D.selectedItem.element==F){A(D.selectedItem.titleElement).removeClass(D.toThemeProperty("jqx-fill-state-pressed"));A(D.selectedItem.titleElement).removeClass(D.toThemeProperty("jqx-tree-item-selected"));D.selectedItem=null}}this._raiseEvent("5");if(D.checkboxes){D._updateCheckLayout(null)}},clear:function(){this.items=new Array();this.itemMapping=new Array();var B=this.host.find("ul:first");if(B.length>0){B[0].innerHTML=""}this.selectedItem=null},disableItem:function(B){if(B==null){return false}if(B.treeInstance!=undefined){B=B.element}var C=this;A.each(C.items,function(){var D=this;if(D.element==B){D.disabled=true;A(D.titleElement).addClass(C.toThemeProperty("jqx-fill-state-disabled"));A(D.titleElement).addClass(C.toThemeProperty("jqx-tree-item-disabled"));if(C.checkboxes&&D.checkBoxElement){A(D.checkBoxElement).jqxCheckBox({disabled:true})}return false}})},_updateInputSelection:function(){if(this.input){if(this.selectedItem==null){this.input.val("")}else{var E=this.selectItem.value;if(E==null){E=this.selectedItem.label}this.input.val(E)}if(this.checkboxes){var D=this.getCheckedItems();if(this.submitCheckedItems){var F="";for(var B=0;B<D.length;B++){var C=D[B].value;if(C==null){C=D[B].label}if(B==D.length-1){F+=C}else{F+=C+","}}this.input.val(F)}}}},getCheckedItems:function(){var B=new Array();var C=this;A.each(C.items,function(){var D=this;if(D.checked){B.push(D)}});return B},getUncheckedItems:function(){var B=new Array();var C=this;A.each(C.items,function(){var D=this;if(!D.checked){B.push(D)}});return B},checkAll:function(){var B=this;A.each(B.items,function(){var C=this;if(!C.disabled){C.checked=true;A(C.checkBoxElement).jqxCheckBox("_setState",true)}});this._raiseEvent("6",{element:this,checked:true})},uncheckAll:function(){var B=this;A.each(B.items,function(){var C=this;if(!C.disabled){C.checked=false;A(C.checkBoxElement).jqxCheckBox("_setState",false)}});this._raiseEvent("6",{element:this,checked:false})},checkItem:function(C,G,E){if(C==null){return false}if(G===undefined){G=true}if(C.treeInstance!=undefined){C=C.element}var D=this;var F=false;var B=null;A.each(D.items,function(){var H=this;if(H.element==C&&!H.disabled){F=true;H.checked=G;B=H;A(H.checkBoxElement).jqxCheckBox({checked:G});return false}});if(F){this._raiseEvent("6",{element:C,checked:G});this._updateInputSelection()}if(E==undefined){if(B){if(this.hasThreeStates){this.checkItems(B,B)}}}},uncheckItem:function(B){this.checkItem(B,false)},enableItem:function(B){if(B==null){return false}if(B.treeInstance!=undefined){B=B.element}var C=this;A.each(C.items,function(){var D=this;if(D.element==B){D.disabled=false;A(D.titleElement).removeClass(C.toThemeProperty("jqx-fill-state-disabled"));A(D.titleElement).removeClass(C.toThemeProperty("jqx-tree-item-disabled"));if(C.checkboxes&&D.checkBoxElement){A(D.checkBoxElement).jqxCheckBox({disabled:false})}return false}})},enableAll:function(){var B=this;A.each(B.items,function(){var C=this;C.disabled=false;A(C.titleElement).removeClass(B.toThemeProperty("jqx-tree-item-disabled"));A(C.titleElement).removeClass(B.toThemeProperty("jqx-fill-state-disabled"));if(B.checkboxes&&C.checkBoxElement){A(C.checkBoxElement).jqxCheckBox({disabled:false})}})},lockItem:function(B){if(B==null){return false}var C=this;A.each(C.items,function(){var D=this;if(D.element==B){D.locked=true;return false}})},unlockItem:function(B){if(B==null){return false}var C=this;A.each(C.items,function(){var D=this;if(D.element==B){D.locked=false;return false}})},getItems:function(){return this.items},getItem:function(B){if(B==null||B==undefined){return null}if(this.itemMapping["id"+B.id]){var C=this.itemMapping["id"+B.id].item;return C}return null},isExpanded:function(B){if(B==null||B==undefined){return false}var C=this.itemMapping["id"+B.id].item;if(C!=null){return C.isExpanded}return false},isSelected:function(B){if(B==null||B==undefined){return false}var C=this.itemMapping["id"+B.id].item;if(C!=null){return C==this.selectedItem}return false},getPrevItem:function(D){var B=this.getItem(D);if(D.treeInstance!=undefined){B=D}var C=this._prevVisibleItem(B);return C},getNextItem:function(D){var B=this.getItem(D);if(D.treeInstance!=undefined){B=D}var C=this._nextVisibleItem(B);return C},getSelectedItem:function(B){return this.selectedItem},val:function(B){if(arguments.length==0||typeof(B)=="object"){return this.selectedItem}if(typeof B=="string"){var C=this.host.find("#"+B);if(C.length>0){var D=this.getItem(C[0]);this.selectItem(D)}}else{var D=this.getItem(B);this.selectItem(D)}},getActiveDescendant:function(){if(this.selectedItem){return this.selectedItem.element.id}return""},clearSelection:function(){this.selectItem(null)},selectItem:function(D,E){if(this.disabled){return}var B=this;if(D&&D.treeInstance!=undefined){D=D.element}if(D==null||D==undefined){if(B.selectedItem!=null){A(B.selectedItem.titleElement).removeClass(B.toThemeProperty("jqx-fill-state-pressed"));A(B.selectedItem.titleElement).removeClass(B.toThemeProperty("jqx-tree-item-selected"));B.selectedItem=null}return}if(this.selectedItem!=null&&this.selectedItem.element==D){return}var C=this.selectedItem!=null?this.selectedItem.element:null;if(C){A(C).removeAttr("aria-selected")}A.each(B.items,function(){var F=this;this.selected=false;if(!F.disabled){if(F.element==D){if(B.selectedItem==null||(B.selectedItem!=null&&B.selectedItem.titleElement!=F.titleElement)){if(B.selectedItem!=null){A(B.selectedItem.titleElement).removeClass(B.toThemeProperty("jqx-fill-state-pressed"));A(B.selectedItem.titleElement).removeClass(B.toThemeProperty("jqx-tree-item-selected"))}A(F.titleElement).addClass(B.toThemeProperty("jqx-fill-state-pressed"));A(F.titleElement).addClass(B.toThemeProperty("jqx-tree-item-selected"));B.selectedItem=F;this.selected=true;A(F.element).attr("aria-selected","true");A.jqx.aria(B,"aria-activedescendant",F.element.id)}}}});this._updateInputSelection();if(!E){E=null}this._raiseEvent("2",{element:D,prevElement:C,type:E})},collapseAll:function(){this.isUpdating=true;var B=this;var C=B.items;var D=this.animationHideDuration;this.animationHideDuration=0;A.each(C,function(){var E=this;if(E.isExpanded==true){B._collapseItem(B,E)}});setTimeout(function(){B.isUpdating=false;B._calculateWidth()},this.animationHideDuration);this.animationHideDuration=D},expandAll:function(){var C=this;this.isUpdating=true;var B=this.animationShowDuration;this.animationShowDuration=0;A.each(this.items,function(){var D=this;if(D.hasItems){C._expandItem(C,D)}});setTimeout(function(){C.isUpdating=false;C._calculateWidth()},this.animationShowDuration);this.animationShowDuration=B},collapseItem:function(B){if(B==null){return false}if(B.treeInstance!=undefined){B=B.element}var C=this;A.each(this.items,function(){var D=this;if(D.isExpanded==true&&D.element==B){C._collapseItem(C,D);return false}});return true},expandItem:function(B){if(B==null){return false}if(B.treeInstance!=undefined){B=B.element}var C=this;A.each(C.items,function(){var D=this;if(D.isExpanded==false&&D.element==B&&!D.disabled&&!D.locked){C._expandItem(C,D);if(D.parentElement){C.expandItem(D.parentElement)}}});return true},_getClosedSubtreeOffset:function(E){var D=A(E.subtreeElement);var C=-D.outerHeight();var B=-D.outerWidth();B=0;return{left:B,top:C}},_collapseItem:function(J,D,K,G){if(J==null||D==null){return false}if(D.disabled){return false}if(J.disabled){return false}if(J.locked){return false}var B=A(D.subtreeElement);var F=this._getClosedSubtreeOffset(D);var E=F.top;var H=F.left;$treeElement=A(D.element);var I=J.animationHideDelay;I=0;if(B.data("timer").show!=null){clearTimeout(B.data("timer").show);B.data("timer").show=null}var C=function(){D.isExpanded=false;if(J.checkboxes){var L=B.find(".chkbox");L.stop();L.css("opacity",1);B.find(".chkbox").animate({opacity:0},50)}var M=A(D.arrow);J._arrowStyle(M,"",D.isExpanded);B.slideUp(J.animationHideDuration,function(){D.isCollapsing=false;J._calculateWidth();var N=A(D.arrow);J._arrowStyle(N,"",D.isExpanded);B.hide();J._raiseEvent("1",{element:D.element})})};if(I>0){B.data("timer").hide=setTimeout(function(){C()},I)}else{C()}},_expandItem:function(I,D){if(I==null||D==null){return false}if(D.isExpanded){return false}if(D.locked){return false}if(D.disabled){return false}if(I.disabled){return false}var B=A(D.subtreeElement);if((B.data("timer"))!=null&&B.data("timer").hide!=null){clearTimeout(B.data("timer").hide)}var C=A(D.element);var E=0;var J=0;if(parseInt(B.css("top"))==E){D.isExpanded=true;return}var G=A(D.arrow);I._arrowStyle(G,"",D.isExpanded);if(I.checkboxes){var H=B.find(".chkbox");H.stop();H.css("opacity",0);H.animate({opacity:1},I.animationShowDuration)}B.slideDown(I.animationShowDuration,I.easing,function(){var K=A(D.arrow);D.isExpanded=true;I._arrowStyle(K,"",D.isExpanded);D.isExpanding=false;I._raiseEvent("0",{element:D.element});I._calculateWidth()});if(I.checkboxes){I._updateCheckItemLayout(D);if(D.subtreeElement){var F=A(D.subtreeElement).find("li");A.each(F,function(){var K=I.getItem(this);if(K!=null){I._updateCheckItemLayout(K)}})}}},_calculateWidth:function(){var G=this;var B=this.checkboxes?20:0;var D=0;if(this.isUpdating){return}A.each(this.items,function(){var H=A(this.element).height();if(H!=0){var I=A(this.titleElement).outerWidth()+10+B+(1+this.level)*20;D=Math.max(D,I);if(this.hasItems){var J=parseInt(A(this.titleElement).css("padding-top"));if(isNaN(J)){J=0}J=J*2;J+=2;var K=(J+A(this.titleElement).height())/2-17/2;if(A.jqx.browser.msie&&A.jqx.browser.version<9){A(this.arrow).css("margin-top","3px")}else{if(parseInt(K)>=0){A(this.arrow).css("margin-top",parseInt(K)+"px")}}}}});if(this.toggleIndicatorSize>16){D=D+this.toggleIndicatorSize-16}if(G.panel){if(D>this.host.width()){var E=D-this.host.width();var C=G.panel.jqxPanel("vScrollBar").css("visibility")!=="hidden"?10:0;E+=C;G.panel.jqxPanel({horizontalScrollBarMax:E})}else{G.panel.jqxPanel({horizontalScrollBarMax:0})}}this.host.find("ul:first").width(D);var F=this.host.width()-30;if(F>0){this.host.find("ul:first").css("min-width",F)}if(G.panel){G.panel.jqxPanel("_arrange")}},_arrowStyle:function(G,B,F){var D=this;if(G.length>0){G.removeClass();var E="";if(B=="hover"){E="-"+B}var H=F?"-expand":"-collapse";var C="jqx-tree-item-arrow"+H+E;G.addClass(D.toThemeProperty(C));if(!this.rtl){var H=!F?"-right":"-down";G.addClass(D.toThemeProperty("jqx-icon-arrow"+H+""))}if(this.rtl){G.addClass(D.toThemeProperty(C+"-rtl"));var H=!F?"-left":"-down";G.addClass(D.toThemeProperty("jqx-icon-arrow"+H+""))}}},_initialize:function(F,E){var C=this;var B=0;this.host.addClass(C.toThemeProperty("jqx-widget"));this.host.addClass(C.toThemeProperty("jqx-widget-content"));this.host.addClass(C.toThemeProperty("jqx-tree"));this._updateDisabledState();var D=A.jqx.browser.msie&&A.jqx.browser.version<8;A.each(this.items,function(){var K=this;$element=A(K.element);var M=null;if(C.checkboxes&&!K.hasItems&&K.checkBoxElement){A(K.checkBoxElement).css("margin-left","0px")}if(!D){if(!K.hasItems){if(!C.rtl){K.element.style.marginLeft=parseInt(C.toggleIndicatorSize)+"px"}else{K.element.style.marginRight=parseInt(C.toggleIndicatorSize)+"px"}var L=A(K.arrow);if(L.length>0){L.remove();K.arrow=null}return true}else{if(!C.rtl){K.element.style.marginLeft="0px"}else{K.element.style.marginRight="0px"}}}else{if(!K.hasItems&&A(K.element).find("ul").length>0){A(K.element).find("ul").remove()}}var L=A(K.arrow);if(L.length>0){L.remove()}M=A('<span style="height: 17px; border: none; background-color: transparent;" id="arrow'+$element[0].id+'"></span>');M.prependTo($element);if(!C.rtl){M.css("float","left")}else{M.css("float","right")}M.css("clear","both");M.width(C.toggleIndicatorSize);C._arrowStyle(M,"",K.isExpanded);var J=parseInt(A(this.titleElement).css("padding-top"));if(isNaN(J)){J=0}J=J*2;J+=2;var I=(J+A(this.titleElement).height())/2-17/2;if(A.jqx.browser.msie&&A.jqx.browser.version<9){M.css("margin-top","3px")}else{if(parseInt(I)>=0){M.css("margin-top",parseInt(I)+"px")}}$element.addClass(C.toThemeProperty("jqx-disableselect"));M.addClass(C.toThemeProperty("jqx-disableselect"));var G="click";var H=C.isTouchDevice();if(H){G=A.jqx.mobile.getTouchEventName("touchend")}C.addHandler(M,G,function(){if(!K.isExpanded){C._expandItem(C,K)}else{C._collapseItem(C,K)}return false});C.addHandler(M,"selectstart",function(){return false});C.addHandler(M,"mouseup",function(){if(!H){return false}});K.hasItems=A(K.element).find("li").length>0;K.arrow=M[0];if(!K.hasItems){M.css("visibility","hidden")}$element.css("float","none")})},_getOffset:function(F){var H=A(window).scrollTop();var B=A(window).scrollLeft();var G=A.jqx.mobile.isSafariMobileBrowser();var D=A(F).offset();var E=D.top;var C=D.left;if(G!=null&&G){return{left:C-B,top:E-H}}else{return A(F).offset()}},_renderHover:function(E,C,D){var B=this;if(!D){var F=A(C.titleElement);B.addHandler(F,"mouseenter",function(){if(!C.disabled&&B.enableHover&&!B.disabled){F.addClass(B.toThemeProperty("jqx-fill-state-hover"));F.addClass(B.toThemeProperty("jqx-tree-item-hover"))}});B.addHandler(F,"mouseleave",function(){if(!C.disabled&&B.enableHover&&!B.disabled){F.removeClass(B.toThemeProperty("jqx-fill-state-hover"));F.removeClass(B.toThemeProperty("jqx-tree-item-hover"))}})}},_updateDisabledState:function(){if(this.disabled){this.host.addClass(this.toThemeProperty("jqx-fill-state-disabled"))}else{this.host.removeClass(this.toThemeProperty("jqx-fill-state-disabled"))}},_addInput:function(){if(this.input==null){var B=this.host.attr("name");if(B){this.host.attr("name","")}this.input=A("<input type='hidden'/>");this.host.append(this.input);this.input.attr("name",B);this._updateInputSelection()}},render:function(){this._updateItemsNavigation();this._render()},_render:function(H,C){if(A.jqx.browser.msie&&A.jqx.browser.version<8){var I=this;A.each(this.items,function(){var K=A(this.element);var L=K.parent();var N=parseInt(this.titleElement.css("margin-left"))+this.titleElement[0].scrollWidth+13;K.css("min-width",N);var M=parseInt(L.css("min-width"));if(isNaN(M)){M=0}var O=K.css("min-width");if(M<parseInt(K.css("min-width"))){L.css("min-width",O)}this.titleElement[0].style.width=null})}var E=1000;var G=[5,5];var I=this;A.data(I.element,"animationHideDelay",I.animationHideDelay);A.data(document.body,"treeel",this);this._initialize();var J=this.isTouchDevice();if(J&&this.toggleMode=="dblclick"){this.toggleMode="click"}if(H==undefined||H==true){A.each(this.items,function(){I._updateItemEvents(I,this)})}if(this.allowDrag&&this._enableDragDrop&&(C==undefined||C==true)){this._enableDragDrop()}this._addInput();if(this.host.jqxPanel){if(this.host.find("#panel"+this.element.id).length>0){this.panel.jqxPanel({touchMode:this.touchMode});this.panel.jqxPanel("refresh");return}this.host.find("ul:first").wrap('<div style="background-color: transparent; overflow: hidden; width: 100%; height: 100%;" id="panel'+this.element.id+'"></div>');var F=this.host.find("div:first");var D="fixed";if(this.height==null||this.height=="auto"){D="verticalwrap"}if(this.width==null||this.width=="auto"){if(D=="fixed"){D="horizontalwrap"}else{D="wrap"}}F.jqxPanel({rtl:this.rtl,theme:this.theme,width:"100%",height:"100%",touchMode:this.touchMode,sizeMode:D});if(A.jqx.browser.msie&&A.jqx.browser.version<8){F.jqxPanel("content").css("left","0px")}F.data({nestedWidget:true});if(this.height==null||(this.height!=null&&this.height.toString().indexOf("%")!=-1)){if(this.isTouchDevice()){this.removeHandler(F,A.jqx.mobile.getTouchEventName("touchend")+".touchScroll touchcancel.touchScroll");this.removeHandler(F,A.jqx.mobile.getTouchEventName("touchmove")+".touchScroll");this.removeHandler(F,A.jqx.mobile.getTouchEventName("touchstart")+".touchScroll")}}var B=A.data(F[0],"jqxPanel").instance;if(B!=null){this.vScrollInstance=B.vScrollInstance;this.hScrollInstance=B.hScrollInstance}this.panelInstance=B;if(A.jqx.browser.msie&&A.jqx.browser.version<8){this.host.attr("hideFocus",true);this.host.find("div").attr("hideFocus",true);this.host.find("ul").attr("hideFocus",true)}F[0].className="";this.panel=F}this._raiseEvent("3",this)},focus:function(){try{this.host.focus()}catch(B){}},_updateItemEvents:function(E,D){var F=this.isTouchDevice();if(F){this.toggleMode=A.jqx.mobile.getTouchEventName("touchend")}var C=A(D.element);if(E.enableRoundedCorners){C.addClass(E.toThemeProperty("jqx-rc-all"))}var B=!F?"mousedown":A.jqx.mobile.getTouchEventName("touchend");if(E.touchMode===true){E.removeHandler(A(D.checkBoxElement),"mousedown")}E.removeHandler(A(D.checkBoxElement),B);E.addHandler(A(D.checkBoxElement),B,function(K){if(!E.disabled){if(!this.treeItem.disabled){this.treeItem.checked=!this.treeItem.checked;E.checkItem(this.treeItem.element,this.treeItem.checked,"tree");if(E.hasThreeStates){E.checkItems(this.treeItem,this.treeItem)}}}return false});var G=A(D.titleElement);E.removeHandler(C);var H=this.allowDrag&&this._enableDragDrop;if(!H){E.removeHandler(G)}else{E.removeHandler(G,"mousedown.item");E.removeHandler(G,"click");E.removeHandler(G,"dblclick");E.removeHandler(G,"mouseenter");E.removeHandler(G,"mouseleave")}E._renderHover(C,D,F);var J=A(D.subtreeElement);if(J.length>0){var I=D.isExpanded?"block":"none";J.css({overflow:"hidden",display:I});J.data("timer",{})}E.addHandler(G,"selectstart",function(K){return false});if(A.jqx.browser.opera){E.addHandler(G,"mousedown.item",function(K){return false})}if(E.toggleMode!="click"){E.addHandler(G,"click",function(K){E.selectItem(D.element,"mouse");if(E.panel!=null){E.panel.jqxPanel({focused:true})}G.focus();E._raiseEvent("9",{element:D.element})})}E.addHandler(G,E.toggleMode,function(K){if(J.length>0){clearTimeout(J.data("timer").hide)}if(E.panel!=null){E.panel.jqxPanel({focused:true})}E.selectItem(D.element,"mouse");if(D.isExpanding==undefined){D.isExpanding=false}if(D.isCollapsing==undefined){D.isCollapsing=false}if(J.length>0){if(!D.isExpanded){if(false==D.isExpanding){D.isExpanding=true;E._expandItem(E,D)}}else{if(false==D.isCollapsing){D.isCollapsing=true;E._collapseItem(E,D,true)}}return false}})},isTouchDevice:function(){if(this._isTouchDevice!=undefined){return this._isTouchDevice}var B=A.jqx.mobile.isTouchDevice();if(this.touchMode==true){B=true}else{if(this.touchMode==false){B=false}}this._isTouchDevice=B;return B},createID:function(){return A.jqx.utilities.createId()},createTree:function(D){if(D==null){return}var B=this;var F=A(D).find("li");var E=0;this.items=new Array();this.itemMapping=new Array();A(D).addClass(B.toThemeProperty("jqx-tree-dropdown-root"));if(this.rtl){A(D).addClass(B.toThemeProperty("jqx-tree-dropdown-root-rtl"))}if(this.rtl||A.jqx.browser.msie&&A.jqx.browser.version<8){this._measureItem=A("<span style='position: relative; visibility: hidden;'></span>");this._measureItem.addClass(this.toThemeProperty("jqx-widget"));this._measureItem.addClass(this.toThemeProperty("jqx-fill-state-normal"));this._measureItem.addClass(this.toThemeProperty("jqx-tree-item"));this._measureItem.addClass(this.toThemeProperty("jqx-item"));A(document.body).append(this._measureItem)}if(A.jqx.browser.msie&&A.jqx.browser.version<8){}for(var C=0;C<F.length;C++){this._createItem(F[C])}if(this.rtl||A.jqx.browser.msie&&A.jqx.browser.version<8){this._measureItem.remove()}this._updateItemsNavigation();this._updateCheckStates()},_updateCheckLayout:function(C){var B=this;if(!this.checkboxes){return}A.each(this.items,function(){if(this.level==C||C==undefined){B._updateCheckItemLayout(this)}})},_updateCheckItemLayout:function(C){if(this.checkboxes){if(A(C.titleElement).css("display")!="none"){var D=A(C.checkBoxElement);var B=A(C.titleElement).outerHeight()/2-1-parseInt(this.checkSize)/2;D.css("margin-top",B);if(!this.rtl){if(A.jqx.browser.msie&&A.jqx.browser.version<8){C.titleElement.css("margin-left",parseInt(this.checkSize)+25)}else{if(C.hasItems){D.css("margin-left",this.toggleIndicatorSize)}}}}}},_updateCheckStates:function(){var B=this;if(B.hasThreeStates){A.each(this.items,function(){B._updateCheckState(this)})}else{A.each(this.items,function(){if(this.checked==null){B.checkItem(this.element,false,"tree")}})}},_updateCheckState:function(D){if(D==null||D==undefined){return}var C=this;var F=0;var G=false;var E=0;var B=A(D.element).find("li");E=B.length;if(D.checked&&E>0){A.each(B,function(H){var J=C.itemMapping["id"+this.id].item;var I=J.element.getAttribute("item-checked");if(I==undefined||I==null||I=="true"||I==true){C.checkItem(J.element,true,"tree")}})}A.each(B,function(H){var I=C.itemMapping["id"+this.id].item;if(I.checked!=false){if(I.checked==null){G=true}F++}});if(E>0){if(F==E){this.checkItem(D.element,true,"tree")}else{if(F>0){this.checkItem(D.element,null,"tree")}else{this.checkItem(D.element,false,"tree")}}}},_updateItemsNavigation:function(){var B=this.host.find("ul:first");var G=A(B).find("li");var F=0;for(var C=0;C<G.length;C++){var E=G[C];if(this.itemMapping["id"+E.id]){var D=this.itemMapping["id"+E.id].item;if(!D){continue}D.prevItem=null;D.nextItem=null;if(C>0){if(this.itemMapping["id"+G[C-1].id]){D.prevItem=this.itemMapping["id"+G[C-1].id].item}}if(C<G.length-1){if(this.itemMapping["id"+G[C+1].id]){D.nextItem=this.itemMapping["id"+G[C+1].id].item}}}}},_applyTheme:function(D,B){var H=this;this.host.removeClass("jqx-tree-"+D);this.host.removeClass("jqx-widget-"+D);this.host.removeClass("jqx-widget-content-"+D);this.host.addClass(H.toThemeProperty("jqx-tree"));this.host.addClass(H.toThemeProperty("jqx-widget"));var F=this.host.find("ul:first");A(F).removeClass(H.toThemeProperty("jqx-tree-dropdown-root-"+D));A(F).addClass(H.toThemeProperty("jqx-tree-dropdown-root"));if(this.rtl){A(F).removeClass(H.toThemeProperty("jqx-tree-dropdown-root-rtl-"+D));A(F).addClass(H.toThemeProperty("jqx-tree-dropdown-root-rtl"))}var E=A(F).find("li");for(var C=0;C<E.length;C++){var G=E[C];A(G).children().each(function(){if(this.tagName=="ul"||this.tagName=="UL"){A(this).removeClass(H.toThemeProperty("jqx-tree-dropdown-"+D));A(this).addClass(H.toThemeProperty("jqx-tree-dropdown"));if(H.rtl){A(this).removeClass(H.toThemeProperty("jqx-tree-dropdown-rtl-"+D));A(this).addClass(H.toThemeProperty("jqx-tree-dropdown-rtl"))}return false}})}A.each(this.items,function(){var I=this;var K=A(I.element);K.removeClass(H.toThemeProperty("jqx-tree-item-li-"+D));K.addClass(H.toThemeProperty("jqx-tree-item-li"));if(this.rtl){K.removeClass(H.toThemeProperty("jqx-tree-item-li-"+D));K.addClass(H.toThemeProperty("jqx-tree-item-li"))}A(I.titleElement).removeClass(H.toThemeProperty("jqx-tree-item-"+D));A(I.titleElement).addClass(H.toThemeProperty("jqx-tree-item"));A(I.titleElement).removeClass("jqx-item-"+D);A(I.titleElement).addClass(H.toThemeProperty("jqx-item"));var J=A(I.arrow);if(J.length>0){H._arrowStyle(J,"",I.isExpanded)}if(I.checkBoxElement){A(I.checkBoxElement).jqxCheckBox({theme:B})}if(H.enableRoundedCorners){K.removeClass("jqx-rc-all-"+D);K.addClass(H.toThemeProperty("jqx-rc-all"))}});if(this.host.jqxPanel){this.panel.jqxPanel({theme:B})}},_refreshMapping:function(N,D){var B=this.host.find("li");var L=new Array();var C=new Array();var G=A.data(document.body,"treeItemsStorage");var J=this;for(var E=0;E<B.length;E++){var F=B[E];var P=A(F);var I=G[F.id];if(I==null){continue}C[C.length]=I;if(N==undefined||N==true){this._updateItemEvents(this,I)}I.level=P.parents("li").length;I.treeInstance=this;var H=null;var O=null;if(I.titleElement[0].className.indexOf("jqx-fill-state-pressed")!=-1){A(I.titleElement).removeClass(J.toThemeProperty("jqx-fill-state-pressed"));A(I.titleElement).removeClass(J.toThemeProperty("jqx-tree-item-selected"))}var M=P.children();M.each(function(){if(this.tagName=="ul"||this.tagName=="UL"){I.subtreeElement=this;A(this).addClass(J.toThemeProperty("jqx-tree-dropdown"));if(J.rtl){A(this).addClass(J.toThemeProperty("jqx-tree-dropdown-rtl"))}return false}});var K=P.parents();K.each(function(){if((this.tagName=="li"||this.tagName=="LI")){O=this.id;H=this;return false}});I.parentElement=H;I.parentId=O;I.hasItems=A(I.element).find("li").length>0;if(I!=null){L[E]={element:F,item:I};L["id"+F.id]=L[E]}}this.itemMapping=L;this.items=C},_createItem:function(Y){if(Y==null||Y==undefined){return}var J=Y.id;if(!J){J=this.createID()}var Ag=Y;var W=A(Y);Ag.id=J;var a=A.data(document.body,"treeItemsStorage");if(a==undefined){a=new Array()}var Al=this.items.length;this.items[Al]=new A.jqx._jqxTree.jqxTreeItem();this.treeElements[J]=this.items[Al];a[Ag.id]=this.items[Al];A.data(document.body,"treeItemsStorage",a);Al=this.items.length;var Af=0;var Ab=this;var Aa=null;W.attr("role","treeitem");W.children().each(function(){if(this.tagName=="ul"||this.tagName=="UL"){Ab.items[Al-1].subtreeElement=this;A(this).addClass(Ab.toThemeProperty("jqx-tree-dropdown"));if(Ab.rtl){A(this).addClass(Ab.toThemeProperty("jqx-tree-dropdown-rtl"));A(this).css("clear","both")}return false}});W.parents().each(function(){if((this.tagName=="li"||this.tagName=="LI")){Af=this.id;Aa=this;return false}});var O=Y.getAttribute("item-expanded");if(O==null||O==undefined||(O!="true"&&O!=true)){O=false}else{O=true}Ag.removeAttribute("item-expanded");var Ah=Y.getAttribute("item-locked");if(Ah==null||Ah==undefined||(Ah!="true"&&Ah!=true)){Ah=false}else{Ah=true}Ag.removeAttribute("item-locked");var K=Y.getAttribute("item-selected");if(K==null||K==undefined||(K!="true"&&K!=true)){K=false}else{K=true}Ag.removeAttribute("item-selected");var k=Y.getAttribute("item-disabled");if(k==null||k==undefined||(k!="true"&&k!=true)){k=false}else{k=true}Ag.removeAttribute("item-disabled");var R=Y.getAttribute("item-checked");if(R==null||R==undefined||(R!="true"&&R!=true)){R=false}else{R=true}var Ac=Y.getAttribute("item-title");if(Ac==null||Ac==undefined||(Ac!="true"&&Ac!=true)){Ac=false}Ag.removeAttribute("item-title");var Ai=Y.getAttribute("item-icon");var P=Y.getAttribute("item-iconsize");var V=Y.getAttribute("item-label");var N=Y.getAttribute("item-value");Ag.removeAttribute("item-icon");Ag.removeAttribute("item-iconsize");Ag.removeAttribute("item-label");Ag.removeAttribute("item-value");var Ae=this.items[Al-1];Ae.id=J;if(Ae.value==undefined){if(this._valueList&&this._valueList[J]){Ae.value=this._valueList[J]}else{Ae.value=N}}Ae.icon=Ai;Ae.iconsize=P;Ae.parentId=Af;Ae.disabled=k;Ae.parentElement=Aa;Ae.element=Y;Ae.locked=Ah;Ae.selected=K;Ae.checked=R;Ae.isExpanded=O;Ae.treeInstance=this;this.itemMapping[Al-1]={element:Ag,item:Ae};this.itemMapping["id"+Ag.id]=this.itemMapping[Al-1];var S=false;var Aj=false;S=false;if(this.rtl){A(Ae.element).css("float","right");A(Ae.element).css("clear","both")}if(!S||!Aj){if(A(Ag.firstChild).length>0){if(Ae.icon){var P=Ae.iconsize;if(!P){P=16}var Ai=A('<img width="'+P+'" height="'+P+'" style="float: left;" class="itemicon" src="'+Ae.icon+'"/>');A(Ag).prepend(Ai);Ai.css("margin-right","4px");if(this.rtl){Ai.css("margin-right","0px");Ai.css("margin-left","4px");Ai.css("float","right")}}var X=Ag.innerHTML.indexOf("<ul");if(X==-1){X=Ag.innerHTML.indexOf("<UL")}if(X==-1){Ae.originalTitle=Ag.innerHTML;Ag.innerHTML='<div style="display: inline-block;">'+Ag.innerHTML+"</div>";Ae.titleElement=A(A(Ag)[0].firstChild)}else{var Ad=Ag.innerHTML.substring(0,X);Ad=A.trim(Ad);Ae.originalTitle=Ad;Ad=A('<div style="display: inline-block;">'+Ad+"</div>");var U=A(Ag).find("ul:first");U.remove();Ag.innerHTML="";A(Ag).prepend(Ad);A(Ag).append(U);Ae.titleElement=Ad;if(this.rtl){Ad.css("float","right");U.css("padding-right","10px")}}if(A.jqx.browser.msie&&A.jqx.browser.version<8){A(A(Ag)[0].firstChild).css("display","inline-block");var T=false;if(this._measureItem.parents().length==0){A(document.body).append(this._measureItem);T=true}this._measureItem.css("min-width","20px");this._measureItem[0].innerHTML=(A(Ae.titleElement).text());var Q=this._measureItem.width();if(Ae.icon){Q+=20}if(A(A(item.titleElement).find("img")).length>0){Q+=20}A(A(Ag)[0].firstChild).css("max-width",Q+"px");if(T){this._measureItem.remove()}}}else{Ae.originalTitle="Item";A(Ag).append(A("<span>Item</span>"));A(Ag.firstChild).wrap("<span/>");Ae.titleElement=A(Ag)[0].firstChild;if(A.jqx.browser.msie&&A.jqx.browser.version<8){A(Ag.firstChild).css("display","inline-block")}}}var Ak=A(Ae.titleElement);var M=this.toThemeProperty("jqx-rc-all");if(this.allowDrag){Ak.addClass("draggable")}if(V==null||V==undefined){V=Ae.titleElement;Ae.label=A.trim(Ak.text())}else{Ae.label=V}A(Ag).addClass(this.toThemeProperty("jqx-tree-item-li"));if(this.rtl){A(Ag).addClass(this.toThemeProperty("jqx-tree-item-li-rtl"))}M+=" "+this.toThemeProperty("jqx-tree-item")+" "+this.toThemeProperty("jqx-item");if(this.rtl){M+=" "+this.toThemeProperty("jqx-tree-item-rtl")}Ak[0].className=Ak[0].className+" "+M;Ae.level=A(Y).parents("li").length;Ae.hasItems=A(Y).find("li").length>0;if(this.rtl&&Ae.parentElement){if(!this.checkboxes){}}if(this.checkboxes){if(this.host.jqxCheckBox){var L=A('<div style="overflow: visible; position: absolute; width: 18px; height: 18px;" tabIndex=0 class="chkbox"/>');L.width(parseInt(this.checkSize));L.height(parseInt(this.checkSize));A(Ag).prepend(L);if(this.rtl){L.css("float","right");L.css("position","static")}L.jqxCheckBox({hasInput:false,checked:Ae.checked,boxSize:this.checkSize,animationShowDelay:0,animationHideDelay:0,disabled:k,theme:this.theme});if(!this.rtl){Ak.css("margin-left",parseInt(this.checkSize)+6)}else{var Am=5;if(Ae.parentElement){L.css("margin-right",Am+5+"px")}else{L.css("margin-right",Am+"px")}}Ae.checkBoxElement=L[0];L[0].treeItem=Ae;var Z=Ak.outerHeight()/2-1-parseInt(this.checkSize)/2;L.css("margin-top",Z);if(A.jqx.browser.msie&&A.jqx.browser.version<8){Ak.css("width","1%");Ak.css("margin-left",parseInt(this.checkSize)+25)}else{if(Ae.hasItems){if(!this.rtl){L.css("margin-left",this.toggleIndicatorSize)}}}}else{throw new Error("jqxTree: Missing reference to jqxcheckbox.js.");return}}else{if(A.jqx.browser.msie&&A.jqx.browser.version<8){Ak.css("width","1%")}}if(k){this.disableItem(Ae.element)}if(K){this.selectItem(Ae.element)}if(A.jqx.browser.msie&&A.jqx.browser.version<8){A(Ag).css("margin","0px");A(Ag).css("padding","0px")}},destroy:function(){this.removeHandler(A(window),"resize.jqxtree"+this.element.id);this.host.removeClass();if(this.isTouchDevice()){this.removeHandler(this.panel,A.jqx.mobile.getTouchEventName("touchend")+".touchScroll touchcancel.touchScroll");this.removeHandler(this.panel,A.jqx.mobile.getTouchEventName("touchmove")+".touchScroll");this.removeHandler(this.panel,A.jqx.mobile.getTouchEventName("touchstart")+".touchScroll")}var C=this;var B=this.isTouchDevice();A.each(this.items,function(){var D=this;var F=A(this.element);var E=!B?"click":A.jqx.mobile.getTouchEventName("touchend");C.removeHandler(A(D.checkBoxElement),E);var G=A(D.titleElement);C.removeHandler(F);var H=C.allowDrag&&C._enableDragDrop;if(!H){C.removeHandler(G)}else{C.removeHandler(G,"mousedown.item");C.removeHandler(G,"click");C.removeHandler(G,"dblclick");C.removeHandler(G,"mouseenter");C.removeHandler(G,"mouseleave")}$arrowSpan=A(D.arrow);if($arrowSpan.length>0){C.removeHandler($arrowSpan,E);C.removeHandler($arrowSpan,"selectstart");C.removeHandler($arrowSpan,"mouseup");if(!B){C.removeHandler($arrowSpan,"mouseenter");C.removeHandler($arrowSpan,"mouseleave")}C.removeHandler(G,"selectstart")}if(A.jqx.browser.opera){C.removeHandler(G,"mousedown.item")}if(C.toggleMode!="click"){C.removeHandler(G,"click")}C.removeHandler(G,C.toggleMode)});if(this.panel){this.panel.jqxPanel("destroy");this.panel=null}this.host.remove()},_raiseEvent:function(F,E){if(E==undefined){E={owner:null}}var B=this.events[F];args=E;args.owner=this;var C=new A.Event(B);C.owner=this;C.args=args;var D=this.host.trigger(C);return D},propertyChangedHandler:function(L,F,H,C){if(this.isInitialized==undefined||this.isInitialized==false){return}if(F=="submitCheckedItems"){L._updateInputSelection()}if(F=="disabled"){L._updateDisabledState()}if(F=="theme"){L._applyTheme(H,C)}if(F=="keyboardNavigation"){L.enableKeyboardNavigation=C}if(F=="width"||F=="height"){L.refresh();L._initialize();L._calculateWidth();if(L.host.jqxPanel){var D="fixed";if(this.height==null||this.height=="auto"){D="verticalwrap"}if(this.width==null||this.width=="auto"){if(D=="fixed"){D="horizontalwrap"}else{D="wrap"}}L.panel.jqxPanel({sizeMode:D})}}if(F=="touchMode"){L._isTouchDevice=null;if(C){L.enableHover=false}L._render()}if(F=="source"||F=="checkboxes"){if(this.source!=null){var G=[];A.each(L.items,function(){if(this.isExpanded){G[G.length]={label:this.label,level:this.level}}});var J=L.loadItems(L.source);if(!L.host.jqxPanel){L.element.innerHTML=J}else{L.panel.jqxPanel("setcontent",J)}var B=L.disabled;var K=L.host.find("ul:first");if(K.length>0){L.createTree(K[0]);L._render()}var E=L;var I=E.animationShowDuration;E.animationShowDuration=0;L.disabled=false;if(G.length>0){A.each(L.items,function(){for(var M=0;M<G.length;M++){if(G[M].label==this.label&&G[M].level==this.level){var N=E.getItem(this.element);E._expandItem(E,N)}}})}L.disabled=B;E.animationShowDuration=I}}if(F=="hasThreeStates"){L._render();L._updateCheckStates()}if(F=="toggleIndicatorSize"){L._updateCheckLayout();L._render()}}})})(jqxBaseFramework);(function(A){A.jqx._jqxTree.jqxTreeItem=function(C,B,D){var E={label:null,id:C,parentId:B,parentElement:null,parentItem:null,disabled:false,selected:false,locked:false,checked:false,level:0,isExpanded:false,hasItems:false,element:null,subtreeElement:null,checkBoxElement:null,titleElement:null,arrow:null,prevItem:null,nextItem:null};return E}})(jqxBaseFramework);