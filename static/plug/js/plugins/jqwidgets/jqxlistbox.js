(function(A){A.jqx.jqxWidget("jqxListBox","",{});A.extend(A.jqx._jqxListBox.prototype,{defineInstance:function(){var B={disabled:false,width:null,height:null,items:new Array(),multiple:false,selectedIndex:-1,selectedIndexes:new Array(),source:null,scrollBarSize:A.jqx.utilities.scrollBarSize,enableHover:true,enableSelection:true,visualItems:new Array(),groups:new Array(),equalItemsWidth:true,itemHeight:-1,visibleItems:new Array(),emptyGroupText:"Group",checkboxes:false,hasThreeStates:false,autoHeight:false,autoItemsHeight:false,roundedcorners:true,touchMode:"auto",displayMember:"",groupMember:"",valueMember:"",searchMember:"",searchMode:"startswithignorecase",incrementalSearch:true,incrementalSearchDelay:1000,incrementalSearchKeyDownDelay:300,allowDrag:false,allowDrop:true,dropAction:"default",touchModeStyle:"auto",keyboardNavigation:true,enableMouseWheel:true,multipleextended:false,selectedValues:new Array(),emptyString:"",rtl:false,rendered:null,renderer:null,dragStart:null,dragEnd:null,focusable:true,ready:null,_checkForHiddenParent:true,autoBind:true,_renderOnDemand:false,filterable:false,filterHeight:27,filterPlaceHolder:"Looking for",filterDelay:100,filterChange:null,aria:{"aria-disabled":{name:"disabled",type:"boolean"}},events:["select","unselect","change","checkChange","dragStart","dragEnd","bindingComplete","itemAdd","itemRemove","itemUpdate"]};A.extend(true,this,B);return B},createInstance:function(D){var C=this;if(A.jqx.utilities.scrollBarSize!=15){C.scrollBarSize=A.jqx.utilities.scrollBarSize}if(C.width==null){C.width=200}if(C.height==null){C.height=200}C.renderListBox();var B=C;A.jqx.utilities.resize(C.host,function(){B._updateSize()},false,C._checkForHiddenParent)},resize:function(C,B){this.width=C;this.height=B;this._updateSize()},render:function(){this.renderListBox();this.refresh()},renderListBox:function(){var I=this;var H=I.element.nodeName.toLowerCase();if(H=="select"||H=="ul"||H=="ol"){I.field=I.element;if(I.field.className){I._className=I.field.className}var F={title:I.field.title};if(I.field.id.length){F.id=I.field.id.replace(/[^\w]/g,"_")+"_jqxListBox"}else{F.id=A.jqx.utilities.createId()+"_jqxListBox"}var J=A("<div></div>",F);if(!I.width){I.width=A(I.field).width()}if(!I.height){I.height=A(I.field).outerHeight()}I.element.style.cssText=I.field.style.cssText;A(I.field).hide().after(J);var L=I.host.data();I.host=J;I.host.data(L);I.element=J[0];I.element.id=I.field.id;I.field.id=F.id;if(I._className){I.host.addClass(I._className);A(I.field).removeClass(I._className)}if(I.field.tabIndex){var K=I.field.tabIndex;I.field.tabIndex=-1;I.element.tabIndex=K}}I.element.innerHTML="";var I=I;var E=I.element.className;E+=" "+I.toThemeProperty("jqx-listbox");E+=" "+I.toThemeProperty("jqx-reset");E+=" "+I.toThemeProperty("jqx-rc-all");E+=" "+I.toThemeProperty("jqx-widget");E+=" "+I.toThemeProperty("jqx-widget-content");I.element.className=E;var M=false;if(I.width!=null&&I.width.toString().indexOf("%")!=-1){I.host.width(I.width);M=true}if(I.height!=null&&I.height.toString().indexOf("%")!=-1){I.host.height(I.height);if(I.host.height()==0){I.host.height(200)}M=true}if(I.width!=null&&I.width.toString().indexOf("px")!=-1){I.host.width(I.width)}else{if(I.width!=undefined&&!isNaN(I.width)){I.element.style.width=parseInt(I.width)+"px"}}if(I.height!=null&&I.height.toString().indexOf("px")!=-1){I.host.height(I.height)}else{if(I.height!=undefined&&!isNaN(I.height)){I.element.style.height=parseInt(I.height)+"px"}}if(I.multiple||I.multipleextended||I.checkboxes){A.jqx.aria(I,"aria-multiselectable",true)}else{A.jqx.aria(I,"aria-multiselectable",false)}var B="<div style='-webkit-appearance: none; background: transparent; outline: none; width:100%; height: 100%; align:left; border: 0px; padding: 0px; margin: 0px; left: 0px; top: 0px; valign:top; position: relative;'><div style='-webkit-appearance: none; border: none; background: transparent; outline: none; width:100%; height: 100%; padding: 0px; margin: 0px; align:left; left: 0px; top: 0px; valign:top; position: relative;'><div id='filter"+I.element.id+"' style='display: none; visibility: inherit; align:left; valign:top; left: 0px; top: 0px; position: absolute;'><input style='position: absolute;'/></div><div id='listBoxContent' style='-webkit-appearance: none; border: none; background: transparent; outline: none; border: none; padding: 0px; overflow: hidden; margin: 0px; align:left; valign:top; left: 0px; top: 0px; position: absolute;'></div><div id='verticalScrollBar"+I.element.id+"' style='visibility: inherit; align:left; valign:top; left: 0px; top: 0px; position: absolute;'></div><div id='horizontalScrollBar"+I.element.id+"' style='visibility: inherit; align:left; valign:top; left: 0px; top: 0px; position: absolute;'></div><div id='bottomRight' style='align:left; valign:top; left: 0px; top: 0px; border: none; position: absolute;'/></div></div>";I.host.attr("role","listbox");I.element.innerHTML=B;if(I._checkForHiddenParent){I._addInput();if(!I.host.attr("tabIndex")){I.host.attr("tabIndex",1)}}I.filter=A(I.element.firstChild.firstChild.firstChild);I.filterInput=A(I.filter[0].firstChild);I.filterInput.attr("placeholder",I.filterPlaceHolder);I.filterInput.addClass(I.toThemeProperty("jqx-widget jqx-listbox-filter-input jqx-input jqx-rc-all"));I.addHandler(I.filterInput,"keyup.textchange",function(O){if(O.keyCode==13){I._search(O)}else{if(I.filterDelay>0){if(I._filterTimer){clearTimeout(I._filterTimer)}I._filterTimer=setTimeout(function(){I._search(O)},I.filterDelay)}}O.stopPropagation()});var C=A(I.element.firstChild.firstChild.firstChild.nextSibling.nextSibling);if(!I.host.jqxButton){throw new Error("jqxListBox: Missing reference to jqxbuttons.js.");return}if(!C.jqxScrollBar){throw new Error("jqxListBox: Missing reference to jqxscrollbar.js.");return}var N=parseInt(I.host.height())/2;if(N==0){N=10}I.vScrollBar=C.jqxScrollBar({_initialLayout:true,vertical:true,rtl:I.rtl,theme:I.theme,touchMode:I.touchMode,largestep:N});var G=A(I.element.firstChild.firstChild.firstChild.nextSibling.nextSibling.nextSibling);I.hScrollBar=G.jqxScrollBar({_initialLayout:true,vertical:false,rtl:I.rtl,touchMode:I.touchMode,theme:I.theme});I.content=A(I.element.firstChild.firstChild.firstChild.nextSibling);I.content[0].id="listBoxContent"+I.element.id;I.bottomRight=A(I.element.firstChild.firstChild.firstChild.nextSibling.nextSibling.nextSibling.nextSibling).addClass(I.toThemeProperty("jqx-listbox-bottomright")).addClass(I.toThemeProperty("jqx-scrollbar-state-normal"));I.bottomRight[0].id="bottomRight"+I.element.id;I.vScrollInstance=A.data(I.vScrollBar[0],"jqxScrollBar").instance;I.hScrollInstance=A.data(I.hScrollBar[0],"jqxScrollBar").instance;if(I.isTouchDevice()){if(!(A.jqx.browser.msie&&A.jqx.browser.version<9)){var D=A("<div class='overlay' unselectable='on' style='z-index: 99; -webkit-appearance: none; border: none; background: black; opacity: 0.01; outline: none; border: none; padding: 0px; overflow: hidden; margin: 0px; align:left; valign:top; left: 0px; top: 0px; position: absolute;'></div>");I.content.parent().append(D);I.overlayContent=I.host.find(".overlay");if(I.filterable){I.overlayContent.css("top","30px")}}}I._updateTouchScrolling();I.host.addClass("jqx-disableselect");if(I.host.jqxDragDrop){jqxListBoxDragDrop()}},_highlight:function(C,D){var B=D.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g,"\\$&");return C.replace(new RegExp("("+B+")","ig"),function(E,F){return"<b>"+F+"</b>"})},_addInput:function(){var B=this.host.attr("name");if(B){this.host.attr("name","")}this.input=A("<input type='hidden'/>");this.host.append(this.input);this.input.attr("name",B)},_updateTouchScrolling:function(){var B=this;if(this.isTouchDevice()){B.enableHover=false;var C=this.overlayContent?this.overlayContent:this.content;this.removeHandler(A(C),A.jqx.mobile.getTouchEventName("touchstart")+".touchScroll");this.removeHandler(A(C),A.jqx.mobile.getTouchEventName("touchmove")+".touchScroll");this.removeHandler(A(C),A.jqx.mobile.getTouchEventName("touchend")+".touchScroll");this.removeHandler(A(C),"touchcancel.touchScroll");A.jqx.mobile.touchScroll(C,B.vScrollInstance.max,function(F,E){if(E!=null&&B.vScrollBar.css("visibility")!="hidden"){var D=B.vScrollInstance.value;B.vScrollInstance.setPosition(E);B._lastScroll=new Date()}if(F!=null&&B.hScrollBar.css("visibility")!="hidden"){var D=B.hScrollInstance.value;B.hScrollInstance.setPosition(F);B._lastScroll=new Date()}},this.element.id,this.hScrollBar,this.vScrollBar);if(B.vScrollBar.css("visibility")!="visible"&&B.hScrollBar.css("visibility")!="visible"){A.jqx.mobile.setTouchScroll(false,this.element.id)}else{A.jqx.mobile.setTouchScroll(true,this.element.id)}this._arrange()}},isTouchDevice:function(){var B=A.jqx.mobile.isTouchDevice();if(this.touchMode==true){if(this.touchDevice){return true}if(A.jqx.browser.msie&&A.jqx.browser.version<9){return false}this.touchDevice=true;B=true;A.jqx.mobile.setMobileSimulator(this.element)}else{if(this.touchMode==false){B=false}}if(B&&this.touchModeStyle!=false){this.scrollBarSize=A.jqx.utilities.touchScrollBarSize}if(B){this.host.addClass(this.toThemeProperty("jqx-touch"))}return B},beginUpdate:function(){this.updatingListBox=true},endUpdate:function(){this.updatingListBox=false;this._addItems();this._renderItems()},beginUpdateLayout:function(){this.updating=true},resumeUpdateLayout:function(){this.updating=false;this.vScrollInstance.value=0;this._render(false)},propertiesChangedHandler:function(C,D,B){if(B.width&&B.height&&Object.keys(B).length==2){C._cachedItemHtml=new Array();C.refresh()}},propertyChangedHandler:function(F,G,D,C){if(this.isInitialized==undefined||this.isInitialized==false){return}if(D==C){return}if(F.batchUpdate&&F.batchUpdate.width&&F.batchUpdate.height&&Object.keys(F.batchUpdate).length==2){return}if(G=="_renderOnDemand"){F._render(false,true);if(F.selectedIndex!=-1){var H=F.selectedIndex;F.selectedIndex=-1;F._stopEvents=true;F.selectIndex(H,false,true);if(F.selectedIndex==-1){F.selectedIndex=H}F._stopEvents=false}}if(G=="filterable"){F.refresh()}if(G=="filterHeight"){F._arrange()}if(G=="filterPlaceHolder"){F.filterInput.attr("placeholder",C)}if(G=="renderer"){F._cachedItemHtml=new Array();F.refresh()}if(G=="itemHeight"){F.refresh()}if(G=="source"||G=="checkboxes"){if(C==null&&D&&D.unbindBindingUpdate){D.unbindBindingUpdate(F.element.id);D.unbindDownloadComplete(F.element.id)}F.clearSelection();F.refresh()}if(G=="scrollBarSize"||G=="equalItemsWidth"){if(C!=D){F._updatescrollbars()}}if(G=="disabled"){F._renderItems();F.vScrollBar.jqxScrollBar({disabled:C});F.hScrollBar.jqxScrollBar({disabled:C})}if(G=="touchMode"||G=="rtl"){F._removeHandlers();F.vScrollBar.jqxScrollBar({touchMode:C});F.hScrollBar.jqxScrollBar({touchMode:C});if(G=="touchMode"){if(!(A.jqx.browser.msie&&A.jqx.browser.version<9)){var B=A("<div class='overlay' unselectable='on' style='z-index: 99; -webkit-appearance: none; border: none; background: black; opacity: 0.01; outline: none; border: none; padding: 0px; overflow: hidden; margin: 0px; align:left; valign:top; left: 0px; top: 0px; position: absolute;'></div>");F.content.parent().append(B);F.overlayContent=F.host.find(".overlay")}}if(F.filterable&&F.filterInput){if(G=="rtl"&&C){F.filterInput.addClass(F.toThemeProperty("jqx-rtl"))}else{if(G=="rtl"&&!C){F.filterInput.removeClass(F.toThemeProperty("jqx-rtl"))}}F._arrange()}F._updateTouchScrolling();F._addHandlers();F._render(false)}if(!this.updating){if(G=="width"||G=="height"){F._updateSize()}}if(G=="theme"){if(D!=C){F.hScrollBar.jqxScrollBar({theme:F.theme});F.vScrollBar.jqxScrollBar({theme:F.theme});F.host.removeClass();F.host.addClass(F.toThemeProperty("jqx-listbox"));F.host.addClass(F.toThemeProperty("jqx-widget"));F.host.addClass(F.toThemeProperty("jqx-widget-content"));F.host.addClass(F.toThemeProperty("jqx-reset"));F.host.addClass(F.toThemeProperty("jqx-rc-all"));F.refresh()}}if(G=="selectedIndex"){F.clearSelection();F.selectIndex(C,true)}if(G=="displayMember"||G=="valueMember"){if(D!=C){var E=F.selectedIndex;F.refresh();F.selectedIndex=E;F.selectedIndexes[E]=E}F._renderItems()}if(G=="autoHeight"){if(D!=C){F._render(false)}else{F._updatescrollbars();F._renderItems()}}if(F._checkForHiddenParent&&A.jqx.isHidden(F.host)){A.jqx.utilities.resize(this.host,function(){F._updateSize()},false,F._checkForHiddenParent)}},loadFromSelect:function(B){if(B==null){return}var F="#"+B;var C=A(F);if(C.length>0){var E=A.jqx.parseSourceTag(C[0]);var G=E.items;var D=E.index;this.source=G;this.fromSelect=true;this.clearSelection();this.selectedIndex=D;this.selectedIndexes[this.selectedIndex]=this.selectedIndex;this.refresh()}},invalidate:function(){this._cachedItemHtml=[];this._renderItems();this.virtualSize=null;this._updateSize()},refresh:function(D){var C=this;if(this.vScrollBar==undefined){return}this._cachedItemHtml=[];this.visibleItems=new Array();var B=function(E){if(E==true){if(C.selectedIndex!=-1){var F=C.selectedIndex;C.selectedIndex=-1;C._stopEvents=true;C.selectIndex(F,false,true);if(C.selectedIndex==-1){C.selectedIndex=F}C._stopEvents=false}}};if(this.itemswrapper!=null){this.itemswrapper.remove();this.itemswrapper=null}if(A.jqx.dataAdapter&&this.source!=null&&this.source._source){this.databind(this.source,D);B(D);return}if(this.autoBind||(!this.autoBind&&!D)){if(this.field){this.loadSelectTag()}this.items=this.loadItems(this.source)}this._render(false,D==true);B(D);this._raiseEvent("6")},loadSelectTag:function(){var B=A.jqx.parseSourceTag(this.field);this.source=B.items;if(this.selectedIndex==-1){this.selectedIndex=B.index}},_render:function(C,B){if(this._renderOnDemand){this.visibleItems=new Array();this.renderedVisibleItems=new Array();this._renderItems();return}this._addItems();this._renderItems();this.vScrollInstance.setPosition(0);this._cachedItemHtml=new Array();if(C==undefined||C){if(this.items!=undefined&&this.items!=null){if(this.selectedIndex>=0&&this.selectedIndex<this.items.length){this.selectIndex(this.selectedIndex,true,true,true)}}}if(this.allowDrag&&this._enableDragDrop){this._enableDragDrop();if(this.isTouchDevice()){this._removeHandlers();if(this.overlayContent){this.overlayContent.remove();this.overlayContent=null}this._updateTouchScrolling();this._addHandlers();return}}this._updateTouchScrolling();if(this.rendered){this.rendered()}if(this.ready){this.ready()}},_hitTest:function(E,F){if(this.filterable){F-=this.filterHeight;if(F<0){F=0}}var C=parseInt(this.vScrollInstance.value);var D=this._searchFirstVisibleIndex(F+C,this.renderedVisibleItems);if(this.renderedVisibleItems[D]!=undefined&&this.renderedVisibleItems[D].isGroup){return null}if(this.renderedVisibleItems.length>0){var B=this.renderedVisibleItems[this.renderedVisibleItems.length-1];if(B.height+B.top<F+C){return null}}D=this._searchFirstVisibleIndex(F+C);return this.visibleItems[D];return null},_searchFirstVisibleIndex:function(C,F){if(C==undefined){C=parseInt(this.vScrollInstance.value)}var E=0;if(F==undefined||F==null){F=this.visibleItems}var D=F.length;while(E<=D){mid=parseInt((E+D)/2);var B=F[mid];if(B==undefined){break}if(B.initialTop>C&&B.initialTop+B.height>C){D=mid-1}else{if(B.initialTop<C&&B.initialTop+B.height<=C){E=mid+1}else{return mid;break}}}return 0},_renderItems:function(){if(this.items==undefined||this.items.length==0){this.visibleItems=new Array();return}if(this.updatingListBox==true){return}var Y=this.isTouchDevice();var Ae=this.vScrollInstance;var At=this.hScrollInstance;var AC=parseInt(Ae.value);var AB=parseInt(At.value);if(this.rtl){if(this.hScrollBar[0].style.visibility!="hidden"){AB=At.max-AB}}var Aa=this.items.length;var y=this.host.width();var V=parseInt(this.content[0].style.width);var Az=V+parseInt(At.max);var Am=parseInt(this.vScrollBar[0].style.width)+2;if(this.vScrollBar[0].style.visibility=="hidden"){Am=0}if(this.hScrollBar[0].style.visibility!="visible"){Az=V}var Ax=this._getVirtualItemsCount();var a=new Array();var Ad=0;var Ag=parseInt(this.element.style.height)+2;if(this.element.style.height.indexOf("%")!=-1){Ag=this.host.outerHeight()}if(isNaN(Ag)){Ag=0}var Ap=0;var Ak=0;var T=0;if(Ae.value==0||this.visibleItems.length==0){for(var Aj=0;Aj<this.items.length;Aj++){var Ai=this.items[Aj];if(Ai.visible){Ai.top=-AC;Ai.initialTop=-AC;if(!Ai.isGroup&&Ai.visible){this.visibleItems[Ak++]=Ai;Ai.visibleIndex=Ak-1}this.renderedVisibleItems[T++]=Ai;Ai.left=-AB;var AA=Ai.top+Ai.height;if(AA>=0&&Ai.top-Ai.height<=Ag){a[Ad++]={index:Aj,item:Ai}}AC-=Ai.height;AC--}}}var Ay=AC>0?this._searchFirstVisibleIndex(this.vScrollInstance.value,this.renderedVisibleItems):0;var Z=0;Ad=0;var Ah=this.vScrollInstance.value;var U=0;while(Z<100+Ag){var Ai=this.renderedVisibleItems[Ay];if(Ai==undefined){break}if(Ai.visible){Ai.left=-AB;var AA=Ai.top+Ai.height-Ah;if(AA>=0&&Ai.initialTop-Ah-Ai.height<=2*Ag){a[Ad++]={index:Ay,item:Ai}}}Ay++;if(Ai.visible){Z+=Ai.initialTop-Ah+Ai.height-Z}U++;if(U>this.items.length-1){break}}if(this._renderOnDemand){return}var Aw=this.toThemeProperty("jqx-listitem-state-normal")+" "+this.toThemeProperty("jqx-item");var Au=this.toThemeProperty("jqx-listitem-state-group");var S=this.toThemeProperty("jqx-listitem-state-disabled")+" "+this.toThemeProperty("jqx-fill-state-disabled");var Ab=0;var Av=this;for(var Aj=0;Aj<this.visualItems.length;Aj++){var Af=this.visualItems[Aj];var X=function(){var C=Af[0].firstChild;if(Av.checkboxes){C=Af[0].lastChild}if(C!=null){C.style.visibility="hidden";C.className=""}if(Av.checkboxes){var B=Af.find(".chkbox");B.css({visibility:"hidden"})}};if(Aj<a.length){var Ai=a[Aj].item;if(Ai.initialTop-Ah>=Ag){X();continue}var Ac=A(Af[0].firstChild);if(this.checkboxes){Ac=A(Af[0].lastChild)}if(Ac.length==0){continue}if(Ac[0]==null){continue}Ac[0].className="";Ac[0].style.display="block";Ac[0].style.visibility="inherit";var Al="";if(!Ai.isGroup&&!this.selectedIndexes[Ai.index]>=0){Al=Aw}else{Al=Au}if(Ai.disabled||this.disabled){Al+=" "+S}if(this.roundedcorners){Al+=" "+this.toThemeProperty("jqx-rc-all")}if(Y){Al+=" "+this.toThemeProperty("jqx-listitem-state-normal-touch")}Ac[0].className=Al;if(this.renderer){if(!Ai.key){Ai.key=this.generatekey()}if(!this._cachedItemHtml){this._cachedItemHtml=new Array()}if(this._cachedItemHtml[Ai.key]){if(Ac[0].innerHTML!=this._cachedItemHtml[Ai.key]){Ac[0].innerHTML=this._cachedItemHtml[Ai.key]}}else{var Ao=this.renderer(Ai.index,Ai.label,Ai.value);Ac[0].innerHTML=Ao;this._cachedItemHtml[Ai.key]=Ac[0].innerHTML}}else{if(this.itemHeight!==-1){var As=2+2*parseInt(Ac.css("padding-top"));Ac[0].style.lineHeight=(Ai.height-As)+"px";Ac.css("vertical-align","middle")}if(Ai.html!=null&&Ai.html.toString().length>0){Ac[0].innerHTML=Ai.html}else{if(Ai.label!=null||Ai.value!=null){if(Ai.label!=null){if(Ac[0].innerHTML!==Ai.label){Ac[0].innerHTML=Ai.label}if(A.trim(Ai.label)==""){Ac[0].innerHTML=this.emptyString;if(this.emptyString==""){Ac[0].style.height=(Ai.height-8)+"px"}}if(!this.incrementalSearch&&!Ai.disabled){if(this.searchString!=undefined&&this.searchString!=""){Ac[0].innerHTML=this._highlight(Ai.label.toString(),this.searchString)}}}else{if(Ai.label===null){Ac[0].innerHTML=this.emptyString;if(this.emptyString==""){Ac[0].style.height=(Ai.height-8)+"px"}}else{if(Ac[0].innerHTML!==Ai.value){Ac[0].innerHTML=Ai.value}else{if(Ai.label==""){Ac[0].innerHTML=" "}}}}}else{if(Ai.label==""||Ai.label==null){Ac[0].innerHTML="";Ac[0].style.height=(Ai.height-8)+"px"}}}}Af[0].style.left=Ai.left+"px";Af[0].style.top=Ai.initialTop-Ah+"px";Ai.element=Ac[0];if(Ai.title){Ac[0].title=Ai.title}if(this.equalItemsWidth&&!Ai.isGroup){if(Ap==0){var AD=parseInt(Az);var An=parseInt(Ac.outerWidth())-parseInt(Ac.width());AD-=An;var W=1;if(W!=null){W=parseInt(W)}else{W=0}AD-=2*W;Ap=AD;if(this.checkboxes&&this.hScrollBar[0].style.visibility=="hidden"){Ap-=18}}if(V>this.virtualSize.width){Ac[0].style.width=Ap+"px";Ai.width=Ap}else{Ac[0].style.width=-4+this.virtualSize.width+"px";Ai.width=this.virtualSize.width-4}}else{if(Ac.width()<this.host.width()){Ac.width(this.host.width()-2)}}if(this.rtl){Ac[0].style.textAlign="right"}if(this.autoItemsHeight){Ac[0].style.whiteSpace="pre-line";Ac.width(Ap);Ai.width=Ap}Ab=0;if(this.checkboxes&&!Ai.isGroup){if(Ab==0){Ab=(Ai.height-16)/2;Ab++}var R=A(Af.children()[0]);R[0].item=Ai;if(!this.rtl){if(Ac[0].style.left!="18px"){Ac[0].style.left="18px"}}else{if(Ac[0].style.left!="0px"){Ac[0].style.left="0px"}}if(this.rtl){R.css("left",8+Ai.width+"px")}R[0].style.top=Ab+"px";R[0].style.display="block";R[0].style.visibility="inherit";var Aq=Ai.checked;var Ar=Ai.checked?" "+this.toThemeProperty("jqx-checkbox-check-checked"):"";if(R[0].firstChild&&R[0].firstChild.firstChild&&R[0].firstChild.firstChild.firstChild){if(R[0].firstChild.firstChild){if(Aq){R[0].firstChild.firstChild.firstChild.className=Ar}else{if(Aq===false){R[0].firstChild.firstChild.firstChild.className=""}else{if(Aq===null){R[0].firstChild.firstChild.firstChild.className=this.toThemeProperty("jqx-checkbox-check-indeterminate")}}}}}if(A.jqx.ariaEnabled){if(Aq){Af[0].setAttribute("aria-selected",true)}else{Af[0].removeAttribute("aria-selected")}}}else{if(this.checkboxes){var R=A(Af.children()[0]);R.css({display:"none",visibility:"inherit"})}}if(!Ai.disabled&&((!this.filterable&&this.selectedIndexes[Ai.visibleIndex]>=0)||(Ai.selected&&this.filterable))){Ac.addClass(this.toThemeProperty("jqx-listitem-state-selected"));Ac.addClass(this.toThemeProperty("jqx-fill-state-pressed"));if(A.jqx.ariaEnabled){Af[0].setAttribute("aria-selected",true);this._activeElement=Af[0]}}else{if(!this.checkboxes){if(A.jqx.ariaEnabled){Af[0].removeAttribute("aria-selected")}}}}else{X()}}},generatekey:function(){var B=function(){return(((1+Math.random())*65536)|0).toString(16).substring(1)};return(B()+B()+"-"+B()+"-"+B()+"-"+B()+"-"+B()+B()+B())},_calculateVirtualSize:function(N){if(this._renderOnDemand){return}var D=0;var L=2;var S=0;var M=document.createElement("span");if(this.equalItemsWidth){A(M).css("float","left")}M.style.whiteSpace="pre";var J=0;var K=undefined===N?this.host.outerHeight():N+2;document.body.appendChild(M);var U=this.items.length;var H=this.host.width();if(this.autoItemsHeight){H-=10;if(this.vScrollBar.css("visibility")!="hidden"){H-=20}}if(this.autoItemsHeight||this.renderer||this.groups.length>=1||(U>0&&this.items[0].html!=null&&this.items[0].html!="")){for(var S=0;S<U;S++){var C=this.items[S];if(C.isGroup&&(C.label==""&&C.html=="")){continue}if(!C.visible){continue}var T="";if(!C.isGroup){T+=this.toThemeProperty("jqx-widget jqx-listitem-state-normal jqx-rc-all")}else{T+=this.toThemeProperty("jqx-listitem-state-group jqx-rc-all")}T+=" "+this.toThemeProperty("jqx-fill-state-normal");if(this.isTouchDevice()){T+=" "+this.toThemeProperty("jqx-touch")}M.className=T;if(this.autoItemsHeight){M.style.whiteSpace="pre-line";var P=this.checkboxes?-20:0;M.style.width=(P+H)+"px"}if(this.renderer){var I=this.renderer(C.index,C.label,C.value);M.innerHTML=I}else{if(C.html!=null&&C.html.toString().length>0){M.innerHTML=C.html}else{if(C.label!=null||C.value!=null){if(C.label!=null){M.innerHTML=C.label;if(C.label==""){M.innerHTML="Empty"}}else{M.innerHTML=C.value}}}}var B=M.offsetHeight;var F=M.offsetWidth;if(this.itemHeight>-1){B=this.itemHeight}C.height=B;C.width=F;B++;L+=B;D=Math.max(D,F);if(L<=K){J++}}}else{var L=0;var O=0;var Q="";var G=0;var R=0;var E=-1;for(var S=0;S<U;S++){var C=this.items[S];if(C.isGroup&&(C.label==""&&C.html=="")){continue}if(!C.visible){continue}E++;var T="";if(E==0){T+=this.toThemeProperty("jqx-listitem-state-normal jqx-rc-all");T+=" "+this.toThemeProperty("jqx-fill-state-normal");T+=" "+this.toThemeProperty("jqx-widget");T+=" "+this.toThemeProperty("jqx-listbox");T+=" "+this.toThemeProperty("jqx-widget-content");if(this.isTouchDevice()){T+=" "+this.toThemeProperty("jqx-touch");T+=" "+this.toThemeProperty("jqx-listitem-state-normal-touch")}M.className=T;if(this.autoItemsHeight){M.style.whiteSpace="pre-line";var P=this.checkboxes?-20:0;M.style.width=(P+H)+"px"}if(C.html==null||(C.label==""||C.label==null)){M.innerHTML="Item"}else{if(C.html!=null&&C.html.toString().length>0){M.innerHTML=C.html}else{if(C.label!=null||C.value!=null){if(C.label!=null){if(C.label.toString().match(new RegExp("\\w"))!=null||C.label.toString().match(new RegExp("\\d"))!=null){M.innerHTML=C.label}else{M.innerHTML="Item"}}else{M.innerHTML=C.value}}}}var B=1+M.offsetHeight;if(this.itemHeight>-1){B=this.itemHeight}O=B}if(G!=undefined){R=G}if(C.html!=null&&C.html.toString().length>0){G=Math.max(G,C.html.toString().length);if(R!=G){Q=C.html}}else{if(C.label!=null){G=Math.max(G,C.label.length);if(R!=G){Q=C.label}}else{if(C.value!=null){G=Math.max(G,C.value.length);if(R!=G){Q=C.value}}}}C.height=O;L+=O;L++;if(L<=K){J++}}M.innerHTML=Q;D=M.offsetWidth}L+=2;if(J<10){J=10}if(this.filterable){L+=this.filterHeight}M.parentNode.removeChild(M);return{width:D,height:L,itemsPerPage:J}},_getVirtualItemsCount:function(){if(this.virtualItemsCount==0){var B=parseInt(this.host.height())/5;if(B>this.items.length){B=this.items.length}return B}else{return this.virtualItemsCount}},_addItems:function(B){if(this._renderOnDemand){return}var F=this;if(F.updatingListBox==true){return}if(F.items==undefined||F.items.length==0){F.virtualSize={width:0,height:0,itemsPerPage:0};F._updatescrollbars();F.renderedVisibleItems=new Array();if(F.itemswrapper){F.itemswrapper.children().remove()}return}var L=F.host.height();if(B==false){var R=F._calculateVirtualSize(L);var U=R.itemsPerPage*2;if(F.autoHeight){U=F.items.length}F.virtualItemsCount=Math.min(U,F.items.length);var E=R.width;F.virtualSize=R;F._updatescrollbars();return}var N=this;var P=0;F.visibleItems=new Array();F.renderedVisibleItems=new Array();F._removeHandlers();if(F.allowDrag&&F._enableDragDrop){F.itemswrapper=null}if(F.itemswrapper==null){F.content[0].innerHTML="";F.itemswrapper=A('<div style="outline: 0 none; overflow:hidden; width:100%; position: relative;"></div>');F.itemswrapper[0].style.height=(2*L)+"px";F.content[0].appendChild(F.itemswrapper[0])}var R=F._calculateVirtualSize(L);var U=R.itemsPerPage*2;if(F.autoHeight){U=F.items.length}F.virtualItemsCount=Math.min(U,F.items.length);var F=this;var E=R.width;F.virtualSize=R;var S=Math.max(F.host.width(),17+R.width);F.itemswrapper[0].style.width=S+"px";var W=0;var M="";var T=A.jqx.browser.msie&&A.jqx.browser.version<9;var C=T?' unselectable="on"':"";for(var J=W;J<F.virtualItemsCount;J++){var I=F.items[J];var D="listitem"+J+F.element.id;M+="<div"+C+" role='option' id='"+D+"' class='jqx-listitem-element'>";if(F.checkboxes){M+='<div style="background-color: transparent; padding: 0; margin: 0; position: absolute; float: left; width: 16px; height: 16px;" class="chkbox">';var O='<div class="'+F.toThemeProperty("jqx-checkbox-default")+" "+F.toThemeProperty("jqx-fill-state-normal")+" "+F.toThemeProperty("jqx-rc-all")+'"><div style="cursor: pointer; width: 13px; height: 13px;">';var G=I.checked?" "+F.toThemeProperty("jqx-checkbox-check-checked"):"";O+='<span style="width: 13px; height: 13px;" class="checkBoxCheck'+G+'"></span>';O+="</div></div>";M+=O;M+="</div>"}M+="<span"+C+" style='white-space: pre; -ms-touch-action: none;'></span></div>"}if(N.WinJS){F.itemswrapper.html(M)}else{F.itemswrapper[0].innerHTML=M}var V=F.itemswrapper.children();for(var J=W;J<F.virtualItemsCount;J++){var I=F.items[J];var H=A(V[J]);if(F.allowDrag&&F._enableDragDrop){H.addClass("draggable")}if(F.checkboxes){var K=A(H.children()[0]);H.css("float","left");var Q=A(H[0].firstChild);Q.css("float","left")}H[0].style.height=I.height+"px";H[0].style.top=P+"px";P+=I.height+1;F.visualItems[J]=H}F._addHandlers();F._updatescrollbars();if(F.autoItemsHeight){var R=F._calculateVirtualSize(L);var U=R.itemsPerPage*2;if(F.autoHeight){U=F.items.length}F.virtualItemsCount=Math.min(U,F.items.length);var F=this;var E=R.width;F.virtualSize=R;F._updatescrollbars()}if(A.jqx.browser.msie&&A.jqx.browser.version<8){F.host.attr("hideFocus",true);F.host.find("div").attr("hideFocus",true)}},_updatescrollbars:function(){var D=this;if(!D.virtualSize){return}var G=D.virtualSize.height;var C=D.virtualSize.width;var M=D.vScrollInstance;var B=D.hScrollInstance;D._arrange(false);var I=false;var H=D.host.outerWidth();var J=D.host.outerHeight();var K=0;if(C>H){K=D.hScrollBar.outerHeight()+2}if(G+K>J){var O=M.max;M.max=2+parseInt(G)+K-parseInt(J-2);if(D.vScrollBar[0].style.visibility!="inherit"){D.vScrollBar[0].style.visibility="inherit";I=true}if(O!=M.max){M._arrange()}}else{if(D.vScrollBar[0].style.visibility!="hidden"){D.vScrollBar[0].style.visibility="hidden";I=true;M.setPosition(0)}}var E=0;if(D.vScrollBar[0].style.visibility!="hidden"){E=D.scrollBarSize+6}var N=D.checkboxes?20:0;if(D.autoItemsHeight){D.hScrollBar[0].style.visibility="hidden"}else{if(C>=H-E-N){var F=B.max;if(D.vScrollBar[0].style.visibility=="inherit"){B.max=N+E+parseInt(C)-D.host.width()+4}else{B.max=N+parseInt(C)-D.host.width()+6}if(D.hScrollBar[0].style.visibility!="inherit"){D.hScrollBar[0].style.visibility="inherit";I=true}if(F!=B.max){B._arrange()}if(D.vScrollBar[0].style.visibility=="inherit"){M.max=2+parseInt(G)+D.hScrollBar.outerHeight()+2-parseInt(D.host.height())}}else{if(D.hScrollBar[0].style.visibility!="hidden"){D.hScrollBar[0].style.visibility="hidden";I=true}}}B.setPosition(0);if(I){D._arrange()}if(D.itemswrapper){D.itemswrapper[0].style.width=Math.max(0,Math.max(H-2,17+C))+"px";D.itemswrapper[0].style.height=Math.max(0,2*J)+"px"}var L=D.isTouchDevice();if(L){if(D.vScrollBar.css("visibility")!="visible"&&D.hScrollBar.css("visibility")!="visible"){A.jqx.mobile.setTouchScroll(false,D.element.id)}else{A.jqx.mobile.setTouchScroll(true,D.element.id)}}},clear:function(){this.source=null;this.clearSelection();this.refresh()},clearSelection:function(B){for(var C=0;C<this.selectedIndexes.length;C++){if(this.selectedIndexes[C]&&this.selectedIndexes[C]!=-1){this._raiseEvent("1",{index:C,type:"api",item:this.getVisibleItem(C),originalEvent:null})}this.selectedIndexes[C]=-1}this.selectedIndex=-1;this.selectedValue=null;this.selectedValues=new Array();if(B!=false){this._renderItems()}},unselectIndex:function(F,C){if(isNaN(F)){return}this.selectedIndexes[F]=-1;var B=false;for(var D=0;D<this.selectedIndexes.length;D++){var E=this.selectedIndexes[D];if(E!=-1&&E!=undefined){B=true}}if(!B){this.selectedValue=null;this.selectedIndex=-1;var G=this.getVisibleItem(F);if(G){if(this.selectedValues[G.value]){this.selectedValues[G.value]=null}}}if(C==undefined||C==true){this._renderItems();this._raiseEvent("1",{index:F,type:"api",item:this.getVisibleItem(F),originalEvent:null})}this._updateInputSelection();this._raiseEvent("2",{index:F,type:"api",item:this.getItem(F)})},getInfo:function(){var B=this;var E=this.getItems();var D=this.getVisibleItems();var C=function(){var F=B.vScrollInstance.value;if(B.filterable){F-=B.filterHeight}var J=new Array();for(var H=0;H<D.length;H++){var K=D[H];if(K){var L=K.initialTop;var I=K.height;var G=true;if(L+I-F<0||L-F>=B.host.height()){G=false}if(G){J.push(K)}}}return J}();return{items:E,visibleItems:D,viewItems:C}},getItem:function(D){if(D==-1||isNaN(D)||typeof(D)==="string"){if(D===-1){return null}return this.getItemByValue(D)}var C=null;var B=A.each(this.items,function(){if(this.index==D){C=this;return false}});return C},getVisibleItem:function(B){if(B==-1||isNaN(B)||typeof(B)==="string"){if(B===-1){return null}return this.getItemByValue(B)}return this.visibleItems[B]},getVisibleItems:function(){return this.visibleItems},checkIndex:function(D,E,C){if(!this.checkboxes){return}if(isNaN(D)){return}if(D<0||D>=this.visibleItems.length){return}if(this.visibleItems[D]!=null&&this.visibleItems[D].disabled){return}if(this.disabled){return}var B=this.getItem(D);if(this.groups.length>0||this.filterable){var B=this.getVisibleItem(D)}if(B!=null){var F=A(B.checkBoxElement);B.checked=true;if(E==undefined||E==true){this._updateCheckedItems()}}if(C==undefined||C==true){this._raiseEvent(3,{label:B.label,value:B.value,checked:true,item:B})}},getCheckedItems:function(){if(!this.checkboxes){return null}var B=new Array();if(this.items==undefined){return}A.each(this.items,function(){if(this.checked){B[B.length]=this}});return B},checkAll:function(B){if(!this.checkboxes){return}if(this.disabled){return}var C=this;A.each(this.items,function(){var D=this;if(B!==false&&D.checked!==true){C._raiseEvent(3,{label:D.label,value:D.value,checked:true,item:D})}this.checked=true});this._updateCheckedItems()},uncheckAll:function(B){if(!this.checkboxes){return}if(this.disabled){return}var C=this;A.each(this.items,function(){var D=this;if(B!==false&&D.checked!==false){this.checked=false;C._raiseEvent(3,{label:D.label,value:D.value,checked:false,item:D})}this.checked=false});this._updateCheckedItems()},uncheckIndex:function(D,E,C){if(!this.checkboxes){return}if(isNaN(D)){return}if(D<0||D>=this.visibleItems.length){return}if(this.visibleItems[D]!=null&&this.visibleItems[D].disabled){return}if(this.disabled){return}var B=this.getItem(D);if(this.groups.length>0||this.filterable){var B=this.getVisibleItem(D)}if(B!=null){var F=A(B.checkBoxElement);B.checked=false;if(E==undefined||E==true){this._updateCheckedItems()}}if(C==undefined||C==true){this._raiseEvent(3,{label:B.label,value:B.value,checked:false,item:B})}},indeterminateIndex:function(D,E,C){if(!this.checkboxes){return}if(isNaN(D)){return}if(D<0||D>=this.visibleItems.length){return}if(this.visibleItems[D]!=null&&this.visibleItems[D].disabled){return}if(this.disabled){return}var B=this.getItem(D);if(this.groups.length>0||this.filterable){var B=this.getVisibleItem(D)}if(B!=null){var F=A(B.checkBoxElement);B.checked=null;if(E==undefined||E==true){this._updateCheckedItems()}}if(C==undefined||C==true){this._raiseEvent(3,{checked:null})}},getSelectedIndex:function(){return this.selectedIndex},getSelectedItems:function(){var D=this.getVisibleItems();var C=this.selectedIndexes;var B=[];for(var E in C){if(C[E]!=-1){B[B.length]=D[E]}}return B},getSelectedItem:function(){var B=this.getSelectedItems();if(B&&B.length>0){return B[0]}return null},_updateCheckedItems:function(){var C=this.selectedIndex;this.clearSelection(false);var D=this.getCheckedItems();this.selectedIndex=C;this._renderItems();var B=A.data(this.element,"hoveredItem");if(B!=null){A(B).addClass(this.toThemeProperty("jqx-listitem-state-hover"));A(B).addClass(this.toThemeProperty("jqx-fill-state-hover"))}this._updateInputSelection()},getItemByValue:function(B){if(this.visibleItems==null){return}if(B&&B.value){B=B.value}if(this.itemsByValue){return this.itemsByValue[A.trim(B).split(" ").join("?")]}var C=this.visibleItems;for(var D=0;D<C.length;D++){if(C[D].value==B){return C[D];break}}},checkItem:function(C){if(C!=null){var B=this._getItemByParam(C);return this.checkIndex(B.visibleIndex,true)}return false},uncheckItem:function(C){if(C!=null){var B=this._getItemByParam(C);return this.uncheckIndex(B.visibleIndex,true)}return false},indeterminateItem:function(C){if(C!=null){var B=this._getItemByParam(C);return this.indeterminateIndex(B.visibleIndex,true)}return false},val:function(D){if(!this.input){return}var B=function(F){for(var E in F){if(F.hasOwnProperty(E)){return false}}if(typeof D=="number"){return false}if(typeof D=="date"){return false}if(typeof D=="boolean"){return false}if(typeof D=="string"){return false}return true};if(B(D)||arguments.length==0){return this.input.val()}var C=this.getItemByValue(D);if(C!=null){this.selectItem(C)}if(this.input){return this.input.val()}},selectItem:function(C){if(C!=null){if(C.index==undefined){var B=this.getItemByValue(C);if(B){C=B}}return this.selectIndex(C.visibleIndex,true)}else{this.clearSelection()}return false},unselectItem:function(C){if(C!=null){if(C.index==undefined){var B=this.getItemByValue(C);if(B){C=B}}return this.unselectIndex(C.visibleIndex,true)}return false},selectIndex:function(F,B,N,Q,L,M){if(isNaN(F)){return}var E=this.selectedIndex;if(this.filterable){this.selectedIndex=-1}if(F<-1||F>=this.visibleItems.length){return}if(this.visibleItems[F]!=null&&this.visibleItems[F].disabled){return}if(this.disabled){return}if(!this.multiple&&!this.multipleextended&&this.selectedIndex==F&&!Q&&!this.checkboxes){if(this.visibleItems&&this.items&&this.visibleItems.length!=this.items.length){H=this.getVisibleItem(F);if(H){this.selectedValue=H.value;this.selectedValues[H.value]=H.value}}return}if(this.checkboxes){this._updateCheckedItems();var D=E;if(this.selectedIndex==F&&!this.multiple){D=-1}if(L==undefined){L="none"}var H=this.getItem(F);var C=this.getItem(D);if(this.visibleItems&&this.items&&this.visibleItems.length!=this.items.length){H=this.getVisibleItem(F);C=this.getVisibleItem(D)}this._raiseEvent("1",{index:D,type:L,item:C,originalEvent:M});this.selectedIndex=F;this.selectedIndexes[D]=-1;this.selectedIndexes[F]=F;if(H){this.selectedValue=H.value;this.selectedValues[H.value]=H.value}this._raiseEvent("0",{index:F,type:L,item:H,originalEvent:M});this._renderItems();return}this.focused=true;var J=false;if(this.selectedIndex!=F){J=true}var D=E;if(this.selectedIndex==F&&!this.multiple){D=-1}if(L==undefined){L="none"}var H=this.getItem(F);var C=this.getItem(D);if(this.visibleItems&&this.items&&this.visibleItems.length!=this.items.length){H=this.getVisibleItem(F);C=this.getVisibleItem(D)}if(Q!=undefined&&Q){this._raiseEvent("1",{index:D,type:L,item:C,originalEvent:M});this.selectedIndex=F;this.selectedIndexes[D]=-1;this.selectedIndexes[F]=F;if(H){this.selectedValue=H.value;this.selectedValues[H.value]=H.value}this._raiseEvent("0",{index:F,type:L,item:H,originalEvent:M})}else{var K=this;var R=function(V,U,T,W,X,S){K._raiseEvent("1",{index:U,type:T,item:W,originalEvent:S});K.selectedIndex=V;K.selectedIndexes=[];U=V;K.selectedIndexes[V]=V;K.selectedValues=new Array();if(X){K.selectedValues[X.value]=X.value}K._raiseEvent("0",{index:V,type:T,item:X,originalEvent:S})};var G=function(V,U,T,W,X,S){if(K.selectedIndexes[V]==undefined||K.selectedIndexes[V]==-1){K.selectedIndexes[V]=V;K.selectedIndex=V;if(X){K.selectedValues[X.value]=X.value;K._raiseEvent("0",{index:V,type:T,item:X,originalEvent:S})}}else{U=K.selectedIndexes[V];W=K.getVisibleItem(U);if(W){K.selectedValues[W.value]=null}K.selectedIndexes[V]=-1;K.selectedIndex=-1;K._raiseEvent("1",{index:U,type:T,item:W,originalEvent:S})}};if(this.multipleextended){if(!this._shiftKey&&!this._ctrlKey){if(L!="keyboard"&&L!="mouse"){G(F,D,L,C,H,M);K._clickedIndex=F}else{this.clearSelection(false);K._clickedIndex=F;R(F,D,L,C,H,M)}}else{if(this._ctrlKey){if(L=="keyboard"){this.clearSelection(false);K._clickedIndex=F}G(F,D,L,C,H,M)}else{if(this._shiftKey){if(K._clickedIndex==undefined){K._clickedIndex=D}var O=Math.min(K._clickedIndex,F);var I=Math.max(K._clickedIndex,F);this.clearSelection(false);for(var P=O;P<=I;P++){K.selectedIndexes[P]=P;K.selectedValues[K.getVisibleItem(P).value]=K.getVisibleItem(P).value;K._raiseEvent("0",{index:P,type:L,item:this.getVisibleItem(P),originalEvent:M})}if(L!="keyboard"){K.selectedIndex=K._clickedIndex}else{K.selectedIndex=F}}}}}else{if(this.multiple){G(F,D,L,C,H,M)}else{if(H){this.selectedValue=H.value}R(F,D,L,C,H,M)}}}if(N==undefined||N==true){this._renderItems()}if(B!=undefined&&B!=null&&B==true){this.ensureVisible(F)}this._raiseEvent("2",{index:F,item:H,oldItem:C,type:L,originalEvent:M});this._updateInputSelection();return J},_updateInputSelection:function(){this._syncSelection();var E=new Array();if(this.input){if(this.selectedIndex==-1){this.input.val("")}else{if(this.items){if(this.items[this.selectedIndex]!=undefined){this.input.val(this.items[this.selectedIndex].value);E.push(this.items[this.selectedIndex].value)}}}if(this.multiple||this.multipleextended||this.checkboxes){var D=!this.checkboxes?this.getSelectedItems():this.getCheckedItems();var C="";if(D){for(var B=0;B<D.length;B++){if(undefined!=D[B]){if(B==D.length-1){C+=D[B].value}else{C+=D[B].value+","}E.push(D[B].value)}}this.input.val(C)}}}if(this.field&&this.input){if(this.field.nodeName.toLowerCase()=="select"){A.each(this.field,function(G,F){A(this).removeAttr("selected");this.selected=E.indexOf(this.value)>=0;if(this.selected){A(this).attr("selected",true)}})}else{A.each(this.items,function(G,F){A(this.originalItem.originalItem).removeAttr("data-selected");this.selected=E.indexOf(this.value)>=0;if(this.selected){A(this.originalItem.originalItem).attr("data-selected",true)}})}}},isIndexInView:function(F){if(isNaN(F)){return false}if(!this.items){return false}if(F<0||F>=this.items.length){return false}var D=this.vScrollInstance.value;var C=0;if(this.filterable){C=this.filterHeight}var G=this.visibleItems[F];if(G==undefined){return true}var E=G.initialTop;var B=G.height;if(E-D<C||E-D+C+B>=this.host.outerHeight()){return false}return true},_itemsInPage:function(){var B=0;var C=this;if(this.items){A.each(this.items,function(){if((this.initialTop+this.height)>=C.content.height()){return false}B++})}return B},_firstItemIndex:function(){if(this.visibleItems!=null){if(this.visibleItems[0]){if(this.visibleItems[0].isGroup){return this._nextItemIndex(0)}else{return 0}}else{return 0}}return -1},_lastItemIndex:function(){if(this.visibleItems!=null){if(this.visibleItems[this.visibleItems.length-1]){if(this.visibleItems[this.visibleItems.length-1].isGroup){return this._prevItemIndex(this.visibleItems.length-1)}else{return this.visibleItems.length-1}}else{return this.visibleItems.length-1}}return -1},_nextItemIndex:function(B){for(indx=B+1;indx<this.visibleItems.length;indx++){if(this.visibleItems[indx]){if(!this.visibleItems[indx].disabled&&!this.visibleItems[indx].isGroup){return indx}}}return -1},_prevItemIndex:function(B){for(indx=B-1;indx>=0;indx--){if(this.visibleItems[indx]){if(!this.visibleItems[indx].disabled&&!this.visibleItems[indx].isGroup){return indx}}}return -1},clearFilter:function(){this.filterInput.val("");this._updateItemsVisibility("")},_search:function(D){var C=this;var B=C.filterInput.val();if(D.keyCode==9){return}if(C.searchMode=="none"||C.searchMode==null||C.searchMode=="undefined"){return}if(D.keyCode==16||D.keyCode==17||D.keyCode==20){return}if(D.keyCode==37||D.keyCode==39){return false}if(D.altKey||D.keyCode==18){return}if(D.keyCode>=33&&D.keyCode<=40){return}if(D.ctrlKey||D.metaKey||C.ctrlKey){if(D.keyCode!=88&&D.keyCode!=86){return}}if(B===C.searchString){return}C._updateItemsVisibility(B)},_updateItemsVisibility:function(D){var B=this.getItems();if(B==undefined){return{index:-1,matchItem:new Array()}}var H=this;var J=-1;var E=new Array();var I=0;A.each(B,function(M){var L="";if(!this.isGroup){if(this.searchLabel){L=this.searchLabel}else{if(this.label){L=this.label}else{if(this.value){L=this.value}else{if(this.title){L=this.title}else{L="jqxItem"}}}}L=L.toString();var K=false;switch(H.searchMode){case"containsignorecase":K=A.jqx.string.containsIgnoreCase(L,D);break;case"contains":K=A.jqx.string.contains(L,D);break;case"equals":K=A.jqx.string.equals(L,D);break;case"equalsignorecase":K=A.jqx.string.equalsIgnoreCase(L,D);break;case"startswith":K=A.jqx.string.startsWith(L,D);break;case"startswithignorecase":K=A.jqx.string.startsWithIgnoreCase(L,D);break;case"endswith":K=A.jqx.string.endsWith(L,D);break;case"endswithignorecase":K=A.jqx.string.endsWithIgnoreCase(L,D);break}if(!K){this.visible=false}if(K){E[I++]=this;this.visible=true;J=this.visibleIndex}if(D==""){this.visible=true;K=false}}});H.renderedVisibleItems=new Array();H.visibleItems=new Array();H.vScrollInstance.setPosition(0,true);H._addItems(false);H._renderItems();for(var F=0;F<H.items.length;F++){H.selectedIndexes[F]=-1}H.selectedIndex=-1;for(var G in H.selectedValues){var D=H.selectedValues[G];var C=H.getItemByValue(D);if(C){if(C.visible){H.selectedIndex=C.visibleIndex;H.selectedIndexes[C.visibleIndex]=C.visibleIndex}}}H._syncSelection();if(H.filterChange){H.filterChange(D)}},_getMatches:function(B,C){if(B==undefined||B.length==0){return -1}if(C==undefined){C=0}var E=this.getItems();var G=this;var F=-1;var D=0;A.each(E,function(H){var J="";if(!this.isGroup){if(this.searchLabel){J=this.searchLabel.toString()}else{if(this.label){J=this.label.toString()}else{if(this.value){J=this.value.toString()}else{if(this.title){J=this.title.toString()}else{J="jqxItem"}}}}var I=false;switch(G.searchMode){case"containsignorecase":I=A.jqx.string.containsIgnoreCase(J,B);break;case"contains":I=A.jqx.string.contains(J,B);break;case"equals":I=A.jqx.string.equals(J,B);break;case"equalsignorecase":I=A.jqx.string.equalsIgnoreCase(J,B);break;case"startswith":I=A.jqx.string.startsWith(J,B);break;case"startswithignorecase":I=A.jqx.string.startsWithIgnoreCase(J,B);break;case"endswith":I=A.jqx.string.endsWith(J,B);break;case"endswithignorecase":I=A.jqx.string.endsWithIgnoreCase(J,B);break}if(I&&this.visibleIndex>=C){F=this.visibleIndex;return false}}});return F},findItems:function(C){var D=this.getItems();var B=this;var E=0;var F=new Array();A.each(D,function(G){var I="";if(!this.isGroup){if(this.label){I=this.label}else{if(this.value){I=this.value}else{if(this.title){I=this.title}else{I="jqxItem"}}}var H=false;switch(B.searchMode){case"containsignorecase":H=A.jqx.string.containsIgnoreCase(I,C);break;case"contains":H=A.jqx.string.contains(I,C);break;case"equals":H=A.jqx.string.equals(I,C);break;case"equalsignorecase":H=A.jqx.string.equalsIgnoreCase(I,C);break;case"startswith":H=A.jqx.string.startsWith(I,C);break;case"startswithignorecase":H=A.jqx.string.startsWithIgnoreCase(I,C);break;case"endswith":H=A.jqx.string.endsWith(I,C);break;case"endswithignorecase":H=A.jqx.string.endsWithIgnoreCase(I,C);break}if(H){F[E++]=this}}});return F},_syncSelection:function(){var B=this;if(B.filterable){if(B.items){for(var C=0;C<B.items.length;C++){var D=B.items[C];D.selected=false}}for(var C=0;C<B.visibleItems.length;C++){var D=B.visibleItems[C];if(B.selectedIndexes&&B.selectedIndexes[C]==D.visibleIndex){D.selected=true}}if(B.itemswrapper){B._renderItems()}}},_handleKeyDown:function(J){var C=J.keyCode;var G=this;var Q=G.selectedIndex;var R=G.selectedIndex;var L=false;if(!this.keyboardNavigation||!this.enableSelection){return}if(this.filterInput&&J.target==this.filterInput[0]){return}var F=function(){if(G.multiple||G.checkboxes){G.clearSelection(false)}};if(J.altKey){C=-1}if(C==32&&this.checkboxes){var P=this.getItem(Q);if(P!=null){G._updateItemCheck(P,Q);J.preventDefault()}G._searchString="";G.selectIndex(P.visibleIndex,false,true,true,"keyboard",J);G._renderItems();return}if(G.incrementalSearch){var K=-1;if(!G._searchString){G._searchString=""}if((C==8||C==46)&&G._searchString.length>=1){G._searchString=G._searchString.substr(0,G._searchString.length-1)}var B=String.fromCharCode(C);var M=(!isNaN(parseInt(B)));var I=false;if((C>=65&&C<=97)||M||C==8||C==32||C==46){if(!J.shiftKey){B=B.toLocaleLowerCase()}var S=1+G.selectedIndex;if(C!=8&&C!=32&&C!=46){if(G._searchString.length>0&&G._searchString.substr(0,1)==B){S=1+G.selectedIndex;G._searchString+=B}else{G._searchString+=B}}if(C==32){G._searchString+=" "}var N=this._getMatches(G._searchString,S);K=N;if(K==G._lastMatchIndex||K==-1){var N=this._getMatches(G._searchString,0);K=N}G._lastMatchIndex=K;if(K>=0){var H=function(){F();G.selectIndex(K,false,false,false,"keyboard",J);var T=G.isIndexInView(K);if(!T){G.ensureVisible(K)}else{G._renderItems()}};if(G._toSelectTimer){clearTimeout(G._toSelectTimer)}G._toSelectTimer=setTimeout(function(){H()},G.incrementalSearchKeyDownDelay)}I=true}if(G._searchTimer!=undefined){clearTimeout(G._searchTimer)}if(C==27||C==13){G._searchString=""}G._searchTimer=setTimeout(function(){G._searchString="";G._renderItems()},G.incrementalSearchDelay);if(K>=0){return}if(I){return false}}if(C==33){var D=G._itemsInPage();if(G.selectedIndex-D>=0){F();G.selectIndex(R-D,false,false,false,"keyboard",J)}else{F();G.selectIndex(G._firstItemIndex(),false,false,false,"keyboard",J)}G._searchString=""}if(C==32&&this.checkboxes){var P=this.getItem(Q);if(P!=null){G._updateItemCheck(P,Q);J.preventDefault()}G._searchString=""}if(C==36){F();G.selectIndex(G._firstItemIndex(),false,false,false,"keyboard",J);G._searchString=""}if(C==35){F();G.selectIndex(G._lastItemIndex(),false,false,false,"keyboard",J);G._searchString=""}if(C==34){var D=G._itemsInPage();if(G.selectedIndex+D<G.visibleItems.length){F();G.selectIndex(R+D,false,false,false,"keyboard",J)}else{F();G.selectIndex(G._lastItemIndex(),false,false,false,"keyboard",J)}G._searchString=""}if(C==38){G._searchString="";if(G.selectedIndex>0){var O=G._prevItemIndex(G.selectedIndex);if(O!=G.selectedIndex&&O!=-1){F();G.selectIndex(O,false,false,false,"keyboard",J)}else{return true}}else{return false}}else{if(C==40){G._searchString="";if(G.selectedIndex+1<G.visibleItems.length){var O=G._nextItemIndex(G.selectedIndex);if(O!=G.selectedIndex&&O!=-1){F();G.selectIndex(O,false,false,false,"keyboard",J)}else{return true}}else{return false}}}if(C==35||C==36||C==38||C==40||C==34||C==33){var E=G.isIndexInView(G.selectedIndex);if(!E){G.ensureVisible(G.selectedIndex)}else{G._renderItems()}return false}return true},_updateItemCheck:function(B,C){if(this.disabled){return}if(B.checked==true){B.checked=(B.hasThreeStates&&this.hasThreeStates)?null:false}else{B.checked=B.checked!=null}switch(B.checked){case true:this.checkIndex(C);break;case false:this.uncheckIndex(C);break;default:this.indeterminateIndex(C);break}},wheel:function(B,E){if(E.autoHeight||!E.enableMouseWheel){B.returnValue=true;return true}if(E.disabled){return true}var C=0;if(!B){B=window.event}if(B.originalEvent&&B.originalEvent.wheelDelta){B.wheelDelta=B.originalEvent.wheelDelta}if(B.wheelDelta){C=B.wheelDelta/120}else{if(B.detail){C=-B.detail/3}}if(C){var D=E._handleDelta(C);if(D){if(B.preventDefault){B.preventDefault()}if(B.originalEvent!=null){B.originalEvent.mouseHandled=true}if(B.stopPropagation!=undefined){B.stopPropagation()}}if(D){D=false;B.returnValue=D;return D}else{return false}}if(B.preventDefault){B.preventDefault()}B.returnValue=false},_handleDelta:function(B){var D=this.vScrollInstance.value;if(B<0){this.scrollDown()}else{this.scrollUp()}var C=this.vScrollInstance.value;if(D!=C){return true}return false},focus:function(){try{this.focused=true;this.host.focus();var C=this;setTimeout(function(){C.host.focus()},25)}catch(B){}},_removeHandlers:function(){var B=this;this.removeHandler(A(document),"keydown.listbox"+this.element.id);this.removeHandler(A(document),"keyup.listbox"+this.element.id);this.removeHandler(this.vScrollBar,"valueChanged");this.removeHandler(this.hScrollBar,"valueChanged");if(this._mousewheelfunc){this.removeHandler(this.host,"mousewheel",this._mousewheelfunc)}else{this.removeHandler(this.host,"mousewheel")}this.removeHandler(this.host,"keydown");this.removeHandler(this.content,"mouseleave");this.removeHandler(this.content,"focus");this.removeHandler(this.content,"blur");this.removeHandler(this.host,"focus");this.removeHandler(this.host,"blur");this.removeHandler(this.content,"mouseenter");this.removeHandler(this.content,"mouseup");this.removeHandler(this.content,"mousedown");this.removeHandler(this.content,"touchend");if(this._mousemovefunc){this.removeHandler(this.content,"mousemove",this._mousemovefunc)}else{this.removeHandler(this.content,"mousemove")}this.removeHandler(this.content,"selectstart");if(this.overlayContent){this.removeHandler(this.overlayContent,A.jqx.mobile.getTouchEventName("touchend"))}},_updateSize:function(){if(!this.virtualSize){this._oldheight=null;this.virtualSize=this._calculateVirtualSize()}var C=this;C._arrange();if(C.host.height()!=C._oldheight||C.host.width()!=C._oldwidth){var D=C.host.width()!=C._oldwidth;if(C.autoItemsHeight){C._render(false)}else{if(C.items){if(C.items.length>0&&C.virtualItemsCount*C.items[0].height<C._oldheight-2){C._render(false)}else{var B=C.vScrollInstance.value;C._updatescrollbars();C._renderItems();if(B<C.vScrollInstance.max){C.vScrollInstance.setPosition(B)}else{C.vScrollInstance.setPosition(C.vScrollInstance.max)}}}}C._oldwidth=C.host.width();C._oldheight=C.host.height()}},_addHandlers:function(){var G=this;this.focused=false;var H=false;var C=0;var L=null;var C=0;var I=0;var E=new Date();var B=this.isTouchDevice();this.addHandler(this.vScrollBar,"valueChanged",function(N){if(A.jqx.browser.msie&&A.jqx.browser.version>9){setTimeout(function(){G._renderItems()},1)}else{G._renderItems()}});this.addHandler(this.hScrollBar,"valueChanged",function(){G._renderItems()});if(this._mousewheelfunc){this.removeHandler(this.host,"mousewheel",this._mousewheelfunc)}this._mousewheelfunc=function(N){G.wheel(N,G)};this.addHandler(this.host,"mousewheel",this._mousewheelfunc);this.addHandler(A(document),"keydown.listbox"+this.element.id,function(N){G._ctrlKey=N.ctrlKey||N.metaKey;G._shiftKey=N.shiftKey});this.addHandler(A(document),"keyup.listbox"+this.element.id,function(N){G._ctrlKey=N.ctrlKey||N.metaKey;G._shiftKey=N.shiftKey});this.addHandler(this.host,"keydown",function(N){return G._handleKeyDown(N)});this.addHandler(this.content,"mouseleave",function(N){G.focused=false;var O=A.data(G.element,"hoveredItem");if(O!=null){A(O).removeClass(G.toThemeProperty("jqx-listitem-state-hover"));A(O).removeClass(G.toThemeProperty("jqx-fill-state-hover"));A.data(G.element,"hoveredItem",null)}});this.addHandler(this.content,"focus",function(N){if(!G.disabled){G.host.addClass(G.toThemeProperty("jqx-fill-state-focus"));G.focused=true}});this.addHandler(this.content,"blur",function(N){G.focused=false;G.host.removeClass(G.toThemeProperty("jqx-fill-state-focus"))});this.addHandler(this.host,"focus",function(N){if(!G.disabled){G.host.addClass(G.toThemeProperty("jqx-fill-state-focus"));G.focused=true}});this.addHandler(this.host,"blur",function(N){if(A.jqx.browser.msie&&A.jqx.browser.version<9&&G.focused){return}G.host.removeClass(G.toThemeProperty("jqx-fill-state-focus"));G.focused=false});this.addHandler(this.content,"mouseenter",function(N){G.focused=true});var J=A.jqx.utilities.hasTransform(this.host);if(this.enableSelection){var K=G.isTouchDevice()&&this.touchMode!==true;var F=!K?"mousedown":"touchend";var D=!K?"mouseup":"touchend";if(this.overlayContent){this.addHandler(this.overlayContent,A.jqx.mobile.getTouchEventName("touchend"),function(P){if(!G.enableSelection){return true}if(K){G._newScroll=new Date();if(G._newScroll-G._lastScroll<500){return true}}var O=A.jqx.mobile.getTouches(P);var T=O[0];if(T!=undefined){var Q=G.host.offset();var N=parseInt(T.pageX);var R=parseInt(T.pageY);if(G.touchMode==true){if(T._pageX!=undefined){N=parseInt(T._pageX);R=parseInt(T._pageY)}}N=N-Q.left;R=R-Q.top;var S=G._hitTest(N,R);if(S!=null&&!S.isGroup){G._newScroll=new Date();if(G._newScroll-G._lastScroll<500){return false}if(G.checkboxes){G._updateItemCheck(S,S.visibleIndex);return}if(S.html.indexOf("href")!=-1){setTimeout(function(){G.selectIndex(S.visibleIndex,false,true,false,"mouse",P);G.content.trigger("click");return false},100)}else{G.selectIndex(S.visibleIndex,false,true,false,"mouse",P);if(P.preventDefault){P.preventDefault()}G.content.trigger("click");return false}}}})}else{var M=false;this.addHandler(this.content,F,function(V){if(!G.enableSelection){return true}M=true;if(K){G._newScroll=new Date();if(G._newScroll-G._lastScroll<500){return false}}G.focused=true;if(!G.isTouchDevice()&&G.focusable){G.host.focus()}if(V.target.id!=("listBoxContent"+G.element.id)&&G.itemswrapper[0]!=V.target){var N=V.target;var X=A(N).offset();var Q=G.host.offset();if(J){var W=A.jqx.mobile.getLeftPos(N);var T=A.jqx.mobile.getTopPos(N);X.left=W;X.top=T;W=A.jqx.mobile.getLeftPos(G.element);T=A.jqx.mobile.getTopPos(G.element);Q.left=W;Q.top=T}var O=parseInt(X.top)-parseInt(Q.top);var R=parseInt(X.left)-parseInt(Q.left);var S=G._hitTest(R,O);if(S!=null&&!S.isGroup){var P=function(Z,Y){if(!G._shiftKey){G._clickedIndex=Z.visibleIndex}if(!G.checkboxes){G.selectIndex(Z.visibleIndex,false,true,false,"mouse",Y)}else{R=20+Y.pageX-X.left;if(G.rtl){var a=G.hScrollBar.css("visibility")!="hidden"?G.hScrollInstance.max:G.host.width();if(R<=G.host.width()-20){if(!G.allowDrag){G._updateItemCheck(Z,Z.visibleIndex);G.selectIndex(Z.visibleIndex,false,true,false,"mouse",Y)}else{setTimeout(function(){if(!G._dragItem){if(!M){G._updateItemCheck(Z,Z.visibleIndex);G.selectIndex(Z.visibleIndex,false,true,false,"mouse",Y)}}},200)}}}else{if(R+G.hScrollInstance.value>=20){if(!G.allowDrag){G._updateItemCheck(Z,Z.visibleIndex);G.selectIndex(Z.visibleIndex,false,true,false,"mouse",Y)}else{setTimeout(function(){if(!G._dragItem){if(!M){G._updateItemCheck(Z,Z.visibleIndex);G.selectIndex(Z.visibleIndex,false,true,false,"mouse",Y)}}},200)}}}}};if(!S.disabled){if(S.html.indexOf("href")!=-1){setTimeout(function(){P(S,V)},100)}else{P(S,V)}}}if(F=="mousedown"){var U=false;if(V.which){U=(V.which==3)}else{if(V.button){U=(V.button==2)}}if(U){return true}return false}}return true})}this.addHandler(this.content,"mouseup",function(N){G.vScrollInstance.handlemouseup(G,N);M=false});if(A.jqx.browser.msie){this.addHandler(this.content,"selectstart",function(N){return false})}}var B=this.isTouchDevice();if(this.enableHover&&!B){this._mousemovefunc=function(W){if(B){return true}if(!G.enableHover){return true}var P=A.jqx.browser.msie==true&&A.jqx.browser.version<9?0:1;if(W.target==null){return true}if(G.disabled){return true}G.focused=true;var N=G.vScrollInstance.isScrolling();if(!N&&W.target.id!=("listBoxContent"+G.element.id)){if(G.itemswrapper[0]!=W.target){var T=W.target;var U=A(T).offset();var O=G.host.offset();if(J){var X=A.jqx.mobile.getLeftPos(T);var R=A.jqx.mobile.getTopPos(T);U.left=X;U.top=R;X=A.jqx.mobile.getLeftPos(G.element);R=A.jqx.mobile.getTopPos(G.element);O.left=X;O.top=R}var V=parseInt(U.top)-parseInt(O.top);var S=parseInt(U.left)-parseInt(O.left);var Z=G._hitTest(S,V);if(Z!=null&&!Z.isGroup&&!Z.disabled){var Q=A.data(G.element,"hoveredItem");if(Q!=null){A(Q).removeClass(G.toThemeProperty("jqx-listitem-state-hover"));A(Q).removeClass(G.toThemeProperty("jqx-fill-state-hover"))}A.data(G.element,"hoveredItem",Z.element);var Y=A(Z.element);Y.addClass(G.toThemeProperty("jqx-listitem-state-hover"));Y.addClass(G.toThemeProperty("jqx-fill-state-hover"))}}}};this.addHandler(this.content,"mousemove",this._mousemovefunc)}},_arrange:function(G){if(G==undefined){G=true}var F=this;var D=null;var K=null;var R=F.filterable?F.filterHeight:0;var H=function(U){U=F.host.height();if(U==0){U=200;F.host.height(U)}return U};if(F.width!=null&&F.width.toString().indexOf("px")!=-1){D=F.width}else{if(F.width!=undefined&&!isNaN(F.width)){D=F.width}}if(F.height!=null&&F.height.toString().indexOf("px")!=-1){K=F.height}else{if(F.height!=undefined&&!isNaN(F.height)){K=F.height}}if(F.width!=null&&F.width.toString().indexOf("%")!=-1){F.host.css("width",F.width);D=F.host.width()}if(F.height!=null&&F.height.toString().indexOf("%")!=-1){F.host.css("height",F.height);K=H(K)}if(D!=null){D=parseInt(D);if(parseInt(F.element.style.width)!=parseInt(F.width)){F.host.width(F.width)}}if(!F.autoHeight){if(K!=null){K=parseInt(K);if(parseInt(F.element.style.height)!=parseInt(F.height)){F.host.height(F.height);H(K)}}}else{if(F.virtualSize){if(F.hScrollBar.css("visibility")!="hidden"){F.host.height(F.virtualSize.height+parseInt(F.scrollBarSize)+3);F.height=F.virtualSize.height+parseInt(F.scrollBarSize)+3;K=F.height}else{F.host.height(F.virtualSize.height);F.height=F.virtualSize.height;K=F.virtualSize.height}}}var P=F.scrollBarSize;if(isNaN(P)){P=parseInt(P);if(isNaN(P)){P="17px"}else{P=P+"px"}}P=parseInt(P);var N=4;var T=2;var Q=1;if(F.vScrollBar){if(F.vScrollBar[0].style.visibility!="hidden"){Q=P+N}else{F.vScrollInstance.setPosition(0)}}else{return}if(P==0){Q=1;T=1}if(F.hScrollBar){if(F.hScrollBar[0].style.visibility!="hidden"){T=P+N}else{F.hScrollInstance.setPosition(0)}}else{return}if(F.autoItemsHeight){F.hScrollBar[0].style.visibility="hidden";T=0}if(K==null){K=0}var E=parseInt(K)-N-P;if(E<0){E=0}if(parseInt(F.hScrollBar[0].style.height)!=P){if(parseInt(P)<0){P=0}F.hScrollBar[0].style.height=parseInt(P)+"px"}if(F.hScrollBar[0].style.top!=E+"px"){F.hScrollBar[0].style.top=E+"px";F.hScrollBar[0].style.left="0px"}var O=D-P-N;if(O<0){O=0}var M=O+"px";if(F.hScrollBar[0].style.width!=M){F.hScrollBar[0].style.width=M}if(Q<=1){if(D>=2){F.hScrollBar[0].style.width=parseInt(D-2)+"px"}}if(P!=parseInt(F.vScrollBar[0].style.width)){F.vScrollBar[0].style.width=parseInt(P)+"px"}if((parseInt(K)-T)!=parseInt(F.vScrollBar[0].style.height)){var C=parseInt(K)-T;if(C<0){C=0}F.vScrollBar[0].style.height=C+"px"}if(D==null){D=0}var S=parseInt(D)-parseInt(P)-N+"px";if(S!=F.vScrollBar[0].style.left){if(parseInt(S)>=0){F.vScrollBar[0].style.left=S}F.vScrollBar[0].style.top="0px"}var I=F.vScrollInstance;I.disabled=F.disabled;if(G){I._arrange()}var L=F.hScrollInstance;L.disabled=F.disabled;if(G){L._arrange()}if((F.vScrollBar[0].style.visibility!="hidden")&&(F.hScrollBar[0].style.visibility!="hidden")){F.bottomRight[0].style.visibility="inherit";F.bottomRight[0].style.left=1+parseInt(F.vScrollBar[0].style.left)+"px";F.bottomRight[0].style.top=1+parseInt(F.hScrollBar[0].style.top)+"px";if(F.rtl){F.bottomRight.css({left:0})}F.bottomRight[0].style.width=parseInt(P)+3+"px";F.bottomRight[0].style.height=parseInt(P)+3+"px"}else{F.bottomRight[0].style.visibility="hidden"}if(parseInt(F.content[0].style.width)!=(parseInt(D)-Q)){var J=parseInt(D)-Q;if(J<0){J=0}F.content[0].style.width=J+"px"}if(F.rtl){F.vScrollBar.css({left:0+"px",top:"0px"});F.hScrollBar.css({left:F.vScrollBar.width()+2+"px"});if(F.vScrollBar[0].style.visibility!="hidden"){F.content.css("margin-left",4+F.vScrollBar.width())}else{F.content.css("margin-left",0);F.hScrollBar.css({left:"0px"})}if(F.filterable&&F.filterInput){F.filterInput.css({left:F.vScrollBar.width()+6+"px"})}}if(parseInt(F.content[0].style.height)!=(parseInt(K)-T)){var B=parseInt(K)-T;if(B<0){B=0}F.content[0].style.height=B+"px";F.content[0].style.top="0px"}if(R>0){F.content[0].style.top=R+"px";F.content[0].style.height=parseInt(F.content[0].style.height)-R+"px"}if(F.filterable){F.filterInput[0].style.height=(R-6)+"px";F.filterInput[0].style.top="3px";if(!F.rtl){F.filterInput[0].style.left=parseInt(F.content.css("left"))+3+"px"}F.filterInput[0].style.width=parseInt(F.content.css("width"))-7+"px";F.filter[0].style.display="block"}else{F.filter[0].style.display="none"}if(F.overlayContent){F.overlayContent.width(parseInt(D)-Q);F.overlayContent.height(parseInt(K)-T)}},ensureVisible:function(E,F){if(isNaN(E)){var G=this.getItemByValue(E);if(G){E=G.index}}var J=this.isIndexInView(E);if(!J){if(E<0){return}if(this.autoHeight){var K=A.data(this.vScrollBar[0],"jqxScrollBar").instance;K.setPosition(0)}else{for(indx=0;indx<this.visibleItems.length;indx++){var G=this.visibleItems[indx];if(G.visibleIndex==E&&!G.isGroup){var K=A.data(this.vScrollBar[0],"jqxScrollBar").instance;var D=K.value;var H=!this.filterable?0:this.filterHeight+2;var B=this.hScrollBar.css("visibility")==="hidden";var L=B?0:this.scrollBarSize+4;if(G.initialTop<D){K.setPosition(G.initialTop);if(indx==0){K.setPosition(0)}}else{if(G.initialTop+G.height>D+this.host.height()-H){var I=this.host.height();if(this.filterable){K.setPosition(this.filterHeight+2+G.initialTop+G.height+2-I+L)}else{K.setPosition(G.initialTop+G.height+2-I+L);if(indx===this.visibleItems.length-1){K.setPosition(K.max)}}if(F){var D=K.value;var C=G.initialTop;if(this.filterable){C=this.filterHeight+2+G.initialTop}if(D+I<K.max){K.setPosition(C)}}}}break}}}}else{if(F){for(indx=0;indx<this.visibleItems.length;indx++){var G=this.visibleItems[indx];if(G.visibleIndex==E&&!G.isGroup){var D=this.vScrollInstance.value;var C=G.initialTop;if(this.filterable){C=this.filterHeight+2+G.initialTop}if(D+this.host.height()<this.vScrollInstance.max){this.vScrollInstance.setPosition(C)}}}}}this._renderItems()},scrollTo:function(C,B){if(this.vScrollBar.css("visibility")!="hidden"){this.vScrollInstance.setPosition(B)}if(this.hScrollBar.css("visibility")!="hidden"){this.hScrollInstance.setPosition(C)}},scrollDown:function(){if(this.vScrollBar.css("visibility")=="hidden"){return false}var B=this.vScrollInstance;if(B.value+B.largestep<=B.max){B.setPosition(B.value+B.largestep);return true}else{B.setPosition(B.max);return true}return false},scrollUp:function(){if(this.vScrollBar.css("visibility")=="hidden"){return false}var B=this.vScrollInstance;if(B.value-B.largestep>=B.min){B.setPosition(B.value-B.largestep);return true}else{if(B.value!=B.min){B.setPosition(B.min);return true}}return false},databind:function(F,J){this.records=new Array();var H=F._source?true:false;var G=new A.jqx.dataAdapter(F,{autoBind:false});if(H){G=F;F=F._source}var B=function(K){if(F.type!=undefined){G._options.type=F.type}if(F.formatdata!=undefined){G._options.formatData=F.formatdata}if(F.contenttype!=undefined){G._options.contentType=F.contenttype}if(F.async!=undefined){G._options.async=F.async}};var D=function(N,K){var L=function(U){var Z=null;if(typeof U==="string"){var a=U;var Y=U;var X=""}else{if(N.displayMember!=undefined&&N.displayMember!=""){var Y=U[N.valueMember];var a=U[N.displayMember]}}var X="";if(N.groupMember){X=U[N.groupMember]}else{if(U&&U.group!=undefined){X=U.group}}if(N.searchMember){Z=U[N.searchMember]}else{if(U&&U.searchLabel!=undefined){Z=U.searchLabel}}if(!N.valueMember&&!N.displayMember){if(A.type(U)=="string"){a=Y=U.toString()}}if(U&&U.label!=undefined){var a=U.label}if(U&&U.value!=undefined){var Y=U.value}var W=false;if(U&&U.checked!=undefined){W=U.checked}var T="";if(U&&U.html!=undefined){T=U.html}var V=true;if(U&&U.visible!=undefined){V=U.visible}var b=false;if(U&&U.disabled!=undefined){b=U.disabled}var d=false;if(U&&U.hasThreeStates!=undefined){d=U.hasThreeStates}var c={};c.label=a;c.value=Y;c.searchLabel=Z;c.html=T;c.visible=V;c.originalItem=U;c.group=X;c.groupHtml="";c.disabled=b;c.checked=W;c.hasThreeStates=d;return c};if(K!=undefined){var O=G._changedrecords[0];if(O){A.each(G._changedrecords,function(){var U=this.index;var V=this.record;if(K!="remove"){var T=L(V)}switch(K){case"update":N.updateAt(T,U);break;case"add":N.insertAt(T,U);break;case"remove":N.removeAt(U);break}});return}}N.records=G.records;var S=N.records.length;var M=new Array();for(var R=0;R<S;R++){var P=N.records[R];var Q=L(P);Q.index=R;M[R]=Q}N.items=N.loadItems(M,true);N._render();N._raiseEvent("6")};B(this);var E=this;switch(F.datatype){case"local":case"array":default:if(F.localdata!=null||A.isArray(F)){G.unbindBindingUpdate(this.element.id);if(this.autoBind||(!this.autoBind&&!J)){G.dataBind()}D(this);G.bindBindingUpdate(this.element.id,function(K){D(E,K)})}break;case"json":case"jsonp":case"xml":case"xhtml":case"script":case"text":case"csv":case"tab":if(F.localdata!=null){G.unbindBindingUpdate(this.element.id);if(this.autoBind||(!this.autoBind&&!J)){G.dataBind()}D(this);G.bindBindingUpdate(this.element.id,function(){D(E)});return}var C={};if(G._options.data){A.extend(G._options.data,C)}else{if(F.data){A.extend(C,F.data)}G._options.data=C}var I=function(){D(E)};G.unbindDownloadComplete(E.element.id);G.bindDownloadComplete(E.element.id,I);if(this.autoBind||(!this.autoBind&&!J)){G.dataBind()}}},loadItems:function(L,J){if(L==null){this.groups=new Array();this.items=new Array();this.visualItems=new Array();return}var F=this;var G=0;var Q=0;var M=0;this.groups=new Array();this.items=new Array();this.visualItems=new Array();var R=new Array();this.itemsByValue=new Array();A.map(L,function(Y){if(Y==undefined){return null}var V=new A.jqx._jqxListBox.item();var Z=Y.group;var W=Y.groupHtml;var X=Y.title;var S=null;if(F.searchMember){S=Y[F.searchMember]}else{if(Y&&Y.searchLabel!=undefined){S=Y.searchLabel}}if(X==null||X==undefined){X=""}if(Z==null||Z==undefined){Z=""}if(F.groupMember){Z=Y[F.groupMember]}if(W==null||W==undefined){W=""}if(!F.groups[Z]){F.groups[Z]={items:new Array(),index:-1,caption:Z,captionHtml:W};G++;var U=G+"jqxGroup";F.groups[U]=F.groups[Z];Q++;F.groups.length=Q}var T=F.groups[Z];T.index++;T.items[T.index]=V;if(typeof Y==="string"){V.label=Y;V.value=Y;if(arguments.length>1&&arguments[1]&&A.type(arguments[1])=="string"){V.label=Y;V.value=arguments[1]}}else{if(Y.label==null&&Y.value==null&&Y.html==null&&Y.group==null&&Y.groupHtml==null){V.label=Y.toString();V.value=Y.toString()}else{V.label=Y.label;V.value=Y.value;if(V.label===undefined){V.label=Y.value}if(V.value===undefined){V.value=Y.label}}}if(typeof Y!="string"){if(Y.label===undefined){if(F.displayMember!=""){if(Y[F.displayMember]!=undefined){V.label=Y[F.displayMember]}else{V.label=""}}}if(Y.value===undefined){if(F.valueMember!=""){V.value=Y[F.valueMember]}}}V.hasThreeStates=Y.hasThreeStates!=undefined?Y.hasThreeStates:true;V.originalItem=Y;if(J){V.originalItem=Y.originalItem}V.title=X;if(X&&V.value===undefined&&V.label===undefined){V.value=V.label=X}V.html=Y.html||"";if(Y.html&&Y.html!=""){if(X&&X!=""){}}V.group=Z;V.checked=Y.checked||false;V.groupHtml=Y.groupHtml||"";V.disabled=Y.disabled||false;V.visible=Y.visible!=undefined?Y.visible:true;V.searchLabel=S;V.index=M;R[M]=V;M++;return V});var N=new Array();var D=0;if(this.fromSelect==undefined||this.fromSelect==false){for(var H=0;H<Q;H++){var G=H+1;var I=G+"jqxGroup";var B=this.groups[I];if(B==undefined||B==null){break}if(H==0&&B.caption==""&&B.captionHtml==""&&Q<=1){for(var P=0;P<B.items.length;P++){var E=B.items[P].value;if(B.items[P].value==undefined||B.items[P].value==null){E=P}this.itemsByValue[A.trim(E).split(" ").join("?")]=B.items[P]}return B.items}else{var K=new A.jqx._jqxListBox.item();K.isGroup=true;K.label=B.caption;if(B.caption==""&&B.captionHtml==""){B.caption=this.emptyGroupText;K.label=B.caption}K.html=B.captionHtml;N[D]=K;D++}for(var O=0;O<B.items.length;O++){N[D]=B.items[O];var E=B.items[O].value;if(B.items[O].value==""||B.items[O].value==null){E=D}F.itemsByValue[A.trim(E).split(" ").join("?")]=B.items[O];D++}}}else{var D=0;var C=new Array();A.each(R,function(){if(!C[this.group]){if(this.group!=""){var S=new A.jqx._jqxListBox.item();S.isGroup=true;S.label=this.group;N[D]=S;D++;C[this.group]=true}}N[D]=this;var T=this.value;if(this.value==""||this.value==null){T=D-1}F.itemsByValue[A.trim(T).split(" ").join("?")]=this;D++})}return N},_mapItem:function(C){var B=new A.jqx._jqxListBox.item();if(this.displayMember){if(C.label==undefined){C.label=C[this.displayMember]}if(C.value==undefined){C.value=C[this.valueMember]}}if(typeof C==="string"){B.label=C;B.value=C}else{if(typeof C==="number"){B.label=C.toString();B.value=C.toString()}else{B.label=C.label!==undefined?C.label:C.value;B.value=C.value!==undefined?C.value:C.label}}if(B.label==undefined&&B.value==undefined&&B.html==undefined){B.label=B.value=C}B.html=C.html||"";B.group=C.group||"";B.checked=C.checked||false;B.title=C.title||"";B.groupHtml=C.groupHtml||"";B.disabled=C.disabled||false;B.visible=C.visible||true;return B},addItem:function(B){return this.insertAt(B,this.items?this.items.length:0)},_getItemByParam:function(C){if(C!=null){if(C.index==undefined){var B=this.getItemByValue(C);if(B){C=B}}}return C},insertItem:function(B,C){var D=this._getItemByParam(B);return this.insertAt(D,C)},updateItem:function(D,B){var C=this._getItemByParam(B);if(C&&C.index!=undefined){return this.updateAt(D,C.index)}return false},updateAt:function(B,D){if(B!=null){var C=this._mapItem(B);this.itemsByValue[A.trim(C.value).split(" ").join("?")]=this.items[D];this.items[D].value=C.value;this.items[D].label=C.label;this.items[D].html=C.html;this.items[D].disabled=C.disabled;this._raiseEvent("9",{item:this.items[D]})}this._cachedItemHtml=[];this._renderItems();if(this.rendered){this.rendered()}},insertAt:function(G,J){if(G==null){return false}this._cachedItemHtml=[];if(this.items==undefined||this.items.length==0){this.source=new Array();this.refresh();var K=this._mapItem(G);K.index=0;this.items[this.items.length]=K;this._addItems(true);this._renderItems();if(this.rendered){this.rendered()}if(this.allowDrag&&this._enableDragDrop){this._enableDragDrop()}var D=K.value;if(K.value==""||K.value==null){D=J}this.itemsByValue[A.trim(D).split(" ").join("?")]=K;return false}var K=this._mapItem(G);if(J==-1||J==undefined||J==null||J>=this.items.length){K.index=this.items.length;this.items[this.items.length]=K}else{var I=new Array();var C=0;var B=false;var E=0;for(var H=0;H<this.items.length;H++){if(this.items[H].isGroup==false){if(E>=J&&!B){I[C++]=K;K.index=J;E++;B=true}}I[C]=this.items[H];if(!this.items[H].isGroup){I[C].index=E;E++}C++}this.items=I}var D=K.value;if(K.value==""||K.value==null){D=J}this.itemsByValue[A.trim(D).split(" ").join("?")]=K;this.visibleItems=new Array();this.renderedVisibleItems=new Array();var L=A.data(this.vScrollBar[0],"jqxScrollBar").instance;var F=L.value;L.setPosition(0);if((this.allowDrag&&this._enableDragDrop)||(this.virtualSize&&this.virtualSize.height<10+this.host.height())){this._addItems(true)}else{this._addItems(false)}if(this.groups.length>1){}this._renderItems();if(this.allowDrag&&this._enableDragDrop){this._enableDragDrop()}L.setPosition(F);this._raiseEvent("7",{item:K});if(this.rendered){this.rendered()}return true},removeAt:function(D){if(D<0||D>this.items.length-1){return false}if(D==undefined){return false}var O=this.items[D].height;var H=this.items[D].value;if(H==""||H==null){H=D}this.itemsByValue[A.trim(H).split(" ").join("?")]=null;var E=this.items[D];if(this.groups.length>1){var F=new Array();for(var K=0;K<this.items.length;K++){if(!this.items[K].isGroup){F.push({item:this.items[K],key:K})}}if(F[D]){this.items.splice(F[D].key,1)}else{return false}}else{this.items.splice(D,1)}var L=new Array();var G=0;var M=false;var I=0;for(var K=0;K<this.items.length;K++){L[G]=this.items[K];if(!this.items[K].isGroup){L[G].index=I;I++}G++}this.items=L;var B=A.data(this.vScrollBar[0],"jqxScrollBar").instance;var B=A.data(this.vScrollBar[0],"jqxScrollBar").instance;var J=B.value;B.setPosition(0);this.visibleItems=new Array();this.renderedVisibleItems=new Array();if(this.items.length>0){if(this.virtualSize){this.virtualSize.height-=O;var C=this.virtualSize.itemsPerPage*2;if(this.autoHeight){C=this.items.length}this.virtualItemsCount=Math.min(C,this.items.length)}this._updatescrollbars()}else{this._addItems()}this._renderItems();if(this.allowDrag&&this._enableDragDrop){this._enableDragDrop()}if(this.vScrollBar.css("visibility")!="hidden"){B.setPosition(J)}else{B.setPosition(0)}this.itemsByValue=new Array();for(var N=0;N<this.items.length;N++){var H=this.items[N].value;if(this.items[N].value==""||this.items[N].value==null){H=N}this.itemsByValue[A.trim(H).split(" ").join("?")]=this.items[N]}this._raiseEvent("8",{item:E});if(this.rendered){this.rendered()}return true},removeItem:function(C,F){var B=this._getItemByParam(C);var D=-1;if(B&&B.index!=undefined&&F!==true){for(var E=0;E<this.items.length;E++){if(this.items[E].label==B.label&&this.items[E].value==B.value){D=E;break}}if(D!=-1){return this.removeAt(D)}}if(D==-1){return this.removeAt(B.index)}},getItems:function(){return this.items},disableItem:function(C){var B=this._getItemByParam(C);this.disableAt(B.index)},enableItem:function(C){var B=this._getItemByParam(C);this.enableAt(B.index)},disableAt:function(B){if(!this.items){return false}if(B<0||B>this.items.length-1){return false}this.items[B].disabled=true;this._renderItems();return true},enableAt:function(B){if(!this.items){return false}if(B<0||B>this.items.length-1){return false}this.items[B].disabled=false;this._renderItems();return true},destroy:function(){if(this.source&&this.source.unbindBindingUpdate){this.source.unbindBindingUpdate(this.element.id)}this._removeHandlers();this.vScrollBar.jqxScrollBar("destroy");this.hScrollBar.jqxScrollBar("destroy");this.vScrollBar.remove();this.hScrollBar.remove();this.content.remove();A.jqx.utilities.resize(this.host,null,true);var B=A.data(this.element,"jqxListBox");delete this.hScrollInstance;delete this.vScrollInstance;delete this.vScrollBar;delete this.hScrollBar;delete this.content;delete this.bottomRight;delete this.itemswrapper;delete this.visualItems;delete this.visibleItems;delete this.items;delete this.groups;delete this.renderedVisibleItems;delete this._mousewheelfunc;delete this._mousemovefunc;delete this._cachedItemHtml;delete this.itemsByValue;delete this._activeElement;delete this.source;delete this.events;if(this.input){this.input.remove();delete this.input}if(B){delete B.instance}this.host.removeData();this.host.removeClass();this.host.remove();this.element=null;delete this.element;this.host=null;delete this.set;delete this.get;delete this.call;delete this.host},_raiseEvent:function(F,E){if(this._stopEvents==true){return true}if(E==undefined){E={owner:null}}var B=this.events[F];args=E;args.owner=this;this._updateInputSelection();var C=new A.Event(B);C.owner=this;C.args=args;if(this.host!=null){var D=this.host.trigger(C)}return D}})})(jqxBaseFramework);(function(A){A.jqx.parseSourceTag=function(I){var H=new Array();var G=A(I).find("option");var J=A(I).find("optgroup");var D=false;if(G.length===0){G=A(I).find("li");if(G.length>0){D=true}}var N=null;var C=0;var N=-1;var E=this;var K=new Array();A.each(G,function(Q){var R=J.find(this).length>0;var P=null;if(this.text!=null&&(this.label==null||this.label=="")){this.label=this.text}if(D===true){this.label=A(this).text();this.selected=A(this).attr("data-selected");this.checked=this.selected;this.value=A(this).attr("data-value")||Q;this.disabled=A(this).attr("disabled")}var O={style:this.style.cssText,selected:this.selected,html:this.innerHTML,classes:this.className,disabled:this.disabled,value:this.value,label:this.label,title:this.title,originalItem:this};var S=A.jqx.browser.msie&&A.jqx.browser.version<8;if(S&&!D){if(O.value==""&&this.text!=null&&this.text.length>0){O.value=this.text}}if(R){P=J.find(this).parent()[0].label;O.group=P;if(!K[P]){K[P]=new Array();K.length++}K[P].push(O)}if(this.selected){N=Q}O.checked=this.selected;if(O.label!==undefined){H.push(O)}});if(K.length>0){var F=new Array();for(var L in K){if(L==="indexOf"){continue}var M=null;for(var B=0;B<J.length;B++){if(L===J[B].label||J[B].text){M=J[B];break}}A.each(K[L],function(O,P){if(this.label!==undefined){F.push(this)}})}}if(F&&F.length>0){return{items:F,index:N}}else{return{items:H,index:N}}};A.jqx._jqxListBox.item=function(){var B={group:"",groupHtml:"",selected:false,isGroup:false,highlighted:false,value:null,label:"",html:null,visible:true,disabled:false,element:null,width:null,height:null,initialTop:null,top:null,left:null,title:"",index:-1,checkBoxElement:null,originalItem:null,checked:false,visibleIndex:-1};return B}})(jqxBaseFramework);