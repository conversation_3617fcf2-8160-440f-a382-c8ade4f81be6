(function(A){A.jqx.dataview.grouping=function(){this.loadgrouprecords=function(a,Ad,Aj,i,Aa,Q,X,Ac,Ak){var S=a;var Aq=this;var W=new Array();for(var At=0;At<Aq.groups.length;At++){W[At]=Aq.generatekey()}var U=new Array();var Av=0;var W=W;var Ag=new Array();var Z=Ad;var Az=Ad;var Ae=Aq.groups.length;this.loadedrecords=new Array();this.bounditems=new Array();this.loadedrecords=new Array();this.loadedrootgroups=new Array();this.loadedgroups=new Array();this.loadedgroupsByKey=new Array();this.sortedgroups=new Array();var Y=this.sortdata!=null;var R=Y?this.sortdata:this.records;if(this.pageable){var Ai=new Array();var q=0;if(!R[Ad]){A.each(R,function(B,C){Ai[Ad+B++]=this});R=Ai}}if(this.virtualmode){var Ai=new Array();var q=0;for(var Z=0;Z<Aj-Ad;Z++){if(R[Z]){Ai[Ad+q++]=R[Z]}else{if(R[Ad+q]){Ai[Ad+q]=R[Ad+q];q++}}}Z=0;R=Ai}for(var Aw=Ad;Aw<Aj;Aw++){var Al={};if(!Y){Al=A.extend({},R[Aw])}else{Al=A.extend({},R[Aw].value)}id=Al[Aq.uniqueId];if(Aa>=Ac||id!=X[Aa][Aq.uniqueId]||(Q&&Q[id])){Ak[Ak.length]=Aa}var V=new Array();var An=0;for(var At=0;At<Ae;At++){var Ah=Aq.groups[At];var Ab=Al[Ah];if(Ab==null){Ab=""}V[An++]={value:Ab,hash:W[At]}}if(V.length!=Ae){break}var Ao=null;var Ap="";var Ax=-1;for(var T=0;T<V.length;T++){Ax++;var Ar=V[T].value;var Am=V[T].hash;Ap=Ap+"_"+Am+"_"+Ar;if(Ag[Ap]!=undefined&&Ag[Ap]!=null){Ao=Ag[Ap];continue}if(Ao==null){Ao={group:Ar,subItems:new Array(),subGroups:new Array(),level:0};U[Av++]=Ao;Ao.uniqueid=Aq.generatekey();Aq.loadedgroupsByKey[Ar]=Ao}else{var Au={group:Ar,subItems:new Array(),subGroups:new Array(),parentItem:Ao,level:Ao.level+1};Aq.loadedgroupsByKey[Ao.uniqueid+"_"+Ar]=Au;Au.uniqueid=Aq.generatekey();Ao.subGroups[Ao.subGroups.length++]=Au;Ao=Au}Ag[Ap]=Ao}if(Ao!=null){if(undefined==Al.uid){Al.uid=this.getid(this.source.id,Al,Z)}if(!Y){Al.boundindex=Z;this.recordsbyid["id"+Al.uid]=R[Aw]}else{Al.boundindex=R[Aw].index;this.recordsbyid["id"+Al.uid]=R[Aw].value}this.bounditems[Al.boundindex]=Al;this.sortedgroups[Z]=Al;Al.uniqueid=Aq.generatekey();Al.parentItem=Ao;Al.level=Ao.level+1;Ao.subItems[Ao.subItems.length++]=Al}else{if(undefined==Al.uid){Al.uid=this.getid(this.source.id,Al,Z)}if(!Y){Al.boundindex=Z;this.recordsbyid["id"+Al.uid]=R[Aw]}else{Al.boundindex=R[Aw].index;this.recordsbyid["id"+Al.uid]=R[Aw].value}this.sortedgroups[Z]=Al;this.bounditems[Al.boundindex]=Al;Al.uniqueid=Aq.generatekey()}Aa++;Z++;Az++}var Ay=function(D,E,C){for(var B=0;B<E.subItems.length;B++){E.subItems[B].visibleindex=a+C;D.rows[C]=E.subItems[B];D.loadedrecords[C]=E.subItems[B];C++}return C};var As=function(B,F,G){var H=1;var D=B.grid.columns.records?B.grid.columns.records:B.grid.columns;if(B.aggregates==true){for(var C=0;C<D.length;C++){if(D[C].aggregates){H=Math.max(H,D[C].aggregates.length)}}}var E=function(L){if(B.aggregates==true){var K=function(M){var O=L;var P={};var b=function(l){for(var d=0;d<D.length;d++){if(D[d].aggregates){var f=B.grid.getcolumnaggregateddata(D[d].datafield,D[d].aggregates,true,l);for(var k=0;k<D[d].aggregates.length;k++){if(D[d].aggregates[M]){var j=D[d];var g=D[d].aggregates[M];var h=g;h=B.grid._getaggregatename(h);var e=h+":"+f[g];P[j.datafield]=e}}}}};if(O!=null){P.level=O.level;P.visibleindex=a+G;P.uniqueid=B.generatekey();B.rows[G]=P;B.loadedrecords[G++]=P;P.totalsrow=true;if(L.subItems.length>0){O=L.subItems[L.subItems.length-1];P.parentItem=O.parentItem;if(P.parentItem.subItems){P.parentItem.subItems[P.parentItem.subItems.length]=P}var c=[];var N=function(){for(var d=0;d<L.subItems.length;d++){if(L.subItems[d].totalsrow){continue}c[c.length]=L.subItems[d]}};N(L);b(c)}else{if(L.subGroups.length>0){O=L.subGroups[L.subGroups.length-1];P.level=O.level;P.parentItem=L;L.subGroups[L.subGroups.length]=P;var c=[];var N=function(e){if(e.totalsrow){return}for(var d=0;d<e.subItems.length;d++){if(e.subItems[d].totalsrow){continue}c[c.length]=e.subItems[d]}for(var d=0;d<e.subGroups.length;d++){N(e.subGroups[d])}};N(L);b(c)}}}};for(var J=0;J<H;J++){K(J)}}};for(subGroup in F.subGroups){var I=F.subGroups[subGroup];if(I.subGroups){B.loadedgroups[B.loadedgroups.length]=I;I.visibleindex=a+G;B.rows[G]=I;B.loadedrecords[G]=I;G++;if(I.subGroups.length>0){G=As(B,I,G)}else{if(I.subItems.length>0){G=Ay(B,I,G)}}E(I)}}if(F.subItems.length>0){G=Ay(B,F,G)}E(F);return G};var Af=U.length;this.loadedgroups=new Array();this.rows=new Array();var S=0;for(var Z=0;Z<Af;Z++){var Ah=U[Z];this.loadedrootgroups[Z]=Ah;this.loadedgroups[this.loadedgroups.length]=Ah;Ah.visibleindex=a+S;this.rows[S]=Ah;this.loadedrecords[S]=Ah;S++;S=As(this,Ah,S)}return S};this._updategroupsinpage=function(R,a,N,Ap,Ad,Ae,An){var q=new Array();var Ab=[];if(this.groupable&&this.groups.length>0){var Q=0;var Ag=new Array();var Ao=new Array();for(var Ak=0;Ak<R.groups.length;Ak++){Ao[Ak]=R.generatekey()}var T=0;var Ai=new Array();var Af=0;if(An>this.totalrecords){An=this.totalrecords}for(var Aa=Ae;Aa<An;Aa++){var O=A.extend({},R.sortedgroups[Aa]);id=O[R.uniqueId];if(!R.pagesize||(N>=R.pagesize*R.pagenum&&N<R.pagesize*(R.pagenum+1))){if(Ap>=Ad||id!=q[Ap][R.uniqueId]||(updated&&updated[id])){Ab[Ab.length]=Ap}var L=new Array();var Z=0;for(var Ak=0;Ak<R.groups.length;Ak++){var Ah=R.groups[Ak];var V=O[Ah];if(null==V){V=""}L[Z++]={value:V,hash:Ao[Ak]}}if(L.length!=R.groups.length){break}var X=null;var i="";var Am=-1;for(var S=0;S<L.length;S++){Am++;var U=L[S].value;var Aj=L[S].hash;i=i+"_"+Aj+"_"+U;if(Ag[i]!=undefined&&Ag[i]!=null){X=Ag[i];continue}if(X==null){X={group:U,subItems:new Array(),subGroups:new Array(),level:0};Ai[Af++]=X;var W=R.loadedgroupsByKey[U];if(W!=undefined){X.visibleindex=W.visibleindex;X.uniqueid=W.uniqueid}}else{var Ac={group:U,subItems:new Array(),subGroups:new Array(),parentItem:X,level:X.level+1};var W=R.loadedgroupsByKey[X.uniqueid+"_"+U];Ac.visibleindex=W.visibleindex;Ac.uniqueid=W.uniqueid;X.subGroups[X.subGroups.length++]=Ac;X=Ac}Ag[i]=X}if(X!=null){O.parentItem=X;O.level=X.level+1;X.subItems[X.subItems.length++]=O}Ap++}T++;N++}var Y=function(D,E,C){for(var B=0;B<E.subItems.length;B++){q[C]=A.extend({},E.subItems[B]);C++}return C};var P=function(E){var C=false;for(subGroup in E.subGroups){var D=E.subGroups[subGroup];if(D.subGroups){if(D.subGroups.length>0){var B=P(D);if(B){C=true;return true}}if(D.subItems.length>0){C=true;return true}}}if(E.subItems.length>0){C=true;return true}return C};var Al=function(D,G,B){var H=1;var C=D.grid.columns.records?D.grid.columns.records:D.grid.columns;if(D.aggregates==true){for(var F=0;F<C.length;F++){if(C[F].aggregates){H=Math.max(H,C[F].aggregates.length)}}}var E=function(b){if(D.aggregates==true){var K=function(f){var h=b;var g={};var d=function(l){for(var k=0;k<C.length;k++){if(C[k].aggregates){var n=D.grid.getcolumnaggregateddata(C[k].datafield,C[k].aggregates,true,l);for(var p=0;p<C[k].aggregates.length;p++){if(C[k].aggregates[f]){var r=C[k];var j=C[k].aggregates[f];var o=j;o=D.grid._getaggregatename(o);var m=o+":"+n[j];g[r.datafield]=m}}}}};if(h!=null){g.level=h.level;g.visibleindex=B;g.uniqueid=D.generatekey();q[B]=g;D.loadedrecords[B++]=g;g.totalsrow=true;if(b.subItems.length>0){h=b.subItems[b.subItems.length-1];g.parentItem=h.parentItem;if(g.parentItem.subItems){g.parentItem.subItems[g.parentItem.subItems.length]=g}var e=[];var c=function(){for(var j=0;j<b.subItems.length;j++){if(b.subItems[j].totalsrow){continue}e[e.length]=b.subItems[j]}};c(b);d(e)}else{if(b.subGroups.length>0){h=b.subGroups[b.subGroups.length-1];g.level=h.level;g.parentItem=b;b.subGroups[b.subGroups.length]=g;var e=[];var c=function(j){if(j.totalsrow){return}for(var k=0;k<j.subItems.length;k++){if(j.subItems[k].totalsrow){continue}e[e.length]=j.subItems[k]}for(var k=0;k<j.subGroups.length;k++){c(j.subGroups[k])}};c(b);d(e)}}}};for(var J=0;J<H;J++){K(J)}}};for(subGroup in G.subGroups){var I=G.subGroups[subGroup];if(I.subGroups){if(P(I)){q[B]=I;B++;if(I.subGroups.length>0){B=Al(D,I,B)}else{if(I.subItems.length>0){B=Y(D,I,B)}}E(I)}}}if(G.subItems.length>0){B=Y(D,G,B)}E(G);return B};var M=0;for(var T=0;T<Ai.length;T++){var Ah=Ai[T];if(P(Ah)){q[Q]=Ah;Q++;Q=Al(this,Ah,Q)}}}return q}};A.extend(A.jqx._jqxGrid.prototype,{_initgroupsheader:function(){this.groupsheader.css("visibility","hidden");if(this._groupsheader()){this.groupsheader.css("visibility","inherit");var C=this;var E=this.gridlocalization.groupsheaderstring;this.groupsheaderdiv=this.groupsheaderdiv||A('<div style="width: 100%; position: relative;"></div>');this.groupsheaderdiv.height(this.groupsheaderheight);this.groupsheaderdiv.css("top",0);this.groupsheader.append(this.groupsheaderdiv);this.groupheadersbounds=new Array();var B=this.groups.length;this.groupsheaderdiv.children().remove();this.groupsheaderdiv[0].innerHTML="";var D=new Array();if(B>0){A.each(this.groups,function(J){var K=this;var M=C._getColumnText(this);var L=M.label;var H=C._rendergroupcolumn(L,K);H.addClass(C.toThemeProperty("jqx-grid-group-column"));C.groupsheaderdiv.append(H);if(C.closeablegroups){var G=A(H.find(".jqx-icon-close"));if(C.isTouchDevice()&&C.touchmode!==true){C.addHandler(G,"touchstart",function(){C.removegroupat(J);return false})}else{C.addHandler(G,"click",function(){C.removegroupat(J);return false})}}if(C.sortable){C.addHandler(H,"click",function(){var O=C.getcolumn(K);if(O!=null){C._togglesort(O)}return false})}D[D.length]=H;C._handlegroupstocolumnsdragdrop(this,H);if(J<B-1){var N=H.height();var I=A('<div style="float: left; position: relative;"></div>');if(C.rtl){I.css("float","right")}I.width(C.groupindentwidth/3);I.height(1);I.css("top",N/2);I.addClass(C.toThemeProperty("jqx-grid-group-column-line"));C.groupsheaderdiv.append(I)}})}else{var F=A('<div style="position: relative;">'+E+"</div>");this.groupsheaderdiv.append(F);if(this.rtl){F.addClass(this.toThemeProperty("jqx-rtl"))}}this._groupheaders=D;this._updategroupheadersbounds()}},_updategroupheadersbounds:function(){var D=this;var C=this.groupsheaderdiv.children().outerHeight();var B=(this.groupsheader.height()-C)/2;this.groupsheaderdiv.css("top",B);if(!this.rtl){this.groupsheaderdiv.css("left",B);this.groupsheaderdiv.css("right","")}else{this.groupsheaderdiv.css("left","");this.groupsheaderdiv.css("right",B)}if(this.rtl){this._groupheaders.reverse()}A.each(this._groupheaders,function(F){var E=this.coord();D.groupheadersbounds[F]={left:E.left,top:E.top,width:this.outerWidth(),height:this.outerHeight(),index:F}})},addgroup:function(C){if(C){var B=this;if(B.groups!==B.dataview.groups){B.dataview.groups=B.groups}B.groups[B.groups.length]=C;B.refreshgroups();this._raiseEvent(12,{type:"Add",index:B.groups[B.groups.length],groups:B.groups})}},insertgroup:function(B,D){if(B!=undefined&&B!=null&&B>=0&&B<=this.groups.length){if(D){var C=this;if(C.groups!==C.dataview.groups){C.dataview.groups=C.groups}C.groups.splice(B,0,D.toString());C.refreshgroups();this._raiseEvent(12,{type:"Insert",index:B,groups:C.groups})}}},refreshgroups:function(){this._refreshdataview();this._render(true,true,true,false);this._postrender("group")},_insertaftergroup:function(B,D){var C=this._getGroupIndexByDataField(B);this.insertgroup(C+1,D)},_insertbeforegroup:function(B,D){var C=this._getGroupIndexByDataField(B);this.insertgroup(C,D)},removegroupat:function(C){if(C>=0&&C!=null&&C!=undefined){var B=this;if(B.groups!==B.dataview.groups){B.dataview.groups=B.groups}B.groups.splice(C,1);B.refreshgroups();if(B.virtualmode){B.updatebounddata()}this._raiseEvent(12,{type:"Remove",index:C,groups:B.groups});return true}return false},cleargroups:function(){var B=this;B.groups=[];B.dataview.groups=B.groups;B.refreshgroups();this._raiseEvent(12,{type:"Clear",index:-1,groups:B.groups});return true},removegroup:function(C){if(C==null){return false}var B=this.groups.indexOf(C.toString());return this.removegroupat(B)},getrootgroupscount:function(){var B=this.dataview.loadedrootgroups.length;return B},collapsegroup:function(E){if(E>=0&&E.toString().indexOf(".")===-1){return this._setrootgroupstate(E,false)}var D=E.toString().split(".");var F=null;if(!this.groupsVisibility){this.groupsVisibility=new Array()}for(var B=0;B<D.length;B++){var E=parseInt(D[B]);if(B==0){var F=this.dataview.loadedrootgroups[E];this.groupsVisibility[F.group]=null}else{var C=F.subGroups[E];if(C){F=C;if(B==D.length-1){this._setgroupstate(F,false,true);this.groupsVisibility[F.group]=null}}}}},expandgroup:function(E){if(E>=0&&E.toString().indexOf(".")===-1){return this._setrootgroupstate(E,true)}var D=E.toString().split(".");var F=null;for(var B=0;B<D.length;B++){var E=parseInt(D[B]);if(B==0){var F=this.dataview.loadedrootgroups[E];this._setrootgroupstate(E,true);if(!this.groupsVisibility){this.groupsVisibility=new Array()}this.groupsVisibility[F.group]=F}else{var C=F.subGroups[E];if(C){F=C;this._setgroupstate(F,true,true);if(!this.groupsVisibility){this.groupsVisibility=new Array()}this.groupsVisibility[F.group]=F}}}},collapseallgroups:function(B){this._setbatchgroupstate(false,B)},expandallgroups:function(B){this._setbatchgroupstate(true,B)},isgroupexpanded:function(D){var B=this.dataview.loadedrootgroups[D];if(B==null){return null}var C=this.expandedgroups[B.uniqueid].expanded;return C},getgroup:function(B){var C=this.dataview.loadedrootgroups[B];if(C==null){return null}var H=this.expandedgroups[C.uniqueid].expanded;var I=C.group;var F=C.level;var G=new Array();this._getsubgroups(G,C);var E=this;var J={group:I,level:F,expanded:H,subgroups:G};if(C.subItems){var D=new Array();A.each(C.subItems,function(){var K=this.boundindex;D[D.length]=E.getrowdata(K)});if(D.length>0){J.subrows=D}}return J},getrootgroups:function(){var B=this.dataview.loadedrootgroups.length;var D=new Array();for(var C=0;C<B;C++){D[C]=this.getgroup(C)}return D},_getsubgroups:function(G,C){var E=this;for(obj in C.subGroups){var I=C.subGroups[obj];var B=E.expandedgroups[I.uniqueid].expanded;var H=I.group;var F=I.level;G[G.length]={group:H,level:F,expanded:B};if(I.subItems){var D=new Array();A.each(I.subItems,function(){var K=this.boundindex;D[D.length]=E.getrowdata(K)});G[G.length-1].subrows=D}if(I.subGroups){var J=new Array();E._getsubgroups(J,I)}}return G},_setbatchgroupstate:function(D,C){var E=this;for(obj in this.dataview.loadedrootgroups){E._setrootgroupstate(obj,D,false,true)}if(C==false){E._requiresupdate=true;E._renderrows(E.virtualsizeinfo);return true}var B=this.vScrollBar[0].style.visibility;this.rendergridcontent(true,false);if(B!=this.vScrollBar[0].style.visibility||this._hiddencolumns){this._updatecolumnwidths();this._updatecellwidths();this._renderrows(this.virtualsizeinfo)}return true},_setrootgroupstate:function(I,E,G,F){if(I==undefined||I==null||I<0){return false}if(!this.groupable||this.groups.length==0){return false}var C=G!=undefined?G:true;if(I>=0&&I<this.dataview.loadedrootgroups.length){var D=this.dataview.loadedrootgroups[I];if(this.pageable){var H=new Array();for(var B=0;B<this.dataview.rows.length;B++){if(this.dataview.rows[B].group!=null&&this.dataview.rows[B].level===0){H.push(this.dataview.rows[B])}}D=H[I];if(!D){return}}return this._setgroupstate(D,E,C,F)}return false},_togglegroupstate:function(C,F){if(C==null||C==undefined){return false}var E=this.vScrollInstance.value;var B=this.expandedgroups[C.uniqueid];if(B==undefined){B=false}else{B=B.expanded}B=!B;if(!this.groupsVisibility){this.groupsVisibility=new Array()}if(B){this.groupsVisibility[C.group]=C}else{this.groupsVisibility[C.group]=null}var D=this._setgroupstate(C,B,F);this._newmax=null;if(E!==0&&this.vScrollBar.css("visibility")!=="hidden"){if(E<=this.vScrollInstance.max){this.vScrollInstance.setPosition(E)}else{this.vScrollInstance.setPosition(this.vScrollInstance.max)}}return D},_setgroupstate:function(H,F,B,G){if(H==null||H==undefined){return false}var D=false;if(this.editable&&this.editcell){this.endcelledit(this.editcell.row,this.editcell.column,false,false)}var C=this.expandedgroups[H.uniqueid];if(C==undefined){C={expanded:false};D=true}if(C.expanded!=F){D=true}if(D){this.expandedgroups[H.uniqueid]={expanded:F,group:H};this._setsubgroupsvisibility(this,H,!F,G);if(B){var E=this.vScrollBar[0].style.visibility;this.rendergridcontent(true,false);if(E!=this.vScrollBar[0].style.visibility||this._hiddencolumns){this._updatecolumnwidths();this._updatecellwidths();this._renderrows(this.virtualsizeinfo)}}if(undefined==this.suspendgroupevents||this.suspendgroupevents==false){if(F){this._raiseEvent(4,{group:H.group,parentgroup:H.parentItem?H.parentItem.group:null,level:H.level,visibleindex:H.visibleindex})}else{this._raiseEvent(5,{group:H.group,parentgroup:H.parentItem?H.parentItem.group:null,level:H.level,visibleindex:H.visibleindex})}}return true}return false},_setgroupitemsvisibility:function(E,C,B){for(var D=0;D<C.subItems.length;D++){E._setrowvisibility(C.subItems[D].visibleindex,B,false)}},_setsubgroupsvisibility:function(F,B,G,C){if(B.parentItem!=null){if(this.hiddens[B.parentItem.visibleindex]){return}}else{if(B.parentItem==null){if(this.hiddens[B.visibleindex]){return}}}for(subGroup in B.subGroups){var D=B.subGroups[subGroup];if(!G){F._setrowvisibility(D.visibleindex,G,false)}var E=!G;if(!C){if(F.expandedgroups[D.uniqueid]==undefined){E=false}else{E=F.expandedgroups[D.uniqueid].expanded}}else{this.expandedgroups[D.uniqueid]={expanded:E,group:D}}if(D.subGroups){if(D.subGroups.length>0){F._setsubgroupsvisibility(F,D,!E||G,C)}else{if(D.subItems.length>0){F._setgroupitemsvisibility(F,D,!E||G)}}}if(G){F._setrowvisibility(D.visibleindex,G,false)}}if(B.subItems&&B.subItems.length>0){F._setgroupitemsvisibility(F,B,G)}},_handlecolumnsdragdrop:function(){var C=this;var B=-1;var G=false;if(!C.groupable){return}var H="mousemove.grouping"+this.element.id;var D="mousedown.grouping"+this.element.id;var E="mouseup.grouping"+this.element.id;var F=false;if(this.isTouchDevice()&&this.touchmode!==true){F=true;H=A.jqx.mobile.getTouchEventName("touchmove")+".grouping"+this.element.id;D=A.jqx.mobile.getTouchEventName("touchstart")+".grouping"+this.element.id;E=A.jqx.mobile.getTouchEventName("touchend")+".grouping"+this.element.id}this.removeHandler(A(document),H);this.addHandler(A(document),H,function(Q){if(!C.showgroupsheader){return true}if(C.dragcolumn!=null){var V=parseInt(Q.pageX);var P=parseInt(Q.pageY);if(F){var I=C.getTouches(Q);var K=I[0];V=parseInt(K.pageX);P=parseInt(K.pageY)}var T=C.host.coord();var M=parseInt(T.left);var N=parseInt(T.top);if(C.dragmousedownoffset==undefined||C.dragmousedownoffset==null){C.dragmousedownoffset={left:0,top:0}}var O=parseInt(V)-parseInt(C.dragmousedownoffset.left);var S=parseInt(P)-parseInt(C.dragmousedownoffset.top);C.dragcolumn.css({left:O+"px",top:S+"px"});G=false;if(V>=M&&V<=M+C.host.width()){if(P>=N&&P<=N+C.host.height()){G=true}}B=-1;if(G){C.dragcolumnicon.removeClass(C.toThemeProperty("jqx-grid-dragcancel-icon"));C.dragcolumnicon.addClass(C.toThemeProperty("jqx-grid-drag-icon"));var J=C.groupsheader.coord();var W=J.top+C.groupsheader.height();var L=A.data(C.dragcolumn[0],"datarecord");if(L){var R=C.groups.indexOf(L.toString())}else{var R=-1}var U=(R==-1)||(C.groups.length>1&&R>-1);if(C.dropline!=null){if(P>=J.top&&P<=W){if(U){B=C._handlegroupdroplines(V)}}else{C.dropline.fadeOut("slow")}}}else{if(C.dropline!=null){C.dropline.fadeOut("slow")}C.dragcolumnicon.removeClass(C.toThemeProperty("jqx-grid-drag-icon"));C.dragcolumnicon.addClass(C.toThemeProperty("jqx-grid-dragcancel-icon"))}if(F){Q.preventDefault();Q.stopPropagation();return false}}});this.removeHandler(A(document),E);this.addHandler(A(document),E,function(Q){if(!C.showgroupsheader){return true}C.__drag=false;A(document.body).removeClass("jqx-disableselect");var U=parseInt(Q.pageX);var J=parseInt(Q.pageY);if(F){var K=C.getTouches(Q);var S=K[0];U=parseInt(S.pageX);J=parseInt(S.pageY)}var R=C.host.coord();var M=parseInt(R.left);var N=parseInt(R.top);var P=C.groupsheader.height();if(C.showtoolbar){N+=C.toolbarheight}C.dragstarted=false;C.dragmousedown=null;if(C.dragcolumn!=null){var T=A.data(C.dragcolumn[0],"datarecord");C.dragcolumn.remove();C.dragcolumn=null;if(T!=null){if(!C.getcolumn(T).groupable){if(C.dropline!=null){C.dropline.remove();C.dropline=null}return}if(G){if(B!=-1){var L=B.index;var I=C.groups[L];var O=C._getGroupIndexByDataField(T);if(O!=L){if(O!=undefined&&O>=0){C.groups.splice(O,1)}if(B.position=="before"){if(!C.rtl){C._insertbeforegroup(I,T)}else{C._insertaftergroup(I,T)}}else{if(!C.rtl){C._insertaftergroup(I,T)}else{C._insertbeforegroup(I,T)}}}}else{if(C.groups.length==0){if(J>N&&J<=N+P){C.addgroup(T)}}else{if(J>N+P){var O=C._getGroupIndexByDataField(T);C.removegroupat(O)}}}}if(C.dropline!=null){C.dropline.remove();C.dropline=null}}}})},_getGroupIndexByDataField:function(B){for(var C=0;C<this.groups.length;C++){if(this.groups[C]==B){return C}}return -1},_isColumnInGroups:function(C){for(var B=0;B<this.groups.length;B++){if(this.groups[B]==C){return true}}return false},_handlegroupdroplines:function(B){var C=this;var D=-1;A.each(C.groupheadersbounds,function(E){if(B<=this.left+this.width/2){var F=this.left-3;if(E>0){F=this.left-1-C.groupindentwidth/6}C.dropline.css("left",F);C.dropline.css("top",this.top);C.dropline.height(this.height);C.dropline.fadeIn("slow");D={index:E,position:"before"};if(C.rtl){D={index:C.groupheadersbounds.length-1-E,position:"before"}}return false}else{if(B>=this.left+this.width/2){C.dropline.css("left",1+this.left+this.width);C.dropline.css("top",this.top);C.dropline.height(this.height);C.dropline.fadeIn("slow");D={index:E,position:"after"};if(C.rtl){D={index:C.groupheadersbounds.length-1-E,position:"after"}}}}});return D},_handlegroupstocolumnsdragdrop:function(G,D){this.dragmousedown=null;this.dragmousedownoffset=null;this.dragstarted=false;this.dragcolumn=null;var H=this;var C;var B="mousedown";var E="mousemove";var F=false;if(this.isTouchDevice()&&this.touchmode!==true){F=true;B=A.jqx.mobile.getTouchEventName("touchstart");E=A.jqx.mobile.getTouchEventName("touchmove")}this.addHandler(D,"dragstart",function(I){return false});this.addHandler(D,B,function(I){if(!H.showgroupsheader){return true}var K=I.pageX;var N=I.pageY;H.__drag=true;H.dragmousedown={left:K,top:N};if(F){var M=H.getTouches(I);var J=M[0];K=J.pageX;N=J.pageY;H.dragmousedown={left:K,top:N};if(I.preventDefault){I.preventDefault()}}var L=A(I.target).coord();H.dragmousedownoffset={left:parseInt(K)-parseInt(L.left),top:parseInt(N-L.top)}});this.addHandler(D,E,function(I){if(!H.showgroupsheader){return true}if(H.dragmousedown){C={left:I.pageX,top:I.pageY};if(F){var M=H.getTouches(I);var K=M[0];C={left:K.pageX,top:K.pageY}}if(!H.dragstarted&&H.dragcolumn==null){var L=Math.abs(C.left-H.dragmousedown.left);var J=Math.abs(C.top-H.dragmousedown.top);if(L>3||J>3){H._createdragcolumn(D,C,true);A(document.body).addClass("jqx-disableselect");A.data(H.dragcolumn[0],"datarecord",G);if(I.preventDefault){I.preventDefault()}}}}})},_createdragcolumn:function(G,B,I){var D=this;var H=B;D.dragcolumn=A("<div></div>");var C=G.clone();D.dragcolumn.css("z-index",999999);C.css("border-width","1px");C.css("opacity","0.4");var E=A(C.find("."+D.toThemeProperty("jqx-grid-column-menubutton")));if(E.length>0){E.css("display","none")}var F=A(C.find(".jqx-icon-close"));if(F.length>0){F.css("display","none")}D.dragcolumnicon=A('<div style="z-index: 9999; position: absolute; left: 100%; top: 50%; margin-left: -18px; margin-top: -7px;"></div>');D.dragcolumnicon.addClass(D.toThemeProperty("jqx-grid-drag-icon"));D.dragcolumn.css("float","left");D.dragcolumn.css("position","absolute");var J=D.host.coord();C.width(G.width()+16);D.dragcolumn.append(C);D.dragcolumn.height(G.height());D.dragcolumn.width(C.width());D.dragcolumn.append(D.dragcolumnicon);A(document.body).append(D.dragcolumn);C.css("margin-left",0);C.css("left",0);C.css("top",0);D.dragcolumn.css("left",H.left+D.dragmousedown.left);D.dragcolumn.css("top",H.top+D.dragmousedown.top);if(I!=undefined&&I){D.dropline=A('<div style="display: none; position: absolute;"></div>');D.dropline.width(2);D.dropline.addClass(D.toThemeProperty("jqx-grid-group-drag-line"));A(document.body).append(D.dropline)}},iscolumngroupable:function(B){return this._getcolumnproperty(B,"groupable")},_handlecolumnstogroupsdragdrop:function(F,G){this.dragmousedown=null;this.dragmousedownoffset=null;this.dragstarted=false;this.dragcolumn=null;var B=this;var D;var E=false;if(this.isTouchDevice()&&this.touchmode!==true){E=true}var C="mousedown.drag";var D="mousemove.drag";if(E){C=A.jqx.mobile.getTouchEventName("touchstart")+".drag";D=A.jqx.mobile.getTouchEventName("touchmove")+".drag"}else{this.addHandler(G,"dragstart",function(H){return false})}this.addHandler(G,C,function(L){if(!B.showgroupsheader){return true}B.__drag=true;if(B._isColumnInGroups(F.displayfield)){if(G.css("cursor")!="col-resize"){return true}else{return true}}if(false==F.groupable){return true}var I=L.pageX;var H=L.pageY;if(E){var M=B.getTouches(L);var K=M[0];I=K.pageX;H=K.pageY}B.dragmousedown={left:I,top:H};if(E){if(L.preventDefault){L.preventDefault()}}var J=A(L.target).coord();B.dragmousedownoffset={left:parseInt(I)-parseInt(J.left),top:parseInt(H-J.top)}});this.addHandler(G,D,function(M){if(!B.showgroupsheader){return true}if(B._isColumnInGroups(F.displayfield)){if(G.css("cursor")!="col-resize"){return true}else{return true}}if(B.dragmousedown){var I=M.pageX;var H=M.pageY;if(E){var K=B.getTouches(M);var J=K[0];I=J.pageX;H=J.pageY}D={left:I,top:H};if(!B.dragstarted&&B.dragcolumn==null){var N=Math.abs(D.left-B.dragmousedown.left);var L=Math.abs(D.top-B.dragmousedown.top);if(N>3||L>3){B._createdragcolumn(G,D,true);A.data(B.dragcolumn[0],"datarecord",F.displayfield);if(M.preventDefault){M.preventDefault()}}}}})},_rendergroupcolumn:function(B,C){var D=A('<div style="float: left; position: relative;"></div>');if(this.rtl){D.css("float","right")}if(this.groupcolumnrenderer!=null){D[0].innerHTML=this.groupcolumnrenderer(B);D.addClass(this.toThemeProperty("jqx-grid-group-column"));D.addClass(this.toThemeProperty("jqx-fill-state-normal"))}if(this.closeablegroups){if(D[0].innerHTML==""){D[0].innerHTML='<a style="float: left;" href="#">'+B+"</a>"}if(this.rtl){D[0].innerHTML='<a style="float: right;" href="#">'+B+"</a>"}var E=!this.rtl?"right":"left";var G='<div style="float: '+E+'; min-height: 16px; min-width: 18px;"><div style="position: absolute; left: 100%; top: 50%; margin-left: -18px; margin-top: -8px; float: none; width: 16px; height: 16px;" class="'+this.toThemeProperty("jqx-icon-close")+'"></div></div>';if(A.jqx.browser.msie&&A.jqx.browser.version<8){G='<div style="float: left; min-height: 16px; min-width: 18px;"><div style="position: absolute; left: 100%; top: 50%; margin-left: -18px; margin-top: -8px; float: none; width: 16px; height: 16px;" class="'+this.toThemeProperty("jqx-icon-close")+'"></div></div>'}if(this.rtl){var G='<div style="float: '+E+'; min-height: 16px; min-width: 18px;"><div style="position: absolute; left: 0px; top: 50%; margin-top: -8px; float: none; width: 16px; height: 16px;" class="'+this.toThemeProperty("jqx-icon-close")+'"></div></div>';if(A.jqx.browser.msie&&A.jqx.browser.version<8){G='<div style="float: left; min-height: 16px; min-width: 18px;"><div style="position: absolute; left: 0px; top: 50%; margin-top: -8px; float: none; width: 16px; height: 16px;" class="'+this.toThemeProperty("jqx-icon-close")+'"></div></div>'}}D[0].innerHTML+=G}else{if(D[0].innerHTML==""){D[0].innerHTML='<a href="#">'+B+"</a>"}}if(this.sortable){var H=A('<div style="float: right; min-height: 16px; min-width: 18px;"><div style="position: absolute; left: 100%; top: 50%; margin-left: -16px; margin-top: -8px; float: none; width: 16px; height: 16px;" class="'+this.toThemeProperty("jqx-grid-column-sortascbutton")+'"></div></div>');var F=A('<div style="float: right; min-height: 16px; min-width: 18px;"><div style="position: absolute; left: 100%; top: 50%; margin-left: -16px; margin-top: -8px; float: none; width: 16px; height: 16px;" class="'+this.toThemeProperty("jqx-grid-column-sortdescbutton")+'"></div></div>');if(this.closeablegroups){var H=A('<div style="float: right; min-height: 16px; min-width: 18px;"><div style="position: absolute; left: 100%; top: 50%; margin-left: -32px; margin-top: -8px; float: none; width: 16px; height: 16px;" class="'+this.toThemeProperty("jqx-grid-column-sortascbutton")+'"></div></div>');var F=A('<div style="float: right; min-height: 16px; min-width: 18px;"><div style="position: absolute; left: 100%; top: 50%; margin-left: -32px; margin-top: -8px; float: none; width: 16px; height: 16px;" class="'+this.toThemeProperty("jqx-grid-column-sortdescbutton")+'"></div></div>')}if(this.rtl){var H=A('<div style="float: right; min-height: 16px; min-width: 18px;"><div style="position: absolute; left: 0px; top: 50%; margin-left: 0px; margin-top: -8px; float: none; width: 16px; height: 16px;" class="'+this.toThemeProperty("jqx-grid-column-sortascbutton")+'"></div></div>');var F=A('<div style="float: right; min-height: 16px; min-width: 18px;"><div style="position: absolute; left: 0px; top: 50%; margin-left: 0px; margin-top: -8px; float: none; width: 16px; height: 16px;" class="'+this.toThemeProperty("jqx-grid-column-sortdescbutton")+'"></div></div>');if(this.closeablegroups){var H=A('<div style="float: right; min-height: 16px; min-width: 18px;"><div style="position: absolute; left: 0px; top: 50%; margin-left: 16px; margin-top: -8px; float: none; width: 16px; height: 16px;" class="'+this.toThemeProperty("jqx-grid-column-sortascbutton")+'"></div></div>');var F=A('<div style="float: right; min-height: 16px; min-width: 18px;"><div style="position: absolute; left: 0px; top: 50%; margin-left: 16px; margin-top: -8px; float: none; width: 16px; height: 16px;" class="'+this.toThemeProperty("jqx-grid-column-sortdescbutton")+'"></div></div>')}}H.css("display","none");F.css("display","none");if(A.jqx.browser.msie&&A.jqx.browser.version<8){H.css("float","left");F.css("float","left")}D.append(H);D.append(F);A.data(document.body,"groupsortelements"+C,{sortasc:H,sortdesc:F})}D.addClass(this.toThemeProperty("jqx-fill-state-normal"));D.addClass(this.toThemeProperty("jqx-grid-group-column"));return D},_rendergroup:function(L,Ak,X,N,R,Ac,Q){var Aa=Ak;var Y=Ak.cells[X.level];if(this.rtl){Y=Ak.cells[Ak.cells.length-1-X.level]}var Af=this._findgroupstate(X.uniqueid);if(X.bounddata.subGroups.length>0||X.bounddata.subItems.length>0){var T=this.rtl?"-rtl":"";var Ad=this.toThemeProperty("jqx-icon-arrow-right");if(T){Ad=this.toThemeProperty("jqx-icon-arrow-left")}if(Af){Y.className+=" "+this.toThemeProperty("jqx-grid-group-expand"+T)+" "+this.toThemeProperty("jqx-icon-arrow-down")}else{Y.className+=" "+this.toThemeProperty("jqx-grid-group-collapse"+T)+" "+Ad}}var P=this._getColumnText(this.groups[X.level]).label;var j=this.groupindentwidth;var J=this.rowdetails&&this.showrowdetailscolumn?(1+L)*j:(L)*j;var Al=Q-J;var a=X.level+1;if(this.rtl){a=0}var U=Aa.cells[a];var Ae=2;while(U!=undefined&&U.style.display=="none"&&Ae<Aa.cells.length-1){U=Aa.cells[a+Ae-1];Ae++}var Z=A(U);if(!U){return}U.style.width=parseInt(Al)+"px";if(U.className.indexOf("jqx-grid-cell-filter")!=-1){Z.removeClass(this.toThemeProperty("jqx-grid-cell-filter"))}if(U.className.indexOf("jqx-grid-cell-sort")!=-1){Z.removeClass(this.toThemeProperty("jqx-grid-cell-sort"))}if(U.className.indexOf("jqx-grid-cell-pinned")!=-1){Z.removeClass(this.toThemeProperty("jqx-grid-cell-pinned"))}if(this.groupsrenderer!=null){var S={group:X.group,level:X.level,parent:X.bounddata.parentItem,subGroups:X.bounddata.subGroups,subItems:X.bounddata.subItems,groupcolumn:this._getColumnText(this.groups[X.level]).column};var M=this.groupsrenderer(P+": "+X.group,X.group,Af,S);if(M){U.innerHTML=M}else{var W=X.bounddata.subItems.length>0?X.bounddata.subItems.length:X.bounddata.subGroups.length;if(this.showgroupaggregates){var Ab=X.bounddata.subItems.length>0?X.bounddata.subItems:X.bounddata.subGroups;W=0;for(var Ag=0;Ag<Ab.length;Ag++){if(Ab[Ag].totalsrow){continue}W++}}U.innerHTML='<div class="'+this.toThemeProperty("jqx-grid-groups-row")+'" style="position: absolute;"><span>'+P+': </span><span class="'+this.toThemeProperty("jqx-grid-groups-row-details")+'">'+X.group+" ("+W+")</span></div>"}}else{var m=this._getcolumnbydatafield(this.groups[X.level]);var Aj=X.group;if(m!=null){if(m.cellsformat){if(A.jqx.dataFormat){if(A.jqx.dataFormat.isDate(Aj)){Aj=A.jqx.dataFormat.formatdate(Aj,m.cellsformat,this.gridlocalization)}else{if(A.jqx.dataFormat.isNumber(Aj)){Aj=A.jqx.dataFormat.formatnumber(Aj,m.cellsformat,this.gridlocalization)}}}}var W=X.bounddata.subItems.length>0?X.bounddata.subItems.length:X.bounddata.subGroups.length;if(this.showgroupaggregates){var Ab=X.bounddata.subItems.length>0?X.bounddata.subItems:X.bounddata.subGroups;W=0;for(var Ag=0;Ag<Ab.length;Ag++){if(Ab[Ag].totalsrow){continue}W++}}U.innerHTML='<div class="'+this.toThemeProperty("jqx-grid-groups-row")+'" style="position: absolute;"><span>'+P+': </span><span class="'+this.toThemeProperty("jqx-grid-groups-row-details")+'">'+Aj+" ("+W+")</span></div>"}else{throw new Error("jqxGrid: Unable to find '"+this.groups[X.level]+"' group in the Grid's columns collection.")}}if(this.rtl){if(!m){m=this._getcolumnbydatafield(this.groups[X.level])}var K=this.hScrollBar.css("visibility")=="hidden"?0:this.hScrollInstance.max-this.hScrollInstance.value;var Ah=this.vScrollBar.css("visibility")=="hidden"?0:this.scrollbarsize+6;var J=this.rowdetails&&this.showrowdetailscolumn?(2+X.level)*j:(1+X.level)*j;U.style.width=Q+parseInt(K)-J-Ah+"px";Z.addClass(this.toThemeProperty("jqx-rtl"));var O=A(Ak.cells[Ak.cells.length-1]).css("z-index");Z.css("z-index",O);var Ai=Z.find("div");var Al=Ai.width();Ai.css("left","100%");var V=this.columns.records[Ak.cells.length-2-X.level]!=null?this.columns.records[Ak.cells.length-2-X.level].pinned:false;if(this.table.width()<Q){Q=this.table.width();if(this.vScrollBar.css("visibility")!="hidden"){Q+=this.vScrollBar.outerWidth()}}if(m.pinned||V){if(this.rowdetails&&this.showrowdetailscolumn){Q+=30}Ai.css("margin-left",-Al);U.style.width=Q+K-J-Ah+"px"}else{var K=this.hScrollBar.css("visibility")=="hidden"?0:this.hScrollInstance.max;U.style.width=Q+K-J-Ah+"px";var Al=Ai.width();Ai.css("margin-left",-Al)}}}})})(jqxBaseFramework);