(function(A){A.jqx.jqxWidget("jqxTextArea","",{});A.extend(A.jqx._jqxTextArea.prototype,{defineInstance:function(){var C=this;var B={disabled:false,filter:C._filter,sort:C._sort,highlight:C._highlight,dropDownWidth:null,renderer:C._renderer,opened:false,$popup:document.createElement("ul"),source:[],roundedCorners:true,searchMode:"default",placeHolder:"",width:null,height:null,rtl:false,displayMember:"",valueMember:"",popupZIndex:20000,items:8,minLength:1,maxLength:null,scrollBarSize:A.jqx.utilities.scrollBarSize,query:"",changeType:null,events:["change","select","open","close"]};A.extend(true,C,B);return B},createInstance:function(){var B=this;B._textareaWidthFix=0;if(A.jqx.browser.chrome){B._textareaWidthFix=6}else{if(A.jqx.browser.msie){B._textareaWidthFix=3}}if(document.body.contains(B.element)===false){B._notInDOM=true}B._popupHelper=A(B.$popup);B.render();B.isInitialized=true},render:function(){var D=this;if(D.isInitialized===true){D.refresh();return}if(A.jqx.utilities.scrollBarSize!==15){D.scrollBarSize=A.jqx.utilities.scrollBarSize}var F=document.createElement("div");F.style.overflow="hidden";F.style.width="100%";F.style.height="100%";F.style.backgroundColor="transparent";F.style["-webkit-appearance"]="none";F.style.outline="none";F.style.align="left";F.style.border="0px";F.style.padding="0px";F.style.margin="0px";F.style.left="0px";F.style.top="0px";F.style.valign="top";F.style.position="relative";var C=document.createElement("div");C.style.align="left";C.style.valign="top";C.style.left="0px";C.style.top="0px";C.style.position="absolute";D._baseHost=D.host;A.jqx.utilities.resize(D._baseHost,function(){if(D._notInDOM){D._notInDOM=false;if(D.element.nodeName.toLowerCase()==="textarea"){D.isInitialized=false;D.render()}return}D._ttimer=setTimeout(function(){D.textarea.style.width="";D._arrange()},100)},false,true);if(D.element.tagName.toLowerCase()==="div"){D.element.appendChild(F);var B=document.createElement("textarea");B.className=D.toThemeProperty("jqx-text-area-element");D.textarea=B;F.appendChild(B);F.appendChild(C);D.wrapper=D.element}else{if(D.element.tagName.toLowerCase()==="textarea"){if(D._notInDOM){return}D.textarea=D.element;var E=document.createElement("div");D.element.parentNode.insertBefore(E,D.element);E.appendChild(F);F.appendChild(D.element);F.appendChild(C);var H=D.host.data();D.host=A(E);D.host.data(H);E.style.cssText=D.element.style.cssText;D.element.style.cssText="";D.element.className=D.toThemeProperty("jqx-text-area-element");D.wrapper=E;E.setAttribute("id",D.element.id);D.element=E;D.textarea.setAttribute("id",D.element.id+"TextArea")}}var G=D.host;D._addClasses();if(!G.jqxButton){throw new Error("jqxTextArea: Missing reference to jqxbuttons.js.")}if(!G.jqxScrollBar){throw new Error("jqxTextArea: Missing reference to jqxscrollbar.js.")}if(null===D.width&&D.element.style&&null!==D.element.style.width){D.width=D.element.style.width}if(null===D.height&&D.element.style&&null!==D.element.style.height){D.height=D.element.style.height}D._setSize();D.vScrollBar=A(C);D.vScrollBar.jqxScrollBar({vertical:true,width:15,height:"100%",max:D.height,theme:D.theme});if(A.trim(D.textarea.value)===""){D.textarea.value=""}D.textarea.setAttribute("placeholder",D.placeHolder);if(D.maxLength!==null){D.textarea.setAttribute("maxlength",D.maxLength)}if(A.jqx.browser.msie&&A.jqx.browser.version<10&&D.textarea.value===""){D.textarea.value=D.placeHolder}if((D.source instanceof Array&&D.source.length)||D.source._source||A.isFunction(D.source)){D._oldsource=D.source;D._updateSource();D._addPopupClasses();A.jqx.aria(D,"aria-haspopup",true)}D._arrange();D._addHandlers()},refresh:function(C){if(C!==true){var B=this;B._setSize();B._arrange();B._removeHandlers();B._addHandlers();if(B.opened===true){B.open()}}},_arrange:function(){var C=this;var B=C.textarea;var E=B.scrollHeight-C._height(B);var D=Math.max(0,E);C.vScrollBar.jqxScrollBar({max:D,value:B.scrollTop});if(E<5){B.style.width=this._toPx(C._width(C.element));C.vScrollBar[0].style.visibility="hidden"}else{B.style.width=this._toPx(C._width(C.element)-C.scrollBarSize-C._textareaWidthFix);C.vScrollBar[0].style.visibility="visible";C._arrangeScrollbars(C.scrollBarSize)}},val:function(C){var B=this,E=B.textarea,F=E.value,D;if(A.jqx.browser.msie&&A.jqx.browser.version<10&&F===B.placeHolder){F=""}if(arguments.length===0||(typeof C==="object"&&A.isEmptyObject(C)===true)){if(B.displayMember!==""&&B.valueMember!==""&&B.selectedItem){if(F===""){return""}return B.selectedItem}return F}if(C&&C.label){if(B.selectedItem&&C.label===B.selectedItem.label&&C.value===B.selectedItem.value){return C.label}B.selectedItem={label:C.label,value:C.value};B.element.setAttribute("data-value",C.value);B.element.setAttribute("data-label",C.label);E.value=C.label;D=C.label}else{if(F===C){return C}E.value=C;B.element.setAttribute("data-value",C);B.element.setAttribute("data-label",C);D=C}B._arrange();B._raiseEvent("0");return D},focus:function(){this.textarea.focus()},selectAll:function(){var B=this.textarea;setTimeout(function(){if("selectionStart" in B){B.focus();B.setSelectionRange(0,B.value.length)}else{var C=B.createTextRange();C.collapse(true);C.moveEnd("character",B.value.length);C.moveStart("character",0);C.select()}},10)},_arrangeScrollbars:function(I){var F=this;var B=F._width(F.element);var G=F._height(F.element);var C=F.vScrollBar,D=C[0];var E=D.style.visibility!=="hidden";var K=2;var J=2;C.jqxScrollBar({width:I,height:parseInt(G,10)-K});D.style.left=(B-I-K-J)+"px";D.style.top="0px";var H=F._width(F.element)-F.vScrollBar.outerWidth();if(F.rtl){D.style.left="0px";var L=E?(parseInt(I,10)+3)+"px":0;F.textarea.style.paddingLeft=F._toPx(L);F.textarea.style.width=F._toPx(H-4)}else{if(C.css("visibility")!=="hidden"){F.textarea.style.width=this._toPx(H-F._textareaWidthFix)}}C.jqxScrollBar("refresh")},destroy:function(){var B=this;B._popupHelper.remove();B.vScrollBar.jqxScrollBar("destroy");B._removeHandlers();B.host.remove()},propertiesChangedHandler:function(B,C,D){if(D&&D.width&&D.height&&Object.keys(D).length==2){B.element.style.width=B._toPx(B.width);B.element.style.height=B._toPx(B.height);B._arrange()}},propertyChangedHandler:function(D,E,C,B){if(D.isInitialized===undefined||D.isInitialized===false){return}if(D.batchUpdate&&D.batchUpdate.width&&D.batchUpdate.height&&Object.keys(D.batchUpdate).length==2){return}if(B!==C){switch(E){case"theme":D.vScrollBar.jqxScrollBar({theme:D.theme});break;case"width":case"height":D.element.style[E]=D._toPx(B);D._arrange();break;case"source":D._oldsource=B;D._updateSource();break;case"displayMember":case"valueMember":D.source=D._oldsource;D._updateSource();break;case"opened":if(B===true){D.open()}else{D.close()}break;case"maxLength":D.textarea.setAttribute("maxlength",B);break;case"placeHolder":D.textarea.setAttribute("placeholder",B);if(A.jqx.browser.msie&&A.jqx.browser.version<10&&D.textarea.value===C){D.textarea.value=B}break;case"scrollBarSize":D._arrange();break;case"dropDownWidth":D.$popup.style.width=D._toPx(B);break;case"roundedCorners":if(B===true){D.element.className+=" "+D.toThemeProperty("jqx-rc-all");D.$popup.className+=" "+D.toThemeProperty("jqx-rc-all")}else{D.host.removeClass(D.toThemeProperty("jqx-rc-all"));D._popupHelper.removeClass(D.toThemeProperty("jqx-rc-all"))}break;case"disabled":D.vScrollBar.jqxScrollBar({disabled:B});if(B===true){D.element.className+=" "+D.toThemeProperty("jqx-fill-state-disabled");D.textarea.setAttribute("disabled","disabled")}else{D.host.removeClass(D.toThemeProperty("jqx-fill-state-disabled"));D.textarea.removeAttribute("disabled")}A.jqx.aria(D,"aria-disabled",B);break;case"rtl":if(B===true){D.textarea.className+=" "+D.toThemeProperty("jqx-text-area-element-rtl")}else{A(D.textarea).removeClass(D.toThemeProperty("jqx-text-area-element-rtl"))}D._arrange();break;default:D.refresh();break}}},_raiseEvent:function(B,G){var D=this;if(G===undefined){G={owner:null}}var C=D.events[B];G.owner=D;var H=new A.Event(C);H.owner=D;if(B===0){G.type=this.changeType;this.changeType=null}H.args=G;if(H.preventDefault){H.preventDefault()}var E;if(C==="change"||D._baseHost[0].tagName.toLowerCase()==="div"){E=D.host}else{E=D._baseHost}var F=E.trigger(H);return F},_addHandlers:function(){var C=this,F=C.element.id,B=C.host,E=C.textarea;var D=A.jqx.browser.mozilla?"wheel":"mousewheel";C.addHandler(B,D+".jqxTextArea"+F,function(G){C.wheel(G,C)});C.addHandler(B,"mouseenter.jqxTextArea"+F,function(){C.focused=true});C.addHandler(B,"mouseleave.jqxTextArea"+F,function(){C.focused=false});C.addHandler(B,"focus.jqxTextArea"+F,function(){C.focused=true});C.addHandler(B,"blur.jqxTextArea"+F,function(){C.focused=false});C.addHandler(C.wrapper,"scroll.jqxTextArea"+F,function(){if(C.wrapper.scrollTop!==0){C.wrapper.scrollTop=0}if(C.wrapper.scrollLeft!==0){C.wrapper.scrollLeft=0}});C.addHandler(E,"change.jqxTextArea"+F,function(G){G.stopPropagation();G.preventDefault();C._arrange();C._raiseEvent("0")});C.addHandler(E,"select.jqxTextArea"+F,function(G){G.stopPropagation();G.preventDefault()});C.addHandler(E,"scroll.jqxTextArea"+F,function(){var G=Math.max(0,E.scrollHeight-C._height(E));C.vScrollBar.jqxScrollBar({max:G,value:E.scrollTop})});C.addHandler(E,"focus.jqxTextArea"+F,function(){C.element.className+=" "+C.toThemeProperty("jqx-fill-state-focus");if(A.jqx.browser.msie&&A.jqx.browser.version<10&&E.value===C.placeHolder){E.value=""}});C.addHandler(E,"blur.jqxTextArea"+F,function(){C.host.removeClass(C.toThemeProperty("jqx-fill-state-focus"));if(A.jqx.browser.msie&&A.jqx.browser.version<10){var G=C.textarea.value;if(G===""){C.textarea.value=C.placeHolder}else{if(C.maxLength!==null&&G.length>C.maxLength){C.textarea.value=G.substr(0,C.maxLength)}}}});C.addHandler(E,"keydown.jqxTextArea"+F,function(G){C._suppressKeyPressRepeat=~A.inArray(G.keyCode,[40,38,9,13,27]);C.changeType="keyboard";C._move(G)});C.addHandler(E,"keypress.jqxTextArea"+F,function(G){if(C.maxLength!==null&&A.jqx.browser.msie&&A.jqx.browser.version<10&&E.value.length>C.maxLength){return false}if(C._suppressKeyPressRepeat){return}C._move(G)});C.addHandler(E,"keyup.jqxTextArea"+F,function(G){switch(G.keyCode){case 40:case 38:case 16:case 17:case 18:break;case 9:case 13:if(!C.opened){return}C._select();break;case 27:if(!C.opened){return}C.close();break;default:if(C.timer){clearTimeout(C.timer)}C.timer=setTimeout(function(){C._suggest()},300)}G.stopPropagation();G.preventDefault();C._arrange()});C.addHandler(C.vScrollBar,"valueChanged.jqxTextArea"+F,function(G){E.scrollTop=G.currentValue});C.addHandler(C.$popup,"mousedown.jqxTextArea"+F,function(G){G.stopPropagation();G.preventDefault();C.changeType="mouse";C._select()})},_removeHandlers:function(){var B=this,C=B.element.id,E=B.host,D=B.textarea;A.jqx.utilities.resize(B._baseHost,null,true);B.removeHandler(E,"mousewheel.jqxTextArea"+C);B.removeHandler(E,"mouseenter.jqxTextArea"+C);B.removeHandler(E,"mouseleave.jqxTextArea"+C);B.removeHandler(E,"focus.jqxTextArea"+C);B.removeHandler(E,"blur.jqxTextArea"+C);B.removeHandler(B.wrapper,"scroll.jqxTextArea"+C);B.removeHandler(D,"change.jqxTextArea"+C);B.removeHandler(D,"select.jqxTextArea"+C);B.removeHandler(D,"scroll.jqxTextArea"+C);B.removeHandler(D,"focus.jqxTextArea"+C);B.removeHandler(D,"blur.jqxTextArea"+C);B.removeHandler(D,"keydown.jqxTextArea"+C);B.removeHandler(D,"keypress.jqxTextArea"+C);B.removeHandler(D,"keyup.jqxTextArea"+C);B.removeHandler(B.vScrollBar,"valueChanged.jqxTextArea"+C);B.removeHandler(B.$popup,"mousedown.jqxTextArea"+C)},_itemHandler:function(B){A(this._find("jqx-fill-state-pressed",this._popupHelper)).removeClass(this.toThemeProperty("jqx-fill-state-pressed"));B.currentTarget.className+=" "+this.toThemeProperty("jqx-fill-state-pressed")},wheel:function(B,E){var C=0;if(B.originalEvent&&A.jqx.browser.msie&&B.originalEvent.wheelDelta){C=B.originalEvent.wheelDelta/120}if(!B){B=window.event}if(B.wheelDelta){C=B.wheelDelta/120}else{if(B.detail){C=-B.detail/3}else{if(B.originalEvent.wheelDelta){C=B.originalEvent.wheelDelta/120}else{if(B.originalEvent.detail){C=-B.originalEvent.detail/3}else{if(B.originalEvent.deltaY){C=-B.originalEvent.deltaY/3}}}}}if(C){var D=E._handleDelta(C);if(!D){if(B.preventDefault){B.preventDefault()}}if(!D){return D}else{return false}}if(B.preventDefault){B.preventDefault()}B.returnValue=false},_handleDelta:function(F){var E=this,B=E.vScrollBar.jqxScrollBar("getInstance");if(E.focused){var C=B.value;if(F<0){E.scrollDown()}else{E.scrollUp()}var D=B.value;if(C!==D){return false}}return true},scrollDown:function(){var D=this;if(D.vScrollBar.css("visibility")==="hidden"){return false}var B=D.vScrollBar.jqxScrollBar("getInstance");var C=Math.min(B.value+B.largestep,B.max);B.setPosition(C);D._arrange();return true},scrollUp:function(){var D=this;if(D.vScrollBar.css("visibility")==="hidden"){return false}var B=D.vScrollBar.jqxScrollBar("getInstance");var C=Math.max(B.value-B.largestep,B.min);B.setPosition(C);D._arrange();return true},_setSize:function(){var B=this;B.element.style.width=B._toPx(B.width);B.element.style.height=B._toPx(B.height)},_addClasses:function(){var C=this,B="jqx-panel jqx-widget jqx-widget-content jqx-text-area";C.textarea.className+=" "+C.toThemeProperty("jqx-widget jqx-widget-content");if(C.roundedCorners===true){B+=" jqx-rc-all"}if(C.disabled===true){B+=" jqx-fill-state-disabled";C.textarea.setAttribute("disabled","disabled");A.jqx.aria(C,"aria-disabled",true)}else{A.jqx.aria(C,"aria-disabled",false)}if(C.rtl===true){C.textarea.className+=" "+C.toThemeProperty("jqx-text-area-element-rtl")}C.element.className+=" "+C.toThemeProperty(B)},_addPopupClasses:function(){var C=this,B="jqx-popup jqx-input-popup jqx-menu jqx-menu-vertical jqx-menu-dropdown jqx-widget jqx-widget-content";if(A.jqx.browser.msie){B+=" jqx-noshadow"}if(C.roundedCorners){B+=" jqx-rc-all"}C.$popup.className+=" "+C.toThemeProperty(B)},_updateSource:function(){var B=this;var C=function(F){if(F===undefined){return null}if(typeof F==="string"||F instanceof String){return{label:F,value:F}}if(typeof F!=="string"&&F instanceof String===false){var H="";var G="";if(B.displayMember!==""&&B.displayMember!==undefined){if(F[B.displayMember]){H=F[B.displayMember]}}if(B.valueMember!==""&&B.valueMember!==undefined){G=F[B.valueMember]}if(H===""){H=F.label}if(G===""){G=F.value}return{label:H,value:G}}return F};var D=function(F){var H=[];for(var G=0;G<F.length;G++){H[G]=C(F[G])}return H};if(this.source&&this.source._source){this.adapter=this.source;if(this.adapter._source.localdata!=null){this.adapter.unbindBindingUpdate(this.element.id);this.adapter.bindBindingUpdate(this.element.id,function(){B.source=D(B.adapter.records)})}else{var E={};if(this.adapter._options.data){A.extend(B.adapter._options.data,E)}else{if(this.source._source.data){A.extend(E,this.source._source.data)}this.adapter._options.data=E}this.adapter.unbindDownloadComplete(this.element.id);this.adapter.bindDownloadComplete(this.element.id,function(){B.source=D(B.adapter.records)})}this.source.dataBind();return}if(!A.isFunction(this.source)){this.source=D(this.source)}},open:function(){if(A.jqx.isHidden(this.host)){return}var E=A.extend({},this.host.coord(true),{height:this.element.offsetHeight});if(this.$popup.parentNode!==document.body){var C=this.element.id+"_popup";this.$popup.id=C;A.jqx.aria(this,"aria-owns",C);document.body.appendChild(this.$popup)}this.$popup.style.position="absolute";this.$popup.style.zIndex=this.popupZIndex;this.$popup.style.top=this._toPx(E.top+E.height);this.$popup.style.left=this._toPx(E.left);this.$popup.style.display="block";var D=0;var B=this._popupHelper.children();A.each(B,function(){D+=A(this).outerHeight()+1});this.$popup.style.height=this._toPx(D);this.opened=true;this._raiseEvent("2",{popup:this.$popup});A.jqx.aria(this,"aria-expanded",true);return this},close:function(){this.$popup.style.display="none";this.opened=false;this._raiseEvent("3",{popup:this.$popup});A.jqx.aria(this,"aria-expanded",false);return this},_suggest:function(){var C=this,B;C.query=C.textarea.value;if(!C.query||C.query.length<C.minLength){return C.opened?C.close():C}if(A.isFunction(C.source)){B=C.source(C.query,A.proxy(C._load,this))}else{B=C.source}if(B){return C._load(B)}return C},_load:function(B){var C=this;B=A.grep(B,function(D){return C.filter(D)});B=C.sort(B);if(!B.length){if(C.opened){return C.close()}else{return C}}return C._render(B.slice(0,C.items)).open()},_filter:function(E){var D=this;var B=D.query;var C=E;if(E.label!==undefined){C=E.label}else{if(D.displayMember){C=E[D.displayMember]}}switch(D.searchMode){case"none":break;case"contains":return A.jqx.string.contains(C,B);case"equals":return A.jqx.string.equals(C,B);case"equalsignorecase":return A.jqx.string.equalsIgnoreCase(C,B);case"startswith":return A.jqx.string.startsWith(C,B);case"startswithignorecase":return A.jqx.string.startsWithIgnoreCase(C,B);case"endswith":return A.jqx.string.endsWith(C,B);case"endswithignorecase":return A.jqx.string.endsWithIgnoreCase(C,B);default:return A.jqx.string.containsIgnoreCase(C,B)}},_sort:function(E){var H=this,C=[],I=[],G=[];for(var F=0;F<E.length;F++){var B=E[F];var D=B;if(B.label){D=B.label}else{if(H.displayMember){D=B[H.displayMember]}}if(D.toString().toLowerCase().indexOf(H.query.toString().toLowerCase())===0){C.push(B)}else{if(D.toString().indexOf(H.query)>=0){I.push(B)}else{if(D.toString().toLowerCase().indexOf(H.query.toString().toLowerCase())>=0){G.push(B)}}}}return C.concat(I,G)},_render:function(G){var D=this,F=D._popupHelper.children();if(F.length>0){for(var C=0;C<F.length;C++){A(F[C]).remove()}}var B=function(L,J){var H=L,O=document.createElement("li"),I=document.createElement("a"),K,M;I.setAttribute("href","#");O.appendChild(I);if(L.value!==undefined&&L.value!==null){if(L.label!==undefined&&L.label!==null){K=L.label;M=L.value}else{K=L.value;M=L.value}}else{if(L.label!==undefined&&L.label!==null){K=L.label;M=L.label}else{if(D.displayMember!==undefined&&D.displayMember!==""){K=L[D.displayMember];M=L[D.valueMember]}else{K=L;M=L}}}O.setAttribute("data-value",M);O.setAttribute("data-name",K);if(L.label){H=L.label}else{if(D.displayMember){H=L[D.displayMember]}}I.innerHTML=D.highlight(H);var N="";if(D.rtl){N=" jqx-rtl"}if(J===0){N+=" jqx-fill-state-pressed"}O.className=D.toThemeProperty("jqx-item jqx-menu-item jqx-rc-all"+N);D.$popup.appendChild(O);D.addHandler(O,"mouseenter",function(P){D._itemHandler(P)})};var E=function(I){for(var H=0;H<I.length;H++){B(I[H],H)}};E(G);if(!this.dropDownWidth){this.$popup.style.width=D._toPx(D.element.offsetWidth-6)}else{this.$popup.style.width=D._toPx(D.dropDownWidth)}return this},_highlight:function(D){var B=this.query;B=B.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&");var C=new RegExp("("+B+")","ig");return D.replace(C,function(E,F){return"<b>"+F+"</b>"})},_select:function(){var D=this._find("jqx-fill-state-pressed",this._popupHelper);var B=D.getAttribute("data-value");var C=D.getAttribute("data-name");this.textarea.value=this.renderer(C,this.textarea.value);this.selectedItem={label:C,value:B};this.element.setAttribute("data-value",B);this.element.setAttribute("data-label",C);this._raiseEvent("1",{item:{label:C,value:B}});this._arrange();this.textarea.scrollTop=this.textarea.scrollHeight;this._raiseEvent("0");return this.close()},_renderer:function(B){return B},_move:function(C){var B=this;if(!B.opened){return}switch(C.keyCode){case 9:case 13:case 27:C.preventDefault();break;case 38:if(!C.shiftKey){C.preventDefault();B._prev()}break;case 40:if(!C.shiftKey){C.preventDefault();B._next()}break}C.stopPropagation()},_next:function(){var C=this._find("jqx-fill-state-pressed",this._popupHelper),B=C.nextSibling;A(C).removeClass(this.toThemeProperty("jqx-fill-state-pressed"));if(!B){B=this.$popup.firstChild}B.className+=" "+this.toThemeProperty("jqx-fill-state-pressed")},_prev:function(){var C=this._find("jqx-fill-state-pressed",this._popupHelper),B=C.previousSibling;A(C).removeClass(this.toThemeProperty("jqx-fill-state-pressed"));if(!B){B=this.$popup.lastChild}B.className+=" "+this.toThemeProperty("jqx-fill-state-pressed")},_toPx:function(B){if(typeof B==="number"){return B+"px"}else{return B}},_find:function(C,D){var B=D.children();for(var E=0;E<B.length;E++){var F=B[E];if(F.className.indexOf(C)!==-1){return F}}},_width:function(D){var F=A(D),B=F.css("border-left-width"),G=F.css("border-right-width"),C=parseInt(F.css("padding-left"),10),E=parseInt(F.css("padding-right"),10);if(B.indexOf("px")===-1){B=1}else{B=parseInt(B,10)}if(G.indexOf("px")===-1){G=1}else{G=parseInt(G,10)}var H=D.offsetWidth-(B+G+C+E);if(H>0){return H}else{return""}},_height:function(D){var G=A(D),B=G.css("border-top-width"),C=G.css("border-bottom-width"),H=parseInt(G.css("padding-top"),10),E=parseInt(G.css("padding-bottom"),10);if(B.indexOf("px")===-1){B=1}else{B=parseInt(B,10)}if(C.indexOf("px")===-1){C=1}else{C=parseInt(C,10)}var F=D.offsetHeight-(B+C+H+E);if(F>0){return F}else{return""}}})})(jqxBaseFramework);