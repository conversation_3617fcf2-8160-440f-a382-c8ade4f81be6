(function(A){A.jqx.jqxWidget("jqxLoader","",{});A.extend(A.jqx._jqxLoader.prototype,{defineInstance:function(){var B={width:200,height:150,text:"Loading...",html:null,textPosition:"bottom",imagePosition:"center",isModal:false,autoOpen:false,rtl:false,events:["create"]};A.extend(true,this,B)},createInstance:function(B){var C=this;C._render(true);C._raiseEvent("0")},render:function(){this._render()},open:function(B,D){var C=this;if(this.width!==null&&this.width.toString().indexOf("%")!==-1){C.host.css("width",this.width)}if(this.height!==null&&this.height.toString().indexOf("%")!==-1){C.host.css("height",this.height)}C.host.show();C.host.css("left",-C.host.width()/2);C.host.css("top",-C.host.height()/2);if(B&&D){C.host.css("left",B);C.host.css("top",D)}if(C.isModal){C._modal.show()}},close:function(){var B=this;B.host.hide();if(B.isModal){B._modal.hide()}},_checkBrowser:function(){var B=this;if(A.jqx.browser.browser==="msie"){if(A.jqx.browser.version==="7.0"){if(B.isModal===false){B.host.addClass(B.toThemeProperty("jqx-loader-ie-transparency"))}B.host.css("top",Math.max(0,((A(window).height()-A(B.host).outerHeight())/2)+A(window).scrollTop())+"px");B.host.css("left",Math.max(0,((A(window).width()-A(B.host).outerWidth())/2)+A(window).scrollLeft())+"px");A(window).resize(function(){B.host.css("top",Math.max(0,((A(window).height()-A(B.host).outerHeight())/2)+A(window).scrollTop())+"px");B.host.css("left",Math.max(0,((A(window).width()-A(B.host).outerWidth())/2)+A(window).scrollLeft())+"px")});this.host.css({"margin-top":"0","margin-left":"0"})}else{if(A.jqx.browser.version==="8.0"){if(B.isModal===false){B.host.addClass(B.toThemeProperty("jqx-loader-ie-transparency"))}}}}},_textPos:function(){var B=this;this._text=B.host.children("div:eq(1)");if(this._image){this._image.css("background-position-y",B.imagePosition)}if(B.textPosition==="top"){this._text.addClass(B.toThemeProperty("jqx-loader-text-top"));this._text.removeClass(B.toThemeProperty("jqx-loader-text-bottom"));this._text.removeClass(B.toThemeProperty("jqx-loader-text-left"));this._text.removeClass(B.toThemeProperty("jqx-loader-text-right"))}else{if(B.textPosition==="bottom"){this._text.addClass(B.toThemeProperty("jqx-loader-text-bottom"));this._text.removeClass(B.toThemeProperty("jqx-loader-text-top"));this._text.removeClass(B.toThemeProperty("jqx-loader-text-left"));this._text.removeClass(B.toThemeProperty("jqx-loader-text-right"))}else{if(B.textPosition==="left"){this._text.addClass(B.toThemeProperty("jqx-loader-text-left"));this._text.removeClass(B.toThemeProperty("jqx-loader-text-right"));this._text.removeClass(B.toThemeProperty("jqx-loader-text-top"));this._text.removeClass(B.toThemeProperty("jqx-loader-text-bottom"))}else{if(B.textPosition==="right"){this._text.addClass(B.toThemeProperty("jqx-loader-text-right"));this._text.removeClass(B.toThemeProperty("jqx-loader-text-left"));this._text.removeClass(B.toThemeProperty("jqx-loader-text-top"));this._text.removeClass(B.toThemeProperty("jqx-loader-text-bottom"))}}}}},refresh:function(B){if(B!==true){this._render(false)}},destroy:function(){var B=this;B._removeHandlers();B.host.remove()},propertyChangedHandler:function(D,E,C,B){if(B!==C){switch(E){case"width":D.host.width(B);break;case"height":D.host.height(B);break;case"text":D._text.text(B);break;case"html":D.host.html(B);break;case"textPosition":D._textPos(B);break;case"rtl":if(B===true){D._text.addClass(D.toThemeProperty("jqx-loader-rtl"))}else{D._text.removeClass(D.toThemeProperty("jqx-loader-rtl"))}break}}},_raiseEvent:function(B,D){var E=this;var G=E.events[B];var H=new A.Event(G);H.owner=E;H.args=D;try{var F=E.host.trigger(H)}catch(C){}return F},_render:function(C){var D=this;D.host.width(D.width);D.host.height(D.height);if(D.autoOpen===false){D.host.hide()}if(C){if(D.html===null){D.host.append('<div class="'+D.toThemeProperty("jqx-loader-icon")+'"></div><div class="'+D.toThemeProperty("jqx-loader-text")+'">'+D.text+"</div>");D._image=D.host.children("div:eq(0)");D._text=D.host.children("div:eq(1)")}else{D.host.html(this.html)}if(D.isModal===true){var B=D.host.css("display");D._modal=A('<div id="'+D.element.id+'Modal" class="'+D.toThemeProperty("jqx-loader-modal")+'" style="display: '+B+';"></div>');A("body").append(D._modal)}}D._checkBrowser();D._textPos();D._addClass();D._removeHandlers();D._addHandlers()},_addHandlers:function(){var B=this;if(B.isModal===true){B.addHandler(A(document),"keyup.loader"+B.element.id,function(C){if(C.keyCode===27){B.close()}})}},_removeHandlers:function(){var B=this;B.removeHandler(A(document),"keyup.loader"+B.element.id)},_addClass:function(){var B=this;B.host.addClass(B.toThemeProperty("jqx-widget"));B.host.addClass(B.toThemeProperty("jqx-loader"));B.host.addClass(B.toThemeProperty("jqx-rc-all"));B.host.addClass(B.toThemeProperty("jqx-fill-state-normal"));if(B.rtl){B._text.addClass(B.toThemeProperty("jqx-loader-rtl"))}if(A.jqx.browser.msie){B.host.addClass(this.toThemeProperty("jqx-noshadow"))}B.host.addClass(this.toThemeProperty("jqx-rc-t"));B.host.addClass(this.toThemeProperty("jqx-rc-b"));B.host.addClass(this.toThemeProperty("jqx-popup"))}})})(jqxBaseFramework);