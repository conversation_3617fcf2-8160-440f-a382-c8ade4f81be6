(function(A){A.jqx.jqxWidget("jqxCalendar","",{});A.extend(A.jqx._jqxCalendar.prototype,{defineInstance:function(){var B={disabled:false,restrictedDates:new Array(),multipleMonthRows:1,multipleMonthColumns:1,minDate:A.jqx._jqxDateTimeInput.getDateTime(new Date()),maxDate:A.jqx._jqxDateTimeInput.getDateTime(new Date()),min:new Date(1900,0,1),max:new Date(2100,0,1),navigationDelay:400,stepMonths:1,width:null,height:null,value:A.jqx._jqxDateTimeInput.getDateTime(new Date()),firstDayOfWeek:0,showWeekNumbers:false,showDayNames:true,enableWeekend:false,enableOtherMonthDays:true,showOtherMonthDays:true,rowHeaderWidth:25,columnHeaderHeight:20,titleHeight:30,dayNameFormat:"firstTwoLetters",monthNameFormat:"default",titleFormat:["MMMM yyyy","yyyy","yyyy","yyyy"],enableViews:true,readOnly:false,culture:"default",enableFastNavigation:true,enableHover:true,enableAutoNavigation:true,enableTooltips:false,backText:"Back",forwardText:"Forward",specialDates:new Array(),keyboardNavigation:true,selectionMode:"default",selectableDays:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],todayString:"Today",clearString:"Clear",showFooter:false,selection:{from:null,to:null},canRender:true,_checkForHiddenParent:true,height:null,rtl:false,view:"month",views:["month","year","decade"],changing:null,change:null,localization:{backString:"Back",forwardString:"Forward",todayString:"Today",clearString:"Clear",calendar:{name:"Gregorian_USEnglish","/":"/",":":":",firstDay:0,days:{names:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],namesAbbr:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],namesShort:["Su","Mo","Tu","We","Th","Fr","Sa"]},months:{names:["January","February","March","April","May","June","July","August","September","October","November","December",""],namesAbbr:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec",""]},AM:["AM","am","AM"],PM:["PM","pm","PM"],eras:[{name:"A.D.",start:null,offset:0}],twoDigitYearMax:2029,patterns:{d:"M/d/yyyy",D:"dddd, MMMM dd, yyyy",t:"h:mm tt",T:"h:mm:ss tt",f:"dddd, MMMM dd, yyyy h:mm tt",F:"dddd, MMMM dd, yyyy h:mm:ss tt",M:"MMMM dd",Y:"yyyy MMMM",S:"yyyy\u0027-\u0027MM\u0027-\u0027dd\u0027T\u0027HH\u0027:\u0027mm\u0027:\u0027ss",ISO:"yyyy-MM-dd hh:mm:ss"}}},events:["backButtonClick","nextButtonClick","valuechanged","cellMouseDown","cellMouseUp","cellSelected","cellUnselected","change","viewChange"]};A.extend(true,this,B);this.minDate._setYear(1900);this.minDate._setMonth(1);this.minDate._setDay(1);this.minDate._setHours(0);this.minDate._setMinutes(0);this.minDate._setSeconds(0);this.minDate._setMilliseconds(0);this.maxDate._setYear(2100);this.maxDate._setMonth(1);this.maxDate._setDay(1);this.maxDate._setHours(0);this.maxDate._setMinutes(0);this.maxDate._setSeconds(0);this.maxDate._setMilliseconds(0);this.value._setHours(0);this.value._setMinutes(0);this.value._setSeconds(0);this.value._setMilliseconds(0);return B},_createFromInput:function(I){var H=this;if(H.element.nodeName.toLowerCase()=="input"){H.field=H.element;if(H.field.className){H._className=H.field.className}var G={title:H.field.title};if(H.field.value){G.value=H.field.value}if(H.field.checked){G.checked=true}if(H.field.id.length){G.id=H.field.id.replace(/[^\w]/g,"_")+"_"+I}else{G.id=A.jqx.utilities.createId()+"_"+I}if(H.field.getAttribute("min")){var B=new Date(H.field.getAttribute("min"));if(B!="Invalid Date"){H.min=B}}if(H.field.getAttribute("max")){var F=new Date(H.field.getAttribute("max"));if(F!="Invalid Date"){H.max=F}}var D=A("<div></div>",G);D[0].style.cssText=H.field.style.cssText;if(!H.width){H.width=A(H.field).width()}if(!H.height){H.height=A(H.field).outerHeight()}A(H.field).hide().after(D);var C=H.host.data();H.host=D;H.host.data(C);H.element=D[0];H.element.id=H.field.id;H.field.id=G.id;if(H._className){H.host.addClass(H._className);A(H.field).removeClass(H._className)}if(H.field.tabIndex){var E=H.field.tabIndex;H.field.tabIndex=-1;H.element.tabIndex=E}}},createInstance:function(E){var D=this;D._createFromInput("jqxCalendar");this.setCalendarSize();if(this.element.id===""){this.element.id=A.jqx.utilities.createId()}if(A.type(this.value)=="date"){this.value=A.jqx._jqxDateTimeInput.getDateTime(this.value)}this.element.innerHTML="";this.host.attr("data-role","calendar");var F=this.element.id;var C=this;this.propertyChangeMap.width=function(N,M,L,K){C.setCalendarSize()};this.propertyChangeMap.height=function(N,M,L,K){C.setCalendarSize()};if(A.global){A.global.preferCulture(this.culture)}if(this.culture!="default"){if(A.global){A.global.preferCulture(this.culture);this.localization.calendar=A.global.culture.calendar}else{if(Globalize){var J=Globalize.culture(this.culture);this.localization.calendar=J.calendar}}this.firstDayOfWeek=this.localization.calendar.firstDay}if(this.localization.backString!="Back"){this.backText=this.localization.backString}if(this.localization.forwardString!="Forward"){this.forwardText=this.localization.forwardString}if(this.localization.todayString!="Today"&&this.localization.todayString){this.todayString=this.localization.todayString}if(this.localization.clearString!="Clear"&&this.localization.clearString){this.clearString=this.localization.clearString}if(this.localization.calendar&&this.localization.calendar.firstDay!=undefined&&this.culture!="default"){this.firstDayOfWeek=this.localization.calendar.firstDay}this.setMaxDate(this.max,false);this.setMinDate(this.min,false);if(!this.host.attr("tabIndex")){this.host.attr("tabIndex",0)}this.host.css("outline","none");this.host.addClass(this.toThemeProperty("jqx-calendar"));this.host.addClass(this.toThemeProperty("jqx-widget"));this.host.addClass(this.toThemeProperty("jqx-widget-content"));this.host.addClass(this.toThemeProperty("jqx-rc-all"));this._addInput();if(this.views.indexOf("month")==-1){this.view="year"}if(this.views.indexOf("year")==-1&&this.views.indexOf("month")==-1){this.view="decade"}this.addHandler(this.host,"keydown",function(K){var L=true;if(C.keyboardNavigation){if(C._handleKey!=undefined){L=C._handleKey(K);if(!L){if(K.stopPropagation){K.stopPropagation()}if(K.preventDefault){K.preventDefault()}}}}return L});var B=false;var I=this;var H=false;if(C.width!=null&&C.width.toString().indexOf("%")!=-1){H=true}if(C.height!=null&&C.height.toString().indexOf("%")!=-1){H=true}A.jqx.utilities.resize(this.host,function(){var K=I.host.find("#View"+C.element.id);if(!B){B=true;I.render()}else{I.refreshTitle(K)}if(H){if(C.refreshTimer){clearTimeout(C.refreshTimer)}C.refreshTimer=setTimeout(function(){C.refreshControl()},1)}},false,this._checkForHiddenParent);var G="View";this.propertyChangeMap.disabled=function(N,M,L,K){if(K){N.host.addClass(C.toThemeProperty("jqx-fill-state-disabled"))}else{N.host.removeClass(C.toThemeProperty("jqx-fill-state-disabled"))}C.refreshControl()}},_addInput:function(){var B=this.host.attr("name");this.input=A("<input type='hidden'/>");this.host.append(this.input);if(B){this.input.attr("name",B)}this.input.val(this.getDate().toString())},setCalendarSize:function(){if(this.width!=null&&this.width.toString().indexOf("px")!=-1){this.host.width(this.width)}else{if(this.width!=undefined&&!isNaN(this.width)){this.host.width(this.width)}}if(this.width!=null&&this.width.toString().indexOf("%")!=-1){this.host.css("width",this.width)}if(this.height!=null&&this.height.toString().indexOf("px")!=-1){this.host.height(this.height)}else{if(this.height!=undefined&&!isNaN(this.height)){this.host.height(this.height)}}if(this.height!=null&&this.height.toString().indexOf("%")!=-1){this.host.css("height",this.height)}},_getYearAndMonthPart:function(C){if(!C){return new Date(1900,0,1)}var B=new Date(C.getFullYear(),C.getMonth(),1);return B},_handleKey:function(D){if(this.readOnly){return true}var W=D.keyCode;var Z=this;var Q=this._getSelectedDate();if(Q==undefined){if(this.view=="month"&&(W==37||W==38||W==39||W==40)){this.selectedDate=new Date(this.value.year,this.value.month-1,1);this._selectDate(this.selectedDate,"key");Q=this.selectedDate}else{return true}}if(D.altKey){return true}if(this._animating){return false}if(this.view!="month"&&W==13){var U=this._getSelectedCell();this._setDateAndSwitchViews(U,D,"keyboard")}if(this.view=="year"){var G=Q.getMonth();var J=this._getYearAndMonthPart(this.getMinDate());var P=this._getYearAndMonthPart(this.getMaxDate());switch(W){case 37:if(G==0){var L=new Date(Q.getFullYear()-1,11,1);if(L>=J){this.selectedDate=L;this.navigateBackward()}else{if(this.selectedDate!=J){this.selectedDate=J;this.navigateBackward()}}}else{var L=new Date(Q.getFullYear(),G-1,1);if(L>=J){this._selectDate(L,"key")}}return false;case 38:var L=new Date(Q.getFullYear(),G-4,1);if(L<J){L=J}if(G-4<0){this.selectedDate=L;this.navigateBackward()}else{this._selectDate(L,"key")}return false;case 40:var L=new Date(Q.getFullYear(),G+4,1);if(L>P){L=P}if(G+4>11){this.selectedDate=L;this.navigateForward()}else{this._selectDate(L,"key")}return false;case 39:if(G==11){var L=new Date(Q.getFullYear()+1,0,1);if(L<=P){this.selectedDate=L;this.navigateForward()}else{if(this.selectedDate!=P){this.selectedDate=P;this.navigateForward()}}}else{var L=new Date(Q.getFullYear(),G+1,1);if(L<=P){this._selectDate(L,"key")}}return false}return true}if(this.view=="decade"){var N=this._renderStartDate.getFullYear();var K=this._renderEndDate.getFullYear();var M=Q.getFullYear();var F=this.getMinDate().getFullYear();var R=this.getMaxDate().getFullYear();switch(W){case 37:if(M-1>=F){if(M<=N){this.selectedDate=new Date(M-1,Q.getMonth(),1);this.navigateBackward()}else{this._selectDate(new Date(M-1,Q.getMonth(),1),"key")}}return false;case 38:var Y=M-4;if(M-4<F){Y=F}if(Y<N){this.selectedDate=new Date(Y,Q.getMonth(),1);this.navigateBackward()}else{this._selectDate(new Date(Y,Q.getMonth(),1),"key")}return false;case 40:var Y=M+4;if(Y>R){Y=R}if(Y>K){this.selectedDate=new Date(Y,Q.getMonth(),1);this.navigateForward()}else{this._selectDate(new Date(Y,Q.getMonth(),1),"key")}return false;case 39:if(M+1<=R){if(M==K){this.selectedDate=new Date(M+1,Q.getMonth(),1);this.navigateForward()}else{this._selectDate(new Date(M+1,Q.getMonth(),1),"key")}}return false}return true}var I=new A.jqx._jqxDateTimeInput.getDateTime(Q);var S=this.getViewStart();var V=this.getViewEnd();var H=I;var C=A.data(this.element,"View"+this.element.id);if(C==undefined||C==null){return true}if(W==36){I._setDay(1);if(this._isDisabled(I.dateTime)){return false}this._selectDate(I.dateTime,"key");return false}if(W==35){var B=this.value._daysInMonth(this.value.year,this.value.month);I._setDay(B);if(this._isDisabled(I.dateTime)){return false}this._selectDate(I.dateTime,"key");return false}var T=1;if(D.ctrlKey){T=12}if(W==34){var X=this.navigateForward(T);if(X){I._addMonths(T);if(this._isDisabled(I.dateTime)){return false}this._selectDate(I.dateTime,"key")}return false}if(W==33){var X=this.navigateBackward(T);if(X){I._addMonths(-T);if(this._isDisabled(I.dateTime)){return false}this._selectDate(I.dateTime,"key")}return false}if(W==38){I._addDays(-7);if(I.dateTime<this.getMinDate()){return false}if(I.dateTime<S){var X=this.navigateBackward();if(!X){return false}}if(this._isDisabled(I.dateTime)){return false}this._selectDate(I.dateTime,"key");for(var E=0;E<C.cells.length;E++){var U=C.cells[E];var O=U.getDate();if(U.isOtherMonth&&U.isSelected&&O<=I.dateTime){this.value.day=O.getDate();this.navigateBackward();this._selectDate(I.dateTime,"key");break}}return false}else{if(W==40){I._addDays(7);if(I.dateTime>this.getMaxDate()){return false}if(I.dateTime>V){var X=this.navigateForward();if(!X){return false}}if(this._isDisabled(I.dateTime)){return false}this._selectDate(I.dateTime,"key");for(var E=0;E<C.cells.length;E++){var U=C.cells[E];var O=U.getDate();if(U.isOtherMonth&&U.isSelected&&O>=I.dateTime){this.value.day=O.getDate();this.navigateForward();this._selectDate(I.dateTime,"key");break}}return false}}if(W==37){I._addDays(-1);if(I.dateTime<this.getMinDate()){return false}if(I.dateTime<S){var X=this.navigateBackward();if(!X){return false}}if(this._isDisabled(I.dateTime)){return false}this._selectDate(I.dateTime,"key");for(var E=0;E<C.cells.length;E++){var U=C.cells[E];var O=U.getDate();if(U.isOtherMonth&&U.isSelected&&O<=I.dateTime){if(I.dateTime<this.getMinDate()||I.dateTime>this.getMaxDate()){return false}if(this._isDisabled(I.dateTime)){return false}this.navigateBackward();this._selectDate(I.dateTime,"key");break}}return false}else{if(W==39){I._addDays(1);if(I.dateTime>this.getMaxDate()){return false}if(I.dateTime>V){var X=this.navigateForward();if(!X){return false}}if(this._isDisabled(I.dateTime)){return false}this._selectDate(I.dateTime,"key");for(var E=0;E<C.cells.length;E++){var U=C.cells[E];var O=U.getDate();if(U.isOtherMonth&&U.isSelected&&O>=I.dateTime){if(I.dateTime<this.getMinDate()||I.dateTime>this.getMaxDate()){return false}this.navigateForward();this._selectDate(I.dateTime,"key");break}}return false}}return true},render:function(){if(!this.canRender){return}this.host.children().remove();var C=this._renderSingleCalendar("View"+this.element.id);var B=this;this.host.append(C)},addSpecialDate:function(D,E,B){if(this.multipleMonthRows==1&&this.multipleMonthColumns==1){var C=this.specialDates.length;this.specialDates[C]={Date:D,Class:E,Tooltip:B};this.refreshControl()}},refresh:function(B){this.render()},invalidate:function(){this.refreshControl()},refreshControl:function(){if(this.multipleMonthRows==1&&this.multipleMonthColumns==1){this.refreshSingleCalendar("View"+this.element.id,null)}},getViewStart:function(){var C=this.getVisibleDate();var B=this.getFirstDayOfWeek(C);return B.dateTime},getViewEnd:function(){var C=this.getViewStart();var B=new A.jqx._jqxDateTimeInput.getDateTime(C);B._addDays(41);return B.dateTime},refreshSingleCalendar:function(G,B){if(!this.canRender){return}var C=this.host.find("#"+G);var I=this.getVisibleDate();var E=this.getFirstDayOfWeek(I);this.refreshCalendarCells(C,E,G);this.refreshTitle(C);this.refreshRowHeader(C,G);if(this.selectedDate!=undefined){this._selectDate(this.selectedDate)}var H=this.host.height()-this.titleHeight-this.columnHeaderHeight;if(!this.showDayNames){H=this.host.height()-this.titleHeight}if(this.showFooter){H-=20}var F=C.find("#cellsTable"+G);var D=C.find("#calendarRowHeader"+G);F.height(H);D.height(H)},refreshRowHeader:function(C,M){if(!this.showWeekNumbers){return}var O=this.getVisibleDate();var I=this.getFirstDayOfWeek(O);var J=I.dayOfWeek;var F=this.getWeekOfYear(I);var P=new A.jqx._jqxDateTimeInput.getDateTime(new Date(I.dateTime));P._addDays(5);P.dayOfWeek=P.dateTime.getDay();var H=this.getWeekOfYear(P);var S=this.rowHeader.find("table");S.width(this.rowHeaderWidth);var Q=I;var E=new Array();for(var D=0;D<6;D++){var K=F.toString();var N=new A.jqx._jqxCalendar.cell(Q.dateTime);var L=D+1+this.element.id;var G=A(S[0].rows[D].cells[0]);N.element=G;N.row=D;N.column=0;var R=G.find("#headerCellContent"+L);R.addClass(this.toThemeProperty("jqx-calendar-row-cell"));R[0].innerHTML=F;E[D]=N;Q=new A.jqx._jqxDateTimeInput.getDateTime(new Date(Q._addWeeks(1)));F=this.getWeekOfYear(Q)}var B=A.data(this.element,C[0].id);B.rowCells=E;this._refreshOtherMonthRows(B,M)},_refreshOtherMonthRows:function(G,D){if(this.showOtherMonthDays){return}this._displayLastRow(true,D);this._displayFirstRow(true,D);var C=false;var B=false;for(var F=0;F<G.cells.length;F++){var E=G.cells[F];if(E.isVisible&&F<7){C=true}else{if(E.isVisible&&F>=G.cells.length-7){B=true}}}if(!C){this._displayFirstRow(false,D)}if(!B){this._displayLastRow(false,D)}},_displayLastRow:function(E,F){var B=this.host.find("#"+F);var G=B.find("#calendarRowHeader"+B[0].id).find("table");var C=null;if(this.showWeekNumbers){if(G[0].cells){var C=A(G[0].rows[5])}}var D=A(B.find("#cellTable"+B[0].id)[0].rows[5]);if(E){if(this.showWeekNumbers&&C){C.css("display","table-row")}D.css("display","table-row")}else{if(this.showWeekNumbers&&C){C.css("display","none")}D.css("display","none")}},_displayFirstRow:function(E,F){var D=this.host.find("#"+F);var C=D.find("#calendarRowHeader"+D[0].id).find("table");var G=null;if(this.showWeekNumbers){if(C[0].cells){var G=A(C[0].rows[0])}}var B=A(D.find("#cellTable"+D[0].id)[0].rows[0]);if(E){if(this.showWeekNumbers&&G){G.css("display","table-row")}B.css("display","table-row")}else{if(this.showWeekNumbers&&G){G.css("display","none")}B.css("display","none")}},_renderSingleCalendar:function(D,I){if(!this.canRender){return}var O=this.host.find("#"+D.toString());if(O!=null){O.remove()}var C=A("<div id='"+D.toString()+"'></div>");var P=this.getVisibleDate();var N=this.getFirstDayOfWeek(P);var U=new A.jqx._jqxDateTimeInput.getDateTime(N.dateTime);U._addMonths(1);var B=A.jqx._jqxCalendar.monthView(N,U,null,null,null,C);if(I==undefined||I==null){this.host.append(C);if(this.height!=undefined&&!isNaN(this.height)){C.height(this.height)}else{if(this.height!=null&&this.height.toString().indexOf("px")!=-1){C.height(this.height)}}if(this.width!=undefined&&!isNaN(this.width)){C.width(this.width)}else{if(this.width!=null&&this.width.toString().indexOf("px")!=-1){C.width(this.width)}}if(this.width!=null&&this.width.toString().indexOf("%")!=-1){C.width("100%")}if(this.height!=null&&this.height.toString().indexOf("%")!=-1){C.height("100%")}}else{I.append(C)}A.data(this.element,D,B);var E=this.host.height()-this.titleHeight-this.columnHeaderHeight;if(!this.showDayNames){E=this.host.height()-this.titleHeight}if(this.showFooter){E-=20}if(this.rowHeaderWidth<0){this.rowHeaderWidth=0}if(this.columnHeaderHeight<0){this.columnHeaderHeight=0}if(this.titleHeight<0){this.titleHeight=0}var S=this.rowHeaderWidth;var H=this.columnHeaderHeight;if(!this.showWeekNumbers){S=0}if(!this.showDayNames){H=0}var G="<div style='height:"+this.titleHeight+"px;'><table role='grid' style='margin: 0px; width: 100%; height: 100%; border-spacing: 0px;' cellspacing='0' cellpadding='0'><tr role='row' id='calendarTitle' width='100%'><td role='gridcell' NOWRAP id='leftNavigationArrow'></td><td aria-live='assertive' aria-atomic='true' role='gridcell' align='center' NOWRAP id='calendarTitleHeader'></td><td role='gridcell' NOWRAP id='rightNavigationArrow'></td></tr></table></div>";var Q="<table role='grid' class='"+this.toThemeProperty("jqx-calendar-month")+"' style='margin: 0px; border-spacing: 0px;' cellspacing='0' cellpadding='0'><tr role='row' id='calendarHeader' height='"+H+"'><td role='gridcell' id='selectCell' width='"+S+"'></td><td role='gridcell' colspan='2' style='border: none; padding-left: 2px; padding-right: 2px' id='calendarColumnHeader'></td></tr><tr role='row' id='calendarContent'><td role='gridcell' id='calendarRowHeader' valign='top' height='"+E+"' width='"+S+"'></td><td role='gridcell' valign='top' colspan='2' style='padding-left: 2px; padding-right: 2px' id='cellsTable' height='"+E+"'></td></tr></table>";var M="<div id='footer' style='margin: 0px; display: none; height:"+T+"px;'><table style='width: 100%; height: 100%; border-spacing: 0px;' cellspacing='0' cellpadding='0'><tr id='calendarFooter'><td align='right' id='todayButton'></td><td align='left' colspan='2' id=doneButton></td></tr></table></div>";C[0].innerHTML=G+Q+M;this.header=C.find("#calendarHeader");this.header[0].id="calendarHeader"+D;this.header.addClass(this.toThemeProperty("calendar-header"));this.columnHeader=C.find("#calendarColumnHeader");this.columnHeader[0].id="calendarColumnHeader"+D;this.table=C.find("#cellsTable");this.table[0].id="cellsTable"+D;this.rowHeader=C.find("#calendarRowHeader");this.rowHeader[0].id="calendarRowHeader"+D;this.selectCell=C.find("#selectCell");this.selectCell[0].id="selectCell"+D;this.title=C.find("#calendarTitle");this.title[0].id="calendarTitle"+D;this.leftButton=C.find("#leftNavigationArrow");this.leftButton[0].id="leftNavigationArrow"+D;this.titleHeader=C.find("#calendarTitleHeader");this.titleHeader[0].id="calendarTitleHeader"+D;this.rightButton=C.find("#rightNavigationArrow");this.rightButton[0].id="rightNavigationArrow"+D;this.footer=C.find("#calendarFooter");this._footer=C.find("#footer");this._footer[0].id="footer"+D;this.footer[0].id="calendarFooter"+D;this.todayButton=C.find("#todayButton");this.todayButton[0].id="todayButton"+D;this.doneButton=C.find("#doneButton");this.doneButton[0].id="doneButton"+D;this.title.addClass(this.toThemeProperty("jqx-calendar-title-container"));var T=20;if(this.showFooter){this._footer.css("display","block")}C.find("tr").addClass(this.toThemeProperty("jqx-reset"));C.addClass(this.toThemeProperty("jqx-widget-content"));C.addClass(this.toThemeProperty("jqx-calendar-month-container"));this.month=C;this.selectCell.addClass(this.toThemeProperty("jqx-reset"));this.selectCell.addClass(this.toThemeProperty("jqx-calendar-top-left-header"));if(this.showWeekNumbers){this._renderRowHeader(C)}else{this.table[0].colSpan=3;this.columnHeader[0].colSpan=3;this.rowHeader.css("display","none");this.selectCell.css("display","none")}if(this.showFooter){this.footer.height(20);var K=A("<a href='javascript:;'>"+this.todayString+"</a>");K.appendTo(this.todayButton);var J=A("<a href='javascript:;'>"+this.clearString+"</a>");J.appendTo(this.doneButton);J.addClass(this.toThemeProperty("jqx-calendar-footer"));K.addClass(this.toThemeProperty("jqx-calendar-footer"));var L=this;var R="mousedown";if(A.jqx.mobile.isTouchDevice()){R=A.jqx.mobile.getTouchEventName("touchstart")}this.addHandler(K,R,function(){if(L.today){L.today()}else{L.setDate(new Date(),"mouse")}return false});this.addHandler(J,R,function(){if(L.clear){L.clear()}else{L.setDate(null,"mouse")}return false})}if(this.view!="month"){this.header.hide()}if(this.showDayNames&&this.view=="month"){this.renderColumnHeader(C)}this.oldView=this.view;this.renderCalendarCells(C,N,D);if(I==undefined||I==null){this.renderTitle(C)}this._refreshOtherMonthRows(B,D);C.find("tbody").css({border:"none",background:"transparent"});if(this.selectedDate!=undefined){this._selectDate(this.selectedDate)}var F=this;this.addHandler(this.host,"focus",function(){F.focus()});return C},_getTitleFormat:function(){switch(this.view){case"month":return this.titleFormat[0];case"year":return this.titleFormat[1];case"decade":return this.titleFormat[2];case"centuries":return this.titleFormat[3]}},renderTitle:function(G){var J=A("<div role='button' style='float: left;'></div>");var O=A("<div role='button' style='float: right;'></div>");var N=this.title;N.addClass(this.toThemeProperty("jqx-reset"));N.addClass(this.toThemeProperty("jqx-widget-header"));N.addClass(this.toThemeProperty("jqx-calendar-title-header"));var V=N.find("td");if(A.jqx.browser.msie&&A.jqx.browser.version<8){if(V.css("background-color")!="transparent"){var T=N.css("background-color");V.css("background-color",T)}if(V.css("background-image")!="transparent"){var U=N.css("background-image");var D=N.css("background-repeat");var R=N.css("background-position");V.css("background-image",U);V.css("background-repeat",D);V.css("background-position","left center scroll")}}else{V.css("background-color","transparent")}if(this.disabled){N.addClass(this.toThemeProperty("jqx-calendar-title-header-disabled"))}J.addClass(this.toThemeProperty("jqx-calendar-title-navigation"));J.addClass(this.toThemeProperty("jqx-icon-arrow-left"));J.appendTo(this.leftButton);var P=this.leftButton;O.addClass(this.toThemeProperty("jqx-calendar-title-navigation"));O.addClass(this.toThemeProperty("jqx-icon-arrow-right"));O.appendTo(this.rightButton);var Q=this.rightButton;if(this.enableTooltips){if(A(P).jqxTooltip){A(P).jqxTooltip({name:this.element.id,position:"mouse",theme:this.theme,content:this.backText});A(Q).jqxTooltip({name:this.element.id,position:"mouse",theme:this.theme,content:this.forwardText})}}var M=this.titleHeader;var F=this._format(this.value.dateTime,this._getTitleFormat(),this.culture);if(this.view=="decade"){var E=this._format(this._renderStartDate,this._getTitleFormat(),this.culture);var I=this._format(this._renderEndDate,this._getTitleFormat(),this.culture);F=E+" - "+I}else{if(this.view=="centuries"){var E=this._format(this._renderCenturyStartDate,this._getTitleFormat(),this.culture);var I=this._format(this._renderCenturyEndDate,this._getTitleFormat(),this.culture);F=E+" - "+I}}var S=A("<div style='background: transparent; margin: 0; padding: 0; border: none;'>"+F+"</div>");M.append(S);S.addClass(this.toThemeProperty("jqx-calendar-title-content"));var C=parseInt(J.width());var L=G.width()-2*C;var B=M.find(".jqx-calendar-title-content").width(L);A.data(J,"navigateLeft",this);A.data(O,"navigateRight",this);var K=A.jqx.mobile.isTouchDevice();if(!this.disabled){var H=this;this.addHandler(M,"mousedown",function(b){if(H.enableViews){if(!H._viewAnimating&&!H._animating){var X=H.view;H.oldView=X;switch(H.view){case"month":H.view="year";break;case"year":H.view="decade";break}if(H.views.indexOf("year")==-1&&H.view=="year"){H.view="decade"}if(H.views.indexOf("decade")==-1&&H.view=="decade"){H.view=X}if(X!=H.view){var W="View"+H.element.id;var Y=H.host.find("#"+W);var Z=H.getVisibleDate();var a=H.getFirstDayOfWeek(Z);H.renderCalendarCells(Y,a,W,true);H.refreshTitle(Y);H._raiseEvent("8")}}return false}});this.addHandler(J,"mousedown",function(W){if(!H._animating){A.data(J,"navigateLeftRepeat",true);var X=A.data(J,"navigateLeft");if(X.enableFastNavigation&&!K){X.startRepeat(X,J,true,H.navigationDelay+200)}X.navigateBackward(H.stepMonths,"arrow");W.stopPropagation();W.preventDefault();return X._raiseEvent(0,W)}else{return false}});this.addHandler(J,"mouseup",function(W){A.data(J,"navigateLeftRepeat",false)});this.addHandler(J,"mouseleave",function(W){A.data(J,"navigateLeftRepeat",false)});this.addHandler(O,"mousedown",function(W){if(!H._animating){A.data(O,"navigateRightRepeat",true);var X=A.data(O,"navigateRight");if(X.enableFastNavigation&&!K){X.startRepeat(X,O,false,H.navigationDelay+200)}X.navigateForward(H.stepMonths,"arrow");W.stopPropagation();W.preventDefault();return X._raiseEvent(1,W)}else{return false}});this.addHandler(O,"mouseup",function(W){A.data(O,"navigateRightRepeat",false)});this.addHandler(O,"mouseleave",function(W){A.data(O,"navigateRightRepeat",false)})}},refreshTitle:function(H){var B=this._format(this.value.dateTime,this._getTitleFormat(),this.culture);if(this.view=="decade"){var C=this._format(this._renderStartDate,this._getTitleFormat(),this.culture);var F=this._format(this._renderEndDate,this._getTitleFormat(),this.culture);B=C+" - "+F}else{if(this.view=="centuries"){var C=this._format(this._renderCenturyStartDate,this._getTitleFormat(),this.culture);var F=this._format(this._renderCenturyEndDate,this._getTitleFormat(),this.culture);B=C+" - "+F}}var D=this.titleHeader;if(this.titleHeader){var G=D.find(".jqx-calendar-title-content");var E=A("<div style='background: transparent; margin: 0; padding: 0; border: none;'>"+B+"</div>");D.append(E);E.addClass(this.toThemeProperty("jqx-calendar-title-content"));if(G!=null){G.remove()}}},startRepeat:function(B,D,F,C){var E=window.setTimeout(function(){var G=A.data(D,"navigateLeftRepeat");if(!F){G=A.data(D,"navigateRightRepeat")}if(G){if(C<25){C=25}if(F){B.navigateBackward(1,"arrow");B.startRepeat(B,D,true,C)}else{B.navigateForward(1,"arrow");E=B.startRepeat(B,D,false,C)}}else{window.clearTimeout(E);return}},C)},navigateForward:function(C,H){if(C==undefined||C==null){C=this.stepMonths}var G=this.value.year;if(this.view=="decade"){G=this._renderStartDate.getFullYear()+12;if(this._renderEndDate.getFullYear()>=this.getMaxDate().getFullYear()){return}}else{if(this.view=="year"){G=this.value.year+1}else{if(this.view=="centuries"){G=this.value.year+100}}}if(this.view!="month"){var E=this.getMaxDate().getFullYear();if(E<G||G>E){G=E}if(this.value.year==G){if(this.view==="decade"){if(this.value.year>this._renderEndDate.getFullYear()){this.value.year=G;this.value.month=1;this.value.day=1}else{return}}else{return}}this.value.year=G;this.value.month=1;this.value.day=1}var F=this.value.day;var D=this.value.month;if(D+C<=12){var B=this.value._daysInMonth(this.value.year,this.value.month+C);if(F>B){F=B}}if(this.view=="month"){var I=new Date(this.value.year,this.value.month-1+C,F);if(H=="arrow"&&this.selectableDays.length==7&&this.selectionMode!="range"){this.selectedDate=new Date(this.value.year,this.value.month-1+C,1)}}else{var I=new Date(this.value.year,this.value.month-1,F)}return this.navigateTo(I)},navigateBackward:function(H,G){if(H==undefined||H==null){H=this.stepMonths}var B=this.value.year;if(this.view=="decade"){B=this._renderStartDate.getFullYear()-12}else{if(this.view=="year"){B=this.value.year-1}else{if(this.view=="centuries"){B=this.value.year-100}}}if(this.view!="month"){var D=this.getMinDate().getFullYear();if(B<D){B=D}if(this.view=="decade"){if(this._renderStartDate){if(this._renderStartDate.getFullYear()==B){return}}}this.value.year=B;this.value.month=1;this.value.day=1}var E=this.value.day;var C=this.value.month;if(C-H>=1){var I=this.value._daysInMonth(this.value.year,this.value.month-H);if(E>I){E=I}}if(this.view=="month"){var F=new Date(this.value.year,this.value.month-1-H,E);if(G=="arrow"&&this.selectableDays.length==7&&this.selectionMode!="range"){this.selectedDate=new Date(this.value.year,this.value.month-1-H,1)}}else{var F=new Date(this.value.year,this.value.month-1,E)}return this.navigateTo(F)},_isRestrictedRange:function(B,D){if(B>D){return true}var C=B;while(C.valueOf()<=D.valueOf()){if(this._isRestrictedDate(C)){return true}C.setDate(C.getDate()+1)}return false},_hasUnrestrictedRanges:function(B,D){if(B>D){return false}var C=B;while(C.valueOf()<=D.valueOf()){if(!this._isRestrictedDate(C)){return true}C.setDate(C.getDate()+1)}return false},_getNextUnrestrictedDay:function(B,D){if(B>D){return null}var C=B;while(C.valueOf()<=D.valueOf()){if(!this._isRestrictedDate(C)){return C}C.setDate(C.getDate()+1)}return null},_isRestrictedDate:function(C){var F=this;if(!A.isArray(F.restrictedDates)){return false}for(var B=0;B<F.restrictedDates.length;B++){var E=F.restrictedDates[B];if(typeof(E)=="object"&&E.from!=undefined&&E.to!=undefined){var G=E.from;var D=E.to;if(C.valueOf()>=G.valueOf()&&C.valueOf()<=D.valueOf()){return true}}else{if(E.getMonth()==C.getMonth()&&E.getDate()==C.getDate()&&E.getFullYear()==C.getFullYear()){return true}}}return false},_isDisabled:function(B){var C=["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"];var D=B.getDay();var E=C[D];if(this.selectableDays.indexOf(E)==-1){return true}if(this._isRestrictedDate(B)){return true}return false},refreshCalendarCells:function(T,P,M){if(this.view=="year"||this.view=="decade"||this.view=="centuries"){this.refreshViews(T,P,M);return}var B=this.table;var D=B.find("#cellTable"+M.toString());var S=P;var O=new Array();var J=0;var H=new A.jqx._jqxDateTimeInput.getDateTime(new Date());for(var C=0;C<6;C++){for(var K=0;K<7;K++){var R=C+1;var I=K;if(this.rtl){I=6-I}var G=I+1;var L="#cell"+R+G+this.element.id;var F=new Date(S.dateTime.getFullYear(),S.dateTime.getMonth(),S.dateTime.getDate());var N=new A.jqx._jqxCalendar.cell(F);var Q=A(D[0].rows[C].cells[G-1]);Q[0].id=L.substring(1);N.element=Q;N.row=C;N.column=K;N.isVisible=true;N.isOtherMonth=false;N.isToday=false;N.isWeekend=false;N.isHighlighted=false;N.isSelected=false;if(S.month!=this.value.month){N.isOtherMonth=true;N.isVisible=this.showOtherMonthDays}if(this._isRestrictedDate(F)){N.isRestricted=true;N.isDisabled=true}if(!N.isDisabled){if(F<this.getMinDate()||F>this.getMaxDate()||this._isDisabled(F)){N.isDisabled=true}}if(S.month==H.month&&S.day==H.day&&S.year==H.year){N.isToday=true}if(S.isWeekend()){N.isWeekend=true}A.data(this.element,"cellContent"+L.substring(1),N);A.data(this.element,L.substring(1),N);O[J]=N;J++;A.jqx.utilities.html(Q,S.day);this._applyCellStyle(N,Q,Q);S=new A.jqx._jqxDateTimeInput.getDateTime(new Date(S._addDays(1)))}}var E=A.data(this.element,T[0].id);if(E!=undefined&&E!=null){E.cells=O}this.renderedCells=O;this._refreshOtherMonthRows(E,M)},_getDecadeAndCenturiesData:function(){var D=new Array();var C=new Array();var K=this.getMaxDate().getFullYear()-this.getMinDate().getFullYear();if(K<12){K=12}var L=this.getMinDate();var J=this.getMaxDate();var H=this.value.dateTime.getFullYear();if(this.view=="decade"){if(H+12>J.getFullYear()){H=J.getFullYear()-11}if(H<L.getFullYear()){H=L.getFullYear()}for(var E=0;E<K;E++){var N=new Date(L.getFullYear()+E,0,1);if(L.getFullYear()<=H&&H<=N.getFullYear()){var M=new Date(N.getFullYear(),N.getMonth(),1);for(var B=0;B<12;B++){var G=new Date(M.getFullYear()+B,this.value.dateTime.getMonth(),this.value.dateTime.getDate());var I=G.getFullYear();if(L.getFullYear()<=I&&I<=J.getFullYear()){D.push(I);C.push(G);if(B==0){this._renderStartDate=G}this._renderEndDate=G}else{D.push(I);C.push(G)}}break}}}else{if(this.view=="centuries"){for(var E=0;E<K;E+=120){var N=new Date(L.getFullYear()+E+120,0,1);if(L.getFullYear()<=H&&H<=N.getFullYear()){var M=new Date(N.getFullYear()-130,N.getMonth(),1);if(M<L){M=L}for(var B=0;B<12;B++){var F=new Date(M.getFullYear()+B*10,M.getMonth(),1);if(M.getFullYear()>=L.getFullYear()&&F.getFullYear()<=J.getFullYear()){D.push("<span style='visibility: hidden;'>-</span>"+F.getFullYear()+"-"+(F.getFullYear()+9));C.push(F);if(B==0){this._renderCenturyStartDate=F}this._renderCenturyEndDate=new Date(F.getFullYear()+9,0,1)}}break}}}}return{years:D,dates:C}},refreshViews:function(Y,P,E){var W=this;var R=new Array();var I=Y.find("#cellTable"+E.toString());var Z=this._getDecadeAndCenturiesData();var O=Z.years;var X=Z.dates;var J=0;var S=this.getMinDate();var M=this.getMaxDate();for(var H=0;H<3;H++){for(var K=0;K<4;K++){var U=H+1;var G=K;if(this.rtl){G=3-G}var i=G+1;var a=new Date(this.value.dateTime);a.setDate(1);a.setMonth(H*4+G);var Q=new A.jqx._jqxCalendar.cell(a);var V=I[0].rows["row"+(1+H)+this.element.id];var N=A(V.cells[K]);Q.isSelected=false;Q.isVisible=true;Q.element=N;Q.row=H;Q.column=K;Q.index=R.length;var F="";if(this.view=="year"){var L=this.localization.calendar.months.names;var T=L[H*4+G];switch(this.monthNameFormat){case"default":T=this.localization.calendar.months.namesAbbr[H*4+G];break;case"shortest":T=this.localization.calendar.months.namesShort[H*4+G];break;case"firstTwoLetters":T=T.substring(0,2);break;case"firstLetter":T=T.substring(0,1);break}F=T}else{if(this.view=="decade"||this.view=="centuries"){F=O[H*4+G];if(undefined==F){F="<span style='cursor: default; visibility: hidden;'>2013</span>"}Q.setDate(X[H*4+G])}}var a=Q.getDate();if(this.view=="year"){if(a.getMonth()==this.getDate().getMonth()&&a.getFullYear()==this.getDate().getFullYear()){Q.isSelected=true}}else{if(a.getFullYear()==this.getDate().getFullYear()){Q.isSelected=true}}if(this.view=="year"){if(this._getYearAndMonthPart(a)<this._getYearAndMonthPart(S)){Q.isDisabled=true}if(this._getYearAndMonthPart(a)>this._getYearAndMonthPart(M)){Q.isDisabled=true}}else{if(a.getFullYear()<S.getFullYear()){Q.isDisabled=true}if(a.getFullYear()>M.getFullYear()){Q.isDisabled=true}}A.jqx.utilities.html(N,F);R[J]=Q;J++}}var j=A.data(this.element,Y[0].id);if(j!=undefined&&j!=null){j.cells=R}this.renderedCells=R;this._applyCellStyles()},_createViewClone:function(){var B=this.host.find(".jqx-calendar-month");var C=B.clone();C.css("position","absolute");C.css("top",B.position().top);return C},_addCellsTable:function(B,D){var E=this;var G=this.showFooter?20:0;if(this.view!="month"){D.height(this.host.height()-this.titleHeight)}else{D.height(this.host.height()-this.titleHeight-this.columnHeaderHeight-G)}this._viewAnimating=true;var F=this.host.find(".jqx-calendar-month-container");F.css("position","relative");var C=this.host.find(".jqx-calendar-month");var H=this._createViewClone();F.append(H);if(this.view!="month"){this.header.fadeOut(0);if(this.showWeekNumbers){this.rowHeader.fadeOut(0)}if(this.showFooter){this._footer.fadeOut(0)}}else{this.header.fadeIn(this.navigationDelay+200);if(this.showWeekNumbers){this.rowHeader.fadeIn(this.navigationDelay+200)}if(this.showFooter){this._footer.fadeIn(this.navigationDelay+200)}}B.children().remove();B.append(D);this._animateViews(H,D,function(){if(!E.selectedDate&&E.selectionMode!="range"){E.selectedDate=E.renderedCells[0].getDate()}try{E.renderedCells[0].element.focus();setTimeout(function(){E.renderedCells[0].element.focus()},10)}catch(I){}E._viewAnimating=false});D.addClass(this.toThemeProperty("jqx-calendar-view"))},_animateViews:function(E,D,C){var B=this;B._viewAnimating=true;if(B.oldView==B.view){E.remove();D.fadeOut(0);D.fadeIn(0);C();return}E.fadeOut(this.navigationDelay+100,function(){E.remove()});D.fadeOut(0);D.fadeIn(this.navigationDelay+200,function(){C()})},focus:function(){if(this.disabled){return}try{if(this.renderedCells&&this.renderedCells.length>0){var B=this;var D=false;if(!B.selectedDate&&B.selectionMode!="range"){this.setDate(new Date(),"mouse")}this.element.focus()}}catch(C){}},renderViews:function(Ae,R,M){var Ab=this;var a=new Array();var Af=A("<table role='grid' style='border-color: transparent; width: 100%; height: 100%;' cellspacing='2' cellpadding='0' id=cellTable"+M.toString()+"><tr role='row' id='row1"+this.element.id+"'><td role='gridcell'></td><td role='gridcell'></td><td role='gridcell'></td><td role='gridcell'></td></tr><tr role='row' id='row2"+this.element.id+"'><td role='gridcell'></td><td role='gridcell'></td><td role='gridcell'></td><td role='gridcell'></td></tr><tr role='row' id='row3"+this.element.id+"'><td role='gridcell'></td><td role='gridcell'></td><td role='gridcell'></td><td role='gridcell'></td></tr></table>");var L=this.host.find(".jqx-calendar-month-container");L.css("position","relative");var Aa=Ae.find("#cellsTable"+Ae[0].id);Aa[0].style.borderColor="transparent";var j=this._getDecadeAndCenturiesData();var U=j.years;var i=j.dates;var N=0;var Y=this.getMinDate();var S=this.getMaxDate();var O=new Date(this.value.dateTime);O.setDate(1);for(var Ah=0;Ah<3;Ah++){for(var Ag=0;Ag<4;Ag++){var Z=Ah+1;var P=Ag;if(this.rtl){P=3-P}var k=P+1;var X=Af[0].rows["row"+(1+Ah)+this.element.id];var Ad=new Date(O);Ad.setMonth(Ah*4+P);var W=new A.jqx._jqxCalendar.cell(Ad);var K=A(X.cells[Ag]);W.isVisible=true;W.element=K;W.row=Ah;W.column=Ag;W.index=a.length;W.isSelected=false;var J="";if(this.view=="year"){if(Ad.getMonth()==this.getDate().getMonth()&&Ad.getFullYear()==this.getDate().getFullYear()){W.isSelected=true}var T=this.localization.calendar.months.names;var Q=T[Ah*4+P];switch(this.monthNameFormat){case"default":Q=this.localization.calendar.months.namesAbbr[Ah*4+P];break;case"shortest":Q=this.localization.calendar.months.namesShort[Ah*4+P];break;case"firstTwoLetters":Q=Q.substring(0,2);break;case"firstLetter":Q=Q.substring(0,1);break}J=Q}else{if(this.view=="decade"||this.view=="centuries"){J=U[Ah*4+P];W.setDate(i[Ah*4+P]);if(W.getDate().getFullYear()==this.getDate().getFullYear()){W.isSelected=true}if(undefined==J){J="<span style='cursor: default; visibility: hidden;'>2013</span>"}}}var Ad=W.getDate();if(this.view=="year"){var V=new Date(Ad);V.setDate(1);V.setHours(0,0,0,0);V.setMonth(Ad.getMonth()+1);V=new Date(V.valueOf()-1);if(this._getYearAndMonthPart(Ad)<this._getYearAndMonthPart(Y)||this._getYearAndMonthPart(Ad)>this._getYearAndMonthPart(S)||!this._hasUnrestrictedRanges(Ad,V)){W.isDisabled=true}}else{var Ac=new Date(Ad);Ac.setMonth(0);Ac.setDate(1);Ac.setHours(0,0,0,0);Ac.setFullYear(Ad.getFullYear()+1);Ac=new Date(Ac.valueOf()-1);if(Ad.getFullYear()<Y.getFullYear()||Ad.getFullYear()>S.getFullYear()||!this._hasUnrestrictedRanges(Ad,Ac)){W.isDisabled=true}}A.jqx.utilities.html(K,J);a[N]=W;N++}}A.each(a,function(){var C=this.element;var B=this;if(!Ab.disabled){Ab.addHandler(C,"mousedown",function(D){Ab._setDateAndSwitchViews(B,D,"mouse")});Ab.addHandler(C,"mouseover",function(D){var E=Ab.renderedCells[B.index];if(Ab.view!="centuries"&&E.element.html().toLowerCase().indexOf("span")!=-1){return}E.isHighlighted=true;Ab._applyCellStyle(E,E.element,E.element)});Ab.addHandler(C,"mouseout",function(D){var E=Ab.renderedCells[B.index];if(Ab.view!="centuries"&&E.element.html().toLowerCase().indexOf("span")!=-1){return}E.isHighlighted=false;Ab._applyCellStyle(E,E.element,E.element)})}});var r=A.data(this.element,Ae[0].id);if(r!=undefined&&r!=null){r.cells=a}this.renderedCells=a;this._addCellsTable(Aa,Af);this._applyCellStyles()},_setDateAndSwitchViews:function(I,N,C){if(!this._viewAnimating&&!this._animating){var M=this.getDate();var B=this.renderedCells[I.index].getDate();var D=this.value.dateTime.getDate();var H=new Date(B);if(this.views.indexOf("month")!=-1){H.setDate(D)}else{H.setDate(1);B.setDate(1)}if(H.getMonth()==B.getMonth()){B=H}var F=this.getMinDate();var K=this.getMaxDate();if(this.view=="year"){if(this._getYearAndMonthPart(B)<this._getYearAndMonthPart(F)){return}if(this._getYearAndMonthPart(B)>this._getYearAndMonthPart(K)){return}}else{if(B.getFullYear()<F.getFullYear()){return}if(B.getFullYear()>K.getFullYear()){return}}if(this.selectionMode!="range"){this._selectDate(B,C)}this.oldView=this.view;switch(this.view){case"year":this.view="month";break;case"decade":this.view="year";break}if(this.views.indexOf("month")==-1){this.view="year"}if(this.views.indexOf("year")==-1){this.view="decade"}if(this.view=="year"){if(this._getYearAndMonthPart(B)<this._getYearAndMonthPart(F)){B=F}if(this._getYearAndMonthPart(B)>this._getYearAndMonthPart(K)){B=K}}else{if(B.getFullYear()<F.getFullYear()){B=F}if(B.getFullYear()>K.getFullYear()){B=K}}if(this.changing&&(this.selectedDate&&(this.selectedDate.getFullYear()!=B.getFullYear()||this.selectedDate.getMonth()!=B.getMonth()||this.selectedDate.getDate()!=B.getDate()))){B=this.selectedDate}this.value._setYear(B.getFullYear());this.value._setDay(B.getDate());this.value._setMonth(B.getMonth()+1);this.value._setDay(B.getDate());var E=this.getVisibleDate();var J=this.getFirstDayOfWeek(E);var L="View"+this.element.id;this.renderCalendarCells(this.month,J,L,true);this.refreshTitle(this.month);if(this.showWeekNumbers){this.refreshRowHeader(this.month,L)}if(this.views.length==3){if(this.view=="month"){if(this.selectionMode!="range"){this._selectDate(this.selectedDate,"view")}else{var G=this;A.each(this.renderedCells,function(O){var Q=this;var T=Q.getDate();var R=A(Q.element);var S=R;if(R.length==0){return false}var P=function(U){if(U==null){return new Date()}var V=new Date();V.setHours(0,0,0,0);V.setFullYear(U.getFullYear(),U.getMonth(),U.getDate());return V};if(!Q.isOtherMonth&&P(T).toString()==P(B).toString()){G.value._setMonth(B.getMonth()+1);G.value._setDay(B.getDate());G.value._setYear(B.getFullYear())}Q.isSelected=false;Q.isDisabled=false;if(P(T)<P(G.selection.from)&&G._clicks==1){Q.isDisabled=true}if(G.getMaxDate()<T){Q.isDisabled=true}if(G.getMinDate()>T){Q.isDisabled=true}if(G._isDisabled(T)){Q.isDisabled=true}if(!Q.isDisabled){if(P(T)>=P(G.selection.from)&&P(T)<=P(G.selection.to)){Q.isSelected=true}}});this._applyCellStyles()}}}if(this.view!="month"){if(this.oldView=="year"||(this.views.indexOf("year")==-1&&this.view=="decade")){if(C!="keyboard"){this._raiseEvent("3")}this._raiseEvent("5",{selectionType:"mouse"})}}this._raiseEvent("8")}},renderCalendarCells:function(a,Q,F,H){if(this.view=="year"||this.view=="decade"||this.view=="centuries"){this.renderViews(a,Q,F);return}var k=A("<table role='grid' style='width: 100%; height: 100%; border-color: transparent;' cellspacing='2' cellpadding='1' id=cellTable"+F.toString()+"><tr role='row'><td role='gridcell'></td><td role='gridcell'></td><td role='gridcell'></td><td role='gridcell'></td><td role='gridcell'></td><td role='gridcell'></td><td role='gridcell'></td></tr><tr role='row'><td role='gridcell'></td><td role='gridcell'></td><td role='gridcell'></td><td role='gridcell'></td><td role='gridcell'></td><td role='gridcell'></td><td role='gridcell'></td></tr><tr role='row'><td role='gridcell'></td><td role='gridcell'></td><td role='gridcell'></td><td role='gridcell'></td><td role='gridcell'></td><td role='gridcell'></td><td role='gridcell'></td></tr><tr role='row'><td role='gridcell'></td><td role='gridcell'></td><td role='gridcell'></td><td role='gridcell'></td><td role='gridcell'></td><td role='gridcell'></td><td role='gridcell'></td></tr><tr role='row'><td role='gridcell'></td><td role='gridcell'></td><td role='gridcell'></td><td role='gridcell'></td><td role='gridcell'></td><td role='gridcell'></td><td role='gridcell'></td></tr><tr role='row'><td role='gridcell'></td><td role='gridcell'></td><td role='gridcell'></td><td role='gridcell'></td><td role='gridcell'></td><td role='gridcell'></td><td role='gridcell'></td></tr></table>");var r=this.table;r[0].style.borderColor="transparent";if(H==undefined){var U=r.find("#cellTable"+F.toString());if(U!=null){U.remove()}r.append(k)}var P=Q;var R=this.showDayNames?1:0;var T=this.showWeekNumbers?1:0;var V=new Array();var K=0;var I=(a.width()-this.rowHeaderWidth-2)/7;if(!this.showWeekNumbers){I=(a.width()-2)/7}I=parseInt(I);var Z=new A.jqx._jqxDateTimeInput.getDateTime(new Date());for(var J=0;J<6;J++){for(var L=0;L<7;L++){var W=J+1;var O=L;if(this.rtl){O=6-O}var j=O+1;var G="#cell"+W+j+this.element.id;var Y=new Date(P.dateTime.getFullYear(),P.dateTime.getMonth(),P.dateTime.getDate());var S=new A.jqx._jqxCalendar.cell(Y);var N=A(k[0].rows[J].cells[j-1]);N[0].id=G.substring(1);S.isVisible=true;S.isDisabled=false;if(P.month!=this.value.month){S.isOtherMonth=true;S.isVisible=this.showOtherMonthDays}if(this._isRestrictedDate(Y)){S.isRestricted=true;S.isDisabled=true}if(!S.isDisabled){if(Y<this.getMinDate()||Y>this.getMaxDate()||this._isDisabled(Y)){S.isDisabled=true}}if(P.month==Z.month&&P.day==Z.day&&P.year==Z.year){S.isToday=true}if(P.isWeekend()){S.isWeekend=true}S.element=N;S.row=R;S.column=T;A.jqx.utilities.html(N,P.day);P=new A.jqx._jqxDateTimeInput.getDateTime(new Date(P._addDays(1)));A.data(this.element,"cellContent"+G.substring(1),S);A.data(this.element,""+G.substring(1),S);var i=this;this.addHandler(N,"mousedown",function(D){if(!i.readOnly&&!i.disabled){var C=A(D.target);var E=A.data(i.element,C[0].id);var c=i._raiseEvent(3,D);if(E!=null&&E!=undefined){var B=E.getDate();if(i.getMinDate()<=B&&B<=i.getMaxDate()){if(!E.isDisabled){if(E.isOtherMonth&&i.enableAutoNavigation){if(E.row<2){i.navigateBackward()}else{i.navigateForward()}i._selectDate(E.getDate(),"mouse",D.shiftKey)}else{var e=new Date(i.getDate());i._selectDate(E.getDate(),"mouse",D.shiftKey);i.value._setYear(B.getFullYear());i.value._setDay(1);i.value._setMonth(B.getMonth()+1);i.value._setDay(B.getDate());var d=i.host.find(".jqx-calendar-month");d.stop();d.css("margin-left","0px");var b=i.getDate();i._raiseEvent("2");if(E.isOtherMonth){i._raiseEvent("5",{selectionType:"mouse"})}}}}}return false}});if(!i.disabled){var M=function(B,E){if(!i.readOnly){var C=A(B.target);var D=A.data(i.element,C[0].id);if(D!=null&&D!=undefined){var b=D.getDate();if(i.getMinDate()<=b&&b<=i.getMaxDate()){D.isHighlighted=E;i._applyCellStyle(D,D.element,C)}}}};this.addHandler(N,"mouseenter",function(B){M(B,true);return false});this.addHandler(N,"mouseleave",function(B){M(B,false);return false})}T++;V[K]=S;K++}T=0;R++}var X=A.data(this.element,a[0].id);if(X!=undefined&&X!=null){X.cells=V}this.renderedCells=V;if(H!=undefined){this._addCellsTable(r,k)}this._applyCellStyles();this._refreshOtherMonthRows(X,F)},setMaxDate:function(B,C){if(B!=null&&typeof(B)=="string"){B=new Date(B);if(B=="Invalid Date"){return}}this.maxDate=A.jqx._jqxDateTimeInput.getDateTime(B);if(C!==false){this.render()}},getMaxDate:function(){if(this.maxDate!=null&&this.maxDate!=undefined){return this.maxDate.dateTime}return null},setMinDate:function(B,C){if(B!=null&&typeof(B)=="string"){B=new Date(B);if(B=="Invalid Date"){return}}this.minDate=A.jqx._jqxDateTimeInput.getDateTime(B);if(C!==false){this.render()}},getMinDate:function(){if(this.minDate!=null&&this.minDate!=undefined){return this.minDate.dateTime}return null},navigateTo:function(J,E){if(this.view=="month"){var K=this.getMinDate();var I=new Date(this.getMaxDate().getFullYear(),this.getMaxDate().getMonth()+1,this.getMaxDate().getDate());if((J<this._getYearAndMonthPart(K))||(J>this._getYearAndMonthPart(I))){return false}}else{if(J&&(J.getFullYear()<this.getMinDate().getFullYear()||J.getFullYear()>this.getMaxDate().getFullYear())){return false}}if(J==null){return false}if(E==undefined){var F=this;if(this._animating){return}this._animating=true;var L=this.host.find(".jqx-calendar-month-container");if(this._viewClone){this._viewClone.stop();this._viewClone.remove()}if(this._newViewClone){this._newViewClone.stop();this._newViewClone.remove()}var D=this.host.find(".jqx-calendar-month");D.stop();D.css("margin-left","0px");var H=D.clone();this._viewClone=H;var C=new Date(this.value.dateTime);this.value._setYear(J.getFullYear());this.value._setDay(J.getDate());this.value._setMonth(J.getMonth()+1);F.refreshControl();L.css("position","relative");H.css("position","absolute");H.css("top",D.position().top);L.append(H);if(A.jqx.browser.msie&&A.jqx.browser.version<8){this.month.css("position","relative");this.month.css("overflow","hidden");this.table.css("position","relative");this.table.css("overflow","hidden")}var B=-this.host.width();if(J<C){if(this.view=="month"&&J.getMonth()!=C.getMonth()){B=this.host.width()}else{if(J.getFullYear()!=C.getFullYear()){B=this.host.width()}}}H.animate({marginLeft:parseInt(B)+"px"},this.navigationDelay,function(){H.remove()});var G=D.clone();this._newViewClone=G;G.css("position","absolute");G.css("top",D.position().top);L.append(G);G.css("margin-left",-B);D.css("visibility","hidden");G.animate({marginLeft:"0px"},this.navigationDelay,function(){G.remove();D.css("visibility","inherit");F._animating=false})}else{this.value._setYear(J.getFullYear());this.value._setDay(J.getDate());this.value._setMonth(J.getMonth()+1);var D=this.host.find(".jqx-calendar-month");D.stop();D.css("margin-left","0px");this.refreshControl()}this._raiseEvent("2");this._raiseEvent("8");return true},setDate:function(B){if(B!=null&&typeof(B)=="string"){B=new Date(B)}if(this.canRender==false){this.canRender=true;this.render()}this.navigateTo(B,"api");this._selectDate(B);if(this.selectionMode=="range"){this._selectDate(B,"mouse")}return true},val:function(B){if(arguments.length!=0){if(B==null){this.setDate(null)}if(B instanceof Date){this.setDate(B)}if(typeof(B)=="string"){this.setDate(B)}}return this.getDate()},getDate:function(){if(this.selectedDate==undefined){return new Date()}return this.selectedDate},getValue:function(){if(this.value==undefined){return new Date()}return this.value.dateTime},setRange:function(C,B){if(this.canRender==false){this.canRender=true;this.render()}this.navigateTo(C,"api");this._selectDate(C,"mouse");this._selectDate(B,"mouse")},getRange:function(){return this.selection},_selectDate:function(K,J,H){if(this.selectionMode=="none"){return}if(J==null||J==undefined){J="none"}if(H==null||H==undefined){H=false}var F=A.data(this.element,"View"+this.element.id);if(F==undefined||F==null){return}if(this.changing){if(K&&this.selectedDate){if(this.selectedDate.getFullYear()!=K.getFullYear()||this.selectedDate.getDate()!=K.getDate()||this.selectedDate.getMonth()!=K.getMonth()){var C=this.changing(this.selectedDate,K)}if(C){K=C}}}var D=this;if(this.input){if(K!=null){this.input.val(K.toString())}else{this.input.val("")}}var B=this.selectedDate;this.selectedDate=K;if(this.view!="month"){if(B!=K){this._raiseEvent(7,{selectionType:J})}A.each(this.renderedCells,function(M){var N=this;var O=N.getDate();var P=A(N.element);var Q=P.find("#cellContent"+P[0].id);if(K==null){N.isSelected=false;N.isDisabled=false}else{N.isSelected=false;if(O){if((O.getMonth()==K.getMonth()&&D.view=="year"&&O.getFullYear()==K.getFullYear())||(D.view=="decade"&&O.getFullYear()==K.getFullYear())){N.isSelected=true;try{if(J!="none"){N.element.focus()}}catch(L){}}}}D._applyCellStyle(N,P,P)});if(this.change){this.change(K)}return}if(this.view=="month"){if(this.selectionMode=="range"&&J=="key"){var I=this.getVisibleDate();var G=this.getFirstDayOfWeek(I);this.refreshCalendarCells(this.month,G,"View"+this.element.id)}}var E=false;A.each(this.renderedCells,function(L){var Q=this;var U=Q.getDate();var P=A(Q.element);var V=P;if(P.length==0){return false}if(K==null){Q.isSelected=false;Q.isDisabled=false;if(L==0){D.selection={from:null,to:null};D._raiseEvent("2");D._raiseEvent("5",{selectionType:J})}}else{if(D.selectionMode!="range"||J=="key"){if(U.getDate()==K.getDate()&&U.getMonth()==K.getMonth()&&U.getFullYear()==K.getFullYear()&&Q.isSelected){D._applyCellStyle(Q,P,V);D._raiseEvent("5",{selectionType:J});return}if(Q.isSelected){D._raiseEvent("6",{selectionType:J})}Q.isSelected=false;if(U.getDate()==K.getDate()&&U.getMonth()==K.getMonth()&&U.getFullYear()==K.getFullYear()){Q.isSelected=true;if(L==0){D.selection={date:K}}try{if(J!="none"){Q.element.focus();D.host.focus()}}catch(M){}if(!Q.isOtherMonth){D.value._setMonth(K.getMonth()+1);D.value._setDay(K.getDate());D.value._setYear(K.getFullYear());D._raiseEvent("2");D._raiseEvent("5",{selectionType:J})}}if(D.selectionMode=="range"){D._clicks=0;D.selection={from:K,to:K}}}else{if(D.selectionMode=="range"){if(J=="view"){Q.isSelected=false;Q.isDisabled=false;if(D.getMaxDate()<U){Q.isDisabled=true}if(D.getMinDate()>U){Q.isDisabled=true}if(D._isRestrictedDate(U)){Q.isDisabled=true;Q.isRestricted=true}if(!Q.isDisabled&&D._isDisabled(U)){Q.isDisabled=true}D._applyCellStyle(Q,P,V);return true}if(L==0){if(J!="none"){if(D._clicks==undefined){D._clicks=0}D._clicks++;if(H){D._clicks++}if(D._clicks==1){D.selection={from:K,to:K}}else{var S=D.selection.from;var O=S<=K?S:K;var R=S<=K?K:S;if(O){var W=new Date(O.getFullYear(),O.getMonth(),O.getDate())}if(R){var T=new Date(R.getFullYear(),R.getMonth(),R.getDate(),23,59,59)}D.selection={from:W,to:T};D._clicks=0}}else{if(D.selection==null||D.selection.from==null){D.selection={from:K,to:K};if(D._clicks==undefined){D._clicks=0}D._clicks++;if(D._clicks==2){D._clicks=0}}}}var N=function(Y){if(Y==null){return new Date()}var X=new Date();X.setHours(0,0,0,0);X.setFullYear(Y.getFullYear(),Y.getMonth(),Y.getDate());return X};if(!Q.isOtherMonth&&N(U).toString()==N(K).toString()){D.value._setMonth(K.getMonth()+1);D.value._setDay(K.getDate());D.value._setYear(K.getFullYear());D._raiseEvent("2");D._raiseEvent("5",{selectionType:J})}Q.isSelected=false;Q.isDisabled=E;if(N(U)<N(D.selection.from)&&D._clicks==1){Q.isDisabled=true}if(D.getMaxDate()<U){Q.isDisabled=true}if(D.getMinDate()>U){Q.isDisabled=true}if(D._isRestrictedDate(U)){Q.isRestricted=true;Q.isDisabled=true}if(!Q.isDisabled&&D._isDisabled(U)){Q.isDisabled=true}if(!Q.isDisabled){if(N(U)>=N(D.selection.from)&&N(U)<=N(D.selection.to)){Q.isSelected=true}}else{if(!D.allowRestrictedDaysInRange&&N(U)>=N(D.selection.from)&&D.selection.to==D.selection.from){E=true}}}}}D._applyCellStyle(Q,P,V)});if(D.selectionMode=="range"&&D._clicks==0){D._raiseEvent(7,{selectionType:J});return}else{if(D.selectionMode=="range"){return}}if(B!=K){D._raiseEvent(7,{selectionType:J});if(this.change){this.change(K)}}},_getSelectedDate:function(){var B=A.data(this.element,"View"+this.element.id);if(B==undefined||B==null){return}if(this.view!="month"){return this.selectedDate}for(var E=0;E<B.cells.length;E++){var D=B.cells[E];var C=D.getDate();if(D.isSelected){return C}}if(this.selectedDate){return this.selectedDate}},_getSelectedCell:function(){var B=A.data(this.element,"View"+this.element.id);if(B==undefined||B==null){return}for(var E=0;E<B.cells.length;E++){var D=B.cells[E];var C=D.getDate();if(D.isSelected){return D}}},_applyCellStyle:function(G,C,B){var D=this;if(B==null||(B!=null&&B.length==0)){B=C}var F="";F=this.toThemeProperty("jqx-rc-all");F+=" "+this.toThemeProperty("jqx-item");if(this.disabled||(G.isDisabled&&!G.isRestricted)){F+=" "+this.toThemeProperty("jqx-calendar-cell-disabled");F+=" "+this.toThemeProperty("jqx-fill-state-disabled")}if(!this.disabled&&G.isRestricted){F+=" "+this.toThemeProperty("jqx-calendar-cell-restrictedDate")}if(G.isOtherMonth&&this.enableOtherMonthDays&&G.isVisible){F+=" "+this.toThemeProperty("jqx-calendar-cell-othermonth")}if(G.isWeekend&&this.enableWeekend&&G.isVisible&&G.isVisible){F+=" "+this.toThemeProperty("jqx-calendar-cell-weekend")}if(!G.isVisible){F+=" "+this.toThemeProperty("jqx-calendar-cell-hidden")}else{F+=" "+this.toThemeProperty("jqx-calendar-cell");if(this.view!="month"){if(B.length>0&&B.html().toLowerCase().indexOf("span")!=-1){B.css("cursor","default")}}}B.removeAttr("aria-selected");if(G.isSelected&&G.isVisible){F+=" "+this.toThemeProperty("jqx-calendar-cell-selected");F+=" "+this.toThemeProperty("jqx-fill-state-pressed");B.attr("aria-selected",true);this.host.removeAttr("aria-activedescendant").attr("aria-activedescendant",B[0].id);var H=G.getDate();if(this._isDisabled(H)){F+=" "+this.toThemeProperty("jqx-calendar-cell-selected-invalid")}}if(G.isHighlighted&&G.isVisible&&this.enableHover){if(!G.isDisabled){F+=" "+this.toThemeProperty("jqx-calendar-cell-hover");F+=" "+this.toThemeProperty("jqx-fill-state-hover")}}F+=" "+this.toThemeProperty("jqx-calendar-cell-"+this.view);if(G.isToday&&G.isVisible){F+=" "+this.toThemeProperty("jqx-calendar-cell-today")}B[0].className=F;if(this.specialDates.length>0){var E=this;A.each(this.specialDates,function(){if(this.Class!=undefined&&this.Class!=null&&this.Class!=""){B.removeClass(this.Class)}else{B.removeClass(D.toThemeProperty("jqx-calendar-cell-specialDate"))}var I=G.getDate();if(I.getFullYear()==this.Date.getFullYear()&&I.getMonth()==this.Date.getMonth()&&I.getDate()==this.Date.getDate()){if(G.tooltip==null&&this.Tooltip!=null){G.tooltip=this.Tooltip;if(A(B).jqxTooltip){var J=this.Class;A(B).jqxTooltip({value:{cell:G,specialDate:this.Date},name:E.element.id,content:this.Tooltip,position:"mouse",theme:E.theme,opening:function(K){if(B.hasClass(D.toThemeProperty("jqx-calendar-cell-specialDate"))){return true}if(B.hasClass(J)){return true}return false}})}}B.removeClass(D.toThemeProperty("jqx-calendar-cell-othermonth"));B.removeClass(D.toThemeProperty("jqx-calendar-cell-weekend"));if(this.Class==undefined||this.Class==""){B.addClass(D.toThemeProperty("jqx-calendar-cell-specialDate"));return false}else{B.addClass(this.Class);return false}}})}},_applyCellStyles:function(){var F=A.data(this.element,"View"+this.element.id);if(F==undefined||F==null){return}for(var C=0;C<F.cells.length;C++){var D=F.cells[C];var E=A(D.element);var B=E.find("#cellContent"+E[0].id);if(B.length==0){B=E}this._applyCellStyle(D,E,B)}},getWeekOfYear:function(G){var E=new Date(G.dateTime);dowOffset=this.firstDayOfWeek;var D=new Date(E.getFullYear(),0,1);var F=D.getDay()-dowOffset;F=(F>=0?F:F+7);var C=Math.floor((E.getTime()-D.getTime()-(E.getTimezoneOffset()-D.getTimezoneOffset())*60000)/86400000)+1;var B;if(F<4){B=Math.floor((C+F-1)/7)+1;if(B>52){nYear=new Date(E.getFullYear()+1,0,1);nday=nYear.getDay()-dowOffset;nday=nday>=0?nday:nday+7;B=nday<4?1:53}}else{B=Math.floor((C+F-1)/7)}return B},renderColumnHeader:function(F){if(!this.showDayNames){return}var G=A("<table role='grid' style='border-spacing: 0px; border-collapse: collapse; width: 100%; height: 100%;' cellspacing='0' cellpadding='1'><tr role='row'><td role='gridcell'></td><td role='gridcell'></td><td role='gridcell'></td><td role='gridcell'></td><td role='gridcell'></td><td role='gridcell'></td><td role='gridcell'></td></tr></table>");G.find("table").addClass(this.toThemeProperty("jqx-reset"));G.find("tr").addClass(this.toThemeProperty("jqx-reset"));G.find("td").css({background:"transparent",padding:1,margin:0,border:"none"});G.addClass(this.toThemeProperty("jqx-reset"));G.addClass(this.toThemeProperty("jqx-widget-content"));G.addClass(this.toThemeProperty("jqx-calendar-column-header"));this.columnHeader.append(G);var T=this.getVisibleDate();var K=this.getFirstDayOfWeek(T);var O=K.dayOfWeek;var V=this.getWeekOfYear(K);var D=this.firstDayOfWeek;var E=this.localization.calendar.days.names;var L=new Array();var S=K;var M=(F.width()-this.rowHeaderWidth-2)/7;if(!this.showWeekNumbers){M=(F.width()-2)/7}for(var B=0;B<7;B++){var R=E[D];if(this.rtl){R=E[6-D]}switch(this.dayNameFormat){case"default":R=this.localization.calendar.days.namesAbbr[D];if(this.rtl){R=this.localization.calendar.days.namesAbbr[6-D]}break;case"shortest":R=this.localization.calendar.days.namesShort[D];if(this.rtl){R=this.localization.calendar.days.namesShort[6-D]}break;case"firstTwoLetters":R=R.substring(0,2);break;case"firstLetter":R=R.substring(0,1);break}var P=new A.jqx._jqxCalendar.cell(S.dateTime);var J=B+1;var N=J+this.element.id;var I=A(G[0].rows[0].cells[B]);var C=B;if(this.enableTooltips){if(A(I).jqxTooltip){A(I).jqxTooltip({name:this.element.id,content:E[D],theme:this.theme,position:"mouse"})}}if(D>=6){D=0}else{D++}B=C;P.element=I;P.row=0;P.column=B+1;var U=this._textwidth(R);var Q="<div style='padding: 0; margin: 0; border: none; background: transparent;' id='columnCell"+N+"'>"+R+"</div>";I.append(Q);I.find("#columnCell"+N).addClass(this.toThemeProperty("jqx-calendar-column-cell"));I.width(M);if(this.disabled){I.find("#columnCell"+N).addClass(this.toThemeProperty("jqx-calendar-column-cell-disabled"))}if(U>0&&M>0){while(U>I.width()){if(R.length==0){break}R=R.substring(0,R.length-1);A.jqx.utilities.html(I.find("#columnCell"+N),R);U=this._textwidth(R)}}L[B]=P;S=new A.jqx._jqxDateTimeInput.getDateTime(new Date(S._addDays(1)))}if(parseInt(this.columnHeader.width())>parseInt(this.host.width())){this.columnHeader.width(this.host.width())}var H=A.data(this.element,F[0].id);H.columnCells=L},_format:function(B,C,D){var F=false;try{if(Globalize!=undefined){F=true}}catch(E){}if(A.global){A.global.culture.calendar=this.localization.calendar;return A.global.format(B,C,this.culture)}else{if(F){try{if(Globalize.cultures[this.culture]){Globalize.cultures[this.culture].calendar=this.localization.calendar;return Globalize.format(B,C,this.culture)}else{return Globalize.format(B,C,this.culture)}}catch(E){return Globalize.format(B,C)}}else{if(A.jqx.dataFormat){return A.jqx.dataFormat.formatdate(B,C,this.localization.calendar)}}}},_textwidth:function(B){var D=A("<span>"+B+"</span>");D.addClass(this.toThemeProperty("jqx-calendar-column-cell"));A(this.host).append(D);var C=D.width();D.remove();return C},_textheight:function(B){var D=A("<span>"+B+"</span>");A(this.host).append(D);var C=D.height();D.remove();return C},_renderRowHeader:function(G){var P=this.getVisibleDate();var N=this.getFirstDayOfWeek(P);var O=N.dayOfWeek;var C=this.getWeekOfYear(N);var J=new A.jqx._jqxDateTimeInput.getDateTime(new Date(N.dateTime));J._addDays(5);J.dayOfWeek=J.dateTime.getDay();var L=this.getWeekOfYear(J);if(53==C&&J.dateTime.getMonth()==0){C=1}var K=A("<table style='overflow: hidden; width: 100%; height: 100%;' cellspacing='0' cellpadding='1'><tr><td></td></tr><tr><td/></tr><tr><td/></tr><tr><td/></tr><tr><td/></tr><tr><td/></tr></table>");K.find("table").addClass(this.toThemeProperty("jqx-reset"));K.find("td").addClass(this.toThemeProperty("jqx-reset"));K.find("tr").addClass(this.toThemeProperty("jqx-reset"));K.addClass(this.toThemeProperty("jqx-calendar-row-header"));K.width(this.rowHeaderWidth);this.rowHeader.append(K);var Q=N;var B=new Array();for(var H=0;H<6;H++){var R=C.toString();var E=new A.jqx._jqxCalendar.cell(Q.dateTime);var F=H+1+this.element.id;var D=A(K[0].rows[H].cells[0]);E.element=D;E.row=H;E.column=0;var M="<div style='background: transparent; border: none; padding: 0; margin: 0;' id ='headerCellContent"+F+"'>"+R+"</div>";D.append(M);D.find("#headerCellContent"+F).addClass(this.toThemeProperty("jqx-calendar-row-cell"));B[H]=E;Q=new A.jqx._jqxDateTimeInput.getDateTime(new Date(Q._addWeeks(1)));C=this.getWeekOfYear(Q)}var I=A.data(this.element,G[0].id);I.rowCells=B},getFirstDayOfWeek:function(C){var B=C;if(this.firstDayOfWeek<0||this.firstDayOfWeek>6){this.firstDayOfWeek=6}var E=B.dayOfWeek-this.firstDayOfWeek;if(E<=0){E+=7}var D=A.jqx._jqxDateTimeInput.getDateTime(B._addDays(-E));return D},getVisibleDate:function(){var D=new A.jqx._jqxDateTimeInput.getDateTime(new Date(this.value.dateTime));if(D<this.minDate){D=this.minDate}if(D>this.maxDate){this.visibleDate=this.maxDate}D.dateTime.setHours(0);var B=D.day;var C=A.jqx._jqxDateTimeInput.getDateTime(D._addDays(-B+1));D=C;return D},destroy:function(B){this.host.removeClass();if(B!=false){this.host.remove()}},_raiseEvent:function(J,E){if(E==undefined){E={owner:null}}var D=this.events[J];var I=E?E:{};I.owner=this;var G=new A.Event(D);G.owner=this;G.args=I;if(J==0||J==1||J==2||J==3||J==4||J==5||J==6||J==7||J==8){G.args.date=G.args.selectedDate=this.getDate();G.args.range=this.getRange();var B=this.getViewStart();var H=this.getViewEnd();G.args.view={from:B,to:H}}if(J==7){var F=I.selectionType;if(!F){F=null}if(F=="key"){F="keyboard"}if(F=="none"){F=null}I.type=F}var C=this.host.trigger(G);if(J==0||J==1){C=false}return C},propertyMap:function(B){if(B=="value"){if(this.selectionMode!="range"){return this.getDate()}else{return this.getRange()}}return null},_setSize:function(){var B=this.host.find("#View"+this.element.id);if(B.length>0){this.setCalendarSize();if(this.height!=undefined&&!isNaN(this.height)){B.height(this.height)}else{if(this.height!=null&&this.height.toString().indexOf("px")!=-1){B.height(this.height)}}if(this.width!=undefined&&!isNaN(this.width)){B.width(this.width)}else{if(this.width!=null&&this.width.toString().indexOf("px")!=-1){B.width(this.width)}}var D=this.host.height()-this.titleHeight-this.columnHeaderHeight;var C="View"+this.element.id;B.find("#cellsTable"+C).height(D);B.find("#calendarRowHeader"+C).height(D);this.refreshControl()}},resize:function(){this._setSize()},clear:function(){if(this.selectionMode=="range"){this._clicks=1;this.setRange(null,null);this._raiseEvent(7)}else{this.setDate(null,"mouse")}this._clicks=0;this.selection={from:null,to:null}},today:function(){if(this.selectionMode=="range"){this.setRange(new Date(),new Date())}else{this.setDate(new Date(),"mouse")}},propertiesChangedHandler:function(C,D,B){if(B.width&&B.height&&Object.keys(B).length==2){C._setSize()}},propertyChangedHandler:function(C,D,B,G){if(this.isInitialized==undefined||this.isInitialized==false){return}if(C.batchUpdate&&C.batchUpdate.width&&C.batchUpdate.height&&Object.keys(C.batchUpdate).length==2){return}if(D=="enableHover"){return}if(D=="keyboardNavigation"){return}if(D=="localization"){if(this.localization){if(this.localization.backString){this.backText=this.localization.backString}if(this.localization.forwardString){this.forwardText=this.localization.forwardString}if(this.localization.todayString){this.todayString=this.localization.todayString}if(this.localization.clearString){this.clearString=this.localization.clearString}this.firstDayOfWeek=this.localization.calendar.firstDay}}if(D=="culture"){try{if(A.global){A.global.preferCulture(C.culture);C.localization.calendar=A.global.culture.calendar}else{if(Globalize){var E=Globalize.culture(C.culture);C.localization.calendar=E.calendar}}if(C.localization.calendar&&C.localization.calendar.firstDay!=undefined&&C.culture!="default"){C.firstDayOfWeek=C.localization.calendar.firstDay}}catch(F){}}if(D=="views"){if(C.views.indexOf("month")==-1){C.view="year"}if(C.views.indexOf("year")==-1&&C.views.indexOf("month")==-1){C.view="decade"}C.render();return}if(D=="showFooter"){C.render()}if(D=="width"||D=="height"){C._setSize();return}else{if(D=="theme"){A.jqx.utilities.setTheme(B,G,C.host)}else{if(D=="rowHeaderWidth"||D=="showWeekNumbers"){C.render()}else{C.view="month";C.render()}}}}})})(jqxBaseFramework);(function(A){A.jqx._jqxCalendar.cell=function(C){var B={dateTime:new A.jqx._jqxDateTimeInput.getDateTime(C),_date:C,getDate:function(){return this._date},setDate:function(D){this.dateTime=new A.jqx._jqxDateTimeInput.getDateTime(D);this._date=D},isToday:false,isWeekend:false,isOtherMonth:false,isVisible:true,isSelected:false,isHighlighted:false,element:null,row:-1,column:-1,tooltip:null};return B};A.jqx._jqxCalendar.monthView=function(G,B,C,F,H,D){var E={start:G,end:B,cells:C,rowCells:F,columnCells:H,element:D};return E}})(jqxBaseFramework);