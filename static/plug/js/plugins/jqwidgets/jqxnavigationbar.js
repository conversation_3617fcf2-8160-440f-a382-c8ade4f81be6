(function(A){A.jqx.jqxWidget("jqxNavigationBar","",{});A.extend(A.jqx._jqxNavigationBar.prototype,{defineInstance:function(){var B={width:"auto",height:"auto",expandAnimationDuration:250,collapseAnimationDuration:250,animationType:"slide",toggleMode:"click",showArrow:true,arrowPosition:"right",disabled:false,initContent:null,rtl:false,easing:"easeInOutSine",expandMode:"singleFitHeight",expandedIndexes:[],_expandModes:["singleFitHeight","single","multiple","toggle","none"],aria:{"aria-disabled":{name:"disabled",type:"boolean"}},events:["expandingItem","expandedItem","collapsingItem","collapsedItem"]};A.extend(true,this,B);return B},createInstance:function(){this._isTouchDevice=A.jqx.mobile.isTouchDevice();A.jqx.aria(this);this.render()},val:function(B){if(arguments.length===0||typeof(B)=="object"){return this.expandedIndexes}if(typeof B=="string"){this.expandedIndexes.push(parseInt(B,10));this._applyExpandedIndexes()}else{if(B instanceof Array){this.expandedIndexes=B}else{this.expandedIndexes=[B]}this._applyExpandedIndexes()}return this.expandedIndexes},expandAt:function(F){var H=this;if(this.expandMode=="single"||this.expandMode=="singleFitHeight"||this.expandMode=="toggle"){for(var G=0;G<H.items.length;G++){if(G!=F){H.collapseAt(G)}}}var B=this.items[F];if(B.disabled===false&&B.expanded===false&&B._expandChecker==1){B._expandChecker=0;this._raiseEvent("0",{item:F});B._headerHelper.removeClass(this.toThemeProperty("jqx-fill-state-normal"));B._headerHelper.addClass(this.toThemeProperty("jqx-fill-state-pressed jqx-expander-header-expanded"));B._arrowHelper.removeClass(this.toThemeProperty("jqx-icon-arrow-down jqx-icon-arrow-down-hover jqx-icon-arrow-up-hover jqx-icon-arrow-down-selected jqx-expander-arrow-top"));B._arrowHelper.addClass(this.toThemeProperty("jqx-icon-arrow-up jqx-icon-arrow-up-selected jqx-expander-arrow-bottom jqx-expander-arrow-expanded"));if(this.heightFlag===false){H.element.style.overflowX="hidden";H.element.style.overflowY="hidden"}this.eCFlag=1;switch(this.animationType){case"slide":var D=B._contentHelper,C=0,E=D.outerHeight();D.slideDown({duration:this.expandAnimationDuration,easing:this.easing,step:function(I,J){J.now=Math.round(I);if(J.prop!=="height"){C+=J.now}else{if(H._collapseContent){J.now=Math.round(E-H._collapseContent.outerHeight()-C);C=0}else{J.now=Math.round(I)}}},complete:function(){B.expanded=true;A.jqx.aria(B._header,"aria-expanded",true);A.jqx.aria(B._content,"aria-hidden",false);H._updateExpandedIndexes();H._raiseEvent("1",{item:F});H._checkHeight();if(H.heightFlag===true){H.element.style.overflowX="hidden";H.element.style.overflowY="auto"}if(H.initContent&&B._initialized===false){H.initContent(F);B._initialized=true}H.eCFlag=0}});break;case"fade":setTimeout(function(){B._contentHelper.fadeIn({duration:this.expandAnimationDuration,complete:function(){B.expanded=true;A.jqx.aria(B._header,"aria-expanded",true);A.jqx.aria(B._content,"aria-hidden",false);H._updateExpandedIndexes();H._raiseEvent("1",{item:F});H._checkHeight();if(H.heightFlag===true){H.element.style.overflowX="hidden";H.element.style.overflowY="auto"}if(H.initContent&&B._initialized===false){H.initContent(F);B._initialized=true}H.eCFlag=0}})},this.collapseAnimationDuration);break;case"none":B._content.style.display="";B.expanded=true;A.jqx.aria(B._header,"aria-expanded",true);A.jqx.aria(B._content,"aria-hidden",false);this._updateExpandedIndexes();this._raiseEvent("1",{item:F});this._checkHeight();if(this.heightFlag===true){H.element.style.overflowX="hidden";H.element.style.overflowY="auto"}if(this.initContent&&B._initialized===false){this.initContent(F);B._initialized=true}this.eCFlag=0;break}}},collapseAt:function(D){var C=this.items[D];if(C.disabled===false&&C.expanded===true&&C._expandChecker===0){var B=this;C._expandChecker=1;this._raiseEvent("2",{item:D});C._headerHelper.removeClass(this.toThemeProperty("jqx-fill-state-pressed jqx-expander-header-expanded"));C._headerHelper.addClass(this.toThemeProperty("jqx-fill-state-normal"));C._arrowHelper.removeClass(this.toThemeProperty("jqx-icon-arrow-up jqx-icon-arrow-up-selected jqx-icon-arrow-down-selected jqx-expander-arrow-bottom jqx-expander-arrow-expanded"));C._arrowHelper.addClass(this.toThemeProperty("jqx-icon-arrow-down jqx-expander-arrow-top"));if(this.heightFlag===false){B.element.style.overflowX="hidden";B.element.style.overflowY="hidden"}this.eCFlag=1;this._collapseContent=C._contentHelper;switch(this.animationType){case"slide":var E=C._contentHelper;E.slideUp({duration:this.collapseAnimationDuration,step:function(G,F){F.now=Math.round(G)},easing:this.easing,complete:function(){C.expanded=false;C._content.style.display="none";A.jqx.aria(C._header,"aria-expanded",false);A.jqx.aria(C._content,"aria-hidden",true);B._updateExpandedIndexes();B._raiseEvent("3",{item:D});B._checkHeight();if(B.heightFlag===true){B.element.style.overflowX="hidden";B.element.style.overflowY="auto"}B.eCFlag=0;B._collapseContent=null}});break;case"fade":C._contentHelper.fadeOut({duration:this.collapseAnimationDuration,complete:function(){C.expanded=false;A.jqx.aria(C._header,"aria-expanded",false);A.jqx.aria(C._content,"aria-hidden",true);B._updateExpandedIndexes();B._raiseEvent("3",{item:D});B._checkHeight();if(B.heightFlag===true){B.element.style.overflowX="hidden";B.element.style.overflowY="auto"}B.eCFlag=0}});break;case"none":C._content.style.display="none";C.expanded=false;A.jqx.aria(C._header,"aria-expanded",false);A.jqx.aria(C._content,"aria-hidden",true);this._updateExpandedIndexes();this._raiseEvent("3",{item:D});this._checkHeight();if(this.heightFlag===true){B.element.style.overflowX="hidden";B.element.style.overflowY="auto"}this.eCFlag=0;break}}},setHeaderContentAt:function(B,C){this.items[B]._headerText.innerHTML=C},getHeaderContentAt:function(B){return this.items[B]._headerText.innerHTML},setContentAt:function(B,C){this.items[B]._content.innerHTML=C;this._checkContent(B)},getContentAt:function(B){return this.items[B]._content.innerHTML},showArrowAt:function(B){this.items[B]._arrow.style.display="block"},hideArrowAt:function(B){this.items[B]._arrow.style.display="none"},enable:function(){this.disabled=false;this._enabledDisabledCheck();this.refresh();A.jqx.aria(this,"aria-disabled",false)},disable:function(){this.disabled=true;this._enabledDisabledCheck();this.refresh();A.jqx.aria(this,"aria-disabled",true)},enableAt:function(B){this.items[B].disabled=false;this.refresh()},disableAt:function(B){this.items[B].disabled=true;this.refresh()},invalidate:function(){this.refresh()},refresh:function(C){if(C===true){return}this._removeHandlers();for(var B=0;B<this.items.length;B++){this.items[B]._arrow.style.display=this.showArrow?"block":"none"}this._updateExpandedIndexes();this._setTheme();this._setSize();this._toggle();this._keyBoard()},render:function(){this.widgetID=this.element.id;var C=this;if(this._expandModes.indexOf(this.expandMode)==-1){this.expandMode="singleFitHeight"}A.jqx.utilities.resize(this.host,function(){C._setSize()});C.element.setAttribute("role","tablist");if(this.items){this._removeHandlers();A.each(this.items,function(){this._header.className="";this._header.setAttribute("tabindex",null);this._header.style.marginTop="0px";this._headerText.className="";this._header.innerHTML=this._headerText.innerHTML;this._content.setAttribute("tabindex",null)})}this.items=[];var H=C.host.children(),D=H.length;var G="Invalid jqxNavigationBar structure. Please add an even number of child div elements that will represent each item's header and content.";try{if(D%2!==0){throw G}}catch(P){throw new Error(P)}var O="Invalid jqxNavigationBar structure. Please make sure all the children elements of the navigationbar are divs.";try{for(var I=0;I<D;I++){if(H[I].tagName.toLowerCase()!="div"){throw O}}}catch(P){throw new Error(P)}for(var R=0;R<D;R+=2){var B=H[R];B.innerHTML="<div>"+B.innerHTML+"</div>"}var L=0;var J;for(var K=0;K<D/2;K++){J=L+1;var F={};F={};F._header=H[L];F._headerHelper=A(H[L]);H[L].setAttribute("role","tab");F._content=H[J];F._contentHelper=A(H[J]);if(F._contentHelper.initAnimate){F._contentHelper.initAnimate()}F.expandedFlag=false;F.expanded=false;F.focusedH=false;F.focusedC=false;this.items[K]=F;H[J].setAttribute("role","tabpanel");L+=2}var M=this.expandedIndexes.length;if(this.items&&this.items.length===0){return}if(this.expandMode=="single"||this.expandMode=="singleFitHeight"||this.expandMode=="toggle"||this.expandMode=="none"){if(M!==0){this.items[this.expandedIndexes[0]].expanded=true}else{if(M===0&&(this.expandMode=="single"||this.expandMode=="singleFitHeight")){this.items[0].expanded=true}}}else{if(this.expandMode=="multiple"){if(M!==0){for(var N=0;N<M;N++){C.items[this.expandedIndexes[N]].expanded=true}}}}this._enabledDisabledCheck();var E=0;A.each(this.items,function(T){var S=this;S._headerText=A(S._header).children()[0];if(!C.rtl){A(S._headerText).addClass(C.toThemeProperty("jqx-expander-header-content"))}else{A(S._headerText).addClass(C.toThemeProperty("jqx-expander-header-content-rtl"))}S._arrow=document.createElement("div");S._arrowHelper=A(S._arrow);S._header.appendChild(S._arrow);if(C.showArrow){S._arrow.style.display="block"}else{S._arrow.style.display="none"}if(S.expanded===true){S._arrowHelper.addClass(C.toThemeProperty("jqx-icon-arrow-up jqx-icon-arrow-up-selected jqx-expander-arrow-bottom jqx-expander-arrow-expanded"));if(C.initContent){setTimeout(function(){C.initContent(T);S._initialized=true},10)}else{S._initialized=true}S._expandChecker=0;A.jqx.aria(S._header,"aria-expanded",true);A.jqx.aria(S._content,"aria-hidden",false)}else{if(S.expanded===false){S._arrowHelper.addClass(C.toThemeProperty("jqx-icon-arrow-down jqx-expander-arrow-top"));S._initialized=false;S._expandChecker=1;S._content.style.display="none";A.jqx.aria(S._header,"aria-expanded",false);A.jqx.aria(S._content,"aria-hidden",true)}}if(S._header.getAttribute("tabindex")===null){E++;S._header.setAttribute("tabindex",E)}if(S._content.getAttribute("tabindex")===null){E++;S._content.setAttribute("tabindex",E)}});this._setTheme();this._setSize();for(var Q=0;Q<C.items.length;Q++){C._checkContent(Q)}this._toggle();this._keyBoard()},insert:function(E,B,D){var G=document.createElement("div"),F=document.createElement("div");G.innerHTML=B;F.innerHTML=D;if(E>=0&&E<=this.items.length){var C=this.items[E]._header;this.element.insertBefore(G,C);this.element.insertBefore(F,C)}else{this.element.appendChild(G);this.element.appendChild(F)}this.render()},add:function(C,B){this.insert(-1,C,B)},update:function(C,B,D){this.setHeaderContentAt(C,B);this.setContentAt(C,D)},remove:function(B){if(isNaN(B)){B=this.items.length-1}if(!this.items[B]){return}this.items[B]._header.remove();this.items[B]._content.remove();this.items.splice(B,1);var C=this.expandedIndexes.indexOf(B);if(C>-1){this.expandedIndexes.splice(C,1)}this.render()},destroy:function(){this._removeHandlers();this.host.remove()},focus:function(){try{for(var D=0;D<this.items.length;D++){var B=this.items[D];if(B.disabled===false){B._header.focus();return false}}}catch(C){}},_applyExpandedIndexes:function(){var B=this;var H=this.expandedIndexes.length;for(var C=0;C<H;C++){var D=B.expandedIndexes[C];for(var G=0;G<B.items.length;G++){var E=B.items[G];if(G==D){E.expandedFlag=true;if(E.expanded===false){B.expandAt(G)}if(B.expandMode=="single"||B.expandMode=="singleFitHeight"||B.expandMode=="toggle"||B.expandMode=="none"){return false}}else{if(G!=D&&E.expandedFlag===false){B.collapseAt(G)}}}}for(var F=0;F<B.items.length;F++){B.items[F].expandedFlag=false}},propertiesChangedHandler:function(C,D,B){if(B.width&&B.height&&Object.keys(B).length==2){C._setSize()}},propertyChangedHandler:function(D,E,C,B){if(D.batchUpdate&&D.batchUpdate.width&&D.batchUpdate.height&&Object.keys(D.batchUpdate).length==2){return}if(E=="width"||E=="height"){D._setSize();return}if(E=="disabled"){D._enabledDisabledCheck()}else{if(E=="expandedIndexes"){D._applyExpandedIndexes()}else{D.refresh()}}},_raiseEvent:function(B,D){var F=this.events[B];var G=new A.Event(F);G.owner=this;G.args=D;G.item=G.args.item;var E;try{E=this.host.trigger(G)}catch(C){}return E},resize:function(C,B){this.width=C;this.height=B;this._setSize()},_setSize:function(){var C=this;this.headersHeight=0;var B=this.items&&this.items.length>0?parseInt(this.items[0]._headerHelper.css("padding-left"),10):0;var L=this.items&&this.items.length>0?parseInt(this.items[0]._headerHelper.css("padding-right"),10):0;var H=2;var I=B+L+H;if(isNaN(I)){I=12}if(this.width=="auto"){C.element.style.width="auto"}else{if(this.width!=null&&this.width.toString().indexOf("%")!=-1){C.element.style.width=C.width}else{C.element.style.width=(parseInt(this.width,10)+I)+"px"}}if(typeof C.height==="number"){C.element.style.height=C.height+"px"}else{C.element.style.height=C.height}for(var D=0;D<C.items.length;D++){var E=C.items[D];var J=C.arrowPosition;if(C.rtl){switch(J){case"left":J="right";break;case"right":J="left";break}}if(J=="right"){E._headerText.style["float"]="left";E._headerText.style.marginLeft="0px";E._arrow.style["float"]="right";E._arrow.style.position="relative"}else{if(J=="left"){if(C.width=="auto"){E._headerText.style["float"]="left";E._headerText.style.marginLeft="17px";E._arrow.style["float"]="left";E._arrow.style.position="absolute"}else{E._headerText.style["float"]="right";E._headerText.style.marginLeft="0px";E._arrow.style["float"]="left";E._arrow.style.position="relative"}}}E._header.style.height="auto";E._headerText.style.minHeight=E._arrow.offsetHeight;C.headersHeight+=A(E._header).outerHeight();E._arrow.style.marginTop=(E._headerText.offsetHeight/2-E._arrow.offsetHeight/2)+"px"}for(var K=0;K<C.items.length;K++){var G=C.items[K];if(C.height!="auto"){if(C.expandMode=="single"||C.expandMode=="toggle"||C.expandMode=="multiple"){C.element.style.overflowX="hidden";C.element.style.overflowY="auto"}else{if(C.expandMode=="singleFitHeight"){var F=parseInt(G._contentHelper.css("padding-top"),10)+parseInt(G._contentHelper.css("padding-bottom"),10);if(C.height&&C.height.toString().indexOf("%")>=0){G._content.style.height=Math.max(0,(C.element.offsetHeight-C.headersHeight-F+2))+"px"}else{G._content.style.height=Math.max(0,(C.element.offsetHeight-C.headersHeight-F))+"px"}}}}}C._checkHeight()},_toggle:function(){var B=this;if(this._isTouchDevice===false){switch(this.toggleMode){case"click":case"dblclick":A.each(this.items,function(D){var C=this;if(C.disabled===false){B.addHandler(C._header,B.toggleMode+".navigationbar"+B.widgetID,function(){B.focusedH=true;B._animate(D)})}});break;case"none":break}}else{if(this.toggleMode!="none"){A.each(this.items,function(D){var C=this;if(C.disabled===false){B.addHandler(C._header,A.jqx.mobile.getTouchEventName("touchstart")+"."+B.widgetID,function(){B._animate(D)})}})}else{return}}},_animate:function(E,D){var B=this;var C=this.items[E];if(this.expandMode!="none"&&this.eCFlag!=1){if(this.items[E].expanded===true){if(this.expandMode=="multiple"||this.expandMode=="toggle"){this.collapseAt(E)}}else{this.expandAt(E)}if(!B._isTouchDevice){if(D!==true){C._headerHelper.addClass(this.toThemeProperty("jqx-fill-state-hover jqx-expander-header-hover"));C._arrowHelper.addClass(this.toThemeProperty("jqx-expander-arrow-top-hover jqx-expander-arrow-down-hover"))}else{C._headerHelper.removeClass(this.toThemeProperty("jqx-fill-state-hover jqx-expander-header-hover"));C._arrowHelper.removeClass(this.toThemeProperty("jqx-expander-arrow-top-hover jqx-expander-arrow-down-hover"))}}}},_removeHandlers:function(){var B=this;this.removeHandler(this.host,"keydown.navigationbar"+this.widgetID);for(var C=0;C<B.items.length;C++){var D=B.items[C];B.removeHandler(D._header,"click.navigationbar"+B.widgetID);B.removeHandler(D._header,"dblclick.navigationbar"+B.widgetID);B.removeHandler(D._header,"mouseenter.navigationbar"+B.widgetID);B.removeHandler(D._header,"mouseleave.navigationbar"+B.widgetID);B.removeHandler(D._header,"focus.navigationbar"+B.widgetID);B.removeHandler(D._header,"blur.navigationbar"+B.widgetID);B.removeHandler(D._content,"focus.navigationbar"+B.widgetID);B.removeHandler(D._content,"blur.navigationbar"+B.widgetID);B.removeHandler(D._headerText,"focus.navigationbar"+B.widgetID);B.removeHandler(D._arrow,"focus.navigationbar"+B.widgetID)}},_setTheme:function(){var B=this;this.host.addClass(this.toThemeProperty("jqx-reset jqx-widget"));if(this.rtl===true){this.host.addClass(this.toThemeProperty("jqx-rtl"))}A.each(this.items,function(E){var C=this,D=C._headerHelper,F=C._arrowHelper,H=C._contentHelper,I="jqx-widget-header jqx-item jqx-expander-header",G="jqx-widget-content jqx-expander-content jqx-expander-content-bottom";C._header.style.position="relative";C._content.style.position="relative";if(C.disabled===false){D.removeClass(B.toThemeProperty("jqx-fill-state-disabled"));H.removeClass(B.toThemeProperty("jqx-fill-state-disabled"));if(C.expanded===true){I+=" jqx-fill-state-pressed jqx-expander-header-expanded"}else{I+=" jqx-fill-state-normal";D.removeClass(B.toThemeProperty("jqx-expander-header-expanded"))}if(!B._isTouchDevice){B.addHandler(C._header,"mouseenter.navigationbar"+B.widgetID,function(){if(C._expandChecker==1){if(!C.focusedH){C._header.style.zIndex=5}D.removeClass(B.toThemeProperty("jqx-fill-state-normal jqx-fill-state-pressed"));D.addClass(B.toThemeProperty("jqx-fill-state-hover jqx-expander-header-hover"));F.addClass(B.toThemeProperty("jqx-expander-arrow-top-hover jqx-expander-arrow-down-hover"));if(C.expanded){F.addClass(B.toThemeProperty("jqx-icon-arrow-up-hover"))}else{F.addClass(B.toThemeProperty("jqx-icon-arrow-down-hover"))}}});B.addHandler(C._header,"mouseleave.navigationbar"+B.widgetID,function(){if(!C.focusedH){C._header.style.zIndex=0}D.removeClass(B.toThemeProperty("jqx-fill-state-hover jqx-expander-header-hover"));F.removeClass(B.toThemeProperty("jqx-expander-arrow-top-hover jqx-expander-arrow-down-hover jqx-icon-arrow-up-hover jqx-icon-arrow-down-hover"));if(C._expandChecker==1){D.addClass(B.toThemeProperty("jqx-fill-state-normal"))}else{D.addClass(B.toThemeProperty("jqx-fill-state-pressed"))}})}}else{I+=" jqx-fill-state-disabled";G+=" jqx-fill-state-disabled"}B.host.addClass(B.toThemeProperty("jqx-navigationbar"));D.addClass(B.toThemeProperty(I));H.addClass(B.toThemeProperty(G));if(E!==0){C._header.style.marginTop="-1px"}F.addClass(B.toThemeProperty("jqx-expander-arrow"))})},_checkContent:function(D){var B=this.items[D];var E=B._content;this._cntntEmpty=/^\s*$/.test(this.items[D]._content.innerHTML);if(this._cntntEmpty===true){E.style.display="none";E.style.height="0px";B._contentHelper.addClass(this.toThemeProperty("jqx-expander-content-empty"))}else{if(B.expanded){E.style.display="block"}if(this.expandMode=="singleFitHeight"){var C=1;if(D!==0){C=2}E.style.height=Math.max(0,(this.element.offsetHeight-this.headersHeight+C))+"px"}else{E.style.height="auto"}B._contentHelper.removeClass(this.toThemeProperty("jqx-expander-content-empty"))}},_checkHeight:function(){var E=this;if(typeof E.width==="string"&&E.width.indexOf("%")!==-1){return}var C=0;var B=this.items&&this.items.length>0?parseInt(this.items[0]._headerHelper.css("padding-left"),10):0;var J=this.items&&this.items.length>0?parseInt(this.items[0]._headerHelper.css("padding-right"),10):0;var F=2;var G=B+J+F;if(isNaN(G)){G=12}var I=17;for(var H=0;H<E.items.length;H++){var D=E.items[H];C+=(D.expanded?D._contentHelper.outerHeight():0)+D._headerHelper.outerHeight()}if(this.width!="auto"&&this.height!="auto"&&this.expandMode!="singleFitHeight"){if(C>E.element.offsetHeight){E.element.style.width=(parseInt(this.width,10)+G+I)+"px";this.heightFlag=true}else{E.element.style.width=(parseInt(this.width,10)+G)+"px";this.heightFlag=false}}},_enabledDisabledCheck:function(){for(var B=0;B<this.items.length;B++){this.items[B].disabled=this.disabled}},_updateExpandedIndexes:function(){var B=this;this.expandedIndexes=[];A.each(this.items,function(D){var C=this;if(C.expanded===true){B.expandedIndexes.push(D);if(B.expandMode=="single"||B.expandMode=="singleFitHeight"||B.expandMode=="toggle"||B.expandMode=="none"){return false}}})},_keyBoard:function(){var B=this;this._focus();this.addHandler(this.host,"keydown.navigationbar"+this.widgetID,function(C){var D=false,E=B.items.length;A.each(B.items,function(G){var F=this;if((F.focusedH===true||F.focusedC===true)&&F.disabled===false){switch(C.keyCode){case 13:case 32:if(B.toggleMode!="none"){if(F.focusedH===true){B._animate(G,true)}D=true}break;case 37:if(G!==0){B.items[G-1]._header.focus()}else{B.items[E-1]._header.focus()}D=true;break;case 38:if(C.ctrlKey===false){if(G!==0){B.items[G-1]._header.focus()}else{B.items[E-1]._header.focus()}}else{if(F.focusedC===true){F._header.focus()}}D=true;break;case 39:if(G!=E-1){B.items[G+1]._header.focus()}else{B.items[0]._header.focus()}D=true;break;case 40:if(C.ctrlKey===false){if(G!=E-1){B.items[G+1]._header.focus()}else{B.items[0]._header.focus()}}else{if(F.expanded===true){F._content.focus()}}D=true;break;case 35:if(G!=E-1){B.items[E-1]._header.focus()}D=true;break;case 36:if(G!==0){B.items[0]._header.focus()}D=true;break}return false}});if(D&&C.preventDefault){C.preventDefault()}return !D})},_focus:function(){var B=this;if(this.disabled){return}A.each(this.items,function(){var C=this;B.addHandler(C._header,"focus.navigationbar"+this.widgetID,function(){C.focusedH=true;A.jqx.aria(C._header,"aria-selected",true);C._headerHelper.addClass(B.toThemeProperty("jqx-fill-state-focus"));C._header.style.zIndex=10});B.addHandler(C._header,"blur.navigationbar"+this.widgetID,function(){C.focusedH=false;A.jqx.aria(C._header,"aria-selected",false);if(C._header.className.indexOf("jqx-expander-header-hover")!==-1){C._header.style.zIndex=5}else{C._header.style.zIndex=0}C._headerHelper.removeClass(B.toThemeProperty("jqx-fill-state-focus"))});B.addHandler(C._headerText,"focus.navigationbar"+this.widgetID,function(){C._header.focus()});B.addHandler(C._arrow,"focus.navigationbar"+this.widgetID,function(){C._header.focus()});B.addHandler(C._content,"focus.navigationbar"+this.widgetID,function(){C.focusedC=true;C._contentHelper.addClass(B.toThemeProperty("jqx-fill-state-focus"))});B.addHandler(C._content,"blur.navigationbar"+this.widgetID,function(){C.focusedC=false;C._contentHelper.removeClass(B.toThemeProperty("jqx-fill-state-focus"))})})}})})(jqxBaseFramework);