(function(A){A.jqx.jqxWidget("jqxDragDrop","",{});A.extend(A.jqx._jqxDragDrop.prototype,{defineInstance:function(){var B={restricter:"document",handle:false,feedback:"clone",opacity:0.6,revert:false,revertDuration:400,distance:5,disabled:false,tolerance:"intersect",data:null,dropAction:"default",dragZIndex:999999,appendTo:"parent",cursor:"move",onDragEnd:null,onDrag:null,onDragStart:null,onTargetDrop:null,onDropTargetEnter:null,onDropTargetLeave:null,initFeedback:null,dropTarget:null,isDestroyed:false,triggerEvents:true,_touchEvents:{mousedown:A.jqx.mobile.getTouchEventName("touchstart"),click:A.jqx.mobile.getTouchEventName("touchstart"),mouseup:A.jqx.mobile.getTouchEventName("touchend"),mousemove:A.jqx.mobile.getTouchEventName("touchmove"),mouseenter:"mouseenter",mouseleave:"mouseleave"},_restricter:null,_zIndexBackup:0,_targetEnterFired:false,_oldOpacity:1,_feedbackType:undefined,_isTouchDevice:false,_events:["dragStart","dragEnd","dragging","dropTargetEnter","dropTargetLeave"]};A.extend(true,this,B);return B},createInstance:function(){this._createDragDrop()},_createDragDrop:function(){var D=A.data(document.body,"jqx-draggables")||1;this.appendTo=this._getParent();this._isTouchDevice=A.jqx.mobile.isTouchDevice();if((/(static|relative)/).test(this.host.css("position"))){if(!this.feedback||this.feedback==="original"){var B=this._getRelativeOffset(this.host);var C=this.appendTo.offset();if(this.appendTo.css("position")!="static"){C={left:0,top:0}}this.element.style.position="absolute";this.element.style.left=C.left+B.left+"px";this.element.style.top=C.top+B.top+"px"}}this._validateProperties();this._idHandler(D);if(this.disabled){this.disable()}if(typeof this.dropTarget==="string"){this.dropTarget=A(this.dropTarget)}this._refresh();D+=1;A.data(document.body,"jqx-draggables",D);this.host.addClass("jqx-draggable");if(!this.disabled){this.host.css("cursor",this.cursor)}},_getParent:function(){var B=this.appendTo;if(typeof this.appendTo==="string"){switch(this.appendTo){case"parent":B=this.host.parent();break;case"document":B=A(document);break;case"body":B=A(document.body);break;default:B=A(this.appendTo);break}}return B},_idHandler:function(B){if(!this.element.id){var C="jqx-draggable-"+B;this.element.id=C}},_refresh:function(){this._removeEventHandlers();this._addEventHandlers()},_getEvent:function(B){if(this._isTouchDevice){return this._touchEvents[B]}else{return B}},_validateProperties:function(){if(this.feedback==="clone"){this._feedbackType="clone"}else{this._feedbackType="original"}if(this.dropAction!=="default"){this.dropAction="nothing"}},_removeEventHandlers:function(){this.removeHandler(this.host,"dragstart");this.removeHandler(this.host,this._getEvent("mousedown")+".draggable."+this.element.id,this._mouseDown);this.removeHandler(A(document),this._getEvent("mousemove")+".draggable."+this.element.id,this._mouseMove);this.removeHandler(A(document),this._getEvent("mouseup")+".draggable."+this.element.id,this._mouseUp)},_addEventHandlers:function(){var D=this;this.addHandler(this.host,"dragstart",function(F){if(D.disabled){return true}var G=A.jqx.mobile.isTouchDevice();if(!G){F.preventDefault();return false}});this.addHandler(this.host,this._getEvent("mousedown")+".draggable."+this.element.id,this._mouseDown,{self:this});this.addHandler(A(document),this._getEvent("mousemove")+".draggable."+this.element.id,this._mouseMove,{self:this});this.addHandler(A(document),this._getEvent("mouseup")+".draggable."+this.element.id,this._mouseUp,{self:this});try{if(document.referrer!=""||window.frameElement){if(window.top!=null&&window.top!=window.self){var C="";if(window.parent&&document.referrer){C=document.referrer}if(C.indexOf(document.location.host)!=-1){var B=function(F){D._mouseUp(D)};if(window.top.document.addEventListener){window.top.document.addEventListener("mouseup",B,false)}else{if(window.top.document.attachEvent){window.top.document.attachEvent("onmouseup",B)}}}}}}catch(E){}},_mouseDown:function(F){var D=F.data.self,B=D._getMouseCoordinates(F),E=D._mouseCapture(F);D._originalPageX=B.left;D._originalPageY=B.top;if(D.disabled){return true}var C=false;if(!D._mouseStarted){D._mouseUp(F);C=true}if(E){D._mouseDownEvent=F}if(D._isTouchDevice){return true}if(F.which!==1||!E){return true}F.preventDefault();if(C==true){}},_mouseMove:function(C){var B=C.data.self;if(B.disabled){return true}if(B._mouseStarted){B._mouseDrag(C);if(C.preventDefault){C.preventDefault()}return false}if(B._mouseDownEvent&&B._isMovedDistance(C)){if(B._mouseStart(B._mouseDownEvent,C)){B._mouseStarted=true}else{B._mouseStarted=false}if(B._mouseStarted){B._mouseDrag(C)}else{B._mouseUp(C)}}return !B._mouseStarted},_mouseUp:function(C){var B;if(C.data&&C.data.self){B=C.data.self}else{B=this}if(B.disabled){return true}B._mouseDownEvent=false;B._movedDistance=false;if(B._mouseStarted){B._mouseStarted=false;B._mouseStop(C)}if(B.feedback&&B.feedback[0]&&B._feedbackType!=="original"&&typeof B.feedback.remove==="function"&&!B.revert){B.feedback.remove()}if(!B._isTouchDevice){return false}},cancelDrag:function(){var B=this.revertDuration;this.revertDuration=0;this._mouseDownEvent=false;this._movedDistance=false;this._mouseStarted=false;this._mouseStop();this.feedback.remove();this.revertDuration=B},_isMovedDistance:function(B){var C=this._getMouseCoordinates(B);if(this._movedDistance){return true}if(C.left>=this._originalPageX+this.distance||C.left<=this._originalPageX-this.distance||C.top>=this._originalPageY+this.distance||C.top<=this._originalPageY-this.distance){this._movedDistance=true;return true}return false},_getMouseCoordinates:function(B){if(this._isTouchDevice){var C=A.jqx.position(B);return{left:C.left,top:C.top}}else{return{left:B.pageX,top:B.pageY}}},destroy:function(){this._enableSelection(this.host);this.host.removeData("draggable").off(".draggable").removeClass("jqx-draggable jqx-draggable-dragging jqx-draggable-disabled");this._removeEventHandlers();this.isDestroyed=true;return this},_disableSelection:function(B){B.each(function(){A(this).attr("unselectable","on").css({"-ms-user-select":"none","-moz-user-select":"none","-webkit-user-select":"none","user-select":"none"}).each(function(){this.onselectstart=function(){return false}})})},_enableSelection:function(B){B.each(function(){A(this).attr("unselectable","off").css({"-ms-user-select":"text","-moz-user-select":"text","-webkit-user-select":"text","user-select":"text"}).each(function(){this.onselectstart=null})})},_mouseCapture:function(B){if(this.disabled){return false}if(!this._getHandle(B)){return false}this._disableSelection(this.host);return true},_getScrollParent:function(B){var C;if((A.jqx.browser.msie&&(/(static|relative)/).test(B.css("position")))||(/absolute/).test(B.css("position"))){C=B.parents().filter(function(){return(/(relative|absolute|fixed)/).test(A.css(this,"position",1))&&(/(auto|scroll)/).test(A.css(this,"overflow",1)+A.css(this,"overflow-y",1)+A.css(this,"overflow-x",1))}).eq(0)}else{C=B.parents().filter(function(){return(/(auto|scroll)/).test(A.css(this,"overflow",1)+A.css(this,"overflow-y",1)+A.css(this,"overflow-x",1))}).eq(0)}return(/fixed/).test(B.css("position"))||!C.length?A(document):C},_mouseStart:function(C){var B=this._getMouseCoordinates(C),E=this._getParentOffset(this.host);this.feedback=this._createFeedback(C);this._zIndexBackup=this.feedback.css("z-index");this.feedback[0].style.zIndex=this.dragZIndex;this._backupFeedbackProportions();this._backupeMargins();this._positionType=this.feedback.css("position");this._scrollParent=this._getScrollParent(this.feedback);this._offset=this.positionAbs=this.host.offset();this._offset={top:this._offset.top-this.margins.top,left:this._offset.left-this.margins.left};A.extend(this._offset,{click:{left:B.left-this._offset.left,top:B.top-this._offset.top},parent:this._getParentOffset(),relative:this._getRelativeOffset(),hostRelative:this._getRelativeOffset(this.host)});this.position=this._generatePosition(C);this.originalPosition=this._fixPosition();if(this.restricter){this._setRestricter()}this.feedback.addClass(this.toThemeProperty("jqx-draggable-dragging"));var D=this._raiseEvent(0,C);if(this.onDragStart&&typeof this.onDragStart==="function"){this.onDragStart(this.position)}this._mouseDrag(C,true);return true},_fixPosition:function(){var C=this._getRelativeOffset(this.host),B=this.position;B={left:this.position.left+C.left,top:this.position.top+C.top};return B},_mouseDrag:function(B,C){this.position=this._generatePosition(B);this.positionAbs=this._convertPositionTo("absolute");this.feedback[0].style.left=this.position.left+"px";this.feedback[0].style.top=this.position.top+"px";this._raiseEvent(2,B);if(this.onDrag&&typeof this.onDrag==="function"){this.onDrag(this.data,this.position)}this._handleTarget();return false},_over:function(D,B,C){if(this.dropTarget){var F=false,E=this;A.each(this.dropTarget,function(G,H){F=E._overItem(H,D,B,C);if(F.over){return false}})}return F},_overItem:function(D,F,B,H){D=A(D);var E=D.offset(),G=D.outerHeight(),I=D.outerWidth(),C;if(!D||D[0]===this.element){return}var C=false;switch(this.tolerance){case"intersect":if(F.left+B>E.left&&F.left<E.left+I&&F.top+H>E.top&&F.top<E.top+G){C=true}break;case"fit":if(B+F.left<=E.left+I&&F.left>=E.left&&H+F.top<=E.top+G&&F.top>=E.top){C=true}break}return{over:C,target:D}},_handleTarget:function(){if(this.dropTarget){var D=this.feedback.offset(),E=this.feedback.outerWidth(),B=this.feedback.outerHeight(),C=this._over(D,E,B);if(C.over){if(this._targetEnterFired&&C.target.length>0&&this._oldtarget&&this._oldtarget.length>0&&C.target[0]!=this._oldtarget[0]){this._raiseEvent(4,{target:this._oldtarget});if(this.onDropTargetLeave&&typeof this.onDropTargetLeave==="function"){this.onDropTargetLeave(this._oldtarget)}}if(!this._targetEnterFired||(C.target.length>0&&this._oldtarget&&this._oldtarget.length>0&&C.target[0]!=this._oldtarget[0])){this._targetEnterFired=true;this._raiseEvent(3,{target:C.target});if(this.onDropTargetEnter&&typeof this.onDropTargetEnter==="function"){this.onDropTargetEnter(C.target)}}this._oldtarget=C.target}else{if(this._targetEnterFired){this._targetEnterFired=false;this._raiseEvent(4,{target:this._oldtarget||C.target});if(this.onDropTargetLeave&&typeof this.onDropTargetLeave==="function"){this.onDropTargetLeave(this._oldtarget||C.target)}}}}},_mouseStop:function(B){var C=false,D=this._fixPosition(),E={width:this.host.outerWidth(),height:this.host.outerHeight()};this.feedback[0].style.opacity=this._oldOpacity;if(!this.revert){this.feedback[0].style.zIndex=this._zIndexBackup}this._enableSelection(this.host);if(this.dropped){C=this.dropped;this.dropped=false}if((!this.element||!this.element.parentNode)&&this.feedback==="original"){return false}this._dropElement(D);this.feedback.removeClass(this.toThemeProperty("jqx-draggable-dragging"));this._raiseEvent(1,B);if(this.onDragEnd&&typeof this.onDragEnd==="function"){this.onDragEnd(this.data)}if(this.onTargetDrop&&typeof this.onTargetDrop==="function"&&this._over(D,E.width,E.height).over){this.onTargetDrop(this._over(D,E.width,E.height).target)}this._revertHandler();return false},_dropElement:function(B){if(this.dropAction==="default"&&this.feedback&&this.feedback[0]!==this.element&&this.feedback!=="original"){if(!this.revert){if(!(/(fixed|absolute)/).test(this.host.css("position"))){this.host.css("position","relative");var C=this._getRelativeOffset(this.host);B=this.position;B.left-=C.left;B.top-=C.top;this.element.style.left=B.left+"px";this.element.style.top=B.top+"px"}}}},_revertHandler:function(){if(this.revert||(A.isFunction(this.revert)&&this.revert())){var B=this;if(this._feedbackType!="original"){if(this.feedback!=null){if(this.dropAction!="none"){A(this.feedback).animate({left:B.originalPosition.left-B._offset.hostRelative.left,top:B.originalPosition.top-B._offset.hostRelative.top},parseInt(this.revertDuration,10),function(){if(B.feedback&&B.feedback[0]&&B._feedbackType!=="original"&&typeof B.feedback.remove==="function"){B.feedback.remove()}})}else{if(B.feedback&&B.feedback[0]&&B._feedbackType!=="original"&&typeof B.feedback.remove==="function"){B.feedback.remove()}}}}else{this.element.style.zIndex=this.dragZIndex;A(this.host).animate({left:B.originalPosition.left-B._offset.hostRelative.left,top:B.originalPosition.top-B._offset.hostRelative.top},parseInt(this.revertDuration,10),function(){B.element.style.zIndex=B._zIndexBackup})}}},_getHandle:function(B){var C;if(!this.handle){C=true}else{A(this.handle,this.host).find("*").andSelf().each(function(){if(this==B.target){C=true}})}return C},_createFeedback:function(D){var C;if(typeof this._feedbackType==="function"){C=this._feedbackType()}else{if(this._feedbackType==="clone"){C=this.host.clone().removeAttr("id")}else{C=this.host}}if(!(/(absolute|fixed)/).test(C.css("position"))){C.css("position","absolute")}if(this.appendTo[0]!==this.host.parent()[0]||C[0]!==this.element){var B={};C.css({left:this.host.offset().left-this._getParentOffset(this.host).left+this._getParentOffset(C).left,top:this.host.offset().top-this._getParentOffset(this.host).top+this._getParentOffset(C).top});C.appendTo(this.appendTo)}if(typeof this.initFeedback==="function"){this.initFeedback(C)}return C},_getParentOffset:function(C){var C=C||this.feedback;this._offsetParent=C.offsetParent();var B=this._offsetParent.offset();if(this._positionType=="absolute"&&this._scrollParent[0]!==document&&A.contains(this._scrollParent[0],this._offsetParent[0])){B.left+=this._scrollParent.scrollLeft();B.top+=this._scrollParent.scrollTop()}if((this._offsetParent[0]==document.body)||(this._offsetParent[0].tagName&&this._offsetParent[0].tagName.toLowerCase()=="html"&&A.jqx.browser.msie)){B={top:0,left:0}}return{top:B.top+(parseInt(this._offsetParent.css("border-top-width"),10)||0),left:B.left+(parseInt(this._offsetParent.css("border-left-width"),10)||0)}},_getRelativeOffset:function(D){var B=this._scrollParent||D.parent();D=D||this.feedback;if(D.css("position")==="relative"){var C=this.host.position();return{top:C.top-(parseInt(D.css("top"),10)||0),left:C.left-(parseInt(D.css("left"),10)||0)}}else{return{top:0,left:0}}},_backupeMargins:function(){this.margins={left:(parseInt(this.host.css("margin-left"),10)||0),top:(parseInt(this.host.css("margin-top"),10)||0),right:(parseInt(this.host.css("margin-right"),10)||0),bottom:(parseInt(this.host.css("margin-bottom"),10)||0)}},_backupFeedbackProportions:function(){this.feedback[0].style.opacity=this.opacity;this._feedbackProportions={width:this.feedback.outerWidth(),height:this.feedback.outerHeight()}},_setRestricter:function(){if(this.restricter=="parent"){this.restricter=this.feedback[0].parentNode}if(this.restricter=="document"||this.restricter=="window"){this._handleNativeRestricter()}if(typeof this.restricter.left!=="undefined"&&typeof this.restricter.top!=="undefined"&&typeof this.restricter.height!=="undefined"&&typeof this.restricter.width!=="undefined"){this._restricter=[this.restricter.left,this.restricter.top,this.restricter.width,this.restricter.height]}else{if(!(/^(document|window|parent)$/).test(this.restricter)&&this.restricter.constructor!=Array){this._handleDOMParentRestricter()}else{if(this.restricter.constructor==Array){this._restricter=this.restricter}}}},_handleNativeRestricter:function(){this._restricter=[this.restricter==="document"?0:A(window).scrollLeft()-this._offset.relative.left-this._offset.parent.left,this.restricter==="document"?0:A(window).scrollTop()-this._offset.relative.top-this._offset.parent.top,(this.restricter==="document"?0:A(window).scrollLeft())+A(this.restricter==="document"?document:window).width()-this._feedbackProportions.width-this.margins.left,(this.restricter==="document"?0:A(window).scrollTop())+(A(this.restricter==="document"?document:window).height()||document.body.parentNode.scrollHeight)-this._feedbackProportions.height-this.margins.top]},_handleDOMParentRestricter:function(){var B=A(this.restricter),C=B[0];if(!C){return}var D=(A(C).css("overflow")!=="hidden");this._restricter=[(parseInt(A(C).css("borderLeftWidth"),10)||0)+(parseInt(A(C).css("paddingLeft"),10)||0),(parseInt(A(C).css("borderTopWidth"),10)||0)+(parseInt(A(C).css("paddingTop"),10)||0),(D?Math.max(C.scrollWidth,C.offsetWidth):C.offsetWidth)-(parseInt(A(C).css("borderLeftWidth"),10)||0)-(parseInt(A(C).css("paddingRight"),10)||0)-this._feedbackProportions.width-this.margins.left-this.margins.right,(D?Math.max(C.scrollHeight,C.offsetHeight):C.offsetHeight)-(parseInt(A(C).css("borderTopWidth"),10)||0)-(parseInt(A(C).css("paddingBottom"),10)||0)-this._feedbackProportions.height-this.margins.top-this.margins.bottom];this._restrictiveContainer=B},_convertPositionTo:function(F,E){if(!E){E=this.position}var C,D,B;if(F==="absolute"){C=1}else{C=-1}if(this._positionType==="absolute"&&!(this._scrollParent[0]!=document&&A.contains(this._scrollParent[0],this._offsetParent[0]))){D=this._offsetParent}else{D=this._scrollParent}B=(/(html|body)/i).test(D[0].tagName);return this._getPosition(E,C,B,D)},_getPosition:function(E,B,C,D){return{top:(E.top+this._offset.relative.top*B+this._offset.parent.top*B-(A.jqx.browser.safari&&A.jqx.browser.version<526&&this._positionType=="fixed"?0:(this._positionType=="fixed"?-this._scrollParent.scrollTop():(C?0:D.scrollTop()))*B)),left:(E.left+this._offset.relative.left*B+this._offset.parent.left*B-(A.jqx.browser.safari&&A.jqx.browser.version<526&&this._positionType=="fixed"?0:(this._positionType=="fixed"?-this._scrollParent.scrollLeft():C?0:D.scrollLeft())*B))}},_generatePosition:function(G){var E=this._positionType=="absolute"&&!(this._scrollParent[0]!=document&&A.contains(this._scrollParent[0],this._offsetParent[0]))?this._offsetParent:this._scrollParent,D=(/(html|body)/i).test(E[0].tagName);var B=this._getMouseCoordinates(G),I=B.left,F=B.top;if(this.originalPosition){var C;if(this.restricter){if(this._restrictiveContainer){var H=this._restrictiveContainer.offset();C=[this._restricter[0]+H.left,this._restricter[1]+H.top,this._restricter[2]+H.left,this._restricter[3]+H.top]}else{C=this._restricter}if(B.left-this._offset.click.left<C[0]){I=C[0]+this._offset.click.left}if(B.top-this._offset.click.top<C[1]){F=C[1]+this._offset.click.top}if(B.left-this._offset.click.left>C[2]){I=C[2]+this._offset.click.left}if(B.top-this._offset.click.top>C[3]){F=C[3]+this._offset.click.top}}}return{top:(F-this._offset.click.top-this._offset.relative.top-this._offset.parent.top+(A.jqx.browser.safari&&A.jqx.browser.version<526&&this._positionType=="fixed"?0:(this._positionType=="fixed"?-this._scrollParent.scrollTop():(D?0:E.scrollTop())))),left:(I-this._offset.click.left-this._offset.relative.left-this._offset.parent.left+(A.jqx.browser.safari&&A.jqx.browser.version<526&&this._positionType=="fixed"?0:(this._positionType=="fixed"?-this._scrollParent.scrollLeft():D?0:E.scrollLeft())))}},_raiseEvent:function(E,C){if(this.triggerEvents!=undefined&&this.triggerEvents==false){return}var D=this._events[E],B=A.Event(D),C=C||{};C.position=this.position;C.element=this.element;A.extend(C,this.data);C.feedback=this.feedback;B.args=C;return this.host.trigger(B)},disable:function(){this.disabled=true;this.host.addClass(this.toThemeProperty("jqx-draggable-disabled"));this._enableSelection(this.host)},enable:function(){this.disabled=false;this.host.removeClass(this.toThemeProperty("jqx-draggable-disabled"))},propertyChangedHandler:function(D,E,C,B){if(E==="dropTarget"){if(typeof B==="string"){D.dropTarget=A(B)}}else{if(E=="disabled"){if(B){D._enableSelection(D.host)}}else{if(E=="cursor"){D.host.css("cursor",D.cursor)}}}}})})(jqxBaseFramework);(function(A){jqxListBoxDragDrop=function(){A.extend(A.jqx._jqxListBox.prototype,{_hitTestBounds:function(G,H,B){var I=G.host.offset();var J=B-parseInt(I.top);var F=H-parseInt(I.left);var D=G._hitTest(F,J);if(J<0){return null}if(D!=null){var K=parseInt(I.left);var C=K+G.host.width();if(K<=H+D.width/2&&H<=C){return D}return null}if(G.items&&G.items.length>0){var E=G.items[G.items.length-1];if(G.groups.length<2){if(E.top+E.height+15>=J){return E}}}return null},_handleDragStart:function(B,D){var C=A.jqx.mobile.isTouchDevice();if(C){if(D.allowDrag){B.on(A.jqx.mobile.getTouchEventName("touchstart"),function(){A.jqx.mobile.setTouchScroll(false,D.element.id)})}}B.off("dragStart");B.on("dragStart",function(E){if(D.allowDrag&&!D.disabled){D.feedbackElement=A("<div style='z-index: 99999; position: absolute;'></div>");D.feedbackElement.addClass(D.toThemeProperty("jqx-listbox-feedback"));D.feedbackElement.appendTo(A(document.body));D.feedbackElement.hide();D.isDragging=true;D._dragCancel=false;var I=D._getMouseCoordinates(E);var G=D._hitTestBounds(D,I.left,I.top);var F=A.find(".jqx-listbox");D._listBoxes=F;A.each(D._listBoxes,function(){var K=A.data(this,"jqxListBox").instance;K._enableHover=K.enableHover;K.enableHover=false;A.jqx.mobile.setTouchScroll(false,D.element.id)});var J=function(){D._dragCancel=true;A(E.args.element).jqxDragDrop({triggerEvents:false});A(E.args.element).jqxDragDrop("cancelDrag");clearInterval(D._autoScrollTimer);A(E.args.element).jqxDragDrop({triggerEvents:true});A.each(D._listBoxes,function(){var K=A.data(this,"jqxListBox").instance;if(K._enableHover!=undefined){K.enableHover=K._enableHover;A.jqx.mobile.setTouchScroll(true,D.element.id)}})};if(G!=null&&!G.isGroup){D._dragItem=G;if(D.dragStart){var H=D.dragStart(G);if(H==false){J();return false}}if(G.disabled){J()}D._raiseEvent(4,{label:G.label,value:G.value,originalEvent:E.args})}else{if(G==null){J()}}}return false})},_handleDragging:function(C,B){C.off("dragging");C.on("dragging",function(G){var F=G.args;if(B._dragCancel){return}var D=B._getMouseCoordinates(G);var E=D;B._lastDraggingPosition=D;B._dragOverItem=null;B.feedbackElement.hide();A.each(B._listBoxes,function(){if(A.jqx.isHidden(A(this))){return true}var P=A(this).offset();var N=P.top+20;var L=A(this).height()+N-40;var J=P.left;var M=A(this).width();var O=J+M;var Q=A.data(this,"jqxListBox").instance;var H=Q._hitTestBounds(Q,D.left,D.top);var K=Q.vScrollInstance;if(H!=null){if(Q.allowDrop&&!Q.disabled){B._dragOverItem=H;if(H.element){B.feedbackElement.show();var I=A(H.element).offset().top+1;if(E.top>I+H.height/2){I=I+H.height}B.feedbackElement.css("top",I);B.feedbackElement.css("left",J);if(Q.vScrollBar.css("visibility")!="visible"){B.feedbackElement.width(A(this).width())}else{B.feedbackElement.width(A(this).width()-20)}}}}if(D.left>=J&&D.left<O){if(F.position.top<N&&F.position.top>=N-30){clearInterval(Q._autoScrollTimer);if(K.value!=0){B.feedbackElement.hide()}Q._autoScrollTimer=setInterval(function(){var R=Q.scrollUp();if(!R){clearInterval(Q._autoScrollTimer)}},100)}else{if(F.position.top>L&&F.position.top<L+30){clearInterval(Q._autoScrollTimer);if((Q.vScrollBar.css("visibility")!="hidden")&&K.value!=K.max){B.feedbackElement.hide()}Q._autoScrollTimer=setInterval(function(){var R=Q.scrollDown();if(!R){clearInterval(Q._autoScrollTimer)}},100)}else{clearInterval(Q._autoScrollTimer)}}}else{if(B._dragOverItem==null){B.feedbackElement.hide()}clearInterval(Q._autoScrollTimer)}})})},_handleDragEnd:function(D,C){var B=A.find(".jqx-listbox");D.off("dragEnd");D.on("dragEnd",function(R){clearInterval(C._autoScrollTimer);var O=A.jqx.mobile.isTouchDevice();var K=O?C._lastDraggingPosition:C._getMouseCoordinates(R);var S=A.find(".jqx-listbox");var L=null;C.feedbackElement.remove();if(C._dragCancel){R.stopPropagation();return}A.each(S,function(){if(A.jqx.isHidden(A(this))){return true}var U=parseInt(A(this).offset().left);var W=U+A(this).width();var V=A.data(this,"jqxListBox").instance;clearInterval(V._autoScrollTimer);if(V._enableHover!=undefined){V.enableHover=V._enableHover;A.jqx.mobile.setTouchScroll(true,C.element.id)}if(C._dragItem!=null){if(K.left+C._dragItem.width/2>=U&&K.left<W){var T=parseInt(A(this).offset().top);var X=T+A(this).height();if(K.top>=T&&K.top<=X){L=A(this)}}}});var F=C._dragItem;if(L!=null&&L.length>0){var N=A.data(L[0],"jqxListBox").instance;var P=N.allowDrop;if(P&&!N.disabled){var N=A.data(L[0],"jqxListBox").instance;var G=N._hitTestBounds(N,K.left,K.top);G=C._dragOverItem;if(G!=null&&!G.isGroup){var E=true;if(C.dragEnd){E=C.dragEnd(F,G,R.args);if(E==false){A(R.args.element).jqxDragDrop({triggerEvents:false});A(R.args.element).jqxDragDrop("cancelDrag");clearInterval(C._autoScrollTimer);A(R.args.element).jqxDragDrop({triggerEvents:true});if(R.preventDefault){R.preventDefault()}if(R.stopPropagation){R.stopPropagation()}return false}if(E==undefined){E=true}}if(E){var I=G.visibleIndex;var J=function(){var V=G.visibleIndex;for(var U=V-2;U<=V+2;U++){if(N.items&&N.items.length>U){var T=N.items[U];if(T!=null){if(T.value==F.value){return T.visibleIndex}}}}return V};if(N.dropAction!="none"){if(G.element){var H=A(G.element).offset().top+1}else{var H=A(N.element).offset().top+1}if(N.content.find(".draggable").length>0){N.content.find(".draggable").jqxDragDrop("destroy")}if(K.top>H+G.height/2){N.insertAt(C._dragItem,G.index+1)}else{N.insertAt(C._dragItem,G.index)}if(C.dropAction=="default"){if(F.visibleIndex>0){C.clearSelection();C.selectIndex(F.visibleIndex-1)}C.removeItem(F,true)}var Q=J();N.clearSelection();N.selectIndex(Q)}}}else{if(N.dropAction!="none"){if(N.content.find(".draggable").length>0){N.content.find(".draggable").jqxDragDrop("destroy")}if(C.dragEnd){var E=C.dragEnd(C._dragItem,null,R.args);if(E==false){A(R.args.element).jqxDragDrop({triggerEvents:false});A(R.args.element).jqxDragDrop("cancelDrag");clearInterval(C._autoScrollTimer);A(R.args.element).jqxDragDrop({triggerEvents:true});if(R.preventDefault){R.preventDefault()}if(R.stopPropagation){R.stopPropagation()}return false}if(E==undefined){E=true}}N.addItem(C._dragItem);if(N.dropAction=="default"){if(F.visibleIndex>0){C.selectIndex(F.visibleIndex-1)}C.removeItem(F,true)}N.clearSelection();N.selectIndex(N.items.length-1)}}}}else{if(C.dragEnd){var M=C.dragEnd(F,R.args);if(false==M){if(R.preventDefault){R.preventDefault()}if(R.stopPropagation){R.stopPropagation()}return false}}}if(F!=null){C._raiseEvent(5,{label:F.label,value:F.value,originalEvent:R.args})}return false})},_enableDragDrop:function(){if(this.allowDrag&&this.host.jqxDragDrop){var C=this.content.find(".draggable");if(C.length>0){var B=this;C.jqxDragDrop({cursor:"arrow",revertDuration:0,appendTo:"body",dragZIndex:99999,revert:true,initFeedback:function(D){var F=A('<span style="white-space: nowrap;" class="'+B.toThemeProperty("jqx-fill-state-normal")+'">'+D.text()+"</span>");A(document.body).append(F);var E=F.width();F.remove();D.width(E+5);D.addClass(B.toThemeProperty("jqx-fill-state-pressed"))}});this._autoScrollTimer=null;B._dragItem=null;B._handleDragStart(C,B);B._handleDragging(C,B);B._handleDragEnd(C,B)}}},_getMouseCoordinates:function(B){this._isTouchDevice=A.jqx.mobile.isTouchDevice();if(this._isTouchDevice){var C=A.jqx.position(B.args);return{left:C.left,top:C.top}}else{return{left:B.args.pageX,top:B.args.pageY}}}})};jqxTreeDragDrop=function(){A.extend(A.jqx._jqxTree.prototype,{_hitTestBounds:function(F,B,H){var C=this;var D=null;if(F._visibleItems){var G=parseInt(F.host.offset().left);var E=F.host.outerWidth();A.each(F._visibleItems,function(J){if(B>=G&&B<G+E){if(this.top+5<H&&H<this.top+this.height){var I=A(this.element).parents("li:first");if(I.length>0){D=F.getItem(I[0]);if(D!=null){D.height=this.height;D.top=this.top;return false}}}}})}return D},_handleDragStart:function(B,D){if(D._dragOverItem){D._dragOverItem.titleElement.removeClass(D.toThemeProperty("jqx-fill-state-hover"))}var C=A.jqx.mobile.isTouchDevice();if(C){if(D.allowDrag){B.on(A.jqx.mobile.getTouchEventName("touchstart"),function(){A.jqx.mobile.setTouchScroll(false,"panel"+D.element.id)})}}B.off("dragStart");B.on("dragStart",function(E){D.feedbackElement=A("<div style='z-index: 99999; position: absolute;'></div>");D.feedbackElement.addClass(D.toThemeProperty("jqx-listbox-feedback"));D.feedbackElement.appendTo(A(document.body));D.feedbackElement.hide();D._dragCancel=false;var F=E.args.position;var G=A.find(".jqx-tree");D._trees=G;A.each(G,function(){var K=A.data(this,"jqxTree").instance;var J=K.host.find(".draggable");K._syncItems(J);if(K.allowDrag&&!K.disabled){var I=A(E.target).parents("li:first");if(I.length>0){var L=K.getItem(I[0]);if(L){D._dragItem=L;if(K.dragStart){var H=K.dragStart(L);if(H==false){D._dragCancel=true;A(E.args.element).jqxDragDrop({triggerEvents:false});A(E.args.element).jqxDragDrop("cancelDrag");clearInterval(D._autoScrollTimer);A(E.args.element).jqxDragDrop({triggerEvents:K});return false}}K._raiseEvent(8,{label:L.label,value:L.value,originalEvent:E.args})}}}});return false})},_getMouseCoordinates:function(B){this._isTouchDevice=A.jqx.mobile.isTouchDevice();if(this._isTouchDevice){var C=A.jqx.position(B.args);return{left:C.left,top:C.top}}else{return{left:B.args.pageX,top:B.args.pageY}}},_handleDragging:function(C,B){var C=this.host.find(".draggable");C.off("dragging");C.on("dragging",function(D){var I=D.args;var E=I.position;var F=B._trees;if(B._dragCancel){return}if(B._dragOverItem){B._dragOverItem.titleElement.removeClass(B.toThemeProperty("jqx-fill-state-hover"))}var G=true;var H=B._getMouseCoordinates(D);B._lastDraggingPosition=H;A.each(F,function(){if(A.jqx.isHidden(A(this))){return true}var T=A(this).offset();var M=T.top+20;var O=A(this).height()+M-40;var S=T.left;var P=A(this).width();var J=S+P;var L=A.data(this,"jqxTree").instance;if(L.disabled||!L.allowDrop){return}var Q=L.vScrollInstance;var K=L._hitTestBounds(L,H.left,H.top);if(K!=null){if(B._dragOverItem){B._dragOverItem.titleElement.removeClass(L.toThemeProperty("jqx-fill-state-hover"))}B._dragOverItem=K;if(K.element){B.feedbackElement.show();var N=K.top;var R=H.top;B._dropPosition="before";if(R>N+K.height/3){N=K.top+K.height/2;B._dragOverItem.titleElement.addClass(B.toThemeProperty("jqx-fill-state-hover"));B.feedbackElement.hide();B._dropPosition="inside"}if(R>(K.top+K.height)-K.height/3){N=K.top+K.height;B._dragOverItem.titleElement.removeClass(B.toThemeProperty("jqx-fill-state-hover"));B.feedbackElement.show();B._dropPosition="after"}B.feedbackElement.css("top",N);var S=-2+parseInt(K.titleElement.offset().left);B.feedbackElement.css("left",S);B.feedbackElement.width(A(K.titleElement).width()+12)}}if(H.left>=S&&H.left<J){if(H.top+20>=M&&H.top<=M+L.host.height()){G=false}if(H.top<M&&H.top>=M-30){clearInterval(L._autoScrollTimer);if(Q.value!=0){B.feedbackElement.hide()}L._autoScrollTimer=setInterval(function(){var U=L.panelInstance.scrollUp();var V=L.host.find(".draggable");L._syncItems(V);if(!U){clearInterval(L._autoScrollTimer)}},100)}else{if(H.top>O&&H.top<O+30){clearInterval(L._autoScrollTimer);if(Q.value!=Q.max){B.feedbackElement.hide()}L._autoScrollTimer=setInterval(function(){var U=L.panelInstance.scrollDown();var V=L.host.find(".draggable");L._syncItems(V);if(!U){clearInterval(L._autoScrollTimer)}},100)}else{clearInterval(L._autoScrollTimer)}}}else{clearInterval(L._autoScrollTimer)}});if(G){if(B.feedbackElement){B.feedbackElement.hide()}}})},_handleDragEnd:function(C,B){C.off("dragEnd");C.on("dragEnd",function(Q){var S=B.host.find(".draggable");clearInterval(B._autoScrollTimer);var J=Q.args.position;var E=B._trees;var H=null;var M=A.jqx.mobile.isTouchDevice();var R=M?B._lastDraggingPosition:B._getMouseCoordinates(Q);B.feedbackElement.remove();if(B._dragCancel){return false}if(B._dragOverItem){B._dragOverItem.titleElement.removeClass(B.toThemeProperty("jqx-fill-state-hover"))}A.each(E,function(){if(A.jqx.isHidden(A(this))){return true}var W=parseInt(A(this).offset().left);var V=W+A(this).width();var U=A.data(this,"jqxTree").instance;clearInterval(U._autoScrollTimer);if(B._dragItem!=null){if(R.left>=W&&R.left<V){var X=parseInt(A(this).offset().top);var Y=X+A(this).height();if(R.top>=X&&R.top<=Y){H=A(this)}}}});var D=B._dragItem;if(H!=null&&H.length>0){var O=H.jqxTree("allowDrop");if(O){var P=A.data(H[0],"jqxTree").instance;var N=B._dragOverItem;if(N!=null&&B._dragOverItem.treeInstance.element.id==P.element.id){var G=true;if(B.dragEnd){G=B.dragEnd(D,N,Q.args,B._dropPosition,H);if(G==false){A(Q.args.element).jqxDragDrop({triggerEvents:false});A(Q.args.element).jqxDragDrop("cancelDrag");clearInterval(B._autoScrollTimer);A(Q.args.element).jqxDragDrop({triggerEvents:true})}if(undefined==G){G=true}}if(G){var T=function(){var U=B._dragItem.treeInstance;U._refreshMapping();U._updateItemsNavigation();U._render(true,false);if(U.checkboxes){U._updateCheckStates()}B._dragItem.treeInstance=P;B._syncItems(B._dragItem.treeInstance.host.find(".draggable"))};if(P.dropAction!="none"){if(B._dragItem.id!=B._dragOverItem.id){if(B._dropPosition=="inside"){P._drop(B._dragItem.element,B._dragOverItem.element,-1,P);T()}else{var L=0;if(B._dropPosition=="after"){L++}P._drop(B._dragItem.element,B._dragOverItem.parentElement,L+A(B._dragOverItem.element).index(),P);T()}}}P._render(true,false);var F=P.host.find(".draggable");B._syncItems(F);B._dragOverItem=null;B._dragItem=null;P._refreshMapping();P._updateItemsNavigation();P.selectedItem=null;P.selectItem(D.element);if(P.checkboxes){P._updateCheckStates()}P._render(true,false)}}else{if(P.dropAction!="none"){if(P.allowDrop){var G=true;if(B.dragEnd){G=B.dragEnd(D,N,Q.args,B._dropPosition,H);if(G==false){A(Q.args.element).jqxDragDrop({triggerEvents:false});A(Q.args.element).jqxDragDrop("cancelDrag");clearInterval(B._autoScrollTimer);A(Q.args.element).jqxDragDrop({triggerEvents:true})}if(undefined==G){G=true}}if(G){B._dragItem.parentElement=null;P._drop(B._dragItem.element,null,-1,P);var K=B._dragItem.treeInstance;K._refreshMapping();K._updateItemsNavigation();if(K.checkboxes){K._updateCheckStates()}var F=K.host.find(".draggable");B._syncItems(F);B._dragItem.treeInstance=P;P.items[P.items.length]=B._dragItem;P._render(true,false);P._refreshMapping();P.selectItem(D.element);P._updateItemsNavigation();var F=P.host.find(".draggable");P._syncItems(F);if(P.checkboxes){P._updateCheckStates()}B._dragOverItem=null;B._dragItem=null}}}}}}else{if(B.dragEnd){var I=B.dragEnd(D,Q.args);if(false==I){return false}}}if(D!=null){B._raiseEvent(7,{label:D.label,value:D.value,originalEvent:Q.args})}return false})},_drop:function(G,E,B,F){if(A(E).parents("#"+G.id).length>0){return}if(E!=null){if(E.id==G.id){return}}var C=this;if(F.element.innerHTML.indexOf("UL")){var D=F.host.find("ul:first")}if(E==undefined&&E==null){if(B==undefined||B==-1){D.append(G)}else{if(D.children("li").eq(B).length==0){D.children("li").eq(B-1).after(G)}else{if(D.children("li").eq(B)[0].id!=G.id){D.children("li").eq(B).before(G)}}}}else{if(B==undefined||B==-1){E=A(E);var I=E.find("ul:first");if(I.length==0){ulElement=A("<ul></ul>");A(E).append(ulElement);I=E.find("ul:first");var H=F.itemMapping["id"+E[0].id].item;H.subtreeElement=I[0];H.hasItems=true;I.addClass(F.toThemeProperty("jqx-tree-dropdown"));I.append(G);G=I.find("li:first");H.parentElement=G}else{I.append(G)}}else{E=A(E);var I=E.find("ul:first");if(I.length==0){ulElement=A("<ul></ul>");A(E).append(ulElement);I=E.find("ul:first");if(E){var H=F.itemMapping["id"+E[0].id].item;H.subtreeElement=I[0];H.hasItems=true}I.addClass(F.toThemeProperty("jqx-tree-dropdown"));I.append(G);G=I.find("li:first");H.parentElement=G}else{if(I.children("li").eq(B).length==0){I.children("li").eq(B-1).after(G)}else{if(I.children("li").eq(B)[0].id!=G.id){I.children("li").eq(B).before(G)}}}}}},_enableDragDrop:function(){if(this.allowDrag&&this.host.jqxDragDrop){var B=this.host.find(".draggable");var D=this;if(B.length>0){B.jqxDragDrop({cursor:"arrow",revertDuration:0,appendTo:"body",dragZIndex:99999,revert:true,initFeedback:function(F){var E=A('<span style="white-space: nowrap;" class="'+D.toThemeProperty("jqx-fill-state-normal")+'">'+F.text()+"</span>");A(document.body).append(E);var G=E.width();E.remove();F.width(G+5);F.addClass(D.toThemeProperty("jqx-fill-state-pressed"))}});var C=B.jqxDragDrop("isDestroyed");if(C===true){B.jqxDragDrop("_createDragDrop")}this._autoScrollTimer=null;D._dragItem=null;D._handleDragStart(B,D);D._handleDragging(B,D);D._handleDragEnd(B,D)}}}})}})(jqxBaseFramework);