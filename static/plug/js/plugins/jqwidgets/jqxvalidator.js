(function(A){A.jqx.jqxWidget("jqxValidator","",{});A.extend(A.jqx._jqxValidator.prototype,{defineInstance:function(){var B={rules:null,scroll:true,focus:true,scrollDuration:300,scrollCallback:null,position:"right",arrow:true,animation:"fade",animationDuration:150,closeOnClick:true,onError:null,onSuccess:null,ownerElement:null,_events:["validationError","validationSuccess"],hintPositionOffset:5,_inputHint:[],rtl:false,hintType:"tooltip"};A.extend(true,this,B);return B},createInstance:function(){if(this.hintType=="label"&&this.animationDuration==150){this.animationDuration=0}this._configureInputs();this._removeEventListeners();this._addEventListeners()},destroy:function(){this._removeEventListeners();this.hide()},validate:function(D){var L=true,C,N=Infinity,E,G,M,F=[],I;this.updatePosition();var J=this;var P=0;for(var O=0;O<this.rules.length;O+=1){if(typeof this.rules[O].rule==="function"){P++}}this.positions=new Array();for(var O=0;O<this.rules.length;O+=1){var H=A(this.rules[O].input);if(typeof this.rules[O].rule==="function"){var K=function(R,Q){C=R;if(false==C){L=false;var S=A(Q.input);M=A(Q.input);F.push(M);var T=M.offset();if(T){E=T.top;if(N>E){N=E;G=M}}}P--;if(P==0){if(typeof D==="function"){J._handleValidation(L,N,G,F);if(D){D(L)}}}};this._validateRule(this.rules[O],K)}else{C=this._validateRule(this.rules[O])}if(false==C){L=false;M=A(this.rules[O].input);F.push(M);var B=M.offset();if(B){E=B.top;if(N>E){N=E;G=M}}}}if(P==0){this._handleValidation(L,N,G,F);return L}else{return undefined}},validateInput:function(D){var C=this._getRulesForInput(D),B=true;for(var E=0;E<C.length;E+=1){if(!this._validateRule(C[E])){B=false}}return B},hideHint:function(C){var B=this._getRulesForInput(C);for(var D=0;D<B.length;D+=1){this._hideHintByRule(B[D])}},hide:function(){var C;for(var B=0;B<this.rules.length;B+=1){C=this.rules[B];this._hideHintByRule(this.rules[B])}},updatePosition:function(){var C;this.positions=new Array();for(var B=0;B<this.rules.length;B+=1){C=this.rules[B];if(C.hint){this._hintLayout(C.hint,A(C.input),C.position,C)}}},_getRulesForInput:function(C){var B=[];for(var D=0;D<this.rules.length;D+=1){if(this.rules[D].input===C){B.push(this.rules[D])}}return B},_validateRule:function(G,D){var E=A(G.input),C,B=true;var I=this;var H=function(K){if(!K){var J=I.animation;I.animation=null;if(G.hint){I._hideHintByRule(G)}if(A(E).css("display")=="none"){I._hideHintByRule(G);return}if(A(E).parents().length==0){I._hideHintByRule(G);return}C=G.hintRender.apply(I,[G.message,E]);I._hintLayout(C,E,G.position,G);I._showHint(C);G.hint=C;I._removeLowPriorityHints(G);if(D){D(false,G)}I.animation=J}else{I._hideHintByRule(G);if(D){D(true,G)}}};var F=false;if(typeof G.rule==="function"){F=G.rule.call(this,E,H);if(F==true&&D){D(true,G)}}if(typeof G.rule==="function"&&F==false){if(typeof G.hintRender==="function"&&!G.hint&&!this._higherPriorityActive(G)&&E.is(":visible")){C=G.hintRender.apply(this,[G.message,E]);this._removeLowPriorityHints(G);this._hintLayout(C,E,G.position,G);this._showHint(C);G.hint=C}B=false;if(D){D(false,G)}}else{this._hideHintByRule(G)}return B},_hideHintByRule:function(C){var E=A(C.input);var D=this,F;var B=function(){if(D.hintType!="label"){return}var G=D;if(G.position=="top"||G.position=="left"){if(E.prev().hasClass(".jqx-validator-error-label")){return}}else{if(E.next().hasClass(".jqx-validator-error-label")){return}}if(E[0].nodeName.toLowerCase()!="input"){if(E.find("input").length>0){if(E.find(".jqx-input").length>0){E.find(".jqx-input").removeClass(G.toThemeProperty("jqx-validator-error-element"))}else{if(E.find(".jqx-text-area").length>0){E.find(".jqx-text-area").removeClass(G.toThemeProperty("jqx-validator-error-element"))}else{if(E.is(".jqx-checkbox")){E.find(".jqx-checkbox-default").removeClass(G.toThemeProperty("jqx-validator-error-element"))}}}if(E.is(".jqx-radiobutton")){E.find(".jqx-radiobutton-default").removeClass(G.toThemeProperty("jqx-validator-error-element"))}else{E.removeClass(G.toThemeProperty("jqx-validator-error-element"))}}}else{E.removeClass(G.toThemeProperty("jqx-validator-error-element"))}};if(C){F=C.hint;if(F){if(this.positions){if(this.positions[Math.round(F.offset().top)+"_"+Math.round(F.offset().left)]){this.positions[Math.round(F.offset().top)+"_"+Math.round(F.offset().left)]=null}}if(this.animation==="fade"){F.fadeOut(this.animationDuration,function(){F.remove();B()})}else{F.remove();B()}}C.hint=null}},_handleValidation:function(D,C,B,E){if(!D){this._scrollHandler(C);if(this.focus){B.focus()}this._raiseEvent(0,{invalidInputs:E});if(typeof this.onError==="function"){this.onError(E)}}else{this._raiseEvent(1);if(typeof this.onSuccess==="function"){this.onSuccess()}}},_scrollHandler:function(C){if(this.scroll){var B=this;A("html,body").animate({scrollTop:C},this.scrollDuration,function(){if(typeof B.scrollCallback==="function"){B.scrollCallback.call(B)}})}},_higherPriorityActive:function(B){var C=false,E;for(var D=this.rules.length-1;D>=0;D-=1){E=this.rules[D];if(C&&E.input===B.input&&E.hint){return true}if(E===B){C=true}}return false},_removeLowPriorityHints:function(B){var C=false,E;for(var D=0;D<this.rules.length;D+=1){E=this.rules[D];if(C&&E.input===B.input){this._hideHintByRule(E)}if(E===B){C=true}}},_getHintRuleByInput:function(C){var B;for(var D=0;D<this.rules.length;D+=1){B=this.rules[D];if(A(B.input)[0]===C[0]&&B.hint){return B}}return null},_removeEventListeners:function(){var F,D,C;for(var B=0;B<this.rules.length;B+=1){F=this.rules[B];C=F.action.split(",");D=A(F.input);for(var E=0;E<C.length;E+=1){this.removeHandler(D,A.trim(C[E])+".jqx-validator")}}},_addEventListeners:function(){var G,F;if(this.host.parents(".jqx-window").length>0){var E=this;var B=function(){E.updatePosition()};var D=this.host.parents(".jqx-window");this.addHandler(D,"closed",function(){E.hide()});this.addHandler(D,"moved",B);this.addHandler(D,"moving",B);this.addHandler(D,"resized",B);this.addHandler(D,"resizing",B);this.addHandler(A(document.parentWindow),"scroll",function(){if(E.scroll){B()}})}for(var C=0;C<this.rules.length;C+=1){G=this.rules[C];F=A(G.input);this._addListenerTo(F,G)}},_addListenerTo:function(G,B){var F=this,H=B.action.split(",");var D=false;if(this._isjQWidget(G)){D=true}for(var C=0;C<H.length;C+=1){var E=A.trim(H[C]);if(D&&(E=="blur"||E=="focus")){if(G&&G[0].nodeName.toLowerCase()!="input"){G=G.find("input")}}this.addHandler(G,E+".jqx-validator",function(I){F._validateRule(B)})}},_configureInputs:function(){var C,B;this.rules=this.rules||[];for(var D=0;D<this.rules.length;D+=1){this._handleInput(D)}},_handleInput:function(B){var C=this.rules[B];if(!C.position){C.position=this.position}if(!C.message){C.message="Validation Failed!"}if(!C.action){C.action="blur"}if(!C.hintRender){C.hintRender=this._hintRender}if(!C.rule){C.rule=null}else{this._handleRule(C)}},_handleRule:function(F){var E=F.rule,C,B,D=false;if(typeof E==="string"){if(E.indexOf("=")>=0){E=E.split("=");B=E[1].split(",");E=E[0]}C=this["_"+E];if(C){F.rule=function(G,H){return C.apply(this,[G].concat(B))}}else{D=true}}else{if(typeof E!=="function"){D=true}else{F.rule=E}}if(D){throw new Error("Wrong parameter!")}},_required:function(D){switch(this._getType(D)){case"jqx-input-inner":if(D.find("input").length>0){return A.trim(D.find("input").val())!==""}break;case"textarea":case"password":case"jqx-input":case"jqx-text-area":case"text":var B=A.data(D[0]);if(B.jqxMaskedInput){var C=D.jqxMaskedInput("promptChar"),E=D.jqxMaskedInput("value");return E&&E.indexOf(C)<0}else{if(B.jqxNumberInput){return D.jqxNumberInput("inputValue")!==""}else{if(B.jqxDateTimeInput){return true}else{return A.trim(D.val())!==""}}}case"checkbox":return D.is(":checked");case"radio":return D.is(":checked");case"div":if(D.is(".jqx-checkbox")){return D.jqxCheckBox("checked")}if(D.is(".jqx-radiobutton")){return D.jqxRadioButton("checked")}return false}return false},_notNumber:function(B){return this._validateText(B,function(C){if(C==""){return true}var D=/\d/;return !D.test(C)})},_startWithLetter:function(B){return this._validateText(B,function(C){if(C==""){return true}var D=/\d/;return !D.test(C.substring(0,1))})},_number:function(B){return this._validateText(B,function(C){if(C==""){return true}var D=new Number(C);return !isNaN(D)&&isFinite(D)})},_phone:function(B){return this._validateText(B,function(C){if(C==""){return true}var D=/^\(\d{3}\)(\d){3}-(\d){4}$/;return D.test(C)})},_length:function(D,B,C){return this._minLength(D,B)&&this._maxLength(D,C)},_maxLength:function(C,B){B=parseInt(B,10);return this._validateText(C,function(D){return D.length<=B})},_minLength:function(C,B){B=parseInt(B,10);return this._validateText(C,function(D){return D.length>=B})},_email:function(B){return this._validateText(B,function(C){if(C==""){return true}var D=/^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;return D.test(C)})},_zipCode:function(B){return this._validateText(B,function(C){if(C==""){return true}var D=/^(^\d{5}$)|(^\d{5}-\d{4}$)|(\d{3}-\d{2}-\d{4})$/;return D.test(C)})},_ssn:function(B){return this._validateText(B,function(C){if(C==""){return true}var D=/\d{3}-\d{2}-\d{4}/;return D.test(C)})},_validateText:function(C,B){var D;if(this._isTextInput(C)){if(this._isjQWidget(C)){if(C.find("input").length>0){D=C.find("input").val()}else{D=C.val()}}else{D=C.val()}return B(D)}return false},_isjQWidget:function(B){var C=A.data(B[0]);if(C.jqxMaskedInput||C.jqxNumberInput||C.jqxDateTimeInput){return true}return false},_isTextInput:function(B){var C=this._getType(B);return C==="text"||C==="textarea"||C==="password"||C==="jqx-input-inner"||B.is(".jqx-input")||B.is(".jqx-text-area")},_getType:function(D){if(!D[0]){return}var C=D[0].tagName.toLowerCase(),B;if(C==="textarea"){return"textarea"}else{if(D.is(".jqx-input")){return"jqx-input"}else{if(D.is(".jqx-text-area")){return"jqx-text-area"}else{if(D.find(".jqx-input").length>0){return"jqx-input-inner"}else{if(C==="input"){B=A(D).attr("type")?A(D).attr("type").toLowerCase():"text";return B}}}}}return C},_hintRender:function(C,E){if(this.hintType=="label"){var F=A('<label class="'+this.toThemeProperty("jqx-validator-error-label")+'"></label>');F.html(C);var B=this;if(this.closeOnClick){F.click(function(){B.hideHint(E.selector)})}if(this.position=="left"||this.position=="top"){F.insertBefore(A(E))}else{F.insertAfter(A(E))}return F}var F=A('<div class="'+this.toThemeProperty("jqx-validator-hint")+' jqx-rc-all"></div>'),D=this;F.html(C);if(this.closeOnClick){F.click(function(){D.hideHint(E.selector)})}if(this.ownerElement==null){F.appendTo(document.body)}else{if(this.ownerElement.innerHTML){F.appendTo(A(this.ownerElement))}else{F.appendTo(this.ownerElement)}}return F},_hintLayout:function(C,F,E,G){if(this._hintRender===G.hintRender){var D;D=this._getPosition(F,E,C,G);if(this.hintType=="label"){var B="2px";if(this.position=="left"||this.position=="top"){B="-2px"}if(F[0].nodeName.toLowerCase()!="input"&&F[0].nodeName.toLowerCase()!="textarea"){if(F.find(".jqx-text-area").length>0){F.find(".jqx-text-area").addClass(this.toThemeProperty("jqx-validator-error-element"))}if(F.find("input").length>0){if(F.find(".jqx-input").length>0){F.find(".jqx-input").addClass(this.toThemeProperty("jqx-validator-error-element"))}else{if(F.find(".jqx-text-area").length>0){F.find(".jqx-text-area").addClass(this.toThemeProperty("jqx-validator-error-element"))}else{if(F.is(".jqx-checkbox")){F.find(".jqx-checkbox-default").addClass(this.toThemeProperty("jqx-validator-error-element"))}}}if(F.is(".jqx-radiobutton")){F.find(".jqx-radiobutton-default").addClass(this.toThemeProperty("jqx-validator-error-element"))}else{F.addClass(this.toThemeProperty("jqx-validator-error-element"))}}}else{F.addClass(this.toThemeProperty("jqx-validator-error-element"))}var I=A("<span></span>");I.addClass(this.toThemeProperty("jqx-validator-hint"));I.html(C.text());I.appendTo(A(document.body));var H=I.outerWidth();I.remove();C.css({position:"relative",left:A(F).css("margin-left"),width:A(F).width(),top:B});if(E=="center"){C.css("width",H);C.css("left","0px");C.css("margin-left","auto");C.css("margin-right","auto")}return}C.css({position:"absolute",left:D.left,top:D.top});if(this.arrow){this._addArrow(F,C,E,D)}}},_showHint:function(B){if(B){if(this.animation==="fade"){B.fadeOut(0);B.fadeIn(this.animationDuration)}}},_getPosition:function(F,I,K,J){var B=F.offset(),E,H;var G=F.outerWidth();var C=F.outerHeight();if(this.rtl&&I.indexOf("left")>=0){I="right"}if(this.rtl&&I.indexOf("right")>=0){I="left"}if(this.ownerElement!=null){B={left:0,top:0};B.top=parseInt(B.top)+F.position().top;B.left=parseInt(B.left)+F.position().left}if(J&&J.hintPositionRelativeElement){var D=A(J.hintPositionRelativeElement);B=D.offset();G=D.width();C=D.height()}if(I.indexOf("top")>=0){E=B.top-C}else{if(I.indexOf("bottom")>=0){E=B.top+K.outerHeight()+this.hintPositionOffset+5}else{E=B.top}}if(I.indexOf("center")>=0){H=B.left+this.hintPositionOffset+(G-K.outerWidth())/2}else{if(I.indexOf("left")>=0){H=B.left-K.outerWidth()-this.hintPositionOffset}else{if(I.indexOf("right")>=0){H=B.left+G+this.hintPositionOffset}else{H=B.left+this.hintPositionOffset}}}if(I.indexOf(":")>=0){I=I.split(":")[1].split(",");H+=parseInt(I[0],10);E+=parseInt(I[1],10)}if(!this.positions){this.positions=new Array()}if(this.positions[Math.round(E)+"_"+Math.round(H)]){if(this.positions[Math.round(E)+"_"+Math.round(H)].top==E){E+=F.outerHeight()}}this.positions[Math.round(E)+"_"+Math.round(H)]={left:H,top:E};return{left:H,top:E}},_addArrow:function(C,B,K,D){var G=A('<div class="'+this.toThemeProperty("jqx-validator-hint-arrow")+'"></div>'),L,F;if(this.rtl&&K.indexOf("left")>=0){K="right"}if(this.rtl&&K.indexOf("right")>=0){K="left"}B.children(".jqx-validator-hint-arrow").remove();B.append(G);var I=G.outerHeight(),J=G.outerWidth(),E=B.outerHeight(),H=B.outerWidth();this._addImage(G);if(K.indexOf("top")>=0){F=E-I}else{if(K.indexOf("bottom")>=0){F=-I}else{F=(E-I)/2-I/2}}if(K.indexOf("center")>=0){L=(H-J)/2}else{if(K.indexOf("left")>=0){L=H-J/2-1}else{if(K.indexOf("right")>=0){L=-J/2}}}if(K.indexOf("topright")>=0||K.indexOf("bottomright")>=0){L=0}if(K.indexOf("topleft")>=0||K.indexOf("bottomleft")>=0){L=H-J}G.css({position:"absolute",left:L,top:F})},_addImage:function(B){var C=B.css("background-image");C=C.replace('url("',"");C=C.replace('")',"");C=C.replace("url(","");C=C.replace(")","");B.css("background-image","none");B.append('<img src="'+C+'" alt="Arrow" style="position: relative; top: 0px; left: 0px; width: '+B.width()+"px; height: "+B.height()+'px;" />')},_raiseEvent:function(C,B){var D=A.Event(this._events[C]);D.args=B;return this.host.trigger(D)},propertyChangedHandler:function(D,E,C,B){if(E==="rules"){this._configureInputs();this._removeEventListeners();this._addEventListeners()}}})})(jqxBaseFramework);