/*
 * jQuery Validation Plugin v1.15.0
 *
 * http://jqueryvalidation.org/
 *
 * Copyright (c) 2016 <PERSON><PERSON><PERSON>
 * Released under the MIT license
 */
(function(A){if(typeof define==="function"&&define.amd){define(["jquery","./jquery.validate"],A)}else{if(typeof module==="object"&&module.exports){module.exports=A(require("jquery"))}else{A(jQuery)}}}(function(A){(function(){function B(C){return C.replace(/<.[^<>]*?>/g," ").replace(/&nbsp;|&#160;/gi," ").replace(/[.(),;:!?%#$'\"_+=\/\-“”’]*/g,"")}A.validator.addMethod("maxWords",function(C,D,E){return this.optional(D)||B(C).match(/\b\w+\b/g).length<=E},A.validator.format("Please enter {0} words or less."));A.validator.addMethod("minWords",function(C,D,E){return this.optional(D)||B(C).match(/\b\w+\b/g).length>=E},A.validator.format("Please enter at least {0} words."));A.validator.addMethod("rangeWords",function(E,F,G){var D=B(E),C=/\b\w+\b/g;return this.optional(F)||D.match(C).length>=G[0]&&D.match(C).length<=G[1]},A.validator.format("Please enter between {0} and {1} words."))}());A.validator.addMethod("accept",function(E,B,D){var H=typeof D==="string"?D.replace(/\s/g,""):"image/*",C=this.optional(B),F,G,I;if(C){return C}if(A(B).attr("type")==="file"){H=H.replace(/[\-\[\]\/\{\}\(\)\+\?\.\\\^\$\|]/g,"\\$&").replace(/,/g,"|").replace("/*","/.*");if(B.files&&B.files.length){I=new RegExp(".?("+H+")$","i");for(F=0;F<B.files.length;F++){G=B.files[F];if(!G.type.match(I)){return false}}}}return true},A.validator.format("Please enter a value with a valid mimetype."));A.validator.addMethod("alphanumeric",function(B,C){return this.optional(C)||/^\w+$/i.test(B)},"Letters, numbers, and underscores only please");A.validator.addMethod("bankaccountNL",function(D,C){if(this.optional(C)){return true}if(!(/^[0-9]{9}|([0-9]{2} ){3}[0-9]{3}$/.test(D))){return false}var G=D.replace(/ /g,""),E=0,I=G.length,H,F,B;for(H=0;H<I;H++){F=I-H;B=G.substring(H,H+1);E=E+F*B}return E%11===0},"Please specify a valid bank account number");A.validator.addMethod("bankorgiroaccountNL",function(B,C){return this.optional(C)||(A.validator.methods.bankaccountNL.call(this,B,C))||(A.validator.methods.giroaccountNL.call(this,B,C))},"Please specify a valid bank or giro account number");A.validator.addMethod("bic",function(B,C){return this.optional(C)||/^([A-Z]{6}[A-Z2-9][A-NP-Z1-9])(X{3}|[A-WY-Z0-9][A-Z0-9]{2})?$/.test(B.toUpperCase())},"Please specify a valid BIC code");A.validator.addMethod("cifES",function(D){var F=[],I,E,G,B,C,H;D=D.toUpperCase();if(!D.match("((^[A-Z]{1}[0-9]{7}[A-Z0-9]{1}$|^[T]{1}[A-Z0-9]{8}$)|^[0-9]{8}[A-Z]{1}$)")){return false}for(G=0;G<9;G++){F[G]=parseInt(D.charAt(G),10)}E=F[2]+F[4]+F[6];for(B=1;B<8;B+=2){C=(2*F[B]).toString();H=C.charAt(1);E+=parseInt(C.charAt(0),10)+(H===""?0:parseInt(H,10))}if(/^[ABCDEFGHJNPQRSUVW]{1}/.test(D)){E+="";I=10-parseInt(E.charAt(E.length-1),10);D+=I;return(F[8].toString()===String.fromCharCode(64+I)||F[8].toString()===D.charAt(D.length-1))}return false},"Please specify a valid CIF number.");A.validator.addMethod("cpfBR",function(E){E=E.replace(/([~!@#$%^&*()_+=`{}\[\]\-|\\:;'<>,.\/? ])+/g,"");if(E.length!==11){return false}var G=0,F,D,B,C;F=parseInt(E.substring(9,10),10);D=parseInt(E.substring(10,11),10);B=function(J,H){var I=(J*10)%11;if((I===10)||(I===11)){I=0}return(I===H)};if(E===""||E==="00000000000"||E==="11111111111"||E==="22222222222"||E==="33333333333"||E==="44444444444"||E==="55555555555"||E==="66666666666"||E==="77777777777"||E==="88888888888"||E==="99999999999"){return false}for(C=1;C<=9;C++){G=G+parseInt(E.substring(C-1,C),10)*(11-C)}if(B(G,F)){G=0;for(C=1;C<=10;C++){G=G+parseInt(E.substring(C-1,C),10)*(12-C)}return B(G,D)}return false},"Please specify a valid CPF number");A.validator.addMethod("creditcard",function(F,G){if(this.optional(G)){return"dependency-mismatch"}if(/[^0-9 \-]+/.test(F)){return false}var E=0,B=0,H=false,C,D;F=F.replace(/\D/g,"");if(F.length<13||F.length>19){return false}for(C=F.length-1;C>=0;C--){D=F.charAt(C);B=parseInt(D,10);if(H){if((B*=2)>9){B-=9}}E+=B;H=!H}return(E%10)===0},"Please enter a valid credit card number.");A.validator.addMethod("creditcardtypes",function(D,E,B){if(/[^0-9\-]+/.test(D)){return false}D=D.replace(/\D/g,"");var C=0;if(B.mastercard){C|=1}if(B.visa){C|=2}if(B.amex){C|=4}if(B.dinersclub){C|=8}if(B.enroute){C|=16}if(B.discover){C|=32}if(B.jcb){C|=64}if(B.unknown){C|=128}if(B.all){C=1|2|4|8|16|32|64|128}if(C&1&&/^(5[12345])/.test(D)){return D.length===16}if(C&2&&/^(4)/.test(D)){return D.length===16}if(C&4&&/^(3[47])/.test(D)){return D.length===15}if(C&8&&/^(3(0[012345]|[68]))/.test(D)){return D.length===14}if(C&16&&/^(2(014|149))/.test(D)){return D.length===15}if(C&32&&/^(6011)/.test(D)){return D.length===16}if(C&64&&/^(3)/.test(D)){return D.length===16}if(C&64&&/^(2131|1800)/.test(D)){return D.length===15}if(C&128){return true}return false},"Please enter a valid credit card number.");A.validator.addMethod("currency",function(G,H,F){var D=typeof F==="string",B=D?F:F[0],E=D?true:F[1],C;B=B.replace(/,/g,"");B=E?B+"]":B+"]?";C="^["+B+"([1-9]{1}[0-9]{0,2}(\\,[0-9]{3})*(\\.[0-9]{0,2})?|[1-9]{1}[0-9]{0,}(\\.[0-9]{0,2})?|0(\\.[0-9]{0,2})?|(\\.[0-9]{1,2})?)$";C=new RegExp(C);return this.optional(H)||C.test(G)},"Please specify a valid currency");A.validator.addMethod("dateFA",function(B,C){return this.optional(C)||/^[1-4]\d{3}\/((0?[1-6]\/((3[0-1])|([1-2][0-9])|(0?[1-9])))|((1[0-2]|(0?[7-9]))\/(30|([1-2][0-9])|(0?[1-9]))))$/.test(B)},A.validator.messages.date);A.validator.addMethod("dateITA",function(E,C){var I=false,B=/^\d{1,2}\/\d{1,2}\/\d{4}$/,H,J,D,G,F;if(B.test(E)){H=E.split("/");J=parseInt(H[0],10);D=parseInt(H[1],10);G=parseInt(H[2],10);F=new Date(Date.UTC(G,D-1,J,12,0,0,0));if((F.getUTCFullYear()===G)&&(F.getUTCMonth()===D-1)&&(F.getUTCDate()===J)){I=true}else{I=false}}else{I=false}return this.optional(C)||I},A.validator.messages.date);A.validator.addMethod("dateNL",function(B,C){return this.optional(C)||/^(0?[1-9]|[12]\d|3[01])[\.\/\-](0?[1-9]|1[012])[\.\/\-]([12]\d)?(\d\d)$/.test(B)},A.validator.messages.date);A.validator.addMethod("extension",function(C,D,B){B=typeof B==="string"?B.replace(/,/g,"|"):"png|jpe?g|gif";return this.optional(D)||C.match(new RegExp("\\.("+B+")$","i"))},A.validator.format("Please enter a value with a valid extension."));A.validator.addMethod("giroaccountNL",function(B,C){return this.optional(C)||/^[0-9]{1,7}$/.test(B)},"Please specify a valid giro account number");A.validator.addMethod("iban",function(J,H){if(this.optional(H)){return true}var D=J.replace(/ /g,"").toUpperCase(),E="",M=true,C="",O="",I,K,N,P,G,L,Q,B,F;I=D.substring(0,2);L={"AL":"\\d{8}[\\dA-Z]{16}","AD":"\\d{8}[\\dA-Z]{12}","AT":"\\d{16}","AZ":"[\\dA-Z]{4}\\d{20}","BE":"\\d{12}","BH":"[A-Z]{4}[\\dA-Z]{14}","BA":"\\d{16}","BR":"\\d{23}[A-Z][\\dA-Z]","BG":"[A-Z]{4}\\d{6}[\\dA-Z]{8}","CR":"\\d{17}","HR":"\\d{17}","CY":"\\d{8}[\\dA-Z]{16}","CZ":"\\d{20}","DK":"\\d{14}","DO":"[A-Z]{4}\\d{20}","EE":"\\d{16}","FO":"\\d{14}","FI":"\\d{14}","FR":"\\d{10}[\\dA-Z]{11}\\d{2}","GE":"[\\dA-Z]{2}\\d{16}","DE":"\\d{18}","GI":"[A-Z]{4}[\\dA-Z]{15}","GR":"\\d{7}[\\dA-Z]{16}","GL":"\\d{14}","GT":"[\\dA-Z]{4}[\\dA-Z]{20}","HU":"\\d{24}","IS":"\\d{22}","IE":"[\\dA-Z]{4}\\d{14}","IL":"\\d{19}","IT":"[A-Z]\\d{10}[\\dA-Z]{12}","KZ":"\\d{3}[\\dA-Z]{13}","KW":"[A-Z]{4}[\\dA-Z]{22}","LV":"[A-Z]{4}[\\dA-Z]{13}","LB":"\\d{4}[\\dA-Z]{20}","LI":"\\d{5}[\\dA-Z]{12}","LT":"\\d{16}","LU":"\\d{3}[\\dA-Z]{13}","MK":"\\d{3}[\\dA-Z]{10}\\d{2}","MT":"[A-Z]{4}\\d{5}[\\dA-Z]{18}","MR":"\\d{23}","MU":"[A-Z]{4}\\d{19}[A-Z]{3}","MC":"\\d{10}[\\dA-Z]{11}\\d{2}","MD":"[\\dA-Z]{2}\\d{18}","ME":"\\d{18}","NL":"[A-Z]{4}\\d{10}","NO":"\\d{11}","PK":"[\\dA-Z]{4}\\d{16}","PS":"[\\dA-Z]{4}\\d{21}","PL":"\\d{24}","PT":"\\d{21}","RO":"[A-Z]{4}[\\dA-Z]{16}","SM":"[A-Z]\\d{10}[\\dA-Z]{12}","SA":"\\d{2}[\\dA-Z]{18}","RS":"\\d{18}","SK":"\\d{20}","SI":"\\d{15}","ES":"\\d{20}","SE":"\\d{20}","CH":"\\d{5}[\\dA-Z]{12}","TN":"\\d{20}","TR":"\\d{5}[\\dA-Z]{17}","AE":"\\d{3}\\d{16}","GB":"[A-Z]{4}\\d{14}","VG":"[\\dA-Z]{4}\\d{16}"};G=L[I];if(typeof G!=="undefined"){Q=new RegExp("^[A-Z]{2}\\d{2}"+G+"$","");if(!(Q.test(D))){return false}}K=D.substring(4,D.length)+D.substring(0,4);for(B=0;B<K.length;B++){N=K.charAt(B);if(N!=="0"){M=false}if(!M){E+="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ".indexOf(N)}}for(F=0;F<E.length;F++){P=E.charAt(F);O=""+C+""+P;C=O%97}return C===1},"Please specify a valid IBAN");A.validator.addMethod("integer",function(B,C){return this.optional(C)||/^-?\d+$/.test(B)},"A positive or negative non-decimal number please");A.validator.addMethod("ipv4",function(B,C){return this.optional(C)||/^(25[0-5]|2[0-4]\d|[01]?\d\d?)\.(25[0-5]|2[0-4]\d|[01]?\d\d?)\.(25[0-5]|2[0-4]\d|[01]?\d\d?)\.(25[0-5]|2[0-4]\d|[01]?\d\d?)$/i.test(B)},"Please enter a valid IP v4 address.");A.validator.addMethod("ipv6",function(B,C){return this.optional(C)||/^((([0-9A-Fa-f]{1,4}:){7}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){6}:[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){5}:([0-9A-Fa-f]{1,4}:)?[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){4}:([0-9A-Fa-f]{1,4}:){0,2}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){3}:([0-9A-Fa-f]{1,4}:){0,3}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){2}:([0-9A-Fa-f]{1,4}:){0,4}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){6}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:){0,5}:((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(::([0-9A-Fa-f]{1,4}:){0,5}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|([0-9A-Fa-f]{1,4}::([0-9A-Fa-f]{1,4}:){0,5}[0-9A-Fa-f]{1,4})|(::([0-9A-Fa-f]{1,4}:){0,6}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){1,7}:))$/i.test(B)},"Please enter a valid IP v6 address.");A.validator.addMethod("lettersonly",function(B,C){return this.optional(C)||/^[a-z]+$/i.test(B)},"Letters only please");A.validator.addMethod("letterswithbasicpunc",function(B,C){return this.optional(C)||/^[a-z\-.,()'"\s]+$/i.test(B)},"Letters or punctuation only please");A.validator.addMethod("mobileNL",function(B,C){return this.optional(C)||/^((\+|00(\s|\s?\-\s?)?)31(\s|\s?\-\s?)?(\(0\)[\-\s]?)?|0)6((\s|\s?\-\s?)?[0-9]){8}$/.test(B)},"Please specify a valid mobile number");A.validator.addMethod("mobileUK",function(B,C){B=B.replace(/\(|\)|\s+|-/g,"");return this.optional(C)||B.length>9&&B.match(/^(?:(?:(?:00\s?|\+)44\s?|0)7(?:[1345789]\d{2}|624)\s?\d{3}\s?\d{3})$/)},"Please specify a valid mobile number");A.validator.addMethod("nieES",function(B){B=B.toUpperCase();if(!B.match("((^[A-Z]{1}[0-9]{7}[A-Z0-9]{1}$|^[T]{1}[A-Z0-9]{8}$)|^[0-9]{8}[A-Z]{1}$)")){return false}if(/^[T]{1}/.test(B)){return(B[8]===/^[T]{1}[A-Z0-9]{8}$/.test(B))}if(/^[XYZ]{1}/.test(B)){return(B[8]==="TRWAGMYFPDXBNJZSQVHLCKE".charAt(B.replace("X","0").replace("Y","1").replace("Z","2").substring(0,8)%23))}return false},"Please specify a valid NIE number.");A.validator.addMethod("nifES",function(B){B=B.toUpperCase();if(!B.match("((^[A-Z]{1}[0-9]{7}[A-Z0-9]{1}$|^[T]{1}[A-Z0-9]{8}$)|^[0-9]{8}[A-Z]{1}$)")){return false}if(/^[0-9]{8}[A-Z]{1}$/.test(B)){return("TRWAGMYFPDXBNJZSQVHLCKE".charAt(B.substring(8,0)%23)===B.charAt(8))}if(/^[KLM]{1}/.test(B)){return(B[8]===String.fromCharCode(64))}return false},"Please specify a valid NIF number.");jQuery.validator.addMethod("notEqualTo",function(C,D,B){return this.optional(D)||!A.validator.methods.equalTo.call(this,C,D,B)},"Please enter a different value, values must not be the same.");A.validator.addMethod("nowhitespace",function(B,C){return this.optional(C)||/^\S+$/i.test(B)},"No white space please");A.validator.addMethod("pattern",function(C,D,B){if(this.optional(D)){return true}if(typeof B==="string"){B=new RegExp("^(?:"+B+")$")}return B.test(C)},"Invalid format.");A.validator.addMethod("phoneNL",function(B,C){return this.optional(C)||/^((\+|00(\s|\s?\-\s?)?)31(\s|\s?\-\s?)?(\(0\)[\-\s]?)?|0)[1-9]((\s|\s?\-\s?)?[0-9]){8}$/.test(B)},"Please specify a valid phone number.");A.validator.addMethod("phoneUK",function(B,C){B=B.replace(/\(|\)|\s+|-/g,"");return this.optional(C)||B.length>9&&B.match(/^(?:(?:(?:00\s?|\+)44\s?)|(?:\(?0))(?:\d{2}\)?\s?\d{4}\s?\d{4}|\d{3}\)?\s?\d{3}\s?\d{3,4}|\d{4}\)?\s?(?:\d{5}|\d{3}\s?\d{3})|\d{5}\)?\s?\d{4,5})$/)},"Please specify a valid phone number");A.validator.addMethod("phoneUS",function(B,C){B=B.replace(/\s+/g,"");return this.optional(C)||B.length>9&&B.match(/^(\+?1-?)?(\([2-9]([02-9]\d|1[02-9])\)|[2-9]([02-9]\d|1[02-9]))-?[2-9]([02-9]\d|1[02-9])-?\d{4}$/)},"Please specify a valid phone number");A.validator.addMethod("phonesUK",function(B,C){B=B.replace(/\(|\)|\s+|-/g,"");return this.optional(C)||B.length>9&&B.match(/^(?:(?:(?:00\s?|\+)44\s?|0)(?:1\d{8,9}|[23]\d{9}|7(?:[1345789]\d{8}|624\d{6})))$/)},"Please specify a valid uk phone number");A.validator.addMethod("postalCodeCA",function(B,C){return this.optional(C)||/^[ABCEGHJKLMNPRSTVXY]\d[ABCEGHJKLMNPRSTVWXYZ] *\d[ABCEGHJKLMNPRSTVWXYZ]\d$/i.test(B)},"Please specify a valid postal code");A.validator.addMethod("postalcodeBR",function(B,C){return this.optional(C)||/^\d{2}.\d{3}-\d{3}?$|^\d{5}-?\d{3}?$/.test(B)},"Informe um CEP válido.");A.validator.addMethod("postalcodeIT",function(B,C){return this.optional(C)||/^\d{5}$/.test(B)},"Please specify a valid postal code");A.validator.addMethod("postalcodeNL",function(B,C){return this.optional(C)||/^[1-9][0-9]{3}\s?[a-zA-Z]{2}$/.test(B)},"Please specify a valid postal code");A.validator.addMethod("postcodeUK",function(B,C){return this.optional(C)||/^((([A-PR-UWYZ][0-9])|([A-PR-UWYZ][0-9][0-9])|([A-PR-UWYZ][A-HK-Y][0-9])|([A-PR-UWYZ][A-HK-Y][0-9][0-9])|([A-PR-UWYZ][0-9][A-HJKSTUW])|([A-PR-UWYZ][A-HK-Y][0-9][ABEHMNPRVWXY]))\s?([0-9][ABD-HJLNP-UW-Z]{2})|(GIR)\s?(0AA))$/i.test(B)},"Please specify a valid UK postcode");A.validator.addMethod("require_from_group",function(E,F,D){var H=A(D[1],F.form),C=H.eq(0),G=C.data("valid_req_grp")?C.data("valid_req_grp"):A.extend({},this),B=H.filter(function(){return G.elementValue(this)}).length>=D[0];C.data("valid_req_grp",G);if(!A(F).data("being_validated")){H.data("being_validated",true);H.each(function(){G.element(this)});H.data("being_validated",false)}return B},A.validator.format("Please fill at least {0} of these fields."));A.validator.addMethod("skip_or_fill_minimum",function(D,C,F){var B=A(F[1],C.form),E=B.eq(0),I=E.data("valid_skip")?E.data("valid_skip"):A.extend({},this),G=B.filter(function(){return I.elementValue(this)}).length,H=G===0||G>=F[0];E.data("valid_skip",I);if(!A(C).data("being_validated")){B.data("being_validated",true);B.each(function(){I.element(this)});B.data("being_validated",false)}return H},A.validator.format("Please either skip these fields or fill at least {0} of them."));A.validator.addMethod("stateUS",function(D,B,E){var C=typeof E==="undefined",H=(C||typeof E.caseSensitive==="undefined")?false:E.caseSensitive,G=(C||typeof E.includeTerritories==="undefined")?false:E.includeTerritories,F=(C||typeof E.includeMilitary==="undefined")?false:E.includeMilitary,I;if(!G&&!F){I="^(A[KLRZ]|C[AOT]|D[CE]|FL|GA|HI|I[ADLN]|K[SY]|LA|M[ADEINOST]|N[CDEHJMVY]|O[HKR]|PA|RI|S[CD]|T[NX]|UT|V[AT]|W[AIVY])$"}else{if(G&&F){I="^(A[AEKLPRSZ]|C[AOT]|D[CE]|FL|G[AU]|HI|I[ADLN]|K[SY]|LA|M[ADEINOPST]|N[CDEHJMVY]|O[HKR]|P[AR]|RI|S[CD]|T[NX]|UT|V[AIT]|W[AIVY])$"}else{if(G){I="^(A[KLRSZ]|C[AOT]|D[CE]|FL|G[AU]|HI|I[ADLN]|K[SY]|LA|M[ADEINOPST]|N[CDEHJMVY]|O[HKR]|P[AR]|RI|S[CD]|T[NX]|UT|V[AIT]|W[AIVY])$"}else{I="^(A[AEKLPRZ]|C[AOT]|D[CE]|FL|GA|HI|I[ADLN]|K[SY]|LA|M[ADEINOST]|N[CDEHJMVY]|O[HKR]|PA|RI|S[CD]|T[NX]|UT|V[AT]|W[AIVY])$"}}}I=H?new RegExp(I):new RegExp(I,"i");return this.optional(B)||I.test(D)},"Please specify a valid state");A.validator.addMethod("strippedminlength",function(C,D,B){return A(C).text().length>=B},A.validator.format("Please enter at least {0} characters"));A.validator.addMethod("time",function(B,C){return this.optional(C)||/^([01]\d|2[0-3]|[0-9])(:[0-5]\d){1,2}$/.test(B)},"Please enter a valid time, between 00:00 and 23:59");A.validator.addMethod("time12h",function(B,C){return this.optional(C)||/^((0?[1-9]|1[012])(:[0-5]\d){1,2}(\ ?[AP]M))$/i.test(B)},"Please enter a valid time in 12-hour am/pm format");A.validator.addMethod("url2",function(B,C){return this.optional(C)||/^(https?|ftp):\/\/(((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:)*@)?(((\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5]))|((([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.)*(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.?)(:\d*)?)(\/((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)+(\/(([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)*)*)?)?(\?((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)|[\uE000-\uF8FF]|\/|\?)*)?(#((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)|\/|\?)*)?$/i.test(B)},A.validator.messages.url);A.validator.addMethod("vinUS",function(C){if(C.length!==17){return false}var I=["A","B","C","D","E","F","G","H","J","K","L","M","N","P","R","S","T","U","V","W","X","Y","Z"],D=[1,2,3,4,5,6,7,8,1,2,3,4,5,7,9,2,3,4,5,6,7,8,9],B=[8,7,6,5,4,3,2,10,0,9,8,7,6,5,4,3,2],E=0,G,H,L,J,K,F;for(G=0;G<17;G++){J=B[G];L=C.slice(G,G+1);if(G===8){F=L}if(!isNaN(L)){L*=J}else{for(H=0;H<I.length;H++){if(L.toUpperCase()===I[H]){L=D[H];L*=J;if(isNaN(F)&&H===8){F=I[H]}break}}}E+=L}K=E%11;if(K===10){K="X"}if(K===F){return true}return false},"The specified vehicle identification number (VIN) is invalid.");A.validator.addMethod("zipcodeUS",function(B,C){return this.optional(C)||/^\d{5}(-\d{4})?$/.test(B)},"The specified US ZIP Code is invalid");A.validator.addMethod("ziprange",function(B,C){return this.optional(C)||/^90[2-5]\d\{2\}-\d{4}$/.test(B)},"Your ZIP-code must be in the range 902xx-xxxx to 905xx-xxxx");A.validator.addMethod("onlyAcceptImg",function(B,C){return this.optional(C)||/.(jpg|jpeg|png)$/.test(B)},"只支持jpeg,jpg,png图片格式")}));