function NavToggle(){$(".navbar-minimalize").trigger("click")}function SmoothlyMenu(){$("body").hasClass("mini-navbar")?$("body").hasClass("fixed-sidebar")?($("#side-menu").hide(),setTimeout(function(){$("#side-menu").fadeIn(500)},300)):$("#side-menu").removeAttr("style"):($("#side-menu").hide(),setTimeout(function(){$("#side-menu").fadeIn(500)},100))}function localStorageSupport(){return"localStorage" in window&&null!==window.localStorage}$(document).ready(function(){function A(){var B=$("body > #wrapper").height()-61;$(".sidebard-panel").css("min-height",B+"px")}$("#side-menu").metisMenu(),$(".right-sidebar-toggle").click(function(){$("#right-sidebar").toggleClass("sidebar-open")}),$(".sidebar-container").slimScroll({height:"100%",railOpacity:0.4,wheelStep:10}),$(".open-small-chat").click(function(){$(this).children().toggleClass("fa-comments").toggleClass("fa-remove"),$(".small-chat-box").toggleClass("active")}),$(".small-chat-box .content").slimScroll({height:"234px",railOpacity:0.4}),$(".check-link").click(function(){var B=$(this).find("i"),C=$(this).next("span");return B.toggleClass("fa-check-square").toggleClass("fa-square-o"),C.toggleClass("todo-completed"),!1}),$(function(){$(".sidebar-collapse").slimScroll({height:"100%",railOpacity:0.9,alwaysVisible:!1})}),$(".navbar-minimalize").click(function(){$("body").toggleClass("mini-navbar"),SmoothlyMenu()}),A(),$(window).bind("load resize click scroll",function(){$("body").hasClass("body-small")||A()}),$(window).scroll(function(){$(window).scrollTop()>0&&!$("body").hasClass("fixed-nav")?$("#right-sidebar").addClass("sidebar-top"):$("#right-sidebar").removeClass("sidebar-top")}),$(".full-height-scroll").slimScroll({height:"100%"}),$("#side-menu>li").click(function(){$("body").hasClass("mini-navbar")&&NavToggle()}),$("#side-menu>li li a").click(function(){$(window).width()<769&&NavToggle()}),$(".nav-close").click(NavToggle),/(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent)&&$("#content-main").css("overflow-y","auto")}),$(window).bind("load resize",function(){$(this).width()<769&&($("body").addClass("mini-navbar"),$(".navbar-static-side").fadeIn())}),$(function(){if($("#fixednavbar").click(function(){$("#fixednavbar").is(":checked")?($(".navbar-static-top").removeClass("navbar-static-top").addClass("navbar-fixed-top"),$("body").removeClass("boxed-layout"),$("body").addClass("fixed-nav"),$("#boxedlayout").prop("checked",!1),localStorageSupport&&localStorage.setItem("boxedlayout","off"),localStorageSupport&&localStorage.setItem("fixednavbar","on")):($(".navbar-fixed-top").removeClass("navbar-fixed-top").addClass("navbar-static-top"),$("body").removeClass("fixed-nav"),localStorageSupport&&localStorage.setItem("fixednavbar","off"))}),$("#collapsemenu").click(function(){$("#collapsemenu").is(":checked")?($("body").addClass("mini-navbar"),SmoothlyMenu(),localStorageSupport&&localStorage.setItem("collapse_menu","on")):($("body").removeClass("mini-navbar"),SmoothlyMenu(),localStorageSupport&&localStorage.setItem("collapse_menu","off"))}),$("#boxedlayout").click(function(){$("#boxedlayout").is(":checked")?($("body").addClass("boxed-layout"),$("#fixednavbar").prop("checked",!1),$(".navbar-fixed-top").removeClass("navbar-fixed-top").addClass("navbar-static-top"),$("body").removeClass("fixed-nav"),localStorageSupport&&localStorage.setItem("fixednavbar","off"),localStorageSupport&&localStorage.setItem("boxedlayout","on")):($("body").removeClass("boxed-layout"),localStorageSupport&&localStorage.setItem("boxedlayout","off"))}),$(".s-skin-0").click(function(){return $("body").removeClass("skin-1"),$("body").removeClass("skin-2"),$("body").removeClass("skin-3"),!1}),$(".s-skin-1").click(function(){return $("body").removeClass("skin-2"),$("body").removeClass("skin-3"),$("body").addClass("skin-1"),!1}),$(".s-skin-3").click(function(){return $("body").removeClass("skin-1"),$("body").removeClass("skin-2"),$("body").addClass("skin-3"),!1}),localStorageSupport){var A=localStorage.getItem("collapse_menu"),D=localStorage.getItem("fixednavbar"),B=localStorage.getItem("boxedlayout");"on"==A&&$("#collapsemenu").prop("checked","checked"),"on"==D&&$("#fixednavbar").prop("checked","checked"),"on"==B&&$("#boxedlayout").prop("checked","checked")}if(localStorageSupport){var A=localStorage.getItem("collapse_menu"),D=localStorage.getItem("fixednavbar"),B=localStorage.getItem("boxedlayout"),C=$("body");"on"==A&&(C.hasClass("body-small")||C.addClass("mini-navbar")),"on"==D&&($(".navbar-static-top").removeClass("navbar-static-top").addClass("navbar-fixed-top"),C.addClass("fixed-nav")),"on"==B&&C.addClass("boxed-layout")}});