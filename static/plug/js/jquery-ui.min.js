/* jQuery UI - v1.11.2 - 2014-10-26
* http://jqueryui.com
* Includes: core.js, widget.js, mouse.js, position.js, draggable.js, droppable.js, resizable.js, selectable.js, sortable.js, accordion.js, autocomplete.js, button.js, datepicker.js, dialog.js, menu.js, progressbar.js, selectmenu.js, slider.js, spinner.js, tabs.js, tooltip.js, effect.js, effect-blind.js, effect-bounce.js, effect-clip.js, effect-drop.js, effect-explode.js, effect-fade.js, effect-fold.js, effect-highlight.js, effect-puff.js, effect-pulsate.js, effect-scale.js, effect-shake.js, effect-size.js, effect-slide.js, effect-transfer.js
* Copyright 2014 jQuery Foundation and other contributors; Licensed MIT */
!function(A){"function"==typeof define&&define.amd?define(["jquery"],A):A(jQuery)}(function(P){function N(Y,V){var W,Z,U,X=Y.nodeName.toLowerCase();return"area"===X?(W=Y.parentNode,Z=W.name,Y.href&&Z&&"map"===W.nodeName.toLowerCase()?(U=P("img[usemap='#"+Z+"']")[0],!!U&&O(U)):!1):(/input|select|textarea|button|object/.test(X)?!Y.disabled:"a"===X?Y.href||V:V)&&O(Y)}function O(U){return P.expr.filters.visible(U)&&!P(U).parents().addBack().filter(function(){return"hidden"===P.css(this,"visibility")}).length}function S(W){for(var U,V;W.length&&W[0]!==document;){if(U=W.css("position"),("absolute"===U||"relative"===U||"fixed"===U)&&(V=parseInt(W.css("zIndex"),10),!isNaN(V)&&0!==V)){return V}W=W.parent()}return 0}function T(){this._curInst=null,this._keyEvent=!1,this._disabledInputs=[],this._datepickerShowing=!1,this._inDialog=!1,this._mainDivId="ui-datepicker-div",this._inlineClass="ui-datepicker-inline",this._appendClass="ui-datepicker-append",this._triggerClass="ui-datepicker-trigger",this._dialogClass="ui-datepicker-dialog",this._disableClass="ui-datepicker-disabled",this._unselectableClass="ui-datepicker-unselectable",this._currentClass="ui-datepicker-current-day",this._dayOverClass="ui-datepicker-days-cell-over",this.regional=[],this.regional[""]={closeText:"Done",prevText:"Prev",nextText:"Next",currentText:"Today",monthNames:["January","February","March","April","May","June","July","August","September","October","November","December"],monthNamesShort:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],dayNames:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],dayNamesShort:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],dayNamesMin:["Su","Mo","Tu","We","Th","Fr","Sa"],weekHeader:"Wk",dateFormat:"mm/dd/yy",firstDay:0,isRTL:!1,showMonthAfterYear:!1,yearSuffix:""},this._defaults={showOn:"focus",showAnim:"fadeIn",showOptions:{},defaultDate:null,appendText:"",buttonText:"...",buttonImage:"",buttonImageOnly:!1,hideIfNoPrevNext:!1,navigationAsDateFormat:!1,gotoCurrent:!1,changeMonth:!1,changeYear:!1,yearRange:"c-10:c+10",showOtherMonths:!1,selectOtherMonths:!1,showWeek:!1,calculateWeek:this.iso8601Week,shortYearCutoff:"+10",minDate:null,maxDate:null,duration:"fast",beforeShowDay:null,beforeShow:null,onSelect:null,onChangeMonthYear:null,onClose:null,numberOfMonths:1,showCurrentAtPos:0,stepMonths:1,stepBigMonths:12,altField:"",altFormat:"",constrainInput:!0,showButtonPanel:!1,autoSize:!1,disabled:!1},P.extend(this._defaults,this.regional[""]),this.regional.en=P.extend(!0,{},this.regional[""]),this.regional["en-US"]=P.extend(!0,{},this.regional.en),this.dpDiv=Q(P("<div id='"+this._mainDivId+"' class='ui-datepicker ui-widget ui-widget-content ui-helper-clearfix ui-corner-all'></div>"))}function Q(U){var V="button, .ui-datepicker-prev, .ui-datepicker-next, .ui-datepicker-calendar td a";return U.delegate(V,"mouseout",function(){P(this).removeClass("ui-state-hover"),-1!==this.className.indexOf("ui-datepicker-prev")&&P(this).removeClass("ui-datepicker-prev-hover"),-1!==this.className.indexOf("ui-datepicker-next")&&P(this).removeClass("ui-datepicker-next-hover")}).delegate(V,"mouseover",R)}function R(){P.datepicker._isDisabledDatepicker(A.inline?A.dpDiv.parent()[0]:A.input[0])||(P(this).parents(".ui-datepicker-calendar").find("a").removeClass("ui-state-hover"),P(this).addClass("ui-state-hover"),-1!==this.className.indexOf("ui-datepicker-prev")&&P(this).addClass("ui-datepicker-prev-hover"),-1!==this.className.indexOf("ui-datepicker-next")&&P(this).addClass("ui-datepicker-next-hover"))}function H(V,W){P.extend(V,W);for(var U in W){null==W[U]&&(V[U]=W[U])}return V}
/*
 * jQuery UI Spinner 1.11.2
 * http://jqueryui.com
 *
 * Copyright 2014 jQuery Foundation and other contributors
 * Released under the MIT license.
 * http://jquery.org/license
 *
 * http://api.jqueryui.com/spinner/
 */
function I(U){return function(){var V=this.element.val();U.apply(this,arguments),this._refresh(),V!==this.element.val()&&this._trigger("change")}}
/*
 * jQuery UI Core 1.11.2
 * http://jqueryui.com
 *
 * Copyright 2014 jQuery Foundation and other contributors
 * Released under the MIT license.
 * http://jquery.org/license
 *
 * http://api.jqueryui.com/category/ui-core/
 */
P.ui=P.ui||{},P.extend(P.ui,{version:"1.11.2",keyCode:{BACKSPACE:8,COMMA:188,DELETE:46,DOWN:40,END:35,ENTER:13,ESCAPE:27,HOME:36,LEFT:37,PAGE_DOWN:34,PAGE_UP:33,PERIOD:190,RIGHT:39,SPACE:32,TAB:9,UP:38}}),P.fn.extend({scrollParent:function(W){var X=this.css("position"),U="absolute"===X,V=W?/(auto|scroll|hidden)/:/(auto|scroll)/,Y=this.parents().filter(function(){var Z=P(this);return U&&"static"===Z.css("position")?!1:V.test(Z.css("overflow")+Z.css("overflow-y")+Z.css("overflow-x"))}).eq(0);return"fixed"!==X&&Y.length?Y:P(this[0].ownerDocument||document)},uniqueId:function(){var U=0;return function(){return this.each(function(){this.id||(this.id="ui-id-"+ ++U)})}}(),removeUniqueId:function(){return this.each(function(){/^ui-id-\d+$/.test(this.id)&&P(this).removeAttr("id")})}}),P.extend(P.expr[":"],{data:P.expr.createPseudo?P.expr.createPseudo(function(U){return function(V){return !!P.data(V,U)}}):function(V,W,U){return !!P.data(V,U[3])},focusable:function(U){return N(U,!isNaN(P.attr(U,"tabindex")))},tabbable:function(W){var U=P.attr(W,"tabindex"),V=isNaN(U);return(V||U>=0)&&N(W,!V)}}),P("<a>").outerWidth(1).jquery||P.each(["Width","Height"],function(X,Y){function V(e,g,a,h){return P.each(W,function(){g-=parseFloat(P.css(e,"padding"+this))||0,a&&(g-=parseFloat(P.css(e,"border"+this+"Width"))||0),h&&(g-=parseFloat(P.css(e,"margin"+this))||0)}),g}var W="Width"===Y?["Left","Right"]:["Top","Bottom"],Z=Y.toLowerCase(),U={innerWidth:P.fn.innerWidth,innerHeight:P.fn.innerHeight,outerWidth:P.fn.outerWidth,outerHeight:P.fn.outerHeight};P.fn["inner"+Y]=function(a){return void 0===a?U["inner"+Y].call(this):this.each(function(){P(this).css(Z,V(this,a)+"px")})},P.fn["outer"+Y]=function(c,a){return"number"!=typeof c?U["outer"+Y].call(this,c):this.each(function(){P(this).css(Z,V(this,c,!0,a)+"px")})}}),P.fn.addBack||(P.fn.addBack=function(U){return this.add(null==U?this.prevObject:this.prevObject.filter(U))}),P("<a>").data("a-b","a").removeData("a-b").data("a-b")&&(P.fn.removeData=function(U){return function(V){return arguments.length?U.call(this,P.camelCase(V)):U.call(this)}}(P.fn.removeData)),P.ui.ie=!!/msie [\w.]+/.exec(navigator.userAgent.toLowerCase()),P.fn.extend({focus:function(U){return function(W,V){return"number"==typeof W?this.each(function(){var X=this;setTimeout(function(){P(X).focus(),V&&V.call(X)},W)}):U.apply(this,arguments)}}(P.fn.focus),disableSelection:function(){var U="onselectstart" in document.createElement("div")?"selectstart":"mousedown";return function(){return this.bind(U+".ui-disableSelection",function(V){V.preventDefault()})}}(),enableSelection:function(){return this.unbind(".ui-disableSelection")},zIndex:function(W){if(void 0!==W){return this.css("zIndex",W)}if(this.length){for(var X,U,V=P(this[0]);V.length&&V[0]!==document;){if(X=V.css("position"),("absolute"===X||"relative"===X||"fixed"===X)&&(U=parseInt(V.css("zIndex"),10),!isNaN(U)&&0!==U)){return U}V=V.parent()}}return 0}}),P.ui.plugin={add:function(W,X,U){var V,Y=P.ui[W].prototype;for(V in U){Y.plugins[V]=Y.plugins[V]||[],Y.plugins[V].push([X,U[V]])}},call:function(Y,W,X,U){var V,Z=Y.plugins[W];if(Z&&(U||Y.element[0].parentNode&&11!==Y.element[0].parentNode.nodeType)){for(V=0;V<Z.length;V++){Y.options[Z[V][0]]&&Z[V][1].apply(Y.element,X)}}}};
/*
 * jQuery UI Widget 1.11.2
 * http://jqueryui.com
 *
 * Copyright 2014 jQuery Foundation and other contributors
 * Released under the MIT license.
 * http://jquery.org/license
 *
 * http://api.jqueryui.com/jQuery.widget/
 */
var F=0,G=Array.prototype.slice;P.cleanData=function(U){return function(Y){var W,X,Z;for(Z=0;null!=(X=Y[Z]);Z++){try{W=P._data(X,"events"),W&&W.remove&&P(X).triggerHandler("remove")}catch(V){}}U(Y)}}(P.cleanData),P.widget=function(Y,Z,l){var U,a,k,W,X={},V=Y.split(".")[0];return Y=Y.split(".")[1],U=V+"-"+Y,l||(l=Z,Z=P.Widget),P.expr[":"][U.toLowerCase()]=function(c){return !!P.data(c,U)},P[V]=P[V]||{},a=P[V][Y],k=P[V][Y]=function(d,c){return this._createWidget?void (arguments.length&&this._createWidget(d,c)):new k(d,c)},P.extend(k,a,{version:l.version,_proto:P.extend({},l),_childConstructors:[]}),W=new Z,W.options=P.widget.extend({},W.options),P.each(l,function(e,c){return P.isFunction(c)?void (X[e]=function(){var d=function(){return Z.prototype[e].apply(this,arguments)},b=function(f){return Z.prototype[e].apply(this,f)};return function(){var g,h=this._super,i=this._superApply;return this._super=d,this._superApply=b,g=c.apply(this,arguments),this._super=h,this._superApply=i,g}}()):void (X[e]=c)}),k.prototype=P.widget.extend(W,{widgetEventPrefix:a?W.widgetEventPrefix||Y:Y},X,{constructor:k,namespace:V,widgetName:Y,widgetFullName:U}),a?(P.each(a._childConstructors,function(f,g){var e=g.prototype;P.widget(e.namespace+"."+e.widgetName,k,g._proto)}),delete a._childConstructors):Z._childConstructors.push(k),P.widget.bridge(Y,k),k},P.widget.extend=function(X){for(var Y,V,W=G.call(arguments,1),Z=0,U=W.length;U>Z;Z++){for(Y in W[Z]){V=W[Z][Y],W[Z].hasOwnProperty(Y)&&void 0!==V&&(X[Y]=P.isPlainObject(V)?P.isPlainObject(X[Y])?P.widget.extend({},X[Y],V):P.widget.extend({},V):V)}}return X},P.widget.bridge=function(V,W){var U=W.prototype.widgetFullName||V;P.fn[V]=function(Z){var a="string"==typeof Z,X=G.call(arguments,1),Y=this;return Z=!a&&X.length?P.widget.extend.apply(null,[Z].concat(X)):Z,this.each(a?function(){var b,d=P.data(this,U);return"instance"===Z?(Y=d,!1):d?P.isFunction(d[Z])&&"_"!==Z.charAt(0)?(b=d[Z].apply(d,X),b!==d&&void 0!==b?(Y=b&&b.jquery?Y.pushStack(b.get()):b,!1):void 0):P.error("no such method '"+Z+"' for "+V+" widget instance"):P.error("cannot call methods on "+V+" prior to initialization; attempted to call method '"+Z+"'")}:function(){var c=P.data(this,U);c?(c.option(Z||{}),c._init&&c._init()):P.data(this,U,new W(Z,this))}),Y}},P.Widget=function(){},P.Widget._childConstructors=[],P.Widget.prototype={widgetName:"widget",widgetEventPrefix:"",defaultElement:"<div>",options:{disabled:!1,create:null},_createWidget:function(U,V){V=P(V||this.defaultElement||this)[0],this.element=P(V),this.uuid=F++,this.eventNamespace="."+this.widgetName+this.uuid,this.bindings=P(),this.hoverable=P(),this.focusable=P(),V!==this&&(P.data(V,this.widgetFullName,this),this._on(!0,this.element,{remove:function(W){W.target===V&&this.destroy()}}),this.document=P(V.style?V.ownerDocument:V.document||V),this.window=P(this.document[0].defaultView||this.document[0].parentWindow)),this.options=P.widget.extend({},this.options,this._getCreateOptions(),U),this._create(),this._trigger("create",null,this._getCreateEventData()),this._init()},_getCreateOptions:P.noop,_getCreateEventData:P.noop,_create:P.noop,_init:P.noop,destroy:function(){this._destroy(),this.element.unbind(this.eventNamespace).removeData(this.widgetFullName).removeData(P.camelCase(this.widgetFullName)),this.widget().unbind(this.eventNamespace).removeAttr("aria-disabled").removeClass(this.widgetFullName+"-disabled ui-state-disabled"),this.bindings.unbind(this.eventNamespace),this.hoverable.removeClass("ui-state-hover"),this.focusable.removeClass("ui-state-focus")},_destroy:P.noop,widget:function(){return this.element},option:function(X,Y){var V,W,Z,U=X;if(0===arguments.length){return P.widget.extend({},this.options)}if("string"==typeof X){if(U={},V=X.split("."),X=V.shift(),V.length){for(W=U[X]=P.widget.extend({},this.options[X]),Z=0;Z<V.length-1;Z++){W[V[Z]]=W[V[Z]]||{},W=W[V[Z]]}if(X=V.pop(),1===arguments.length){return void 0===W[X]?null:W[X]}W[X]=Y}else{if(1===arguments.length){return void 0===this.options[X]?null:this.options[X]}U[X]=Y}}return this._setOptions(U),this},_setOptions:function(V){var U;for(U in V){this._setOption(U,V[U])}return this},_setOption:function(V,U){return this.options[V]=U,"disabled"===V&&(this.widget().toggleClass(this.widgetFullName+"-disabled",!!U),U&&(this.hoverable.removeClass("ui-state-hover"),this.focusable.removeClass("ui-state-focus"))),this},enable:function(){return this._setOptions({disabled:!1})},disable:function(){return this._setOptions({disabled:!0})},_on:function(W,X,U){var V,Y=this;"boolean"!=typeof W&&(U=X,X=W,W=!1),U?(X=V=P(X),this.bindings=this.bindings.add(X)):(U=X,X=this.element,V=this.widget()),P.each(U,function(a,Z){function b(){return W||Y.options.disabled!==!0&&!P(this).hasClass("ui-state-disabled")?("string"==typeof Z?Y[Z]:Z).apply(Y,arguments):void 0}"string"!=typeof Z&&(b.guid=Z.guid=Z.guid||b.guid||P.guid++);var c=a.match(/^([\w:-]*)\s*(.*)$/),e=c[1]+Y.eventNamespace,f=c[2];f?V.delegate(f,e,b):X.bind(e,b)})},_off:function(U,V){V=(V||"").split(" ").join(this.eventNamespace+" ")+this.eventNamespace,U.unbind(V).undelegate(V),this.bindings=P(this.bindings.not(U).get()),this.focusable=P(this.focusable.not(U).get()),this.hoverable=P(this.hoverable.not(U).get())},_delay:function(X,V){function W(){return("string"==typeof X?U[X]:X).apply(U,arguments)}var U=this;return setTimeout(W,V||0)},_hoverable:function(U){this.hoverable=this.hoverable.add(U),this._on(U,{mouseenter:function(V){P(V.currentTarget).addClass("ui-state-hover")},mouseleave:function(V){P(V.currentTarget).removeClass("ui-state-hover")}})},_focusable:function(U){this.focusable=this.focusable.add(U),this._on(U,{focusin:function(V){P(V.currentTarget).addClass("ui-state-focus")},focusout:function(V){P(V.currentTarget).removeClass("ui-state-focus")}})},_trigger:function(X,Y,V){var W,Z,U=this.options[X];if(V=V||{},Y=P.Event(Y),Y.type=(X===this.widgetEventPrefix?X:this.widgetEventPrefix+X).toLowerCase(),Y.target=this.element[0],Z=Y.originalEvent){for(W in Z){W in Y||(Y[W]=Z[W])}}return this.element.trigger(Y,V),!(P.isFunction(U)&&U.apply(this.element[0],[Y].concat(V))===!1||Y.isDefaultPrevented())}},P.each({show:"fadeIn",hide:"fadeOut"},function(U,V){P.Widget.prototype["_"+U]=function(X,Y,a){"string"==typeof Y&&(Y={effect:Y});var W,Z=Y?Y===!0||"number"==typeof Y?V:Y.effect||V:U;Y=Y||{},"number"==typeof Y&&(Y={duration:Y}),W=!P.isEmptyObject(Y),Y.complete=a,Y.delay&&X.delay(Y.delay),W&&P.effects&&P.effects.effect[Z]?X[U](Y):Z!==U&&X[Z]?X[Z](Y.duration,Y.easing,a):X.queue(function(b){P(this)[U](),a&&a.call(X[0]),b()})}});var L=(P.widget,!1);P(document).mouseup(function(){L=!1});P.widget("ui.mouse",{version:"1.11.2",options:{cancel:"input,textarea,button,select,option",distance:1,delay:0},_mouseInit:function(){var U=this;this.element.bind("mousedown."+this.widgetName,function(V){return U._mouseDown(V)}).bind("click."+this.widgetName,function(V){return !0===P.data(V.target,U.widgetName+".preventClickEvent")?(P.removeData(V.target,U.widgetName+".preventClickEvent"),V.stopImmediatePropagation(),!1):void 0}),this.started=!1},_mouseDestroy:function(){this.element.unbind("."+this.widgetName),this._mouseMoveDelegate&&this.document.unbind("mousemove."+this.widgetName,this._mouseMoveDelegate).unbind("mouseup."+this.widgetName,this._mouseUpDelegate)},_mouseDown:function(W){if(!L){this._mouseMoved=!1,this._mouseStarted&&this._mouseUp(W),this._mouseDownEvent=W;var X=this,U=1===W.which,V="string"==typeof this.options.cancel&&W.target.nodeName?P(W.target).closest(this.options.cancel).length:!1;return U&&!V&&this._mouseCapture(W)?(this.mouseDelayMet=!this.options.delay,this.mouseDelayMet||(this._mouseDelayTimer=setTimeout(function(){X.mouseDelayMet=!0},this.options.delay)),this._mouseDistanceMet(W)&&this._mouseDelayMet(W)&&(this._mouseStarted=this._mouseStart(W)!==!1,!this._mouseStarted)?(W.preventDefault(),!0):(!0===P.data(W.target,this.widgetName+".preventClickEvent")&&P.removeData(W.target,this.widgetName+".preventClickEvent"),this._mouseMoveDelegate=function(Y){return X._mouseMove(Y)},this._mouseUpDelegate=function(Y){return X._mouseUp(Y)},this.document.bind("mousemove."+this.widgetName,this._mouseMoveDelegate).bind("mouseup."+this.widgetName,this._mouseUpDelegate),W.preventDefault(),L=!0,!0)):!0}},_mouseMove:function(U){if(this._mouseMoved){if(P.ui.ie&&(!document.documentMode||document.documentMode<9)&&!U.button){return this._mouseUp(U)}if(!U.which){return this._mouseUp(U)}}return(U.which||U.button)&&(this._mouseMoved=!0),this._mouseStarted?(this._mouseDrag(U),U.preventDefault()):(this._mouseDistanceMet(U)&&this._mouseDelayMet(U)&&(this._mouseStarted=this._mouseStart(this._mouseDownEvent,U)!==!1,this._mouseStarted?this._mouseDrag(U):this._mouseUp(U)),!this._mouseStarted)},_mouseUp:function(U){return this.document.unbind("mousemove."+this.widgetName,this._mouseMoveDelegate).unbind("mouseup."+this.widgetName,this._mouseUpDelegate),this._mouseStarted&&(this._mouseStarted=!1,U.target===this._mouseDownEvent.target&&P.data(U.target,this.widgetName+".preventClickEvent",!0),this._mouseStop(U)),L=!1,!1},_mouseDistanceMet:function(U){return Math.max(Math.abs(this._mouseDownEvent.pageX-U.pageX),Math.abs(this._mouseDownEvent.pageY-U.pageY))>=this.options.distance},_mouseDelayMet:function(){return this.mouseDelayMet},_mouseStart:function(){},_mouseDrag:function(){},_mouseStop:function(){},_mouseCapture:function(){return !0}});
/*
 * jQuery UI Position 1.11.2
 * http://jqueryui.com
 *
 * Copyright 2014 jQuery Foundation and other contributors
 * Released under the MIT license.
 * http://jquery.org/license
 *
 * http://api.jqueryui.com/position/
 */
!function(){function r(f,d,e){return[parseFloat(f[0])*(Z.test(f[0])?d/100:1),parseFloat(f[1])*(Z.test(f[1])?e/100:1)]}function s(d,e){return parseInt(P.css(d,e),10)||0}function v(d){var e=d[0];return 9===e.nodeType?{width:d.width(),height:d.height(),offset:{top:0,left:0}}:P.isWindow(e)?{width:d.width(),height:d.height(),offset:{top:d.scrollTop(),left:d.scrollLeft()}}:e.preventDefault?{width:0,height:0,offset:{top:e.pageY,left:e.pageX}}:{width:d.outerWidth(),height:d.outerHeight(),offset:d.offset()}}P.ui=P.ui||{};var U,t,u=Math.max,X=Math.abs,Y=Math.round,V=/left|center|right/,W=/top|center|bottom/,p=/[\+\-]\d+(\.[\d]+)?%?/,q=/^\w+/,Z=/%$/,a=P.fn.position;P.position={scrollbarWidth:function(){if(void 0!==U){return U}var g,h,e=P("<div style='display:block;position:absolute;width:50px;height:50px;overflow:hidden;'><div style='height:100px;width:auto;'></div></div>"),i=e.children()[0];return P("body").append(e),g=i.offsetWidth,e.css("overflow","scroll"),h=i.offsetWidth,g===h&&(h=e[0].clientWidth),e.remove(),U=g-h},getScrollInfo:function(i){var j=i.isWindow||i.isDocument?"":i.element.css("overflow-x"),g=i.isWindow||i.isDocument?"":i.element.css("overflow-y"),h="scroll"===j||"auto"===j&&i.width<i.element[0].scrollWidth,k="scroll"===g||"auto"===g&&i.height<i.element[0].scrollHeight;return{width:k?P.position.scrollbarWidth():0,height:h?P.position.scrollbarWidth():0}},getWithinInfo:function(h){var i=P(h||window),f=P.isWindow(i[0]),g=!!i[0]&&9===i[0].nodeType;return{element:i,isWindow:f,isDocument:g,offset:i.offset()||{left:0,top:0},scrollLeft:i.scrollLeft(),scrollTop:i.scrollTop(),width:f||g?i.width():i.outerWidth(),height:f||g?i.height():i.outerHeight()}}},P.fn.position=function(b){if(!b||!b.of){return a.apply(this,arguments)}b=P.extend({},b);var l,d,f,k,c,i,j=P(b.of),g=P.position.getWithinInfo(b.within),h=P.position.getScrollInfo(g),m=(b.collision||"flip").split(" "),o={};return i=v(j),j[0].preventDefault&&(b.at="left top"),d=i.width,f=i.height,k=i.offset,c=P.extend({},k),P.each(["my","at"],function(){var w,e,n=(b[this]||"").split(" ");1===n.length&&(n=V.test(n[0])?n.concat(["center"]):W.test(n[0])?["center"].concat(n):["center","center"]),n[0]=V.test(n[0])?n[0]:"center",n[1]=W.test(n[1])?n[1]:"center",w=p.exec(n[0]),e=p.exec(n[1]),o[this]=[w?w[0]:0,e?e[0]:0],b[this]=[q.exec(n[0])[0],q.exec(n[1])[0]]}),1===m.length&&(m[1]=m[0]),"right"===b.at[0]?c.left+=d:"center"===b.at[0]&&(c.left+=d/2),"bottom"===b.at[1]?c.top+=f:"center"===b.at[1]&&(c.top+=f/2),l=r(o.at,d,f),c.left+=l[0],c.top+=l[1],this.each(function(){var Af,w,x=P(this),Ac=x.outerWidth(),Ad=x.outerHeight(),Ab=s(this,"marginLeft"),e=s(this,"marginTop"),Ae=Ac+Ab+s(this,"marginRight")+h.width,y=Ad+e+s(this,"marginBottom")+h.height,n=P.extend({},c),Aa=r(o.my,x.outerWidth(),x.outerHeight());"right"===b.my[0]?n.left-=Ac:"center"===b.my[0]&&(n.left-=Ac/2),"bottom"===b.my[1]?n.top-=Ad:"center"===b.my[1]&&(n.top-=Ad/2),n.left+=Aa[0],n.top+=Aa[1],t||(n.left=Y(n.left),n.top=Y(n.top)),Af={marginLeft:Ab,marginTop:e},P.each(["left","top"],function(z,Ag){P.ui.position[m[z]]&&P.ui.position[m[z]][Ag](n,{targetWidth:d,targetHeight:f,elemWidth:Ac,elemHeight:Ad,collisionPosition:Af,collisionWidth:Ae,collisionHeight:y,offset:[l[0]+Aa[0],l[1]+Aa[1]],my:b.my,at:b.at,within:g,elem:x})}),b.using&&(w=function(Aj){var Ah=k.left-n.left,Ai=Ah+d-Ac,z=k.top-n.top,Ak=z+f-Ad,Ag={target:{element:j,left:k.left,top:k.top,width:d,height:f},element:{element:x,left:n.left,top:n.top,width:Ac,height:Ad},horizontal:0>Ai?"left":Ah>0?"right":"center",vertical:0>Ak?"top":z>0?"bottom":"middle"};Ac>d&&X(Ah+Ai)<d&&(Ag.horizontal="center"),Ad>f&&X(z+Ak)<f&&(Ag.vertical="middle"),Ag.important=u(X(Ah),X(Ai))>u(X(z),X(Ak))?"horizontal":"vertical",b.using.call(this,Aj,Ag)}),x.offset(P.extend(n,{using:w}))})},P.ui.position={fit:{left:function(w,n){var o,y=n.within,g=y.isWindow?y.scrollLeft:y.offset.left,x=y.width,l=w.left-n.collisionPosition.marginLeft,m=g-l,k=l+n.collisionWidth-x-g;n.collisionWidth>x?m>0&&0>=k?(o=w.left+m+n.collisionWidth-x-g,w.left+=m-o):w.left=k>0&&0>=m?g:m>k?g+x-n.collisionWidth:g:m>0?w.left+=m:k>0?w.left-=k:w.left=u(w.left-l,w.left)},top:function(w,n){var o,y=n.within,g=y.isWindow?y.scrollTop:y.offset.top,x=n.within.height,l=w.top-n.collisionPosition.marginTop,m=g-l,k=l+n.collisionHeight-x-g;n.collisionHeight>x?m>0&&0>=k?(o=w.top+m+n.collisionHeight-x-g,w.top+=m-o):w.top=k>0&&0>=m?g:m>k?g+x-n.collisionHeight:g:m>0?w.top+=m:k>0?w.top-=k:w.top=u(w.top-l,w.top)}},flip:{left:function(Af,Ad){var Ae,Ai,h=Ad.within,Ag=h.offset.left+h.scrollLeft,Ah=h.width,y=h.isWindow?h.scrollLeft:h.offset.left,w=Af.left-Ad.collisionPosition.marginLeft,x=w-y,Ab=w+Ad.collisionWidth-Ah-y,Ac="left"===Ad.my[0]?-Ad.elemWidth:"right"===Ad.my[0]?Ad.elemWidth:0,z="left"===Ad.at[0]?Ad.targetWidth:"right"===Ad.at[0]?-Ad.targetWidth:0,Aa=-2*Ad.offset[0];0>x?(Ae=Af.left+Ac+z+Aa+Ad.collisionWidth-Ah-Ag,(0>Ae||Ae<X(x))&&(Af.left+=Ac+z+Aa)):Ab>0&&(Ai=Af.left-Ad.collisionPosition.marginLeft+Ac+z+Aa-y,(Ai>0||X(Ai)<Ab)&&(Af.left+=Ac+z+Aa))},top:function(Ag,Ae){var Af,Aj,h=Ae.within,Ah=h.offset.top+h.scrollTop,Ai=h.height,z=h.isWindow?h.scrollTop:h.offset.top,x=Ag.top-Ae.collisionPosition.marginTop,y=x-z,Ac=x+Ae.collisionHeight-Ai-z,Ad="top"===Ae.my[1],Aa=Ad?-Ae.elemHeight:"bottom"===Ae.my[1]?Ae.elemHeight:0,Ab="top"===Ae.at[1]?Ae.targetHeight:"bottom"===Ae.at[1]?-Ae.targetHeight:0,w=-2*Ae.offset[1];0>y?(Aj=Ag.top+Aa+Ab+w+Ae.collisionHeight-Ai-Ah,Ag.top+Aa+Ab+w>y&&(0>Aj||Aj<X(y))&&(Ag.top+=Aa+Ab+w)):Ac>0&&(Af=Ag.top-Ae.collisionPosition.marginTop+Aa+Ab+w-z,Ag.top+Aa+Ab+w>Ac&&(Af>0||X(Af)<Ac)&&(Ag.top+=Aa+Ab+w))}},flipfit:{left:function(){P.ui.position.flip.left.apply(this,arguments),P.ui.position.fit.left.apply(this,arguments)},top:function(){P.ui.position.flip.top.apply(this,arguments),P.ui.position.fit.top.apply(this,arguments)}}},function(){var n,o,j,k,f,l=document.getElementsByTagName("body")[0],m=document.createElement("div");n=document.createElement(l?"div":"body"),j={visibility:"hidden",width:0,height:0,border:0,margin:0,background:"none"},l&&P.extend(j,{position:"absolute",left:"-1000px",top:"-1000px"});for(f in j){n.style[f]=j[f]}n.appendChild(m),o=l||document.documentElement,o.insertBefore(n,o.firstChild),m.style.cssText="position: absolute; left: 10.7432222px;",k=P(m).offset().left,t=k>10&&11>k,n.innerHTML="",o.removeChild(n)}()}();P.ui.position;
/*
 * jQuery UI Draggable 1.11.2
 * http://jqueryui.com
 *
 * Copyright 2014 jQuery Foundation and other contributors
 * Released under the MIT license.
 * http://jquery.org/license
 *
 * http://api.jqueryui.com/draggable/
 */
P.widget("ui.draggable",P.ui.mouse,{version:"1.11.2",widgetEventPrefix:"drag",options:{addClasses:!0,appendTo:"parent",axis:!1,connectToSortable:!1,containment:!1,cursor:"auto",cursorAt:!1,grid:!1,handle:!1,helper:"original",iframeFix:!1,opacity:!1,refreshPositions:!1,revert:!1,revertDuration:500,scope:"default",scroll:!0,scrollSensitivity:20,scrollSpeed:20,snap:!1,snapMode:"both",snapTolerance:20,stack:!1,zIndex:!1,drag:null,start:null,stop:null},_create:function(){"original"===this.options.helper&&this._setPositionRelative(),this.options.addClasses&&this.element.addClass("ui-draggable"),this.options.disabled&&this.element.addClass("ui-draggable-disabled"),this._setHandleClassName(),this._mouseInit()},_setOption:function(V,U){this._super(V,U),"handle"===V&&(this._removeHandleClassName(),this._setHandleClassName())},_destroy:function(){return(this.helper||this.element).is(".ui-draggable-dragging")?void (this.destroyOnClear=!0):(this.element.removeClass("ui-draggable ui-draggable-dragging ui-draggable-disabled"),this._removeHandleClassName(),void this._mouseDestroy())},_mouseCapture:function(U){var V=this.options;return this._blurActiveElement(U),this.helper||V.disabled||P(U.target).closest(".ui-resizable-handle").length>0?!1:(this.handle=this._getHandle(U),this.handle?(this._blockFrames(V.iframeFix===!0?"iframe":V.iframeFix),!0):!1)},_blockFrames:function(U){this.iframeBlocks=this.document.find(U).map(function(){var V=P(this);return P("<div>").css("position","absolute").appendTo(V.parent()).outerWidth(V.outerWidth()).outerHeight(V.outerHeight()).offset(V.offset())[0]})},_unblockFrames:function(){this.iframeBlocks&&(this.iframeBlocks.remove(),delete this.iframeBlocks)},_blurActiveElement:function(V){var W=this.document[0];if(this.handleElement.is(V.target)){try{W.activeElement&&"body"!==W.activeElement.nodeName.toLowerCase()&&P(W.activeElement).blur()}catch(U){}}},_mouseStart:function(U){var V=this.options;return this.helper=this._createHelper(U),this.helper.addClass("ui-draggable-dragging"),this._cacheHelperProportions(),P.ui.ddmanager&&(P.ui.ddmanager.current=this),this._cacheMargins(),this.cssPosition=this.helper.css("position"),this.scrollParent=this.helper.scrollParent(!0),this.offsetParent=this.helper.offsetParent(),this.hasFixedAncestor=this.helper.parents().filter(function(){return"fixed"===P(this).css("position")}).length>0,this.positionAbs=this.element.offset(),this._refreshOffsets(U),this.originalPosition=this.position=this._generatePosition(U,!1),this.originalPageX=U.pageX,this.originalPageY=U.pageY,V.cursorAt&&this._adjustOffsetFromHelper(V.cursorAt),this._setContainment(),this._trigger("start",U)===!1?(this._clear(),!1):(this._cacheHelperProportions(),P.ui.ddmanager&&!V.dropBehaviour&&P.ui.ddmanager.prepareOffsets(this,U),this._normalizeRightBottom(),this._mouseDrag(U,!0),P.ui.ddmanager&&P.ui.ddmanager.dragStart(this,U),!0)},_refreshOffsets:function(U){this.offset={top:this.positionAbs.top-this.margins.top,left:this.positionAbs.left-this.margins.left,scroll:!1,parent:this._getParentOffset(),relative:this._getRelativeOffset()},this.offset.click={left:U.pageX-this.offset.left,top:U.pageY-this.offset.top}},_mouseDrag:function(V,W){if(this.hasFixedAncestor&&(this.offset.parent=this._getParentOffset()),this.position=this._generatePosition(V,!0),this.positionAbs=this._convertPositionTo("absolute"),!W){var U=this._uiHash();if(this._trigger("drag",V,U)===!1){return this._mouseUp({}),!1}this.position=U.position}return this.helper[0].style.left=this.position.left+"px",this.helper[0].style.top=this.position.top+"px",P.ui.ddmanager&&P.ui.ddmanager.drag(this,V),!1},_mouseStop:function(V){var W=this,U=!1;return P.ui.ddmanager&&!this.options.dropBehaviour&&(U=P.ui.ddmanager.drop(this,V)),this.dropped&&(U=this.dropped,this.dropped=!1),"invalid"===this.options.revert&&!U||"valid"===this.options.revert&&U||this.options.revert===!0||P.isFunction(this.options.revert)&&this.options.revert.call(this.element,U)?P(this.helper).animate(this.originalPosition,parseInt(this.options.revertDuration,10),function(){W._trigger("stop",V)!==!1&&W._clear()}):this._trigger("stop",V)!==!1&&this._clear(),!1},_mouseUp:function(U){return this._unblockFrames(),P.ui.ddmanager&&P.ui.ddmanager.dragStop(this,U),this.handleElement.is(U.target)&&this.element.focus(),P.ui.mouse.prototype._mouseUp.call(this,U)},cancel:function(){return this.helper.is(".ui-draggable-dragging")?this._mouseUp({}):this._clear(),this},_getHandle:function(U){return this.options.handle?!!P(U.target).closest(this.element.find(this.options.handle)).length:!0},_setHandleClassName:function(){this.handleElement=this.options.handle?this.element.find(this.options.handle):this.element,this.handleElement.addClass("ui-draggable-handle")},_removeHandleClassName:function(){this.handleElement.removeClass("ui-draggable-handle")},_createHelper:function(W){var X=this.options,U=P.isFunction(X.helper),V=U?P(X.helper.apply(this.element[0],[W])):"clone"===X.helper?this.element.clone().removeAttr("id"):this.element;return V.parents("body").length||V.appendTo("parent"===X.appendTo?this.element[0].parentNode:X.appendTo),U&&V[0]===this.element[0]&&this._setPositionRelative(),V[0]===this.element[0]||/(fixed|absolute)/.test(V.css("position"))||V.css("position","absolute"),V},_setPositionRelative:function(){/^(?:r|a|f)/.test(this.element.css("position"))||(this.element[0].style.position="relative")},_adjustOffsetFromHelper:function(U){"string"==typeof U&&(U=U.split(" ")),P.isArray(U)&&(U={left:+U[0],top:+U[1]||0}),"left" in U&&(this.offset.click.left=U.left+this.margins.left),"right" in U&&(this.offset.click.left=this.helperProportions.width-U.right+this.margins.left),"top" in U&&(this.offset.click.top=U.top+this.margins.top),"bottom" in U&&(this.offset.click.top=this.helperProportions.height-U.bottom+this.margins.top)},_isRootNode:function(U){return/(html|body)/i.test(U.tagName)||U===this.document[0]},_getParentOffset:function(){var U=this.offsetParent.offset(),V=this.document[0];return"absolute"===this.cssPosition&&this.scrollParent[0]!==V&&P.contains(this.scrollParent[0],this.offsetParent[0])&&(U.left+=this.scrollParent.scrollLeft(),U.top+=this.scrollParent.scrollTop()),this._isRootNode(this.offsetParent[0])&&(U={top:0,left:0}),{top:U.top+(parseInt(this.offsetParent.css("borderTopWidth"),10)||0),left:U.left+(parseInt(this.offsetParent.css("borderLeftWidth"),10)||0)}},_getRelativeOffset:function(){if("relative"!==this.cssPosition){return{top:0,left:0}}var V=this.element.position(),U=this._isRootNode(this.scrollParent[0]);return{top:V.top-(parseInt(this.helper.css("top"),10)||0)+(U?0:this.scrollParent.scrollTop()),left:V.left-(parseInt(this.helper.css("left"),10)||0)+(U?0:this.scrollParent.scrollLeft())}},_cacheMargins:function(){this.margins={left:parseInt(this.element.css("marginLeft"),10)||0,top:parseInt(this.element.css("marginTop"),10)||0,right:parseInt(this.element.css("marginRight"),10)||0,bottom:parseInt(this.element.css("marginBottom"),10)||0}},_cacheHelperProportions:function(){this.helperProportions={width:this.helper.outerWidth(),height:this.helper.outerHeight()}},_setContainment:function(){var W,X,U,V=this.options,Y=this.document[0];return this.relativeContainer=null,V.containment?"window"===V.containment?void (this.containment=[P(window).scrollLeft()-this.offset.relative.left-this.offset.parent.left,P(window).scrollTop()-this.offset.relative.top-this.offset.parent.top,P(window).scrollLeft()+P(window).width()-this.helperProportions.width-this.margins.left,P(window).scrollTop()+(P(window).height()||Y.body.parentNode.scrollHeight)-this.helperProportions.height-this.margins.top]):"document"===V.containment?void (this.containment=[0,0,P(Y).width()-this.helperProportions.width-this.margins.left,(P(Y).height()||Y.body.parentNode.scrollHeight)-this.helperProportions.height-this.margins.top]):V.containment.constructor===Array?void (this.containment=V.containment):("parent"===V.containment&&(V.containment=this.helper[0].parentNode),X=P(V.containment),U=X[0],void (U&&(W=/(scroll|auto)/.test(X.css("overflow")),this.containment=[(parseInt(X.css("borderLeftWidth"),10)||0)+(parseInt(X.css("paddingLeft"),10)||0),(parseInt(X.css("borderTopWidth"),10)||0)+(parseInt(X.css("paddingTop"),10)||0),(W?Math.max(U.scrollWidth,U.offsetWidth):U.offsetWidth)-(parseInt(X.css("borderRightWidth"),10)||0)-(parseInt(X.css("paddingRight"),10)||0)-this.helperProportions.width-this.margins.left-this.margins.right,(W?Math.max(U.scrollHeight,U.offsetHeight):U.offsetHeight)-(parseInt(X.css("borderBottomWidth"),10)||0)-(parseInt(X.css("paddingBottom"),10)||0)-this.helperProportions.height-this.margins.top-this.margins.bottom],this.relativeContainer=X))):void (this.containment=null)},_convertPositionTo:function(X,V){V||(V=this.position);var W="absolute"===X?1:-1,U=this._isRootNode(this.scrollParent[0]);return{top:V.top+this.offset.relative.top*W+this.offset.parent.top*W-("fixed"===this.cssPosition?-this.offset.scroll.top:U?0:this.offset.scroll.top)*W,left:V.left+this.offset.relative.left*W+this.offset.parent.left*W-("fixed"===this.cssPosition?-this.offset.scroll.left:U?0:this.offset.scroll.left)*W}},_generatePosition:function(k,Y){var Z,n,U,l,m=this.options,W=this._isRootNode(this.scrollParent[0]),X=k.pageX,V=k.pageY;return W&&this.offset.scroll||(this.offset.scroll={top:this.scrollParent.scrollTop(),left:this.scrollParent.scrollLeft()}),Y&&(this.containment&&(this.relativeContainer?(n=this.relativeContainer.offset(),Z=[this.containment[0]+n.left,this.containment[1]+n.top,this.containment[2]+n.left,this.containment[3]+n.top]):Z=this.containment,k.pageX-this.offset.click.left<Z[0]&&(X=Z[0]+this.offset.click.left),k.pageY-this.offset.click.top<Z[1]&&(V=Z[1]+this.offset.click.top),k.pageX-this.offset.click.left>Z[2]&&(X=Z[2]+this.offset.click.left),k.pageY-this.offset.click.top>Z[3]&&(V=Z[3]+this.offset.click.top)),m.grid&&(U=m.grid[1]?this.originalPageY+Math.round((V-this.originalPageY)/m.grid[1])*m.grid[1]:this.originalPageY,V=Z?U-this.offset.click.top>=Z[1]||U-this.offset.click.top>Z[3]?U:U-this.offset.click.top>=Z[1]?U-m.grid[1]:U+m.grid[1]:U,l=m.grid[0]?this.originalPageX+Math.round((X-this.originalPageX)/m.grid[0])*m.grid[0]:this.originalPageX,X=Z?l-this.offset.click.left>=Z[0]||l-this.offset.click.left>Z[2]?l:l-this.offset.click.left>=Z[0]?l-m.grid[0]:l+m.grid[0]:l),"y"===m.axis&&(X=this.originalPageX),"x"===m.axis&&(V=this.originalPageY)),{top:V-this.offset.click.top-this.offset.relative.top-this.offset.parent.top+("fixed"===this.cssPosition?-this.offset.scroll.top:W?0:this.offset.scroll.top),left:X-this.offset.click.left-this.offset.relative.left-this.offset.parent.left+("fixed"===this.cssPosition?-this.offset.scroll.left:W?0:this.offset.scroll.left)}},_clear:function(){this.helper.removeClass("ui-draggable-dragging"),this.helper[0]===this.element[0]||this.cancelHelperRemoval||this.helper.remove(),this.helper=null,this.cancelHelperRemoval=!1,this.destroyOnClear&&this.destroy()},_normalizeRightBottom:function(){"y"!==this.options.axis&&"auto"!==this.helper.css("right")&&(this.helper.width(this.helper.width()),this.helper.css("right","auto")),"x"!==this.options.axis&&"auto"!==this.helper.css("bottom")&&(this.helper.height(this.helper.height()),this.helper.css("bottom","auto"))},_trigger:function(V,W,U){return U=U||this._uiHash(),P.ui.plugin.call(this,V,[W,U,this],!0),/^(drag|start|stop)/.test(V)&&(this.positionAbs=this._convertPositionTo("absolute"),U.offset=this.positionAbs),P.Widget.prototype._trigger.call(this,V,W,U)},plugins:{},_uiHash:function(){return{helper:this.helper,position:this.position,originalPosition:this.originalPosition,offset:this.positionAbs}}}),P.ui.plugin.add("draggable","connectToSortable",{start:function(W,X,U){var V=P.extend({},X,{item:U.element});U.sortables=[],P(U.options.connectToSortable).each(function(){var Y=P(this).sortable("instance");Y&&!Y.options.disabled&&(U.sortables.push(Y),Y.refreshPositions(),Y._trigger("activate",W,V))})},stop:function(W,X,U){var V=P.extend({},X,{item:U.element});U.cancelHelperRemoval=!1,P.each(U.sortables,function(){var Y=this;Y.isOver?(Y.isOver=0,U.cancelHelperRemoval=!0,Y.cancelHelperRemoval=!1,Y._storedCSS={position:Y.placeholder.css("position"),top:Y.placeholder.css("top"),left:Y.placeholder.css("left")},Y._mouseStop(W),Y.options.helper=Y.options._helper):(Y.cancelHelperRemoval=!0,Y._trigger("deactivate",W,V))})},drag:function(V,W,U){P.each(U.sortables,function(){var X=!1,Y=this;Y.positionAbs=U.positionAbs,Y.helperProportions=U.helperProportions,Y.offset.click=U.offset.click,Y._intersectsWith(Y.containerCache)&&(X=!0,P.each(U.sortables,function(){return this.positionAbs=U.positionAbs,this.helperProportions=U.helperProportions,this.offset.click=U.offset.click,this!==Y&&this._intersectsWith(this.containerCache)&&P.contains(Y.element[0],this.element[0])&&(X=!1),X})),X?(Y.isOver||(Y.isOver=1,Y.currentItem=W.helper.appendTo(Y.element).data("ui-sortable-item",!0),Y.options._helper=Y.options.helper,Y.options.helper=function(){return W.helper[0]},V.target=Y.currentItem[0],Y._mouseCapture(V,!0),Y._mouseStart(V,!0,!0),Y.offset.click.top=U.offset.click.top,Y.offset.click.left=U.offset.click.left,Y.offset.parent.left-=U.offset.parent.left-Y.offset.parent.left,Y.offset.parent.top-=U.offset.parent.top-Y.offset.parent.top,U._trigger("toSortable",V),U.dropped=Y.element,P.each(U.sortables,function(){this.refreshPositions()}),U.currentItem=U.element,Y.fromOutside=U),Y.currentItem&&(Y._mouseDrag(V),W.position=Y.position)):Y.isOver&&(Y.isOver=0,Y.cancelHelperRemoval=!0,Y.options._revert=Y.options.revert,Y.options.revert=!1,Y._trigger("out",V,Y._uiHash(Y)),Y._mouseStop(V,!0),Y.options.revert=Y.options._revert,Y.options.helper=Y.options._helper,Y.placeholder&&Y.placeholder.remove(),U._refreshOffsets(V),W.position=U._generatePosition(V,!0),U._trigger("fromSortable",V),U.dropped=!1,P.each(U.sortables,function(){this.refreshPositions()}))})}}),P.ui.plugin.add("draggable","cursor",{start:function(W,X,U){var V=P("body"),Y=U.options;V.css("cursor")&&(Y._cursor=V.css("cursor")),V.css("cursor",Y.cursor)},stop:function(W,X,U){var V=U.options;V._cursor&&P("body").css("cursor",V._cursor)}}),P.ui.plugin.add("draggable","opacity",{start:function(W,X,U){var V=P(X.helper),Y=U.options;V.css("opacity")&&(Y._opacity=V.css("opacity")),V.css("opacity",Y.opacity)},stop:function(W,X,U){var V=U.options;V._opacity&&P(X.helper).css("opacity",V._opacity)}}),P.ui.plugin.add("draggable","scroll",{start:function(W,U,V){V.scrollParentNotHidden||(V.scrollParentNotHidden=V.helper.scrollParent(!1)),V.scrollParentNotHidden[0]!==V.document[0]&&"HTML"!==V.scrollParentNotHidden[0].tagName&&(V.overflowOffset=V.scrollParentNotHidden.offset())},drag:function(Y,Z,V){var W=V.options,a=!1,U=V.scrollParentNotHidden[0],X=V.document[0];U!==X&&"HTML"!==U.tagName?(W.axis&&"x"===W.axis||(V.overflowOffset.top+U.offsetHeight-Y.pageY<W.scrollSensitivity?U.scrollTop=a=U.scrollTop+W.scrollSpeed:Y.pageY-V.overflowOffset.top<W.scrollSensitivity&&(U.scrollTop=a=U.scrollTop-W.scrollSpeed)),W.axis&&"y"===W.axis||(V.overflowOffset.left+U.offsetWidth-Y.pageX<W.scrollSensitivity?U.scrollLeft=a=U.scrollLeft+W.scrollSpeed:Y.pageX-V.overflowOffset.left<W.scrollSensitivity&&(U.scrollLeft=a=U.scrollLeft-W.scrollSpeed))):(W.axis&&"x"===W.axis||(Y.pageY-P(X).scrollTop()<W.scrollSensitivity?a=P(X).scrollTop(P(X).scrollTop()-W.scrollSpeed):P(window).height()-(Y.pageY-P(X).scrollTop())<W.scrollSensitivity&&(a=P(X).scrollTop(P(X).scrollTop()+W.scrollSpeed))),W.axis&&"y"===W.axis||(Y.pageX-P(X).scrollLeft()<W.scrollSensitivity?a=P(X).scrollLeft(P(X).scrollLeft()-W.scrollSpeed):P(window).width()-(Y.pageX-P(X).scrollLeft())<W.scrollSensitivity&&(a=P(X).scrollLeft(P(X).scrollLeft()+W.scrollSpeed)))),a!==!1&&P.ui.ddmanager&&!W.dropBehaviour&&P.ui.ddmanager.prepareOffsets(V,Y)}}),P.ui.plugin.add("draggable","snap",{start:function(W,X,U){var V=U.options;U.snapElements=[],P(V.snap.constructor!==String?V.snap.items||":data(ui-draggable)":V.snap).each(function(){var Y=P(this),Z=Y.offset();this!==U.element[0]&&U.snapElements.push({item:this,width:Y.outerWidth(),height:Y.outerHeight(),top:Z.top,left:Z.left})})},drag:function(Aa,Ab,Ae){var Af,Ac,Ad,u,v,Z,a,y,z,w,x=Ae.options,W=x.snapTolerance,X=Ab.offset.left,U=X+Ae.helperProportions.width,V=Ab.offset.top,Y=V+Ae.helperProportions.height;for(z=Ae.snapElements.length-1;z>=0;z--){v=Ae.snapElements[z].left-Ae.margins.left,Z=v+Ae.snapElements[z].width,a=Ae.snapElements[z].top-Ae.margins.top,y=a+Ae.snapElements[z].height,v-W>U||X>Z+W||a-W>Y||V>y+W||!P.contains(Ae.snapElements[z].item.ownerDocument,Ae.snapElements[z].item)?(Ae.snapElements[z].snapping&&Ae.options.snap.release&&Ae.options.snap.release.call(Ae.element,Aa,P.extend(Ae._uiHash(),{snapItem:Ae.snapElements[z].item})),Ae.snapElements[z].snapping=!1):("inner"!==x.snapMode&&(Af=Math.abs(a-Y)<=W,Ac=Math.abs(y-V)<=W,Ad=Math.abs(v-U)<=W,u=Math.abs(Z-X)<=W,Af&&(Ab.position.top=Ae._convertPositionTo("relative",{top:a-Ae.helperProportions.height,left:0}).top),Ac&&(Ab.position.top=Ae._convertPositionTo("relative",{top:y,left:0}).top),Ad&&(Ab.position.left=Ae._convertPositionTo("relative",{top:0,left:v-Ae.helperProportions.width}).left),u&&(Ab.position.left=Ae._convertPositionTo("relative",{top:0,left:Z}).left)),w=Af||Ac||Ad||u,"outer"!==x.snapMode&&(Af=Math.abs(a-V)<=W,Ac=Math.abs(y-Y)<=W,Ad=Math.abs(v-X)<=W,u=Math.abs(Z-U)<=W,Af&&(Ab.position.top=Ae._convertPositionTo("relative",{top:a,left:0}).top),Ac&&(Ab.position.top=Ae._convertPositionTo("relative",{top:y-Ae.helperProportions.height,left:0}).top),Ad&&(Ab.position.left=Ae._convertPositionTo("relative",{top:0,left:v}).left),u&&(Ab.position.left=Ae._convertPositionTo("relative",{top:0,left:Z-Ae.helperProportions.width}).left)),!Ae.snapElements[z].snapping&&(Af||Ac||Ad||u||w)&&Ae.options.snap.snap&&Ae.options.snap.snap.call(Ae.element,Aa,P.extend(Ae._uiHash(),{snapItem:Ae.snapElements[z].item})),Ae.snapElements[z].snapping=Af||Ac||Ad||u||w)}}}),P.ui.plugin.add("draggable","stack",{start:function(X,Y,V){var W,Z=V.options,U=P.makeArray(P(Z.stack)).sort(function(a,d){return(parseInt(P(a).css("zIndex"),10)||0)-(parseInt(P(d).css("zIndex"),10)||0)});U.length&&(W=parseInt(P(U[0]).css("zIndex"),10)||0,P(U).each(function(a){P(this).css("zIndex",W+a)}),this.css("zIndex",W+U.length))}}),P.ui.plugin.add("draggable","zIndex",{start:function(W,X,U){var V=P(X.helper),Y=U.options;V.css("zIndex")&&(Y._zIndex=V.css("zIndex")),V.css("zIndex",Y.zIndex)},stop:function(W,X,U){var V=U.options;V._zIndex&&P(X.helper).css("zIndex",V._zIndex)}});P.ui.draggable;
/*
 * jQuery UI Droppable 1.11.2
 * http://jqueryui.com
 *
 * Copyright 2014 jQuery Foundation and other contributors
 * Released under the MIT license.
 * http://jquery.org/license
 *
 * http://api.jqueryui.com/droppable/
 */
P.widget("ui.droppable",{version:"1.11.2",widgetEventPrefix:"drop",options:{accept:"*",activeClass:!1,addClasses:!0,greedy:!1,hoverClass:!1,scope:"default",tolerance:"intersect",activate:null,deactivate:null,drop:null,out:null,over:null},_create:function(){var V,W=this.options,U=W.accept;this.isover=!1,this.isout=!0,this.accept=P.isFunction(U)?U:function(X){return X.is(U)},this.proportions=function(){return arguments.length?void (V=arguments[0]):V?V:V={width:this.element[0].offsetWidth,height:this.element[0].offsetHeight}},this._addToManager(W.scope),W.addClasses&&this.element.addClass("ui-droppable")},_addToManager:function(U){P.ui.ddmanager.droppables[U]=P.ui.ddmanager.droppables[U]||[],P.ui.ddmanager.droppables[U].push(this)},_splice:function(V){for(var U=0;U<V.length;U++){V[U]===this&&V.splice(U,1)}},_destroy:function(){var U=P.ui.ddmanager.droppables[this.options.scope];this._splice(U),this.element.removeClass("ui-droppable ui-droppable-disabled")},_setOption:function(V,W){if("accept"===V){this.accept=P.isFunction(W)?W:function(X){return X.is(W)}}else{if("scope"===V){var U=P.ui.ddmanager.droppables[this.options.scope];this._splice(U),this._addToManager(W)}}this._super(V,W)},_activate:function(U){var V=P.ui.ddmanager.current;this.options.activeClass&&this.element.addClass(this.options.activeClass),V&&this._trigger("activate",U,this.ui(V))},_deactivate:function(U){var V=P.ui.ddmanager.current;this.options.activeClass&&this.element.removeClass(this.options.activeClass),V&&this._trigger("deactivate",U,this.ui(V))},_over:function(U){var V=P.ui.ddmanager.current;V&&(V.currentItem||V.element)[0]!==this.element[0]&&this.accept.call(this.element[0],V.currentItem||V.element)&&(this.options.hoverClass&&this.element.addClass(this.options.hoverClass),this._trigger("over",U,this.ui(V)))},_out:function(U){var V=P.ui.ddmanager.current;V&&(V.currentItem||V.element)[0]!==this.element[0]&&this.accept.call(this.element[0],V.currentItem||V.element)&&(this.options.hoverClass&&this.element.removeClass(this.options.hoverClass),this._trigger("out",U,this.ui(V)))},_drop:function(W,X){var U=X||P.ui.ddmanager.current,V=!1;return U&&(U.currentItem||U.element)[0]!==this.element[0]?(this.element.find(":data(ui-droppable)").not(".ui-draggable-dragging").each(function(){var Y=P(this).droppable("instance");return Y.options.greedy&&!Y.options.disabled&&Y.options.scope===U.options.scope&&Y.accept.call(Y.element[0],U.currentItem||U.element)&&P.ui.intersect(U,P.extend(Y,{offset:Y.element.offset()}),Y.options.tolerance,W)?(V=!0,!1):void 0}),V?!1:this.accept.call(this.element[0],U.currentItem||U.element)?(this.options.activeClass&&this.element.removeClass(this.options.activeClass),this.options.hoverClass&&this.element.removeClass(this.options.hoverClass),this._trigger("drop",W,this.ui(U)),this.element):!1):!1},ui:function(U){return{draggable:U.currentItem||U.element,helper:U.helper,position:U.position,offset:U.positionAbs}}}),P.ui.intersect=function(){function U(X,V,W){return X>=V&&V+W>X}return function(o,p,s,V){if(!p.offset){return !1}var q=(o.positionAbs||o.position.absolute).left+o.margins.left,r=(o.positionAbs||o.position.absolute).top+o.margins.top,Y=q+o.helperProportions.width,Z=r+o.helperProportions.height,W=p.offset.left,X=p.offset.top,a=W+p.proportions().width,n=X+p.proportions().height;switch(s){case"fit":return q>=W&&a>=Y&&r>=X&&n>=Z;case"intersect":return W<q+o.helperProportions.width/2&&Y-o.helperProportions.width/2<a&&X<r+o.helperProportions.height/2&&Z-o.helperProportions.height/2<n;case"pointer":return U(V.pageY,X,p.proportions().height)&&U(V.pageX,W,p.proportions().width);case"touch":return(r>=X&&n>=r||Z>=X&&n>=Z||X>r&&Z>n)&&(q>=W&&a>=q||Y>=W&&a>=Y||W>q&&Y>a);default:return !1}}}(),P.ui.ddmanager={current:null,droppables:{"default":[]},prepareOffsets:function(Y,Z){var V,W,a=P.ui.ddmanager.droppables[Y.options.scope]||[],U=Z?Z.type:null,X=(Y.currentItem||Y.element).find(":data(ui-droppable)").addBack();P:for(V=0;V<a.length;V++){if(!(a[V].options.disabled||Y&&!a[V].accept.call(a[V].element[0],Y.currentItem||Y.element))){for(W=0;W<X.length;W++){if(X[W]===a[V].element[0]){a[V].proportions().height=0;continue P}}a[V].visible="none"!==a[V].element.css("display"),a[V].visible&&("mousedown"===U&&a[V]._activate.call(a[V],Z),a[V].offset=a[V].element.offset(),a[V].proportions({width:a[V].element[0].offsetWidth,height:a[V].element[0].offsetHeight}))}}},drop:function(V,W){var U=!1;return P.each((P.ui.ddmanager.droppables[V.options.scope]||[]).slice(),function(){this.options&&(!this.options.disabled&&this.visible&&P.ui.intersect(V,this,this.options.tolerance,W)&&(U=this._drop.call(this,W)||U),!this.options.disabled&&this.visible&&this.accept.call(this.element[0],V.currentItem||V.element)&&(this.isout=!0,this.isover=!1,this._deactivate.call(this,W)))}),U},dragStart:function(U,V){U.element.parentsUntil("body").bind("scroll.droppable",function(){U.options.refreshPositions||P.ui.ddmanager.prepareOffsets(U,V)})},drag:function(U,V){U.options.refreshPositions&&P.ui.ddmanager.prepareOffsets(U,V),P.each(P.ui.ddmanager.droppables[U.options.scope]||[],function(){if(!this.options.disabled&&!this.greedyChild&&this.visible){var X,Y,a,W=P.ui.intersect(U,this,this.options.tolerance,V),Z=!W&&this.isover?"isout":W&&!this.isover?"isover":null;Z&&(this.options.greedy&&(Y=this.options.scope,a=this.element.parents(":data(ui-droppable)").filter(function(){return P(this).droppable("instance").options.scope===Y}),a.length&&(X=P(a[0]).droppable("instance"),X.greedyChild="isover"===Z)),X&&"isover"===Z&&(X.isover=!1,X.isout=!0,X._out.call(X,V)),this[Z]=!0,this["isout"===Z?"isover":"isout"]=!1,this["isover"===Z?"_over":"_out"].call(this,V),X&&"isout"===Z&&(X.isout=!1,X.isover=!0,X._over.call(X,V)))}})},dragStop:function(U,V){U.element.parentsUntil("body").unbind("scroll.droppable"),U.options.refreshPositions||P.ui.ddmanager.prepareOffsets(U,V)}};P.ui.droppable;
/*
 * jQuery UI Resizable 1.11.2
 * http://jqueryui.com
 *
 * Copyright 2014 jQuery Foundation and other contributors
 * Released under the MIT license.
 * http://jquery.org/license
 *
 * http://api.jqueryui.com/resizable/
 */
P.widget("ui.resizable",P.ui.mouse,{version:"1.11.2",widgetEventPrefix:"resize",options:{alsoResize:!1,animate:!1,animateDuration:"slow",animateEasing:"swing",aspectRatio:!1,autoHide:!1,containment:!1,ghost:!1,grid:!1,handles:"e,s,se",helper:!1,maxHeight:null,maxWidth:null,minHeight:10,minWidth:10,zIndex:90,resize:null,start:null,stop:null},_num:function(U){return parseInt(U,10)||0},_isNumber:function(U){return !isNaN(parseInt(U,10))},_hasScroll:function(W,X){if("hidden"===P(W).css("overflow")){return !1}var U=X&&"left"===X?"scrollLeft":"scrollTop",V=!1;return W[U]>0?!0:(W[U]=1,V=W[U]>0,W[U]=0,V)},_create:function(){var Y,Z,V,W,a,U=this,X=this.options;if(this.element.addClass("ui-resizable"),P.extend(this,{_aspectRatio:!!X.aspectRatio,aspectRatio:X.aspectRatio,originalElement:this.element,_proportionallyResizeElements:[],_helper:X.helper||X.ghost||X.animate?X.helper||"ui-resizable-helper":null}),this.element[0].nodeName.match(/canvas|textarea|input|select|button|img/i)&&(this.element.wrap(P("<div class='ui-wrapper' style='overflow: hidden;'></div>").css({position:this.element.css("position"),width:this.element.outerWidth(),height:this.element.outerHeight(),top:this.element.css("top"),left:this.element.css("left")})),this.element=this.element.parent().data("ui-resizable",this.element.resizable("instance")),this.elementIsWrapper=!0,this.element.css({marginLeft:this.originalElement.css("marginLeft"),marginTop:this.originalElement.css("marginTop"),marginRight:this.originalElement.css("marginRight"),marginBottom:this.originalElement.css("marginBottom")}),this.originalElement.css({marginLeft:0,marginTop:0,marginRight:0,marginBottom:0}),this.originalResizeStyle=this.originalElement.css("resize"),this.originalElement.css("resize","none"),this._proportionallyResizeElements.push(this.originalElement.css({position:"static",zoom:1,display:"block"})),this.originalElement.css({margin:this.originalElement.css("margin")}),this._proportionallyResize()),this.handles=X.handles||(P(".ui-resizable-handle",this.element).length?{n:".ui-resizable-n",e:".ui-resizable-e",s:".ui-resizable-s",w:".ui-resizable-w",se:".ui-resizable-se",sw:".ui-resizable-sw",ne:".ui-resizable-ne",nw:".ui-resizable-nw"}:"e,s,se"),this.handles.constructor===String){for("all"===this.handles&&(this.handles="n,e,s,w,se,sw,ne,nw"),Y=this.handles.split(","),this.handles={},Z=0;Z<Y.length;Z++){V=P.trim(Y[Z]),a="ui-resizable-"+V,W=P("<div class='ui-resizable-handle "+a+"'></div>"),W.css({zIndex:X.zIndex}),"se"===V&&W.addClass("ui-icon ui-icon-gripsmall-diagonal-se"),this.handles[V]=".ui-resizable-"+V,this.element.append(W)}}this._renderAxis=function(i){var j,g,h,k;i=i||this.element;for(j in this.handles){this.handles[j].constructor===String&&(this.handles[j]=this.element.children(this.handles[j]).first().show()),this.elementIsWrapper&&this.originalElement[0].nodeName.match(/textarea|input|select|button/i)&&(g=P(this.handles[j],this.element),k=/sw|ne|nw|se|n|s/.test(j)?g.outerHeight():g.outerWidth(),h=["padding",/ne|nw|n/.test(j)?"Top":/se|sw|s/.test(j)?"Bottom":/^e$/.test(j)?"Right":"Left"].join(""),i.css(h,k),this._proportionallyResize()),P(this.handles[j]).length}},this._renderAxis(this.element),this._handles=P(".ui-resizable-handle",this.element).disableSelection(),this._handles.mouseover(function(){U.resizing||(this.className&&(W=this.className.match(/ui-resizable-(se|sw|ne|nw|n|e|s|w)/i)),U.axis=W&&W[1]?W[1]:"se")}),X.autoHide&&(this._handles.hide(),P(this.element).addClass("ui-resizable-autohide").mouseenter(function(){X.disabled||(P(this).removeClass("ui-resizable-autohide"),U._handles.show())}).mouseleave(function(){X.disabled||U.resizing||(P(this).addClass("ui-resizable-autohide"),U._handles.hide())})),this._mouseInit()},_destroy:function(){this._mouseDestroy();var U,V=function(W){P(W).removeClass("ui-resizable ui-resizable-disabled ui-resizable-resizing").removeData("resizable").removeData("ui-resizable").unbind(".resizable").find(".ui-resizable-handle").remove()};return this.elementIsWrapper&&(V(this.element),U=this.element,this.originalElement.css({position:U.css("position"),width:U.outerWidth(),height:U.outerHeight(),top:U.css("top"),left:U.css("left")}).insertAfter(U),U.remove()),this.originalElement.css("resize",this.originalResizeStyle),V(this.originalElement),this},_mouseCapture:function(W){var X,U,V=!1;for(X in this.handles){U=P(this.handles[X])[0],(U===W.target||P.contains(U,W.target))&&(V=!0)}return !this.options.disabled&&V},_mouseStart:function(X){var Y,V,W,Z=this.options,U=this.element;return this.resizing=!0,this._renderProxy(),Y=this._num(this.helper.css("left")),V=this._num(this.helper.css("top")),Z.containment&&(Y+=P(Z.containment).scrollLeft()||0,V+=P(Z.containment).scrollTop()||0),this.offset=this.helper.offset(),this.position={left:Y,top:V},this.size=this._helper?{width:this.helper.width(),height:this.helper.height()}:{width:U.width(),height:U.height()},this.originalSize=this._helper?{width:U.outerWidth(),height:U.outerHeight()}:{width:U.width(),height:U.height()},this.sizeDiff={width:U.outerWidth()-U.width(),height:U.outerHeight()-U.height()},this.originalPosition={left:Y,top:V},this.originalMousePosition={left:X.pageX,top:X.pageY},this.aspectRatio="number"==typeof Z.aspectRatio?Z.aspectRatio:this.originalSize.width/this.originalSize.height||1,W=P(".ui-resizable-"+this.axis).css("cursor"),P("body").css("cursor","auto"===W?this.axis+"-resize":W),U.addClass("ui-resizable-resizing"),this._propagate("start",X),!0},_mouseDrag:function(X){var Y,j,U=this.originalMousePosition,Z=this.axis,a=X.pageX-U.left||0,V=X.pageY-U.top||0,W=this._change[Z];return this._updatePrevProperties(),W?(Y=W.apply(this,[X,a,V]),this._updateVirtualBoundaries(X.shiftKey),(this._aspectRatio||X.shiftKey)&&(Y=this._updateRatio(Y,X)),Y=this._respectSize(Y,X),this._updateCache(Y),this._propagate("resize",X),j=this._applyChanges(),!this._helper&&this._proportionallyResizeElements.length&&this._proportionallyResize(),P.isEmptyObject(j)||(this._updatePrevProperties(),this._trigger("resize",X,this.ui()),this._applyChanges()),!1):!1},_mouseStop:function(Z){this.resizing=!1;var a,n,U,l,m,X,Y,V=this.options,W=this;return this._helper&&(a=this._proportionallyResizeElements,n=a.length&&/textarea/i.test(a[0].nodeName),U=n&&this._hasScroll(a[0],"left")?0:W.sizeDiff.height,l=n?0:W.sizeDiff.width,m={width:W.helper.width()-l,height:W.helper.height()-U},X=parseInt(W.element.css("left"),10)+(W.position.left-W.originalPosition.left)||null,Y=parseInt(W.element.css("top"),10)+(W.position.top-W.originalPosition.top)||null,V.animate||this.element.css(P.extend(m,{top:Y,left:X})),W.helper.height(W.size.height),W.helper.width(W.size.width),this._helper&&!V.animate&&this._proportionallyResize()),P("body").css("cursor","auto"),this.element.removeClass("ui-resizable-resizing"),this._propagate("stop",Z),this._helper&&this.helper.remove(),!1},_updatePrevProperties:function(){this.prevPosition={top:this.position.top,left:this.position.left},this.prevSize={width:this.size.width,height:this.size.height}},_applyChanges:function(){var U={};return this.position.top!==this.prevPosition.top&&(U.top=this.position.top+"px"),this.position.left!==this.prevPosition.left&&(U.left=this.position.left+"px"),this.size.width!==this.prevSize.width&&(U.width=this.size.width+"px"),this.size.height!==this.prevSize.height&&(U.height=this.size.height+"px"),this.helper.css(U),U},_updateVirtualBoundaries:function(Z){var X,Y,V,W,h,U=this.options;h={minWidth:this._isNumber(U.minWidth)?U.minWidth:0,maxWidth:this._isNumber(U.maxWidth)?U.maxWidth:1/0,minHeight:this._isNumber(U.minHeight)?U.minHeight:0,maxHeight:this._isNumber(U.maxHeight)?U.maxHeight:1/0},(this._aspectRatio||Z)&&(X=h.minHeight*this.aspectRatio,V=h.minWidth/this.aspectRatio,Y=h.maxHeight*this.aspectRatio,W=h.maxWidth/this.aspectRatio,X>h.minWidth&&(h.minWidth=X),V>h.minHeight&&(h.minHeight=V),Y<h.maxWidth&&(h.maxWidth=Y),W<h.maxHeight&&(h.maxHeight=W)),this._vBoundaries=h},_updateCache:function(U){this.offset=this.helper.offset(),this._isNumber(U.left)&&(this.position.left=U.left),this._isNumber(U.top)&&(this.position.top=U.top),this._isNumber(U.height)&&(this.size.height=U.height),this._isNumber(U.width)&&(this.size.width=U.width)},_updateRatio:function(X){var V=this.position,W=this.size,U=this.axis;return this._isNumber(X.height)?X.width=X.height*this.aspectRatio:this._isNumber(X.width)&&(X.height=X.width/this.aspectRatio),"sw"===U&&(X.left=V.left+(W.width-X.width),X.top=null),"nw"===U&&(X.top=V.top+(W.height-X.height),X.left=V.left+(W.width-X.width)),X},_respectSize:function(m){var Z=this._vBoundaries,l=this.axis,p=this._isNumber(m.width)&&Z.maxWidth&&Z.maxWidth<m.width,U=this._isNumber(m.height)&&Z.maxHeight&&Z.maxHeight<m.height,n=this._isNumber(m.width)&&Z.minWidth&&Z.minWidth>m.width,o=this._isNumber(m.height)&&Z.minHeight&&Z.minHeight>m.height,X=this.originalPosition.left+this.originalSize.width,Y=this.position.top+this.size.height,V=/sw|nw|w/.test(l),W=/nw|ne|n/.test(l);return n&&(m.width=Z.minWidth),o&&(m.height=Z.minHeight),p&&(m.width=Z.maxWidth),U&&(m.height=Z.maxHeight),n&&V&&(m.left=X-Z.minWidth),p&&V&&(m.left=X-Z.maxWidth),o&&W&&(m.top=Y-Z.minHeight),U&&W&&(m.top=Y-Z.maxHeight),m.width||m.height||m.left||!m.top?m.width||m.height||m.top||!m.left||(m.left=null):m.top=null,m},_getPaddingPlusBorderDimensions:function(Y){for(var W=0,X=[],U=[Y.css("borderTopWidth"),Y.css("borderRightWidth"),Y.css("borderBottomWidth"),Y.css("borderLeftWidth")],V=[Y.css("paddingTop"),Y.css("paddingRight"),Y.css("paddingBottom"),Y.css("paddingLeft")];4>W;W++){X[W]=parseInt(U[W],10)||0,X[W]+=parseInt(V[W],10)||0}return{height:X[0]+X[2],width:X[1]+X[3]}},_proportionallyResize:function(){if(this._proportionallyResizeElements.length){for(var W,U=0,V=this.helper||this.element;U<this._proportionallyResizeElements.length;U++){W=this._proportionallyResizeElements[U],this.outerDimensions||(this.outerDimensions=this._getPaddingPlusBorderDimensions(W)),W.css({height:V.height()-this.outerDimensions.height||0,width:V.width()-this.outerDimensions.width||0})}}},_renderProxy:function(){var U=this.element,V=this.options;this.elementOffset=U.offset(),this._helper?(this.helper=this.helper||P("<div style='overflow:hidden;'></div>"),this.helper.addClass(this._helper).css({width:this.element.outerWidth()-1,height:this.element.outerHeight()-1,position:"absolute",left:this.elementOffset.left+"px",top:this.elementOffset.top+"px",zIndex:++V.zIndex}),this.helper.appendTo("body").disableSelection()):this.helper=this.element},_change:{e:function(V,U){return{width:this.originalSize.width+U}},w:function(X,V){var W=this.originalSize,U=this.originalPosition;return{left:U.left+V,width:W.width-V}},n:function(Y,W,X){var U=this.originalSize,V=this.originalPosition;return{top:V.top+X,height:U.height-X}},s:function(W,U,V){return{height:this.originalSize.height+V}},se:function(V,W,U){return P.extend(this._change.s.apply(this,arguments),this._change.e.apply(this,[V,W,U]))},sw:function(V,W,U){return P.extend(this._change.s.apply(this,arguments),this._change.w.apply(this,[V,W,U]))},ne:function(V,W,U){return P.extend(this._change.n.apply(this,arguments),this._change.e.apply(this,[V,W,U]))},nw:function(V,W,U){return P.extend(this._change.n.apply(this,arguments),this._change.w.apply(this,[V,W,U]))}},_propagate:function(U,V){P.ui.plugin.call(this,U,[V,this.ui()]),"resize"!==U&&this._trigger(U,V,this.ui())},plugins:{},ui:function(){return{originalElement:this.originalElement,element:this.element,helper:this.helper,position:this.position,size:this.size,originalSize:this.originalSize,originalPosition:this.originalPosition}}}),P.ui.plugin.add("resizable","animate",{stop:function(Z){var a=P(this).resizable("instance"),n=a.options,U=a._proportionallyResizeElements,l=U.length&&/textarea/i.test(U[0].nodeName),m=l&&a._hasScroll(U[0],"left")?0:a.sizeDiff.height,X=l?0:a.sizeDiff.width,Y={width:a.size.width-X,height:a.size.height-m},V=parseInt(a.element.css("left"),10)+(a.position.left-a.originalPosition.left)||null,W=parseInt(a.element.css("top"),10)+(a.position.top-a.originalPosition.top)||null;a.element.animate(P.extend(Y,W&&V?{top:W,left:V}:{}),{duration:n.animateDuration,easing:n.animateEasing,step:function(){var b={width:parseInt(a.element.css("width"),10),height:parseInt(a.element.css("height"),10),top:parseInt(a.element.css("top"),10),left:parseInt(a.element.css("left"),10)};U&&U.length&&P(U[0]).css({width:b.width,height:b.height}),a._updateCache(b),a._propagate("resize",Z)}})}}),P.ui.plugin.add("resizable","containment",{start:function(){var n,o,r,U,p,q,X,Y=P(this).resizable("instance"),V=Y.options,W=Y.element,Z=V.containment,a=Z instanceof P?Z.get(0):/parent/.test(Z)?W.parent().get(0):Z;a&&(Y.containerElement=P(a),/document/.test(Z)||Z===document?(Y.containerOffset={left:0,top:0},Y.containerPosition={left:0,top:0},Y.parentData={element:P(document),left:0,top:0,width:P(document).width(),height:P(document).height()||document.body.parentNode.scrollHeight}):(n=P(a),o=[],P(["Top","Right","Left","Bottom"]).each(function(c,b){o[c]=Y._num(n.css("padding"+b))}),Y.containerOffset=n.offset(),Y.containerPosition=n.position(),Y.containerSize={height:n.innerHeight()-o[3],width:n.innerWidth()-o[1]},r=Y.containerOffset,U=Y.containerSize.height,p=Y.containerSize.width,q=Y._hasScroll(a,"left")?a.scrollWidth:p,X=Y._hasScroll(a)?a.scrollHeight:U,Y.parentData={element:a,left:r.left,top:r.top,width:q,height:X}))},resize:function(p){var q,t,U,r,s=P(this).resizable("instance"),X=s.options,Y=s.containerOffset,V=s.position,W=s._aspectRatio||p.shiftKey,a={top:0,left:0},o=s.containerElement,Z=!0;o[0]!==document&&/static/.test(o.css("position"))&&(a=Y),V.left<(s._helper?Y.left:0)&&(s.size.width=s.size.width+(s._helper?s.position.left-Y.left:s.position.left-a.left),W&&(s.size.height=s.size.width/s.aspectRatio,Z=!1),s.position.left=X.helper?Y.left:0),V.top<(s._helper?Y.top:0)&&(s.size.height=s.size.height+(s._helper?s.position.top-Y.top:s.position.top),W&&(s.size.width=s.size.height*s.aspectRatio,Z=!1),s.position.top=s._helper?Y.top:0),U=s.containerElement.get(0)===s.element.parent().get(0),r=/relative|absolute/.test(s.containerElement.css("position")),U&&r?(s.offset.left=s.parentData.left+s.position.left,s.offset.top=s.parentData.top+s.position.top):(s.offset.left=s.element.offset().left,s.offset.top=s.element.offset().top),q=Math.abs(s.sizeDiff.width+(s._helper?s.offset.left-a.left:s.offset.left-Y.left)),t=Math.abs(s.sizeDiff.height+(s._helper?s.offset.top-a.top:s.offset.top-Y.top)),q+s.size.width>=s.parentData.width&&(s.size.width=s.parentData.width-q,W&&(s.size.height=s.size.width/s.aspectRatio,Z=!1)),t+s.size.height>=s.parentData.height&&(s.size.height=s.parentData.height-t,W&&(s.size.width=s.size.height*s.aspectRatio,Z=!1)),Z||(s.position.left=s.prevPosition.left,s.position.top=s.prevPosition.top,s.size.width=s.prevSize.width,s.size.height=s.prevSize.height)},stop:function(){var Y=P(this).resizable("instance"),Z=Y.options,l=Y.containerOffset,U=Y.containerPosition,a=Y.containerElement,k=P(Y.helper),W=k.offset(),X=k.outerWidth()-Y.sizeDiff.width,V=k.outerHeight()-Y.sizeDiff.height;Y._helper&&!Z.animate&&/relative/.test(a.css("position"))&&P(this).css({left:W.left-U.left-l.left,width:X,height:V}),Y._helper&&!Z.animate&&/static/.test(a.css("position"))&&P(this).css({left:W.left-U.left-l.left,width:X,height:V})}}),P.ui.plugin.add("resizable","alsoResize",{start:function(){var V=P(this).resizable("instance"),W=V.options,U=function(X){P(X).each(function(){var Y=P(this);Y.data("ui-resizable-alsoresize",{width:parseInt(Y.width(),10),height:parseInt(Y.height(),10),left:parseInt(Y.css("left"),10),top:parseInt(Y.css("top"),10)})})};"object"!=typeof W.alsoResize||W.alsoResize.parentNode?U(W.alsoResize):W.alsoResize.length?(W.alsoResize=W.alsoResize[0],U(W.alsoResize)):P.each(W.alsoResize,function(X){U(X)})},resize:function(X,Y){var j=P(this).resizable("instance"),U=j.options,Z=j.originalSize,a=j.originalPosition,V={height:j.size.height-Z.height||0,width:j.size.width-Z.width||0,top:j.position.top-a.top||0,left:j.position.left-a.left||0},W=function(e,c){P(e).each(function(){var i=P(this),h=P(this).data("ui-resizable-alsoresize"),k={},d=c&&c.length?c:i.parents(Y.originalElement[0]).length?["width","height"]:["width","height","top","left"];P.each(d,function(l,f){var g=(h[f]||0)+(V[f]||0);g&&g>=0&&(k[f]=g||null)}),i.css(k)})};"object"!=typeof U.alsoResize||U.alsoResize.nodeType?W(U.alsoResize):P.each(U.alsoResize,function(d,c){W(d,c)})},stop:function(){P(this).removeData("resizable-alsoresize")}}),P.ui.plugin.add("resizable","ghost",{start:function(){var V=P(this).resizable("instance"),W=V.options,U=V.size;V.ghost=V.originalElement.clone(),V.ghost.css({opacity:0.25,display:"block",position:"relative",height:U.height,width:U.width,margin:0,left:0,top:0}).addClass("ui-resizable-ghost").addClass("string"==typeof W.ghost?W.ghost:""),V.ghost.appendTo(V.helper)},resize:function(){var U=P(this).resizable("instance");U.ghost&&U.ghost.css({position:"relative",height:U.size.height,width:U.size.width})},stop:function(){var U=P(this).resizable("instance");U.ghost&&U.helper&&U.helper.get(0).removeChild(U.ghost.get(0))}}),P.ui.plugin.add("resizable","grid",{resize:function(){var y,z=P(this).resizable("instance"),Ac=z.options,Ad=z.size,Aa=z.originalSize,Ab=z.originalPosition,a=z.axis,t="number"==typeof Ac.grid?[Ac.grid,Ac.grid]:Ac.grid,Y=t[0]||1,Z=t[1]||1,w=Math.round((Ad.width-Aa.width)/Y)*Y,x=Math.round((Ad.height-Aa.height)/Z)*Z,u=Aa.width+w,v=Aa.height+x,W=Ac.maxWidth&&Ac.maxWidth<u,X=Ac.maxHeight&&Ac.maxHeight<v,U=Ac.minWidth&&Ac.minWidth>u,V=Ac.minHeight&&Ac.minHeight>v;Ac.grid=t,U&&(u+=Y),V&&(v+=Z),W&&(u-=Y),X&&(v-=Z),/^(se|s|e)$/.test(a)?(z.size.width=u,z.size.height=v):/^(ne)$/.test(a)?(z.size.width=u,z.size.height=v,z.position.top=Ab.top-x):/^(sw)$/.test(a)?(z.size.width=u,z.size.height=v,z.position.left=Ab.left-w):((0>=v-Z||0>=u-Y)&&(y=z._getPaddingPlusBorderDimensions(this)),v-Z>0?(z.size.height=v,z.position.top=Ab.top-x):(v=Z-y.height,z.size.height=v,z.position.top=Ab.top+Aa.height-v),u-Y>0?(z.size.width=u,z.position.left=Ab.left-w):(u=Z-y.height,z.size.width=u,z.position.left=Ab.left+Aa.width-u))}});P.ui.resizable,P.widget("ui.selectable",P.ui.mouse,{version:"1.11.2",options:{appendTo:"body",autoRefresh:!0,distance:0,filter:"*",tolerance:"touch",selected:null,selecting:null,start:null,stop:null,unselected:null,unselecting:null},_create:function(){var U,V=this;this.element.addClass("ui-selectable"),this.dragged=!1,this.refresh=function(){U=P(V.options.filter,V.element[0]),U.addClass("ui-selectee"),U.each(function(){var W=P(this),X=W.offset();P.data(this,"selectable-item",{element:this,$element:W,left:X.left,top:X.top,right:X.left+W.outerWidth(),bottom:X.top+W.outerHeight(),startselected:!1,selected:W.hasClass("ui-selected"),selecting:W.hasClass("ui-selecting"),unselecting:W.hasClass("ui-unselecting")})})},this.refresh(),this.selectees=U.addClass("ui-selectee"),this._mouseInit(),this.helper=P("<div class='ui-selectable-helper'></div>")},_destroy:function(){this.selectees.removeClass("ui-selectee").removeData("selectable-item"),this.element.removeClass("ui-selectable ui-selectable-disabled"),this._mouseDestroy()},_mouseStart:function(V){var W=this,U=this.options;this.opos=[V.pageX,V.pageY],this.options.disabled||(this.selectees=P(U.filter,this.element[0]),this._trigger("start",V),P(U.appendTo).append(this.helper),this.helper.css({left:V.pageX,top:V.pageY,width:0,height:0}),U.autoRefresh&&this.refresh(),this.selectees.filter(".ui-selected").each(function(){var X=P.data(this,"selectable-item");X.startselected=!0,V.metaKey||V.ctrlKey||(X.$element.removeClass("ui-selected"),X.selected=!1,X.$element.addClass("ui-unselecting"),X.unselecting=!0,W._trigger("unselecting",V,{unselecting:X.element}))}),P(V.target).parents().addBack().each(function(){var X,Y=P.data(this,"selectable-item");return Y?(X=!V.metaKey&&!V.ctrlKey||!Y.$element.hasClass("ui-selected"),Y.$element.removeClass(X?"ui-unselecting":"ui-selected").addClass(X?"ui-selecting":"ui-unselecting"),Y.unselecting=!X,Y.selecting=X,Y.selected=X,X?W._trigger("selecting",V,{selecting:Y.element}):W._trigger("unselecting",V,{unselecting:Y.element}),!1):void 0}))},_mouseDrag:function(X){if(this.dragged=!0,!this.options.disabled){var Y,j=this,U=this.options,Z=this.opos[0],a=this.opos[1],V=X.pageX,W=X.pageY;return Z>V&&(Y=V,V=Z,Z=Y),a>W&&(Y=W,W=a,a=Y),this.helper.css({left:Z,top:a,width:V-Z,height:W-a}),this.selectees.each(function(){var b=P.data(this,"selectable-item"),d=!1;b&&b.element!==j.element[0]&&("touch"===U.tolerance?d=!(b.left>V||b.right<Z||b.top>W||b.bottom<a):"fit"===U.tolerance&&(d=b.left>Z&&b.right<V&&b.top>a&&b.bottom<W),d?(b.selected&&(b.$element.removeClass("ui-selected"),b.selected=!1),b.unselecting&&(b.$element.removeClass("ui-unselecting"),b.unselecting=!1),b.selecting||(b.$element.addClass("ui-selecting"),b.selecting=!0,j._trigger("selecting",X,{selecting:b.element}))):(b.selecting&&((X.metaKey||X.ctrlKey)&&b.startselected?(b.$element.removeClass("ui-selecting"),b.selecting=!1,b.$element.addClass("ui-selected"),b.selected=!0):(b.$element.removeClass("ui-selecting"),b.selecting=!1,b.startselected&&(b.$element.addClass("ui-unselecting"),b.unselecting=!0),j._trigger("unselecting",X,{unselecting:b.element}))),b.selected&&(X.metaKey||X.ctrlKey||b.startselected||(b.$element.removeClass("ui-selected"),b.selected=!1,b.$element.addClass("ui-unselecting"),b.unselecting=!0,j._trigger("unselecting",X,{unselecting:b.element})))))}),!1}},_mouseStop:function(U){var V=this;return this.dragged=!1,P(".ui-unselecting",this.element[0]).each(function(){var W=P.data(this,"selectable-item");W.$element.removeClass("ui-unselecting"),W.unselecting=!1,W.startselected=!1,V._trigger("unselected",U,{unselected:W.element})}),P(".ui-selecting",this.element[0]).each(function(){var W=P.data(this,"selectable-item");W.$element.removeClass("ui-selecting").addClass("ui-selected"),W.selecting=!1,W.selected=!0,W.startselected=!0,V._trigger("selected",U,{selected:W.element})}),this._trigger("stop",U),this.helper.remove(),!1}}),P.widget("ui.sortable",P.ui.mouse,{version:"1.11.2",widgetEventPrefix:"sort",ready:!1,options:{appendTo:"parent",axis:!1,connectWith:!1,containment:!1,cursor:"auto",cursorAt:!1,dropOnEmpty:!0,forcePlaceholderSize:!1,forceHelperSize:!1,grid:!1,handle:!1,helper:"original",items:"> *",opacity:!1,placeholder:!1,revert:!1,scroll:!0,scrollSensitivity:20,scrollSpeed:20,scope:"default",tolerance:"intersect",zIndex:1000,activate:null,beforeStop:null,change:null,deactivate:null,out:null,over:null,receive:null,remove:null,sort:null,start:null,stop:null,update:null},_isOverAxis:function(W,U,V){return W>=U&&U+V>W},_isFloating:function(U){return/left|right/.test(U.css("float"))||/inline|table-cell/.test(U.css("display"))},_create:function(){var U=this.options;this.containerCache={},this.element.addClass("ui-sortable"),this.refresh(),this.floating=this.items.length?"x"===U.axis||this._isFloating(this.items[0].item):!1,this.offset=this.element.offset(),this._mouseInit(),this._setHandleClassName(),this.ready=!0},_setOption:function(V,U){this._super(V,U),"handle"===V&&this._setHandleClassName()},_setHandleClassName:function(){this.element.find(".ui-sortable-handle").removeClass("ui-sortable-handle"),P.each(this.items,function(){(this.instance.options.handle?this.item.find(this.instance.options.handle):this.item).addClass("ui-sortable-handle")})},_destroy:function(){this.element.removeClass("ui-sortable ui-sortable-disabled").find(".ui-sortable-handle").removeClass("ui-sortable-handle"),this._mouseDestroy();for(var U=this.items.length-1;U>=0;U--){this.items[U].item.removeData(this.widgetName+"-item")}return this},_mouseCapture:function(W,X){var U=null,V=!1,Y=this;return this.reverting?!1:this.options.disabled||"static"===this.options.type?!1:(this._refreshItems(W),P(W.target).parents().each(function(){return P.data(this,Y.widgetName+"-item")===Y?(U=P(this),!1):void 0}),P.data(W.target,Y.widgetName+"-item")===Y&&(U=P(W.target)),U&&(!this.options.handle||X||(P(this.options.handle,U).find("*").addBack().each(function(){this===W.target&&(V=!0)}),V))?(this.currentItem=U,this._removeCurrentsFromItems(),!0):!1)},_mouseStart:function(X,Y,V){var W,Z,U=this.options;if(this.currentContainer=this,this.refreshPositions(),this.helper=this._createHelper(X),this._cacheHelperProportions(),this._cacheMargins(),this.scrollParent=this.helper.scrollParent(),this.offset=this.currentItem.offset(),this.offset={top:this.offset.top-this.margins.top,left:this.offset.left-this.margins.left},P.extend(this.offset,{click:{left:X.pageX-this.offset.left,top:X.pageY-this.offset.top},parent:this._getParentOffset(),relative:this._getRelativeOffset()}),this.helper.css("position","absolute"),this.cssPosition=this.helper.css("position"),this.originalPosition=this._generatePosition(X),this.originalPageX=X.pageX,this.originalPageY=X.pageY,U.cursorAt&&this._adjustOffsetFromHelper(U.cursorAt),this.domPosition={prev:this.currentItem.prev()[0],parent:this.currentItem.parent()[0]},this.helper[0]!==this.currentItem[0]&&this.currentItem.hide(),this._createPlaceholder(),U.containment&&this._setContainment(),U.cursor&&"auto"!==U.cursor&&(Z=this.document.find("body"),this.storedCursor=Z.css("cursor"),Z.css("cursor",U.cursor),this.storedStylesheet=P("<style>*{ cursor: "+U.cursor+" !important; }</style>").appendTo(Z)),U.opacity&&(this.helper.css("opacity")&&(this._storedOpacity=this.helper.css("opacity")),this.helper.css("opacity",U.opacity)),U.zIndex&&(this.helper.css("zIndex")&&(this._storedZIndex=this.helper.css("zIndex")),this.helper.css("zIndex",U.zIndex)),this.scrollParent[0]!==document&&"HTML"!==this.scrollParent[0].tagName&&(this.overflowOffset=this.scrollParent.offset()),this._trigger("start",X,this._uiHash()),this._preserveHelperProportions||this._cacheHelperProportions(),!V){for(W=this.containers.length-1;W>=0;W--){this.containers[W]._trigger("activate",X,this._uiHash(this))}}return P.ui.ddmanager&&(P.ui.ddmanager.current=this),P.ui.ddmanager&&!U.dropBehaviour&&P.ui.ddmanager.prepareOffsets(this,X),this.dragging=!0,this.helper.addClass("ui-sortable-helper"),this._mouseDrag(X),!0},_mouseDrag:function(Y){var Z,V,W,a,U=this.options,X=!1;for(this.position=this._generatePosition(Y),this.positionAbs=this._convertPositionTo("absolute"),this.lastPositionAbs||(this.lastPositionAbs=this.positionAbs),this.options.scroll&&(this.scrollParent[0]!==document&&"HTML"!==this.scrollParent[0].tagName?(this.overflowOffset.top+this.scrollParent[0].offsetHeight-Y.pageY<U.scrollSensitivity?this.scrollParent[0].scrollTop=X=this.scrollParent[0].scrollTop+U.scrollSpeed:Y.pageY-this.overflowOffset.top<U.scrollSensitivity&&(this.scrollParent[0].scrollTop=X=this.scrollParent[0].scrollTop-U.scrollSpeed),this.overflowOffset.left+this.scrollParent[0].offsetWidth-Y.pageX<U.scrollSensitivity?this.scrollParent[0].scrollLeft=X=this.scrollParent[0].scrollLeft+U.scrollSpeed:Y.pageX-this.overflowOffset.left<U.scrollSensitivity&&(this.scrollParent[0].scrollLeft=X=this.scrollParent[0].scrollLeft-U.scrollSpeed)):(Y.pageY-P(document).scrollTop()<U.scrollSensitivity?X=P(document).scrollTop(P(document).scrollTop()-U.scrollSpeed):P(window).height()-(Y.pageY-P(document).scrollTop())<U.scrollSensitivity&&(X=P(document).scrollTop(P(document).scrollTop()+U.scrollSpeed)),Y.pageX-P(document).scrollLeft()<U.scrollSensitivity?X=P(document).scrollLeft(P(document).scrollLeft()-U.scrollSpeed):P(window).width()-(Y.pageX-P(document).scrollLeft())<U.scrollSensitivity&&(X=P(document).scrollLeft(P(document).scrollLeft()+U.scrollSpeed))),X!==!1&&P.ui.ddmanager&&!U.dropBehaviour&&P.ui.ddmanager.prepareOffsets(this,Y)),this.positionAbs=this._convertPositionTo("absolute"),this.options.axis&&"y"===this.options.axis||(this.helper[0].style.left=this.position.left+"px"),this.options.axis&&"x"===this.options.axis||(this.helper[0].style.top=this.position.top+"px"),Z=this.items.length-1;Z>=0;Z--){if(V=this.items[Z],W=V.item[0],a=this._intersectsWithPointer(V),a&&V.instance===this.currentContainer&&W!==this.currentItem[0]&&this.placeholder[1===a?"next":"prev"]()[0]!==W&&!P.contains(this.placeholder[0],W)&&("semi-dynamic"===this.options.type?!P.contains(this.element[0],W):!0)){if(this.direction=1===a?"down":"up","pointer"!==this.options.tolerance&&!this._intersectsWithSides(V)){break}this._rearrange(Y,V),this._trigger("change",Y,this._uiHash());break}}return this._contactContainers(Y),P.ui.ddmanager&&P.ui.ddmanager.drag(this,Y),this._trigger("sort",Y,this._uiHash()),this.lastPositionAbs=this.positionAbs,!1},_mouseStop:function(X,Y){if(X){if(P.ui.ddmanager&&!this.options.dropBehaviour&&P.ui.ddmanager.drop(this,X),this.options.revert){var V=this,W=this.placeholder.offset(),Z=this.options.axis,U={};Z&&"x"!==Z||(U.left=W.left-this.offset.parent.left-this.margins.left+(this.offsetParent[0]===document.body?0:this.offsetParent[0].scrollLeft)),Z&&"y"!==Z||(U.top=W.top-this.offset.parent.top-this.margins.top+(this.offsetParent[0]===document.body?0:this.offsetParent[0].scrollTop)),this.reverting=!0,P(this.helper).animate(U,parseInt(this.options.revert,10)||500,function(){V._clear(X)})}else{this._clear(X,Y)}return !1}},cancel:function(){if(this.dragging){this._mouseUp({target:null}),"original"===this.options.helper?this.currentItem.css(this._storedCSS).removeClass("ui-sortable-helper"):this.currentItem.show();for(var U=this.containers.length-1;U>=0;U--){this.containers[U]._trigger("deactivate",null,this._uiHash(this)),this.containers[U].containerCache.over&&(this.containers[U]._trigger("out",null,this._uiHash(this)),this.containers[U].containerCache.over=0)}}return this.placeholder&&(this.placeholder[0].parentNode&&this.placeholder[0].parentNode.removeChild(this.placeholder[0]),"original"!==this.options.helper&&this.helper&&this.helper[0].parentNode&&this.helper.remove(),P.extend(this,{helper:null,dragging:!1,reverting:!1,_noFinalSort:null}),this.domPosition.prev?P(this.domPosition.prev).after(this.currentItem):P(this.domPosition.parent).prepend(this.currentItem)),this},serialize:function(V){var W=this._getItemsAsjQuery(V&&V.connected),U=[];return V=V||{},P(W).each(function(){var X=(P(V.item||this).attr(V.attribute||"id")||"").match(V.expression||/(.+)[\-=_](.+)/);X&&U.push((V.key||X[1]+"[]")+"="+(V.key&&V.expression?X[1]:X[2]))}),!U.length&&V.key&&U.push(V.key+"="),U.join("&")},toArray:function(V){var W=this._getItemsAsjQuery(V&&V.connected),U=[];return V=V||{},W.each(function(){U.push(P(V.item||this).attr(V.attribute||"id")||"")}),U},_intersectsWith:function(s){var q=this.positionAbs.left,r=q+this.helperProportions.width,v=this.positionAbs.top,U=v+this.helperProportions.height,t=s.left,u=t+s.width,X=s.top,Y=X+s.height,V=this.offset.click.top,W=this.offset.click.left,o="x"===this.options.axis||v+V>X&&Y>v+V,p="y"===this.options.axis||q+W>t&&u>q+W,Z=o&&p;return"pointer"===this.options.tolerance||this.options.forcePointerForContainers||"pointer"!==this.options.tolerance&&this.helperProportions[this.floating?"width":"height"]>s[this.floating?"width":"height"]?Z:t<q+this.helperProportions.width/2&&r-this.helperProportions.width/2<u&&X<v+this.helperProportions.height/2&&U-this.helperProportions.height/2<Y},_intersectsWithPointer:function(Y){var W="x"===this.options.axis||this._isOverAxis(this.positionAbs.top+this.offset.click.top,Y.top,Y.height),X="y"===this.options.axis||this._isOverAxis(this.positionAbs.left+this.offset.click.left,Y.left,Y.width),U=W&&X,V=this._getDragVerticalDirection(),Z=this._getDragHorizontalDirection();return U?this.floating?Z&&"right"===Z||"down"===V?2:1:V&&("down"===V?2:1):!1},_intersectsWithSides:function(Y){var W=this._isOverAxis(this.positionAbs.top+this.offset.click.top,Y.top+Y.height/2,Y.height),X=this._isOverAxis(this.positionAbs.left+this.offset.click.left,Y.left+Y.width/2,Y.width),U=this._getDragVerticalDirection(),V=this._getDragHorizontalDirection();return this.floating&&V?"right"===V&&X||"left"===V&&!X:U&&("down"===U&&W||"up"===U&&!W)},_getDragVerticalDirection:function(){var U=this.positionAbs.top-this.lastPositionAbs.top;return 0!==U&&(U>0?"down":"up")},_getDragHorizontalDirection:function(){var U=this.positionAbs.left-this.lastPositionAbs.left;return 0!==U&&(U>0?"right":"left")},refresh:function(U){return this._refreshItems(U),this._setHandleClassName(),this.refreshPositions(),this},_connectWith:function(){var U=this.options;return U.connectWith.constructor===String?[U.connectWith]:U.connectWith},_getItemsAsjQuery:function(Y){function Z(){W.push(this)}var l,U,a,k,W=[],X=[],V=this._connectWith();if(V&&Y){for(l=V.length-1;l>=0;l--){for(a=P(V[l]),U=a.length-1;U>=0;U--){k=P.data(a[U],this.widgetFullName),k&&k!==this&&!k.options.disabled&&X.push([P.isFunction(k.options.items)?k.options.items.call(k.element):P(k.options.items,k.element).not(".ui-sortable-helper").not(".ui-sortable-placeholder"),k])}}}for(X.push([P.isFunction(this.options.items)?this.options.items.call(this.element,null,{options:this.options,item:this.currentItem}):P(this.options.items,this.element).not(".ui-sortable-helper").not(".ui-sortable-placeholder"),this]),l=X.length-1;l>=0;l--){X[l][0].each(Z)}return P(W)},_removeCurrentsFromItems:function(){var U=this.currentItem.find(":data("+this.widgetName+"-item)");this.items=P.grep(this.items,function(W){for(var V=0;V<U.length;V++){if(U[V]===W.item[0]){return !1}}return !0})},_refreshItems:function(n){this.items=[],this.containers=[this];var o,r,U,p,q,X,Y,V,W=this.items,Z=[[P.isFunction(this.options.items)?this.options.items.call(this.element[0],n,{item:this.currentItem}):P(this.options.items,this.element),this]],a=this._connectWith();if(a&&this.ready){for(o=a.length-1;o>=0;o--){for(U=P(a[o]),r=U.length-1;r>=0;r--){p=P.data(U[r],this.widgetFullName),p&&p!==this&&!p.options.disabled&&(Z.push([P.isFunction(p.options.items)?p.options.items.call(p.element[0],n,{item:this.currentItem}):P(p.options.items,p.element),p]),this.containers.push(p))}}}for(o=Z.length-1;o>=0;o--){for(q=Z[o][1],X=Z[o][0],r=0,V=X.length;V>r;r++){Y=P(X[r]),Y.data(this.widgetName+"-item",q),W.push({item:Y,instance:q,width:0,height:0,left:0,top:0})}}},refreshPositions:function(W){this.offsetParent&&this.helper&&(this.offset.parent=this._getParentOffset());var X,U,V,Y;for(X=this.items.length-1;X>=0;X--){U=this.items[X],U.instance!==this.currentContainer&&this.currentContainer&&U.item[0]!==this.currentItem[0]||(V=this.options.toleranceElement?P(this.options.toleranceElement,U.item):U.item,W||(U.width=V.outerWidth(),U.height=V.outerHeight()),Y=V.offset(),U.left=Y.left,U.top=Y.top)}if(this.options.custom&&this.options.custom.refreshContainers){this.options.custom.refreshContainers.call(this)}else{for(X=this.containers.length-1;X>=0;X--){Y=this.containers[X].element.offset(),this.containers[X].containerCache.left=Y.left,this.containers[X].containerCache.top=Y.top,this.containers[X].containerCache.width=this.containers[X].element.outerWidth(),this.containers[X].containerCache.height=this.containers[X].element.outerHeight()}}return this},_createPlaceholder:function(V){V=V||this;var W,U=V.options;U.placeholder&&U.placeholder.constructor!==String||(W=U.placeholder,U.placeholder={element:function(){var X=V.currentItem[0].nodeName.toLowerCase(),Y=P("<"+X+">",V.document[0]).addClass(W||V.currentItem[0].className+" ui-sortable-placeholder").removeClass("ui-sortable-helper");return"tr"===X?V.currentItem.children().each(function(){P("<td>&#160;</td>",V.document[0]).attr("colspan",P(this).attr("colspan")||1).appendTo(Y)}):"img"===X&&Y.attr("src",V.currentItem.attr("src")),W||Y.css("visibility","hidden"),Y},update:function(Y,X){(!W||U.forcePlaceholderSize)&&(X.height()||X.height(V.currentItem.innerHeight()-parseInt(V.currentItem.css("paddingTop")||0,10)-parseInt(V.currentItem.css("paddingBottom")||0,10)),X.width()||X.width(V.currentItem.innerWidth()-parseInt(V.currentItem.css("paddingLeft")||0,10)-parseInt(V.currentItem.css("paddingRight")||0,10)))}}),V.placeholder=P(U.placeholder.element.call(V.element,V.currentItem)),V.currentItem.after(V.placeholder),U.placeholder.update(V,V.placeholder)},_contactContainers:function(p){var q,t,U,r,s,X,Y,V,W,a,o=null,Z=null;for(q=this.containers.length-1;q>=0;q--){if(!P.contains(this.currentItem[0],this.containers[q].element[0])){if(this._intersectsWith(this.containers[q].containerCache)){if(o&&P.contains(this.containers[q].element[0],o.element[0])){continue}o=this.containers[q],Z=q}else{this.containers[q].containerCache.over&&(this.containers[q]._trigger("out",p,this._uiHash(this)),this.containers[q].containerCache.over=0)}}}if(o){if(1===this.containers.length){this.containers[Z].containerCache.over||(this.containers[Z]._trigger("over",p,this._uiHash(this)),this.containers[Z].containerCache.over=1)}else{for(U=10000,r=null,W=o.floating||this._isFloating(this.currentItem),s=W?"left":"top",X=W?"width":"height",a=W?"clientX":"clientY",t=this.items.length-1;t>=0;t--){P.contains(this.containers[Z].element[0],this.items[t].item[0])&&this.items[t].item[0]!==this.currentItem[0]&&(Y=this.items[t].item.offset()[s],V=!1,p[a]-Y>this.items[t][X]/2&&(V=!0),Math.abs(p[a]-Y)<U&&(U=Math.abs(p[a]-Y),r=this.items[t],this.direction=V?"up":"down"))}if(!r&&!this.options.dropOnEmpty){return}if(this.currentContainer===this.containers[Z]){return void (this.currentContainer.containerCache.over||(this.containers[Z]._trigger("over",p,this._uiHash()),this.currentContainer.containerCache.over=1))}r?this._rearrange(p,r,null,!0):this._rearrange(p,null,this.containers[Z].element,!0),this._trigger("change",p,this._uiHash()),this.containers[Z]._trigger("change",p,this._uiHash(this)),this.currentContainer=this.containers[Z],this.options.placeholder.update(this.currentContainer,this.placeholder),this.containers[Z]._trigger("over",p,this._uiHash(this)),this.containers[Z].containerCache.over=1}}},_createHelper:function(V){var W=this.options,U=P.isFunction(W.helper)?P(W.helper.apply(this.element[0],[V,this.currentItem])):"clone"===W.helper?this.currentItem.clone():this.currentItem;return U.parents("body").length||P("parent"!==W.appendTo?W.appendTo:this.currentItem[0].parentNode)[0].appendChild(U[0]),U[0]===this.currentItem[0]&&(this._storedCSS={width:this.currentItem[0].style.width,height:this.currentItem[0].style.height,position:this.currentItem.css("position"),top:this.currentItem.css("top"),left:this.currentItem.css("left")}),(!U[0].style.width||W.forceHelperSize)&&U.width(this.currentItem.width()),(!U[0].style.height||W.forceHelperSize)&&U.height(this.currentItem.height()),U},_adjustOffsetFromHelper:function(U){"string"==typeof U&&(U=U.split(" ")),P.isArray(U)&&(U={left:+U[0],top:+U[1]||0}),"left" in U&&(this.offset.click.left=U.left+this.margins.left),"right" in U&&(this.offset.click.left=this.helperProportions.width-U.right+this.margins.left),"top" in U&&(this.offset.click.top=U.top+this.margins.top),"bottom" in U&&(this.offset.click.top=this.helperProportions.height-U.bottom+this.margins.top)},_getParentOffset:function(){this.offsetParent=this.helper.offsetParent();var U=this.offsetParent.offset();return"absolute"===this.cssPosition&&this.scrollParent[0]!==document&&P.contains(this.scrollParent[0],this.offsetParent[0])&&(U.left+=this.scrollParent.scrollLeft(),U.top+=this.scrollParent.scrollTop()),(this.offsetParent[0]===document.body||this.offsetParent[0].tagName&&"html"===this.offsetParent[0].tagName.toLowerCase()&&P.ui.ie)&&(U={top:0,left:0}),{top:U.top+(parseInt(this.offsetParent.css("borderTopWidth"),10)||0),left:U.left+(parseInt(this.offsetParent.css("borderLeftWidth"),10)||0)}},_getRelativeOffset:function(){if("relative"===this.cssPosition){var U=this.currentItem.position();return{top:U.top-(parseInt(this.helper.css("top"),10)||0)+this.scrollParent.scrollTop(),left:U.left-(parseInt(this.helper.css("left"),10)||0)+this.scrollParent.scrollLeft()}}return{top:0,left:0}},_cacheMargins:function(){this.margins={left:parseInt(this.currentItem.css("marginLeft"),10)||0,top:parseInt(this.currentItem.css("marginTop"),10)||0}},_cacheHelperProportions:function(){this.helperProportions={width:this.helper.outerWidth(),height:this.helper.outerHeight()}},_setContainment:function(){var W,X,U,V=this.options;"parent"===V.containment&&(V.containment=this.helper[0].parentNode),("document"===V.containment||"window"===V.containment)&&(this.containment=[0-this.offset.relative.left-this.offset.parent.left,0-this.offset.relative.top-this.offset.parent.top,P("document"===V.containment?document:window).width()-this.helperProportions.width-this.margins.left,(P("document"===V.containment?document:window).height()||document.body.parentNode.scrollHeight)-this.helperProportions.height-this.margins.top]),/^(document|window|parent)$/.test(V.containment)||(W=P(V.containment)[0],X=P(V.containment).offset(),U="hidden"!==P(W).css("overflow"),this.containment=[X.left+(parseInt(P(W).css("borderLeftWidth"),10)||0)+(parseInt(P(W).css("paddingLeft"),10)||0)-this.margins.left,X.top+(parseInt(P(W).css("borderTopWidth"),10)||0)+(parseInt(P(W).css("paddingTop"),10)||0)-this.margins.top,X.left+(U?Math.max(W.scrollWidth,W.offsetWidth):W.offsetWidth)-(parseInt(P(W).css("borderLeftWidth"),10)||0)-(parseInt(P(W).css("paddingRight"),10)||0)-this.helperProportions.width-this.margins.left,X.top+(U?Math.max(W.scrollHeight,W.offsetHeight):W.offsetHeight)-(parseInt(P(W).css("borderTopWidth"),10)||0)-(parseInt(P(W).css("paddingBottom"),10)||0)-this.helperProportions.height-this.margins.top])},_convertPositionTo:function(W,X){X||(X=this.position);var U="absolute"===W?1:-1,V="absolute"!==this.cssPosition||this.scrollParent[0]!==document&&P.contains(this.scrollParent[0],this.offsetParent[0])?this.scrollParent:this.offsetParent,Y=/(html|body)/i.test(V[0].tagName);return{top:X.top+this.offset.relative.top*U+this.offset.parent.top*U-("fixed"===this.cssPosition?-this.scrollParent.scrollTop():Y?0:V.scrollTop())*U,left:X.left+this.offset.relative.left*U+this.offset.parent.left*U-("fixed"===this.cssPosition?-this.scrollParent.scrollLeft():Y?0:V.scrollLeft())*U}},_generatePosition:function(X){var Y,j,U=this.options,Z=X.pageX,a=X.pageY,V="absolute"!==this.cssPosition||this.scrollParent[0]!==document&&P.contains(this.scrollParent[0],this.offsetParent[0])?this.scrollParent:this.offsetParent,W=/(html|body)/i.test(V[0].tagName);return"relative"!==this.cssPosition||this.scrollParent[0]!==document&&this.scrollParent[0]!==this.offsetParent[0]||(this.offset.relative=this._getRelativeOffset()),this.originalPosition&&(this.containment&&(X.pageX-this.offset.click.left<this.containment[0]&&(Z=this.containment[0]+this.offset.click.left),X.pageY-this.offset.click.top<this.containment[1]&&(a=this.containment[1]+this.offset.click.top),X.pageX-this.offset.click.left>this.containment[2]&&(Z=this.containment[2]+this.offset.click.left),X.pageY-this.offset.click.top>this.containment[3]&&(a=this.containment[3]+this.offset.click.top)),U.grid&&(Y=this.originalPageY+Math.round((a-this.originalPageY)/U.grid[1])*U.grid[1],a=this.containment?Y-this.offset.click.top>=this.containment[1]&&Y-this.offset.click.top<=this.containment[3]?Y:Y-this.offset.click.top>=this.containment[1]?Y-U.grid[1]:Y+U.grid[1]:Y,j=this.originalPageX+Math.round((Z-this.originalPageX)/U.grid[0])*U.grid[0],Z=this.containment?j-this.offset.click.left>=this.containment[0]&&j-this.offset.click.left<=this.containment[2]?j:j-this.offset.click.left>=this.containment[0]?j-U.grid[0]:j+U.grid[0]:j)),{top:a-this.offset.click.top-this.offset.relative.top-this.offset.parent.top+("fixed"===this.cssPosition?-this.scrollParent.scrollTop():W?0:V.scrollTop()),left:Z-this.offset.click.left-this.offset.relative.left-this.offset.parent.left+("fixed"===this.cssPosition?-this.scrollParent.scrollLeft():W?0:V.scrollLeft())}},_rearrange:function(Y,W,X,U){X?X[0].appendChild(this.placeholder[0]):W.item[0].parentNode.insertBefore(this.placeholder[0],"down"===this.direction?W.item[0]:W.item[0].nextSibling),this.counter=this.counter?++this.counter:1;var V=this.counter;this._delay(function(){V===this.counter&&this.refreshPositions(!U)})},_clear:function(Y,W){function X(e,Z,d){return function(a){d._trigger(e,a,Z._uiHash(Z))}}this.reverting=!1;var U,V=[];if(!this._noFinalSort&&this.currentItem.parent().length&&this.placeholder.before(this.currentItem),this._noFinalSort=null,this.helper[0]===this.currentItem[0]){for(U in this._storedCSS){("auto"===this._storedCSS[U]||"static"===this._storedCSS[U])&&(this._storedCSS[U]="")}this.currentItem.css(this._storedCSS).removeClass("ui-sortable-helper")}else{this.currentItem.show()}for(this.fromOutside&&!W&&V.push(function(Z){this._trigger("receive",Z,this._uiHash(this.fromOutside))}),!this.fromOutside&&this.domPosition.prev===this.currentItem.prev().not(".ui-sortable-helper")[0]&&this.domPosition.parent===this.currentItem.parent()[0]||W||V.push(function(Z){this._trigger("update",Z,this._uiHash())}),this!==this.currentContainer&&(W||(V.push(function(Z){this._trigger("remove",Z,this._uiHash())}),V.push(function(Z){return function(a){Z._trigger("receive",a,this._uiHash(this))}}.call(this,this.currentContainer)),V.push(function(Z){return function(a){Z._trigger("update",a,this._uiHash(this))}}.call(this,this.currentContainer)))),U=this.containers.length-1;U>=0;U--){W||V.push(X("deactivate",this,this.containers[U])),this.containers[U].containerCache.over&&(V.push(X("out",this,this.containers[U])),this.containers[U].containerCache.over=0)}if(this.storedCursor&&(this.document.find("body").css("cursor",this.storedCursor),this.storedStylesheet.remove()),this._storedOpacity&&this.helper.css("opacity",this._storedOpacity),this._storedZIndex&&this.helper.css("zIndex","auto"===this._storedZIndex?"":this._storedZIndex),this.dragging=!1,W||this._trigger("beforeStop",Y,this._uiHash()),this.placeholder[0].parentNode.removeChild(this.placeholder[0]),this.cancelHelperRemoval||(this.helper[0]!==this.currentItem[0]&&this.helper.remove(),this.helper=null),!W){for(U=0;U<V.length;U++){V[U].call(this,Y)}this._trigger("stop",Y,this._uiHash())}return this.fromOutside=!1,!this.cancelHelperRemoval},_trigger:function(){P.Widget.prototype._trigger.apply(this,arguments)===!1&&this.cancel()},_uiHash:function(U){var V=U||this;return{helper:V.helper,placeholder:V.placeholder||P([]),position:V.position,originalPosition:V.originalPosition,offset:V.positionAbs,item:V.currentItem,sender:U?U.element:null}}}),P.widget("ui.accordion",{version:"1.11.2",options:{active:0,animate:{},collapsible:!1,event:"click",header:"> li > :first-child,> :not(li):even",heightStyle:"auto",icons:{activeHeader:"ui-icon-triangle-1-s",header:"ui-icon-triangle-1-e"},activate:null,beforeActivate:null},hideProps:{borderTopWidth:"hide",borderBottomWidth:"hide",paddingTop:"hide",paddingBottom:"hide",height:"hide"},showProps:{borderTopWidth:"show",borderBottomWidth:"show",paddingTop:"show",paddingBottom:"show",height:"show"},_create:function(){var U=this.options;this.prevShow=this.prevHide=P(),this.element.addClass("ui-accordion ui-widget ui-helper-reset").attr("role","tablist"),U.collapsible||U.active!==!1&&null!=U.active||(U.active=0),this._processPanels(),U.active<0&&(U.active+=this.headers.length),this._refresh()},_getCreateEventData:function(){return{header:this.active,panel:this.active.length?this.active.next():P()}},_createIcons:function(){var U=this.options.icons;U&&(P("<span>").addClass("ui-accordion-header-icon ui-icon "+U.header).prependTo(this.headers),this.active.children(".ui-accordion-header-icon").removeClass(U.header).addClass(U.activeHeader),this.headers.addClass("ui-accordion-icons"))},_destroyIcons:function(){this.headers.removeClass("ui-accordion-icons").children(".ui-accordion-header-icon").remove()},_destroy:function(){var U;this.element.removeClass("ui-accordion ui-widget ui-helper-reset").removeAttr("role"),this.headers.removeClass("ui-accordion-header ui-accordion-header-active ui-state-default ui-corner-all ui-state-active ui-state-disabled ui-corner-top").removeAttr("role").removeAttr("aria-expanded").removeAttr("aria-selected").removeAttr("aria-controls").removeAttr("tabIndex").removeUniqueId(),this._destroyIcons(),U=this.headers.next().removeClass("ui-helper-reset ui-widget-content ui-corner-bottom ui-accordion-content ui-accordion-content-active ui-state-disabled").css("display","").removeAttr("role").removeAttr("aria-hidden").removeAttr("aria-labelledby").removeUniqueId(),"content"!==this.options.heightStyle&&U.css("height","")},_setOption:function(V,U){return"active"===V?void this._activate(U):("event"===V&&(this.options.event&&this._off(this.headers,this.options.event),this._setupEvents(U)),this._super(V,U),"collapsible"!==V||U||this.options.active!==!1||this._activate(0),"icons"===V&&(this._destroyIcons(),U&&this._createIcons()),void ("disabled"===V&&(this.element.toggleClass("ui-state-disabled",!!U).attr("aria-disabled",U),this.headers.add(this.headers.next()).toggleClass("ui-state-disabled",!!U))))},_keydown:function(W){if(!W.altKey&&!W.ctrlKey){var X=P.ui.keyCode,U=this.headers.length,V=this.headers.index(W.target),Y=!1;switch(W.keyCode){case X.RIGHT:case X.DOWN:Y=this.headers[(V+1)%U];break;case X.LEFT:case X.UP:Y=this.headers[(V-1+U)%U];break;case X.SPACE:case X.ENTER:this._eventHandler(W);break;case X.HOME:Y=this.headers[0];break;case X.END:Y=this.headers[U-1]}Y&&(P(W.target).attr("tabIndex",-1),P(Y).attr("tabIndex",0),Y.focus(),W.preventDefault())}},_panelKeyDown:function(U){U.keyCode===P.ui.keyCode.UP&&U.ctrlKey&&P(U.currentTarget).prev().focus()},refresh:function(){var U=this.options;this._processPanels(),U.active===!1&&U.collapsible===!0||!this.headers.length?(U.active=!1,this.active=P()):U.active===!1?this._activate(0):this.active.length&&!P.contains(this.element[0],this.active[0])?this.headers.length===this.headers.find(".ui-state-disabled").length?(U.active=!1,this.active=P()):this._activate(Math.max(0,U.active-1)):U.active=this.headers.index(this.active),this._destroyIcons(),this._refresh()},_processPanels:function(){var V=this.headers,U=this.panels;this.headers=this.element.find(this.options.header).addClass("ui-accordion-header ui-state-default ui-corner-all"),this.panels=this.headers.next().addClass("ui-accordion-content ui-helper-reset ui-widget-content ui-corner-bottom").filter(":not(.ui-accordion-content-active)").hide(),U&&(this._off(V.not(this.headers)),this._off(U.not(this.panels)))},_refresh:function(){var W,X=this.options,U=X.heightStyle,V=this.element.parent();this.active=this._findActive(X.active).addClass("ui-accordion-header-active ui-state-active ui-corner-top").removeClass("ui-corner-all"),this.active.next().addClass("ui-accordion-content-active").show(),this.headers.attr("role","tab").each(function(){var a=P(this),f=a.uniqueId().attr("id"),Y=a.next(),Z=Y.uniqueId().attr("id");a.attr("aria-controls",Z),Y.attr("aria-labelledby",f)}).next().attr("role","tabpanel"),this.headers.not(this.active).attr({"aria-selected":"false","aria-expanded":"false",tabIndex:-1}).next().attr({"aria-hidden":"true"}).hide(),this.active.length?this.active.attr({"aria-selected":"true","aria-expanded":"true",tabIndex:0}).next().attr({"aria-hidden":"false"}):this.headers.eq(0).attr("tabIndex",0),this._createIcons(),this._setupEvents(X.event),"fill"===U?(W=V.height(),this.element.siblings(":visible").each(function(){var Z=P(this),Y=Z.css("position");"absolute"!==Y&&"fixed"!==Y&&(W-=Z.outerHeight(!0))}),this.headers.each(function(){W-=P(this).outerHeight(!0)}),this.headers.next().each(function(){P(this).height(Math.max(0,W-P(this).innerHeight()+P(this).height()))}).css("overflow","auto")):"auto"===U&&(W=0,this.headers.next().each(function(){W=Math.max(W,P(this).css("height","").height())}).height(W))},_activate:function(U){var V=this._findActive(U)[0];V!==this.active[0]&&(V=V||this.active[0],this._eventHandler({target:V,currentTarget:V,preventDefault:P.noop}))},_findActive:function(U){return"number"==typeof U?this.headers.eq(U):P()},_setupEvents:function(U){var V={keydown:"_keydown"};U&&P.each(U.split(" "),function(X,W){V[W]="_eventHandler"}),this._off(this.headers.add(this.headers.next())),this._on(this.headers,V),this._on(this.headers.next(),{keydown:"_panelKeyDown"}),this._hoverable(this.headers),this._focusable(this.headers)},_eventHandler:function(Y){var Z=this.options,l=this.active,U=P(Y.currentTarget),a=U[0]===l[0],k=a&&Z.collapsible,W=k?P():U.next(),X=l.next(),V={oldHeader:l,oldPanel:X,newHeader:k?P():U,newPanel:W};Y.preventDefault(),a&&!Z.collapsible||this._trigger("beforeActivate",Y,V)===!1||(Z.active=k?!1:this.headers.index(U),this.active=a?P():U,this._toggle(V),l.removeClass("ui-accordion-header-active ui-state-active"),Z.icons&&l.children(".ui-accordion-header-icon").removeClass(Z.icons.activeHeader).addClass(Z.icons.header),a||(U.removeClass("ui-corner-all").addClass("ui-accordion-header-active ui-state-active ui-corner-top"),Z.icons&&U.children(".ui-accordion-header-icon").removeClass(Z.icons.header).addClass(Z.icons.activeHeader),U.next().addClass("ui-accordion-content-active")))},_toggle:function(V){var W=V.newPanel,U=this.prevShow.length?this.prevShow:V.oldPanel;this.prevShow.add(this.prevHide).stop(!0,!0),this.prevShow=W,this.prevHide=U,this.options.animate?this._animate(W,U,V):(U.hide(),W.show(),this._toggleComplete(V)),U.attr({"aria-hidden":"true"}),U.prev().attr("aria-selected","false"),W.length&&U.length?U.prev().attr({tabIndex:-1,"aria-expanded":"false"}):W.length&&this.headers.filter(function(){return 0===P(this).attr("tabIndex")}).attr("tabIndex",-1),W.attr("aria-hidden","false").prev().attr({"aria-selected":"true",tabIndex:0,"aria-expanded":"true"})},_animate:function(o,m,n){var r,U,p,q=this,X=0,Y=o.length&&(!m.length||o.index()<m.index()),V=this.options.animate||{},W=Y&&V.down||V,Z=function(){q._toggleComplete(n)};return"number"==typeof W&&(p=W),"string"==typeof W&&(U=W),U=U||W.easing||V.easing,p=p||W.duration||V.duration,m.length?o.length?(r=o.show().outerHeight(),m.animate(this.hideProps,{duration:p,easing:U,step:function(d,c){c.now=Math.round(d)}}),void o.hide().animate(this.showProps,{duration:p,easing:U,complete:Z,step:function(d,b){b.now=Math.round(d),"height"!==b.prop?X+=b.now:"content"!==q.options.heightStyle&&(b.now=Math.round(r-m.outerHeight()-X),X=0)}})):m.animate(this.hideProps,p,U,Z):o.animate(this.showProps,p,U,Z)},_toggleComplete:function(V){var U=V.oldPanel;U.removeClass("ui-accordion-content-active").prev().removeClass("ui-corner-top").addClass("ui-corner-all"),U.length&&(U.parent()[0].className=U.parent()[0].className),this._trigger("activate",null,V)}}),P.widget("ui.menu",{version:"1.11.2",defaultElement:"<ul>",delay:300,options:{icons:{submenu:"ui-icon-carat-1-e"},items:"> *",menus:"ul",position:{my:"left-1 top",at:"right top"},role:"menu",blur:null,focus:null,select:null},_create:function(){this.activeMenu=this.element,this.mouseHandled=!1,this.element.uniqueId().addClass("ui-menu ui-widget ui-widget-content").toggleClass("ui-menu-icons",!!this.element.find(".ui-icon").length).attr({role:this.options.role,tabIndex:0}),this.options.disabled&&this.element.addClass("ui-state-disabled").attr("aria-disabled","true"),this._on({"mousedown .ui-menu-item":function(U){U.preventDefault()},"click .ui-menu-item":function(U){var V=P(U.target);!this.mouseHandled&&V.not(".ui-state-disabled").length&&(this.select(U),U.isPropagationStopped()||(this.mouseHandled=!0),V.has(".ui-menu").length?this.expand(U):!this.element.is(":focus")&&P(this.document[0].activeElement).closest(".ui-menu").length&&(this.element.trigger("focus",[!0]),this.active&&1===this.active.parents(".ui-menu").length&&clearTimeout(this.timer)))},"mouseenter .ui-menu-item":function(U){if(!this.previousFilter){var V=P(U.currentTarget);V.siblings(".ui-state-active").removeClass("ui-state-active"),this.focus(U,V)}},mouseleave:"collapseAll","mouseleave .ui-menu":"collapseAll",focus:function(W,U){var V=this.active||this.element.find(this.options.items).eq(0);U||this.focus(W,V)},blur:function(U){this._delay(function(){P.contains(this.element[0],this.document[0].activeElement)||this.collapseAll(U)})},keydown:"_keydown"}),this.refresh(),this._on(this.document,{click:function(U){this._closeOnDocumentClick(U)&&this.collapseAll(U),this.mouseHandled=!1}})},_destroy:function(){this.element.removeAttr("aria-activedescendant").find(".ui-menu").addBack().removeClass("ui-menu ui-widget ui-widget-content ui-menu-icons ui-front").removeAttr("role").removeAttr("tabIndex").removeAttr("aria-labelledby").removeAttr("aria-expanded").removeAttr("aria-hidden").removeAttr("aria-disabled").removeUniqueId().show(),this.element.find(".ui-menu-item").removeClass("ui-menu-item").removeAttr("role").removeAttr("aria-disabled").removeUniqueId().removeClass("ui-state-hover").removeAttr("tabIndex").removeAttr("role").removeAttr("aria-haspopup").children().each(function(){var U=P(this);U.data("ui-menu-submenu-carat")&&U.remove()}),this.element.find(".ui-menu-divider").removeClass("ui-menu-divider ui-widget-content")},_keydown:function(X){var Y,V,W,Z,U=!0;switch(X.keyCode){case P.ui.keyCode.PAGE_UP:this.previousPage(X);break;case P.ui.keyCode.PAGE_DOWN:this.nextPage(X);break;case P.ui.keyCode.HOME:this._move("first","first",X);break;case P.ui.keyCode.END:this._move("last","last",X);break;case P.ui.keyCode.UP:this.previous(X);break;case P.ui.keyCode.DOWN:this.next(X);break;case P.ui.keyCode.LEFT:this.collapse(X);break;case P.ui.keyCode.RIGHT:this.active&&!this.active.is(".ui-state-disabled")&&this.expand(X);break;case P.ui.keyCode.ENTER:case P.ui.keyCode.SPACE:this._activate(X);break;case P.ui.keyCode.ESCAPE:this.collapse(X);break;default:U=!1,V=this.previousFilter||"",W=String.fromCharCode(X.keyCode),Z=!1,clearTimeout(this.filterTimer),W===V?Z=!0:W=V+W,Y=this._filterMenuItems(W),Y=Z&&-1!==Y.index(this.active.next())?this.active.nextAll(".ui-menu-item"):Y,Y.length||(W=String.fromCharCode(X.keyCode),Y=this._filterMenuItems(W)),Y.length?(this.focus(X,Y),this.previousFilter=W,this.filterTimer=this._delay(function(){delete this.previousFilter},1000)):delete this.previousFilter}U&&X.preventDefault()},_activate:function(U){this.active.is(".ui-state-disabled")||(this.active.is("[aria-haspopup='true']")?this.expand(U):this.select(U))},refresh:function(){var W,X,U=this,V=this.options.icons.submenu,Y=this.element.find(this.options.menus);this.element.toggleClass("ui-menu-icons",!!this.element.find(".ui-icon").length),Y.filter(":not(.ui-menu)").addClass("ui-menu ui-widget ui-widget-content ui-front").hide().attr({role:this.options.role,"aria-hidden":"true","aria-expanded":"false"}).each(function(){var a=P(this),e=a.parent(),Z=P("<span>").addClass("ui-menu-icon ui-icon "+V).data("ui-menu-submenu-carat",!0);e.attr("aria-haspopup","true").prepend(Z),a.attr("aria-labelledby",e.attr("id"))}),W=Y.add(this.element),X=W.find(this.options.items),X.not(".ui-menu-item").each(function(){var Z=P(this);U._isDivider(Z)&&Z.addClass("ui-widget-content ui-menu-divider")}),X.not(".ui-menu-item, .ui-menu-divider").addClass("ui-menu-item").uniqueId().attr({tabIndex:-1,role:this._itemRole()}),X.filter(".ui-state-disabled").attr("aria-disabled","true"),this.active&&!P.contains(this.element[0],this.active[0])&&this.blur()},_itemRole:function(){return{menu:"menuitem",listbox:"option"}[this.options.role]},_setOption:function(V,U){"icons"===V&&this.element.find(".ui-menu-icon").removeClass(this.options.icons.submenu).addClass(U.submenu),"disabled"===V&&this.element.toggleClass("ui-state-disabled",!!U).attr("aria-disabled",U),this._super(V,U)},focus:function(X,V){var W,U;this.blur(X,X&&"focus"===X.type),this._scrollIntoView(V),this.active=V.first(),U=this.active.addClass("ui-state-focus").removeClass("ui-state-active"),this.options.role&&this.element.attr("aria-activedescendant",U.attr("id")),this.active.parent().closest(".ui-menu-item").addClass("ui-state-active"),X&&"keydown"===X.type?this._close():this.timer=this._delay(function(){this._close()},this.delay),W=V.children(".ui-menu"),W.length&&X&&/^mouse/.test(X.type)&&this._startOpening(W),this.activeMenu=V.parent(),this._trigger("focus",X,{item:V})},_scrollIntoView:function(Y){var Z,V,W,a,U,X;this._hasScroll()&&(Z=parseFloat(P.css(this.activeMenu[0],"borderTopWidth"))||0,V=parseFloat(P.css(this.activeMenu[0],"paddingTop"))||0,W=Y.offset().top-this.activeMenu.offset().top-Z-V,a=this.activeMenu.scrollTop(),U=this.activeMenu.height(),X=Y.outerHeight(),0>W?this.activeMenu.scrollTop(a+W):W+X>U&&this.activeMenu.scrollTop(a+W-U+X))},blur:function(V,U){U||clearTimeout(this.timer),this.active&&(this.active.removeClass("ui-state-focus"),this.active=null,this._trigger("blur",V,{item:this.active}))},_startOpening:function(U){clearTimeout(this.timer),"true"===U.attr("aria-hidden")&&(this.timer=this._delay(function(){this._close(),this._open(U)},this.delay))},_open:function(U){var V=P.extend({of:this.active},this.options.position);clearTimeout(this.timer),this.element.find(".ui-menu").not(U.parents(".ui-menu")).hide().attr("aria-hidden","true"),U.show().removeAttr("aria-hidden").attr("aria-expanded","true").position(V)},collapseAll:function(U,V){clearTimeout(this.timer),this.timer=this._delay(function(){var W=V?this.element:P(U&&U.target).closest(this.element.find(".ui-menu"));W.length||(W=this.element),this._close(W),this.blur(U),this.activeMenu=W},this.delay)},_close:function(U){U||(U=this.active?this.active.parent():this.element),U.find(".ui-menu").hide().attr("aria-hidden","true").attr("aria-expanded","false").end().find(".ui-state-active").not(".ui-state-focus").removeClass("ui-state-active")},_closeOnDocumentClick:function(U){return !P(U.target).closest(".ui-menu").length},_isDivider:function(U){return !/[^\-\u2014\u2013\s]/.test(U.text())},collapse:function(V){var U=this.active&&this.active.parent().closest(".ui-menu-item",this.element);U&&U.length&&(this._close(),this.focus(V,U))},expand:function(V){var U=this.active&&this.active.children(".ui-menu ").find(this.options.items).first();U&&U.length&&(this._open(U.parent()),this._delay(function(){this.focus(V,U)}))},next:function(U){this._move("next","first",U)},previous:function(U){this._move("prev","last",U)},isFirstItem:function(){return this.active&&!this.active.prevAll(".ui-menu-item").length},isLastItem:function(){return this.active&&!this.active.nextAll(".ui-menu-item").length},_move:function(X,V,W){var U;this.active&&(U="first"===X||"last"===X?this.active["first"===X?"prevAll":"nextAll"](".ui-menu-item").eq(-1):this.active[X+"All"](".ui-menu-item").eq(0)),U&&U.length&&this.active||(U=this.activeMenu.find(this.options.items)[V]()),this.focus(W,U)},nextPage:function(W){var X,U,V;return this.active?void (this.isLastItem()||(this._hasScroll()?(U=this.active.offset().top,V=this.element.height(),this.active.nextAll(".ui-menu-item").each(function(){return X=P(this),X.offset().top-U-V<0}),this.focus(W,X)):this.focus(W,this.activeMenu.find(this.options.items)[this.active?"last":"first"]()))):void this.next(W)},previousPage:function(W){var X,U,V;return this.active?void (this.isFirstItem()||(this._hasScroll()?(U=this.active.offset().top,V=this.element.height(),this.active.prevAll(".ui-menu-item").each(function(){return X=P(this),X.offset().top-U+V>0}),this.focus(W,X)):this.focus(W,this.activeMenu.find(this.options.items).first()))):void this.next(W)},_hasScroll:function(){return this.element.outerHeight()<this.element.prop("scrollHeight")},select:function(U){this.active=this.active||P(U.target).closest(".ui-menu-item");var V={item:this.active};this.active.has(".ui-menu").length||this.collapseAll(U,!0),this._trigger("select",U,V)},_filterMenuItems:function(V){var W=V.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g,"\\$&"),U=new RegExp("^"+W,"i");return this.activeMenu.find(this.options.items).filter(".ui-menu-item").filter(function(){return U.test(P.trim(P(this).text()))})}});
/*
 * jQuery UI Autocomplete 1.11.2
 * http://jqueryui.com
 *
 * Copyright 2014 jQuery Foundation and other contributors
 * Released under the MIT license.
 * http://jquery.org/license
 *
 * http://api.jqueryui.com/autocomplete/
 */
P.widget("ui.autocomplete",{version:"1.11.2",defaultElement:"<input>",options:{appendTo:null,autoFocus:!1,delay:300,minLength:1,position:{my:"left top",at:"left bottom",collision:"none"},source:null,change:null,close:null,focus:null,open:null,response:null,search:null,select:null},requestIndex:0,pending:0,_create:function(){var X,Y,V,W=this.element[0].nodeName.toLowerCase(),Z="textarea"===W,U="input"===W;this.isMultiLine=Z?!0:U?!1:this.element.prop("isContentEditable"),this.valueMethod=this.element[Z||U?"val":"text"],this.isNewMenu=!0,this.element.addClass("ui-autocomplete-input").attr("autocomplete","off"),this._on(this.element,{keydown:function(a){if(this.element.prop("readOnly")){return X=!0,V=!0,void (Y=!0)}X=!1,V=!1,Y=!1;var b=P.ui.keyCode;switch(a.keyCode){case b.PAGE_UP:X=!0,this._move("previousPage",a);break;case b.PAGE_DOWN:X=!0,this._move("nextPage",a);break;case b.UP:X=!0,this._keyEvent("previous",a);break;case b.DOWN:X=!0,this._keyEvent("next",a);break;case b.ENTER:this.menu.active&&(X=!0,a.preventDefault(),this.menu.select(a));break;case b.TAB:this.menu.active&&this.menu.select(a);break;case b.ESCAPE:this.menu.element.is(":visible")&&(this.isMultiLine||this._value(this.term),this.close(a),a.preventDefault());break;default:Y=!0,this._searchTimeout(a)}},keypress:function(a){if(X){return X=!1,void ((!this.isMultiLine||this.menu.element.is(":visible"))&&a.preventDefault())}if(!Y){var b=P.ui.keyCode;switch(a.keyCode){case b.PAGE_UP:this._move("previousPage",a);break;case b.PAGE_DOWN:this._move("nextPage",a);break;case b.UP:this._keyEvent("previous",a);break;case b.DOWN:this._keyEvent("next",a)}}},input:function(b){return V?(V=!1,void b.preventDefault()):void this._searchTimeout(b)},focus:function(){this.selectedItem=null,this.previous=this._value()},blur:function(b){return this.cancelBlur?void delete this.cancelBlur:(clearTimeout(this.searching),this.close(b),void this._change(b))}}),this._initSource(),this.menu=P("<ul>").addClass("ui-autocomplete ui-front").appendTo(this._appendTo()).menu({role:null}).hide().menu("instance"),this._on(this.menu.element,{mousedown:function(a){a.preventDefault(),this.cancelBlur=!0,this._delay(function(){delete this.cancelBlur});var d=this.menu.element[0];P(a.target).closest(".ui-menu-item").length||this._delay(function(){var c=this;this.document.one("mousedown",function(b){b.target===c.element[0]||b.target===d||P.contains(d,b.target)||c.close()})})},menufocus:function(g,h){var a,f;return this.isNewMenu&&(this.isNewMenu=!1,g.originalEvent&&/^mouse/.test(g.originalEvent.type))?(this.menu.blur(),void this.document.one("mousemove",function(){P(g.target).trigger(g.originalEvent)})):(f=h.item.data("ui-autocomplete-item"),!1!==this._trigger("focus",g,{item:f})&&g.originalEvent&&/^key/.test(g.originalEvent.type)&&this._value(f.value),a=h.item.attr("aria-label")||f.value,void (a&&P.trim(a).length&&(this.liveRegion.children().hide(),P("<div>").text(a).appendTo(this.liveRegion))))},menuselect:function(h,f){var g=f.item.data("ui-autocomplete-item"),e=this.previous;this.element[0]!==this.document[0].activeElement&&(this.element.focus(),this.previous=e,this._delay(function(){this.previous=e,this.selectedItem=g})),!1!==this._trigger("select",h,{item:g})&&this._value(g.value),this.term=this._value(),this.close(h),this.selectedItem=g}}),this.liveRegion=P("<span>",{role:"status","aria-live":"assertive","aria-relevant":"additions"}).addClass("ui-helper-hidden-accessible").appendTo(this.document[0].body),this._on(this.window,{beforeunload:function(){this.element.removeAttr("autocomplete")}})},_destroy:function(){clearTimeout(this.searching),this.element.removeClass("ui-autocomplete-input").removeAttr("autocomplete"),this.menu.element.remove(),this.liveRegion.remove()},_setOption:function(V,U){this._super(V,U),"source"===V&&this._initSource(),"appendTo"===V&&this.menu.element.appendTo(this._appendTo()),"disabled"===V&&U&&this.xhr&&this.xhr.abort()},_appendTo:function(){var U=this.options.appendTo;return U&&(U=U.jquery||U.nodeType?P(U):this.document.find(U).eq(0)),U&&U[0]||(U=this.element.closest(".ui-front")),U.length||(U=this.document[0].body),U},_initSource:function(){var V,W,U=this;P.isArray(this.options.source)?(V=this.options.source,this.source=function(Y,X){X(P.ui.autocomplete.filter(V,Y.term))}):"string"==typeof this.options.source?(W=this.options.source,this.source=function(Y,X){U.xhr&&U.xhr.abort(),U.xhr=P.ajax({url:W,data:Y,dataType:"json",success:function(Z){X(Z)},error:function(){X([])}})}):this.source=this.options.source},_searchTimeout:function(U){clearTimeout(this.searching),this.searching=this._delay(function(){var W=this.term===this._value(),X=this.menu.element.is(":visible"),V=U.altKey||U.ctrlKey||U.metaKey||U.shiftKey;(!W||W&&!X&&!V)&&(this.selectedItem=null,this.search(null,U))},this.options.delay)},search:function(V,U){return V=null!=V?V:this._value(),this.term=this._value(),V.length<this.options.minLength?this.close(U):this._trigger("search",U)!==!1?this._search(V):void 0},_search:function(U){this.pending++,this.element.addClass("ui-autocomplete-loading"),this.cancelSearch=!1,this.source({term:U},this._response())},_response:function(){var U=++this.requestIndex;return P.proxy(function(V){U===this.requestIndex&&this.__response(V),this.pending--,this.pending||this.element.removeClass("ui-autocomplete-loading")},this)},__response:function(U){U&&(U=this._normalize(U)),this._trigger("response",null,{content:U}),!this.options.disabled&&U&&U.length&&!this.cancelSearch?(this._suggest(U),this._trigger("open")):this._close()},close:function(U){this.cancelSearch=!0,this._close(U)},_close:function(U){this.menu.element.is(":visible")&&(this.menu.element.hide(),this.menu.blur(),this.isNewMenu=!0,this._trigger("close",U))},_change:function(U){this.previous!==this._value()&&this._trigger("change",U,{item:this.selectedItem})},_normalize:function(U){return U.length&&U[0].label&&U[0].value?U:P.map(U,function(V){return"string"==typeof V?{label:V,value:V}:P.extend({},V,{label:V.label||V.value,value:V.value||V.label})})},_suggest:function(U){var V=this.menu.element.empty();this._renderMenu(V,U),this.isNewMenu=!0,this.menu.refresh(),V.show(),this._resizeMenu(),V.position(P.extend({of:this.element},this.options.position)),this.options.autoFocus&&this.menu.next()},_resizeMenu:function(){var U=this.menu.element;U.outerWidth(Math.max(U.width("").outerWidth()+1,this.element.outerWidth()))},_renderMenu:function(V,W){var U=this;P.each(W,function(Y,X){U._renderItemData(V,X)})},_renderItemData:function(V,U){return this._renderItem(V,U).data("ui-autocomplete-item",U)},_renderItem:function(U,V){return P("<li>").text(V.label).appendTo(U)},_move:function(V,U){return this.menu.element.is(":visible")?this.menu.isFirstItem()&&/^previous/.test(V)||this.menu.isLastItem()&&/^next/.test(V)?(this.isMultiLine||this._value(this.term),void this.menu.blur()):void this.menu[V](U):void this.search(null,U)},widget:function(){return this.menu.element},_value:function(){return this.valueMethod.apply(this.element,arguments)},_keyEvent:function(V,U){(!this.isMultiLine||this.menu.element.is(":visible"))&&(this._move(V,U),U.preventDefault())}}),P.extend(P.ui.autocomplete,{escapeRegex:function(U){return U.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g,"\\$&")},filter:function(V,W){var U=new RegExp(P.ui.autocomplete.escapeRegex(W),"i");return P.grep(V,function(X){return U.test(X.label||X.value||X)})}}),P.widget("ui.autocomplete",P.ui.autocomplete,{options:{messages:{noResults:"No search results.",results:function(U){return U+(U>1?" results are":" result is")+" available, use up and down arrow keys to navigate."}}},__response:function(U){var V;this._superApply(arguments),this.options.disabled||this.cancelSearch||(V=U&&U.length?this.options.messages.results(U.length):this.options.messages.noResults,this.liveRegion.children().hide(),P("<div>").text(V).appendTo(this.liveRegion))}});var M,J=(P.ui.autocomplete,"ui-button ui-widget ui-state-default ui-corner-all"),K="ui-button-icons-only ui-button-icon-only ui-button-text-icons ui-button-text-icon-primary ui-button-text-icon-secondary ui-button-text-only",C=function(){var U=P(this);setTimeout(function(){U.find(":ui-button").button("refresh")},1)},D=function(W){var X=W.name,U=W.form,V=P([]);return X&&(X=X.replace(/'/g,"\\'"),V=U?P(U).find("[name='"+X+"'][type=radio]"):P("[name='"+X+"'][type=radio]",W.ownerDocument).filter(function(){return !this.form})),V};P.widget("ui.button",{version:"1.11.2",defaultElement:"<button>",options:{disabled:null,text:!0,label:null,icons:{primary:null,secondary:null}},_create:function(){this.element.closest("form").unbind("reset"+this.eventNamespace).bind("reset"+this.eventNamespace,C),"boolean"!=typeof this.options.disabled?this.options.disabled=!!this.element.prop("disabled"):this.element.prop("disabled",this.options.disabled),this._determineButtonType(),this.hasTitle=!!this.buttonElement.attr("title");var W=this,X=this.options,U="checkbox"===this.type||"radio"===this.type,V=U?"":"ui-state-active";null===X.label&&(X.label="input"===this.type?this.buttonElement.val():this.buttonElement.html()),this._hoverable(this.buttonElement),this.buttonElement.addClass(J).attr("role","button").bind("mouseenter"+this.eventNamespace,function(){X.disabled||this===M&&P(this).addClass("ui-state-active")}).bind("mouseleave"+this.eventNamespace,function(){X.disabled||P(this).removeClass(V)}).bind("click"+this.eventNamespace,function(Y){X.disabled&&(Y.preventDefault(),Y.stopImmediatePropagation())}),this._on({focus:function(){this.buttonElement.addClass("ui-state-focus")},blur:function(){this.buttonElement.removeClass("ui-state-focus")}}),U&&this.element.bind("change"+this.eventNamespace,function(){W.refresh()}),"checkbox"===this.type?this.buttonElement.bind("click"+this.eventNamespace,function(){return X.disabled?!1:void 0}):"radio"===this.type?this.buttonElement.bind("click"+this.eventNamespace,function(){if(X.disabled){return !1}P(this).addClass("ui-state-active"),W.buttonElement.attr("aria-pressed","true");var Y=W.element[0];D(Y).not(Y).map(function(){return P(this).button("widget")[0]}).removeClass("ui-state-active").attr("aria-pressed","false")}):(this.buttonElement.bind("mousedown"+this.eventNamespace,function(){return X.disabled?!1:(P(this).addClass("ui-state-active"),M=this,void W.document.one("mouseup",function(){M=null}))}).bind("mouseup"+this.eventNamespace,function(){return X.disabled?!1:void P(this).removeClass("ui-state-active")}).bind("keydown"+this.eventNamespace,function(Y){return X.disabled?!1:void ((Y.keyCode===P.ui.keyCode.SPACE||Y.keyCode===P.ui.keyCode.ENTER)&&P(this).addClass("ui-state-active"))}).bind("keyup"+this.eventNamespace+" blur"+this.eventNamespace,function(){P(this).removeClass("ui-state-active")}),this.buttonElement.is("a")&&this.buttonElement.keyup(function(Y){Y.keyCode===P.ui.keyCode.SPACE&&P(this).click()})),this._setOption("disabled",X.disabled),this._resetButton()},_determineButtonType:function(){var W,U,V;this.type=this.element.is("[type=checkbox]")?"checkbox":this.element.is("[type=radio]")?"radio":this.element.is("input")?"input":"button","checkbox"===this.type||"radio"===this.type?(W=this.element.parents().last(),U="label[for='"+this.element.attr("id")+"']",this.buttonElement=W.find(U),this.buttonElement.length||(W=W.length?W.siblings():this.element.siblings(),this.buttonElement=W.filter(U),this.buttonElement.length||(this.buttonElement=W.find(U))),this.element.addClass("ui-helper-hidden-accessible"),V=this.element.is(":checked"),V&&this.buttonElement.addClass("ui-state-active"),this.buttonElement.prop("aria-pressed",V)):this.buttonElement=this.element},widget:function(){return this.buttonElement},_destroy:function(){this.element.removeClass("ui-helper-hidden-accessible"),this.buttonElement.removeClass(J+" ui-state-active "+K).removeAttr("role").removeAttr("aria-pressed").html(this.buttonElement.find(".ui-button-text").html()),this.hasTitle||this.buttonElement.removeAttr("title")},_setOption:function(V,U){return this._super(V,U),"disabled"===V?(this.widget().toggleClass("ui-state-disabled",!!U),this.element.prop("disabled",!!U),void (U&&this.buttonElement.removeClass("checkbox"===this.type||"radio"===this.type?"ui-state-focus":"ui-state-focus ui-state-active"))):void this._resetButton()},refresh:function(){var U=this.element.is("input, button")?this.element.is(":disabled"):this.element.hasClass("ui-button-disabled");U!==this.options.disabled&&this._setOption("disabled",U),"radio"===this.type?D(this.element[0]).each(function(){P(this).is(":checked")?P(this).button("widget").addClass("ui-state-active").attr("aria-pressed","true"):P(this).button("widget").removeClass("ui-state-active").attr("aria-pressed","false")}):"checkbox"===this.type&&(this.element.is(":checked")?this.buttonElement.addClass("ui-state-active").attr("aria-pressed","true"):this.buttonElement.removeClass("ui-state-active").attr("aria-pressed","false"))},_resetButton:function(){if("input"===this.type){return void (this.options.label&&this.element.val(this.options.label))}var W=this.buttonElement.removeClass(K),X=P("<span></span>",this.document[0]).addClass("ui-button-text").html(this.options.label).appendTo(W.empty()).text(),U=this.options.icons,V=U.primary&&U.secondary,Y=[];U.primary||U.secondary?(this.options.text&&Y.push("ui-button-text-icon"+(V?"s":U.primary?"-primary":"-secondary")),U.primary&&W.prepend("<span class='ui-button-icon-primary ui-icon "+U.primary+"'></span>"),U.secondary&&W.append("<span class='ui-button-icon-secondary ui-icon "+U.secondary+"'></span>"),this.options.text||(Y.push(V?"ui-button-icons-only":"ui-button-icon-only"),this.hasTitle||W.attr("title",P.trim(X)))):Y.push("ui-button-text-only"),W.addClass(Y.join(" "))}}),P.widget("ui.buttonset",{version:"1.11.2",options:{items:"button, input[type=button], input[type=submit], input[type=reset], input[type=checkbox], input[type=radio], a, :data(ui-button)"},_create:function(){this.element.addClass("ui-buttonset")},_init:function(){this.refresh()},_setOption:function(V,U){"disabled"===V&&this.buttons.button("option",V,U),this._super(V,U)},refresh:function(){var V="rtl"===this.element.css("direction"),W=this.element.find(this.options.items),U=W.filter(":ui-button");W.not(":ui-button").button(),U.button("refresh"),this.buttons=W.map(function(){return P(this).button("widget")[0]}).removeClass("ui-corner-all ui-corner-left ui-corner-right").filter(":first").addClass(V?"ui-corner-right":"ui-corner-left").end().filter(":last").addClass(V?"ui-corner-left":"ui-corner-right").end().end()},_destroy:function(){this.element.removeClass("ui-buttonset"),this.buttons.map(function(){return P(this).button("widget")[0]}).removeClass("ui-corner-left ui-corner-right").end().button("destroy")}});P.ui.button;
/*
 * jQuery UI Datepicker 1.11.2
 * http://jqueryui.com
 *
 * Copyright 2014 jQuery Foundation and other contributors
 * Released under the MIT license.
 * http://jquery.org/license
 *
 * http://api.jqueryui.com/datepicker/
 */
P.extend(P.ui,{datepicker:{version:"1.11.2"}});var A;P.extend(T.prototype,{markerClassName:"hasDatepicker",maxRows:4,_widgetDatepicker:function(){return this.dpDiv},setDefaults:function(U){return H(this._defaults,U||{}),this},_attachDatepicker:function(W,X){var U,V,Y;U=W.nodeName.toLowerCase(),V="div"===U||"span"===U,W.id||(this.uuid+=1,W.id="dp"+this.uuid),Y=this._newInst(P(W),V),Y.settings=P.extend({},X||{}),"input"===U?this._connectDatepicker(W,Y):V&&this._inlineDatepicker(W,Y)},_newInst:function(V,W){var U=V[0].id.replace(/([^A-Za-z0-9_\-])/g,"\\\\$1");return{id:U,input:V,selectedDay:0,selectedMonth:0,selectedYear:0,drawMonth:0,drawYear:0,inline:W,dpDiv:W?Q(P("<div class='"+this._inlineClass+" ui-datepicker ui-widget ui-widget-content ui-helper-clearfix ui-corner-all'></div>")):this.dpDiv}},_connectDatepicker:function(V,W){var U=P(V);W.append=P([]),W.trigger=P([]),U.hasClass(this.markerClassName)||(this._attachments(U,W),U.addClass(this.markerClassName).keydown(this._doKeyDown).keypress(this._doKeyPress).keyup(this._doKeyUp),this._autoSize(W),P.data(V,"datepicker",W),W.settings.disabled&&this._disableDatepicker(V))},_attachments:function(Y,Z){var V,W,a,U=this._get(Z,"appendText"),X=this._get(Z,"isRTL");Z.append&&Z.append.remove(),U&&(Z.append=P("<span class='"+this._appendClass+"'>"+U+"</span>"),Y[X?"before":"after"](Z.append)),Y.unbind("focus",this._showDatepicker),Z.trigger&&Z.trigger.remove(),V=this._get(Z,"showOn"),("focus"===V||"both"===V)&&Y.focus(this._showDatepicker),("button"===V||"both"===V)&&(W=this._get(Z,"buttonText"),a=this._get(Z,"buttonImage"),Z.trigger=P(this._get(Z,"buttonImageOnly")?P("<img/>").addClass(this._triggerClass).attr({src:a,alt:W,title:W}):P("<button type='button'></button>").addClass(this._triggerClass).html(a?P("<img/>").attr({src:a,alt:W,title:W}):W)),Y[X?"before":"after"](Z.trigger),Z.trigger.click(function(){return P.datepicker._datepickerShowing&&P.datepicker._lastInput===Y[0]?P.datepicker._hideDatepicker():P.datepicker._datepickerShowing&&P.datepicker._lastInput!==Y[0]?(P.datepicker._hideDatepicker(),P.datepicker._showDatepicker(Y[0])):P.datepicker._showDatepicker(Y[0]),!1}))},_autoSize:function(Z){if(this._get(Z,"autoSize")&&!Z.inline){var X,Y,V,W,h=new Date(2009,11,20),U=this._get(Z,"dateFormat");U.match(/[DM]/)&&(X=function(b){for(Y=0,V=0,W=0;W<b.length;W++){b[W].length>Y&&(Y=b[W].length,V=W)}return V},h.setMonth(X(this._get(Z,U.match(/MM/)?"monthNames":"monthNamesShort"))),h.setDate(X(this._get(Z,U.match(/DD/)?"dayNames":"dayNamesShort"))+20-h.getDay())),Z.input.attr("size",this._formatDate(Z,h).length)}},_inlineDatepicker:function(V,W){var U=P(V);U.hasClass(this.markerClassName)||(U.addClass(this.markerClassName).append(W.dpDiv),P.data(V,"datepicker",W),this._setDate(W,this._getDefaultDate(W),!0),this._updateDatepicker(W),this._updateAlternate(W),W.settings.disabled&&this._disableDatepicker(V),W.dpDiv.css("display","block"))},_dialogDatepicker:function(a,h,p,U,n){var o,X,V,W,Y,Z=this._dialogInst;return Z||(this.uuid+=1,o="dp"+this.uuid,this._dialogInput=P("<input type='text' id='"+o+"' style='position: absolute; top: -100px; width: 0px;'/>"),this._dialogInput.keydown(this._doKeyDown),P("body").append(this._dialogInput),Z=this._dialogInst=this._newInst(this._dialogInput,!1),Z.settings={},P.data(this._dialogInput[0],"datepicker",Z)),H(Z.settings,U||{}),h=h&&h.constructor===Date?this._formatDate(Z,h):h,this._dialogInput.val(h),this._pos=n?n.length?n:[n.pageX,n.pageY]:null,this._pos||(X=document.documentElement.clientWidth,V=document.documentElement.clientHeight,W=document.documentElement.scrollLeft||document.body.scrollLeft,Y=document.documentElement.scrollTop||document.body.scrollTop,this._pos=[X/2-100+W,V/2-150+Y]),this._dialogInput.css("left",this._pos[0]+20+"px").css("top",this._pos[1]+"px"),Z.settings.onSelect=p,this._inDialog=!0,this.dpDiv.addClass(this._dialogClass),this._showDatepicker(this._dialogInput[0]),P.blockUI&&P.blockUI(this.dpDiv),P.data(this._dialogInput[0],"datepicker",Z),this},_destroyDatepicker:function(W){var X,U=P(W),V=P.data(W,"datepicker");U.hasClass(this.markerClassName)&&(X=W.nodeName.toLowerCase(),P.removeData(W,"datepicker"),"input"===X?(V.append.remove(),V.trigger.remove(),U.removeClass(this.markerClassName).unbind("focus",this._showDatepicker).unbind("keydown",this._doKeyDown).unbind("keypress",this._doKeyPress).unbind("keyup",this._doKeyUp)):("div"===X||"span"===X)&&U.removeClass(this.markerClassName).empty())},_enableDatepicker:function(W){var X,U,V=P(W),Y=P.data(W,"datepicker");V.hasClass(this.markerClassName)&&(X=W.nodeName.toLowerCase(),"input"===X?(W.disabled=!1,Y.trigger.filter("button").each(function(){this.disabled=!1}).end().filter("img").css({opacity:"1.0",cursor:""})):("div"===X||"span"===X)&&(U=V.children("."+this._inlineClass),U.children().removeClass("ui-state-disabled"),U.find("select.ui-datepicker-month, select.ui-datepicker-year").prop("disabled",!1)),this._disabledInputs=P.map(this._disabledInputs,function(Z){return Z===W?null:Z}))},_disableDatepicker:function(W){var X,U,V=P(W),Y=P.data(W,"datepicker");V.hasClass(this.markerClassName)&&(X=W.nodeName.toLowerCase(),"input"===X?(W.disabled=!0,Y.trigger.filter("button").each(function(){this.disabled=!0}).end().filter("img").css({opacity:"0.5",cursor:"default"})):("div"===X||"span"===X)&&(U=V.children("."+this._inlineClass),U.children().addClass("ui-state-disabled"),U.find("select.ui-datepicker-month, select.ui-datepicker-year").prop("disabled",!0)),this._disabledInputs=P.map(this._disabledInputs,function(Z){return Z===W?null:Z}),this._disabledInputs[this._disabledInputs.length]=W)},_isDisabledDatepicker:function(V){if(!V){return !1}for(var U=0;U<this._disabledInputs.length;U++){if(this._disabledInputs[U]===V){return !0}}return !1},_getInst:function(U){try{return P.data(U,"datepicker")}catch(V){throw"Missing instance data for this datepicker"}},_optionDatepicker:function(X,Y,h){var U,Z,a,W,V=this._getInst(X);return 2===arguments.length&&"string"==typeof Y?"defaults"===Y?P.extend({},P.datepicker._defaults):V?"all"===Y?P.extend({},V.settings):this._get(V,Y):null:(U=Y||{},"string"==typeof Y&&(U={},U[Y]=h),void (V&&(this._curInst===V&&this._hideDatepicker(),Z=this._getDateDatepicker(X,!0),a=this._getMinMaxDate(V,"min"),W=this._getMinMaxDate(V,"max"),H(V.settings,U),null!==a&&void 0!==U.dateFormat&&void 0===U.minDate&&(V.settings.minDate=this._formatDate(V,a)),null!==W&&void 0!==U.dateFormat&&void 0===U.maxDate&&(V.settings.maxDate=this._formatDate(V,W)),"disabled" in U&&(U.disabled?this._disableDatepicker(X):this._enableDatepicker(X)),this._attachments(P(X),V),this._autoSize(V),this._setDate(V,Z),this._updateAlternate(V),this._updateDatepicker(V))))},_changeDatepicker:function(W,U,V){this._optionDatepicker(W,U,V)},_refreshDatepicker:function(V){var U=this._getInst(V);U&&this._updateDatepicker(U)},_setDateDatepicker:function(W,U){var V=this._getInst(W);V&&(this._setDate(V,U),this._updateDatepicker(V),this._updateAlternate(V))},_getDateDatepicker:function(W,U){var V=this._getInst(W);return V&&!V.inline&&this._setDateFromField(V,U),V?this._getDate(V):null},_doKeyDown:function(Y){var Z,V,W,a=P.datepicker._getInst(Y.target),U=!0,X=a.dpDiv.is(".ui-datepicker-rtl");if(a._keyEvent=!0,P.datepicker._datepickerShowing){switch(Y.keyCode){case 9:P.datepicker._hideDatepicker(),U=!1;break;case 13:return W=P("td."+P.datepicker._dayOverClass+":not(."+P.datepicker._currentClass+")",a.dpDiv),W[0]&&P.datepicker._selectDay(Y.target,a.selectedMonth,a.selectedYear,W[0]),Z=P.datepicker._get(a,"onSelect"),Z?(V=P.datepicker._formatDate(a),Z.apply(a.input?a.input[0]:null,[V,a])):P.datepicker._hideDatepicker(),!1;case 27:P.datepicker._hideDatepicker();break;case 33:P.datepicker._adjustDate(Y.target,Y.ctrlKey?-P.datepicker._get(a,"stepBigMonths"):-P.datepicker._get(a,"stepMonths"),"M");break;case 34:P.datepicker._adjustDate(Y.target,Y.ctrlKey?+P.datepicker._get(a,"stepBigMonths"):+P.datepicker._get(a,"stepMonths"),"M");break;case 35:(Y.ctrlKey||Y.metaKey)&&P.datepicker._clearDate(Y.target),U=Y.ctrlKey||Y.metaKey;break;case 36:(Y.ctrlKey||Y.metaKey)&&P.datepicker._gotoToday(Y.target),U=Y.ctrlKey||Y.metaKey;break;case 37:(Y.ctrlKey||Y.metaKey)&&P.datepicker._adjustDate(Y.target,X?1:-1,"D"),U=Y.ctrlKey||Y.metaKey,Y.originalEvent.altKey&&P.datepicker._adjustDate(Y.target,Y.ctrlKey?-P.datepicker._get(a,"stepBigMonths"):-P.datepicker._get(a,"stepMonths"),"M");break;case 38:(Y.ctrlKey||Y.metaKey)&&P.datepicker._adjustDate(Y.target,-7,"D"),U=Y.ctrlKey||Y.metaKey;break;case 39:(Y.ctrlKey||Y.metaKey)&&P.datepicker._adjustDate(Y.target,X?-1:1,"D"),U=Y.ctrlKey||Y.metaKey,Y.originalEvent.altKey&&P.datepicker._adjustDate(Y.target,Y.ctrlKey?+P.datepicker._get(a,"stepBigMonths"):+P.datepicker._get(a,"stepMonths"),"M");break;case 40:(Y.ctrlKey||Y.metaKey)&&P.datepicker._adjustDate(Y.target,7,"D"),U=Y.ctrlKey||Y.metaKey;break;default:U=!1}}else{36===Y.keyCode&&Y.ctrlKey?P.datepicker._showDatepicker(this):U=!1}U&&(Y.preventDefault(),Y.stopPropagation())},_doKeyPress:function(W){var X,U,V=P.datepicker._getInst(W.target);return P.datepicker._get(V,"constrainInput")?(X=P.datepicker._possibleChars(P.datepicker._get(V,"dateFormat")),U=String.fromCharCode(null==W.charCode?W.keyCode:W.charCode),W.ctrlKey||W.metaKey||" ">U||!X||X.indexOf(U)>-1):void 0},_doKeyUp:function(W){var X,U=P.datepicker._getInst(W.target);if(U.input.val()!==U.lastVal){try{X=P.datepicker.parseDate(P.datepicker._get(U,"dateFormat"),U.input?U.input.val():null,P.datepicker._getFormatConfig(U)),X&&(P.datepicker._setDateFromField(U),P.datepicker._updateAlternate(U),P.datepicker._updateDatepicker(U))}catch(V){}}return !0},_showDatepicker:function(Y){if(Y=Y.target||Y,"input"!==Y.nodeName.toLowerCase()&&(Y=P("input",Y.parentNode)[0]),!P.datepicker._isDisabledDatepicker(Y)&&P.datepicker._lastInput!==Y){var Z,U,a,d,X,V,W;Z=P.datepicker._getInst(Y),P.datepicker._curInst&&P.datepicker._curInst!==Z&&(P.datepicker._curInst.dpDiv.stop(!0,!0),Z&&P.datepicker._datepickerShowing&&P.datepicker._hideDatepicker(P.datepicker._curInst.input[0])),U=P.datepicker._get(Z,"beforeShow"),a=U?U.apply(Y,[Y,Z]):{},a!==!1&&(H(Z.settings,a),Z.lastVal=null,P.datepicker._lastInput=Y,P.datepicker._setDateFromField(Z),P.datepicker._inDialog&&(Y.value=""),P.datepicker._pos||(P.datepicker._pos=P.datepicker._findPos(Y),P.datepicker._pos[1]+=Y.offsetHeight),d=!1,P(Y).parents().each(function(){return d|="fixed"===P(this).css("position"),!d}),X={left:P.datepicker._pos[0],top:P.datepicker._pos[1]},P.datepicker._pos=null,Z.dpDiv.empty(),Z.dpDiv.css({position:"absolute",display:"block",top:"-1000px"}),P.datepicker._updateDatepicker(Z),X=P.datepicker._checkOffset(Z,X,d),Z.dpDiv.css({position:P.datepicker._inDialog&&P.blockUI?"static":d?"fixed":"absolute",display:"none",left:X.left+"px",top:X.top+"px"}),Z.inline||(V=P.datepicker._get(Z,"showAnim"),W=P.datepicker._get(Z,"duration"),Z.dpDiv.css("z-index",S(P(Y))+1),P.datepicker._datepickerShowing=!0,P.effects&&P.effects.effect[V]?Z.dpDiv.show(V,P.datepicker._get(Z,"showOptions"),W):Z.dpDiv[V||"show"](V?W:null),P.datepicker._shouldFocusInput(Z)&&Z.input.focus(),P.datepicker._curInst=Z))}},_updateDatepicker:function(X){this.maxRows=4,A=X,X.dpDiv.empty().append(this._generateHTML(X)),this._attachHandlers(X);var Y,V=this._getNumberOfMonths(X),W=V[1],Z=17,U=X.dpDiv.find("."+this._dayOverClass+" a");U.length>0&&R.apply(U.get(0)),X.dpDiv.removeClass("ui-datepicker-multi-2 ui-datepicker-multi-3 ui-datepicker-multi-4").width(""),W>1&&X.dpDiv.addClass("ui-datepicker-multi-"+W).css("width",Z*W+"em"),X.dpDiv[(1!==V[0]||1!==V[1]?"add":"remove")+"Class"]("ui-datepicker-multi"),X.dpDiv[(this._get(X,"isRTL")?"add":"remove")+"Class"]("ui-datepicker-rtl"),X===P.datepicker._curInst&&P.datepicker._datepickerShowing&&P.datepicker._shouldFocusInput(X)&&X.input.focus(),X.yearshtml&&(Y=X.yearshtml,setTimeout(function(){Y===X.yearshtml&&X.yearshtml&&X.dpDiv.find("select.ui-datepicker-year:first").replaceWith(X.yearshtml),Y=X.yearshtml=null},0))},_shouldFocusInput:function(U){return U.input&&U.input.is(":visible")&&!U.input.is(":disabled")&&!U.input.is(":focus")},_checkOffset:function(Y,Z,l){var U=Y.dpDiv.outerWidth(),a=Y.dpDiv.outerHeight(),k=Y.input?Y.input.outerWidth():0,W=Y.input?Y.input.outerHeight():0,X=document.documentElement.clientWidth+(l?0:P(document).scrollLeft()),V=document.documentElement.clientHeight+(l?0:P(document).scrollTop());return Z.left-=this._get(Y,"isRTL")?U-k:0,Z.left-=l&&Z.left===Y.input.offset().left?P(document).scrollLeft():0,Z.top-=l&&Z.top===Y.input.offset().top+W?P(document).scrollTop():0,Z.left-=Math.min(Z.left,Z.left+U>X&&X>U?Math.abs(Z.left+U-X):0),Z.top-=Math.min(Z.top,Z.top+a>V&&V>a?Math.abs(a+W):0),Z},_findPos:function(W){for(var X,U=this._getInst(W),V=this._get(U,"isRTL");W&&("hidden"===W.type||1!==W.nodeType||P.expr.filters.hidden(W));){W=W[V?"previousSibling":"nextSibling"]}return X=P(W).offset(),[X.left,X.top]},_hideDatepicker:function(X){var Y,V,W,Z,U=this._curInst;!U||X&&U!==P.data(X,"datepicker")||this._datepickerShowing&&(Y=this._get(U,"showAnim"),V=this._get(U,"duration"),W=function(){P.datepicker._tidyDialog(U)},P.effects&&(P.effects.effect[Y]||P.effects[Y])?U.dpDiv.hide(Y,P.datepicker._get(U,"showOptions"),V,W):U.dpDiv["slideDown"===Y?"slideUp":"fadeIn"===Y?"fadeOut":"hide"](Y?V:null,W),Y||W(),this._datepickerShowing=!1,Z=this._get(U,"onClose"),Z&&Z.apply(U.input?U.input[0]:null,[U.input?U.input.val():"",U]),this._lastInput=null,this._inDialog&&(this._dialogInput.css({position:"absolute",left:"0",top:"-100px"}),P.blockUI&&(P.unblockUI(),P("body").append(this.dpDiv))),this._inDialog=!1)},_tidyDialog:function(U){U.dpDiv.removeClass(this._dialogClass).unbind(".ui-datepicker-calendar")},_checkExternalClick:function(V){if(P.datepicker._curInst){var W=P(V.target),U=P.datepicker._getInst(W[0]);(W[0].id!==P.datepicker._mainDivId&&0===W.parents("#"+P.datepicker._mainDivId).length&&!W.hasClass(P.datepicker.markerClassName)&&!W.closest("."+P.datepicker._triggerClass).length&&P.datepicker._datepickerShowing&&(!P.datepicker._inDialog||!P.blockUI)||W.hasClass(P.datepicker.markerClassName)&&P.datepicker._curInst!==U)&&P.datepicker._hideDatepicker()}},_adjustDate:function(W,X,U){var V=P(W),Y=this._getInst(V[0]);this._isDisabledDatepicker(V[0])||(this._adjustInstDate(Y,X+("M"===U?this._get(Y,"showCurrentAtPos"):0),U),this._updateDatepicker(Y))},_gotoToday:function(W){var X,U=P(W),V=this._getInst(U[0]);this._get(V,"gotoCurrent")&&V.currentDay?(V.selectedDay=V.currentDay,V.drawMonth=V.selectedMonth=V.currentMonth,V.drawYear=V.selectedYear=V.currentYear):(X=new Date,V.selectedDay=X.getDate(),V.drawMonth=V.selectedMonth=X.getMonth(),V.drawYear=V.selectedYear=X.getFullYear()),this._notifyChange(V),this._adjustDate(U)},_selectMonthYear:function(W,X,U){var V=P(W),Y=this._getInst(V[0]);Y["selected"+("M"===U?"Month":"Year")]=Y["draw"+("M"===U?"Month":"Year")]=parseInt(X.options[X.selectedIndex].value,10),this._notifyChange(Y),this._adjustDate(V)},_selectDay:function(X,Y,V,W){var Z,U=P(X);P(W).hasClass(this._unselectableClass)||this._isDisabledDatepicker(U[0])||(Z=this._getInst(U[0]),Z.selectedDay=Z.currentDay=P("a",W).html(),Z.selectedMonth=Z.currentMonth=Y,Z.selectedYear=Z.currentYear=V,this._selectDate(X,this._formatDate(Z,Z.currentDay,Z.currentMonth,Z.currentYear)))},_clearDate:function(U){var V=P(U);this._selectDate(V,"")},_selectDate:function(W,X){var U,V=P(W),Y=this._getInst(V[0]);X=null!=X?X:this._formatDate(Y),Y.input&&Y.input.val(X),this._updateAlternate(Y),U=this._get(Y,"onSelect"),U?U.apply(Y.input?Y.input[0]:null,[X,Y]):Y.input&&Y.input.trigger("change"),Y.inline?this._updateDatepicker(Y):(this._hideDatepicker(),this._lastInput=Y.input[0],"object"!=typeof Y.input[0]&&Y.input.focus(),this._lastInput=null)},_updateAlternate:function(W){var X,U,V,Y=this._get(W,"altField");Y&&(X=this._get(W,"altFormat")||this._get(W,"dateFormat"),U=this._getDate(W),V=this.formatDate(X,U,this._getFormatConfig(W)),P(Y).each(function(){P(this).val(V)}))},noWeekends:function(V){var U=V.getDay();return[U>0&&6>U,""]},iso8601Week:function(W){var U,V=new Date(W.getTime());return V.setDate(V.getDate()+4-(V.getDay()||7)),U=V.getTime(),V.setMonth(0),V.setDate(1),Math.floor(Math.round((U-V)/86400000)/7)+1},parseDate:function(Ah,Ai,Al){if(null==Ah||null==Ai){throw"Invalid arguments"}if(Ai="object"==typeof Ai?Ai.toString():Ai+"",""===Ai){return null}var Am,Aj,Ak,Ab,Ac=0,z=(Al?Al.shortYearCutoff:null)||this._defaults.shortYearCutoff,Aa="string"!=typeof z?z:(new Date).getFullYear()%100+parseInt(z,10),Af=(Al?Al.dayNamesShort:null)||this._defaults.dayNamesShort,Ag=(Al?Al.dayNames:null)||this._defaults.dayNames,Ad=(Al?Al.monthNamesShort:null)||this._defaults.monthNamesShort,Ae=(Al?Al.monthNames:null)||this._defaults.monthNames,W=-1,X=-1,U=-1,V=-1,a=!1,y=function(d){var b=Am+1<Ah.length&&Ah.charAt(Am+1)===d;return b&&Am++,b},Y=function(k){var j=y(k),h="@"===k?14:"!"===k?20:"y"===k&&j?4:"o"===k?3:2,i="y"===k?h:1,l=new RegExp("^\\d{"+i+","+h+"}"),c=Ai.substring(Ac).match(l);if(!c){throw"Missing number at position "+Ac}return Ac+=c[0].length,parseInt(c[0],10)},Z=function(j,h,i){var k=-1,c=P.map(y(j)?i:h,function(e,d){return[[d,e]]}).sort(function(e,d){return -(e[1].length-d[1].length)});if(P.each(c,function(g,f){var e=f[1];return Ai.substr(Ac,e.length).toLowerCase()===e.toLowerCase()?(k=f[0],Ac+=e.length,!1):void 0}),-1!==k){return k+1}throw"Unknown name at position "+Ac},An=function(){if(Ai.charAt(Ac)!==Ah.charAt(Am)){throw"Unexpected literal at position "+Ac}Ac++};for(Am=0;Am<Ah.length;Am++){if(a){"'"!==Ah.charAt(Am)||y("'")?An():a=!1}else{switch(Ah.charAt(Am)){case"d":U=Y("d");break;case"D":Z("D",Af,Ag);break;case"o":V=Y("o");break;case"m":X=Y("m");break;case"M":X=Z("M",Ad,Ae);break;case"y":W=Y("y");break;case"@":Ab=new Date(Y("@")),W=Ab.getFullYear(),X=Ab.getMonth()+1,U=Ab.getDate();break;case"!":Ab=new Date((Y("!")-this._ticksTo1970)/10000),W=Ab.getFullYear(),X=Ab.getMonth()+1,U=Ab.getDate();break;case"'":y("'")?An():a=!0;break;default:An()}}}if(Ac<Ai.length&&(Ak=Ai.substr(Ac),!/^\s+/.test(Ak))){throw"Extra/unparsed characters found in date: "+Ak}if(-1===W?W=(new Date).getFullYear():100>W&&(W+=(new Date).getFullYear()-(new Date).getFullYear()%100+(Aa>=W?0:-100)),V>-1){for(X=1,U=V;;){if(Aj=this._getDaysInMonth(W,X-1),Aj>=U){break}X++,U-=Aj}}if(Ab=this._daylightSavingAdjust(new Date(W,X-1,U)),Ab.getFullYear()!==W||Ab.getMonth()+1!==X||Ab.getDate()!==U){throw"Invalid date"}return Ab},ATOM:"yy-mm-dd",COOKIE:"D, dd M yy",ISO_8601:"yy-mm-dd",RFC_822:"D, d M y",RFC_850:"DD, dd-M-y",RFC_1036:"D, d M y",RFC_1123:"D, d M yy",RFC_2822:"D, d M yy",RSS:"D, d M y",TICKS:"!",TIMESTAMP:"@",W3C:"yy-mm-dd",_ticksTo1970:24*(718685+Math.floor(492.5)-Math.floor(19.7)+Math.floor(4.925))*60*60*10000000,formatDate:function(q,o,p){if(!o){return""}var t,U=(p?p.dayNamesShort:null)||this._defaults.dayNamesShort,r=(p?p.dayNames:null)||this._defaults.dayNames,s=(p?p.monthNamesShort:null)||this._defaults.monthNamesShort,X=(p?p.monthNames:null)||this._defaults.monthNames,Y=function(a){var d=t+1<q.length&&q.charAt(t+1)===a;return d&&t++,d},V=function(h,f,g){var e=""+f;if(Y(h)){for(;e.length<g;){e="0"+e}}return e},W=function(h,f,g,e){return Y(h)?e[f]:g[f]},Z="",n=!1;if(o){for(t=0;t<q.length;t++){if(n){"'"!==q.charAt(t)||Y("'")?Z+=q.charAt(t):n=!1}else{switch(q.charAt(t)){case"d":Z+=V("d",o.getDate(),2);break;case"D":Z+=W("D",o.getDay(),U,r);break;case"o":Z+=V("o",Math.round((new Date(o.getFullYear(),o.getMonth(),o.getDate()).getTime()-new Date(o.getFullYear(),0,0).getTime())/86400000),3);break;case"m":Z+=V("m",o.getMonth()+1,2);break;case"M":Z+=W("M",o.getMonth(),s,X);break;case"y":Z+=Y("y")?o.getFullYear():(o.getYear()%100<10?"0":"")+o.getYear()%100;break;case"@":Z+=o.getTime();break;case"!":Z+=10000*o.getTime()+this._ticksTo1970;break;case"'":Y("'")?Z+="'":n=!0;break;default:Z+=q.charAt(t)}}}}return Z},_possibleChars:function(Y){var W,X="",U=!1,V=function(a){var Z=W+1<Y.length&&Y.charAt(W+1)===a;return Z&&W++,Z};for(W=0;W<Y.length;W++){if(U){"'"!==Y.charAt(W)||V("'")?X+=Y.charAt(W):U=!1}else{switch(Y.charAt(W)){case"d":case"m":case"y":case"@":X+="0123456789";break;case"D":case"M":return null;case"'":V("'")?X+="'":U=!0;break;default:X+=Y.charAt(W)}}}return X},_get:function(V,U){return void 0!==V.settings[U]?V.settings[U]:this._defaults[U]},_setDateFromField:function(Y,W){if(Y.input.val()!==Y.lastVal){var X=this._get(Y,"dateFormat"),j=Y.lastVal=Y.input?Y.input.val():null,U=this._getDefaultDate(Y),Z=U,i=this._getFormatConfig(Y);try{Z=this.parseDate(X,j,i)||U}catch(V){j=W?"":j}Y.selectedDay=Z.getDate(),Y.drawMonth=Y.selectedMonth=Z.getMonth(),Y.drawYear=Y.selectedYear=Z.getFullYear(),Y.currentDay=j?Z.getDate():0,Y.currentMonth=j?Z.getMonth():0,Y.currentYear=j?Z.getFullYear():0,this._adjustInstDate(Y)}},_getDefaultDate:function(U){return this._restrictMinMax(U,this._determineDate(U,this._get(U,"defaultDate"),new Date))},_determineDate:function(X,Y,V){var W=function(d){var c=new Date;return c.setDate(c.getDate()+d),c},Z=function(m){try{return P.datepicker.parseDate(P.datepicker._get(X,"dateFormat"),m,P.datepicker._getFormatConfig(X))}catch(p){}for(var a=(m.toLowerCase().match(/^c/)?P.datepicker._getDate(X):null)||new Date,n=a.getFullYear(),o=a.getMonth(),k=a.getDate(),l=/([+\-]?[0-9]+)\s*(d|D|w|W|m|M|y|Y)?/g,b=l.exec(m);b;){switch(b[2]||"d"){case"d":case"D":k+=parseInt(b[1],10);break;case"w":case"W":k+=7*parseInt(b[1],10);break;case"m":case"M":o+=parseInt(b[1],10),k=Math.min(k,P.datepicker._getDaysInMonth(n,o));break;case"y":case"Y":n+=parseInt(b[1],10),k=Math.min(k,P.datepicker._getDaysInMonth(n,o))}b=l.exec(m)}return new Date(n,o,k)},U=null==Y||""===Y?V:"string"==typeof Y?Z(Y):"number"==typeof Y?isNaN(Y)?V:W(Y):new Date(Y.getTime());return U=U&&"Invalid Date"===U.toString()?V:U,U&&(U.setHours(0),U.setMinutes(0),U.setSeconds(0),U.setMilliseconds(0)),this._daylightSavingAdjust(U)},_daylightSavingAdjust:function(U){return U?(U.setHours(U.getHours()>12?U.getHours()+2:0),U):null},_setDate:function(Z,X,Y){var V=!X,W=Z.selectedMonth,h=Z.selectedYear,U=this._restrictMinMax(Z,this._determineDate(Z,X,new Date));Z.selectedDay=Z.currentDay=U.getDate(),Z.drawMonth=Z.selectedMonth=Z.currentMonth=U.getMonth(),Z.drawYear=Z.selectedYear=Z.currentYear=U.getFullYear(),W===Z.selectedMonth&&h===Z.selectedYear||Y||this._notifyChange(Z),this._adjustInstDate(Z),Z.input&&Z.input.val(V?"":this._formatDate(Z))},_getDate:function(V){var U=!V.currentYear||V.input&&""===V.input.val()?null:this._daylightSavingAdjust(new Date(V.currentYear,V.currentMonth,V.currentDay));return U},_attachHandlers:function(V){var W=this._get(V,"stepMonths"),U="#"+V.id.replace(/\\\\/g,"\\");V.dpDiv.find("[data-handler]").map(function(){var X={prev:function(){P.datepicker._adjustDate(U,-W,"M")},next:function(){P.datepicker._adjustDate(U,+W,"M")},hide:function(){P.datepicker._hideDatepicker()},today:function(){P.datepicker._gotoToday(U)},selectDay:function(){return P.datepicker._selectDay(U,+this.getAttribute("data-month"),+this.getAttribute("data-year"),this),!1},selectMonth:function(){return P.datepicker._selectMonthYear(U,this,"M"),!1},selectYear:function(){return P.datepicker._selectMonthYear(U,this,"Y"),!1}};P(this).bind(this.getAttribute("data-event"),X[this.getAttribute("data-handler")])})},_generateHTML:function(AZ){var AX,AY,A2,Aa,A0,A1,AR,AS,AP,AQ,AV,AW,AT,AU,AJ,AK,AH,AI,AN,AO,AL,AM,AF,AG,AE,Az,Ax,Ay,AC,AD,AA,AB,Ar,As,Ap,Aq,Av,Aw,At,Au=new Date,Aj=this._daylightSavingAdjust(new Date(Au.getFullYear(),Au.getMonth(),Au.getDate())),Ak=this._get(AZ,"isRTL"),Ah=this._get(AZ,"showButtonPanel"),Ai=this._get(AZ,"hideIfNoPrevNext"),An=this._get(AZ,"navigationAsDateFormat"),Ao=this._getNumberOfMonths(AZ),Al=this._get(AZ,"showCurrentAtPos"),Am=this._get(AZ,"stepMonths"),Ae=1!==Ao[0]||1!==Ao[1],Af=this._daylightSavingAdjust(AZ.currentDay?new Date(AZ.currentYear,AZ.currentMonth,AZ.currentDay):new Date(9999,9,9)),Ad=this._getMinMaxDate(AZ,"min"),Ab=this._getMinMaxDate(AZ,"max"),Ag=AZ.drawMonth-Al,Ac=AZ.drawYear;if(0>Ag&&(Ag+=12,Ac--),Ab){for(AX=this._daylightSavingAdjust(new Date(Ab.getFullYear(),Ab.getMonth()-Ao[0]*Ao[1]+1,Ab.getDate())),AX=Ad&&Ad>AX?Ad:AX;this._daylightSavingAdjust(new Date(Ac,Ag,1))>AX;){Ag--,0>Ag&&(Ag=11,Ac--)}}for(AZ.drawMonth=Ag,AZ.drawYear=Ac,AY=this._get(AZ,"prevText"),AY=An?this.formatDate(AY,this._daylightSavingAdjust(new Date(Ac,Ag-Am,1)),this._getFormatConfig(AZ)):AY,A2=this._canAdjustMonth(AZ,-1,Ac,Ag)?"<a class='ui-datepicker-prev ui-corner-all' data-handler='prev' data-event='click' title='"+AY+"'><span class='ui-icon ui-icon-circle-triangle-"+(Ak?"e":"w")+"'>"+AY+"</span></a>":Ai?"":"<a class='ui-datepicker-prev ui-corner-all ui-state-disabled' title='"+AY+"'><span class='ui-icon ui-icon-circle-triangle-"+(Ak?"e":"w")+"'>"+AY+"</span></a>",Aa=this._get(AZ,"nextText"),Aa=An?this.formatDate(Aa,this._daylightSavingAdjust(new Date(Ac,Ag+Am,1)),this._getFormatConfig(AZ)):Aa,A0=this._canAdjustMonth(AZ,1,Ac,Ag)?"<a class='ui-datepicker-next ui-corner-all' data-handler='next' data-event='click' title='"+Aa+"'><span class='ui-icon ui-icon-circle-triangle-"+(Ak?"w":"e")+"'>"+Aa+"</span></a>":Ai?"":"<a class='ui-datepicker-next ui-corner-all ui-state-disabled' title='"+Aa+"'><span class='ui-icon ui-icon-circle-triangle-"+(Ak?"w":"e")+"'>"+Aa+"</span></a>",A1=this._get(AZ,"currentText"),AR=this._get(AZ,"gotoCurrent")&&AZ.currentDay?Af:Aj,A1=An?this.formatDate(A1,AR,this._getFormatConfig(AZ)):A1,AS=AZ.inline?"":"<button type='button' class='ui-datepicker-close ui-state-default ui-priority-primary ui-corner-all' data-handler='hide' data-event='click'>"+this._get(AZ,"closeText")+"</button>",AP=Ah?"<div class='ui-datepicker-buttonpane ui-widget-content'>"+(Ak?AS:"")+(this._isInRange(AZ,AR)?"<button type='button' class='ui-datepicker-current ui-state-default ui-priority-secondary ui-corner-all' data-handler='today' data-event='click'>"+A1+"</button>":"")+(Ak?"":AS)+"</div>":"",AQ=parseInt(this._get(AZ,"firstDay"),10),AQ=isNaN(AQ)?0:AQ,AV=this._get(AZ,"showWeek"),AW=this._get(AZ,"dayNames"),AT=this._get(AZ,"dayNamesMin"),AU=this._get(AZ,"monthNames"),AJ=this._get(AZ,"monthNamesShort"),AK=this._get(AZ,"beforeShowDay"),AH=this._get(AZ,"showOtherMonths"),AI=this._get(AZ,"selectOtherMonths"),AN=this._getDefaultDate(AZ),AO="",AM=0;AM<Ao[0];AM++){for(AF="",this.maxRows=4,AG=0;AG<Ao[1];AG++){if(AE=this._daylightSavingAdjust(new Date(Ac,Ag,AZ.selectedDay)),Az=" ui-corner-all",Ax="",Ae){if(Ax+="<div class='ui-datepicker-group",Ao[1]>1){switch(AG){case 0:Ax+=" ui-datepicker-group-first",Az=" ui-corner-"+(Ak?"right":"left");break;case Ao[1]-1:Ax+=" ui-datepicker-group-last",Az=" ui-corner-"+(Ak?"left":"right");break;default:Ax+=" ui-datepicker-group-middle",Az=""}}Ax+="'>"}for(Ax+="<div class='ui-datepicker-header ui-widget-header ui-helper-clearfix"+Az+"'>"+(/all|left/.test(Az)&&0===AM?Ak?A0:A2:"")+(/all|right/.test(Az)&&0===AM?Ak?A2:A0:"")+this._generateMonthYearHeader(AZ,Ag,Ac,Ad,Ab,AM>0||AG>0,AU,AJ)+"</div><table class='ui-datepicker-calendar'><thead><tr>",Ay=AV?"<th class='ui-datepicker-week-col'>"+this._get(AZ,"weekHeader")+"</th>":"",AL=0;7>AL;AL++){AC=(AL+AQ)%7,Ay+="<th scope='col'"+((AL+AQ+6)%7>=5?" class='ui-datepicker-week-end'":"")+"><span title='"+AW[AC]+"'>"+AT[AC]+"</span></th>"}for(Ax+=Ay+"</tr></thead><tbody>",AD=this._getDaysInMonth(Ac,Ag),Ac===AZ.selectedYear&&Ag===AZ.selectedMonth&&(AZ.selectedDay=Math.min(AZ.selectedDay,AD)),AA=(this._getFirstDayOfMonth(Ac,Ag)-AQ+7)%7,AB=Math.ceil((AA+AD)/7),Ar=Ae&&this.maxRows>AB?this.maxRows:AB,this.maxRows=Ar,As=this._daylightSavingAdjust(new Date(Ac,Ag,1-AA)),Ap=0;Ar>Ap;Ap++){for(Ax+="<tr>",Aq=AV?"<td class='ui-datepicker-week-col'>"+this._get(AZ,"calculateWeek")(As)+"</td>":"",AL=0;7>AL;AL++){Av=AK?AK.apply(AZ.input?AZ.input[0]:null,[As]):[!0,""],Aw=As.getMonth()!==Ag,At=Aw&&!AI||!Av[0]||Ad&&Ad>As||Ab&&As>Ab,Aq+="<td class='"+((AL+AQ+6)%7>=5?" ui-datepicker-week-end":"")+(Aw?" ui-datepicker-other-month":"")+(As.getTime()===AE.getTime()&&Ag===AZ.selectedMonth&&AZ._keyEvent||AN.getTime()===As.getTime()&&AN.getTime()===AE.getTime()?" "+this._dayOverClass:"")+(At?" "+this._unselectableClass+" ui-state-disabled":"")+(Aw&&!AH?"":" "+Av[1]+(As.getTime()===Af.getTime()?" "+this._currentClass:"")+(As.getTime()===Aj.getTime()?" ui-datepicker-today":""))+"'"+(Aw&&!AH||!Av[2]?"":" title='"+Av[2].replace(/'/g,"&#39;")+"'")+(At?"":" data-handler='selectDay' data-event='click' data-month='"+As.getMonth()+"' data-year='"+As.getFullYear()+"'")+">"+(Aw&&!AH?"&#xa0;":At?"<span class='ui-state-default'>"+As.getDate()+"</span>":"<a class='ui-state-default"+(As.getTime()===Aj.getTime()?" ui-state-highlight":"")+(As.getTime()===Af.getTime()?" ui-state-active":"")+(Aw?" ui-priority-secondary":"")+"' href='#'>"+As.getDate()+"</a>")+"</td>",As.setDate(As.getDate()+1),As=this._daylightSavingAdjust(As)}Ax+=Aq+"</tr>"}Ag++,Ag>11&&(Ag=0,Ac++),Ax+="</tbody></table>"+(Ae?"</div>"+(Ao[0]>0&&AG===Ao[1]-1?"<div class='ui-datepicker-row-break'></div>":""):""),AF+=Ax}AO+=AF}return AO+=AP,AZ._keyEvent=!1,AO},_generateMonthYearHeader:function(Af,Ad,Ae,Ai,Aj,Ag,Ah,x){var y,v,w,Ab,Ac,z,Aa,W,X=this._get(Af,"changeMonth"),U=this._get(Af,"changeYear"),V=this._get(Af,"showMonthAfterYear"),Y="<div class='ui-datepicker-title'>",Z="";if(Ag||!X){Z+="<span class='ui-datepicker-month'>"+Ah[Ad]+"</span>"}else{for(y=Ai&&Ai.getFullYear()===Ae,v=Aj&&Aj.getFullYear()===Ae,Z+="<select class='ui-datepicker-month' data-handler='selectMonth' data-event='change'>",w=0;12>w;w++){(!y||w>=Ai.getMonth())&&(!v||w<=Aj.getMonth())&&(Z+="<option value='"+w+"'"+(w===Ad?" selected='selected'":"")+">"+x[w]+"</option>")}Z+="</select>"}if(V||(Y+=Z+(!Ag&&X&&U?"":"&#xa0;")),!Af.yearshtml){if(Af.yearshtml="",Ag||!U){Y+="<span class='ui-datepicker-year'>"+Ae+"</span>"}else{for(Ab=this._get(Af,"yearRange").split(":"),Ac=(new Date).getFullYear(),z=function(d){var c=d.match(/c[+\-].*/)?Ae+parseInt(d.substring(1),10):d.match(/[+\-].*/)?Ac+parseInt(d,10):parseInt(d,10);return isNaN(c)?Ac:c},Aa=z(Ab[0]),W=Math.max(Aa,z(Ab[1]||"")),Aa=Ai?Math.max(Aa,Ai.getFullYear()):Aa,W=Aj?Math.min(W,Aj.getFullYear()):W,Af.yearshtml+="<select class='ui-datepicker-year' data-handler='selectYear' data-event='change'>";W>=Aa;Aa++){Af.yearshtml+="<option value='"+Aa+"'"+(Aa===Ae?" selected='selected'":"")+">"+Aa+"</option>"}Af.yearshtml+="</select>",Y+=Af.yearshtml,Af.yearshtml=null}}return Y+=this._get(Af,"yearSuffix"),V&&(Y+=(!Ag&&X&&U?"":"&#xa0;")+Z),Y+="</div>"},_adjustInstDate:function(Z,X,Y){var V=Z.drawYear+("Y"===Y?X:0),W=Z.drawMonth+("M"===Y?X:0),h=Math.min(Z.selectedDay,this._getDaysInMonth(V,W))+("D"===Y?X:0),U=this._restrictMinMax(Z,this._daylightSavingAdjust(new Date(V,W,h)));Z.selectedDay=U.getDate(),Z.drawMonth=Z.selectedMonth=U.getMonth(),Z.drawYear=Z.selectedYear=U.getFullYear(),("M"===Y||"Y"===Y)&&this._notifyChange(Z)},_restrictMinMax:function(Y,W){var X=this._getMinMaxDate(Y,"min"),U=this._getMinMaxDate(Y,"max"),V=X&&X>W?X:W;return U&&V>U?U:V},_notifyChange:function(V){var U=this._get(V,"onChangeMonthYear");U&&U.apply(V.input?V.input[0]:null,[V.selectedYear,V.selectedMonth+1,V])},_getNumberOfMonths:function(V){var U=this._get(V,"numberOfMonths");return null==U?[1,1]:"number"==typeof U?[1,U]:U},_getMinMaxDate:function(V,U){return this._determineDate(V,this._get(V,U+"Date"),null)},_getDaysInMonth:function(V,U){return 32-this._daylightSavingAdjust(new Date(V,U,32)).getDate()},_getFirstDayOfMonth:function(V,U){return new Date(V,U,1).getDay()},_canAdjustMonth:function(Y,W,X,U){var V=this._getNumberOfMonths(Y),Z=this._daylightSavingAdjust(new Date(X,U+(0>W?W:V[0]*V[1]),1));return 0>W&&Z.setDate(this._getDaysInMonth(Z.getFullYear(),Z.getMonth())),this._isInRange(Y,Z)},_isInRange:function(Z,X){var Y,l,U=this._getMinMaxDate(Z,"min"),j=this._getMinMaxDate(Z,"max"),k=null,V=null,W=this._get(Z,"yearRange");return W&&(Y=W.split(":"),l=(new Date).getFullYear(),k=parseInt(Y[0],10),V=parseInt(Y[1],10),Y[0].match(/[+\-].*/)&&(k+=l),Y[1].match(/[+\-].*/)&&(V+=l)),(!U||X.getTime()>=U.getTime())&&(!j||X.getTime()<=j.getTime())&&(!k||X.getFullYear()>=k)&&(!V||X.getFullYear()<=V)},_getFormatConfig:function(V){var U=this._get(V,"shortYearCutoff");return U="string"!=typeof U?U:(new Date).getFullYear()%100+parseInt(U,10),{shortYearCutoff:U,dayNamesShort:this._get(V,"dayNamesShort"),dayNames:this._get(V,"dayNames"),monthNamesShort:this._get(V,"monthNamesShort"),monthNames:this._get(V,"monthNames")}},_formatDate:function(Y,W,X,U){W||(Y.currentDay=Y.selectedDay,Y.currentMonth=Y.selectedMonth,Y.currentYear=Y.selectedYear);var V=W?"object"==typeof W?W:this._daylightSavingAdjust(new Date(U,X,W)):this._daylightSavingAdjust(new Date(Y.currentYear,Y.currentMonth,Y.currentDay));return this.formatDate(this._get(Y,"dateFormat"),V,this._getFormatConfig(Y))}}),P.fn.datepicker=function(U){if(!this.length){return this}P.datepicker.initialized||(P(document).mousedown(P.datepicker._checkExternalClick),P.datepicker.initialized=!0),0===P("#"+P.datepicker._mainDivId).length&&P("body").append(P.datepicker.dpDiv);var V=Array.prototype.slice.call(arguments,1);return"string"!=typeof U||"isDisabled"!==U&&"getDate"!==U&&"widget"!==U?"option"===U&&2===arguments.length&&"string"==typeof arguments[1]?P.datepicker["_"+U+"Datepicker"].apply(P.datepicker,[this[0]].concat(V)):this.each(function(){"string"==typeof U?P.datepicker["_"+U+"Datepicker"].apply(P.datepicker,[this].concat(V)):P.datepicker._attachDatepicker(this,U)}):P.datepicker["_"+U+"Datepicker"].apply(P.datepicker,[this[0]].concat(V))},P.datepicker=new T,P.datepicker.initialized=!1,P.datepicker.uuid=(new Date).getTime(),P.datepicker.version="1.11.2";var B=(P.datepicker,P.widget("ui.dialog",{version:"1.11.2",options:{appendTo:"body",autoOpen:!0,buttons:[],closeOnEscape:!0,closeText:"Close",dialogClass:"",draggable:!0,hide:null,height:"auto",maxHeight:null,maxWidth:null,minHeight:150,minWidth:150,modal:!1,position:{my:"center",at:"center",of:window,collision:"fit",using:function(U){var V=P(this).css(U).offset().top;0>V&&P(this).css("top",U.top-V)}},resizable:!0,show:null,title:null,width:300,beforeClose:null,close:null,drag:null,dragStart:null,dragStop:null,focus:null,open:null,resize:null,resizeStart:null,resizeStop:null},sizeRelatedOptions:{buttons:!0,height:!0,maxHeight:!0,maxWidth:!0,minHeight:!0,minWidth:!0,width:!0},resizableRelatedOptions:{maxHeight:!0,maxWidth:!0,minHeight:!0,minWidth:!0},_create:function(){this.originalCss={display:this.element[0].style.display,width:this.element[0].style.width,minHeight:this.element[0].style.minHeight,maxHeight:this.element[0].style.maxHeight,height:this.element[0].style.height},this.originalPosition={parent:this.element.parent(),index:this.element.parent().children().index(this.element)},this.originalTitle=this.element.attr("title"),this.options.title=this.options.title||this.originalTitle,this._createWrapper(),this.element.show().removeAttr("title").addClass("ui-dialog-content ui-widget-content").appendTo(this.uiDialog),this._createTitlebar(),this._createButtonPane(),this.options.draggable&&P.fn.draggable&&this._makeDraggable(),this.options.resizable&&P.fn.resizable&&this._makeResizable(),this._isOpen=!1,this._trackFocus()},_init:function(){this.options.autoOpen&&this.open()},_appendTo:function(){var U=this.options.appendTo;return U&&(U.jquery||U.nodeType)?P(U):this.document.find(U||"body").eq(0)},_destroy:function(){var V,U=this.originalPosition;this._destroyOverlay(),this.element.removeUniqueId().removeClass("ui-dialog-content ui-widget-content").css(this.originalCss).detach(),this.uiDialog.stop(!0,!0).remove(),this.originalTitle&&this.element.attr("title",this.originalTitle),V=U.parent.children().eq(U.index),V.length&&V[0]!==this.element[0]?V.before(this.element):U.parent.append(this.element)},widget:function(){return this.uiDialog},disable:P.noop,enable:P.noop,close:function(W){var X,U=this;if(this._isOpen&&this._trigger("beforeClose",W)!==!1){if(this._isOpen=!1,this._focusedElement=null,this._destroyOverlay(),this._untrackInstance(),!this.opener.filter(":focusable").focus().length){try{X=this.document[0].activeElement,X&&"body"!==X.nodeName.toLowerCase()&&P(X).blur()}catch(V){}}this._hide(this.uiDialog,this.options.hide,function(){U._trigger("close",W)})}},isOpen:function(){return this._isOpen},moveToTop:function(){this._moveToTop()},_moveToTop:function(W,X){var U=!1,V=this.uiDialog.siblings(".ui-front:visible").map(function(){return +P(this).css("z-index")}).get(),Y=Math.max.apply(null,V);return Y>=+this.uiDialog.css("z-index")&&(this.uiDialog.css("z-index",Y+1),U=!0),U&&!X&&this._trigger("focus",W),U},open:function(){var U=this;return this._isOpen?void (this._moveToTop()&&this._focusTabbable()):(this._isOpen=!0,this.opener=P(this.document[0].activeElement),this._size(),this._position(),this._createOverlay(),this._moveToTop(null,!0),this.overlay&&this.overlay.css("z-index",this.uiDialog.css("z-index")-1),this._show(this.uiDialog,this.options.show,function(){U._focusTabbable(),U._trigger("focus")}),this._makeFocusTarget(),void this._trigger("open"))},_focusTabbable:function(){var U=this._focusedElement;U||(U=this.element.find("[autofocus]")),U.length||(U=this.element.find(":tabbable")),U.length||(U=this.uiDialogButtonPane.find(":tabbable")),U.length||(U=this.uiDialogTitlebarClose.filter(":tabbable")),U.length||(U=this.uiDialog),U.eq(0).focus()},_keepFocus:function(U){function V(){var W=this.document[0].activeElement,X=this.uiDialog[0]===W||P.contains(this.uiDialog[0],W);X||this._focusTabbable()}U.preventDefault(),V.call(this),this._delay(V)},_createWrapper:function(){this.uiDialog=P("<div>").addClass("ui-dialog ui-widget ui-widget-content ui-corner-all ui-front "+this.options.dialogClass).hide().attr({tabIndex:-1,role:"dialog"}).appendTo(this._appendTo()),this._on(this.uiDialog,{keydown:function(W){if(this.options.closeOnEscape&&!W.isDefaultPrevented()&&W.keyCode&&W.keyCode===P.ui.keyCode.ESCAPE){return W.preventDefault(),void this.close(W)}if(W.keyCode===P.ui.keyCode.TAB&&!W.isDefaultPrevented()){var X=this.uiDialog.find(":tabbable"),U=X.filter(":first"),V=X.filter(":last");W.target!==V[0]&&W.target!==this.uiDialog[0]||W.shiftKey?W.target!==U[0]&&W.target!==this.uiDialog[0]||!W.shiftKey||(this._delay(function(){V.focus()}),W.preventDefault()):(this._delay(function(){U.focus()}),W.preventDefault())}},mousedown:function(U){this._moveToTop(U)&&this._focusTabbable()}}),this.element.find("[aria-describedby]").length||this.uiDialog.attr({"aria-describedby":this.element.uniqueId().attr("id")})},_createTitlebar:function(){var U;this.uiDialogTitlebar=P("<div>").addClass("ui-dialog-titlebar ui-widget-header ui-corner-all ui-helper-clearfix").prependTo(this.uiDialog),this._on(this.uiDialogTitlebar,{mousedown:function(V){P(V.target).closest(".ui-dialog-titlebar-close")||this.uiDialog.focus()}}),this.uiDialogTitlebarClose=P("<button type='button'></button>").button({label:this.options.closeText,icons:{primary:"ui-icon-closethick"},text:!1}).addClass("ui-dialog-titlebar-close").appendTo(this.uiDialogTitlebar),this._on(this.uiDialogTitlebarClose,{click:function(V){V.preventDefault(),this.close(V)}}),U=P("<span>").uniqueId().addClass("ui-dialog-title").prependTo(this.uiDialogTitlebar),this._title(U),this.uiDialog.attr({"aria-labelledby":U.attr("id")})},_title:function(U){this.options.title||U.html("&#160;"),U.text(this.options.title)},_createButtonPane:function(){this.uiDialogButtonPane=P("<div>").addClass("ui-dialog-buttonpane ui-widget-content ui-helper-clearfix"),this.uiButtonSet=P("<div>").addClass("ui-dialog-buttonset").appendTo(this.uiDialogButtonPane),this._createButtons()},_createButtons:function(){var U=this,V=this.options.buttons;return this.uiDialogButtonPane.remove(),this.uiButtonSet.empty(),P.isEmptyObject(V)||P.isArray(V)&&!V.length?void this.uiDialog.removeClass("ui-dialog-buttons"):(P.each(V,function(Y,W){var X,Z;W=P.isFunction(W)?{click:W,text:Y}:W,W=P.extend({type:"button"},W),X=W.click,W.click=function(){X.apply(U.element[0],arguments)},Z={icons:W.icons,text:W.showText},delete W.icons,delete W.showText,P("<button></button>",W).button(Z).appendTo(U.uiButtonSet)}),this.uiDialog.addClass("ui-dialog-buttons"),void this.uiDialogButtonPane.appendTo(this.uiDialog))},_makeDraggable:function(){function V(X){return{position:X.position,offset:X.offset}}var W=this,U=this.options;this.uiDialog.draggable({cancel:".ui-dialog-content, .ui-dialog-titlebar-close",handle:".ui-dialog-titlebar",containment:"document",start:function(X,Y){P(this).addClass("ui-dialog-dragging"),W._blockFrames(),W._trigger("dragStart",X,V(Y))},drag:function(Y,X){W._trigger("drag",Y,V(X))},stop:function(Z,a){var X=a.offset.left-W.document.scrollLeft(),Y=a.offset.top-W.document.scrollTop();U.position={my:"left top",at:"left"+(X>=0?"+":"")+X+" top"+(Y>=0?"+":"")+Y,of:W.window},P(this).removeClass("ui-dialog-dragging"),W._unblockFrames(),W._trigger("dragStop",Z,V(a))}})},_makeResizable:function(){function X(b){return{originalPosition:b.originalPosition,originalSize:b.originalSize,position:b.position,size:b.size}}var Y=this,V=this.options,W=V.resizable,Z=this.uiDialog.css("position"),U="string"==typeof W?W:"n,e,s,w,se,sw,ne,nw";this.uiDialog.resizable({cancel:".ui-dialog-content",containment:"document",alsoResize:this.element,maxWidth:V.maxWidth,maxHeight:V.maxHeight,minWidth:V.minWidth,minHeight:this._minHeight(),handles:U,start:function(a,b){P(this).addClass("ui-dialog-resizing"),Y._blockFrames(),Y._trigger("resizeStart",a,X(b))},resize:function(c,b){Y._trigger("resize",c,X(b))},stop:function(c,j){var a=Y.uiDialog.offset(),b=a.left-Y.document.scrollLeft(),d=a.top-Y.document.scrollTop();V.height=Y.uiDialog.height(),V.width=Y.uiDialog.width(),V.position={my:"left top",at:"left"+(b>=0?"+":"")+b+" top"+(d>=0?"+":"")+d,of:Y.window},P(this).removeClass("ui-dialog-resizing"),Y._unblockFrames(),Y._trigger("resizeStop",c,X(j))}}).css("position",Z)},_trackFocus:function(){this._on(this.widget(),{focusin:function(U){this._makeFocusTarget(),this._focusedElement=P(U.target)}})},_makeFocusTarget:function(){this._untrackInstance(),this._trackingInstances().unshift(this)},_untrackInstance:function(){var U=this._trackingInstances(),V=P.inArray(this,U);-1!==V&&U.splice(V,1)},_trackingInstances:function(){var U=this.document.data("ui-dialog-instances");return U||(U=[],this.document.data("ui-dialog-instances",U)),U},_minHeight:function(){var U=this.options;return"auto"===U.height?U.minHeight:Math.min(U.minHeight,U.height)},_position:function(){var U=this.uiDialog.is(":visible");U||this.uiDialog.show(),this.uiDialog.position(this.options.position),U||this.uiDialog.hide()},_setOptions:function(W){var X=this,U=!1,V={};P.each(W,function(Z,Y){X._setOption(Z,Y),Z in X.sizeRelatedOptions&&(U=!0),Z in X.resizableRelatedOptions&&(V[Z]=Y)}),U&&(this._size(),this._position()),this.uiDialog.is(":data(ui-resizable)")&&this.uiDialog.resizable("option",V)},_setOption:function(Y,W){var X,U,V=this.uiDialog;"dialogClass"===Y&&V.removeClass(this.options.dialogClass).addClass(W),"disabled"!==Y&&(this._super(Y,W),"appendTo"===Y&&this.uiDialog.appendTo(this._appendTo()),"buttons"===Y&&this._createButtons(),"closeText"===Y&&this.uiDialogTitlebarClose.button({label:""+W}),"draggable"===Y&&(X=V.is(":data(ui-draggable)"),X&&!W&&V.draggable("destroy"),!X&&W&&this._makeDraggable()),"position"===Y&&this._position(),"resizable"===Y&&(U=V.is(":data(ui-resizable)"),U&&!W&&V.resizable("destroy"),U&&"string"==typeof W&&V.resizable("option","handles",W),U||W===!1||this._makeResizable()),"title"===Y&&this._title(this.uiDialogTitlebar.find(".ui-dialog-title")))},_size:function(){var X,V,W,U=this.options;this.element.show().css({width:"auto",minHeight:0,maxHeight:"none",height:0}),U.minWidth>U.width&&(U.width=U.minWidth),X=this.uiDialog.css({height:"auto",width:U.width}).outerHeight(),V=Math.max(0,U.minHeight-X),W="number"==typeof U.maxHeight?Math.max(0,U.maxHeight-X):"none","auto"===U.height?this.element.css({minHeight:V,maxHeight:W,height:"auto"}):this.element.height(Math.max(0,U.height-X)),this.uiDialog.is(":data(ui-resizable)")&&this.uiDialog.resizable("option","minHeight",this._minHeight())},_blockFrames:function(){this.iframeBlocks=this.document.find("iframe").map(function(){var U=P(this);return P("<div>").css({position:"absolute",width:U.outerWidth(),height:U.outerHeight()}).appendTo(U.parent()).offset(U.offset())[0]})},_unblockFrames:function(){this.iframeBlocks&&(this.iframeBlocks.remove(),delete this.iframeBlocks)},_allowInteraction:function(U){return P(U.target).closest(".ui-dialog").length?!0:!!P(U.target).closest(".ui-datepicker").length},_createOverlay:function(){if(this.options.modal){var U=!0;this._delay(function(){U=!1}),this.document.data("ui-dialog-overlays")||this._on(this.document,{focusin:function(V){U||this._allowInteraction(V)||(V.preventDefault(),this._trackingInstances()[0]._focusTabbable())}}),this.overlay=P("<div>").addClass("ui-widget-overlay ui-front").appendTo(this._appendTo()),this._on(this.overlay,{mousedown:"_keepFocus"}),this.document.data("ui-dialog-overlays",(this.document.data("ui-dialog-overlays")||0)+1)}},_destroyOverlay:function(){if(this.options.modal&&this.overlay){var U=this.document.data("ui-dialog-overlays")-1;U?this.document.data("ui-dialog-overlays",U):this.document.unbind("focusin").removeData("ui-dialog-overlays"),this.overlay.remove(),this.overlay=null}}}),P.widget("ui.progressbar",{version:"1.11.2",options:{max:100,value:0,change:null,complete:null},min:0,_create:function(){this.oldValue=this.options.value=this._constrainedValue(),this.element.addClass("ui-progressbar ui-widget ui-widget-content ui-corner-all").attr({role:"progressbar","aria-valuemin":this.min}),this.valueDiv=P("<div class='ui-progressbar-value ui-widget-header ui-corner-left'></div>").appendTo(this.element),this._refreshValue()},_destroy:function(){this.element.removeClass("ui-progressbar ui-widget ui-widget-content ui-corner-all").removeAttr("role").removeAttr("aria-valuemin").removeAttr("aria-valuemax").removeAttr("aria-valuenow"),this.valueDiv.remove()},value:function(U){return void 0===U?this.options.value:(this.options.value=this._constrainedValue(U),void this._refreshValue())},_constrainedValue:function(U){return void 0===U&&(U=this.options.value),this.indeterminate=U===!1,"number"!=typeof U&&(U=0),this.indeterminate?!1:Math.min(this.options.max,Math.max(this.min,U))},_setOptions:function(V){var U=V.value;delete V.value,this._super(V),this.options.value=this._constrainedValue(U),this._refreshValue()},_setOption:function(V,U){"max"===V&&(U=Math.max(this.min,U)),"disabled"===V&&this.element.toggleClass("ui-state-disabled",!!U).attr("aria-disabled",U),this._super(V,U)},_percentage:function(){return this.indeterminate?100:100*(this.options.value-this.min)/(this.options.max-this.min)},_refreshValue:function(){var U=this.options.value,V=this._percentage();this.valueDiv.toggle(this.indeterminate||U>this.min).toggleClass("ui-corner-right",U===this.options.max).width(V.toFixed(0)+"%"),this.element.toggleClass("ui-progressbar-indeterminate",this.indeterminate),this.indeterminate?(this.element.removeAttr("aria-valuenow"),this.overlayDiv||(this.overlayDiv=P("<div class='ui-progressbar-overlay'></div>").appendTo(this.valueDiv))):(this.element.attr({"aria-valuemax":this.options.max,"aria-valuenow":U}),this.overlayDiv&&(this.overlayDiv.remove(),this.overlayDiv=null)),this.oldValue!==U&&(this.oldValue=U,this._trigger("change")),U===this.options.max&&this._trigger("complete")}}),P.widget("ui.selectmenu",{version:"1.11.2",defaultElement:"<select>",options:{appendTo:null,disabled:null,icons:{button:"ui-icon-triangle-1-s"},position:{my:"left top",at:"left bottom",collision:"none"},width:null,change:null,close:null,focus:null,open:null,select:null},_create:function(){var U=this.element.uniqueId().attr("id");this.ids={element:U,button:U+"-button",menu:U+"-menu"},this._drawButton(),this._drawMenu(),this.options.disabled&&this.disable()},_drawButton:function(){var U=this,V=this.element.attr("tabindex");this.label=P("label[for='"+this.ids.element+"']").attr("for",this.ids.button),this._on(this.label,{click:function(W){this.button.focus(),W.preventDefault()}}),this.element.hide(),this.button=P("<span>",{"class":"ui-selectmenu-button ui-widget ui-state-default ui-corner-all",tabindex:V||this.options.disabled?-1:0,id:this.ids.button,role:"combobox","aria-expanded":"false","aria-autocomplete":"list","aria-owns":this.ids.menu,"aria-haspopup":"true"}).insertAfter(this.element),P("<span>",{"class":"ui-icon "+this.options.icons.button}).prependTo(this.button),this.buttonText=P("<span>",{"class":"ui-selectmenu-text"}).appendTo(this.button),this._setText(this.buttonText,this.element.find("option:selected").text()),this._resizeButton(),this._on(this.button,this._buttonEvents),this.button.one("focusin",function(){U.menuItems||U._refreshMenu()}),this._hoverable(this.button),this._focusable(this.button)},_drawMenu:function(){var U=this;this.menu=P("<ul>",{"aria-hidden":"true","aria-labelledby":this.ids.button,id:this.ids.menu}),this.menuWrap=P("<div>",{"class":"ui-selectmenu-menu ui-front"}).append(this.menu).appendTo(this._appendTo()),this.menuInstance=this.menu.menu({role:"listbox",select:function(W,V){W.preventDefault(),U._setSelection(),U._select(V.item.data("ui-selectmenu-item"),W)},focus:function(X,W){var V=W.item.data("ui-selectmenu-item");null!=U.focusIndex&&V.index!==U.focusIndex&&(U._trigger("focus",X,{item:V}),U.isOpen||U._select(V,X)),U.focusIndex=V.index,U.button.attr("aria-activedescendant",U.menuItems.eq(V.index).attr("id"))}}).menu("instance"),this.menu.addClass("ui-corner-bottom").removeClass("ui-corner-all"),this.menuInstance._off(this.menu,"mouseleave"),this.menuInstance._closeOnDocumentClick=function(){return !1},this.menuInstance._isDivider=function(){return !1}},refresh:function(){this._refreshMenu(),this._setText(this.buttonText,this._getSelectedItem().text()),this.options.width||this._resizeButton()},_refreshMenu:function(){this.menu.empty();var V,U=this.element.find("option");U.length&&(this._parseOptions(U),this._renderMenu(this.menu,this.items),this.menuInstance.refresh(),this.menuItems=this.menu.find("li").not(".ui-selectmenu-optgroup"),V=this._getSelectedItem(),this.menuInstance.focus(null,V),this._setAria(V.data("ui-selectmenu-item")),this._setOption("disabled",this.element.prop("disabled")))},open:function(U){this.options.disabled||(this.menuItems?(this.menu.find(".ui-state-focus").removeClass("ui-state-focus"),this.menuInstance.focus(null,this._getSelectedItem())):this._refreshMenu(),this.isOpen=!0,this._toggleAttr(),this._resizeMenu(),this._position(),this._on(this.document,this._documentClick),this._trigger("open",U))},_position:function(){this.menuWrap.position(P.extend({of:this.button},this.options.position))},close:function(U){this.isOpen&&(this.isOpen=!1,this._toggleAttr(),this.range=null,this._off(this.document),this._trigger("close",U))},widget:function(){return this.button},menuWidget:function(){return this.menu},_renderMenu:function(W,X){var U=this,V="";P.each(X,function(Y,Z){Z.optgroup!==V&&(P("<li>",{"class":"ui-selectmenu-optgroup ui-menu-divider"+(Z.element.parent("optgroup").prop("disabled")?" ui-state-disabled":""),text:Z.optgroup}).appendTo(W),V=Z.optgroup),U._renderItemData(W,Z)})},_renderItemData:function(V,U){return this._renderItem(V,U).data("ui-selectmenu-item",U)},_renderItem:function(V,W){var U=P("<li>");return W.disabled&&U.addClass("ui-state-disabled"),this._setText(U,W.label),U.appendTo(V)},_setText:function(V,U){U?V.text(U):V.html("&#160;")},_move:function(Y,W){var X,U,V=".ui-menu-item";this.isOpen?X=this.menuItems.eq(this.focusIndex):(X=this.menuItems.eq(this.element[0].selectedIndex),V+=":not(.ui-state-disabled)"),U="first"===Y||"last"===Y?X["first"===Y?"prevAll":"nextAll"](V).eq(-1):X[Y+"All"](V).eq(0),U.length&&this.menuInstance.focus(W,U)},_getSelectedItem:function(){return this.menuItems.eq(this.element[0].selectedIndex)},_toggle:function(U){this[this.isOpen?"close":"open"](U)},_setSelection:function(){var U;this.range&&(window.getSelection?(U=window.getSelection(),U.removeAllRanges(),U.addRange(this.range)):this.range.select(),this.button.focus())},_documentClick:{mousedown:function(U){this.isOpen&&(P(U.target).closest(".ui-selectmenu-menu, #"+this.ids.button).length||this.close(U))}},_buttonEvents:{mousedown:function(){var U;window.getSelection?(U=window.getSelection(),U.rangeCount&&(this.range=U.getRangeAt(0))):this.range=document.selection.createRange()},click:function(U){this._setSelection(),this._toggle(U)},keydown:function(U){var V=!0;switch(U.keyCode){case P.ui.keyCode.TAB:case P.ui.keyCode.ESCAPE:this.close(U),V=!1;break;case P.ui.keyCode.ENTER:this.isOpen&&this._selectFocusedItem(U);break;case P.ui.keyCode.UP:U.altKey?this._toggle(U):this._move("prev",U);break;case P.ui.keyCode.DOWN:U.altKey?this._toggle(U):this._move("next",U);break;case P.ui.keyCode.SPACE:this.isOpen?this._selectFocusedItem(U):this._toggle(U);break;case P.ui.keyCode.LEFT:this._move("prev",U);break;case P.ui.keyCode.RIGHT:this._move("next",U);break;case P.ui.keyCode.HOME:case P.ui.keyCode.PAGE_UP:this._move("first",U);break;case P.ui.keyCode.END:case P.ui.keyCode.PAGE_DOWN:this._move("last",U);break;default:this.menu.trigger(U),V=!1}V&&U.preventDefault()}},_selectFocusedItem:function(V){var U=this.menuItems.eq(this.focusIndex);U.hasClass("ui-state-disabled")||this._select(U.data("ui-selectmenu-item"),V)},_select:function(W,U){var V=this.element[0].selectedIndex;this.element[0].selectedIndex=W.index,this._setText(this.buttonText,W.label),this._setAria(W),this._trigger("select",U,{item:W}),W.index!==V&&this._trigger("change",U,{item:W}),this.close(U)},_setAria:function(V){var U=this.menuItems.eq(V.index).attr("id");this.button.attr({"aria-labelledby":U,"aria-activedescendant":U}),this.menu.attr("aria-activedescendant",U)},_setOption:function(V,U){"icons"===V&&this.button.find("span.ui-icon").removeClass(this.options.icons.button).addClass(U.button),this._super(V,U),"appendTo"===V&&this.menuWrap.appendTo(this._appendTo()),"disabled"===V&&(this.menuInstance.option("disabled",U),this.button.toggleClass("ui-state-disabled",U).attr("aria-disabled",U),this.element.prop("disabled",U),U?(this.button.attr("tabindex",-1),this.close()):this.button.attr("tabindex",0)),"width"===V&&this._resizeButton()},_appendTo:function(){var U=this.options.appendTo;return U&&(U=U.jquery||U.nodeType?P(U):this.document.find(U).eq(0)),U&&U[0]||(U=this.element.closest(".ui-front")),U.length||(U=this.document[0].body),U},_toggleAttr:function(){this.button.toggleClass("ui-corner-top",this.isOpen).toggleClass("ui-corner-all",!this.isOpen).attr("aria-expanded",this.isOpen),this.menuWrap.toggleClass("ui-selectmenu-open",this.isOpen),this.menu.attr("aria-hidden",!this.isOpen)},_resizeButton:function(){var U=this.options.width;U||(U=this.element.show().outerWidth(),this.element.hide()),this.button.outerWidth(U)},_resizeMenu:function(){this.menu.outerWidth(Math.max(this.button.outerWidth(),this.menu.width("").outerWidth()+1))},_getCreateOptions:function(){return{disabled:this.element.prop("disabled")}},_parseOptions:function(U){var V=[];U.each(function(Y,W){var X=P(W),Z=X.parent("optgroup");V.push({element:X,index:Y,value:X.attr("value"),label:X.text(),optgroup:Z.attr("label")||"",disabled:Z.prop("disabled")||X.prop("disabled")})}),this.items=V},_destroy:function(){this.menuWrap.remove(),this.button.remove(),this.element.show(),this.element.removeUniqueId(),this.label.attr("for",this.ids.element)}}),P.widget("ui.slider",P.ui.mouse,{version:"1.11.2",widgetEventPrefix:"slide",options:{animate:!1,distance:0,max:100,min:0,orientation:"horizontal",range:!1,step:1,value:0,values:null,change:null,slide:null,start:null,stop:null},numPages:5,_create:function(){this._keySliding=!1,this._mouseSliding=!1,this._animateOff=!0,this._handleIndex=null,this._detectOrientation(),this._mouseInit(),this._calculateNewMax(),this.element.addClass("ui-slider ui-slider-"+this.orientation+" ui-widget ui-widget-content ui-corner-all"),this._refresh(),this._setOption("disabled",this.options.disabled),this._animateOff=!1},_refresh:function(){this._createRange(),this._createHandles(),this._setupEvents(),this._refreshValue()},_createHandles:function(){var X,Y,V=this.options,W=this.element.find(".ui-slider-handle").addClass("ui-state-default ui-corner-all"),Z="<span class='ui-slider-handle ui-state-default ui-corner-all' tabindex='0'></span>",U=[];for(Y=V.values&&V.values.length||1,W.length>Y&&(W.slice(Y).remove(),W=W.slice(0,Y)),X=W.length;Y>X;X++){U.push(Z)}this.handles=W.add(P(U.join("")).appendTo(this.element)),this.handle=this.handles.eq(0),this.handles.each(function(a){P(this).data("ui-slider-handle-index",a)})},_createRange:function(){var U=this.options,V="";U.range?(U.range===!0&&(U.values?U.values.length&&2!==U.values.length?U.values=[U.values[0],U.values[0]]:P.isArray(U.values)&&(U.values=U.values.slice(0)):U.values=[this._valueMin(),this._valueMin()]),this.range&&this.range.length?this.range.removeClass("ui-slider-range-min ui-slider-range-max").css({left:"",bottom:""}):(this.range=P("<div></div>").appendTo(this.element),V="ui-slider-range ui-widget-header ui-corner-all"),this.range.addClass(V+("min"===U.range||"max"===U.range?" ui-slider-range-"+U.range:""))):(this.range&&this.range.remove(),this.range=null)},_setupEvents:function(){this._off(this.handles),this._on(this.handles,this._handleEvents),this._hoverable(this.handles),this._focusable(this.handles)},_destroy:function(){this.handles.remove(),this.range&&this.range.remove(),this.element.removeClass("ui-slider ui-slider-horizontal ui-slider-vertical ui-widget ui-widget-content ui-corner-all"),this._mouseDestroy()},_mouseCapture:function(a){var m,p,U,n,o,X,Y,V,W=this,Z=this.options;return Z.disabled?!1:(this.elementSize={width:this.element.outerWidth(),height:this.element.outerHeight()},this.elementOffset=this.element.offset(),m={x:a.pageX,y:a.pageY},p=this._normValueFromMouse(m),U=this._valueMax()-this._valueMin()+1,this.handles.each(function(d){var e=Math.abs(p-W.values(d));(U>e||U===e&&(d===W._lastChangedValue||W.values(d)===Z.min))&&(U=e,n=P(this),o=d)}),X=this._start(a,o),X===!1?!1:(this._mouseSliding=!0,this._handleIndex=o,n.addClass("ui-state-active").focus(),Y=n.offset(),V=!P(a.target).parents().addBack().is(".ui-slider-handle"),this._clickOffset=V?{left:0,top:0}:{left:a.pageX-Y.left-n.width()/2,top:a.pageY-Y.top-n.height()/2-(parseInt(n.css("borderTopWidth"),10)||0)-(parseInt(n.css("borderBottomWidth"),10)||0)+(parseInt(n.css("marginTop"),10)||0)},this.handles.hasClass("ui-state-hover")||this._slide(a,o,p),this._animateOff=!0,!0))},_mouseStart:function(){return !0},_mouseDrag:function(W){var U={x:W.pageX,y:W.pageY},V=this._normValueFromMouse(U);return this._slide(W,this._handleIndex,V),!1},_mouseStop:function(U){return this.handles.removeClass("ui-state-active"),this._mouseSliding=!1,this._stop(U,this._handleIndex),this._change(U,this._handleIndex),this._handleIndex=null,this._clickOffset=null,this._animateOff=!1,!1},_detectOrientation:function(){this.orientation="vertical"===this.options.orientation?"vertical":"horizontal"},_normValueFromMouse:function(Y){var W,X,U,V,Z;return"horizontal"===this.orientation?(W=this.elementSize.width,X=Y.x-this.elementOffset.left-(this._clickOffset?this._clickOffset.left:0)):(W=this.elementSize.height,X=Y.y-this.elementOffset.top-(this._clickOffset?this._clickOffset.top:0)),U=X/W,U>1&&(U=1),0>U&&(U=0),"vertical"===this.orientation&&(U=1-U),V=this._valueMax()-this._valueMin(),Z=this._valueMin()+U*V,this._trimAlignValue(Z)},_start:function(W,U){var V={handle:this.handles[U],value:this.value()};return this.options.values&&this.options.values.length&&(V.value=this.values(U),V.values=this.values()),this._trigger("start",W,V)},_slide:function(Y,W,X){var U,V,Z;this.options.values&&this.options.values.length?(U=this.values(W?0:1),2===this.options.values.length&&this.options.range===!0&&(0===W&&X>U||1===W&&U>X)&&(X=U),X!==this.values(W)&&(V=this.values(),V[W]=X,Z=this._trigger("slide",Y,{handle:this.handles[W],value:X,values:V}),U=this.values(W?0:1),Z!==!1&&this.values(W,X))):X!==this.value()&&(Z=this._trigger("slide",Y,{handle:this.handles[W],value:X}),Z!==!1&&this.value(X))},_stop:function(W,U){var V={handle:this.handles[U],value:this.value()};this.options.values&&this.options.values.length&&(V.value=this.values(U),V.values=this.values()),this._trigger("stop",W,V)},_change:function(W,U){if(!this._keySliding&&!this._mouseSliding){var V={handle:this.handles[U],value:this.value()};this.options.values&&this.options.values.length&&(V.value=this.values(U),V.values=this.values()),this._lastChangedValue=U,this._trigger("change",W,V)}},value:function(U){return arguments.length?(this.options.value=this._trimAlignValue(U),this._refreshValue(),void this._change(null,0)):this._value()},values:function(W,X){var U,V,Y;if(arguments.length>1){return this.options.values[W]=this._trimAlignValue(X),this._refreshValue(),void this._change(null,W)}if(!arguments.length){return this._values()}if(!P.isArray(arguments[0])){return this.options.values&&this.options.values.length?this._values(W):this.value()}for(U=this.options.values,V=arguments[0],Y=0;Y<U.length;Y+=1){U[Y]=this._trimAlignValue(V[Y]),this._change(null,Y)}this._refreshValue()},_setOption:function(W,X){var U,V=0;switch("range"===W&&this.options.range===!0&&("min"===X?(this.options.value=this._values(0),this.options.values=null):"max"===X&&(this.options.value=this._values(this.options.values.length-1),this.options.values=null)),P.isArray(this.options.values)&&(V=this.options.values.length),"disabled"===W&&this.element.toggleClass("ui-state-disabled",!!X),this._super(W,X),W){case"orientation":this._detectOrientation(),this.element.removeClass("ui-slider-horizontal ui-slider-vertical").addClass("ui-slider-"+this.orientation),this._refreshValue(),this.handles.css("horizontal"===X?"bottom":"left","");break;case"value":this._animateOff=!0,this._refreshValue(),this._change(null,0),this._animateOff=!1;break;case"values":for(this._animateOff=!0,this._refreshValue(),U=0;V>U;U+=1){this._change(null,U)}this._animateOff=!1;break;case"step":case"min":case"max":this._animateOff=!0,this._calculateNewMax(),this._refreshValue(),this._animateOff=!1;break;case"range":this._animateOff=!0,this._refresh(),this._animateOff=!1}},_value:function(){var U=this.options.value;return U=this._trimAlignValue(U)},_values:function(X){var V,W,U;if(arguments.length){return V=this.options.values[X],V=this._trimAlignValue(V)}if(this.options.values&&this.options.values.length){for(W=this.options.values.slice(),U=0;U<W.length;U+=1){W[U]=this._trimAlignValue(W[U])}return W}return[]},_trimAlignValue:function(X){if(X<=this._valueMin()){return this._valueMin()}if(X>=this._valueMax()){return this._valueMax()}var V=this.options.step>0?this.options.step:1,W=(X-this._valueMin())%V,U=X-W;return 2*Math.abs(W)>=V&&(U+=W>0?V:-V),parseFloat(U.toFixed(5))},_calculateNewMax:function(){var U=(this.options.max-this._valueMin())%this.options.step;this.max=this.options.max-U},_valueMin:function(){return this.options.min},_valueMax:function(){return this.max},_refreshValue:function(){var Z,a,n,U,l,m=this.options.range,X=this.options,Y=this,V=this._animateOff?!1:X.animate,W={};this.options.values&&this.options.values.length?this.handles.each(function(b){a=(Y.values(b)-Y._valueMin())/(Y._valueMax()-Y._valueMin())*100,W["horizontal"===Y.orientation?"left":"bottom"]=a+"%",P(this).stop(1,1)[V?"animate":"css"](W,X.animate),Y.options.range===!0&&("horizontal"===Y.orientation?(0===b&&Y.range.stop(1,1)[V?"animate":"css"]({left:a+"%"},X.animate),1===b&&Y.range[V?"animate":"css"]({width:a-Z+"%"},{queue:!1,duration:X.animate})):(0===b&&Y.range.stop(1,1)[V?"animate":"css"]({bottom:a+"%"},X.animate),1===b&&Y.range[V?"animate":"css"]({height:a-Z+"%"},{queue:!1,duration:X.animate}))),Z=a}):(n=this.value(),U=this._valueMin(),l=this._valueMax(),a=l!==U?(n-U)/(l-U)*100:0,W["horizontal"===this.orientation?"left":"bottom"]=a+"%",this.handle.stop(1,1)[V?"animate":"css"](W,X.animate),"min"===m&&"horizontal"===this.orientation&&this.range.stop(1,1)[V?"animate":"css"]({width:a+"%"},X.animate),"max"===m&&"horizontal"===this.orientation&&this.range[V?"animate":"css"]({width:100-a+"%"},{queue:!1,duration:X.animate}),"min"===m&&"vertical"===this.orientation&&this.range.stop(1,1)[V?"animate":"css"]({height:a+"%"},X.animate),"max"===m&&"vertical"===this.orientation&&this.range[V?"animate":"css"]({height:100-a+"%"},{queue:!1,duration:X.animate}))},_handleEvents:{keydown:function(X){var Y,V,W,Z,U=P(X.target).data("ui-slider-handle-index");switch(X.keyCode){case P.ui.keyCode.HOME:case P.ui.keyCode.END:case P.ui.keyCode.PAGE_UP:case P.ui.keyCode.PAGE_DOWN:case P.ui.keyCode.UP:case P.ui.keyCode.RIGHT:case P.ui.keyCode.DOWN:case P.ui.keyCode.LEFT:if(X.preventDefault(),!this._keySliding&&(this._keySliding=!0,P(X.target).addClass("ui-state-active"),Y=this._start(X,U),Y===!1)){return}}switch(Z=this.options.step,V=W=this.options.values&&this.options.values.length?this.values(U):this.value(),X.keyCode){case P.ui.keyCode.HOME:W=this._valueMin();break;case P.ui.keyCode.END:W=this._valueMax();break;case P.ui.keyCode.PAGE_UP:W=this._trimAlignValue(V+(this._valueMax()-this._valueMin())/this.numPages);break;case P.ui.keyCode.PAGE_DOWN:W=this._trimAlignValue(V-(this._valueMax()-this._valueMin())/this.numPages);break;case P.ui.keyCode.UP:case P.ui.keyCode.RIGHT:if(V===this._valueMax()){return}W=this._trimAlignValue(V+Z);break;case P.ui.keyCode.DOWN:case P.ui.keyCode.LEFT:if(V===this._valueMin()){return}W=this._trimAlignValue(V-Z)}this._slide(X,U,W)},keyup:function(U){var V=P(U.target).data("ui-slider-handle-index");this._keySliding&&(this._keySliding=!1,this._stop(U,V),this._change(U,V),P(U.target).removeClass("ui-state-active"))}}}),P.widget("ui.spinner",{version:"1.11.2",defaultElement:"<input>",widgetEventPrefix:"spin",options:{culture:null,icons:{down:"ui-icon-triangle-1-s",up:"ui-icon-triangle-1-n"},incremental:!0,max:null,min:null,numberFormat:null,page:10,step:1,change:null,spin:null,start:null,stop:null},_create:function(){this._setOption("max",this.options.max),this._setOption("min",this.options.min),this._setOption("step",this.options.step),""!==this.value()&&this._value(this.element.val(),!0),this._draw(),this._on(this._events),this._refresh(),this._on(this.window,{beforeunload:function(){this.element.removeAttr("autocomplete")}})},_getCreateOptions:function(){var U={},V=this.element;return P.each(["min","max","step"],function(Y,W){var X=V.attr(W);void 0!==X&&X.length&&(U[W]=X)}),U},_events:{keydown:function(U){this._start(U)&&this._keydown(U)&&U.preventDefault()},keyup:"_stop",focus:function(){this.previous=this.element.val()},blur:function(U){return this.cancelBlur?void delete this.cancelBlur:(this._stop(),this._refresh(),void (this.previous!==this.element.val()&&this._trigger("change",U)))},mousewheel:function(V,U){if(U){if(!this.spinning&&!this._start(V)){return !1}this._spin((U>0?1:-1)*this.options.step,V),clearTimeout(this.mousewheelTimer),this.mousewheelTimer=this._delay(function(){this.spinning&&this._stop(V)},100),V.preventDefault()}},"mousedown .ui-spinner-button":function(V){function W(){var X=this.element[0]===this.document[0].activeElement;X||(this.element.focus(),this.previous=U,this._delay(function(){this.previous=U}))}var U;U=this.element[0]===this.document[0].activeElement?this.previous:this.element.val(),V.preventDefault(),W.call(this),this.cancelBlur=!0,this._delay(function(){delete this.cancelBlur,W.call(this)}),this._start(V)!==!1&&this._repeat(null,P(V.currentTarget).hasClass("ui-spinner-up")?1:-1,V)},"mouseup .ui-spinner-button":"_stop","mouseenter .ui-spinner-button":function(U){return P(U.currentTarget).hasClass("ui-state-active")?this._start(U)===!1?!1:void this._repeat(null,P(U.currentTarget).hasClass("ui-spinner-up")?1:-1,U):void 0},"mouseleave .ui-spinner-button":"_stop"},_draw:function(){var U=this.uiSpinner=this.element.addClass("ui-spinner-input").attr("autocomplete","off").wrap(this._uiSpinnerHtml()).parent().append(this._buttonHtml());this.element.attr("role","spinbutton"),this.buttons=U.find(".ui-spinner-button").attr("tabIndex",-1).button().removeClass("ui-corner-all"),this.buttons.height()>Math.ceil(0.5*U.height())&&U.height()>0&&U.height(U.height()),this.options.disabled&&this.disable()},_keydown:function(V){var W=this.options,U=P.ui.keyCode;switch(V.keyCode){case U.UP:return this._repeat(null,1,V),!0;case U.DOWN:return this._repeat(null,-1,V),!0;case U.PAGE_UP:return this._repeat(null,W.page,V),!0;case U.PAGE_DOWN:return this._repeat(null,-W.page,V),!0}return !1},_uiSpinnerHtml:function(){return"<span class='ui-spinner ui-widget ui-widget-content ui-corner-all'></span>"},_buttonHtml:function(){return"<a class='ui-spinner-button ui-spinner-up ui-corner-tr'><span class='ui-icon "+this.options.icons.up+"'>&#9650;</span></a><a class='ui-spinner-button ui-spinner-down ui-corner-br'><span class='ui-icon "+this.options.icons.down+"'>&#9660;</span></a>"},_start:function(U){return this.spinning||this._trigger("start",U)!==!1?(this.counter||(this.counter=1),this.spinning=!0,!0):!1},_repeat:function(W,U,V){W=W||500,clearTimeout(this.timer),this.timer=this._delay(function(){this._repeat(40,U,V)},W),this._spin(U*this.options.step,V)},_spin:function(W,U){var V=this.value()||0;this.counter||(this.counter=1),V=this._adjustValue(V+W*this._increment(this.counter)),this.spinning&&this._trigger("spin",U,{value:V})===!1||(this._value(V),this.counter++)},_increment:function(U){var V=this.options.incremental;return V?P.isFunction(V)?V(U):Math.floor(U*U*U/50000-U*U/500+17*U/200+1):1},_precision:function(){var U=this._precisionOf(this.options.step);return null!==this.options.min&&(U=Math.max(U,this._precisionOf(this.options.min))),U},_precisionOf:function(W){var U=W.toString(),V=U.indexOf(".");return -1===V?0:U.length-V-1},_adjustValue:function(X){var V,W,U=this.options;return V=null!==U.min?U.min:0,W=X-V,W=Math.round(W/U.step)*U.step,X=V+W,X=parseFloat(X.toFixed(this._precision())),null!==U.max&&X>U.max?U.max:null!==U.min&&X<U.min?U.min:X},_stop:function(U){this.spinning&&(clearTimeout(this.timer),clearTimeout(this.mousewheelTimer),this.counter=0,this.spinning=!1,this._trigger("stop",U))},_setOption:function(W,U){if("culture"===W||"numberFormat"===W){var V=this._parse(this.element.val());return this.options[W]=U,void this.element.val(this._format(V))}("max"===W||"min"===W||"step"===W)&&"string"==typeof U&&(U=this._parse(U)),"icons"===W&&(this.buttons.first().find(".ui-icon").removeClass(this.options.icons.up).addClass(U.up),this.buttons.last().find(".ui-icon").removeClass(this.options.icons.down).addClass(U.down)),this._super(W,U),"disabled"===W&&(this.widget().toggleClass("ui-state-disabled",!!U),this.element.prop("disabled",!!U),this.buttons.button(U?"disable":"enable"))},_setOptions:I(function(U){this._super(U)}),_parse:function(U){return"string"==typeof U&&""!==U&&(U=window.Globalize&&this.options.numberFormat?Globalize.parseFloat(U,10,this.options.culture):+U),""===U||isNaN(U)?null:U},_format:function(U){return""===U?"":window.Globalize&&this.options.numberFormat?Globalize.format(U,this.options.numberFormat,this.options.culture):U},_refresh:function(){this.element.attr({"aria-valuemin":this.options.min,"aria-valuemax":this.options.max,"aria-valuenow":this._parse(this.element.val())})},isValid:function(){var U=this.value();return null===U?!1:U===this._adjustValue(U)},_value:function(W,U){var V;""!==W&&(V=this._parse(W),null!==V&&(U||(V=this._adjustValue(V)),W=this._format(V))),this.element.val(W),this._refresh()},_destroy:function(){this.element.removeClass("ui-spinner-input").prop("disabled",!1).removeAttr("autocomplete").removeAttr("role").removeAttr("aria-valuemin").removeAttr("aria-valuemax").removeAttr("aria-valuenow"),this.uiSpinner.replaceWith(this.element)},stepUp:I(function(U){this._stepUp(U)}),_stepUp:function(U){this._start()&&(this._spin((U||1)*this.options.step),this._stop())},stepDown:I(function(U){this._stepDown(U)}),_stepDown:function(U){this._start()&&(this._spin((U||1)*-this.options.step),this._stop())},pageUp:I(function(U){this._stepUp((U||1)*this.options.page)}),pageDown:I(function(U){this._stepDown((U||1)*this.options.page)}),value:function(U){return arguments.length?void I(this._value).call(this,U):this._parse(this.element.val())},widget:function(){return this.uiSpinner}}),P.widget("ui.tabs",{version:"1.11.2",delay:300,options:{active:null,collapsible:!1,event:"click",heightStyle:"content",hide:null,show:null,activate:null,beforeActivate:null,beforeLoad:null,load:null},_isLocal:function(){var U=/#.*$/;return function(X){var Y,V;X=X.cloneNode(!1),Y=X.href.replace(U,""),V=location.href.replace(U,"");try{Y=decodeURIComponent(Y)}catch(W){}try{V=decodeURIComponent(V)}catch(W){}return X.hash.length>1&&Y===V}}(),_create:function(){var U=this,V=this.options;this.running=!1,this.element.addClass("ui-tabs ui-widget ui-widget-content ui-corner-all").toggleClass("ui-tabs-collapsible",V.collapsible),this._processTabs(),V.active=this._initialActive(),P.isArray(V.disabled)&&(V.disabled=P.unique(V.disabled.concat(P.map(this.tabs.filter(".ui-state-disabled"),function(W){return U.tabs.index(W)}))).sort()),this.active=this.options.active!==!1&&this.anchors.length?this._findActive(V.active):P(),this._refresh(),this.active.length&&this.load(V.active)},_initialActive:function(){var V=this.options.active,W=this.options.collapsible,U=location.hash.substring(1);return null===V&&(U&&this.tabs.each(function(Y,X){return P(X).attr("aria-controls")===U?(V=Y,!1):void 0}),null===V&&(V=this.tabs.index(this.tabs.filter(".ui-tabs-active"))),(null===V||-1===V)&&(V=this.tabs.length?0:!1)),V!==!1&&(V=this.tabs.index(this.tabs.eq(V)),-1===V&&(V=W?!1:0)),!W&&V===!1&&this.anchors.length&&(V=0),V},_getCreateEventData:function(){return{tab:this.active,panel:this.active.length?this._getPanelForTab(this.active):P()}},_tabKeydown:function(W){var X=P(this.document[0].activeElement).closest("li"),U=this.tabs.index(X),V=!0;if(!this._handlePageNav(W)){switch(W.keyCode){case P.ui.keyCode.RIGHT:case P.ui.keyCode.DOWN:U++;break;case P.ui.keyCode.UP:case P.ui.keyCode.LEFT:V=!1,U--;break;case P.ui.keyCode.END:U=this.anchors.length-1;break;case P.ui.keyCode.HOME:U=0;break;case P.ui.keyCode.SPACE:return W.preventDefault(),clearTimeout(this.activating),void this._activate(U);case P.ui.keyCode.ENTER:return W.preventDefault(),clearTimeout(this.activating),void this._activate(U===this.options.active?!1:U);default:return}W.preventDefault(),clearTimeout(this.activating),U=this._focusNextTab(U,V),W.ctrlKey||(X.attr("aria-selected","false"),this.tabs.eq(U).attr("aria-selected","true"),this.activating=this._delay(function(){this.option("active",U)},this.delay))}},_panelKeydown:function(U){this._handlePageNav(U)||U.ctrlKey&&U.keyCode===P.ui.keyCode.UP&&(U.preventDefault(),this.active.focus())},_handlePageNav:function(U){return U.altKey&&U.keyCode===P.ui.keyCode.PAGE_UP?(this._activate(this._focusNextTab(this.options.active-1,!1)),!0):U.altKey&&U.keyCode===P.ui.keyCode.PAGE_DOWN?(this._activate(this._focusNextTab(this.options.active+1,!0)),!0):void 0},_findNextTab:function(W,X){function U(){return W>V&&(W=0),0>W&&(W=V),W}for(var V=this.tabs.length-1;-1!==P.inArray(U(),this.options.disabled);){W=X?W+1:W-1}return W},_focusNextTab:function(V,U){return V=this._findNextTab(V,U),this.tabs.eq(V).focus(),V},_setOption:function(V,U){return"active"===V?void this._activate(U):"disabled"===V?void this._setupDisabled(U):(this._super(V,U),"collapsible"===V&&(this.element.toggleClass("ui-tabs-collapsible",U),U||this.options.active!==!1||this._activate(0)),"event"===V&&this._setupEvents(U),void ("heightStyle"===V&&this._setupHeightStyle(U)))},_sanitizeSelector:function(U){return U?U.replace(/[!"$%&'()*+,.\/:;<=>?@\[\]\^`{|}~]/g,"\\$&"):""},refresh:function(){var U=this.options,V=this.tablist.children(":has(a[href])");U.disabled=P.map(V.filter(".ui-state-disabled"),function(W){return V.index(W)}),this._processTabs(),U.active!==!1&&this.anchors.length?this.active.length&&!P.contains(this.tablist[0],this.active[0])?this.tabs.length===U.disabled.length?(U.active=!1,this.active=P()):this._activate(this._findNextTab(Math.max(0,U.active-1),!1)):U.active=this.tabs.index(this.active):(U.active=!1,this.active=P()),this._refresh()},_refresh:function(){this._setupDisabled(this.options.disabled),this._setupEvents(this.options.event),this._setupHeightStyle(this.options.heightStyle),this.tabs.not(this.active).attr({"aria-selected":"false","aria-expanded":"false",tabIndex:-1}),this.panels.not(this._getPanelForTab(this.active)).hide().attr({"aria-hidden":"true"}),this.active.length?(this.active.addClass("ui-tabs-active ui-state-active").attr({"aria-selected":"true","aria-expanded":"true",tabIndex:0}),this._getPanelForTab(this.active).show().attr({"aria-hidden":"false"})):this.tabs.eq(0).attr("tabIndex",0)},_processTabs:function(){var W=this,X=this.tabs,U=this.anchors,V=this.panels;this.tablist=this._getList().addClass("ui-tabs-nav ui-helper-reset ui-helper-clearfix ui-widget-header ui-corner-all").attr("role","tablist").delegate("> li","mousedown"+this.eventNamespace,function(Y){P(this).is(".ui-state-disabled")&&Y.preventDefault()}).delegate(".ui-tabs-anchor","focus"+this.eventNamespace,function(){P(this).closest("li").is(".ui-state-disabled")&&this.blur()}),this.tabs=this.tablist.find("> li:has(a[href])").addClass("ui-state-default ui-corner-top").attr({role:"tab",tabIndex:-1}),this.anchors=this.tabs.map(function(){return P("a",this)[0]}).addClass("ui-tabs-anchor").attr({role:"presentation",tabIndex:-1}),this.panels=P(),this.anchors.each(function(k,n){var Y,l,m,a=P(n).uniqueId().attr("id"),b=P(n).closest("li"),Z=b.attr("aria-controls");W._isLocal(n)?(Y=n.hash,m=Y.substring(1),l=W.element.find(W._sanitizeSelector(Y))):(m=b.attr("aria-controls")||P({}).uniqueId()[0].id,Y="#"+m,l=W.element.find(Y),l.length||(l=W._createPanel(m),l.insertAfter(W.panels[k-1]||W.tablist)),l.attr("aria-live","polite")),l.length&&(W.panels=W.panels.add(l)),Z&&b.data("ui-tabs-aria-controls",Z),b.attr({"aria-controls":m,"aria-labelledby":a}),l.attr("aria-labelledby",a)}),this.panels.addClass("ui-tabs-panel ui-widget-content ui-corner-bottom").attr("role","tabpanel"),X&&(this._off(X.not(this.tabs)),this._off(U.not(this.anchors)),this._off(V.not(this.panels)))},_getList:function(){return this.tablist||this.element.find("ol,ul").eq(0)},_createPanel:function(U){return P("<div>").attr("id",U).addClass("ui-tabs-panel ui-widget-content ui-corner-bottom").data("ui-tabs-destroy",!0)},_setupDisabled:function(V){P.isArray(V)&&(V.length?V.length===this.anchors.length&&(V=!0):V=!1);for(var W,U=0;W=this.tabs[U];U++){V===!0||-1!==P.inArray(U,V)?P(W).addClass("ui-state-disabled").attr("aria-disabled","true"):P(W).removeClass("ui-state-disabled").removeAttr("aria-disabled")}this.options.disabled=V},_setupEvents:function(U){var V={};U&&P.each(U.split(" "),function(X,W){V[W]="_eventHandler"}),this._off(this.anchors.add(this.tabs).add(this.panels)),this._on(!0,this.anchors,{click:function(W){W.preventDefault()}}),this._on(this.anchors,V),this._on(this.tabs,{keydown:"_tabKeydown"}),this._on(this.panels,{keydown:"_panelKeydown"}),this._focusable(this.tabs),this._hoverable(this.tabs)},_setupHeightStyle:function(V){var W,U=this.element.parent();"fill"===V?(W=U.height(),W-=this.element.outerHeight()-this.element.height(),this.element.siblings(":visible").each(function(){var Y=P(this),X=Y.css("position");"absolute"!==X&&"fixed"!==X&&(W-=Y.outerHeight(!0))}),this.element.children().not(this.panels).each(function(){W-=P(this).outerHeight(!0)}),this.panels.each(function(){P(this).height(Math.max(0,W-P(this).innerHeight()+P(this).height()))}).css("overflow","auto")):"auto"===V&&(W=0,this.panels.each(function(){W=Math.max(W,P(this).height("").height())}).height(W))},_eventHandler:function(Z){var a=this.options,n=this.active,U=P(Z.currentTarget),l=U.closest("li"),m=l[0]===n[0],X=m&&a.collapsible,Y=X?P():this._getPanelForTab(l),V=n.length?this._getPanelForTab(n):P(),W={oldTab:n,oldPanel:V,newTab:X?P():l,newPanel:Y};Z.preventDefault(),l.hasClass("ui-state-disabled")||l.hasClass("ui-tabs-loading")||this.running||m&&!a.collapsible||this._trigger("beforeActivate",Z,W)===!1||(a.active=X?!1:this.tabs.index(l),this.active=m?P():l,this.xhr&&this.xhr.abort(),V.length||Y.length||P.error("jQuery UI Tabs: Mismatching fragment identifier."),Y.length&&this.load(this.tabs.index(l),Z),this._toggle(Z,W))},_toggle:function(Y,Z){function V(){a.running=!1,a._trigger("activate",Y,Z)}function W(){Z.newTab.closest("li").addClass("ui-tabs-active ui-state-active"),U.length&&a.options.show?a._show(U,a.options.show,V):(U.show(),V())}var a=this,U=Z.newPanel,X=Z.oldPanel;this.running=!0,X.length&&this.options.hide?this._hide(X,this.options.hide,function(){Z.oldTab.closest("li").removeClass("ui-tabs-active ui-state-active"),W()}):(Z.oldTab.closest("li").removeClass("ui-tabs-active ui-state-active"),X.hide(),W()),X.attr("aria-hidden","true"),Z.oldTab.attr({"aria-selected":"false","aria-expanded":"false"}),U.length&&X.length?Z.oldTab.attr("tabIndex",-1):U.length&&this.tabs.filter(function(){return 0===P(this).attr("tabIndex")}).attr("tabIndex",-1),U.attr("aria-hidden","false"),Z.newTab.attr({"aria-selected":"true","aria-expanded":"true",tabIndex:0})},_activate:function(V){var W,U=this._findActive(V);U[0]!==this.active[0]&&(U.length||(U=this.active),W=U.find(".ui-tabs-anchor")[0],this._eventHandler({target:W,currentTarget:W,preventDefault:P.noop}))},_findActive:function(U){return U===!1?P():this.tabs.eq(U)},_getIndex:function(U){return"string"==typeof U&&(U=this.anchors.index(this.anchors.filter("[href$='"+U+"']"))),U},_destroy:function(){this.xhr&&this.xhr.abort(),this.element.removeClass("ui-tabs ui-widget ui-widget-content ui-corner-all ui-tabs-collapsible"),this.tablist.removeClass("ui-tabs-nav ui-helper-reset ui-helper-clearfix ui-widget-header ui-corner-all").removeAttr("role"),this.anchors.removeClass("ui-tabs-anchor").removeAttr("role").removeAttr("tabIndex").removeUniqueId(),this.tablist.unbind(this.eventNamespace),this.tabs.add(this.panels).each(function(){P.data(this,"ui-tabs-destroy")?P(this).remove():P(this).removeClass("ui-state-default ui-state-active ui-state-disabled ui-corner-top ui-corner-bottom ui-widget-content ui-tabs-active ui-tabs-panel").removeAttr("tabIndex").removeAttr("aria-live").removeAttr("aria-busy").removeAttr("aria-selected").removeAttr("aria-labelledby").removeAttr("aria-hidden").removeAttr("aria-expanded").removeAttr("role")}),this.tabs.each(function(){var U=P(this),V=U.data("ui-tabs-aria-controls");V?U.attr("aria-controls",V).removeData("ui-tabs-aria-controls"):U.removeAttr("aria-controls")}),this.panels.show(),"content"!==this.options.heightStyle&&this.panels.css("height","")},enable:function(U){var V=this.options.disabled;V!==!1&&(void 0===U?V=!1:(U=this._getIndex(U),V=P.isArray(V)?P.map(V,function(W){return W!==U?W:null}):P.map(this.tabs,function(X,W){return W!==U?W:null})),this._setupDisabled(V))},disable:function(U){var V=this.options.disabled;if(V!==!0){if(void 0===U){V=!0}else{if(U=this._getIndex(U),-1!==P.inArray(U,V)){return}V=P.isArray(V)?P.merge([U],V).sort():[U]}this._setupDisabled(V)}},load:function(Y,Z){Y=this._getIndex(Y);var V=this,W=this.tabs.eq(Y),a=W.find(".ui-tabs-anchor"),U=this._getPanelForTab(W),X={tab:W,panel:U};this._isLocal(a[0])||(this.xhr=P.ajax(this._ajaxSettings(a,Z,X)),this.xhr&&"canceled"!==this.xhr.statusText&&(W.addClass("ui-tabs-loading"),U.attr("aria-busy","true"),this.xhr.success(function(b){setTimeout(function(){U.html(b),V._trigger("load",Z,X)},1)}).complete(function(d,c){setTimeout(function(){"abort"===c&&V.panels.stop(!1,!0),W.removeClass("ui-tabs-loading"),U.removeAttr("aria-busy"),d===V.xhr&&delete V.xhr},1)})))},_ajaxSettings:function(W,X,U){var V=this;return{url:W.attr("href"),beforeSend:function(Y,Z){return V._trigger("beforeLoad",X,P.extend({jqXHR:Y,ajaxSettings:Z},U))}}},_getPanelForTab:function(U){var V=P(U).attr("aria-controls");return this.element.find(this._sanitizeSelector("#"+V))}}),P.widget("ui.tooltip",{version:"1.11.2",options:{content:function(){var U=P(this).attr("title")||"";return P("<a>").text(U).html()},hide:!0,items:"[title]:not([disabled])",position:{my:"left top+15",at:"left bottom",collision:"flipfit flip"},show:!0,tooltipClass:null,track:!1,close:null,open:null},_addDescribedBy:function(V,W){var U=(V.attr("aria-describedby")||"").split(/\s+/);U.push(W),V.data("ui-tooltip-id",W).attr("aria-describedby",P.trim(U.join(" ")))},_removeDescribedBy:function(W){var X=W.data("ui-tooltip-id"),U=(W.attr("aria-describedby")||"").split(/\s+/),V=P.inArray(X,U);-1!==V&&U.splice(V,1),W.removeData("ui-tooltip-id"),U=P.trim(U.join(" ")),U?W.attr("aria-describedby",U):W.removeAttr("aria-describedby")},_create:function(){this._on({mouseover:"open",focusin:"open"}),this.tooltips={},this.parents={},this.options.disabled&&this._disable(),this.liveRegion=P("<div>").attr({role:"log","aria-live":"assertive","aria-relevant":"additions"}).addClass("ui-helper-hidden-accessible").appendTo(this.document[0].body)},_setOption:function(V,W){var U=this;return"disabled"===V?(this[W?"_disable":"_enable"](),void (this.options[V]=W)):(this._super(V,W),void ("content"===V&&P.each(this.tooltips,function(Y,X){U._updateContent(X.element)})))},_disable:function(){var U=this;P.each(this.tooltips,function(X,V){var W=P.Event("blur");W.target=W.currentTarget=V.element[0],U.close(W,!0)}),this.element.find(this.options.items).addBack().each(function(){var V=P(this);V.is("[title]")&&V.data("ui-tooltip-title",V.attr("title")).removeAttr("title")})},_enable:function(){this.element.find(this.options.items).addBack().each(function(){var U=P(this);U.data("ui-tooltip-title")&&U.attr("title",U.data("ui-tooltip-title"))})},open:function(V){var W=this,U=P(V?V.target:this.element).closest(this.options.items);U.length&&!U.data("ui-tooltip-id")&&(U.attr("title")&&U.data("ui-tooltip-title",U.attr("title")),U.data("ui-tooltip-open",!0),V&&"mouseover"===V.type&&U.parents().each(function(){var Y,X=P(this);X.data("ui-tooltip-open")&&(Y=P.Event("blur"),Y.target=Y.currentTarget=this,W.close(Y,!0)),X.attr("title")&&(X.uniqueId(),W.parents[this.id]={element:this,title:X.attr("title")},X.attr("title",""))}),this._updateContent(U,V))},_updateContent:function(Y,W){var X,U=this.options.content,V=this,Z=W?W.type:null;return"string"==typeof U?this._open(W,Y,U):(X=U.call(Y[0],function(a){Y.data("ui-tooltip-open")&&V._delay(function(){W&&(W.type=Z),this._open(W,Y,a)})}),void (X&&this._open(W,Y,X)))},_open:function(Z,a,n){function U(b){W.of=b,m.is(":hidden")||m.position(W)}var l,m,X,Y,V,W=P.extend({},this.options.position);if(n){if(l=this._find(a)){return void l.tooltip.find(".ui-tooltip-content").html(n)}a.is("[title]")&&(Z&&"mouseover"===Z.type?a.attr("title",""):a.removeAttr("title")),l=this._tooltip(a),m=l.tooltip,this._addDescribedBy(a,m.attr("id")),m.find(".ui-tooltip-content").html(n),this.liveRegion.children().hide(),n.clone?(V=n.clone(),V.removeAttr("id").find("[id]").removeAttr("id")):V=n,P("<div>").html(V).appendTo(this.liveRegion),this.options.track&&Z&&/^mouse/.test(Z.type)?(this._on(this.document,{mousemove:U}),U(Z)):m.position(P.extend({of:a},this.options.position)),m.hide(),this._show(m,this.options.show),this.options.show&&this.options.show.delay&&(Y=this.delayedShow=setInterval(function(){m.is(":visible")&&(U(W.of),clearInterval(Y))},P.fx.interval)),this._trigger("open",Z,{tooltip:m}),X={keyup:function(e){if(e.keyCode===P.ui.keyCode.ESCAPE){var c=P.Event(e);c.currentTarget=a[0],this.close(c,!0)}}},a[0]!==this.element[0]&&(X.remove=function(){this._removeTooltip(m)}),Z&&"mouseover"!==Z.type||(X.mouseleave="close"),Z&&"focusin"!==Z.type||(X.focusout="close"),this._on(!0,a,X)}},close:function(W){var X,U=this,V=P(W?W.currentTarget:this.element),Y=this._find(V);Y&&(X=Y.tooltip,Y.closing||(clearInterval(this.delayedShow),V.data("ui-tooltip-title")&&!V.attr("title")&&V.attr("title",V.data("ui-tooltip-title")),this._removeDescribedBy(V),Y.hiding=!0,X.stop(!0),this._hide(X,this.options.hide,function(){U._removeTooltip(P(this))}),V.removeData("ui-tooltip-open"),this._off(V,"mouseleave focusout keyup"),V[0]!==this.element[0]&&this._off(V,"remove"),this._off(this.document,"mousemove"),W&&"mouseleave"===W.type&&P.each(this.parents,function(Z,a){P(a.element).attr("title",a.title),delete U.parents[Z]}),Y.closing=!0,this._trigger("close",W,{tooltip:X}),Y.hiding||(Y.closing=!1)))},_tooltip:function(V){var W=P("<div>").attr("role","tooltip").addClass("ui-tooltip ui-widget ui-corner-all ui-widget-content "+(this.options.tooltipClass||"")),U=W.uniqueId().attr("id");return P("<div>").addClass("ui-tooltip-content").appendTo(W),W.appendTo(this.document[0].body),this.tooltips[U]={element:V,tooltip:W}},_find:function(V){var U=V.data("ui-tooltip-id");return U?this.tooltips[U]:null},_removeTooltip:function(U){U.remove(),delete this.tooltips[U.attr("id")]},_destroy:function(){var U=this;P.each(this.tooltips,function(X,V){var W=P.Event("blur"),Y=V.element;W.target=W.currentTarget=Y[0],U.close(W,!0),P("#"+X).remove(),Y.data("ui-tooltip-title")&&(Y.attr("title")||Y.attr("title",Y.data("ui-tooltip-title")),Y.removeData("ui-tooltip-title"))}),this.liveRegion.remove()}}),"ui-effects-"),E=P;P.effects={effect:{}},
/*
 * jQuery Color Animations v2.1.2
 * https://github.com/jquery/jquery-color
 *
 * Copyright 2014 jQuery Foundation and other contributors
 * Released under the MIT license.
 * http://jquery.org/license
 *
 * Date: Wed Jan 16 08:47:09 2013 -0600
 */
function(u,s){function t(h,f,g){var e=q[f.type]||{};return null==h?g||!f.def?null:f.def:(h=e.floor?~~h:parseFloat(h),isNaN(h)?f.def:e.mod?(h+e.mod)%e.mod:0>h?0:e.max<h?e.max:h)}function x(e){var f=V(),a=f._rgba=[];return e=e.toLowerCase(),p(Y,function(k,d){var l,b=d.re.exec(e),c=b&&d.parse(b),j=d.space||"rgba";return c?(l=f[j](c),f[W[j].cache]=l[W[j].cache],a=f._rgba=l._rgba,!1):void 0}),a.length?("0,0,0,0"===a.join()&&u.extend(a,v.transparent),f):v[e]}function U(f,d,e){return e=(e+1)%1,1>6*e?f+(d-f)*e*6:1>2*e?d:2>3*e?f+(d-f)*(2/3-e)*6:f}var v,w="backgroundColor borderBottomColor borderLeftColor borderRightColor borderTopColor color columnRuleColor outlineColor textDecorationColor textEmphasisColor",X=/^([\-+])=\s*(\d+\.?\d*)/,Y=[{re:/rgba?\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*(?:,\s*(\d?(?:\.\d+)?)\s*)?\)/,parse:function(b){return[b[1],b[2],b[3],b[4]]}},{re:/rgba?\(\s*(\d+(?:\.\d+)?)\%\s*,\s*(\d+(?:\.\d+)?)\%\s*,\s*(\d+(?:\.\d+)?)\%\s*(?:,\s*(\d?(?:\.\d+)?)\s*)?\)/,parse:function(b){return[2.55*b[1],2.55*b[2],2.55*b[3],b[4]]}},{re:/#([a-f0-9]{2})([a-f0-9]{2})([a-f0-9]{2})/,parse:function(b){return[parseInt(b[1],16),parseInt(b[2],16),parseInt(b[3],16)]}},{re:/#([a-f0-9])([a-f0-9])([a-f0-9])/,parse:function(b){return[parseInt(b[1]+b[1],16),parseInt(b[2]+b[2],16),parseInt(b[3]+b[3],16)]}},{re:/hsla?\(\s*(\d+(?:\.\d+)?)\s*,\s*(\d+(?:\.\d+)?)\%\s*,\s*(\d+(?:\.\d+)?)\%\s*(?:,\s*(\d?(?:\.\d+)?)\s*)?\)/,space:"hsla",parse:function(b){return[b[1],b[2]/100,b[3]/100,b[4]]}}],V=u.Color=function(g,h,a,f){return new u.Color.fn.parse(g,h,a,f)},W={rgba:{props:{red:{idx:0,type:"byte"},green:{idx:1,type:"byte"},blue:{idx:2,type:"byte"}}},hsla:{props:{hue:{idx:0,type:"degrees"},saturation:{idx:1,type:"percent"},lightness:{idx:2,type:"percent"}}}},q={"byte":{floor:!0,max:255},percent:{max:1},degrees:{mod:360,floor:!0}},r=V.support={},Z=u("<p>")[0],p=u.each;Z.style.cssText="background-color:rgba(1,1,1,.5)",r.rgba=Z.style.backgroundColor.indexOf("rgba")>-1,p(W,function(d,c){c.cache="_"+d,c.props.alpha={idx:3,type:"percent",def:1}}),V.fn=u.extend(V.prototype,{parse:function(c,a,b,d){if(c===s){return this._rgba=[null,null,null,null],this}(c.jquery||c.nodeType)&&(c=u(c).css(a),a=s);var f=this,j=u.type(c),k=this._rgba=[];return a!==s&&(c=[c,a,b,d],j="array"),"string"===j?this.parse(x(c)||v._default):"array"===j?(p(W.rgba.props,function(g,e){k[e.idx]=t(c[e.idx],e)}),this):"object"===j?(c instanceof V?p(W,function(g,e){c[e.cache]&&(f[e.cache]=c[e.cache].slice())}):p(W,function(g,e){var h=e.cache;p(e.props,function(l,i){if(!f[h]&&e.to){if("alpha"===l||null==c[l]){return}f[h]=e.to(f._rgba)}f[h][i.idx]=t(c[l],i,!0)}),f[h]&&u.inArray(null,f[h].slice(0,3))<0&&(f[h][3]=1,e.from&&(f._rgba=e.from(f[h])))}),this):void 0},is:function(h){var f=V(h),g=!0,e=this;return p(W,function(d,c){var i,b=f[c.cache];return b&&(i=e[c.cache]||c.to&&c.to(e._rgba)||[],p(c.props,function(k,j){return null!=b[j.idx]?g=b[j.idx]===i[j.idx]:void 0})),g}),g},_space:function(){var d=[],c=this;return p(W,function(b,a){c[a.cache]&&d.push(b)}),d.pop()},transition:function(m,l){var y=V(m),c=y._space(),n=W[c],o=0===this.alpha()?V("transparent"):this,j=o[n.cache]||n.to(o._rgba),k=j.slice();return y=y[n.cache],p(n.props,function(z,h){var Aa=h.idx,b=j[Aa],d=y[Aa],i=q[h.type]||{};null!==d&&(null===b?k[Aa]=d:(i.mod&&(d-b>i.mod/2?b+=i.mod:b-d>i.mod/2&&(b-=i.mod)),k[Aa]=t((d-b)*l+b,h)))}),this[c](k)},blend:function(g){if(1===this._rgba[3]){return this}var h=this._rgba.slice(),a=h.pop(),f=V(g)._rgba;return V(u.map(h,function(d,c){return(1-a)*f[c]+a*d}))},toRgbaString:function(){var a="rgba(",d=u.map(this._rgba,function(e,c){return null==e?c>2?1:0:e});return 1===d[3]&&(d.pop(),a="rgb("),a+d.join()+")"},toHslaString:function(){var a="hsla(",d=u.map(this.hsla(),function(e,c){return null==e&&(e=c>2?1:0),c&&3>c&&(e=Math.round(100*e)+"%"),e});return 1===d[3]&&(d.pop(),a="hsl("),a+d.join()+")"},toHexString:function(e){var f=this._rgba.slice(),a=f.pop();return e&&f.push(~~(255*a)),"#"+u.map(f,function(b){return b=(b||0).toString(16),1===b.length?"0"+b:b}).join("")},toString:function(){return 0===this._rgba[3]?"transparent":this.toRgbaString()}}),V.fn.parse.prototype=V.fn,W.hsla.to=function(Ad){if(null==Ad[0]||null==Ad[1]||null==Ad[2]){return[null,null,null,Ad[3]]}var Ab,Ac,Ag=Ad[0]/255,m=Ad[1]/255,Ae=Ad[2]/255,Af=Ad[3],y=Math.max(Ag,m,Ae),z=Math.min(Ag,m,Ae),n=y-z,o=y+z,Aa=0.5*o;return Ab=z===y?0:Ag===y?60*(m-Ae)/n+360:m===y?60*(Ae-Ag)/n+120:60*(Ag-m)/n+240,Ac=0===n?0:0.5>=Aa?n/o:n/(2-o),[Math.round(Ab)%360,Ac,Aa,null==Af?1:Af]},W.hsla.from=function(m){if(null==m[0]||null==m[1]||null==m[2]){return[null,null,null,m[3]]}var k=m[0]/360,l=m[1],i=m[2],n=m[3],e=0.5>=i?i*(1+l):i+l-i*l,j=2*i-e;return[Math.round(255*U(j,e,k+1/3)),Math.round(255*U(j,e,k)),Math.round(255*U(j,e,k-1/3)),n]},p(W,function(b,c){var l=c.props,a=c.cache,h=c.to,j=c.from;V.fn[b]=function(g){if(h&&!this[a]&&(this[a]=h(this._rgba)),g===s){return this[a].slice()}var i,f=u.type(g),k="array"===f||"object"===f?g:arguments,n=this[a].slice();return p(l,function(o,m){var e=k["object"===f?o:m.idx];null==e&&(e=n[m.idx]),n[m.idx]=t(e,m)}),j?(i=V(j(n)),i[a]=n,i):V(n)},p(l,function(d,e){V.fn[d]||(V.fn[d]=function(o){var Aa,m=u.type(o),n="alpha"===d?this._hsla?"hsla":"rgba":b,z=this[n](),y=z[e.idx];return"undefined"===m?y:("function"===m&&(o=o.call(this,y),m=u.type(o)),null==o&&e.empty?this:("string"===m&&(Aa=X.exec(o),Aa&&(o=y+parseFloat(Aa[2])*("+"===Aa[1]?1:-1))),z[e.idx]=o,this[n](z)))})})}),V.hook=function(a){var d=a.split(" ");p(d,function(e,f){u.cssHooks[f]={set:function(m,k){var n,c,j="";if("transparent"!==k&&("string"!==u.type(k)||(n=x(k)))){if(k=V(n||k),!r.rgba&&1!==k._rgba[3]){for(c="backgroundColor"===f?m.parentNode:m;(""===j||"transparent"===j)&&c&&c.style;){try{j=u.css(c,"backgroundColor"),c=c.parentNode}catch(l){}}k=k.blend(j&&"transparent"!==j?j:"_default")}k=k.toRgbaString()}try{m.style[f]=k}catch(l){}}},u.fx.step[f]=function(c){c.colorInit||(c.start=V(c.elem,f),c.end=V(c.end),c.colorInit=!0),u.cssHooks[f].set(c.elem,c.start.transition(c.end,c.pos))}})},V.hook(w),u.cssHooks.borderColor={expand:function(d){var c={};return p(["Top","Right","Bottom","Left"],function(b,a){c["border"+a+"Color"]=d}),c}},v=u.Color.names={aqua:"#00ffff",black:"#000000",blue:"#0000ff",fuchsia:"#ff00ff",gray:"#808080",green:"#008000",lime:"#00ff00",maroon:"#800000",navy:"#000080",olive:"#808000",purple:"#800080",red:"#ff0000",silver:"#c0c0c0",teal:"#008080",white:"#ffffff",yellow:"#ffff00",transparent:[null,null,null,0],_default:"#ffffff"}}(E),function(){function W(a){var g,Y,Z=a.ownerDocument.defaultView?a.ownerDocument.defaultView.getComputedStyle(a,null):a.currentStyle,h={};if(Z&&Z.length&&Z[0]&&Z[Z[0]]){for(Y=Z.length;Y--;){g=Z[Y],"string"==typeof Z[g]&&(h[P.camelCase(g)]=Z[g])}}else{for(g in Z){"string"==typeof Z[g]&&(h[g]=Z[g])}}return h}function X(a,e){var Z,h,Y={};for(Z in e){h=e[Z],a[Z]!==h&&(V[Z]||(P.fx.step[Z]||!isNaN(parseFloat(h)))&&(Y[Z]=h))}return Y}var U=["add","remove","toggle"],V={border:1,borderBottom:1,borderColor:1,borderLeft:1,borderRight:1,borderTop:1,borderWidth:1,margin:1,padding:1};P.each(["borderLeftStyle","borderRightStyle","borderBottomStyle","borderTopStyle"],function(Y,Z){P.fx.step[Z]=function(b){("none"!==b.end&&!b.setAttr||1===b.pos&&!b.setAttr)&&(E.style(b.elem,Z,b.end),b.setAttr=!0)}}),P.fn.addBack||(P.fn.addBack=function(Y){return this.add(null==Y?this.prevObject:this.prevObject.filter(Y))}),P.effects.animateClass=function(a,c,Y,Z){var b=P.speed(c,Y,Z);return this.queue(function(){var k,d=P(this),e=d.attr("class")||"",i=b.children?d.find("*").addBack():d;i=i.map(function(){var f=P(this);return{el:f,start:W(this)}}),k=function(){P.each(U,function(g,f){a[f]&&d[f+"Class"](a[f])})},k(),i=i.map(function(){return this.end=W(this.el[0]),this.diff=X(this.start,this.end),this}),d.attr("class",e),i=i.map(function(){var g=this,h=P.Deferred(),f=P.extend({},b,{queue:!1,complete:function(){h.resolve(g)}});return this.el.animate(this.diff,f),h.promise()}),P.when.apply(P,i.get()).done(function(){k(),P.each(arguments,function(){var f=this.el;P.each(this.diff,function(g){f.css(g,"")})}),b.complete.call(d[0])})})},P.fn.extend({addClass:function(Y){return function(b,Z,a,g){return Z?P.effects.animateClass.call(this,{add:b},Z,a,g):Y.apply(this,arguments)}}(P.fn.addClass),removeClass:function(Y){return function(b,Z,a,g){return arguments.length>1?P.effects.animateClass.call(this,{remove:b},Z,a,g):Y.apply(this,arguments)}}(P.fn.removeClass),toggleClass:function(Y){return function(h,a,b,i,Z){return"boolean"==typeof a||void 0===a?b?P.effects.animateClass.call(this,a?{add:h}:{remove:h},b,i,Z):Y.apply(this,arguments):P.effects.animateClass.call(this,{toggle:h},a,b,i)}}(P.fn.toggleClass),switchClass:function(a,g,Y,Z,h){return P.effects.animateClass.call(this,{add:g,remove:a},Y,Z,h)}})}(),function(){function U(Y,Z,W,X){return P.isPlainObject(Y)&&(Z=Y,Y=Y.effect),Y={effect:Y},null==Z&&(Z={}),P.isFunction(Z)&&(X=Z,W=null,Z={}),("number"==typeof Z||P.fx.speeds[Z])&&(X=W,W=Z,Z={}),P.isFunction(W)&&(X=W,W=null),Z&&P.extend(Y,Z),W=W||Z.duration,Y.duration=P.fx.off?0:"number"==typeof W?W:W in P.fx.speeds?P.fx.speeds[W]:P.fx.speeds._default,Y.complete=X||Z.complete,Y}function V(W){return !W||"number"==typeof W||P.fx.speeds[W]?!0:"string"!=typeof W||P.effects.effect[W]?P.isFunction(W)?!0:"object"!=typeof W||W.effect?!1:!0:!0}P.extend(P.effects,{version:"1.11.2",save:function(Y,W){for(var X=0;X<W.length;X++){null!==W[X]&&Y.data(B+W[X],Y[0].style[W[X]])}},restore:function(Z,X){var Y,W;for(W=0;W<X.length;W++){null!==X[W]&&(Y=Z.data(B+X[W]),void 0===Y&&(Y=""),Z.css(X[W],Y))}},setMode:function(X,W){return"toggle"===W&&(W=X.is(":hidden")?"show":"hide"),W},getBaseline:function(Z,X){var Y,W;switch(Z[0]){case"top":Y=0;break;case"middle":Y=0.5;break;case"bottom":Y=1;break;default:Y=Z[0]/X.height}switch(Z[1]){case"left":W=0;break;case"center":W=0.5;break;case"right":W=1;break;default:W=Z[1]/X.width}return{x:W,y:Y}},createWrapper:function(Z){if(Z.parent().is(".ui-effects-wrapper")){return Z.parent()}var a={width:Z.outerWidth(!0),height:Z.outerHeight(!0),"float":Z.css("float")},X=P("<div></div>").addClass("ui-effects-wrapper").css({fontSize:"100%",background:"transparent",border:"none",margin:0,padding:0}),Y={width:Z.width(),height:Z.height()},h=document.activeElement;try{h.id}catch(W){h=document.body}return Z.wrap(X),(Z[0]===h||P.contains(Z[0],h))&&P(h).focus(),X=Z.parent(),"static"===Z.css("position")?(X.css({position:"relative"}),Z.css({position:"relative"})):(P.extend(a,{position:Z.css("position"),zIndex:Z.css("z-index")}),P.each(["top","left","bottom","right"],function(c,b){a[b]=Z.css(b),isNaN(parseInt(a[b],10))&&(a[b]="auto")}),Z.css({position:"relative",top:0,left:0,right:"auto",bottom:"auto"})),Z.css(Y),X.css(a).show()},removeWrapper:function(W){var X=document.activeElement;return W.parent().is(".ui-effects-wrapper")&&(W.parent().replaceWith(W),(W[0]===X||P.contains(W[0],X))&&P(X).focus()),W},setTransition:function(Y,Z,W,X){return X=X||{},P.each(Z,function(d,b){var e=Y.cssUnit(b);e[0]>0&&(X[b]=e[0]*W+e[1])}),X}}),P.fn.extend({effect:function(){function Z(i){function j(){P.isFunction(k)&&k.call(g[0]),P.isFunction(i)&&i()}var g=P(this),k=X.complete,d=X.mode;(g.is(":hidden")?"hide"===d:"show"===d)?(g[d](),j()):W.call(g[0],X,j)}var X=U.apply(this,arguments),Y=X.mode,a=X.queue,W=P.effects.effect[X.effect];return P.fx.off||!W?Y?this[Y](X.duration,X.complete):this.each(function(){X.complete&&X.complete.call(this)}):a===!1?this.each(Z):this.queue(a||"fx",Z)},show:function(W){return function(X){if(V(X)){return W.apply(this,arguments)}var Y=U.apply(this,arguments);return Y.mode="show",this.effect.call(this,Y)}}(P.fn.show),hide:function(W){return function(X){if(V(X)){return W.apply(this,arguments)}var Y=U.apply(this,arguments);return Y.mode="hide",this.effect.call(this,Y)}}(P.fn.hide),toggle:function(W){return function(X){if(V(X)||"boolean"==typeof X){return W.apply(this,arguments)}var Y=U.apply(this,arguments);return Y.mode="toggle",this.effect.call(this,Y)}}(P.fn.toggle),cssUnit:function(X){var Y=this.css(X),W=[];return P.each(["em","px","%","pt"],function(c,Z){Y.indexOf(Z)>0&&(W=[parseFloat(Y),Z])}),W}})}(),function(){var U={};P.each(["Quad","Cubic","Quart","Quint","Expo"],function(W,V){U[V]=function(X){return Math.pow(X,W+2)}}),P.extend(U,{Sine:function(V){return 1-Math.cos(V*Math.PI/2)},Circ:function(V){return 1-Math.sqrt(1-V*V)},Elastic:function(V){return 0===V||1===V?V:-Math.pow(2,8*(V-1))*Math.sin((80*(V-1)-7.5)*Math.PI/15)},Back:function(V){return V*V*(3*V-2)},Bounce:function(X){for(var V,W=4;X<((V=Math.pow(2,--W))-1)/11;){}return 1/Math.pow(4,3-W)-7.5625*Math.pow((3*V-2)/22-X,2)}}),P.each(U,function(V,W){P.easing["easeIn"+V]=W,P.easing["easeOut"+V]=function(X){return 1-W(1-X)},P.easing["easeInOut"+V]=function(X){return 0.5>X?W(2*X)/2:1-W(-2*X+2)/2}})}();P.effects,P.effects.effect.blind=function(w,x){var Aa,Ab,y,z=P(this),Z=/up|down|vertical/,a=/up|left|vertical|horizontal/,X=["position","top","bottom","left","right","height","width"],Y=P.effects.setMode(z,w.mode||"hide"),u=w.direction||"up",v=Z.test(u),s=v?"height":"width",t=v?"top":"left",V=a.test(u),W={},U="show"===Y;z.parent().is(".ui-effects-wrapper")?P.effects.save(z.parent(),X):P.effects.save(z,X),z.show(),Aa=P.effects.createWrapper(z).css({overflow:"hidden"}),Ab=Aa[s](),y=parseFloat(Aa.css(t))||0,W[s]=U?Ab:0,V||(z.css(v?"bottom":"right",0).css(v?"top":"left","auto").css({position:"absolute"}),W[t]=U?y:Ab+y),U&&(Aa.css(s,0),V||Aa.css(t,y+Ab)),Aa.animate(W,{duration:w.duration,easing:w.easing,queue:!1,complete:function(){"hide"===Y&&z.hide(),P.effects.restore(z,X),P.effects.removeWrapper(z),x()}})},P.effects.effect.bounce=function(Ac,Ad){var Ag,Ah,Ae,Af=P(this),w=["position","top","bottom","left","right","height","width"],x=P.effects.setMode(Af,Ac.mode||"effect"),a="hide"===x,v="show"===x,Aa=Ac.direction||"up",Ab=Ac.distance,y=Ac.times||5,z=2*y+(v||a?1:0),W=Ac.duration/z,X=Ac.easing,U="up"===Aa||"down"===Aa?"top":"left",V="up"===Aa||"left"===Aa,Y=Af.queue(),Z=Y.length;for((v||a)&&w.push("opacity"),P.effects.save(Af,w),Af.show(),P.effects.createWrapper(Af),Ab||(Ab=Af["top"===U?"outerHeight":"outerWidth"]()/3),v&&(Ae={opacity:1},Ae[U]=0,Af.css("opacity",0).css(U,V?2*-Ab:2*Ab).animate(Ae,W,X)),a&&(Ab/=Math.pow(2,y-1)),Ae={},Ae[U]=0,Ag=0;y>Ag;Ag++){Ah={},Ah[U]=(V?"-=":"+=")+Ab,Af.animate(Ah,W,X).animate(Ae,W,X),Ab=a?2*Ab:Ab/2}a&&(Ah={opacity:0},Ah[U]=(V?"-=":"+=")+Ab,Af.animate(Ah,W,X)),Af.queue(function(){a&&Af.hide(),P.effects.restore(Af,w),P.effects.removeWrapper(Af),Ad()}),Z>1&&Y.splice.apply(Y,[1,0].concat(Y.splice(Z,z+1))),Af.dequeue()},P.effects.effect.clip=function(r,s){var v,U,t,u=P(this),X=["position","top","bottom","left","right","height","width"],Y=P.effects.setMode(u,r.mode||"hide"),V="show"===Y,W=r.direction||"vertical",p="vertical"===W,q=p?"height":"width",Z=p?"top":"left",a={};P.effects.save(u,X),u.show(),v=P.effects.createWrapper(u).css({overflow:"hidden"}),U="IMG"===u[0].tagName?v:u,t=U[q](),V&&(U.css(q,0),U.css(Z,t/2)),a[q]=V?t:0,a[Z]=V?0:t/2,U.animate(a,{queue:!1,duration:r.duration,easing:r.easing,complete:function(){V||u.hide(),P.effects.restore(u,X),P.effects.removeWrapper(u),s()}})},P.effects.effect.drop=function(a,m){var p,U=P(this),n=["position","top","bottom","left","right","opacity","height","width"],o=P.effects.setMode(U,a.mode||"hide"),X="show"===o,Y=a.direction||"left",V="up"===Y||"down"===Y?"top":"left",W="up"===Y||"left"===Y?"pos":"neg",Z={opacity:X?1:0};P.effects.save(U,n),U.show(),P.effects.createWrapper(U),p=a.distance||U["top"===V?"outerHeight":"outerWidth"](!0)/2,X&&U.css("opacity",0).css(V,"pos"===W?-p:p),Z[V]=(X?"pos"===W?"+=":"-=":"pos"===W?"-=":"+=")+p,U.animate(Z,{queue:!1,duration:a.duration,easing:a.easing,complete:function(){"hide"===o&&U.hide(),P.effects.restore(U,n),P.effects.removeWrapper(U),m()}})},P.effects.effect.explode=function(Aa,Ab){function Ae(){Y.push(this),Y.length===y*z&&Af()}function Af(){w.css({visibility:"visible"}),P(Y).remove(),W||w.hide(),Ab()}var Ac,Ad,u,v,Z,a,y=Aa.pieces?Math.round(Math.sqrt(Aa.pieces)):3,z=y,w=P(this),x=P.effects.setMode(w,Aa.mode||"hide"),W="show"===x,X=w.show().css("visibility","hidden").offset(),U=Math.ceil(w.outerWidth()/z),V=Math.ceil(w.outerHeight()/y),Y=[];for(Ac=0;y>Ac;Ac++){for(v=X.top+Ac*V,a=Ac-(y-1)/2,Ad=0;z>Ad;Ad++){u=X.left+Ad*U,Z=Ad-(z-1)/2,w.clone().appendTo("body").wrap("<div></div>").css({position:"absolute",visibility:"visible",left:-Ad*U,top:-Ac*V}).parent().addClass("ui-effects-explode").css({position:"absolute",overflow:"hidden",width:U,height:V,left:u+(W?Z*U:0),top:v+(W?a*V:0),opacity:W?0:1}).animate({left:u+(W?0:Z*U),top:v+(W?0:a*V),opacity:W?1:0},Aa.duration||500,Aa.easing,Ae)}}},P.effects.effect.fade=function(W,X){var U=P(this),V=P.effects.setMode(U,W.mode||"toggle");U.animate({opacity:V},{queue:!1,duration:W.duration,easing:W.easing,complete:X})},P.effects.effect.fold=function(w,x){var Aa,Ab,y=P(this),z=["position","top","bottom","left","right","height","width"],Z=P.effects.setMode(y,w.mode||"hide"),a="show"===Z,X="hide"===Z,Y=w.size||15,u=/([0-9]+)%/.exec(Y),v=!!w.horizFirst,s=a!==v,t=s?["width","height"]:["height","width"],V=w.duration/2,W={},U={};P.effects.save(y,z),y.show(),Aa=P.effects.createWrapper(y).css({overflow:"hidden"}),Ab=s?[Aa.width(),Aa.height()]:[Aa.height(),Aa.width()],u&&(Y=parseInt(u[1],10)/100*Ab[X?0:1]),a&&Aa.css(v?{height:0,width:Y}:{height:Y,width:0}),W[t[0]]=a?Ab[0]:Y,U[t[1]]=a?Ab[1]:0,Aa.animate(W,V,w.easing).animate(U,V,w.easing,function(){X&&y.hide(),P.effects.restore(y,z),P.effects.removeWrapper(y),x()})},P.effects.effect.highlight=function(X,Y){var V=P(this),W=["backgroundImage","backgroundColor","opacity"],Z=P.effects.setMode(V,X.mode||"show"),U={backgroundColor:V.css("backgroundColor")};"hide"===Z&&(U.opacity=0),P.effects.save(V,W),V.show().css({backgroundImage:"none",backgroundColor:X.color||"#ffff99"}).animate(U,{queue:!1,duration:X.duration,easing:X.easing,complete:function(){"hide"===Z&&V.hide(),P.effects.restore(V,W),Y()}})},P.effects.effect.size=function(Aa,Ab){var Ae,Af,Ac,Ad=P(this),u=["position","top","bottom","left","right","width","height","overflow","opacity"],v=["position","top","bottom","left","right","overflow","opacity"],Z=["width","height","overflow"],a=["fontSize"],y=["borderTopWidth","borderBottomWidth","paddingTop","paddingBottom"],z=["borderLeftWidth","borderRightWidth","paddingLeft","paddingRight"],w=P.effects.setMode(Ad,Aa.mode||"effect"),x=Aa.restore||"effect"!==w,W=Aa.scale||"both",X=Aa.origin||["middle","center"],U=Ad.css("position"),V=x?u:v,Y={height:0,width:0,outerHeight:0,outerWidth:0};"show"===w&&Ad.show(),Ae={height:Ad.height(),width:Ad.width(),outerHeight:Ad.outerHeight(),outerWidth:Ad.outerWidth()},"toggle"===Aa.mode&&"show"===w?(Ad.from=Aa.to||Y,Ad.to=Aa.from||Ae):(Ad.from=Aa.from||("show"===w?Y:Ae),Ad.to=Aa.to||("hide"===w?Y:Ae)),Ac={from:{y:Ad.from.height/Ae.height,x:Ad.from.width/Ae.width},to:{y:Ad.to.height/Ae.height,x:Ad.to.width/Ae.width}},("box"===W||"both"===W)&&(Ac.from.y!==Ac.to.y&&(V=V.concat(y),Ad.from=P.effects.setTransition(Ad,y,Ac.from.y,Ad.from),Ad.to=P.effects.setTransition(Ad,y,Ac.to.y,Ad.to)),Ac.from.x!==Ac.to.x&&(V=V.concat(z),Ad.from=P.effects.setTransition(Ad,z,Ac.from.x,Ad.from),Ad.to=P.effects.setTransition(Ad,z,Ac.to.x,Ad.to))),("content"===W||"both"===W)&&Ac.from.y!==Ac.to.y&&(V=V.concat(a).concat(Z),Ad.from=P.effects.setTransition(Ad,a,Ac.from.y,Ad.from),Ad.to=P.effects.setTransition(Ad,a,Ac.to.y,Ad.to)),P.effects.save(Ad,V),Ad.show(),P.effects.createWrapper(Ad),Ad.css("overflow","hidden").css(Ad.from),X&&(Af=P.effects.getBaseline(X,Ae),Ad.from.top=(Ae.outerHeight-Ad.outerHeight())*Af.y,Ad.from.left=(Ae.outerWidth-Ad.outerWidth())*Af.x,Ad.to.top=(Ae.outerHeight-Ad.to.outerHeight)*Af.y,Ad.to.left=(Ae.outerWidth-Ad.to.outerWidth)*Af.x),Ad.css(Ad.from),("content"===W||"both"===W)&&(y=y.concat(["marginTop","marginBottom"]).concat(a),z=z.concat(["marginLeft","marginRight"]),Z=u.concat(y).concat(z),Ad.find("*[width]").each(function(){var e=P(this),b={height:e.height(),width:e.width(),outerHeight:e.outerHeight(),outerWidth:e.outerWidth()};x&&P.effects.save(e,Z),e.from={height:b.height*Ac.from.y,width:b.width*Ac.from.x,outerHeight:b.outerHeight*Ac.from.y,outerWidth:b.outerWidth*Ac.from.x},e.to={height:b.height*Ac.to.y,width:b.width*Ac.to.x,outerHeight:b.height*Ac.to.y,outerWidth:b.width*Ac.to.x},Ac.from.y!==Ac.to.y&&(e.from=P.effects.setTransition(e,y,Ac.from.y,e.from),e.to=P.effects.setTransition(e,y,Ac.to.y,e.to)),Ac.from.x!==Ac.to.x&&(e.from=P.effects.setTransition(e,z,Ac.from.x,e.from),e.to=P.effects.setTransition(e,z,Ac.to.x,e.to)),e.css(e.from),e.animate(e.to,Aa.duration,Aa.easing,function(){x&&P.effects.restore(e,Z)})})),Ad.animate(Ad.to,{queue:!1,duration:Aa.duration,easing:Aa.easing,complete:function(){0===Ad.to.opacity&&Ad.css("opacity",Ad.from.opacity),"hide"===w&&Ad.hide(),P.effects.restore(Ad,V),x||("static"===U?Ad.css({position:"relative",top:Ad.to.top,left:Ad.to.left}):P.each(["top","left"],function(d,c){Ad.css(c,function(h,i){var f=parseInt(i,10),g=d?Ad.to.left:Ad.to.top;return"auto"===i?g+"px":f+g+"px"})})),P.effects.removeWrapper(Ad),Ab()}})},P.effects.effect.scale=function(Z,a){var n=P(this),U=P.extend(!0,{},Z),l=P.effects.setMode(n,Z.mode||"effect"),m=parseInt(Z.percent,10)||(0===parseInt(Z.percent,10)?0:"hide"===l?0:100),X=Z.direction||"both",Y=Z.origin,V={height:n.height(),width:n.width(),outerHeight:n.outerHeight(),outerWidth:n.outerWidth()},W={y:"horizontal"!==X?m/100:1,x:"vertical"!==X?m/100:1};U.effect="size",U.queue=!1,U.complete=a,"effect"!==l&&(U.origin=Y||["middle","center"],U.restore=!0),U.from=Z.from||("show"===l?{height:0,width:0,outerHeight:0,outerWidth:0}:V),U.to={height:V.height*W.y,width:V.width*W.x,outerHeight:V.outerHeight*W.y,outerWidth:V.outerWidth*W.x},U.fade&&("show"===l&&(U.from.opacity=0,U.to.opacity=1),"hide"===l&&(U.from.opacity=1,U.to.opacity=0)),n.effect(U)},P.effects.effect.puff=function(X,Y){var j=P(this),U=P.effects.setMode(j,X.mode||"hide"),Z="hide"===U,a=parseInt(X.percent,10)||150,V=a/100,W={height:j.height(),width:j.width(),outerHeight:j.outerHeight(),outerWidth:j.outerWidth()};P.extend(X,{effect:"scale",queue:!1,fade:!0,mode:U,complete:Y,percent:Z?a:100,from:Z?W:{height:W.height*V,width:W.width*V,outerHeight:W.outerHeight*V,outerWidth:W.outerWidth*V}}),j.effect(X)},P.effects.effect.pulsate=function(p,q){var t,U=P(this),r=P.effects.setMode(U,p.mode||"show"),s="show"===r,X="hide"===r,Y=s||"hide"===r,V=2*(p.times||5)+(Y?1:0),W=p.duration/V,a=0,o=U.queue(),Z=o.length;for((s||!U.is(":visible"))&&(U.css("opacity",0).show(),a=1),t=1;V>t;t++){U.animate({opacity:a},W,p.easing),a=1-a}U.animate({opacity:a},W,p.easing),U.queue(function(){X&&U.hide(),q()}),Z>1&&o.splice.apply(o,[1,0].concat(o.splice(Z,V+1))),U.dequeue()},P.effects.effect.shake=function(y,z){var Ac,Ad=P(this),Aa=["position","top","bottom","left","right","height","width"],Ab=P.effects.setMode(Ad,y.mode||"effect"),a=y.direction||"left",t=y.distance||20,Y=y.times||3,Z=2*Y+1,w=Math.round(y.duration/Z),x="up"===a||"down"===a?"top":"left",u="up"===a||"left"===a,v={},W={},X={},U=Ad.queue(),V=U.length;for(P.effects.save(Ad,Aa),Ad.show(),P.effects.createWrapper(Ad),v[x]=(u?"-=":"+=")+t,W[x]=(u?"+=":"-=")+2*t,X[x]=(u?"-=":"+=")+2*t,Ad.animate(v,w,y.easing),Ac=1;Y>Ac;Ac++){Ad.animate(W,w,y.easing).animate(X,w,y.easing)}Ad.animate(W,w,y.easing).animate(v,w/2,y.easing).queue(function(){"hide"===Ab&&Ad.hide(),P.effects.restore(Ad,Aa),P.effects.removeWrapper(Ad),z()}),V>1&&U.splice.apply(U,[1,0].concat(U.splice(V,Z+1))),Ad.dequeue()},P.effects.effect.slide=function(a,m){var p,U=P(this),n=["position","top","bottom","left","right","width","height"],o=P.effects.setMode(U,a.mode||"show"),X="show"===o,Y=a.direction||"left",V="up"===Y||"down"===Y?"top":"left",W="up"===Y||"left"===Y,Z={};P.effects.save(U,n),U.show(),p=a.distance||U["top"===V?"outerHeight":"outerWidth"](!0),P.effects.createWrapper(U).css({overflow:"hidden"}),X&&U.css(V,W?isNaN(p)?"-"+p:-p:p),Z[V]=(X?W?"+=":"-=":W?"-=":"+=")+p,U.animate(Z,{queue:!1,duration:a.duration,easing:a.easing,complete:function(){"hide"===o&&U.hide(),P.effects.restore(U,n),P.effects.removeWrapper(U),m()}})},P.effects.effect.transfer=function(n,o){var r=P(this),U=P(n.to),p="fixed"===U.css("position"),q=P("body"),X=p?q.scrollTop():0,Y=p?q.scrollLeft():0,V=U.offset(),W={top:V.top-X,left:V.left-Y,height:U.innerHeight(),width:U.innerWidth()},Z=r.offset(),a=P("<div class='ui-effects-transfer'></div>").appendTo(document.body).addClass(n.className).css({top:Z.top-X,left:Z.left-Y,height:r.innerHeight(),width:r.innerWidth(),position:p?"fixed":"absolute"}).animate(W,n.duration,n.easing,function(){a.remove(),o()})}});