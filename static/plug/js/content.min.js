function $childNode(A){return window.frames[A]}function animationHover(B,A){B=$(B),<PERSON>.hover(function(){B.addClass("animated "+A)},function(){window.setTimeout(function(){B.removeClass("animated "+A)},2000)})}function WinMove(){var C="[class*=col]",B=".ibox-title",A="[class*=col]";$(C).sortable({handle:B,connectWith:A,tolerance:"pointer",forcePlaceholderSize:!0,opacity:0.8}).disableSelection()}var $parentNode=window.parent.document;if($(".tooltip-demo").tooltip({selector:"[data-toggle=tooltip]",container:"body"}),$(".modal").appendTo("body"),$("[data-toggle=popover]").popover(),$(".collapse-link").click(function(){var C=$(this).closest("div.ibox"),B=$(this).find("i"),A=C.find("div.ibox-content");<PERSON><PERSON>slideTog<PERSON>(200),B.toggleClass("fa-chevron-up").toggleClass("fa-chevron-down"),C.toggleClass("").toggleClass("border-bottom"),setTimeout(function(){C.resize(),C.find("[id^=map-]").resize()},50)}),$(".close-link").click(function(){var A=$(this).closest("div.ibox");A.remove()}),top==this){var gohome='<div class="gohome"><a class="animated bounceInUp" href="index.html?v=4.0" title="返回首页"><i class="fa fa-home"></i></a></div>';$("body").append(gohome)};