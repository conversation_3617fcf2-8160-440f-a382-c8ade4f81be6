(function(Ah,H){var Q,U=Array.prototype.slice,F=decodeURIComponent,X=Ah.param,W,T,J,V=Ah.bbq=Ah.bbq||{},I,M,N,Ab=Ah.event.special,Aa="hashchange",Ae="querystring",Af="fragment",Aj="elemUrlAttr",Z="location",O="href",L="src",Ai=/^.*\?|#.*$/g,K=/^.*\#/,P,Ad={};function Ag(A){return typeof A==="string"}function Ac(B){var A=U.call(arguments,1);return function(){return B.apply(this,A.concat(U.call(arguments)))}}function R(A){return A.replace(/^[^#]*#?(.*)$/,"$1")}function S(A){return A.replace(/(?:^[^?#]*\?([^#]*).*$)?.*/,"$1")}function Y(B,b,c,C,d){var E,a,A,D,e;if(C!==Q){A=c.match(B?/^([^#]*)\#?(.*)$/:/^([^#?]*)\??([^#]*)(#?.*)/);e=A[3]||"";if(d===2&&Ag(C)){a=C.replace(B?K:Ai,"")}else{D=T(A[2]);C=Ag(C)?T[B?Af:Ae](C):C;a=d===2?C:d===1?Ah.extend({},C,D):Ah.extend({},D,C);a=X(a);if(B){a=a.replace(P,F)}}E=A[1]+(B?"#":a||!A[1]?"?":"")+a+e}else{E=b(c!==Q?c:H[Z][O])}return E}X[Ae]=Ac(Y,0,S);X[Af]=W=Ac(Y,1,R);W.noEscape=function(B){B=B||"";var A=Ah.map(B.split(""),encodeURIComponent);P=new RegExp(A.join("|"),"g")};W.noEscape(",/");Ah.deparam=T=function(C,A){var B={},D={"true":!0,"false":!1,"null":null};Ah.each(C.replace(/\+/g," ").split("&"),function(c,g){var E=g.split("="),f=F(E[0]),h,b=B,d=0,e=f.split("]["),a=e.length-1;if(/\[/.test(e[0])&&/\]$/.test(e[a])){e[a]=e[a].replace(/\]$/,"");e=e.shift().split("[").concat(e);a=e.length-1}else{a=0}if(E.length===2){h=F(E[1]);if(A){h=h&&!isNaN(h)?+h:h==="undefined"?Q:D[h]!==Q?D[h]:h}if(a){for(;d<=a;d++){f=e[d]===""?b.length:e[d];b=b[f]=d<a?b[f]||(e[d+1]&&isNaN(e[d+1])?{}:[]):h}}else{if(Ah.isArray(B[f])){B[f].push(h)}else{if(B[f]!==Q){B[f]=[B[f],h]}else{B[f]=h}}}}else{if(f){B[f]=A?Q:""}}});return B};function Ak(B,A,C){if(A===Q||typeof A==="boolean"){C=A;A=X[B?Af:Ae]()}else{A=Ag(A)?A.replace(B?K:Ai,""):A}return T(A,C)}T[Ae]=Ac(Ak,0);T[Af]=J=Ac(Ak,1);Ah[Aj]||(Ah[Aj]=function(A){return Ah.extend(Ad,A)})({a:O,base:O,iframe:L,img:L,input:L,form:"action",link:O,script:L});N=Ah[Aj];function G(C,B,A,D){if(!Ag(A)&&typeof A!=="object"){D=A;A=B;B=Q}return this.each(function(){var a=Ah(this),b=B||N()[(this.nodeName||"").toLowerCase()]||"",E=b&&a.attr(b)||"";a.attr(b,X[C](E,A,D))})}Ah.fn[Ae]=Ac(G,Ae);Ah.fn[Af]=Ac(G,Af);V.pushState=I=function(C,A){if(Ag(C)&&/^#/.test(C)&&A===Q){A=2}var B=C!==Q,D=W(H[Z][O],B?C:{},B?A:2);H[Z][O]=D+(/#/.test(D)?"":"#")};V.getState=M=function(A,B){return A===Q||typeof A==="boolean"?J(A):J(B)[A]};V.removeState=function(A){var B={};if(A!==Q){B=M();Ah.each(Ah.isArray(A)?A:arguments,function(D,C){delete B[C]})}I(B,2)};Ab[Aa]=Ah.extend(Ab[Aa],{add:function(A){var B;function C(E){var D=E[Af]=W();E.getState=function(a,b){return a===Q||typeof a==="boolean"?T(D,a):T(D,b)[a]};B.apply(this,arguments)}if(Ah.isFunction(A)){B=A;return C}else{B=A.handler;A.handler=C}}})})(jQuery,this);(function(I,F,H){var C,D=I.event.special,L="location",M="hashchange",G="href",K=I.browser,B=document.documentMode,E=K.msie&&(B===H||B<8),A="on"+M in F&&!E;function J(N){N=N||F[L][G];return N.replace(/^[^#]*#?(.*)$/,"$1")}I[M+"Delay"]=100;D[M]=I.extend(D[M],{setup:function(){if(A){return false}I(C.start)},teardown:function(){if(A){return false}I(C.stop)}});C=(function(){var R={},N,O,P,Q;function S(){P=Q=function(T){return T};if(E){O=I('<iframe src="javascript:0"/>').hide().insertAfter("body")[0].contentWindow;Q=function(){return J(O.document[L][G])};P=function(V,T){if(V!==T){var U=O.document;U.open().close();U[L].hash="#"+V}};P(J())}}R.start=function(){if(N){return}var U=J();P||S();(function T(){var V=J(),W=Q(U);if(V!==U){P(U=V,W);I(F).trigger(M)}else{if(W!==U){F[L][G]=F[L][G].replace(/#.*/,"")+"#"+W}}N=setTimeout(T,I[M+"Delay"])})()};R.stop=function(){if(!O){N&&clearTimeout(N);N=0}};return R})()})(jQuery,this);