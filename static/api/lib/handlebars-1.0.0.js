var Handlebars={};(function(G,P){G.VERSION="1.0.0";G.COMPILER_REVISION=4;G.REVISION_CHANGES={1:"<= 1.0.rc.2",2:"== 1.0.0-rc.3",3:"== 1.0.0-rc.4",4:">= 1.0.0"};G.helpers={};G.partials={};var F=Object.prototype.toString,O="[object Function]",L="[object Object]";G.registerHelper=function(U,T,S){if(F.call(U)===L){if(S||T){throw new G.Exception("Arg not supported with multiple helpers")}G.Utils.extend(this.helpers,U)}else{if(S){T.not=S}this.helpers[U]=T}};G.registerPartial=function(S,T){if(F.call(S)===L){G.Utils.extend(this.partials,S)}else{this.partials[S]=T}};<PERSON>.register<PERSON>el<PERSON>("helperMissing",function(S){if(arguments.length===2){return P}else{throw new Error("Missing helper: '"+S+"'")}});G.registerHelper("blockHelperMissing",function(T,V){var W=V.inverse||function(){},U=V.fn;var S=F.call(T);if(S===O){T=T.call(this)}if(T===true){return U(this)}else{if(T===false||T==null){return W(this)}else{if(S==="[object Array]"){if(T.length>0){return G.helpers.each(T,V)}else{return W(this)}}else{return U(T)}}}});G.K=function(){};G.createFrame=Object.create||function(T){G.K.prototype=T;var S=new G.K();G.K.prototype=null;return S};G.logger={DEBUG:0,INFO:1,WARN:2,ERROR:3,level:3,methodMap:{0:"debug",1:"info",2:"warn",3:"error"},log:function(T,S){if(G.logger.level<=T){var U=G.logger.methodMap[T];if(typeof console!=="undefined"&&console[U]){console[U].call(console,S)}}}};G.log=function(T,S){G.logger.log(T,S)};G.registerHelper("each",function(U,W){var X=W.fn,a=W.inverse;var V=0,b="",S;var Z=F.call(U);if(Z===O){U=U.call(this)}if(W.data){S=G.createFrame(W.data)}if(U&&typeof U==="object"){if(U instanceof Array){for(var T=U.length;V<T;V++){if(S){S.index=V}b=b+X(U[V],{data:S})}}else{for(var Y in U){if(U.hasOwnProperty(Y)){if(S){S.key=Y}b=b+X(U[Y],{data:S});V++}}}}if(V===0){b=a(this)}return b});G.registerHelper("if",function(U,T){var S=F.call(U);if(S===O){U=U.call(this)}if(!U||G.Utils.isEmpty(U)){return T.inverse(this)}else{return T.fn(this)}});G.registerHelper("unless",function(T,S){return G.helpers["if"].call(this,T,{fn:S.inverse,inverse:S.fn})});G.registerHelper("with",function(T,U){var S=F.call(T);if(S===O){T=T.call(this)}if(!G.Utils.isEmpty(T)){return U.fn(T)}});G.registerHelper("log",function(S,U){var T=U.data&&U.data.level!=null?parseInt(U.data.level,10):1;G.log(T,S)});var J=(function(){var Y={trace:function T(){},yy:{},symbols_:{"error":2,"root":3,"program":4,"EOF":5,"simpleInverse":6,"statements":7,"statement":8,"openInverse":9,"closeBlock":10,"openBlock":11,"mustache":12,"partial":13,"CONTENT":14,"COMMENT":15,"OPEN_BLOCK":16,"inMustache":17,"CLOSE":18,"OPEN_INVERSE":19,"OPEN_ENDBLOCK":20,"path":21,"OPEN":22,"OPEN_UNESCAPED":23,"CLOSE_UNESCAPED":24,"OPEN_PARTIAL":25,"partialName":26,"params":27,"hash":28,"dataName":29,"param":30,"STRING":31,"INTEGER":32,"BOOLEAN":33,"hashSegments":34,"hashSegment":35,"ID":36,"EQUALS":37,"DATA":38,"pathSegments":39,"SEP":40,"$accept":0,"$end":1},terminals_:{2:"error",5:"EOF",14:"CONTENT",15:"COMMENT",16:"OPEN_BLOCK",18:"CLOSE",19:"OPEN_INVERSE",20:"OPEN_ENDBLOCK",22:"OPEN",23:"OPEN_UNESCAPED",24:"CLOSE_UNESCAPED",25:"OPEN_PARTIAL",31:"STRING",32:"INTEGER",33:"BOOLEAN",36:"ID",37:"EQUALS",38:"DATA",40:"SEP"},productions_:[0,[3,2],[4,2],[4,3],[4,2],[4,1],[4,1],[4,0],[7,1],[7,2],[8,3],[8,3],[8,1],[8,1],[8,1],[8,1],[11,3],[9,3],[10,3],[12,3],[12,3],[13,3],[13,4],[6,2],[17,3],[17,2],[17,2],[17,1],[17,1],[27,2],[27,1],[30,1],[30,1],[30,1],[30,1],[30,1],[28,1],[34,2],[34,1],[35,3],[35,3],[35,3],[35,3],[35,3],[26,1],[26,1],[26,1],[29,2],[21,1],[39,3],[39,1]],performAction:function X(d,g,a,c,f,Z,e){var b=Z.length-1;switch(f){case 1:return Z[b-1];break;case 2:this.$=new c.ProgramNode([],Z[b]);break;case 3:this.$=new c.ProgramNode(Z[b-2],Z[b]);break;case 4:this.$=new c.ProgramNode(Z[b-1],[]);break;case 5:this.$=new c.ProgramNode(Z[b]);break;case 6:this.$=new c.ProgramNode([],[]);break;case 7:this.$=new c.ProgramNode([]);break;case 8:this.$=[Z[b]];break;case 9:Z[b-1].push(Z[b]);this.$=Z[b-1];break;case 10:this.$=new c.BlockNode(Z[b-2],Z[b-1].inverse,Z[b-1],Z[b]);break;case 11:this.$=new c.BlockNode(Z[b-2],Z[b-1],Z[b-1].inverse,Z[b]);break;case 12:this.$=Z[b];break;case 13:this.$=Z[b];break;case 14:this.$=new c.ContentNode(Z[b]);break;case 15:this.$=new c.CommentNode(Z[b]);break;case 16:this.$=new c.MustacheNode(Z[b-1][0],Z[b-1][1]);break;case 17:this.$=new c.MustacheNode(Z[b-1][0],Z[b-1][1]);break;case 18:this.$=Z[b-1];break;case 19:this.$=new c.MustacheNode(Z[b-1][0],Z[b-1][1],Z[b-2][2]==="&");break;case 20:this.$=new c.MustacheNode(Z[b-1][0],Z[b-1][1],true);break;case 21:this.$=new c.PartialNode(Z[b-1]);break;case 22:this.$=new c.PartialNode(Z[b-2],Z[b-1]);break;case 23:break;case 24:this.$=[[Z[b-2]].concat(Z[b-1]),Z[b]];break;case 25:this.$=[[Z[b-1]].concat(Z[b]),null];break;case 26:this.$=[[Z[b-1]],Z[b]];break;case 27:this.$=[[Z[b]],null];break;case 28:this.$=[[Z[b]],null];break;case 29:Z[b-1].push(Z[b]);this.$=Z[b-1];break;case 30:this.$=[Z[b]];break;case 31:this.$=Z[b];break;case 32:this.$=new c.StringNode(Z[b]);break;case 33:this.$=new c.IntegerNode(Z[b]);break;case 34:this.$=new c.BooleanNode(Z[b]);break;case 35:this.$=Z[b];break;case 36:this.$=new c.HashNode(Z[b]);break;case 37:Z[b-1].push(Z[b]);this.$=Z[b-1];break;case 38:this.$=[Z[b]];break;case 39:this.$=[Z[b-2],Z[b]];break;case 40:this.$=[Z[b-2],new c.StringNode(Z[b])];break;case 41:this.$=[Z[b-2],new c.IntegerNode(Z[b])];break;case 42:this.$=[Z[b-2],new c.BooleanNode(Z[b])];break;case 43:this.$=[Z[b-2],Z[b]];break;case 44:this.$=new c.PartialNameNode(Z[b]);break;case 45:this.$=new c.PartialNameNode(new c.StringNode(Z[b]));break;case 46:this.$=new c.PartialNameNode(new c.IntegerNode(Z[b]));break;case 47:this.$=new c.DataNode(Z[b]);break;case 48:this.$=new c.IdNode(Z[b]);break;case 49:Z[b-2].push({part:Z[b],separator:Z[b-1]});this.$=Z[b-2];break;case 50:this.$=[{part:Z[b]}];break}},table:[{3:1,4:2,5:[2,7],6:3,7:4,8:6,9:7,11:8,12:9,13:10,14:[1,11],15:[1,12],16:[1,13],19:[1,5],22:[1,14],23:[1,15],25:[1,16]},{1:[3]},{5:[1,17]},{5:[2,6],7:18,8:6,9:7,11:8,12:9,13:10,14:[1,11],15:[1,12],16:[1,13],19:[1,19],20:[2,6],22:[1,14],23:[1,15],25:[1,16]},{5:[2,5],6:20,8:21,9:7,11:8,12:9,13:10,14:[1,11],15:[1,12],16:[1,13],19:[1,5],20:[2,5],22:[1,14],23:[1,15],25:[1,16]},{17:23,18:[1,22],21:24,29:25,36:[1,28],38:[1,27],39:26},{5:[2,8],14:[2,8],15:[2,8],16:[2,8],19:[2,8],20:[2,8],22:[2,8],23:[2,8],25:[2,8]},{4:29,6:3,7:4,8:6,9:7,11:8,12:9,13:10,14:[1,11],15:[1,12],16:[1,13],19:[1,5],20:[2,7],22:[1,14],23:[1,15],25:[1,16]},{4:30,6:3,7:4,8:6,9:7,11:8,12:9,13:10,14:[1,11],15:[1,12],16:[1,13],19:[1,5],20:[2,7],22:[1,14],23:[1,15],25:[1,16]},{5:[2,12],14:[2,12],15:[2,12],16:[2,12],19:[2,12],20:[2,12],22:[2,12],23:[2,12],25:[2,12]},{5:[2,13],14:[2,13],15:[2,13],16:[2,13],19:[2,13],20:[2,13],22:[2,13],23:[2,13],25:[2,13]},{5:[2,14],14:[2,14],15:[2,14],16:[2,14],19:[2,14],20:[2,14],22:[2,14],23:[2,14],25:[2,14]},{5:[2,15],14:[2,15],15:[2,15],16:[2,15],19:[2,15],20:[2,15],22:[2,15],23:[2,15],25:[2,15]},{17:31,21:24,29:25,36:[1,28],38:[1,27],39:26},{17:32,21:24,29:25,36:[1,28],38:[1,27],39:26},{17:33,21:24,29:25,36:[1,28],38:[1,27],39:26},{21:35,26:34,31:[1,36],32:[1,37],36:[1,28],39:26},{1:[2,1]},{5:[2,2],8:21,9:7,11:8,12:9,13:10,14:[1,11],15:[1,12],16:[1,13],19:[1,19],20:[2,2],22:[1,14],23:[1,15],25:[1,16]},{17:23,21:24,29:25,36:[1,28],38:[1,27],39:26},{5:[2,4],7:38,8:6,9:7,11:8,12:9,13:10,14:[1,11],15:[1,12],16:[1,13],19:[1,19],20:[2,4],22:[1,14],23:[1,15],25:[1,16]},{5:[2,9],14:[2,9],15:[2,9],16:[2,9],19:[2,9],20:[2,9],22:[2,9],23:[2,9],25:[2,9]},{5:[2,23],14:[2,23],15:[2,23],16:[2,23],19:[2,23],20:[2,23],22:[2,23],23:[2,23],25:[2,23]},{18:[1,39]},{18:[2,27],21:44,24:[2,27],27:40,28:41,29:48,30:42,31:[1,45],32:[1,46],33:[1,47],34:43,35:49,36:[1,50],38:[1,27],39:26},{18:[2,28],24:[2,28]},{18:[2,48],24:[2,48],31:[2,48],32:[2,48],33:[2,48],36:[2,48],38:[2,48],40:[1,51]},{21:52,36:[1,28],39:26},{18:[2,50],24:[2,50],31:[2,50],32:[2,50],33:[2,50],36:[2,50],38:[2,50],40:[2,50]},{10:53,20:[1,54]},{10:55,20:[1,54]},{18:[1,56]},{18:[1,57]},{24:[1,58]},{18:[1,59],21:60,36:[1,28],39:26},{18:[2,44],36:[2,44]},{18:[2,45],36:[2,45]},{18:[2,46],36:[2,46]},{5:[2,3],8:21,9:7,11:8,12:9,13:10,14:[1,11],15:[1,12],16:[1,13],19:[1,19],20:[2,3],22:[1,14],23:[1,15],25:[1,16]},{14:[2,17],15:[2,17],16:[2,17],19:[2,17],20:[2,17],22:[2,17],23:[2,17],25:[2,17]},{18:[2,25],21:44,24:[2,25],28:61,29:48,30:62,31:[1,45],32:[1,46],33:[1,47],34:43,35:49,36:[1,50],38:[1,27],39:26},{18:[2,26],24:[2,26]},{18:[2,30],24:[2,30],31:[2,30],32:[2,30],33:[2,30],36:[2,30],38:[2,30]},{18:[2,36],24:[2,36],35:63,36:[1,64]},{18:[2,31],24:[2,31],31:[2,31],32:[2,31],33:[2,31],36:[2,31],38:[2,31]},{18:[2,32],24:[2,32],31:[2,32],32:[2,32],33:[2,32],36:[2,32],38:[2,32]},{18:[2,33],24:[2,33],31:[2,33],32:[2,33],33:[2,33],36:[2,33],38:[2,33]},{18:[2,34],24:[2,34],31:[2,34],32:[2,34],33:[2,34],36:[2,34],38:[2,34]},{18:[2,35],24:[2,35],31:[2,35],32:[2,35],33:[2,35],36:[2,35],38:[2,35]},{18:[2,38],24:[2,38],36:[2,38]},{18:[2,50],24:[2,50],31:[2,50],32:[2,50],33:[2,50],36:[2,50],37:[1,65],38:[2,50],40:[2,50]},{36:[1,66]},{18:[2,47],24:[2,47],31:[2,47],32:[2,47],33:[2,47],36:[2,47],38:[2,47]},{5:[2,10],14:[2,10],15:[2,10],16:[2,10],19:[2,10],20:[2,10],22:[2,10],23:[2,10],25:[2,10]},{21:67,36:[1,28],39:26},{5:[2,11],14:[2,11],15:[2,11],16:[2,11],19:[2,11],20:[2,11],22:[2,11],23:[2,11],25:[2,11]},{14:[2,16],15:[2,16],16:[2,16],19:[2,16],20:[2,16],22:[2,16],23:[2,16],25:[2,16]},{5:[2,19],14:[2,19],15:[2,19],16:[2,19],19:[2,19],20:[2,19],22:[2,19],23:[2,19],25:[2,19]},{5:[2,20],14:[2,20],15:[2,20],16:[2,20],19:[2,20],20:[2,20],22:[2,20],23:[2,20],25:[2,20]},{5:[2,21],14:[2,21],15:[2,21],16:[2,21],19:[2,21],20:[2,21],22:[2,21],23:[2,21],25:[2,21]},{18:[1,68]},{18:[2,24],24:[2,24]},{18:[2,29],24:[2,29],31:[2,29],32:[2,29],33:[2,29],36:[2,29],38:[2,29]},{18:[2,37],24:[2,37],36:[2,37]},{37:[1,65]},{21:69,29:73,31:[1,70],32:[1,71],33:[1,72],36:[1,28],38:[1,27],39:26},{18:[2,49],24:[2,49],31:[2,49],32:[2,49],33:[2,49],36:[2,49],38:[2,49],40:[2,49]},{18:[1,74]},{5:[2,22],14:[2,22],15:[2,22],16:[2,22],19:[2,22],20:[2,22],22:[2,22],23:[2,22],25:[2,22]},{18:[2,39],24:[2,39],36:[2,39]},{18:[2,40],24:[2,40],36:[2,40]},{18:[2,41],24:[2,41],36:[2,41]},{18:[2,42],24:[2,42],36:[2,42]},{18:[2,43],24:[2,43],36:[2,43]},{5:[2,18],14:[2,18],15:[2,18],16:[2,18],19:[2,18],20:[2,18],22:[2,18],23:[2,18],25:[2,18]}],defaultActions:{17:[2,1]},parseError:function V(a,Z){throw new Error(a)},parse:function S(l){var x=this,t=[0],d=[null],e=[],f=this.table,Ad="",z=0,m=0,i=0,g=2,n=1;this.lexer.setInput(l);this.lexer.yy=this.yy;this.yy.lexer=this.lexer;this.yy.parser=this;if(typeof this.lexer.yylloc=="undefined"){this.lexer.yylloc={}}var s=this.lexer.yylloc;e.push(s);var Ab=this.lexer.options&&this.lexer.options.ranges;if(typeof this.yy.parseError==="function"){this.parseError=this.yy.parseError}function y(a){t.length=t.length-2*a;d.length=d.length-a;e.length=e.length-a}function Aa(){var a;a=x.lexer.lex()||1;if(typeof a!=="number"){a=x.symbols_[a]||a}return a}var Ac,k,w,v,b,Z,u={},h,c,o,j;while(true){w=t[t.length-1];if(this.defaultActions[w]){v=this.defaultActions[w]}else{if(Ac===null||typeof Ac=="undefined"){Ac=Aa()}v=f[w]&&f[w][Ac]}if(typeof v==="undefined"||!v.length||!v[0]){var q="";if(!i){j=[];for(h in f[w]){if(this.terminals_[h]&&h>2){j.push("'"+this.terminals_[h]+"'")}}if(this.lexer.showPosition){q="Parse error on line "+(z+1)+":\n"+this.lexer.showPosition()+"\nExpecting "+j.join(", ")+", got '"+(this.terminals_[Ac]||Ac)+"'"}else{q="Parse error on line "+(z+1)+": Unexpected "+(Ac==1?"end of input":"'"+(this.terminals_[Ac]||Ac)+"'")}this.parseError(q,{text:this.lexer.match,token:this.terminals_[Ac]||Ac,line:this.lexer.yylineno,loc:s,expected:j})}}if(v[0] instanceof Array&&v.length>1){throw new Error("Parse Error: multiple actions possible at state: "+w+", token: "+Ac)}switch(v[0]){case 1:t.push(Ac);d.push(this.lexer.yytext);e.push(this.lexer.yylloc);t.push(v[1]);Ac=null;if(!k){m=this.lexer.yyleng;Ad=this.lexer.yytext;z=this.lexer.yylineno;s=this.lexer.yylloc;if(i>0){i--}}else{Ac=k;k=null}break;case 2:c=this.productions_[v[1]][1];u.$=d[d.length-c];u._$={first_line:e[e.length-(c||1)].first_line,last_line:e[e.length-1].last_line,first_column:e[e.length-(c||1)].first_column,last_column:e[e.length-1].last_column};if(Ab){u._$.range=[e[e.length-(c||1)].range[0],e[e.length-1].range[1]]}Z=this.performAction.call(u,Ad,m,z,this.yy,v[1],d,e);if(typeof Z!=="undefined"){return Z}if(c){t=t.slice(0,-1*c*2);d=d.slice(0,-1*c);e=e.slice(0,-1*c)}t.push(this.productions_[v[1]][0]);d.push(u.$);e.push(u._$);o=f[t[t.length-2]][t[t.length-1]];t.push(o);break;case 3:return true}}return true}};var U=(function(){var f=({EOF:1,parseError:function c(h,g){if(this.yy.parser){this.yy.parser.parseError(h,g)}else{throw new Error(h)}},setInput:function(g){this._input=g;this._more=this._less=this.done=false;this.yylineno=this.yyleng=0;this.yytext=this.matched=this.match="";this.conditionStack=["INITIAL"];this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0};if(this.options.ranges){this.yylloc.range=[0,0]}this.offset=0;return this},input:function(){var h=this._input[0];this.yytext+=h;this.yyleng++;this.offset++;this.match+=h;this.matched+=h;var g=h.match(/(?:\r\n?|\n).*/g);if(g){this.yylineno++;this.yylloc.last_line++}else{this.yylloc.last_column++}if(this.options.ranges){this.yylloc.range[1]++}this._input=this._input.slice(1);return h},unput:function(j){var k=j.length;var i=j.split(/(?:\r\n?|\n)/g);this._input=j+this._input;this.yytext=this.yytext.substr(0,this.yytext.length-k-1);this.offset-=k;var g=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1);this.matched=this.matched.substr(0,this.matched.length-1);if(i.length-1){this.yylineno-=i.length-1}var h=this.yylloc.range;this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:i?(i.length===g.length?this.yylloc.first_column:0)+g[g.length-i.length].length-i[0].length:this.yylloc.first_column-k};if(this.options.ranges){this.yylloc.range=[h[0],h[0]+this.yyleng-k]}return this},more:function(){this._more=true;return this},less:function(g){this.unput(this.match.slice(g))},pastInput:function(){var g=this.matched.substr(0,this.matched.length-this.match.length);return(g.length>20?"...":"")+g.substr(-20).replace(/\n/g,"")},upcomingInput:function(){var g=this.match;if(g.length<20){g+=this._input.substr(0,20-g.length)}return(g.substr(0,20)+(g.length>20?"...":"")).replace(/\n/g,"")},showPosition:function(){var g=this.pastInput();var h=new Array(g.length+1).join("-");return g+this.upcomingInput()+"\n"+h+"^"},next:function(){if(this.done){return this.EOF}if(!this._input){this.done=true}var l,h,g,n,o,j;if(!this._more){this.yytext="";this.match=""}var k=this._currentRules();for(var m=0;m<k.length;m++){g=this._input.match(this.rules[k[m]]);if(g&&(!h||g[0].length>h[0].length)){h=g;n=m;if(!this.options.flex){break}}}if(h){j=h[0].match(/(?:\r\n?|\n).*/g);if(j){this.yylineno+=j.length}this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:j?j[j.length-1].length-j[j.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+h[0].length};this.yytext+=h[0];this.match+=h[0];this.matches=h;this.yyleng=this.yytext.length;if(this.options.ranges){this.yylloc.range=[this.offset,this.offset+=this.yyleng]}this._more=false;this._input=this._input.slice(h[0].length);this.matched+=h[0];l=this.performAction.call(this,this.yy,this,k[n],this.conditionStack[this.conditionStack.length-1]);if(this.done&&this._input){this.done=false}if(l){return l}else{return}}if(this._input===""){return this.EOF}else{return this.parseError("Lexical error on line "+(this.yylineno+1)+". Unrecognized text.\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})}},lex:function e(){var g=this.next();if(typeof g!=="undefined"){return g}else{return this.lex()}},begin:function a(g){this.conditionStack.push(g)},popState:function d(){return this.conditionStack.pop()},_currentRules:function b(){return this.conditions[this.conditionStack[this.conditionStack.length-1]].rules},topState:function(){return this.conditionStack[this.conditionStack.length-2]},pushState:function a(g){this.begin(g)}});f.options={};f.performAction=function Z(g,h,j,k){var i=k;switch(j){case 0:h.yytext="\\";return 14;break;case 1:if(h.yytext.slice(-1)!=="\\"){this.begin("mu")}if(h.yytext.slice(-1)==="\\"){h.yytext=h.yytext.substr(0,h.yyleng-1),this.begin("emu")}if(h.yytext){return 14}break;case 2:return 14;break;case 3:if(h.yytext.slice(-1)!=="\\"){this.popState()}if(h.yytext.slice(-1)==="\\"){h.yytext=h.yytext.substr(0,h.yyleng-1)}return 14;break;case 4:h.yytext=h.yytext.substr(0,h.yyleng-4);this.popState();return 15;break;case 5:return 25;break;case 6:return 16;break;case 7:return 20;break;case 8:return 19;break;case 9:return 19;break;case 10:return 23;break;case 11:return 22;break;case 12:this.popState();this.begin("com");break;case 13:h.yytext=h.yytext.substr(3,h.yyleng-5);this.popState();return 15;break;case 14:return 22;break;case 15:return 37;break;case 16:return 36;break;case 17:return 36;break;case 18:return 40;break;case 19:break;case 20:this.popState();return 24;break;case 21:this.popState();return 18;break;case 22:h.yytext=h.yytext.substr(1,h.yyleng-2).replace(/\\"/g,'"');return 31;break;case 23:h.yytext=h.yytext.substr(1,h.yyleng-2).replace(/\\'/g,"'");return 31;break;case 24:return 38;break;case 25:return 33;break;case 26:return 33;break;case 27:return 32;break;case 28:return 36;break;case 29:h.yytext=h.yytext.substr(1,h.yyleng-2);return 36;break;case 30:return"INVALID";break;case 31:return 5;break}};f.rules=[/^(?:\\\\(?=(\{\{)))/,/^(?:[^\x00]*?(?=(\{\{)))/,/^(?:[^\x00]+)/,/^(?:[^\x00]{2,}?(?=(\{\{|$)))/,/^(?:[\s\S]*?--\}\})/,/^(?:\{\{>)/,/^(?:\{\{#)/,/^(?:\{\{\/)/,/^(?:\{\{\^)/,/^(?:\{\{\s*else\b)/,/^(?:\{\{\{)/,/^(?:\{\{&)/,/^(?:\{\{!--)/,/^(?:\{\{![\s\S]*?\}\})/,/^(?:\{\{)/,/^(?:=)/,/^(?:\.(?=[}\/ ]))/,/^(?:\.\.)/,/^(?:[\/.])/,/^(?:\s+)/,/^(?:\}\}\})/,/^(?:\}\})/,/^(?:"(\\["]|[^"])*")/,/^(?:'(\\[']|[^'])*')/,/^(?:@)/,/^(?:true(?=[}\s]))/,/^(?:false(?=[}\s]))/,/^(?:-?[0-9]+(?=[}\s]))/,/^(?:[^\s!"#%-,\.\/;->@\[-\^`\{-~]+(?=[=}\s\/.]))/,/^(?:\[[^\]]*\])/,/^(?:.)/,/^(?:$)/];f.conditions={"mu":{"rules":[5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31],"inclusive":false},"emu":{"rules":[3],"inclusive":false},"com":{"rules":[4],"inclusive":false},"INITIAL":{"rules":[0,1,2,31],"inclusive":true}};return f})();Y.lexer=U;function W(){this.yy={}}W.prototype=Y;Y.Parser=W;return new W})();G.Parser=J;G.parse=function(S){if(S.constructor===G.AST.ProgramNode){return S}G.Parser.yy=G.AST;return G.Parser.parse(S)};G.AST={};G.AST.ProgramNode=function(T,S){this.type="program";this.statements=T;if(S){this.inverse=new G.AST.ProgramNode(S)}};G.AST.MustacheNode=function(T,V,W){this.type="mustache";this.escaped=!W;this.hash=V;var X=this.id=T[0];var S=this.params=T.slice(1);var U=this.eligibleHelper=X.isSimple;this.isHelper=U&&(S.length||V)};G.AST.PartialNode=function(T,S){this.type="partial";this.partialName=T;this.context=S};G.AST.BlockNode=function(W,U,S,T){var V=function(X,Y){if(X.original!==Y.original){throw new G.Exception(X.original+" doesn't match "+Y.original)}};V(W.id,T);this.type="block";this.mustache=W;this.program=U;this.inverse=S;if(this.inverse&&!this.program){this.isInverse=true}};G.AST.ContentNode=function(S){this.type="content";this.string=S};G.AST.HashNode=function(S){this.type="hash";this.pairs=S};G.AST.IdNode=function(S){this.type="ID";var U="",Y=[],V=0;for(var T=0,W=S.length;T<W;T++){var X=S[T].part;U+=(S[T].separator||"")+X;if(X===".."||X==="."||X==="this"){if(Y.length>0){throw new G.Exception("Invalid path: "+U)}else{if(X===".."){V++}else{this.isScoped=true}}}else{Y.push(X)}}this.original=U;this.parts=Y;this.string=Y.join(".");this.depth=V;this.isSimple=S.length===1&&!this.isScoped&&V===0;this.stringModeValue=this.string};G.AST.PartialNameNode=function(S){this.type="PARTIAL_NAME";this.name=S.original};G.AST.DataNode=function(S){this.type="DATA";this.id=S};G.AST.StringNode=function(S){this.type="STRING";this.original=this.string=this.stringModeValue=S};G.AST.IntegerNode=function(S){this.type="INTEGER";this.original=this.integer=S;this.stringModeValue=Number(S)};G.AST.BooleanNode=function(S){this.type="BOOLEAN";this.bool=S;this.stringModeValue=S==="true"};G.AST.CommentNode=function(S){this.type="comment";this.comment=S};var R=["description","fileName","lineNumber","message","name","number","stack"];G.Exception=function(U){var S=Error.prototype.constructor.apply(this,arguments);for(var T=0;T<R.length;T++){this[R[T]]=S[R[T]]}};G.Exception.prototype=new Error();G.SafeString=function(S){this.string=S};G.SafeString.prototype.toString=function(){return this.string.toString()};var D={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#x27;","`":"&#x60;"};var H=/[&<>"'`]/g;var Q=/[&<>"'`]/;var N=function(S){return D[S]||"&amp;"};G.Utils={extend:function(T,U){for(var S in U){if(U.hasOwnProperty(S)){T[S]=U[S]}}},escapeExpression:function(S){if(S instanceof G.SafeString){return S.toString()}else{if(S==null||S===false){return""}}S=S.toString();if(!Q.test(S)){return S}return S.replace(H,N)},isEmpty:function(S){if(!S&&S!==0){return true}else{if(F.call(S)==="[object Array]"&&S.length===0){return true}else{return false}}}};var C=G.Compiler=function(){};var M=G.JavaScriptCompiler=function(){};C.prototype={compiler:C,disassemble:function(){var T=this.opcodes,Z,S=[],W,U;for(var V=0,X=T.length;V<X;V++){Z=T[V];if(Z.opcode==="DECLARE"){S.push("DECLARE "+Z.name+"="+Z.value)}else{W=[];for(var Y=0;Y<Z.args.length;Y++){U=Z.args[Y];if(typeof U==="string"){U='"'+U.replace("\n","\\n")+'"'}W.push(U)}S.push(Z.opcode+" "+W.join(" "))}}return S.join("\n")},equals:function(X){var V=this.opcodes.length;if(X.opcodes.length!==V){return false}for(var T=0;T<V;T++){var U=this.opcodes[T],S=X.opcodes[T];if(U.opcode!==S.opcode||U.args.length!==S.args.length){return false}for(var W=0;W<U.args.length;W++){if(U.args[W]!==S.args[W]){return false}}}V=this.children.length;if(X.children.length!==V){return false}for(T=0;T<V;T++){if(!this.children[T].equals(X.children[T])){return false}}return true},guid:0,compile:function(T,S){this.children=[];this.depths={list:[]};this.options=S;var V=this.options.knownHelpers;this.options.knownHelpers={"helperMissing":true,"blockHelperMissing":true,"each":true,"if":true,"unless":true,"with":true,"log":true};if(V){for(var U in V){this.options.knownHelpers[U]=V[U]}}return this.program(T)},accept:function(S){return this[S.type](S)},program:function(U){var V=U.statements,W;this.opcodes=[];for(var S=0,T=V.length;S<T;S++){W=V[S];this[W.type](W)}this.isSimple=T===1;this.depths.list=this.depths.list.sort(function(Y,X){return Y-X});return this},compileProgram:function(W){var U=new this.compiler().compile(W,this.options);var S=this.guid++,V;this.usePartial=this.usePartial||U.usePartial;this.children[S]=U;for(var T=0,X=U.depths.list.length;T<X;T++){V=U.depths.list[T];if(V<2){continue}else{this.addDepth(V-1)}}return S},block:function(W){var U=W.mustache,V=W.program,T=W.inverse;if(V){V=this.compileProgram(V)}if(T){T=this.compileProgram(T)}var S=this.classifyMustache(U);if(S==="helper"){this.helperMustache(U,V,T)}else{if(S==="simple"){this.simpleMustache(U);this.opcode("pushProgram",V);this.opcode("pushProgram",T);this.opcode("emptyHash");this.opcode("blockValue")}else{this.ambiguousMustache(U,V,T);this.opcode("pushProgram",V);this.opcode("pushProgram",T);this.opcode("emptyHash");this.opcode("ambiguousBlockValue")}}this.opcode("append")},hash:function(W){var V=W.pairs,S,X;this.opcode("pushHash");for(var T=0,U=V.length;T<U;T++){S=V[T];X=S[1];if(this.options.stringParams){if(X.depth){this.addDepth(X.depth)}this.opcode("getContext",X.depth||0);this.opcode("pushStringParam",X.stringModeValue,X.type)}else{this.accept(X)}this.opcode("assignToHash",S[0])}this.opcode("popHash")},partial:function(T){var S=T.partialName;this.usePartial=true;if(T.context){this.ID(T.context)}else{this.opcode("push","depth0")}this.opcode("invokePartial",S.name);this.opcode("append")},content:function(S){this.opcode("appendContent",S.string)},mustache:function(U){var T=this.options;var S=this.classifyMustache(U);if(S==="simple"){this.simpleMustache(U)}else{if(S==="helper"){this.helperMustache(U)}else{this.ambiguousMustache(U)}}if(U.escaped&&!T.noEscape){this.opcode("appendEscaped")}else{this.opcode("append")}},ambiguousMustache:function(X,U,S){var W=X.id,V=W.parts[0],T=U!=null||S!=null;this.opcode("getContext",W.depth);this.opcode("pushProgram",U);this.opcode("pushProgram",S);this.opcode("invokeAmbiguous",V,T)},simpleMustache:function(T){var S=T.id;if(S.type==="DATA"){this.DATA(S)}else{if(S.parts.length){this.ID(S)}else{this.addDepth(S.depth);this.opcode("getContext",S.depth);this.opcode("pushContext")}}this.opcode("resolvePossibleLambda")},helperMustache:function(W,T,S){var V=this.setupFullMustacheParams(W,T,S),U=W.id.parts[0];if(this.options.knownHelpers[U]){this.opcode("invokeKnownHelper",V.length,U)}else{if(this.options.knownHelpersOnly){throw new Error("You specified knownHelpersOnly, but used the unknown helper "+U)}else{this.opcode("invokeHelper",V.length,U)}}},ID:function(V){this.addDepth(V.depth);this.opcode("getContext",V.depth);var U=V.parts[0];if(!U){this.opcode("pushContext")}else{this.opcode("lookupOnContext",V.parts[0])}for(var S=1,T=V.parts.length;S<T;S++){this.opcode("lookup",V.parts[S])}},DATA:function(V){this.options.data=true;if(V.id.isScoped||V.id.depth){throw new G.Exception("Scoped data references are not supported: "+V.original)}this.opcode("lookupData");var S=V.id.parts;for(var T=0,U=S.length;T<U;T++){this.opcode("lookup",S[T])}},STRING:function(S){this.opcode("pushString",S.string)},INTEGER:function(S){this.opcode("pushLiteral",S.integer)},BOOLEAN:function(S){this.opcode("pushLiteral",S.bool)},comment:function(){},opcode:function(S){this.opcodes.push({opcode:S,args:[].slice.call(arguments,1)})},declare:function(S,T){this.opcodes.push({opcode:"DECLARE",name:S,value:T})},addDepth:function(S){if(isNaN(S)){throw new Error("EWOT")}if(S===0){return}if(!this.depths[S]){this.depths[S]=true;this.depths.list.push(S)}},classifyMustache:function(W){var S=W.isHelper;var V=W.eligibleHelper;var T=this.options;if(V&&!S){var U=W.id.parts[0];if(T.knownHelpers[U]){S=true}else{if(T.knownHelpersOnly){V=false}}}if(S){return"helper"}else{if(V){return"ambiguous"}else{return"simple"}}},pushParams:function(U){var S=U.length,T;while(S--){T=U[S];if(this.options.stringParams){if(T.depth){this.addDepth(T.depth)}this.opcode("getContext",T.depth||0);this.opcode("pushStringParam",T.stringModeValue,T.type)}else{this[T.type](T)}}},setupMustacheParams:function(T){var S=T.params;this.pushParams(S);if(T.hash){this.hash(T.hash)}else{this.opcode("emptyHash")}return S},setupFullMustacheParams:function(V,T,S){var U=V.params;this.pushParams(U);this.opcode("pushProgram",T);this.opcode("pushProgram",S);if(V.hash){this.hash(V.hash)}else{this.opcode("emptyHash")}return U}};var K=function(S){this.value=S};M.prototype={nameLookup:function(T,S){if(/^[0-9]+$/.test(S)){return T+"["+S+"]"}else{if(M.isValidJavaScriptVariableName(S)){return T+"."+S}else{return T+"['"+S+"']"}}},appendToBuffer:function(S){if(this.environment.isSimple){return"return "+S+";"}else{return{appendToBuffer:true,content:S,toString:function(){return"buffer += "+S+";"}}}},initializeBuffer:function(){return this.quotedString("")},namespace:"Handlebars",compile:function(T,V,S,X){this.environment=T;this.options=V||{};G.log(G.logger.DEBUG,this.environment.disassemble()+"\n\n");this.name=this.environment.name;this.isChild=!!S;this.context=S||{programs:[],environments:[],aliases:{}};this.preamble();this.stackSlot=0;this.stackVars=[];this.registers={list:[]};this.compileStack=[];this.inlineStack=[];this.compileChildren(T,V);var W=T.opcodes,U;this.i=0;for(E=W.length;this.i<E;this.i++){U=W[this.i];if(U.opcode==="DECLARE"){this[U.name]=U.value}else{this[U.opcode].apply(this,U.args)}}return this.createFunctionContext(X)},nextOpcode:function(){var S=this.environment.opcodes;return S[this.i+1]},eat:function(){this.i=this.i+1},preamble:function(){var U=[];if(!this.isChild){var S=this.namespace;var T="helpers = this.merge(helpers, "+S+".helpers);";if(this.environment.usePartial){T=T+" partials = this.merge(partials, "+S+".partials);"}if(this.options.data){T=T+" data = data || {};"}U.push(T)}else{U.push("")}if(!this.environment.isSimple){U.push(", buffer = "+this.initializeBuffer())}else{U.push("")}this.lastContext=0;this.source=U},createFunctionContext:function(S){var Y=this.stackVars.concat(this.registers.list);if(Y.length>0){this.source[1]=this.source[1]+", "+Y.join(", ")}if(!this.isChild){for(var b in this.context.aliases){if(this.context.aliases.hasOwnProperty(b)){this.source[1]=this.source[1]+", "+b+"="+this.context.aliases[b]}}}if(this.source[1]){this.source[1]="var "+this.source[1].substring(2)+";"}if(!this.isChild){this.source[1]+="\n"+this.context.programs.join("\n")+"\n"}if(!this.environment.isSimple){this.source.push("return buffer;")}var V=this.isChild?["depth0","data"]:["Handlebars","depth0","helpers","partials","data"];for(var U=0,W=this.environment.depths.list.length;U<W;U++){V.push("depth"+this.environment.depths.list[U])}var X=this.mergeSource();if(!this.isChild){var T=G.COMPILER_REVISION,Z=G.REVISION_CHANGES[T];X="this.compilerInfo = ["+T+",'"+Z+"'];\n"+X}if(S){V.push(X);return Function.apply(this,V)}else{var a="function "+(this.name||"")+"("+V.join(",")+") {\n  "+X+"}";G.log(G.logger.DEBUG,a+"\n\n");return a}},mergeSource:function(){var T="",V;for(var S=0,U=this.source.length;S<U;S++){var W=this.source[S];if(W.appendToBuffer){if(V){V=V+"\n    + "+W.content}else{V=W.content}}else{if(V){T+="buffer += "+V+";\n  ";V=P}T+=W+"\n  "}}return T},blockValue:function(){this.context.aliases.blockHelperMissing="helpers.blockHelperMissing";var S=["depth0"];this.setupParams(0,S);this.replaceStack(function(T){S.splice(1,0,T);return"blockHelperMissing.call("+S.join(", ")+")"})},ambiguousBlockValue:function(){this.context.aliases.blockHelperMissing="helpers.blockHelperMissing";var T=["depth0"];this.setupParams(0,T);var S=this.topStack();T.splice(1,0,S);T[T.length-1]="options";this.source.push("if (!"+this.lastHelper+") { "+S+" = blockHelperMissing.call("+T.join(", ")+"); }")},appendContent:function(S){this.source.push(this.appendToBuffer(this.quotedString(S)))},append:function(){this.flushInline();var S=this.popStack();this.source.push("if("+S+" || "+S+" === 0) { "+this.appendToBuffer(S)+" }");if(this.environment.isSimple){this.source.push("else { "+this.appendToBuffer("''")+" }")}},appendEscaped:function(){this.context.aliases.escapeExpression="this.escapeExpression";this.source.push(this.appendToBuffer("escapeExpression("+this.popStack()+")"))},getContext:function(S){if(this.lastContext!==S){this.lastContext=S}},lookupOnContext:function(S){this.push(this.nameLookup("depth"+this.lastContext,S,"context"))},pushContext:function(){this.pushStackLiteral("depth"+this.lastContext)},resolvePossibleLambda:function(){this.context.aliases.functionType='"function"';this.replaceStack(function(S){return"typeof "+S+" === functionType ? "+S+".apply(depth0) : "+S})},lookup:function(S){this.replaceStack(function(T){return T+" == null || "+T+" === false ? "+T+" : "+this.nameLookup(T,S,"context")})},lookupData:function(S){this.push("data")},pushStringParam:function(T,S){this.pushStackLiteral("depth"+this.lastContext);this.pushString(S);if(typeof T==="string"){this.pushString(T)}else{this.pushStackLiteral(T)}},emptyHash:function(){this.pushStackLiteral("{}");if(this.options.stringParams){this.register("hashTypes","{}");this.register("hashContexts","{}")}},pushHash:function(){this.hash={values:[],types:[],contexts:[]}},popHash:function(){var S=this.hash;this.hash=P;if(this.options.stringParams){this.register("hashContexts","{"+S.contexts.join(",")+"}");this.register("hashTypes","{"+S.types.join(",")+"}")}this.push("{\n    "+S.values.join(",\n    ")+"\n  }")},pushString:function(S){this.pushStackLiteral(this.quotedString(S))},push:function(S){this.inlineStack.push(S);return S},pushLiteral:function(S){this.pushStackLiteral(S)},pushProgram:function(S){if(S!=null){this.pushStackLiteral(this.programExpression(S))}else{this.pushStackLiteral(null)}},invokeHelper:function(T,U){this.context.aliases.helperMissing="helpers.helperMissing";var V=this.lastHelper=this.setupHelper(T,U,true);var S=this.nameLookup("depth"+this.lastContext,U,"context");this.push(V.name+" || "+S);this.replaceStack(function(W){return W+" ? "+W+".call("+V.callParams+") : helperMissing.call("+V.helperMissingParams+")"})},invokeKnownHelper:function(S,T){var U=this.setupHelper(S,T);this.push(U.name+".call("+U.callParams+")")},invokeAmbiguous:function(U,V){this.context.aliases.functionType='"function"';this.pushStackLiteral("{}");var W=this.setupHelper(0,U,V);var T=this.lastHelper=this.nameLookup("helpers",U,"helper");var S=this.nameLookup("depth"+this.lastContext,U,"context");var X=this.nextStack();this.source.push("if ("+X+" = "+T+") { "+X+" = "+X+".call("+W.callParams+"); }");this.source.push("else { "+X+" = "+S+"; "+X+" = typeof "+X+" === functionType ? "+X+".apply(depth0) : "+X+"; }")},invokePartial:function(S){var T=[this.nameLookup("partials",S,"partial"),"'"+S+"'",this.popStack(),"helpers","partials"];if(this.options.data){T.push("data")}this.context.aliases.self="this";this.push("self.invokePartial("+T.join(", ")+")")},assignToHash:function(T){var V=this.popStack(),S,W;if(this.options.stringParams){W=this.popStack();S=this.popStack()}var U=this.hash;if(S){U.contexts.push("'"+T+"': "+S)}if(W){U.types.push("'"+T+"': "+W)}U.values.push("'"+T+"': ("+V+")")},compiler:M,compileChildren:function(U,W){var T=U.children,Z,Y;for(var V=0,X=T.length;V<X;V++){Z=T[V];Y=new this.compiler();var S=this.matchExistingProgram(Z);if(S==null){this.context.programs.push("");S=this.context.programs.length;Z.index=S;Z.name="program"+S;this.context.programs[S]=Y.compile(Z,W,this.context);this.context.environments[S]=Z}else{Z.index=S;Z.name="program"+S}}},matchExistingProgram:function(T){for(var S=0,U=this.context.environments.length;S<U;S++){var V=this.context.environments[S];if(V&&V.equals(T)){return S}}},programExpression:function(S){this.context.aliases.self="this";if(S==null){return"self.noop"}var V=this.environment.children[S],T=V.depths.list,Y;var W=[V.index,V.name,"data"];for(var U=0,X=T.length;U<X;U++){Y=T[U];if(Y===1){W.push("depth0")}else{W.push("depth"+(Y-1))}}return(T.length===0?"self.program(":"self.programWithDepth(")+W.join(", ")+")"},register:function(S,T){this.useRegister(S);this.source.push(S+" = "+T+";")},useRegister:function(S){if(!this.registers[S]){this.registers[S]=true;this.registers.list.push(S)}},pushStackLiteral:function(S){return this.push(new K(S))},pushStack:function(T){this.flushInline();var S=this.incrStack();if(T){this.source.push(S+" = "+T+";")}this.compileStack.push(S);return S},replaceStack:function(S){var V="",W=this.isInline(),T;if(W){var X=this.popStack(true);if(X instanceof K){T=X.value}else{var Y=this.stackSlot?this.topStackName():this.incrStack();V="("+this.push(Y)+" = "+X+"),";T=this.topStack()}}else{T=this.topStack()}var U=S.call(this,T);if(W){if(this.inlineStack.length||this.compileStack.length){this.popStack()}this.push("("+V+U+")")}else{if(!/^stack/.test(T)){T=this.nextStack()}this.source.push(T+" = ("+V+U+");")}return T},nextStack:function(){return this.pushStack()},incrStack:function(){this.stackSlot++;if(this.stackSlot>this.stackVars.length){this.stackVars.push("stack"+this.stackSlot)}return this.topStackName()},topStackName:function(){return"stack"+this.stackSlot},flushInline:function(){var T=this.inlineStack;if(T.length){this.inlineStack=[];for(var S=0,V=T.length;S<V;S++){var U=T[S];if(U instanceof K){this.compileStack.push(U)}else{this.pushStack(U)}}}},isInline:function(){return this.inlineStack.length},popStack:function(S){var T=this.isInline(),U=(T?this.inlineStack:this.compileStack).pop();if(!S&&(U instanceof K)){return U.value}else{if(!T){this.stackSlot--}return U}},topStack:function(S){var U=(this.isInline()?this.inlineStack:this.compileStack),T=U[U.length-1];if(!S&&(T instanceof K)){return T.value}else{return T}},quotedString:function(S){return'"'+S.replace(/\\/g,"\\\\").replace(/"/g,'\\"').replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029")+'"'},setupHelper:function(T,U,S){var W=[];this.setupParams(T,W,S);var V=this.nameLookup("helpers",U,"helper");return{params:W,name:V,callParams:["depth0"].concat(W).join(", "),helperMissingParams:S&&["depth0",this.quotedString(U)].concat(W).join(", ")}},setupParams:function(a,W,S){var X=[],Z=[],U=[],T,b,Y;X.push("hash:"+this.popStack());b=this.popStack();Y=this.popStack();if(Y||b){if(!Y){this.context.aliases.self="this";Y="self.noop"}if(!b){this.context.aliases.self="this";b="self.noop"}X.push("inverse:"+b);X.push("fn:"+Y)}for(var V=0;V<a;V++){T=this.popStack();W.push(T);if(this.options.stringParams){U.push(this.popStack());Z.push(this.popStack())}}if(this.options.stringParams){X.push("contexts:["+Z.join(",")+"]");X.push("types:["+U.join(",")+"]");X.push("hashContexts:hashContexts");X.push("hashTypes:hashTypes")}if(this.options.data){X.push("data:data")}X="{"+X.join(",")+"}";if(S){this.register("options",X);W.push("options")}else{W.push(X)}return W.join(", ")}};var A=("break else new var case finally return void catch for switch while continue function this with default if throw delete in try do instanceof typeof abstract enum int short boolean export interface static byte extends long super char final native synchronized class float package throws const goto private transient debugger implements protected volatile double import public let yield").split(" ");var I=M.RESERVED_WORDS={};for(var B=0,E=A.length;B<E;B++){I[A[B]]=true}M.isValidJavaScriptVariableName=function(S){if(!M.RESERVED_WORDS[S]&&/^[a-zA-Z_$][0-9a-zA-Z_$]+$/.test(S)){return true}return false};G.precompile=function(V,U){if(V==null||(typeof V!=="string"&&V.constructor!==G.AST.ProgramNode)){throw new G.Exception("You must pass a string or Handlebars AST to Handlebars.precompile. You passed "+V)}U=U||{};if(!("data" in U)){U.data=true}var T=G.parse(V);var S=new C().compile(T,U);return new M().compile(S,U)};G.compile=function(V,U){if(V==null||(typeof V!=="string"&&V.constructor!==G.AST.ProgramNode)){throw new G.Exception("You must pass a string or Handlebars AST to Handlebars.compile. You passed "+V)}U=U||{};if(!("data" in U)){U.data=true}var S;function T(){var X=G.parse(V);var W=new C().compile(X,U);var Y=new M().compile(W,U,P,true);return G.template(Y)}return function(W,X){if(!S){S=T()}return S.call(this,W,X)}};G.VM={template:function(T){var S={escapeExpression:G.Utils.escapeExpression,invokePartial:G.VM.invokePartial,programs:[],program:function(U,V,X){var W=this.programs[U];if(X){W=G.VM.program(U,V,X)}else{if(!W){W=this.programs[U]=G.VM.program(U,V)}}return W},merge:function(V,U){var W=V||U;if(V&&U){W={};G.Utils.extend(W,U);G.Utils.extend(W,V)}return W},programWithDepth:G.VM.programWithDepth,noop:G.VM.noop,compilerInfo:null};return function(V,Y){Y=Y||{};var U=T.call(S,G,V,Y.helpers,Y.partials,Y.data);var W=S.compilerInfo||[],Z=W[0]||1,a=G.COMPILER_REVISION;if(Z!==a){if(Z<a){var X=G.REVISION_CHANGES[a],b=G.REVISION_CHANGES[Z];throw"Template was precompiled with an older version of Handlebars than the current runtime. Please update your precompiler to a newer version ("+X+") or downgrade your runtime to an older version ("+b+")."}else{throw"Template was precompiled with a newer version of Handlebars than the current runtime. Please update your runtime to a newer version ("+W[1]+")."}}return U}},programWithDepth:function(S,T,W){var V=Array.prototype.slice.call(arguments,3);var U=function(X,Y){Y=Y||{};return T.apply(this,[X,Y.data||W].concat(V))};U.program=S;U.depth=V.length;return U},program:function(S,T,V){var U=function(W,X){X=X||{};return T(W,X.data||V)};U.program=S;U.depth=0;return U},noop:function(){return""},invokePartial:function(X,V,S,Y,W,T){var U={helpers:Y,partials:W,data:T};if(X===P){throw new G.Exception("The partial "+V+" could not be found")}else{if(X instanceof Function){return X(S,U)}else{if(!G.compile){throw new G.Exception("The partial "+V+" could not be compiled when running in runtime-only mode")}else{W[V]=G.compile(X,{data:T!==P});return W[V](S,U)}}}}};G.template=G.VM.template})(Handlebars);