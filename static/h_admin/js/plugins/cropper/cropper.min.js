/*
 * Cropper v0.7.6-beta
 * https://github.com/fengyuanchen/cropper
 *
 * Copyright 2014 <PERSON><PERSON> Chen
 * Released under the MIT license
 */
!function(A){"function"==typeof define&&define.amd?define(["jquery"],A):A("object"==typeof exports?require("jquery"):jQuery)}(function(AC){var AA=AC(window),AB=AC(document),AF=window.location,Q=!0,AD=!1,AE=null,Au=0/0,Av=1/0,As="undefined",At="directive",Ay=".cropper",Az=/^(e|n|w|s|ne|nw|sw|se|all|crop|move|zoom)$/,Aw=/^(x|y|width|height)$/,Ax=/^(naturalWidth|naturalHeight|width|height|aspectRatio|ratio|rotate)$/,Am="cropper-modal",An="cropper-hidden",Ak="cropper-invisible",Al="cropper-move",Aq="cropper-crop",Ar="cropper-disabled",Ao="mousedown touchstart",Ap="mousemove touchmove",Ai="mouseup mouseleave touchend touchleave touchcancel",Aj="wheel mousewheel DOMMouseScroll",Ah="resize"+Ay,Ac="dblclick",Aa="build"+Ay,Ab="built"+Ay,Af="dragstart"+Ay,Ag="dragmove"+Ay,Ad="dragend"+Ay,Ae=function(A){return"number"==typeof A},U=function(A,B){this.element=A,this.$element=AC(A),this.defaults=AC.extend({},U.DEFAULTS,AC.isPlainObject(B)?B:{}),this.$original=AE,this.ready=AD,this.built=AD,this.cropped=AD,this.rotated=AD,this.disabled=AD,this.replaced=AD,this.init()},V=Math.round,S=Math.sqrt,T=Math.min,Y=Math.max,Z=Math.abs,W=Math.sin,X=Math.cos,R=parseFloat;U.prototype={constructor:U,support:{canvas:AC.isFunction(AC("<canvas>")[0].getContext)},init:function(){var A=this.defaults;AC.each(A,function(C,B){switch(C){case"aspectRatio":A[C]=Z(R(B))||Au;break;case"autoCropArea":A[C]=Z(R(B))||0.8;break;case"minWidth":case"minHeight":A[C]=Z(R(B))||0;break;case"maxWidth":case"maxHeight":A[C]=Z(R(B))||Av}}),this.image={rotate:0},this.load()},load:function(){var E,F,B=this,G=this.$element,A=this.element,C=this.image,D="";G.is("img")?F=G.prop("src"):G.is("canvas")&&this.support.canvas&&(F=A.toDataURL()),F&&(this.replaced&&(C.rotate=0),this.defaults.checkImageOrigin&&(G.prop("crossOrigin")||this.isCrossOriginURL(F))&&(D=" crossOrigin"),this.$clone=E=AC("<img"+D+' src="'+F+'">'),E.one("load",function(){C.naturalWidth=this.naturalWidth||E.width(),C.naturalHeight=this.naturalHeight||E.height(),C.aspectRatio=C.naturalWidth/C.naturalHeight,B.url=F,B.ready=Q,B.build()}),E.addClass(Ak).prependTo("body"))},isCrossOriginURL:function(B){var A=B.match(/^(https?:)\/\/([^\:\/\?#]+):?(\d*)/i);return !A||A[1]===AF.protocol&&A[2]===AF.hostname&&A[3]===AF.port?AD:Q},build:function(){var C,B,D=this.$element,A=this.defaults;this.ready&&(this.built&&this.unbuild(),D.one(Aa,A.build),C=AC.Event(Aa),D.trigger(C),C.isDefaultPrevented()||(this.$cropper=B=AC(U.TEMPLATE),D.addClass(An),this.$clone.removeClass(Ak).prependTo(B),this.rotated||(this.$original=this.$clone.clone(),this.$original.addClass(An).prependTo(this.$cropper),this.originalImage=AC.extend({},this.image)),this.$container=D.parent(),this.$container.append(B),this.$canvas=B.find(".cropper-canvas"),this.$dragger=B.find(".cropper-dragger"),this.$viewer=B.find(".cropper-viewer"),A.autoCrop?this.cropped=Q:this.$dragger.addClass(An),A.dragCrop&&this.setDragMode("crop"),A.modal&&this.$canvas.addClass(Am),!A.dashed&&this.$dragger.find(".cropper-dashed").addClass(An),!A.movable&&this.$dragger.find(".cropper-face").data(At,"move"),!A.resizable&&this.$dragger.find(".cropper-line, .cropper-point").addClass(An),this.$scope=A.multiple?this.$cropper:AB,this.addListeners(),this.initPreview(),this.built=Q,this.update(),D.one(Ab,A.built),D.trigger(Ab)))},unbuild:function(){this.built&&(this.built=AD,this.removeListeners(),this.$preview.empty(),this.$preview=AE,this.$dragger=AE,this.$canvas=AE,this.$container=AE,this.$cropper.remove(),this.$cropper=AE)},update:function(A){this.initContainer(),this.initCropper(),this.initImage(),this.initDragger(),A?(this.setData(A,Q),this.setDragMode("crop")):this.setData(this.defaults.data)},resize:function(){clearTimeout(this.resizing),this.resizing=setTimeout(AC.proxy(this.update,this,this.getData()),200)},preview:function(){var D=this.image,E=this.dragger,B=D.width,C=D.height,F=E.left-D.left,A=E.top-D.top;this.$viewer.find("img").css({width:V(B),height:V(C),marginLeft:-V(F),marginTop:-V(A)}),this.$preview.each(function(){var H=AC(this),G=H.width()/E.width;H.find("img").css({width:V(B*G),height:V(C*G),marginLeft:-V(F*G),marginTop:-V(A*G)})})},addListeners:function(){var A=this.defaults;this.$element.on(Af,A.dragstart).on(Ag,A.dragmove).on(Ad,A.dragend),this.$cropper.on(Ao,this._dragstart=AC.proxy(this.dragstart,this)).on(Ac,this._dblclick=AC.proxy(this.dblclick,this)),A.zoomable&&this.$cropper.on(Aj,this._wheel=AC.proxy(this.wheel,this)),this.$scope.on(Ap,this._dragmove=AC.proxy(this.dragmove,this)).on(Ai,this._dragend=AC.proxy(this.dragend,this)),AA.on(Ah,this._resize=AC.proxy(this.resize,this))},removeListeners:function(){var A=this.defaults;this.$element.off(Af,A.dragstart).off(Ag,A.dragmove).off(Ad,A.dragend),this.$cropper.off(Ao,this._dragstart).off(Ac,this._dblclick),A.zoomable&&this.$cropper.off(Aj,this._wheel),this.$scope.off(Ap,this._dragmove).off(Ai,this._dragend),AA.off(Ah,this._resize)},initPreview:function(){var A='<img src="'+this.url+'">';this.$preview=AC(this.defaults.preview),this.$viewer.html(A),this.$preview.html(A).find("img").css("cssText","min-width:0!important;min-height:0!important;max-width:none!important;max-height:none!important;")},initContainer:function(){var A=this.$container;this.container={width:Y(A.width(),300),height:Y(A.height(),150)}},initCropper:function(){var C,A=this.container,B=this.image;B.naturalWidth*A.height/B.naturalHeight-A.width>=0?(C={width:A.width,height:A.width/B.aspectRatio,left:0},C.top=(A.height-C.height)/2):(C={width:A.height*B.aspectRatio,height:A.height,top:0},C.left=(A.width-C.width)/2),this.$cropper.css({width:V(C.width),height:V(C.height),left:V(C.left),top:V(C.top)}),this.cropper=C},initImage:function(){var B=this.image,C=this.cropper,A={_width:C.width,_height:C.height,width:C.width,height:C.height,left:0,top:0,ratio:C.width/B.naturalWidth};this.defaultImage=AC.extend({},B,A),B._width!==C.width||B._height!==C.height?AC.extend(B,A):(B=AC.extend({},A,B),this.replaced&&(this.replaced=AD,B.ratio=A.ratio)),this.image=B,this.renderImage()},renderImage:function(B){var A=this.image;"zoom"===B&&(A.left-=(A.width-A.oldWidth)/2,A.top-=(A.height-A.oldHeight)/2),A.left=T(Y(A.left,A._width-A.width),0),A.top=T(Y(A.top,A._height-A.height),0),this.$clone.css({width:V(A.width),height:V(A.height),marginLeft:V(A.left),marginTop:V(A.top)}),B&&(this.defaults.done(this.getData()),this.preview())},initDragger:function(){var C,D=this.defaults,A=this.cropper,B=D.aspectRatio||this.image.aspectRatio,E=this.image.ratio;C=A.height*B-A.width>=0?{height:A.width/B,width:A.width,left:0,top:(A.height-A.width/B)/2,maxWidth:A.width,maxHeight:A.width/B}:{height:A.height,width:A.height*B,left:(A.width-A.height*B)/2,top:0,maxWidth:A.height*B,maxHeight:A.height},C.minWidth=0,C.minHeight=0,D.aspectRatio?(isFinite(D.maxWidth)?(C.maxWidth=T(C.maxWidth,D.maxWidth*E),C.maxHeight=C.maxWidth/B):isFinite(D.maxHeight)&&(C.maxHeight=T(C.maxHeight,D.maxHeight*E),C.maxWidth=C.maxHeight*B),D.minWidth>0?(C.minWidth=Y(0,D.minWidth*E),C.minHeight=C.minWidth/B):D.minHeight>0&&(C.minHeight=Y(0,D.minHeight*E),C.minWidth=C.minHeight*B)):(C.maxWidth=T(C.maxWidth,D.maxWidth*E),C.maxHeight=T(C.maxHeight,D.maxHeight*E),C.minWidth=Y(0,D.minWidth*E),C.minHeight=Y(0,D.minHeight*E)),C.minWidth=T(C.maxWidth,C.minWidth),C.minHeight=T(C.maxHeight,C.minHeight),C.height*=D.autoCropArea,C.width*=D.autoCropArea,C.left=(A.width-C.width)/2,C.top=(A.height-C.height)/2,C.oldLeft=C.left,C.oldTop=C.top,this.defaultDragger=C,this.dragger=AC.extend({},C)},renderDragger:function(){var B=this.dragger,A=this.cropper;B.width>B.maxWidth?(B.width=B.maxWidth,B.left=B.oldLeft):B.width<B.minWidth&&(B.width=B.minWidth,B.left=B.oldLeft),B.height>B.maxHeight?(B.height=B.maxHeight,B.top=B.oldTop):B.height<B.minHeight&&(B.height=B.minHeight,B.top=B.oldTop),B.left=T(Y(B.left,0),A.width-B.width),B.top=T(Y(B.top,0),A.height-B.height),B.oldLeft=B.left,B.oldTop=B.top,this.dragger=B,this.disabled||this.defaults.done(this.getData()),this.$dragger.css({width:V(B.width),height:V(B.height),left:V(B.left),top:V(B.top)}),this.preview()},reset:function(A){this.cropped&&(A&&(this.defaults.data={}),this.image=AC.extend({},this.defaultImage),this.renderImage(),this.dragger=AC.extend({},this.defaultDragger),this.setData(this.defaults.data))},clear:function(){this.cropped&&(this.cropped=AD,this.setData({x:0,y:0,width:0,height:0}),this.$canvas.removeClass(Am),this.$dragger.addClass(An))},destroy:function(){var A=this.$element;this.ready&&(this.unbuild(),A.removeClass(An).removeData("cropper"),this.rotated&&A.attr("src",this.$original.attr("src")))},replace:function(E,F){var B,A=this,C=this.$element,D=this.element;E&&E!==this.url&&E!==C.attr("src")&&(F||(this.rotated=AD,this.replaced=Q),C.is("img")?(C.attr("src",E),this.load()):C.is("canvas")&&this.support.canvas&&(B=D.getContext("2d"),AC('<img src="'+E+'">').one("load",function(){D.width=this.width,D.height=this.height,B.clearRect(0,0,D.width,D.height),B.drawImage(this,0,0),A.load()})))},setData:function(D,E){var B=this.cropper,C=this.dragger,F=this.image,A=this.defaults.aspectRatio;this.built&&typeof D!==As&&((D===AE||AC.isEmptyObject(D))&&(C=AC.extend({},this.defaultDragger)),AC.isPlainObject(D)&&!AC.isEmptyObject(D)&&(E||(this.defaults.data=D),D=this.transformData(D),Ae(D.x)&&D.x<=B.width-F.left&&(C.left=D.x+F.left),Ae(D.y)&&D.y<=B.height-F.top&&(C.top=D.y+F.top),A?Ae(D.width)&&D.width<=C.maxWidth&&D.width>=C.minWidth?(C.width=D.width,C.height=C.width/A):Ae(D.height)&&D.height<=C.maxHeight&&D.height>=C.minHeight&&(C.height=D.height,C.width=C.height*A):(Ae(D.width)&&D.width<=C.maxWidth&&D.width>=C.minWidth&&(C.width=D.width),Ae(D.height)&&D.height<=C.maxHeight&&D.height>=C.minHeight&&(C.height=D.height))),this.dragger=C,this.renderDragger())},getData:function(D){var B=this.dragger,C=this.image,A={};return this.built&&(A={x:B.left-C.left,y:B.top-C.top,width:B.width,height:B.height},A=this.transformData(A,Q,D)),A},transformData:function(C,D,A){var B=this.image.ratio,E={};return AC.each(C,function(G,F){F=R(F),Aw.test(G)&&!isNaN(F)&&(E[G]=D?A?V(F/B):F/B:F*B)}),E},setAspectRatio:function(B){var A="auto"===B;B=R(B),(A||!isNaN(B)&&B>0)&&(this.defaults.aspectRatio=A?Au:B,this.built&&(this.initDragger(),this.renderDragger()))},getImageData:function(){var A={};return this.ready&&AC.each(this.image,function(C,B){Ax.test(C)&&(A[C]=B)}),A},getDataURL:function(E,F,B){var C,G=AC("<canvas>")[0],A=this.getData(),D="";return AC.isPlainObject(E)||(B=F,F=E,E={}),E=AC.extend({width:A.width,height:A.height},E),this.cropped&&this.support.canvas&&(G.width=E.width,G.height=E.height,C=G.getContext("2d"),"image/jpeg"===F&&(C.fillStyle="#fff",C.fillRect(0,0,E.width,E.height)),C.drawImage(this.$clone[0],A.x,A.y,A.width,A.height,0,0,E.width,E.height),D=G.toDataURL(F,B)),D},setDragMode:function(E){var C=this.$canvas,D=this.defaults,B=AD,A=AD;if(this.built&&!this.disabled){switch(E){case"crop":D.dragCrop&&(B=Q,C.data(At,E));break;case"move":A=Q,C.data(At,E);break;default:C.removeData(At)}C.toggleClass(Aq,B).toggleClass(Al,A)}},enable:function(){this.built&&(this.disabled=AD,this.$cropper.removeClass(Ar))},disable:function(){this.built&&(this.disabled=Q,this.$cropper.addClass(Ar))},rotate:function(B){var A=this.image;B=R(B)||0,this.built&&0!==B&&!this.disabled&&this.defaults.rotatable&&this.support.canvas&&(this.rotated=Q,B=A.rotate=(A.rotate+B)%360,this.replace(this.getRotatedDataURL(B),!0))},getRotatedDataURL:function(H){var I=AC("<canvas>")[0],L=I.getContext("2d"),A=H*Math.PI/180,J=Z(H)%180,K=J>90?180-J:J,D=K*Math.PI/180,E=this.originalImage,B=E.naturalWidth,C=E.naturalHeight,F=Z(B*X(D)+C*W(D)),G=Z(B*W(D)+C*X(D));return I.width=F,I.height=G,L.save(),L.translate(F/2,G/2),L.rotate(A),L.drawImage(this.$original[0],-B/2,-C/2,B,C),L.restore(),I.toDataURL()},zoom:function(E){var C,D,A,B=this.image;E=R(E),this.built&&E&&!this.disabled&&this.defaults.zoomable&&(C=B.width*(1+E),D=B.height*(1+E),A=C/B._width,A>10||(1>A&&(C=B._width,D=B._height),this.setDragMode(1>=A?"crop":"move"),B.oldWidth=B.width,B.oldHeight=B.height,B.width=C,B.height=D,B.ratio=B.width/B.naturalWidth,this.renderImage("zoom")))},dblclick:function(){this.disabled||this.setDragMode(this.$canvas.hasClass(Aq)?"move":"crop")},wheel:function(F){var D,E=F.originalEvent,B=117.25,C=5,G=166.666656494141,A=0.1;this.disabled||(F.preventDefault(),E.deltaY?(D=E.deltaY,D=D%C===0?D/C:D%B===0?D/B:D/G):D=E.wheelDelta?-E.wheelDelta/120:E.detail?E.detail/3:0,this.zoom(D*A))},dragstart:function(E){var F,B,A,C=E.originalEvent.touches,D=E;if(!this.disabled){if(C){if(A=C.length,A>1){if(!this.defaults.zoomable||2!==A){return}D=C[1],this.startX2=D.pageX,this.startY2=D.pageY,F="zoom"}D=C[0]}if(F=F||AC(D.target).data(At),Az.test(F)){if(E.preventDefault(),B=AC.Event(Af),this.$element.trigger(B),B.isDefaultPrevented()){return}this.directive=F,this.cropping=AD,this.startX=D.pageX,this.startY=D.pageY,"crop"===F&&(this.cropping=Q,this.$canvas.addClass(Am))}}},dragmove:function(C){var D,A,B=C.originalEvent.touches,E=C;if(!this.disabled){if(B){if(A=B.length,A>1){if(!this.defaults.zoomable||2!==A){return}E=B[1],this.endX2=E.pageX,this.endY2=E.pageY}E=B[0]}if(this.directive){if(C.preventDefault(),D=AC.Event(Ag),this.$element.trigger(D),D.isDefaultPrevented()){return}this.endX=E.pageX,this.endY=E.pageY,this.dragging()}}},dragend:function(A){var B;if(!this.disabled&&this.directive){if(A.preventDefault(),B=AC.Event(Ad),this.$element.trigger(B),B.isDefaultPrevented()){return}this.cropping&&(this.cropping=AD,this.$canvas.toggleClass(Am,this.cropped&&this.defaults.modal)),this.directive=""}},dragging:function(){var O,M=this.directive,N=this.image,e=this.cropper,P=e.width,G=e.height,H=this.dragger,E=H.width,F=H.height,K=H.left,L=H.top,I=K+E,J=L+F,C=Q,A=this.defaults,B=A.aspectRatio,D={x:this.endX-this.startX,y:this.endY-this.startY};switch(B&&(D.X=D.y*B,D.Y=D.x/B),M){case"all":K+=D.x,L+=D.y;break;case"e":if(D.x>=0&&(I>=P||B&&(0>=L||J>=G))){C=AD;break}E+=D.x,B&&(F=E/B,L-=D.Y/2),0>E&&(M="w",E=0);break;case"n":if(D.y<=0&&(0>=L||B&&(0>=K||I>=P))){C=AD;break}F-=D.y,L+=D.y,B&&(E=F*B,K+=D.X/2),0>F&&(M="s",F=0);break;case"w":if(D.x<=0&&(0>=K||B&&(0>=L||J>=G))){C=AD;break}E-=D.x,K+=D.x,B&&(F=E/B,L+=D.Y/2),0>E&&(M="e",E=0);break;case"s":if(D.y>=0&&(J>=G||B&&(0>=K||I>=P))){C=AD;break}F+=D.y,B&&(E=F*B,K-=D.X/2),0>F&&(M="n",F=0);break;case"ne":if(B){if(D.y<=0&&(0>=L||I>=P)){C=AD;break}F-=D.y,L+=D.y,E=F*B}else{D.x>=0?P>I?E+=D.x:D.y<=0&&0>=L&&(C=AD):E+=D.x,D.y<=0?L>0&&(F-=D.y,L+=D.y):(F-=D.y,L+=D.y)}0>F&&(M="sw",F=0,E=0);break;case"nw":if(B){if(D.y<=0&&(0>=L||0>=K)){C=AD;break}F-=D.y,L+=D.y,E=F*B,K+=D.X}else{D.x<=0?K>0?(E-=D.x,K+=D.x):D.y<=0&&0>=L&&(C=AD):(E-=D.x,K+=D.x),D.y<=0?L>0&&(F-=D.y,L+=D.y):(F-=D.y,L+=D.y)}0>F&&(M="se",F=0,E=0);break;case"sw":if(B){if(D.x<=0&&(0>=K||J>=G)){C=AD;break}E-=D.x,K+=D.x,F=E/B}else{D.x<=0?K>0?(E-=D.x,K+=D.x):D.y>=0&&J>=G&&(C=AD):(E-=D.x,K+=D.x),D.y>=0?G>J&&(F+=D.y):F+=D.y}0>E&&(M="ne",F=0,E=0);break;case"se":if(B){if(D.x>=0&&(I>=P||J>=G)){C=AD;break}E+=D.x,F=E/B}else{D.x>=0?P>I?E+=D.x:D.y>=0&&J>=G&&(C=AD):E+=D.x,D.y>=0?G>J&&(F+=D.y):F+=D.y}0>E&&(M="nw",F=0,E=0);break;case"move":N.left+=D.x,N.top+=D.y,this.renderImage("move"),C=AD;break;case"zoom":A.zoomable&&(this.zoom(function(k,i,j,g,h,l){return(S(h*h+l*l)-S(j*j+g*g))/S(k*k+i*i)}(N.width,N.height,Z(this.startX-this.startX2),Z(this.startY-this.startY2),Z(this.endX-this.endX2),Z(this.endY-this.endY2))),this.endX2=this.startX2,this.endY2=this.startY2);break;case"crop":D.x&&D.y&&(O=this.$cropper.offset(),K=this.startX-O.left,L=this.startY-O.top,E=H.minWidth,F=H.minHeight,D.x>0?D.y>0?M="se":(M="ne",L-=F):D.y>0?(M="sw",K-=E):(M="nw",K-=E,L-=F),this.cropped||(this.cropped=Q,this.$dragger.removeClass(An)))}C&&(H.width=E,H.height=F,H.left=K,H.top=L,this.directive=M,this.renderDragger()),this.startX=this.endX,this.startY=this.endY}},U.TEMPLATE=function(B,A){return A=A.split(","),B.replace(/\d+/g,function(C){return A[C]})}('<0 6="5-container"><0 6="5-canvas"></0><0 6="5-dragger"><1 6="5-viewer"></1><1 6="5-8 8-h"></1><1 6="5-8 8-v"></1><1 6="5-face" 3-2="all"></1><1 6="5-7 7-e" 3-2="e"></1><1 6="5-7 7-n" 3-2="n"></1><1 6="5-7 7-w" 3-2="w"></1><1 6="5-7 7-s" 3-2="s"></1><1 6="5-4 4-e" 3-2="e"></1><1 6="5-4 4-n" 3-2="n"></1><1 6="5-4 4-w" 3-2="w"></1><1 6="5-4 4-s" 3-2="s"></1><1 6="5-4 4-ne" 3-2="ne"></1><1 6="5-4 4-nw" 3-2="nw"></1><1 6="5-4 4-sw" 3-2="sw"></1><1 6="5-4 4-se" 3-2="se"></1></0></0>',"div,span,directive,data,point,cropper,class,line,dashed"),U.DEFAULTS={aspectRatio:"auto",autoCropArea:0.8,data:{},done:AC.noop,preview:"",multiple:AD,autoCrop:Q,dragCrop:Q,dashed:Q,modal:Q,movable:Q,resizable:Q,zoomable:Q,rotatable:Q,checkImageOrigin:Q,minWidth:0,minHeight:0,maxWidth:Av,maxHeight:Av,build:AE,built:AE,dragstart:AE,dragmove:AE,dragend:AE},U.setDefaults=function(A){AC.extend(U.DEFAULTS,A)},U.other=AC.fn.cropper,AC.fn.cropper=function(B){var C,A=[].slice.call(arguments,1);return this.each(function(){var E,F=AC(this),D=F.data("cropper");D||F.data("cropper",D=new U(this,B)),"string"==typeof B&&AC.isFunction(E=D[B])&&(C=E.apply(D,A))}),typeof C!==As?C:this},AC.fn.cropper.Constructor=U,AC.fn.cropper.setDefaults=U.setDefaults,AC.fn.cropper.noConflict=function(){return AC.fn.cropper=U.other,this}});