(function(H){var F="0.3.4",G="hasOwnProperty",K=/[\.\/]/,A="*",I=function(){},J=function(M,L){return M-L},D,E,B={n:{}},C=function(Y,W){var X=B,g=E,L=Array.prototype.slice.call(arguments,2),Z=C.listeners(Y),U=0,V=!1,S,T=[],N={},O=[],P=D,M=[];D=Y,E=0;for(var R=0,Q=Z.length;R<Q;R++){"zIndex" in Z[R]&&(T.push(Z[R].zIndex),Z[R].zIndex<0&&(N[Z[R].zIndex]=Z[R]))}T.sort(J);while(T[U]<0){S=N[T[U++]],O.push(S.apply(W,L));if(E){E=g;return O}}for(R=0;R<Q;R++){S=Z[R];if("zIndex" in S){if(S.zIndex==T[U]){O.push(S.apply(W,L));if(E){break}do{U++,S=N[T[U]],S&&O.push(S.apply(W,L));if(E){break}}while(S)}else{N[S.zIndex]=S}}else{O.push(S.apply(W,L));if(E){break}}}E=g,D=P;return O.length?O:null};C.listeners=function(V){var T=V.split(K),U=B,W,X,N,O,M,R,S,P,Q=[U],L=[];for(O=0,M=T.length;O<M;O++){P=[];for(R=0,S=Q.length;R<S;R++){U=Q[R].n,X=[U[T[O]],U[A]],N=2;while(N--){W=X[N],W&&(P.push(W),L=L.concat(W.f||[]))}}Q=P}return L},C.on=function(Q,O){var P=Q.split(K),N=B;for(var L=0,M=P.length;L<M;L++){N=N.n,!N[P[L]]&&(N[P[L]]={n:{}}),N=N[P[L]]}N.f=N.f||[];for(L=0,M=N.f.length;L<M;L++){if(N.f[L]==O){return I}}N.f.push(O);return function(R){+R==+R&&(O.zIndex=+R)}},C.stop=function(){E=1},C.nt=function(L){if(L){return(new RegExp("(?:\\.|\\/|^)"+L+"(?:\\.|\\/|$)")).test(D)}return D},C.off=C.unbind=function(U,T){var V=U.split(K),W,N,O,M,R,S,P,Q=[B];for(M=0,R=V.length;M<R;M++){for(S=0;S<Q.length;S+=O.length-2){O=[S,1],W=Q[S].n;if(V[M]!=A){W[V[M]]&&O.push(W[V[M]])}else{for(N in W){W[G](N)&&O.push(W[N])}}Q.splice.apply(Q,O)}}for(M=0,R=Q.length;M<R;M++){W=Q[M];while(W.n){if(T){if(W.f){for(S=0,P=W.f.length;S<P;S++){if(W.f[S]==T){W.f.splice(S,1);break}}!W.f.length&&delete W.f}for(N in W.n){if(W.n[G](N)&&W.n[N].f){var L=W.n[N].f;for(S=0,P=L.length;S<P;S++){if(L[S]==T){L.splice(S,1);break}}!L.length&&delete W.n[N].f}}}else{delete W.f;for(N in W.n){W.n[G](N)&&W.n[N].f&&delete W.n[N].f}}W=W.n}}},C.once=function(N,L){var M=function(){var O=L.apply(this,arguments);C.unbind(N,M);return O};return C.on(N,M)},C.version=F,C.toString=function(){return"You are running Eve "+F},typeof module!="undefined"&&module.exports?module.exports=C:typeof define!="undefined"?define("eve",[],function(){return C}):H.eve=C})(this),function(){function AV(B){for(var A=0;A<Aw.length;A++){Aw[A].el.paper==B&&Aw.splice(A--,1)}}function AO(CI,CK,C,CJ,CD,CE){C=Bi(C);var CB,CC,CG,CH=[],CF,Cv,Cw,Cz=CI.ms,CA={},Cx={},Cy={};if(CJ){for(Cu=0,s=Aw.length;Cu<s;Cu++){var Ct=Aw[Cu];if(Ct.el.id==CK.id&&Ct.anim==CI){Ct.percent!=C?(Aw.splice(Cu,1),CG=1):CC=Ct,CK.attr(Ct.totalOrigin);break}}}else{CJ=+Cx}for(var Cu=0,s=CI.percents.length;Cu<s;Cu++){if(CI.percents[Cu]==C||CI.percents[Cu]>CJ*CI.top){C=CI.percents[Cu],Cv=CI.percents[Cu-1]||0,Cz=Cz/CI.top*(C-Cv),CF=CI.percents[Cu+1],CB=CI.anim[C];break}CJ&&CK.attr(CI.anim[CI.percents[Cu]])}if(!!CB){if(!CC){for(var a in CB){if(CB[B3](a)){if(Bm[B3](a)||CK.paper.customAttributes[B3](a)){CA[a]=CK.attr(a),CA[a]==null&&(CA[a]=Bl[a]),Cx[a]=CB[a];switch(Bm[a]){case Bw:Cy[a]=(Cx[a]-CA[a])/Cz;break;case"colour":CA[a]=B1.getRGB(CA[a]);var Z=B1.getRGB(Cx[a]);Cy[a]={r:(Z.r-CA[a].r)/Cz,g:(Z.g-CA[a].g)/Cz,b:(Z.b-CA[a].b)/Cz};break;case"path":var n=AT(CA[a],Cx[a]),r=n[1];CA[a]=n[0],Cy[a]=[];for(Cu=0,s=CA[a].length;Cu<s;Cu++){Cy[a][Cu]=[0];for(var c=1,g=CA[a][Cu].length;c<g;c++){Cy[a][Cu][c]=(r[Cu][c]-CA[a][Cu][c])/Cz}}break;case"transform":var U=CK._,V=AH(U[a],Cx[a]);if(V){CA[a]=V.from,Cx[a]=V.to,Cy[a]=[],Cy[a].real=!0;for(Cu=0,s=CA[a].length;Cu<s;Cu++){Cy[a][Cu]=[CA[a][Cu][0]];for(c=1,g=CA[a][Cu].length;c<g;c++){Cy[a][Cu][c]=(Cx[a][Cu][c]-CA[a][Cu][c])/Cz}}}else{var S=CK.matrix||new Ab,T={_:{transform:U.transform},getBBox:function(){return CK.getBBox(1)}};CA[a]=[S.a,S.b,S.c,S.d,S.e,S.f],Cf(T,Cx[a]),Cx[a]=T._.transform,Cy[a]=[(T.matrix.a-S.a)/Cz,(T.matrix.b-S.b)/Cz,(T.matrix.c-S.c)/Cz,(T.matrix.d-S.d)/Cz,(T.matrix.e-S.e)/Cz,(T.matrix.f-S.f)/Cz]}break;case"csv":var X=BJ(CB[a])[BK](B0),Y=BJ(CA[a])[BK](B0);if(a=="clip-rect"){CA[a]=Y,Cy[a]=[],Cu=Y.length;while(Cu--){Cy[a][Cu]=(X[Cu]-CA[a][Cu])/Cz}}Cx[a]=X;break;default:X=[][BV](CB[a]),Y=[][BV](CA[a]),Cy[a]=[],Cu=CK.paper.customAttributes[a].length;while(Cu--){Cy[a][Cu]=((X[Cu]||0)-(Y[Cu]||0))/Cz}}}}}var W=CB.easing,Q=B1.easing_formulas[W];if(!Q){Q=BJ(W).match(Br);if(Q&&Q.length==5){var N=Q;Q=function(A){return BH(A,+N[1],+N[2],+N[3],+N[4],Cz)}}else{Q=AB}}Cw=CB.start||CI.start||+(new Date),Ct={anim:CI,percent:C,timestamp:Cw,start:Cw+(CI.del||0),status:0,initstatus:CJ||0,stop:!1,ms:Cz,easing:Q,from:CA,diff:Cy,to:Cx,el:CK,callback:CB.callback,prev:Cv,next:CF,repeat:CE||CI.times,origin:CK.attr(),totalOrigin:CD},Aw.push(Ct);if(CJ&&!CC&&!CG){Ct.stop=!0,Ct.start=new Date-Cz*CJ;if(Aw.length==1){return As()}}CG&&(Ct.start=new Date-Ct.ms*CJ),Aw.length==1&&Az(As)}else{CC.initstatus=CJ,CC.start=new Date-CC.ms*CJ}eve("raphael.anim.start."+CK.id,CK,CI)}}function Al(E,C){var D=[],A={};this.ms=C,this.times=1;if(E){for(var B in E){E[B3](B)&&(A[Bi(B)]=E[B],D.push(Bi(B)))}D.sort(Cm)}this.anim=A,this.top=D[D.length-1],this.percents=D}function BH(L,J,K,O,A,M){function G(U,S){var T,W,P,V,Q,R;for(P=U,R=0;R<8;R++){V=I(P)-U;if(BD(V)<S){return P}Q=(3*E*P+2*D)*P+N;if(BD(Q)<1e-06){break}P=P-V/Q}T=0,W=1,P=U;if(P<T){return T}if(P>W){return W}while(T<W){V=I(P);if(BD(V-U)<S){return P}U>V?T=P:W=P,P=(W-T)/2+T}return P}function F(R,P){var Q=G(R,P);return((H*Q+C)*Q+B)*Q}function I(P){return((E*P+D)*P+N)*P}var N=3*J,D=3*(O-J)-N,E=1-N-D,B=3*K,C=3*(A-K)-B,H=1-B-C;return F(L,1/(200*M))}function Cp(){return this.x+BM+this.y+BM+this.width+" × "+this.height}function BE(){return this.x+BM+this.y}function Ab(E,C,D,A,B,F){E!=null?(this.a=+E,this.b=+C,this.c=+D,this.d=+A,this.e=+B,this.f=+F):(this.a=1,this.b=0,this.c=0,this.d=1,this.e=0,this.f=0)}function A6(Q,R,U){Q=B1._path2curve(Q),R=B1._path2curve(R);var V,S,T,K,L,I,J,O,P,M,N=U?0:[];for(var C=0,D=Q.length;C<D;C++){var A=Q[C];if(A[0]=="M"){V=L=A[1],S=I=A[2]}else{A[0]=="C"?(P=[V,S].concat(A.slice(1)),V=P[6],S=P[7]):(P=[V,S,V,S,L,I,L,I],V=L,S=I);for(var B=0,G=R.length;B<G;B++){var H=R[B];if(H[0]=="M"){T=J=H[1],K=O=H[2]}else{H[0]=="C"?(M=[T,K].concat(H.slice(1)),T=M[6],K=M[7]):(M=[T,K,T,K,J,O,J,O],T=J,K=O);var E=Cd(P,M,U);if(U){N+=E}else{for(var F=0,W=E.length;F<W;F++){E[F].segment1=C,E[F].segment2=B,E[F].bez1=P,E[F].bez2=M}N=N.concat(E)}}}}}return N}function Cd(R,S,V){var W=B1.bezierBBox(R),T=B1.bezierBBox(S);if(!B1.isBBoxIntersect(W,T)){return V?0:[]}var U=Av.apply(0,R),L=Av.apply(0,S),M=~~(U/5),J=~~(L/5),K=[],P=[],Q={},N=V?0:[];for(var O=0;O<M+1;O++){var D=B1.findDotsAtSegment.apply(B1,R.concat(O/M));K.push({x:D.x,y:D.y,t:O/M})}for(O=0;O<J+1;O++){D=B1.findDotsAtSegment.apply(B1,S.concat(O/J)),P.push({x:D.x,y:D.y,t:O/J})}for(O=0;O<M;O++){for(var E=0;E<J;E++){var B=K[O],C=K[O+1],H=P[E],I=P[E+1],F=BD(C.x-B.x)<0.001?"y":"x",G=BD(I.x-H.x)<0.001?"y":"x",Y=Ak(B.x,B.y,C.x,C.y,H.x,H.y,I.x,I.y);if(Y){if(Q[Y.x.toFixed(4)]==Y.y.toFixed(4)){continue}Q[Y.x.toFixed(4)]=Y.y.toFixed(4);var Z=B.t+BD((Y[F]-B[F])/(C[F]-B[F]))*(C.t-B.t),X=H.t+BD((Y[G]-H[G])/(I[G]-H[G]))*(I.t-H.t);Z>=0&&Z<=1&&X>=0&&X<=1&&(V?N++:N.push({x:Y.x,y:Y.y,t1:Z,t2:X}))}}}return N}function AU(B,A){return Cd(B,A,1)}function AN(B,A){return Cd(B,A)}function Ak(L,J,K,O,A,M,N,D){if(!(BF(L,K)<BG(A,N)||BG(L,K)>BF(A,N)||BF(J,O)<BG(M,D)||BG(J,O)>BF(M,D))){var E=(L*O-J*K)*(A-N)-(L-K)*(A*D-M*N),B=(L*O-J*K)*(M-D)-(J-O)*(A*D-M*N),C=(L-K)*(M-D)-(J-O)*(A-N);if(!C){return}var H=E/C,I=B/C,F=+H.toFixed(2),G=+I.toFixed(2);if(F<+BG(L,K).toFixed(2)||F>+BF(L,K).toFixed(2)||F<+BG(A,N).toFixed(2)||F>+BF(A,N).toFixed(2)||G<+BG(J,O).toFixed(2)||G>+BF(J,O).toFixed(2)||G<+BG(M,D).toFixed(2)||G>+BF(M,D).toFixed(2)){return}return{x:H,y:I}}}function B9(K,I,J,N,A,L,M,D,E){if(!(E<0||Av(K,I,J,N,A,L,M,D)<E)){var B=1,C=B/2,G=B-C,H,F=0.01;H=Av(K,I,J,N,A,L,M,D,G);while(BD(H-E)>F){C/=2,G+=(H<E?1:-1)*C,H=Av(K,I,J,N,A,L,M,D,G)}return G}}function Av(O,M,N,R,S,P,Q,G,H){H==null&&(H=1),H=H>1?1:H<0?0:H;var E=H/2,F=12,K=[-0.1252,0.1252,-0.3678,0.3678,-0.5873,0.5873,-0.7699,0.7699,-0.9041,0.9041,-0.9816,0.9816],L=[0.2491,0.2491,0.2335,0.2335,0.2032,0.2032,0.1601,0.1601,0.1069,0.1069,0.0472,0.0472],I=0;for(var J=0;J<F;J++){var C=E*K[J]+E,D=Ar(C,O,N,S,Q),A=Ar(C,M,R,P,G),B=D*D+A*A;I+=L[J]*BO.sqrt(B)}return E*I}function Ar(F,D,E,B,C){var G=-3*D+9*E-9*B+3*C,A=F*G+6*D-12*E+6*B;return F*A-3*D+3*E}function Bf(E,C){var D=[];for(var A=0,B=E.length;B-2*!C>A;A+=2){var F=[{x:+E[A-2],y:+E[A-1]},{x:+E[A],y:+E[A+1]},{x:+E[A+2],y:+E[A+3]},{x:+E[A+4],y:+E[A+5]}];C?A?B-4==A?F[3]={x:+E[0],y:+E[1]}:B-2==A&&(F[2]={x:+E[0],y:+E[1]},F[3]={x:+E[2],y:+E[3]}):F[0]={x:+E[B-2],y:+E[B-1]}:B-4==A?F[3]=F[2]:A||(F[0]={x:+E[A],y:+E[A+1]}),D.push(["C",(-F[0].x+6*F[1].x+F[2].x)/6,(-F[0].y+6*F[1].y+F[2].y)/6,(F[1].x+6*F[2].x-F[3].x)/6,(F[1].y+6*F[2].y-F[3].y)/6,F[2].x,F[2].y])}return D}function AJ(){return this.hex}function AY(D,B,C){function A(){var G=Array.prototype.slice.call(arguments,0),H=G.join("␀"),E=A.cache=A.cache||{},F=A.count=A.count||[];if(E[B3](H)){AS(F,H);return C?C(E[H]):E[H]}F.length>=1000&&delete E[F.shift()],F.push(H),E[H]=D[BY](B,G);return C?C(E[H]):E[H]}return A}function AS(D,B){for(var C=0,A=D.length;C<A;C++){if(D[C]===B){return D.push(D.splice(C,1)[0])}}}function B6(C){if(Object(C)!==C){return C}var A=new C.constructor;for(var B in C){C[B3](B)&&(A[B]=B6(C[B]))}return A}function B1(C){if(B1.is(C,"function")){return BZ?C():eve.on("raphael.DOMload",C)}if(B1.is(C,BC)){return B1._engine.create[BY](B1,C.splice(0,3+B1.is(C[0],Bw))).add(C)}var A=Array.prototype.slice.call(arguments,0);if(B1.is(A[A.length-1],"function")){var B=A.pop();return BZ?B.call(B1._engine.create[BY](B1,A)):eve.on("raphael.DOMload",function(){B.call(B1._engine.create[BY](B1,A))})}return B1._engine.create[BY](B1,arguments)}B1.version="2.1.0",B1.eve=eve;var BZ,B0=/[, ]+/,B4={circle:1,rect:1,path:1,ellipse:1,text:1,image:1},B5=/\{(\d+)\}/g,B2="prototype",B3="hasOwnProperty",BT={doc:document,win:window},BU={was:Object.prototype[B3].call(BT.win,"Raphael"),is:BT.win.Raphael},BR=function(){this.ca=this.customAttributes={}},BS,BX="appendChild",BY="apply",BV="concat",BW="createTouch" in BT.doc,BL="",BM=" ",BJ=String,BK="split",BP="click dblclick mousedown mousemove mouseout mouseover mouseup touchstart touchmove touchend touchcancel"[BK](BM),BQ={mousedown:"touchstart",mousemove:"touchmove",mouseup:"touchend"},BN=BJ.prototype.toLowerCase,BO=Math,BF=BO.max,BG=BO.min,BD=BO.abs,By=BO.pow,Bv=BO.PI,Bw="number",BB="string",BC="array",Bz="toString",BA="fill",Bp=Object.prototype.toString,Bq={},Bn="push",Bo=B1._ISURL=/^url\(['"]?([^\)]+?)['"]?\)$/i,Bt=/^\s*((#[a-f\d]{6})|(#[a-f\d]{3})|rgba?\(\s*([\d\.]+%?\s*,\s*[\d\.]+%?\s*,\s*[\d\.]+%?(?:\s*,\s*[\d\.]+%?)?)\s*\)|hsba?\(\s*([\d\.]+(?:deg|\xb0|%)?\s*,\s*[\d\.]+%?\s*,\s*[\d\.]+(?:%?\s*,\s*[\d\.]+)?)%?\s*\)|hsla?\(\s*([\d\.]+(?:deg|\xb0|%)?\s*,\s*[\d\.]+%?\s*,\s*[\d\.]+(?:%?\s*,\s*[\d\.]+)?)%?\s*\))\s*$/i,Bu={NaN:1,Infinity:1,"-Infinity":1},Br=/^(?:cubic-)?bezier\(([^,]+),([^,]+),([^,]+),([^\)]+)\)/,Bs=BO.round,Bh="setAttribute",Bi=parseFloat,Aq=parseInt,Bg=BJ.prototype.toUpperCase,Bl=B1._availableAttrs={"arrow-end":"none","arrow-start":"none",blur:0,"clip-rect":"0 0 1e9 1e9",cursor:"default",cx:0,cy:0,fill:"#fff","fill-opacity":1,font:'10px "Arial"',"font-family":'"Arial"',"font-size":"10","font-style":"normal","font-weight":400,gradient:0,height:0,href:"http://raphaeljs.com/","letter-spacing":0,opacity:1,path:"M0,0",r:0,rx:0,ry:0,src:"",stroke:"#000","stroke-dasharray":"","stroke-linecap":"butt","stroke-linejoin":"butt","stroke-miterlimit":0,"stroke-opacity":1,"stroke-width":1,target:"_blank","text-anchor":"middle",title:"Raphael",transform:"",width:0,x:0,y:0},Bm=B1._availableAnimAttrs={blur:Bw,"clip-rect":"csv",cx:Bw,cy:Bw,fill:"colour","fill-opacity":Bw,"font-size":Bw,height:Bw,opacity:Bw,path:"path",r:Bw,rx:Bw,ry:Bw,stroke:"colour","stroke-opacity":Bw,"stroke-width":Bw,transform:"transform",width:Bw,x:Bw,y:Bw},Bj=/[\x09\x0a\x0b\x0c\x0d\x20\xa0\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\u2028\u2029]/g,Bk=/[\x09\x0a\x0b\x0c\x0d\x20\xa0\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\u2028\u2029]*,[\x09\x0a\x0b\x0c\x0d\x20\xa0\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\u2028\u2029]*/,A9={hs:1,rg:1},Ba=/,?([achlmqrstvxz]),?/gi,A8=/([achlmrqstvz])[\x09\x0a\x0b\x0c\x0d\x20\xa0\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\u2028\u2029,]*((-?\d*\.?\d*(?:e[\-+]?\d+)?[\x09\x0a\x0b\x0c\x0d\x20\xa0\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\u2028\u2029]*,?[\x09\x0a\x0b\x0c\x0d\x20\xa0\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\u2028\u2029]*)+)/ig,Ad=/([rstm])[\x09\x0a\x0b\x0c\x0d\x20\xa0\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\u2028\u2029,]*((-?\d*\.?\d*(?:e[\-+]?\d+)?[\x09\x0a\x0b\x0c\x0d\x20\xa0\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\u2028\u2029]*,?[\x09\x0a\x0b\x0c\x0d\x20\xa0\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\u2028\u2029]*)+)/ig,Bc=/(-?\d*\.?\d*(?:e[\-+]?\d+)?)[\x09\x0a\x0b\x0c\x0d\x20\xa0\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\u2028\u2029]*,?[\x09\x0a\x0b\x0c\x0d\x20\xa0\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\u2028\u2029]*/ig,Cr=B1._radial_gradient=/^r(?:\(([^,]+?)[\x09\x0a\x0b\x0c\x0d\x20\xa0\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\u2028\u2029]*,[\x09\x0a\x0b\x0c\x0d\x20\xa0\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\u2028\u2029]*([^\)]+?)\))?/,Aa={},AD=function(B,A){return B.key-A.key},Cm=function(B,A){return Bi(B)-Bi(A)},Au=function(){},AB=function(A){return A},A3=B1._rectPath=function(E,C,D,A,B){if(B){return[["M",E+B,C],["l",D-B*2,0],["a",B,B,0,0,1,B,B],["l",0,A-B*2],["a",B,B,0,0,1,-B,B],["l",B*2-D,0],["a",B,B,0,0,1,-B,-B],["l",0,B*2-A],["a",B,B,0,0,1,B,-B],["z"]]}return[["M",E,C],["l",D,0],["l",0,A],["l",-D,0],["z"]]},Aj=function(D,B,C,A){A==null&&(A=C);return[["M",D,B],["m",0,-A],["a",C,A,0,1,1,0,2*A],["a",C,A,0,1,1,0,-2*A],["z"]]},Am=B1._getPath={path:function(A){return A.attr("path")},circle:function(B){var A=B.attrs;return Aj(A.cx,A.cy,A.r)},ellipse:function(B){var A=B.attrs;return Aj(A.cx,A.cy,A.rx,A.ry)},rect:function(B){var A=B.attrs;return A3(A.x,A.y,A.width,A.height,A.r)},image:function(B){var A=B.attrs;return A3(A.x,A.y,A.width,A.height)},text:function(B){var A=B._getBBox();return A3(A.x,A.y,A.width,A.height)}},Bx=B1.mapPath=function(F,D){if(!D){return F}var E,I,A,G,H,B,C;F=AT(F);for(A=0,H=F.length;A<H;A++){C=F[A];for(G=1,B=C.length;G<B;G+=2){E=D.x(C[G],C[G+1]),I=D.y(C[G],C[G+1]),C[G]=E,C[G+1]=I}}return F};B1._g=BT,B1.type=BT.win.SVGAngle||BT.doc.implementation.hasFeature("http://www.w3.org/TR/SVG11/feature#BasicStructure","1.1")?"SVG":"VML";if(B1.type=="VML"){var Ce=BT.doc.createElement("div"),AK;Ce.innerHTML='<v:shape adj="1"/>',AK=Ce.firstChild,AK.style.behavior="url(#default#VML)";if(!AK||typeof AK.adj!="object"){return B1.type=BL}Ce=null}B1.svg=!(B1.vml=B1.type=="VML"),B1._Paper=BR,B1.fn=BS=BR.prototype=B1.prototype,B1._id=0,B1._oid=0,B1.is=function(B,A){A=BN.call(A);if(A=="finite"){return !Bu[B3](+B)}if(A=="array"){return B instanceof Array}return A=="null"&&B===null||A==typeof B&&B!==null||A=="object"&&B===Object(B)||A=="array"&&Array.isArray&&Array.isArray(B)||Bp.call(B).slice(8,-1).toLowerCase()==A},B1.angle=function(D,E,H,A,F,G){if(F==null){var B=D-H,C=E-A;if(!B&&!C){return 0}return(180+BO.atan2(-C,-B)*180/Bv+360)%360}return B1.angle(D,E,F,G)-B1.angle(H,A,F,G)},B1.rad=function(A){return A%360*Bv/180},B1.deg=function(A){return A*180/Bv%360},B1.snapTo=function(C,D,A){A=B1.is(A,"finite")?A:10;if(B1.is(C,BC)){var B=C.length;while(B--){if(BD(C[B]-D)<=A){return C[B]}}}else{C=+C;var E=D%C;if(E<A){return D-E}if(E>C-A){return D-E+C}}return D};var Ca=B1.createUUID=function(B,A){return function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(B,A).toUpperCase()}}(/[xy]/g,function(C){var A=BO.random()*16|0,B=C=="x"?A:A&3|8;return B.toString(16)});B1.setWindow=function(A){eve("raphael.setWindow",B1,BT.win,A),BT.win=A,BT.doc=BT.win.document,B1._engine.initWin&&B1._engine.initWin(BT.win)};var Af=function(E){if(B1.vml){var F=/^\s+|\s+$/g,B;try{var C=new ActiveXObject("htmlfile");C.write("<body>"),C.close(),B=C.body}catch(G){B=createPopup().document.body}var A=B.createTextRange();Af=AY(function(J){try{B.style.color=BJ(J).replace(F,BL);var I=A.queryCommandValue("ForeColor");I=(I&255)<<16|I&65280|(I&16711680)>>>16;return"#"+("000000"+I.toString(16)).slice(-6)}catch(H){return"none"}})}else{var D=BT.doc.createElement("i");D.title="Raphaël Colour Picker",D.style.display="none",BT.doc.body.appendChild(D),Af=AY(function(H){D.style.color=H;return BT.doc.defaultView.getComputedStyle(D,BL).getPropertyValue("color")})}return Af(E)},Bb=function(){return"hsb("+[this.h,this.s,this.b]+")"},Co=function(){return"hsl("+[this.h,this.s,this.l]+")"},AA=function(){return this.hex},A2=function(C,D,A){D==null&&B1.is(C,"object")&&"r" in C&&"g" in C&&"b" in C&&(A=C.b,D=C.g,C=C.r);if(D==null&&B1.is(C,BB)){var B=B1.getRGB(C);C=B.r,D=B.g,A=B.b}if(C>1||D>1||A>1){C/=255,D/=255,A/=255}return[C,D,A]},Ck=function(C,D,A,B){C*=255,D*=255,A*=255;var E={r:C,g:D,b:A,hex:B1.rgb(C,D,A),toString:AA};B1.is(B,"finite")&&(E.opacity=B);return E};B1.color=function(A){var B;B1.is(A,"object")&&"h" in A&&"s" in A&&"b" in A?(B=B1.hsb2rgb(A),A.r=B.r,A.g=B.g,A.b=B.b,A.hex=B.hex):B1.is(A,"object")&&"h" in A&&"s" in A&&"l" in A?(B=B1.hsl2rgb(A),A.r=B.r,A.g=B.g,A.b=B.b,A.hex=B.hex):(B1.is(A,"string")&&(A=B1.getRGB(A)),B1.is(A,"object")&&"r" in A&&"g" in A&&"b" in A?(B=B1.rgb2hsl(A),A.h=B.h,A.s=B.s,A.l=B.l,B=B1.rgb2hsb(A),A.v=B.b):(A={hex:"none"},A.r=A.g=A.b=A.h=A.s=A.v=A.l=-1)),A.toString=AA;return A},B1.hsb2rgb=function(F,D,E,I){this.is(F,"object")&&"h" in F&&"s" in F&&"b" in F&&(E=F.b,D=F.s,F=F.h,I=F.o),F*=360;var A,G,H,B,C;F=F%360/60,C=E*D,B=C*(1-BD(F%2-1)),A=G=H=E-C,F=~~F,A+=[C,B,0,0,B,C][F],G+=[B,C,C,B,0,0][F],H+=[0,0,B,C,C,B][F];return Ck(A,G,H,I)},B1.hsl2rgb=function(F,D,E,I){this.is(F,"object")&&"h" in F&&"s" in F&&"l" in F&&(E=F.l,D=F.s,F=F.h);if(F>1||D>1||E>1){F/=360,D/=100,E/=100}F*=360;var A,G,H,B,C;F=F%360/60,C=2*D*(E<0.5?E:1-E),B=C*(1-BD(F%2-1)),A=G=H=E-C/2,F=~~F,A+=[C,B,0,0,B,C][F],G+=[B,C,C,B,0,0][F],H+=[0,0,B,C,C,B][F];return Ck(A,G,H,I)},B1.rgb2hsb=function(F,D,E){E=A2(F,D,E),F=E[0],D=E[1],E=E[2];var B,C,G,A;G=BF(F,D,E),A=G-BG(F,D,E),B=A==0?null:G==F?(D-E)/A:G==D?(E-F)/A+2:(F-D)/A+4,B=(B+360)%6*60/360,C=A==0?0:A/G;return{h:B,s:C,b:G,toString:Bb}},B1.rgb2hsl=function(F,D,E){E=A2(F,D,E),F=E[0],D=E[1],E=E[2];var I,A,G,H,B,C;H=BF(F,D,E),B=BG(F,D,E),C=H-B,I=C==0?null:H==F?(D-E)/C:H==D?(E-F)/C+2:(F-D)/C+4,I=(I+360)%6*60/360,G=(H+B)/2,A=C==0?0:G<0.5?C/(2*G):C/(2-2*G);return{h:I,s:A,l:G,toString:Co}},B1._path2string=function(){return this.join(",").replace(Ba,"$1")};var Ch=B1._preload=function(C,A){var B=BT.doc.createElement("img");B.style.cssText="position:absolute;left:-9999em;top:-9999em",B.onload=function(){A.call(this),this.onload=null,BT.doc.body.removeChild(this)},B.onerror=function(){BT.doc.body.removeChild(this)},BT.doc.body.appendChild(B),B.src=C};B1.getRGB=AY(function(F){if(!F||!!((F=BJ(F)).indexOf("-")+1)){return{r:-1,g:-1,b:-1,hex:"none",error:1,toString:AJ}}if(F=="none"){return{r:-1,g:-1,b:-1,hex:"none",toString:AJ}}!A9[B3](F.toLowerCase().substring(0,2))&&F.charAt()!="#"&&(F=Af(F));var G,I,A,H,D,E,B,C=F.match(Bt);if(C){C[2]&&(H=Aq(C[2].substring(5),16),A=Aq(C[2].substring(3,5),16),I=Aq(C[2].substring(1,3),16)),C[3]&&(H=Aq((E=C[3].charAt(3))+E,16),A=Aq((E=C[3].charAt(2))+E,16),I=Aq((E=C[3].charAt(1))+E,16)),C[4]&&(B=C[4][BK](Bk),I=Bi(B[0]),B[0].slice(-1)=="%"&&(I*=2.55),A=Bi(B[1]),B[1].slice(-1)=="%"&&(A*=2.55),H=Bi(B[2]),B[2].slice(-1)=="%"&&(H*=2.55),C[1].toLowerCase().slice(0,4)=="rgba"&&(D=Bi(B[3])),B[3]&&B[3].slice(-1)=="%"&&(D/=100));if(C[5]){B=C[5][BK](Bk),I=Bi(B[0]),B[0].slice(-1)=="%"&&(I*=2.55),A=Bi(B[1]),B[1].slice(-1)=="%"&&(A*=2.55),H=Bi(B[2]),B[2].slice(-1)=="%"&&(H*=2.55),(B[0].slice(-3)=="deg"||B[0].slice(-1)=="°")&&(I/=360),C[1].toLowerCase().slice(0,4)=="hsba"&&(D=Bi(B[3])),B[3]&&B[3].slice(-1)=="%"&&(D/=100);return B1.hsb2rgb(I,A,H,D)}if(C[6]){B=C[6][BK](Bk),I=Bi(B[0]),B[0].slice(-1)=="%"&&(I*=2.55),A=Bi(B[1]),B[1].slice(-1)=="%"&&(A*=2.55),H=Bi(B[2]),B[2].slice(-1)=="%"&&(H*=2.55),(B[0].slice(-3)=="deg"||B[0].slice(-1)=="°")&&(I/=360),C[1].toLowerCase().slice(0,4)=="hsla"&&(D=Bi(B[3])),B[3]&&B[3].slice(-1)=="%"&&(D/=100);return B1.hsl2rgb(I,A,H,D)}C={r:I,g:A,b:H,toString:AJ},C.hex="#"+(16777216|H|A<<8|I<<16).toString(16).slice(1),B1.is(D,"finite")&&(C.opacity=D);return C}return{r:-1,g:-1,b:-1,hex:"none",error:1,toString:AJ}},B1),B1.hsb=AY(function(B,C,A){return B1.hsb2rgb(B,C,A).hex}),B1.hsl=AY(function(B,C,A){return B1.hsl2rgb(B,C,A).hex}),B1.rgb=AY(function(C,A,B){return"#"+(16777216|B|A<<8|C<<16).toString(16).slice(1)}),B1.getColor=function(C){var A=this.getColor.start=this.getColor.start||{h:0,s:1,b:C||0.75},B=this.hsb2rgb(A.h,A.s,A.b);A.h+=0.075,A.h>1&&(A.h=0,A.s-=0.2,A.s<=0&&(this.getColor.start={h:0,s:1,b:A.b}));return B.hex},B1.getColor.reset=function(){delete this.start},B1.parsePathString=function(C){if(!C){return null}var D=Ay(C);if(D.arr){return B7(D.arr)}var A={a:7,c:6,h:1,l:2,m:2,r:4,q:4,s:4,t:2,v:1,z:0},B=[];B1.is(C,BC)&&B1.is(C[0],BC)&&(B=B7(C)),B.length||BJ(C).replace(A8,function(H,F,G){var I=[],E=F.toLowerCase();G.replace(Bc,function(K,J){J&&I.push(+J)}),E=="m"&&I.length>2&&(B.push([F][BV](I.splice(0,2))),E="l",F=F=="m"?"l":"L");if(E=="r"){B.push([F][BV](I))}else{while(I.length>=A[E]){B.push([F][BV](I.splice(0,A[E])));if(!A[E]){break}}}}),B.toString=B1._path2string,D.arr=B7(B);return B},B1.parseTransformString=AY(function(B){if(!B){return null}var C={r:3,s:4,t:2,m:6},A=[];B1.is(B,BC)&&B1.is(B[0],BC)&&(A=B7(B)),A.length||BJ(B).replace(Ad,function(G,E,F){var D=[],H=BN.call(E);F.replace(Bc,function(J,I){I&&D.push(+I)}),A.push([E][BV](D))}),A.toString=B1._path2string;return A});var Ay=function(B){var A=Ay.ps=Ay.ps||{};A[B]?A[B].sleep=100:A[B]={sleep:100},setTimeout(function(){for(var C in A){A[B3](C)&&C!=B&&(A[C].sleep--,!A[C].sleep&&delete A[C])}});return A[B]};B1.findDotsAtSegment=function(R,P,Q,U,V,S,T,J,K){var H=1-K,I=By(H,3),N=By(H,2),O=K*K,L=O*K,M=I*R+N*3*K*Q+H*3*K*K*V+L*T,C=I*P+N*3*K*U+H*3*K*K*S+L*J,D=R+2*K*(Q-R)+O*(V-2*Q+R),A=P+2*K*(U-P)+O*(S-2*U+P),B=Q+2*K*(V-Q)+O*(T-2*V+Q),F=U+2*K*(S-U)+O*(J-2*S+U),G=H*R+K*Q,E=H*P+K*U,X=H*V+K*T,Y=H*S+K*J,W=90-BO.atan2(D-B,A-F)*180/Bv;(D>B||A<F)&&(W+=180);return{x:M,y:C,m:{x:D,y:A},n:{x:B,y:F},start:{x:G,y:E},end:{x:X,y:Y},alpha:W}},B1.bezierBBox=function(E,F,I,A,G,H,C,D){B1.is(E,"array")||(E=[E,F,I,A,G,H,C,D]);var B=AM.apply(null,E);return{x:B.min.x,y:B.min.y,x2:B.max.x,y2:B.max.y,width:B.max.x-B.min.x,height:B.max.y-B.min.y}},B1.isPointInsideBBox=function(C,A,B){return A>=C.x&&A<=C.x2&&B>=C.y&&B<=C.y2},B1.isBBoxIntersect=function(B,C){var A=B1.isPointInsideBBox;return A(C,B.x,B.y)||A(C,B.x2,B.y)||A(C,B.x,B.y2)||A(C,B.x2,B.y2)||A(B,C.x,C.y)||A(B,C.x2,C.y)||A(B,C.x,C.y2)||A(B,C.x2,C.y2)||(B.x<C.x2&&B.x>C.x||C.x<B.x2&&C.x>B.x)&&(B.y<C.y2&&B.y>C.y||C.y<B.y2&&C.y>B.y)},B1.pathIntersection=function(B,A){return A6(B,A)},B1.pathIntersectionNumber=function(B,A){return A6(B,A,1)},B1.isPointInsidePath=function(C,D,A){var B=B1.pathBBox(C);return B1.isPointInsideBBox(B,D,A)&&A6(C,[["M",D,A],["H",B.x2+10]],1)%2==1},B1._removedFactory=function(A){return function(){eve("raphael.log",null,"Raphaël: you are calling to method “"+A+"” of removed object",A)}};var AE=B1.pathBBox=function(L){var J=Ay(L);if(J.bbox){return J.bbox}if(!L){return{x:0,y:0,width:0,height:0,x2:0,y2:0}}L=AT(L);var K=0,O=0,A=[],M=[],N;for(var F=0,G=L.length;F<G;F++){N=L[F];if(N[0]=="M"){K=N[1],O=N[2],A.push(K),M.push(O)}else{var D=AM(K,O,N[1],N[2],N[3],N[4],N[5],N[6]);A=A[BV](D.min.x,D.max.x),M=M[BV](D.min.y,D.max.y),K=N[5],O=N[6]}}var E=BG[BY](0,A),I=BG[BY](0,M),H=BF[BY](0,A),B=BF[BY](0,M),C={x:E,y:I,x2:H,y2:B,width:H-E,height:B-I};J.bbox=B6(C);return C},B7=function(A){var B=B6(A);B.toString=B1._path2string;return B},Ac=B1._pathToRelative=function(L){var M=Ay(L);if(M.rel){return B7(M.rel)}if(!B1.is(L,BC)||!B1.is(L&&L[0],BC)){L=B1.parsePathString(L)}var P=[],Q=0,N=0,O=0,F=0,G=0;L[0][0]=="M"&&(Q=L[0][1],N=L[0][2],O=Q,F=N,G++,P.push(["M",Q,N]));for(var D=G,E=L.length;D<E;D++){var J=P[D]=[],K=L[D];if(K[0]!=BN.call(K[0])){J[0]=BN.call(K[0]);switch(J[0]){case"a":J[1]=K[1],J[2]=K[2],J[3]=K[3],J[4]=K[4],J[5]=K[5],J[6]=+(K[6]-Q).toFixed(3),J[7]=+(K[7]-N).toFixed(3);break;case"v":J[1]=+(K[1]-N).toFixed(3);break;case"m":O=K[1],F=K[2];default:for(var H=1,I=K.length;H<I;H++){J[H]=+(K[H]-(H%2?Q:N)).toFixed(3)}}}else{J=P[D]=[],K[0]=="m"&&(O=K[1]+Q,F=K[2]+N);for(var B=0,C=K.length;B<C;B++){P[D][B]=K[B]}}var A=P[D].length;switch(P[D][0]){case"z":Q=O,N=F;break;case"h":Q+=+P[D][A-1];break;case"v":N+=+P[D][A-1];break;default:Q+=+P[D][A-2],N+=+P[D][A-1]}}P.toString=B1._path2string,M.rel=B7(P);return P},A7=B1._pathToAbsolute=function(M){var N=Ay(M);if(N.abs){return B7(N.abs)}if(!B1.is(M,BC)||!B1.is(M&&M[0],BC)){M=B1.parsePathString(M)}if(!M||!M.length){return[["M",0,0]]}var Q=[],R=0,O=0,P=0,H=0,I=0;M[0][0]=="M"&&(R=+M[0][1],O=+M[0][2],P=R,H=O,I++,Q[0]=["M",R,O]);var F=M.length==3&&M[0][0]=="M"&&M[1][0].toUpperCase()=="R"&&M[2][0].toUpperCase()=="Z";for(var G,K,L=I,J=M.length;L<J;L++){Q.push(G=[]),K=M[L];if(K[0]!=Bg.call(K[0])){G[0]=Bg.call(K[0]);switch(G[0]){case"A":G[1]=K[1],G[2]=K[2],G[3]=K[3],G[4]=K[4],G[5]=K[5],G[6]=+(K[6]+R),G[7]=+(K[7]+O);break;case"V":G[1]=+K[1]+O;break;case"H":G[1]=+K[1]+R;break;case"R":var C=[R,O][BV](K.slice(1));for(var D=2,A=C.length;D<A;D++){C[D]=+C[D]+R,C[++D]=+C[D]+O}Q.pop(),Q=Q[BV](Bf(C,F));break;case"M":P=+K[1]+R,H=+K[2]+O;default:for(D=1,A=K.length;D<A;D++){G[D]=+K[D]+(D%2?R:O)}}}else{if(K[0]=="R"){C=[R,O][BV](K.slice(1)),Q.pop(),Q=Q[BV](Bf(C,F)),G=["R"][BV](K.slice(-2))}else{for(var B=0,E=K.length;B<E;B++){G[B]=K[B]}}}switch(G[0]){case"Z":R=P,O=H;break;case"H":R=G[1];break;case"V":O=G[1];break;case"M":P=G[G.length-2],H=G[G.length-1];default:R=G[G.length-2],O=G[G.length-1]}}Q.toString=B1._path2string,N.abs=B7(Q);return Q},Cn=function(D,B,C,A){return[D,B,C,A,C,A]},Cs=function(E,C,D,H,A,F){var G=1/3,B=2/3;return[G*E+B*D,G*C+B*H,G*A+B*D,G*F+B*H,A,F]},A1=function(C1,CZ,C0,C4,B,C2,C3,CU,CV,CS){var CT=Bv*120/180,CX=Bv/180*(+B||0),CY=[],CW,CN=AY(function(F,D,E){var A=F*BO.cos(E)-D*BO.sin(E),C=F*BO.sin(E)+D*BO.cos(E);return{x:A,y:C}});if(!CS){CW=CN(C1,CZ,-CX),C1=CW.x,CZ=CW.y,CW=CN(CU,CV,-CX),CU=CW.x,CV=CW.y;var CO=BO.cos(Bv/180*B),CM=BO.sin(Bv/180*B),CQ=(C1-CU)/2,CR=(CZ-CV)/2,CP=CQ*CQ/(C0*C0)+CR*CR/(C4*C4);CP>1&&(CP=BO.sqrt(CP),C0=CP*C0,C4=CP*C4);var CK=C0*C0,CL=C4*C4,CF=(C2==C3?-1:1)*BO.sqrt(BD((CK*CL-CK*CR*CR-CL*CQ*CQ)/(CK*CR*CR+CL*CQ*CQ))),CE=CF*C0*CR/C4+(C1+CU)/2,CI=CF*-C4*CQ/C0+(CZ+CV)/2,CJ=BO.asin(((CZ-CI)/C4).toFixed(9)),CG=BO.asin(((CV-CI)/C4).toFixed(9));CJ=C1<CE?Bv-CJ:CJ,CG=CU<CE?Bv-CG:CG,CJ<0&&(CJ=Bv*2+CJ),CG<0&&(CG=Bv*2+CG),C3&&CJ>CG&&(CJ=CJ-Bv*2),!C3&&CG>CJ&&(CG=CG-Bv*2)}else{CJ=CS[0],CG=CS[1],CE=CS[2],CI=CS[3]}var CH=CG-CJ;if(BD(CH)>CT){var Cy=CG,Cz=CU,Cw=CV;CG=CJ+CT*(C3&&CG>CJ?1:-1),CU=CE+C0*BO.cos(CG),CV=CI+C4*BO.sin(CG),CY=A1(CU,CV,C0,C4,B,0,C3,Cz,Cw,[CG,Cy,CE,CI])}CH=CG-CJ;var Cx=BO.cos(CJ),CC=BO.sin(CJ),CD=BO.cos(CG),CA=BO.sin(CG),CB=BO.tan(CH/4),s=4/3*C0*CB,w=4/3*C4*CB,Z=[C1,CZ],n=[C1+s*CC,CZ-w*Cx],Cu=[CU+s*CA,CV-w*CD],Cv=[CU,CV];n[0]=2*Z[0]-n[0],n[1]=2*Z[1]-n[1];if(CS){return[n,Cu,Cv][BV](CY)}CY=[n,Cu,Cv][BV](CY).join()[BK](",");var z=[];for(var Ct=0,Y=CY.length;Ct<Y;Ct++){z[Ct]=Ct%2?CN(CY[Ct-1],CY[Ct],CX).y:CN(CY[Ct],CY[Ct+1],CX).x}return z},Bd=function(G,E,F,J,A,H,I,C,D){var B=1-D;return{x:By(B,3)*G+By(B,2)*3*D*F+B*3*D*D*A+By(D,3)*I,y:By(B,3)*E+By(B,2)*3*D*J+B*3*D*D*H+By(D,3)*C}},AM=AY(function(M,K,L,P,A,N,O,F){var G=A-2*L+M-(O-2*A+L),D=2*(L-M)-2*(A-L),E=M-L,J=(-D+BO.sqrt(D*D-4*G*E))/2/G,H=(-D-BO.sqrt(D*D-4*G*E))/2/G,I=[K,F],B=[M,O],C;BD(J)>"1e12"&&(J=0.5),BD(H)>"1e12"&&(H=0.5),J>0&&J<1&&(C=Bd(M,K,L,P,A,N,O,F,J),B.push(C.x),I.push(C.y)),H>0&&H<1&&(C=Bd(M,K,L,P,A,N,O,F,H),B.push(C.x),I.push(C.y)),G=N-2*P+K-(F-2*N+P),D=2*(P-K)-2*(N-P),E=K-P,J=(-D+BO.sqrt(D*D-4*G*E))/2/G,H=(-D-BO.sqrt(D*D-4*G*E))/2/G,BD(J)>"1e12"&&(J=0.5),BD(H)>"1e12"&&(H=0.5),J>0&&J<1&&(C=Bd(M,K,L,P,A,N,O,F,J),B.push(C.x),I.push(C.y)),H>0&&H<1&&(C=Bd(M,K,L,P,A,N,O,F,H),B.push(C.x),I.push(C.y));return{min:{x:BG[BY](0,B),y:BG[BY](0,I)},max:{x:BF[BY](0,B),y:BF[BY](0,I)}}}),AT=B1._path2curve=AY(function(M,K){var L=!K&&Ay(M);if(!K&&L.curve){return B7(L.curve)}var P=A7(M),A=K&&A7(K),N={x:0,y:0,bx:0,by:0,X:0,Y:0,qx:null,qy:null},O={x:0,y:0,bx:0,by:0,X:0,Y:0,qx:null,qy:null},G=function(T,R){var S,Q;if(!T){return["C",R.x,R.y,R.x,R.y,R.x,R.y]}!(T[0] in {T:1,Q:1})&&(R.qx=R.qy=null);switch(T[0]){case"M":R.X=T[1],R.Y=T[2];break;case"A":T=["C"][BV](A1[BY](0,[R.x,R.y][BV](T.slice(1))));break;case"S":S=R.x+(R.x-(R.bx||R.x)),Q=R.y+(R.y-(R.by||R.y)),T=["C",S,Q][BV](T.slice(1));break;case"T":R.qx=R.x+(R.x-(R.qx||R.x)),R.qy=R.y+(R.y-(R.qy||R.y)),T=["C"][BV](Cs(R.x,R.y,R.qx,R.qy,T[1],T[2]));break;case"Q":R.qx=T[1],R.qy=T[2],T=["C"][BV](Cs(R.x,R.y,T[1],T[2],T[3],T[4]));break;case"L":T=["C"][BV](Cn(R.x,R.y,T[1],T[2]));break;case"H":T=["C"][BV](Cn(R.x,R.y,T[1],R.y));break;case"V":T=["C"][BV](Cn(R.x,R.y,R.x,T[1]));break;case"Z":T=["C"][BV](Cn(R.x,R.y,R.X,R.Y))}return T},H=function(S,Q){if(S[Q].length>7){S[Q].shift();var R=S[Q];while(R.length){S.splice(Q++,0,["C"][BV](R.splice(0,6)))}S.splice(Q,1),J=BF(P.length,A&&A.length||0)}},E=function(T,R,S,U,Q){T&&R&&T[Q][0]=="M"&&R[Q][0]!="M"&&(R.splice(Q,0,["M",U.x,U.y]),S.bx=0,S.by=0,S.x=T[Q][1],S.y=T[Q][2],J=BF(P.length,A&&A.length||0))};for(var F=0,J=BF(P.length,A&&A.length||0);F<J;F++){P[F]=G(P[F],N),H(P,F),A&&(A[F]=G(A[F],O)),A&&H(A,F),E(P,A,N,O,F),E(A,P,O,N,F);var I=P[F],B=A&&A[F],C=I.length,D=A&&B.length;N.x=I[C-2],N.y=I[C-1],N.bx=Bi(I[C-4])||N.x,N.by=Bi(I[C-3])||N.y,O.bx=A&&(Bi(B[D-4])||O.x),O.by=A&&(Bi(B[D-3])||O.y),O.x=A&&B[D-2],O.y=A&&B[D-1]}A||(L.curve=B7(P));return A?[P,A]:P},null,B7),A5=B1._parseDots=AY(function(F){var G=[];for(var J=0,A=F.length;J<A;J++){var H={},I=F[J].match(/^([^:]*):?([\d\.]*)/);H.color=B1.getRGB(I[1]);if(H.color.error){return null}H.color=H.color.hex,I[2]&&(H.offset=I[2]+"%"),G.push(H)}for(J=1,A=G.length-1;J<A;J++){if(!G[J].offset){var D=Bi(G[J-1].offset||0),E=0;for(var B=J+1;B<A;B++){if(G[B].offset){E=G[B].offset;break}}E||(E=100,B=A),E=Bi(E);var C=(E-D)/(B-J+1);for(;J<B;J++){D+=C,G[J].offset=D+"%"}}}return G}),AI=B1._tear=function(B,A){B==A.top&&(A.top=B.prev),B==A.bottom&&(A.bottom=B.next),B.next&&(B.next.prev=B.prev),B.prev&&(B.prev.next=B.next)},Be=B1._tofront=function(B,A){A.top!==B&&(AI(B,A),B.next=null,B.prev=A.top,A.top.next=B,A.top=B)},B8=B1._toback=function(B,A){A.bottom!==B&&(AI(B,A),B.next=A.bottom,B.prev=null,A.bottom.prev=B,A.bottom=B)},Cb=B1._insertafter=function(C,A,B){AI(C,B),A==B.top&&(B.top=C),A.next&&(A.next.prev=C),C.next=A.next,C.prev=A,A.next=C},An=B1._insertbefore=function(C,A,B){AI(C,B),A==B.bottom&&(B.bottom=C),A.prev&&(A.prev.next=C),C.prev=A.prev,A.prev=C,C.next=A},AP=B1.toMatrix=function(D,B){var C=AE(D),A={_:{transform:BL},getBBox:function(){return C}};Cf(A,B);return A.matrix},Ap=B1.transformPath=function(B,A){return Bx(B,AP(B,A))},Cf=B1._extractTransform=function(O,P){if(P==null){return O._.transform}P=BJ(P).replace(/\.{3}|\u2026/g,O._.transform||BL);var S=B1.parseTransformString(P),T=0,Q=0,R=0,I=1,J=1,G=O._,H=new Ab;G.transform=S||[];if(S){for(var M=0,N=S.length;M<N;M++){var K=S[M],L=K.length,B=BJ(K[0]).toLowerCase(),A=K[0]!=B,E=A?H.invert():0,F,C,D,U,V;B=="t"&&L==3?A?(F=E.x(0,0),C=E.y(0,0),D=E.x(K[1],K[2]),U=E.y(K[1],K[2]),H.translate(D-F,U-C)):H.translate(K[1],K[2]):B=="r"?L==2?(V=V||O.getBBox(1),H.rotate(K[1],V.x+V.width/2,V.y+V.height/2),T+=K[1]):L==4&&(A?(D=E.x(K[2],K[3]),U=E.y(K[2],K[3]),H.rotate(K[1],D,U)):H.rotate(K[1],K[2],K[3]),T+=K[1]):B=="s"?L==2||L==3?(V=V||O.getBBox(1),H.scale(K[1],K[L-1],V.x+V.width/2,V.y+V.height/2),I*=K[1],J*=K[L-1]):L==5&&(A?(D=E.x(K[3],K[4]),U=E.y(K[3],K[4]),H.scale(K[1],K[2],D,U)):H.scale(K[1],K[2],K[3],K[4]),I*=K[1],J*=K[2]):B=="m"&&L==7&&H.add(K[1],K[2],K[3],K[4],K[5],K[6]),G.dirtyT=1,O.matrix=H}}O.matrix=H,G.sx=I,G.sy=J,G.deg=T,G.dx=Q=H.e,G.dy=R=H.f,I==1&&J==1&&!T&&G.bbox?(G.bbox.x+=+Q,G.bbox.y+=+R):G.dirtyT=1},AZ=function(B){var A=B[0];switch(A.toLowerCase()){case"t":return[A,0,0];case"m":return[A,1,0,0,1,0,0];case"r":return B.length==4?[A,0,B[2],B[3]]:[A,0];case"s":return B.length==5?[A,1,1,B[3],B[4]]:B.length==3?[A,1,1]:[A,1]}},AH=B1._equaliseTransform=function(F,G){G=BJ(G).replace(/\.{3}|\u2026/g,F),F=B1.parseTransformString(F)||[],G=B1.parseTransformString(G)||[];var J=BF(F.length,G.length),A=[],H=[],I=0,D,E,B,C;for(;I<J;I++){B=F[I]||AZ(G[I]),C=G[I]||AZ(B);if(B[0]!=C[0]||B[0].toLowerCase()=="r"&&(B[2]!=C[2]||B[3]!=C[3])||B[0].toLowerCase()=="s"&&(B[3]!=C[3]||B[4]!=C[4])){return}A[I]=[],H[I]=[];for(D=0,E=BF(B.length,C.length);D<E;D++){D in B&&(A[I][D]=B[D]),D in C&&(H[I][D]=C[D])}}return{from:A,to:H}};B1._getContainer=function(C,D,A,B){var E;E=B==null&&!B1.is(C,"object")?BT.doc.getElementById(C):C;if(E!=null){if(E.tagName){return D==null?{container:E,width:E.style.pixelWidth||E.offsetWidth,height:E.style.pixelHeight||E.offsetHeight}:{container:E,width:D,height:A}}return{container:1,x:C,y:D,width:A,height:B}}},B1.pathToRelative=Ac,B1._engine={},B1.path2curve=AT,B1.matrix=function(E,C,D,A,B,F){return new Ab(E,C,D,A,B,F)},function(B){function A(E){var D=BO.sqrt(C(E));E[0]&&(E[0]/=D),E[1]&&(E[1]/=D)}function C(D){return D[0]*D[0]+D[1]*D[1]}B.add=function(M,K,L,P,D,N){var O=[[],[],[]],G=[[this.a,this.c,this.e],[this.b,this.d,this.f],[0,0,1]],H=[[M,L,D],[K,P,N],[0,0,1]],E,F,I,J;M&&M instanceof Ab&&(H=[[M.a,M.c,M.e],[M.b,M.d,M.f],[0,0,1]]);for(E=0;E<3;E++){for(F=0;F<3;F++){J=0;for(I=0;I<3;I++){J+=G[E][I]*H[I][F]}O[E][F]=J}}this.a=O[0][0],this.b=O[1][0],this.c=O[0][1],this.d=O[1][1],this.e=O[0][2],this.f=O[1][2]},B.invert=function(){var E=this,D=E.a*E.d-E.b*E.c;return new Ab(E.d/D,-E.b/D,-E.c/D,E.a/D,(E.c*E.f-E.d*E.e)/D,(E.b*E.e-E.a*E.f)/D)},B.clone=function(){return new Ab(this.a,this.b,this.c,this.d,this.e,this.f)},B.translate=function(E,D){this.add(1,0,0,1,E,D)},B.scale=function(G,E,F,D){E==null&&(E=G),(F||D)&&this.add(1,0,0,1,F,D),this.add(G,0,0,E,0,0),(F||D)&&this.add(1,0,0,1,-F,-D)},B.rotate=function(F,G,D){F=B1.rad(F),G=G||0,D=D||0;var E=+BO.cos(F).toFixed(9),H=+BO.sin(F).toFixed(9);this.add(E,H,-H,E,G,D),this.add(1,0,0,1,-G,-D)},B.x=function(E,D){return E*this.a+D*this.c+this.e},B.y=function(E,D){return E*this.b+D*this.d+this.f},B.get=function(D){return +this[BJ.fromCharCode(97+D)].toFixed(4)},B.toString=function(){return B1.svg?"matrix("+[this.get(0),this.get(1),this.get(2),this.get(3),this.get(4),this.get(5)].join()+")":[this.get(0),this.get(2),this.get(1),this.get(3),0,0].join()},B.toFilter=function(){return"progid:DXImageTransform.Microsoft.Matrix(M11="+this.get(0)+", M12="+this.get(2)+", M21="+this.get(1)+", M22="+this.get(3)+", Dx="+this.get(4)+", Dy="+this.get(5)+", sizingmethod='auto expand')"},B.offset=function(){return[this.e.toFixed(4),this.f.toFixed(4)]},B.split=function(){var F={};F.dx=this.e,F.dy=this.f;var E=[[this.a,this.c],[this.b,this.d]];F.scalex=BO.sqrt(C(E[0])),A(E[0]),F.shear=E[0][0]*E[1][0]+E[0][1]*E[1][1],E[1]=[E[1][0]-E[0][0]*F.shear,E[1][1]-E[0][1]*F.shear],F.scaley=BO.sqrt(C(E[1])),A(E[1]),F.shear/=F.scaley;var G=-E[0][1],D=E[1][1];D<0?(F.rotate=B1.deg(BO.acos(D)),G<0&&(F.rotate=360-F.rotate)):F.rotate=B1.deg(BO.asin(G)),F.isSimple=!+F.shear.toFixed(9)&&(F.scalex.toFixed(9)==F.scaley.toFixed(9)||!F.rotate),F.isSuperSimple=!+F.shear.toFixed(9)&&F.scalex.toFixed(9)==F.scaley.toFixed(9)&&!F.rotate,F.noRotation=!+F.shear.toFixed(9)&&!F.rotate;return F},B.toTransformString=function(E){var D=E||this[BK]();if(D.isSimple){D.scalex=+D.scalex.toFixed(4),D.scaley=+D.scaley.toFixed(4),D.rotate=+D.rotate.toFixed(4);return(D.dx||D.dy?"t"+[D.dx,D.dy]:BL)+(D.scalex!=1||D.scaley!=1?"s"+[D.scalex,D.scaley,0,0]:BL)+(D.rotate?"r"+[D.rotate,0,0]:BL)}return"m"+[this.get(0),this.get(1),this.get(2),this.get(3),this.get(4),this.get(5)]}}(Ab.prototype);var Cg=navigator.userAgent.match(/Version\/(.*?)\s/)||navigator.userAgent.match(/Chrome\/(\d+)/);navigator.vendor=="Apple Computer, Inc."&&(Cg&&Cg[1]<4||navigator.platform.slice(0,2)=="iP")||navigator.vendor=="Google Inc."&&Cg&&Cg[1]<8?BS.safari=function(){var A=this.rect(-99,-99,this.width+99,this.height+99).attr({stroke:"none"});setTimeout(function(){A.remove()})}:BS.safari=Au;var Ae=function(){this.returnValue=!1},Ci=function(){return this.originalEvent.preventDefault()},AC=function(){this.cancelBubble=!0},A4=function(){return this.originalEvent.stopPropagation()},Ai=function(){if(BT.doc.addEventListener){return function(E,C,D,A){var B=BW&&BQ[C]?BQ[C]:C,F=function(G){var N=BT.doc.documentElement.scrollTop||BT.doc.body.scrollTop,J=BT.doc.documentElement.scrollLeft||BT.doc.body.scrollLeft,H=G.clientX+J,I=G.clientY+N;if(BW&&BQ[B3](C)){for(var L=0,M=G.targetTouches&&G.targetTouches.length;L<M;L++){if(G.targetTouches[L].target==E){var K=G;G=G.targetTouches[L],G.originalEvent=K,G.preventDefault=Ci,G.stopPropagation=A4;break}}}return D.call(A,G,H,I)};E.addEventListener(B,F,!1);return function(){E.removeEventListener(B,F,!1);return !0}}}if(BT.doc.attachEvent){return function(E,C,D,A){var B=function(J){J=J||BT.win.event;var I=BT.doc.documentElement.scrollTop||BT.doc.body.scrollTop,H=BT.doc.documentElement.scrollLeft||BT.doc.body.scrollLeft,K=J.clientX+H,G=J.clientY+I;J.preventDefault=J.preventDefault||Ae,J.stopPropagation=J.stopPropagation||AC;return D.call(A,J,K,G)};E.attachEvent("on"+C,B);var F=function(){E.detachEvent("on"+C,B);return !0};return F}}}(),AX=[],Ao=function(K){var I=K.clientX,J=K.clientY,N=BT.doc.documentElement.scrollTop||BT.doc.body.scrollTop,A=BT.doc.documentElement.scrollLeft||BT.doc.body.scrollLeft,L,M=AX.length;while(M--){L=AX[M];if(BW){var E=K.touches.length,C;while(E--){C=K.touches[E];if(C.identifier==L.el._drag.id){I=C.clientX,J=C.clientY,(K.originalEvent?K.originalEvent:K).preventDefault();break}}}else{K.preventDefault()}var D=L.el.node,G,H=D.nextSibling,F=D.parentNode,B=D.style.display;BT.win.opera&&F.removeChild(D),D.style.display="none",G=L.el.paper.getElementByPoint(I,J),D.style.display=B,BT.win.opera&&(H?F.insertBefore(D,H):F.appendChild(D)),G&&eve("raphael.drag.over."+L.el.id,L.el,G),I+=A,J+=N,eve("raphael.drag.move."+L.el.id,L.move_scope||L.el,I-L.el._drag.x,J-L.el._drag.y,I,J,K)}},AQ=function(B){B1.unmousemove(Ao).unmouseup(AQ);var C=AX.length,A;while(C--){A=AX[C],A.el._drag={},eve("raphael.drag.end."+A.el.id,A.end_scope||A.start_scope||A.move_scope||A.el,B)}AX=[]},AL=B1.el={};for(var AR=BP.length;AR--;){(function(A){B1[A]=AL[A]=function(C,B){B1.is(C,"function")&&(this.events=this.events||[],this.events.push({name:A,f:C,unbind:Ai(this.shape||this.node||BT.doc,A,C,B||this)}));return this},B1["un"+A]=AL["un"+A]=function(D){var C=this.events||[],B=C.length;while(B--){if(C[B].name==A&&C[B].f==D){C[B].unbind(),C.splice(B,1),!C.length&&delete this.events;return this}}return this}})(BP[AR])}AL.data=function(C,D){var A=Aa[this.id]=Aa[this.id]||{};if(arguments.length==1){if(B1.is(C,"object")){for(var B in C){C[B3](B)&&this.data(B,C[B])}return this}eve("raphael.data.get."+this.id,this,A[C],C);return A[C]}A[C]=D,eve("raphael.data.set."+this.id,this,D,C);return this},AL.removeData=function(A){A==null?Aa[this.id]={}:Aa[this.id]&&delete Aa[this.id][A];return this},AL.hover=function(D,B,C,A){return this.mouseover(D,C).mouseout(B,A||C)},AL.unhover=function(B,A){return this.unmouseover(B).unmouseout(A)};var AW=[];AL.drag=function(E,F,B,C,G,A){function D(H){(H.originalEvent||H).preventDefault();var I=BT.doc.documentElement.scrollTop||BT.doc.body.scrollTop,J=BT.doc.documentElement.scrollLeft||BT.doc.body.scrollLeft;this._drag.x=H.clientX+J,this._drag.y=H.clientY+I,this._drag.id=H.identifier,!AX.length&&B1.mousemove(Ao).mouseup(AQ),AX.push({el:this,move_scope:C,start_scope:G,end_scope:A}),F&&eve.on("raphael.drag.start."+this.id,F),E&&eve.on("raphael.drag.move."+this.id,E),B&&eve.on("raphael.drag.end."+this.id,B),eve("raphael.drag.start."+this.id,G||C||this,H.clientX+J,H.clientY+I,H)}this._drag={},AW.push({el:this,start:D}),this.mousedown(D);return this},AL.onDragOver=function(A){A?eve.on("raphael.drag.over."+this.id,A):eve.unbind("raphael.drag.over."+this.id)},AL.undrag=function(){var A=AW.length;while(A--){AW[A].el==this&&(this.unmousedown(AW[A].start),AW.splice(A,1),eve.unbind("raphael.drag.*."+this.id))}!AW.length&&B1.unmousemove(Ao).unmouseup(AQ)},BS.circle=function(C,D,A){var B=B1._engine.circle(this,C||0,D||0,A||0);this.__set__&&this.__set__.push(B);return B},BS.rect=function(D,E,B,C,F){var A=B1._engine.rect(this,D||0,E||0,B||0,C||0,F||0);this.__set__&&this.__set__.push(A);return A},BS.ellipse=function(C,D,A,B){var E=B1._engine.ellipse(this,C||0,D||0,A||0,B||0);this.__set__&&this.__set__.push(E);return E},BS.path=function(A){A&&!B1.is(A,BB)&&!B1.is(A[0],BC)&&(A+=BL);var B=B1._engine.path(B1.format[BY](B1,arguments),this);this.__set__&&this.__set__.push(B);return B},BS.image=function(D,E,B,C,F){var A=B1._engine.image(this,D||"about:blank",E||0,B||0,C||0,F||0);this.__set__&&this.__set__.push(A);return A},BS.text=function(C,D,A){var B=B1._engine.text(this,C||0,D||0,BJ(A));this.__set__&&this.__set__.push(B);return B},BS.set=function(A){!B1.is(A,"array")&&(A=Array.prototype.splice.call(arguments,0,arguments.length));var B=new Cq(A);this.__set__&&this.__set__.push(B);return B},BS.setStart=function(A){this.__set__=A||this.set()},BS.setFinish=function(B){var A=this.__set__;delete this.__set__;return A},BS.setSize=function(A,B){return B1._engine.setSize.call(this,A,B)},BS.setViewBox=function(C,D,A,B,E){return B1._engine.setViewBox.call(this,C,D,A,B,E)},BS.top=BS.bottom=null,BS.raphael=B1;var Ag=function(F){var D=F.getBoundingClientRect(),E=F.ownerDocument,I=E.body,A=E.documentElement,G=A.clientTop||I.clientTop||0,H=A.clientLeft||I.clientLeft||0,C=D.top+(BT.win.pageYOffset||A.scrollTop||I.scrollTop)-G,B=D.left+(BT.win.pageXOffset||A.scrollLeft||I.scrollLeft)-H;return{y:C,x:B}};BS.getElementByPoint=function(E,C){var D=this,H=D.canvas,A=BT.doc.elementFromPoint(E,C);if(BT.win.opera&&A.tagName=="svg"){var F=Ag(H),G=H.createSVGRect();G.x=E-F.x,G.y=C-F.y,G.width=G.height=1;var B=H.getIntersectionList(G,null);B.length&&(A=B[B.length-1])}if(!A){return null}while(A.parentNode&&A!=H.parentNode&&!A.raphael){A=A.parentNode}A==D.canvas.parentNode&&(A=H),A=A&&A.raphael?D.getById(A.raphaelid):null;return A},BS.getById=function(B){var A=this.bottom;while(A){if(A.id==B){return A}A=A.next}return null},BS.forEach=function(C,A){var B=this.bottom;while(B){if(C.call(A,B)===!1){return this}B=B.next}return this},BS.getElementsByPoint=function(C,A){var B=this.set();this.forEach(function(D){D.isPointInside(C,A)&&B.push(D)});return B},AL.isPointInside=function(B,C){var A=this.realPath=this.realPath||Am[this.type](this);return B1.isPointInsidePath(A,B,C)},AL.getBBox=function(B){if(this.removed){return{}}var A=this._;if(B){if(A.dirty||!A.bboxwt){this.realPath=Am[this.type](this),A.bboxwt=AE(this.realPath),A.bboxwt.toString=Cp,A.dirty=0}return A.bboxwt}if(A.dirty||A.dirtyT||!A.bbox){if(A.dirty||!this.realPath){A.bboxwt=0,this.realPath=Am[this.type](this)}A.bbox=AE(Bx(this.realPath,this.matrix)),A.bbox.toString=Cp,A.dirty=A.dirtyT=0}return A.bbox},AL.clone=function(){if(this.removed){return null}var A=this.paper[this.type]().attr(this.attr());this.__set__&&this.__set__.push(A);return A},AL.glow=function(F){if(this.type=="text"){return null}F=F||{};var D={width:(F.width||10)+(+this.attr("stroke-width")||1),fill:F.fill||!1,opacity:F.opacity||0.5,offsetx:F.offsetx||0,offsety:F.offsety||0,color:F.color||"#000"},E=D.width/2,B=this.paper,C=B.set(),G=this.realPath||Am[this.type](this);G=this.matrix?Bx(G,this.matrix):G;for(var A=1;A<E+1;A++){C.push(B.path(G).attr({stroke:D.color,fill:D.fill?D.color:"none","stroke-linejoin":"round","stroke-linecap":"round","stroke-width":+(D.width/E*A).toFixed(3),opacity:+(D.opacity/E).toFixed(3)}))}return C.insertBefore(this).translate(D.offsetx,D.offsety)};var At={},Cc=function(E,F,I,A,G,H,C,D,B){return B==null?Av(E,F,I,A,G,H,C,D):B1.findDotsAtSegment(E,F,I,A,G,H,C,D,B9(E,F,I,A,G,H,C,D,B))},Cl=function(A,B){return function(O,C,M){O=AT(O);var N,G,H,E,F="",K={},L,I=0;for(var J=0,D=O.length;J<D;J++){H=O[J];if(H[0]=="M"){N=+H[1],G=+H[2]}else{E=Cc(N,G,H[1],H[2],H[3],H[4],H[5],H[6]);if(I+E>C){if(B&&!K.start){L=Cc(N,G,H[1],H[2],H[3],H[4],H[5],H[6],C-I),F+=["C"+L.start.x,L.start.y,L.m.x,L.m.y,L.x,L.y];if(M){return F}K.start=F,F=["M"+L.x,L.y+"C"+L.n.x,L.n.y,L.end.x,L.end.y,H[5],H[6]].join(),I+=E,N=+H[5],G=+H[6];continue}if(!A&&!B){L=Cc(N,G,H[1],H[2],H[3],H[4],H[5],H[6],C-I);return{x:L.x,y:L.y,alpha:L.alpha}}}I+=E,N=+H[5],G=+H[6]}F+=H.shift()+H}K.end=F,L=A?I:B?K:B1.findDotsAtSegment(N,G,H[0],H[1],H[2],H[3],H[4],H[5],1),L.alpha&&(L={x:L.x,y:L.y,alpha:L.alpha});return L}},Ah=Cl(1),Cj=Cl(),BI=Cl(0,1);B1.getTotalLength=Ah,B1.getPointAtLength=Cj,B1.getSubpath=function(D,B,C){if(this.getTotalLength(D)-C<1e-06){return BI(D,B).end}var A=BI(D,C,1);return B?BI(A,B).end:A},AL.getTotalLength=function(){if(this.type=="path"){if(this.node.getTotalLength){return this.node.getTotalLength()}return Ah(this.attrs.path)}},AL.getPointAtLength=function(A){if(this.type=="path"){return Cj(this.attrs.path,A)}},AL.getSubpath=function(A,B){if(this.type=="path"){return B1.getSubpath(this.attrs.path,A,B)}};var Ax=B1.easing_formulas={linear:function(A){return A},"<":function(A){return By(A,1.7)},">":function(A){return By(A,0.48)},"<>":function(E){var C=0.48-E/1.04,D=BO.sqrt(0.1734+C*C),H=D-C,A=By(BD(H),1/3)*(H<0?-1:1),F=-D-C,G=By(BD(F),1/3)*(F<0?-1:1),B=A+G+0.5;return(1-B)*3*B*B+B*B*B},backIn:function(B){var A=1.70158;return B*B*((A+1)*B-A)},backOut:function(B){B=B-1;var A=1.70158;return B*B*((A+1)*B+A)+1},elastic:function(A){if(A==!!A){return A}return By(2,-10*A)*BO.sin((A-0.075)*2*Bv/0.3)+1},bounce:function(D){var B=7.5625,C=2.75,A;D<1/C?A=B*D*D:D<2/C?(D-=1.5/C,A=B*D*D+0.75):D<2.5/C?(D-=2.25/C,A=B*D*D+0.9375):(D-=2.625/C,A=B*D*D+0.984375);return A}};Ax.easeIn=Ax["ease-in"]=Ax["<"],Ax.easeOut=Ax["ease-out"]=Ax[">"],Ax.easeInOut=Ax["ease-in-out"]=Ax["<>"],Ax["back-in"]=Ax.backIn,Ax["back-out"]=Ax.backOut;var Aw=[],Az=window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.oRequestAnimationFrame||window.msRequestAnimationFrame||function(A){setTimeout(A,16)},As=function(){var P=+(new Date),Q=0;for(;Q<Aw.length;Q++){var S=Aw[Q];if(S.el.removed||S.paused){continue}var T=P-S.start,R=S.ms,K=S.easing,L=S.from,I=S.diff,J=S.to,N=S.t,O=S.el,M={},D,B={},C;S.initstatus?(T=(S.initstatus*S.anim.top-S.prev)/(S.percent-S.prev)*R,S.status=S.initstatus,delete S.initstatus,S.stop&&Aw.splice(Q--,1)):S.status=(S.prev+(S.percent-S.prev)*(T/R))/S.anim.top;if(T<0){continue}if(T<R){var G=K(T/R);for(var H in L){if(L[B3](H)){switch(Bm[H]){case Bw:D=+L[H]+G*R*I[H];break;case"colour":D="rgb("+[A0(Bs(L[H].r+G*R*I[H].r)),A0(Bs(L[H].g+G*R*I[H].g)),A0(Bs(L[H].b+G*R*I[H].b))].join(",")+")";break;case"path":D=[];for(var E=0,F=L[H].length;E<F;E++){D[E]=[L[H][E][0]];for(var W=1,X=L[H][E].length;W<X;W++){D[E][W]=+L[H][E][W]+G*R*I[H][E][W]}D[E]=D[E].join(BM)}D=D.join(BM);break;case"transform":if(I[H].real){D=[];for(E=0,F=L[H].length;E<F;E++){D[E]=[L[H][E][0]];for(W=1,X=L[H][E].length;W<X;W++){D[E][W]=L[H][E][W]+G*R*I[H][E][W]}}}else{var V=function(A){return +L[H][A]+G*R*I[H][A]};D=[["m",V(0),V(1),V(2),V(3),V(4),V(5)]]}break;case"csv":if(H=="clip-rect"){D=[],E=4;while(E--){D[E]=+L[H][E]+G*R*I[H][E]}}break;default:var U=[][BV](L[H]);D=[],E=O.paper.customAttributes[H].length;while(E--){D[E]=+U[E]+G*R*I[H][E]}}M[H]=D}}O.attr(M),function(Z,A,Y){setTimeout(function(){eve("raphael.anim.frame."+Z,A,Y)})}(O.id,O,S.anim)}else{(function(Y,Z,A){setTimeout(function(){eve("raphael.anim.frame."+Z.id,Z,A),eve("raphael.anim.finish."+Z.id,Z,A),B1.is(Y,"function")&&Y.call(Z)})})(S.callback,O,S.anim),O.attr(J),Aw.splice(Q--,1);if(S.repeat>1&&!S.next){for(C in J){J[B3](C)&&(B[C]=S.totalOrigin[C])}S.el.attr(B),AO(S.anim,S.el,S.anim.percents[0],null,S.totalOrigin,S.repeat-1)}S.next&&!S.stop&&AO(S.anim,S.el,S.next,null,S.totalOrigin,S.repeat)}}B1.svg&&O&&O.paper&&O.paper.safari(),Aw.length&&Az(As)},A0=function(A){return A>255?255:A<0?0:A};AL.animateWith=function(H,I,L,A,J,K){var D=this;if(D.removed){K&&K.call(D);return D}var E=L instanceof Al?L:B1.animation(L,A,J,K),B,C;AO(E,D,E.percents[0],null,D.attr());for(var F=0,G=Aw.length;F<G;F++){if(Aw[F].anim==I&&Aw[F].el==H){Aw[G-1].start=Aw[F].start;break}}return D},AL.onAnimation=function(A){A?eve.on("raphael.anim.frame."+this.id,A):eve.unbind("raphael.anim.frame."+this.id);return this},Al.prototype.delay=function(B){var A=new Al(this.anim,this.ms);A.times=this.times,A.del=+B||0;return A},Al.prototype.repeat=function(B){var A=new Al(this.anim,this.ms);A.del=this.del,A.times=BO.floor(BF(B,0))||1;return A},B1.animation=function(E,F,B,C){if(E instanceof Al){return E}if(B1.is(B,"function")||!B){C=C||B||null,B=null}E=Object(E),F=+F||0;var G={},A,D;for(D in E){E[B3](D)&&Bi(D)!=D&&Bi(D)+"%"!=D&&(A=!0,G[D]=E[D])}if(!A){return new Al(E,F)}B&&(G.easing=B),C&&(G.callback=C);return new Al({100:G},F)},AL.animate=function(D,E,B,C){var F=this;if(F.removed){C&&C.call(F);return F}var A=D instanceof Al?D:B1.animation(D,E,B,C);AO(A,F,A.percents[0],null,F.attr());return F},AL.setTime=function(B,A){B&&A!=null&&this.status(B,BG(A,B.ms)/B.ms);return this},AL.status=function(E,C){var D=[],A=0,B,F;if(C!=null){AO(E,this,-1,BG(C,1));return this}B=Aw.length;for(;A<B;A++){F=Aw[A];if(F.el.id==this.id&&(!E||F.anim==E)){if(E){return F.status}D.push({anim:F.anim,status:F.status})}}if(E){return 0}return D},AL.pause=function(B){for(var A=0;A<Aw.length;A++){Aw[A].el.id==this.id&&(!B||Aw[A].anim==B)&&eve("raphael.anim.pause."+this.id,this,Aw[A].anim)!==!1&&(Aw[A].paused=!0)}return this},AL.resume=function(C){for(var A=0;A<Aw.length;A++){if(Aw[A].el.id==this.id&&(!C||Aw[A].anim==C)){var B=Aw[A];eve("raphael.anim.resume."+this.id,this,B.anim)!==!1&&(delete B.paused,this.status(B.anim,B.status))}}return this},AL.stop=function(B){for(var A=0;A<Aw.length;A++){Aw[A].el.id==this.id&&(!B||Aw[A].anim==B)&&eve("raphael.anim.stop."+this.id,this,Aw[A].anim)!==!1&&Aw.splice(A--,1)}return this},eve.on("raphael.remove",AV),eve.on("raphael.clear",AV),AL.toString=function(){return"Raphaël’s object"};var Cq=function(C){this.items=[],this.length=0,this.type="set";if(C){for(var A=0,B=C.length;A<B;A++){C[A]&&(C[A].constructor==AL.constructor||C[A].constructor==Cq)&&(this[this.items.length]=this.items[this.items.length]=C[A],this.length++)}}},AG=Cq.prototype;AG.push=function(){var D,B;for(var C=0,A=arguments.length;C<A;C++){D=arguments[C],D&&(D.constructor==AL.constructor||D.constructor==Cq)&&(B=this.items.length,this[B]=this.items[B]=D,this.length++)}return this},AG.pop=function(){this.length&&delete this[this.length--];return this.items.pop()},AG.forEach=function(D,B){for(var C=0,A=this.items.length;C<A;C++){if(D.call(B,this.items[C],C)===!1){return this}}return this};for(var AF in AL){AL[B3](AF)&&(AG[AF]=function(A){return function(){var B=arguments;return this.forEach(function(C){C[A][BY](C,B)})}}(AF))}AG.attr=function(D,E){if(D&&B1.is(D,BC)&&B1.is(D[0],"object")){for(var B=0,C=D.length;B<C;B++){this.items[B].attr(D[B])}}else{for(var F=0,A=this.items.length;F<A;F++){this.items[F].attr(D,E)}}return this},AG.clear=function(){while(this.length){this.pop()}},AG.splice=function(E,C,D){E=E<0?BF(this.length+E,0):E,C=BF(0,BG(this.length-E,C));var H=[],A=[],F=[],G;for(G=2;G<arguments.length;G++){F.push(arguments[G])}for(G=0;G<C;G++){A.push(this[E+G])}for(;G<this.length-E;G++){H.push(this[E+G])}var B=F.length;for(G=0;G<B+H.length;G++){this.items[E+G]=this[E+G]=G<B?F[G]:H[G-B]}G=this.items.length=this.length-=C-B;while(this[G]){delete this[G++]}return new Cq(A)},AG.exclude=function(C){for(var A=0,B=this.length;A<B;A++){if(this[A]==C){this.splice(A,1);return !0}}},AG.animate=function(F,G,J,A){(B1.is(J,"function")||!J)&&(A=J||null);var H=this.items.length,I=H,D,E=this,B;if(!H){return this}A&&(B=function(){!--H&&A.call(E)}),J=B1.is(J,BB)?J:B;var C=B1.animation(F,G,J,B);D=this.items[--I].animate(C);while(I--){this.items[I]&&!this.items[I].removed&&this.items[I].animateWith(D,C,C)}return this},AG.insertAfter=function(B){var A=this.items.length;while(A--){this.items[A].insertAfter(B)}return this},AG.getBBox=function(){var E=[],C=[],D=[],A=[];for(var B=this.items.length;B--;){if(!this.items[B].removed){var F=this.items[B].getBBox();E.push(F.x),C.push(F.y),D.push(F.x+F.width),A.push(F.y+F.height)}}E=BG[BY](0,E),C=BG[BY](0,C),D=BF[BY](0,D),A=BF[BY](0,A);return{x:E,y:C,x2:D,y2:A,width:D-E,height:A-C}},AG.clone=function(C){C=new Cq;for(var A=0,B=this.items.length;A<B;A++){C.push(this.items[A].clone())}return C},AG.toString=function(){return"Raphaël‘s set"},B1.registerFont=function(F){if(!F.face){return F}this.fonts=this.fonts||{};var D={w:F.w,face:{},glyphs:{}},E=F.face["font-family"];for(var B in F.face){F.face[B3](B)&&(D.face[B]=F.face[B])}this.fonts[E]?this.fonts[E].push(D):this.fonts[E]=[D];if(!F.svg){D.face["units-per-em"]=Aq(F.face["units-per-em"],10);for(var C in F.glyphs){if(F.glyphs[B3](C)){var G=F.glyphs[C];D.glyphs[C]={w:G.w,k:{},d:G.d&&"M"+G.d.replace(/[mlcxtrv]/g,function(H){return{l:"L",c:"C",x:"z",t:"m",r:"l",v:"c"}[H]||"M"})+"z"};if(G.k){for(var A in G.k){G[B3](A)&&(D.glyphs[C].k[A]=G.k[A])}}}}}return F},BS.getFont=function(G,H,J,A){A=A||"normal",J=J||"normal",H=+H||{normal:400,bold:700,lighter:300,bolder:800}[H]||400;if(!!B1.fonts){var I=B1.fonts[G];if(!I){var D=new RegExp("(^|\\s)"+G.replace(/[^\w\d\s+!~.:_-]/g,BL)+"(\\s|$)","i");for(var E in B1.fonts){if(B1.fonts[B3](E)&&D.test(E)){I=B1.fonts[E];break}}}var B;if(I){for(var C=0,F=I.length;C<F;C++){B=I[C];if(B.face["font-weight"]==H&&(B.face["font-style"]==J||!B.face["font-style"])&&B.face["font-stretch"]==A){break}}}return B}},BS.print=function(P,S,T,Q,R,J,K){J=J||"middle",K=BF(BG(K||0,1),-1);var H=BJ(T)[BK](BL),I=0,N=0,O=BL,L;B1.is(Q,T)&&(Q=this.getFont(Q));if(Q){L=(R||16)/Q.face["units-per-em"];var M=Q.face.bbox[BK](B0),C=+M[0],F=M[3]-M[1],G=0,D=+M[1]+(J=="baseline"?F+ +Q.face.descent:F/2);for(var E=0,W=H.length;E<W;E++){if(H[E]=="\n"){I=0,U=0,N=0,G+=F}else{var V=N&&Q.glyphs[H[E-1]]||{},U=Q.glyphs[H[E]];I+=N?(V.w||Q.w)+(V.k&&V.k[H[E]]||0)+Q.w*K:0,N=1}U&&U.d&&(O+=B1.transformPath(U.d,["t",I*L,G*L,"s",L,L,C,D,"t",(P-C)/L,(S-D)/L]))}}return this.path(O).attr({fill:"#000",stroke:"none"})},BS.add=function(C){if(B1.is(C,"array")){var D=this.set(),B=0,E=C.length,A;for(;B<E;B++){A=C[B]||{},B4[B3](A.type)&&D.push(this[A.type]().attr(A))}}return D},B1.format=function(B,C){var A=B1.is(C,BC)?[0][BV](C):arguments;B&&B1.is(B,BB)&&A.length-1&&(B=B.replace(B5,function(E,D){return A[++D]==null?BL:A[D]}));return B||BL},B1.fullfill=function(){var C=/\{([^\}]+)\}/g,A=/(?:(?:^|\.)(.+?)(?=\[|\.|$|\()|\[('|")(.+?)\2\])(\(\))?/g,B=function(G,F,D){var E=D;F.replace(A,function(K,I,J,H,L){I=I||H,E&&(I in E&&(E=E[I]),typeof E=="function"&&L&&(E=E()))}),E=(E==null||E==D?G:E)+"";return E};return function(E,D){return String(E).replace(C,function(G,F){return B(G,F,D)})}}(),B1.ninja=function(){BU.was?BT.win.Raphael=BU.is:delete Raphael;return B1},B1.st=AG,function(C,D,A){function B(){/in/.test(C.readyState)?setTimeout(B,9):B1.eve("raphael.DOMload")}C.readyState==null&&C.addEventListener&&(C.addEventListener(D,A=function(){C.removeEventListener(D,A,!1),C.readyState="complete"},!1),C.readyState="loading"),B()}(document,"DOMContentLoaded"),BU.was?BT.win.Raphael=B1:Raphael=B1,eve.on("raphael.DOMload",function(){BZ=!0})}(),window.Raphael.svg&&function(V){var T="hasOwnProperty",U=String,Y=parseFloat,Z=parseInt,W=Math,X=W.max,N=W.abs,O=W.pow,L=/[, ]+/,M=V.eve,R="",S=" ",P="http://www.w3.org/1999/xlink",Q={block:"M5,0 0,2.5 5,5z",classic:"M5,0 0,2.5 5,5 3.5,3 3.5,2z",diamond:"M2.5,0 5,2.5 2.5,5 0,2.5z",open:"M6,1 1,3.5 6,6",oval:"M2.5,0A2.5,2.5,0,0,1,2.5,5 2.5,2.5,0,0,1,2.5,0z"},F={};V.toString=function(){return"Your browser supports SVG.\nYou are running Raphaël "+this.version};var G=function(A,B){if(B){typeof A=="string"&&(A=G(A));for(var C in B){B[T](C)&&(C.substring(0,6)=="xlink:"?A.setAttributeNS(P,C.substring(6),U(B[C])):A.setAttribute(C,U(B[C])))}}else{A=V._g.doc.createElementNS("http://www.w3.org/2000/svg",A),A.style&&(A.style.webkitTapHighlightColor="rgba(0,0,0,0)")}return A},D=function(Ag,A){var h="linear",i=Ag.id+A,z=0.5,l=0.5,q=Ag.node,C=Ag.paper,a=q.style,B=V._g.doc.getElementById(i);if(!B){A=U(A).replace(V._radial_gradient,function(n,k,m){h="radial";if(k&&m){z=Y(k),l=Y(m);var j=(l>0.5)*2-1;O(z-0.5,2)+O(l-0.5,2)>0.25&&(l=W.sqrt(0.25-O(z-0.5,2))*j+0.5)&&l!=0.5&&(l=l.toFixed(5)-1e-05*j)}return R}),A=A.split(/\s*\-\s*/);if(h=="linear"){var f=A.shift();f=-Y(f);if(isNaN(f)){return null}var g=[0,0,W.cos(V.rad(f)),W.sin(V.rad(f))],c=1/(X(N(g[2]),N(g[3]))||1);g[2]*=c,g[3]*=c,g[2]<0&&(g[0]=-g[2],g[2]=0),g[3]<0&&(g[1]=-g[3],g[3]=0)}var d=V._parseDots(A);if(!d){return null}i=i.replace(/[\(\)\s,\xb0#]/g,"_"),Ag.gradient&&i!=Ag.gradient.id&&(C.defs.removeChild(Ag.gradient),delete Ag.gradient);if(!Ag.gradient){B=G(h+"Gradient",{id:i}),Ag.gradient=B,G(B,h=="radial"?{fx:z,fy:l}:{x1:g[0],y1:g[1],x2:g[2],y2:g[3],gradientTransform:Ag.matrix.invert()}),C.defs.appendChild(B);for(var Ah=0,Ai=d.length;Ah<Ai;Ah++){B.appendChild(G("stop",{offset:d[Ah].offset?d[Ah].offset:Ah?"100%":"0%","stop-color":d[Ah].color||"#fff"}))}}}G(q,{fill:"url(#"+i+")",opacity:1,"fill-opacity":1}),a.fill=R,a.opacity=1,a.fillOpacity=1;return 1},E=function(B){var A=B.getBBox(1);G(B.pattern,{patternTransform:B.matrix.invert()+" translate("+A.x+","+A.y+")"})},J=function(An,Ao,Al){if(An.type=="path"){var Am=U(Ao).toLowerCase().split("-"),Ah=An.paper,Ai=Al?"end":"start",q=An.node,Ag=An.attrs,Ak=Ag["stroke-width"],Aj=Am.length,a="classic",b,o,p,c,l,Aw=3,Ax=3,Av=5;while(Aj--){switch(Am[Aj]){case"block":case"classic":case"oval":case"diamond":case"open":case"none":a=Am[Aj];break;case"wide":Ax=5;break;case"narrow":Ax=2;break;case"long":Aw=5;break;case"short":Aw=2}}a=="open"?(Aw+=2,Ax+=2,Av+=2,p=1,c=Al?4:1,l={fill:"none",stroke:Ag.stroke}):(c=p=Aw/2,l={fill:Ag.stroke,stroke:"none"}),An._.arrows?Al?(An._.arrows.endPath&&F[An._.arrows.endPath]--,An._.arrows.endMarker&&F[An._.arrows.endMarker]--):(An._.arrows.startPath&&F[An._.arrows.startPath]--,An._.arrows.startMarker&&F[An._.arrows.startMarker]--):An._.arrows={};if(a!="none"){var Ar="raphael-marker-"+a,Ap="raphael-marker-"+Ai+a+Aw+Ax;V._g.doc.getElementById(Ar)?F[Ar]++:(Ah.defs.appendChild(G(G("path"),{"stroke-linecap":"round",d:Q[a],id:Ar})),F[Ar]=1);var Aq=V._g.doc.getElementById(Ap),Au;Aq?(F[Ap]++,Au=Aq.getElementsByTagName("use")[0]):(Aq=G(G("marker"),{id:Ap,markerHeight:Ax,markerWidth:Aw,orient:"auto",refX:c,refY:Ax/2}),Au=G(G("use"),{"xlink:href":"#"+Ar,transform:(Al?"rotate(180 "+Aw/2+" "+Ax/2+") ":R)+"scale("+Aw/Av+","+Ax/Av+")","stroke-width":(1/((Aw/Av+Ax/Av)/2)).toFixed(4)}),Aq.appendChild(Au),Ah.defs.appendChild(Aq),F[Ap]=1),G(Au,l);var As=p*(a!="diamond"&&a!="oval");Al?(b=An._.arrows.startdx*Ak||0,o=V.getTotalLength(Ag.path)-As*Ak):(b=As*Ak,o=V.getTotalLength(Ag.path)-(An._.arrows.enddx*Ak||0)),l={},l["marker-"+Ai]="url(#"+Ap+")";if(o||b){l.d=Raphael.getSubpath(Ag.path,b,o)}G(q,l),An._.arrows[Ai+"Path"]=Ar,An._.arrows[Ai+"Marker"]=Ap,An._.arrows[Ai+"dx"]=As,An._.arrows[Ai+"Type"]=a,An._.arrows[Ai+"String"]=Ao}else{Al?(b=An._.arrows.startdx*Ak||0,o=V.getTotalLength(Ag.path)-b):(b=0,o=V.getTotalLength(Ag.path)-(An._.arrows.enddx*Ak||0)),An._.arrows[Ai+"Path"]&&G(q,{d:Raphael.getSubpath(Ag.path,b,o)}),delete An._.arrows[Ai+"Path"],delete An._.arrows[Ai+"Marker"],delete An._.arrows[Ai+"dx"],delete An._.arrows[Ai+"Type"],delete An._.arrows[Ai+"String"]}for(l in F){if(F[T](l)&&!F[l]){var At=V._g.doc.getElementById(l);At&&At.parentNode.removeChild(At)}}}},K={"":[0],none:[0],"-":[3,1],".":[1,1],"-.":[3,1,1,1],"-..":[3,1,1,1,1,1],". ":[1,3],"- ":[4,3],"--":[8,3],"- .":[4,3,1,3],"--.":[8,3,1,3],"--..":[8,3,1,3,1,3]},H=function(j,i,B){i=K[U(i).toLowerCase()];if(i){var C=j.attrs["stroke-width"]||"1",k={round:C,square:C,butt:0}[j.attrs["stroke-linecap"]||B["stroke-linecap"]]||0,A=[],c=i.length;while(c--){A[c]=i[c]*C+(c%2?1:-1)*k}G(j.node,{"stroke-dasharray":A.join(",")})}},I=function(n,l){var g=n.node,e=n.attrs,j=g.style.visibility;g.style.visibility="hidden";for(var h in l){if(l[T](h)){if(!V._availableAttrs[T](h)){continue}var a=l[h];e[h]=a;switch(h){case"blur":n.blur(a);break;case"href":case"title":case"target":var c=g.parentNode;if(c.tagName.toLowerCase()!="a"){var b=G("a");c.insertBefore(b,g),b.appendChild(g),c=b}h=="target"?c.setAttributeNS(P,"show",a=="blank"?"new":a):c.setAttributeNS(P,h,a);break;case"cursor":g.style.cursor=a;break;case"transform":n.transform(a);break;case"arrow-start":J(n,a);break;case"arrow-end":J(n,a,1);break;case"clip-rect":var Ak=U(a).split(L);if(Ak.length==4){n.clip&&n.clip.parentNode.parentNode.removeChild(n.clip.parentNode);var Aj=G("clipPath"),y=G("rect");Aj.id=V.createUUID(),G(y,{x:Ak[0],y:Ak[1],width:Ak[2],height:Ak[3]}),Aj.appendChild(y),n.paper.defs.appendChild(Aj),G(g,{"clip-path":"url(#"+Aj.id+")"}),n.clip=y}if(!a){var t=g.getAttribute("clip-path");if(t){var v=V._g.doc.getElementById(t.replace(/(^url\(#|\)$)/g,R));v&&v.parentNode.removeChild(v),G(g,{"clip-path":R}),delete n.clip}}break;case"path":n.type=="path"&&(G(g,{d:a?e.path=V._pathToAbsolute(a):"M0,0"}),n._.dirty=1,n._.arrows&&("startString" in n._.arrows&&J(n,n._.arrows.startString),"endString" in n._.arrows&&J(n,n._.arrows.endString,1)));break;case"width":g.setAttribute(h,a),n._.dirty=1;if(e.fx){h="x",a=e.x}else{break}case"x":e.fx&&(a=-e.x-(e.width||0));case"rx":if(h=="rx"&&n.type=="rect"){break}case"cx":g.setAttribute(h,a),n.pattern&&E(n),n._.dirty=1;break;case"height":g.setAttribute(h,a),n._.dirty=1;if(e.fy){h="y",a=e.y}else{break}case"y":e.fy&&(a=-e.y-(e.height||0));case"ry":if(h=="ry"&&n.type=="rect"){break}case"cy":g.setAttribute(h,a),n.pattern&&E(n),n._.dirty=1;break;case"r":n.type=="rect"?G(g,{rx:a,ry:a}):g.setAttribute(h,a),n._.dirty=1;break;case"src":n.type=="image"&&g.setAttributeNS(P,"href",a);break;case"stroke-width":if(n._.sx!=1||n._.sy!=1){a/=X(N(n._.sx),N(n._.sy))||1}n.paper._vbSize&&(a*=n.paper._vbSize),g.setAttribute(h,a),e["stroke-dasharray"]&&H(n,e["stroke-dasharray"],l),n._.arrows&&("startString" in n._.arrows&&J(n,n._.arrows.startString),"endString" in n._.arrows&&J(n,n._.arrows.endString,1));break;case"stroke-dasharray":H(n,a,l);break;case"fill":var Ai=U(a).match(V._ISURL);if(Ai){Aj=G("pattern");var Ag=G("image");Aj.id=V.createUUID(),G(Aj,{x:0,y:0,patternUnits:"userSpaceOnUse",height:1,width:1}),G(Ag,{x:0,y:0,"xlink:href":Ai[1]}),Aj.appendChild(Ag),function(A){V._preload(Ai[1],function(){var C=this.offsetWidth,B=this.offsetHeight;G(A,{width:C,height:B}),G(Ag,{width:C,height:B}),n.paper.safari()})}(Aj),n.paper.defs.appendChild(Aj),G(g,{fill:"url(#"+Aj.id+")"}),n.pattern=Aj,n.pattern&&E(n);break}var Ah=V.getRGB(a);if(!Ah.error){delete l.gradient,delete e.gradient,!V.is(e.opacity,"undefined")&&V.is(l.opacity,"undefined")&&G(g,{opacity:e.opacity}),!V.is(e["fill-opacity"],"undefined")&&V.is(l["fill-opacity"],"undefined")&&G(g,{"fill-opacity":e["fill-opacity"]})}else{if((n.type=="circle"||n.type=="ellipse"||U(a).charAt()!="r")&&D(n,a)){if("opacity" in e||"fill-opacity" in e){var r=V._g.doc.getElementById(g.getAttribute("fill").replace(/^url\(#|\)$/g,R));if(r){var s=r.getElementsByTagName("stop");G(s[s.length-1],{"stop-opacity":("opacity" in e?e.opacity:1)*("fill-opacity" in e?e["fill-opacity"]:1)})}}e.gradient=a,e.fill="none";break}}Ah[T]("opacity")&&G(g,{"fill-opacity":Ah.opacity>1?Ah.opacity/100:Ah.opacity});case"stroke":Ah=V.getRGB(a),g.setAttribute(h,Ah.hex),h=="stroke"&&Ah[T]("opacity")&&G(g,{"stroke-opacity":Ah.opacity>1?Ah.opacity/100:Ah.opacity}),h=="stroke"&&n._.arrows&&("startString" in n._.arrows&&J(n,n._.arrows.startString),"endString" in n._.arrows&&J(n,n._.arrows.endString,1));break;case"gradient":(n.type=="circle"||n.type=="ellipse"||U(a).charAt()!="r")&&D(n,a);break;case"opacity":e.gradient&&!e[T]("stroke-opacity")&&G(g,{"stroke-opacity":a>1?a/100:a});case"fill-opacity":if(e.gradient){r=V._g.doc.getElementById(g.getAttribute("fill").replace(/^url\(#|\)$/g,R)),r&&(s=r.getElementsByTagName("stop"),G(s[s.length-1],{"stop-opacity":a}));break}default:h=="font-size"&&(a=Z(a,10)+"px");var q=h.replace(/(\-.)/g,function(A){return A.substring(1).toUpperCase()});g.style[q]=a,n._.dirty=1,g.setAttribute(h,a)}}}Af(n,l),g.style.visibility=j},Ae=1.2,Af=function(u,s){if(u.type=="text"&&!!(s[T]("text")||s[T]("font")||s[T]("font-size")||s[T]("x")||s[T]("y"))){var t=u.attrs,b=u.node,c=b.firstChild?Z(V._g.doc.defaultView.getComputedStyle(b.firstChild,R).getPropertyValue("font-size"),10):10;if(s[T]("text")){t.text=s.text;while(b.firstChild){b.removeChild(b.firstChild)}var C=U(s.text).split("\n"),a=[],q;for(var e=0,l=C.length;e<l;e++){q=G("tspan"),e&&G(q,{dy:c*Ae,x:t.x}),q.appendChild(V._g.doc.createTextNode(C[e])),b.appendChild(q),a[e]=q}}else{a=b.getElementsByTagName("tspan");for(e=0,l=a.length;e<l;e++){e?G(a[e],{dy:c*Ae,x:t.x}):G(a[0],{dy:0})}}G(b,{x:t.x,y:t.y}),u._.dirty=1;var B=u._getBBox(),A=t.y-(B.y+B.height/2);A&&V.is(A,"finite")&&G(a[0],{dy:A})}},Ad=function(C,a){var A=0,B=0;this[0]=this.node=C,C.raphael=!0,this.id=V._oid++,C.raphaelid=this.id,this.matrix=V.matrix(),this.realPath=null,this.paper=a,this.attrs=this.attrs||{},this._={transform:[],sx:1,sy:1,deg:0,dx:0,dy:0,dirty:1},!a.bottom&&(a.bottom=this),this.prev=a.top,a.top&&(a.top.next=this),a.top=this,this.next=null},Ac=V.el;Ad.prototype=Ac,Ac.constructor=Ad,V._engine.path=function(e,B){var C=G("path");B.canvas&&B.canvas.appendChild(C);var A=new Ad(C,B);A.type="path",I(A,{fill:"none",stroke:"#000",path:e});return A},Ac.rotate=function(C,B,A){if(this.removed){return this}C=U(C).split(L),C.length-1&&(B=Y(C[1]),A=Y(C[2])),C=Y(C[0]),A==null&&(B=A);if(B==null||A==null){var c=this.getBBox(1);B=c.x+c.width/2,A=c.y+c.height/2}this.transform(this._.transform.concat([["r",C,B,A]]));return this},Ac.scale=function(c,C,B,d){if(this.removed){return this}c=U(c).split(L),c.length-1&&(C=Y(c[1]),B=Y(c[2]),d=Y(c[3])),c=Y(c[0]),C==null&&(C=c),d==null&&(B=d);if(B==null||d==null){var A=this.getBBox(1)}B=B==null?A.x+A.width/2:B,d=d==null?A.y+A.height/2:d,this.transform(this._.transform.concat([["s",c,C,B,d]]));return this},Ac.translate=function(B,A){if(this.removed){return this}B=U(B).split(L),B.length-1&&(A=Y(B[1])),B=Y(B[0])||0,A=+A||0,this.transform(this._.transform.concat([["t",B,A]]));return this},Ac.transform=function(C){var A=this._;if(C==null){return A.transform}V._extractTransform(this,C),this.clip&&G(this.clip,{transform:this.matrix.invert()}),this.pattern&&E(this),this.node&&G(this.node,{transform:this.matrix});if(A.sx!=1||A.sy!=1){var B=this.attrs[T]("stroke-width")?this.attrs["stroke-width"]:1;this.attr({"stroke-width":B})}return this},Ac.hide=function(){!this.removed&&this.paper.safari(this.node.style.display="none");return this},Ac.show=function(){!this.removed&&this.paper.safari(this.node.style.display="");return this},Ac.remove=function(){if(!this.removed&&!!this.node.parentNode){var A=this.paper;A.__set__&&A.__set__.exclude(this),M.unbind("raphael.*.*."+this.id),this.gradient&&A.defs.removeChild(this.gradient),V._tear(this,A),this.node.parentNode.tagName.toLowerCase()=="a"?this.node.parentNode.parentNode.removeChild(this.node.parentNode):this.node.parentNode.removeChild(this.node);for(var B in this){this[B]=typeof this[B]=="function"?V._removedFactory(B):null}this.removed=!0}},Ac._getBBox=function(){if(this.node.style.display=="none"){this.show();var C=!0}var A={};try{A=this.node.getBBox()}catch(B){}finally{A=A||{}}C&&this.hide();return A},Ac.attr=function(r,u){if(this.removed){return this}if(r==null){var A={};for(var s in this.attrs){this.attrs[T](s)&&(A[s]=this.attrs[s])}A.gradient&&A.fill=="none"&&(A.fill=A.gradient)&&delete A.gradient,A.transform=this._.transform;return A}if(u==null&&V.is(r,"string")){if(r=="fill"&&this.attrs.fill=="none"&&this.attrs.gradient){return this.attrs.gradient}if(r=="transform"){return this._.transform}var t=r.split(L),C={};for(var a=0,k=t.length;a<k;a++){r=t[a],r in this.attrs?C[r]=this.attrs[r]:V.is(this.paper.customAttributes[r],"function")?C[r]=this.paper.customAttributes[r].def:C[r]=V._availableAttrs[r]}return k-1?C:C[t[0]]}if(u==null&&V.is(r,"array")){C={};for(a=0,k=r.length;a<k;a++){C[r[a]]=this.attr(r[a])}return C}if(u!=null){var q={};q[r]=u}else{r!=null&&V.is(r,"object")&&(q=r)}for(var b in q){M("raphael.attr."+b+"."+this.id,this,q[b])}for(b in this.paper.customAttributes){if(this.paper.customAttributes[T](b)&&q[T](b)&&V.is(this.paper.customAttributes[b],"function")){var j=this.paper.customAttributes[b].apply(this,[].concat(q[b]));this.attrs[b]=q[b];for(var B in j){j[T](B)&&(q[B]=j[B])}}}I(this,q);return this},Ac.toFront=function(){if(this.removed){return this}this.node.parentNode.tagName.toLowerCase()=="a"?this.node.parentNode.parentNode.appendChild(this.node.parentNode):this.node.parentNode.appendChild(this.node);var A=this.paper;A.top!=this&&V._tofront(this,A);return this},Ac.toBack=function(){if(this.removed){return this}var A=this.node.parentNode;A.tagName.toLowerCase()=="a"?A.parentNode.insertBefore(this.node.parentNode,this.node.parentNode.parentNode.firstChild):A.firstChild!=this.node&&A.insertBefore(this.node,this.node.parentNode.firstChild),V._toback(this,this.paper);var B=this.paper;return this},Ac.insertAfter=function(A){if(this.removed){return this}var B=A.node||A[A.length-1].node;B.nextSibling?B.parentNode.insertBefore(this.node,B.nextSibling):B.parentNode.appendChild(this.node),V._insertafter(this,A,this.paper);return this},Ac.insertBefore=function(A){if(this.removed){return this}var B=A.node||A[0].node;B.parentNode.insertBefore(this.node,B),V._insertbefore(this,A,this.paper);return this},Ac.blur=function(C){var a=this;if(+C!==0){var A=G("filter"),B=G("feGaussianBlur");a.attrs.blur=C,A.id=V.createUUID(),G(B,{stdDeviation:+C||1.5}),A.appendChild(B),a.paper.defs.appendChild(A),a._blur=A,G(a.node,{filter:"url(#"+A.id+")"})}else{a._blur&&(a._blur.parentNode.removeChild(a._blur),delete a._blur,delete a.attrs.blur),a.node.removeAttribute("filter")}},V._engine.circle=function(h,C,g,A){var B=G("circle");h.canvas&&h.canvas.appendChild(B);var i=new Ad(B,h);i.attrs={cx:C,cy:g,r:A,fill:"none",stroke:"#000"},i.type="circle",G(B,i.attrs);return i},V._engine.rect=function(j,C,i,m,A,k){var l=G("rect");j.canvas&&j.canvas.appendChild(l);var B=new Ad(l,j);B.attrs={x:C,y:i,width:m,height:A,r:k||0,rx:k||0,ry:k||0,fill:"none",stroke:"#000"},B.type="rect",G(l,B.attrs);return B},V._engine.ellipse=function(j,h,i,B,C){var k=G("ellipse");j.canvas&&j.canvas.appendChild(k);var A=new Ad(k,j);A.attrs={cx:h,cy:i,rx:B,ry:C,fill:"none",stroke:"#000"},A.type="ellipse",G(k,A.attrs);return A},V._engine.image=function(j,C,i,m,A,k){var l=G("image");G(l,{x:i,y:m,width:A,height:k,preserveAspectRatio:"none"}),l.setAttributeNS(P,"href",C),j.canvas&&j.canvas.appendChild(l);var B=new Ad(l,j);B.attrs={x:i,y:m,width:A,height:k,src:C},B.type="image";return B},V._engine.text=function(a,h,B,C){var i=G("text");a.canvas&&a.canvas.appendChild(i);var A=new Ad(i,a);A.attrs={x:h,y:B,"text-anchor":"middle",text:C,font:V._availableAttrs.font,stroke:"none",fill:"#000"},A.type="text",I(A,A.attrs);return A},V._engine.setSize=function(B,A){this.width=B||this.width,this.height=A||this.height,this.canvas.setAttribute("width",this.width),this.canvas.setAttribute("height",this.height),this._viewBox&&this.setViewBox.apply(this,this._viewBox);return this},V._engine.create=function(){var k=V._getContainer.apply(0,arguments),l=k&&k.container,o=k.x,A=k.y,m=k.width,n=k.height;if(!l){throw new Error("SVG container not found.")}var C=G("svg"),a="overflow:hidden;",B;o=o||0,A=A||0,m=m||512,n=n||342,G(C,{height:n,version:1.1,width:m,xmlns:"http://www.w3.org/2000/svg"}),l==1?(C.style.cssText=a+"position:absolute;left:"+o+"px;top:"+A+"px",V._g.doc.body.appendChild(C),B=1):(C.style.cssText=a+"position:relative",l.firstChild?l.insertBefore(C,l.firstChild):l.appendChild(C)),l=new V._Paper,l.width=m,l.height=n,l.canvas=C,l.clear(),l._left=l._top=0,B&&(l.renderfix=function(){}),l.renderfix();return l},V._engine.setViewBox=function(o,m,n,q,A){M("raphael.setViewBox",this,this._viewBox,[o,m,n,q,A]);var p=X(n/this.width,q/this.height),C=this.top,g=A?"meet":"xMinYMin",B,k;o==null?(this._vbSize&&(p=1),delete this._vbSize,B="0 0 "+this.width+S+this.height):(this._vbSize=p,B=o+S+m+S+n+S+q),G(this.canvas,{viewBox:B,preserveAspectRatio:g});while(p&&C){k="stroke-width" in C.attrs?C.attrs["stroke-width"]:1,C.attr({"stroke-width":k}),C._.dirty=1,C._.dirtyT=1,C=C.prev}this._viewBox=[o,m,n,q,!!A];return this},V.prototype.renderfix=function(){var h=this.canvas,C=h.style,g;try{g=h.getScreenCTM()||h.createSVGMatrix()}catch(A){g=h.createSVGMatrix()}var B=-g.e%1,i=-g.f%1;if(B||i){B&&(this._left=(this._left+B)%1,C.left=this._left+"px"),i&&(this._top=(this._top+i)%1,C.top=this._top+"px")}},V.prototype.clear=function(){V.eve("raphael.clear",this);var A=this.canvas;while(A.firstChild){A.removeChild(A.firstChild)}this.bottom=this.top=null,(this.desc=G("desc")).appendChild(V._g.doc.createTextNode("Created with Raphaël "+V.version)),A.appendChild(this.desc),A.appendChild(this.defs=G("defs"))},V.prototype.remove=function(){M("raphael.remove",this),this.canvas.parentNode&&this.canvas.parentNode.removeChild(this.canvas);for(var A in this){this[A]=typeof this[A]=="function"?V._removedFactory(A):null}};var Aa=V.st;for(var Ab in Ac){Ac[T](Ab)&&!Aa[T](Ab)&&(Aa[Ab]=function(A){return function(){var B=arguments;return this.forEach(function(C){C[A].apply(C,B)})}}(Ab))}}(window.Raphael),window.Raphael.vml&&function(Am){var Ak="hasOwnProperty",Al=String,Ap=parseFloat,I=Math,An=I.round,Ao=I.max,Ae=I.min,Af=I.abs,Ac="fill",Ad=/[, ]+/,Ai=Am.eve,Aj=" progid:DXImageTransform.Microsoft",Ag=" ",Ah="",W={M:"m",L:"l",C:"c",Z:"x",m:"t",l:"r",c:"v",z:"x"},X=/([clmz]),?([^clmz]*)/gi,U=/ progid:\S+Blur\([^\)]+\)/g,V=/-?[^,\s-]+/g,Aa="position:absolute;left:0;top:0;width:1px;height:1px",Ab=21600,Y={path:1,rect:1,image:1},Z={circle:1,ellipse:1},S=function(a){var f=/[ahqstv]/ig,A=Am._pathToAbsolute;Al(a).match(f)&&(A=Am._path2curve),f=/[clmz]/g;if(A==Am._pathToAbsolute&&!Al(a).match(f)){var c=Al(a).replace(X,function(m,k,l){var i=[],j=k.toLowerCase()=="m",h=W[k];l.replace(V,function(b){j&&i.length==2&&(h+=i+W[k=="m"?"l":"L"],i=[]),i.push(An(b*Ab))});return h+i});return c}var E=A(a),F,C;c=[];for(var D=0,G=E.length;D<G;D++){F=E[D],C=E[D][0].toLowerCase(),C=="z"&&(C="x");for(var H=1,B=F.length;H<B;H++){C+=An(F[H]*Ab)+(H!=B-1?",":Ah)}c.push(C)}return c.join(Ag)},T=function(C,D,A){var B=Am.matrix();B.rotate(-C,0.5,0.5);return{dx:B.x(D,A),dy:B.y(D,A)}},R=function(x,u,w,Aq,Ar,y){var z=x._,H=x.matrix,G=z.fillpos,j=x.node,n=j.style,i=1,C="",D,A=Ab/u,B=Ab/w;n.visibility="hidden";if(!!u&&!!w){j.coordsize=Af(A)+Ag+Af(B),n.rotation=y*(u*w<0?-1:1);if(y){var F=T(y,Aq,Ar);Aq=F.dx,Ar=F.dy}u<0&&(C+="x"),w<0&&(C+=" y")&&(i=-1),n.flip=C,j.coordorigin=Aq*-A+Ag+Ar*-B;if(G||z.fillsize){var E=j.getElementsByTagName(Ac);E=E&&E[0],j.removeChild(E),G&&(F=T(y,H.x(G[0],G[1]),H.y(G[0],G[1])),E.position=F.dx*i+Ag+F.dy*i),z.fillsize&&(E.size=z.fillsize[0]*Af(u)+Ag+z.fillsize[1]*Af(w)),j.appendChild(E)}n.visibility="visible"}};Am.toString=function(){return"Your browser doesn’t support SVG. Falling down to VML.\nYou are running Raphaël "+this.version};var M=function(G,F,l){var A=Al(F).toLowerCase().split("-"),H=l?"end":"start",c=A.length,D="classic",E="medium",B="medium";while(c--){switch(A[c]){case"block":case"classic":case"oval":case"diamond":case"open":case"none":D=A[c];break;case"wide":case"narrow":B=A[c];break;case"long":case"short":E=A[c]}}var C=G.node.getElementsByTagName("stroke")[0];C[H+"arrow"]=D,C[H+"arrowlength"]=E,C[H+"arrowwidth"]=B},K=function(A,AD){A.attrs=A.attrs||{};var AE=A.node,AF=A.attrs,AA=AE.style,AB,Ay=Y[A.type]&&(AD.x!=AF.x||AD.y!=AF.y||AD.width!=AF.width||AD.height!=AF.height||AD.cx!=AF.cx||AD.cy!=AF.cy||AD.rx!=AF.rx||AD.ry!=AF.ry||AD.r!=AF.r),Az=Z[A.type]&&(AF.cx!=AD.cx||AF.cy!=AD.cy||AF.r!=AD.r||AF.rx!=AD.rx||AF.ry!=AD.ry),AC=A;for(var Ax in AD){AD[Ak](Ax)&&(AF[Ax]=AD[Ax])}Ay&&(AF.path=Am._getPath[A.type](A),A._.dirty=1),AD.href&&(AE.href=AD.href),AD.title&&(AE.title=AD.title),AD.target&&(AE.target=AD.target),AD.cursor&&(AA.cursor=AD.cursor),"blur" in AD&&A.blur(AD.blur);if(AD.path&&A.type=="path"||Ay){AE.path=S(~Al(AF.path).toLowerCase().indexOf("r")?Am._pathToAbsolute(AF.path):AF.path),A.type=="image"&&(A._.fillpos=[AF.x,AF.y],A._.fillsize=[AF.width,AF.height],R(A,1,1,0,0,0))}"transform" in AD&&A.transform(AD.transform);if(Az){var At=+AF.cx,Av=+AF.cy,Aw=+AF.rx||+AF.r||0,Au=+AF.ry||+AF.r||0;AE.path=Am.format("ar{0},{1},{2},{3},{4},{1},{4},{1}x",An((At-Aw)*Ab),An((Av-Au)*Ab),An((At+Aw)*Ab),An((Av+Au)*Ab),An(At*Ab))}if("clip-rect" in AD){var w=Al(AD["clip-rect"]).split(Ad);if(w.length==4){w[2]=+w[2]+ +w[0],w[3]=+w[3]+ +w[1];var C=AE.clipRect||Am._g.doc.createElement("div"),u=C.style;u.clip=Am.format("rect({1}px {2}px {3}px {0}px)",w),AE.clipRect||(u.position="absolute",u.top=0,u.left=0,u.width=A.paper.width+"px",u.height=A.paper.height+"px",AE.parentNode.insertBefore(C,AE),C.appendChild(AE),AE.clipRect=C)}AD["clip-rect"]||AE.clipRect&&(AE.clipRect.style.clip="auto")}if(A.textpath){var v=A.textpath.style;AD.font&&(v.font=AD.font),AD["font-family"]&&(v.fontFamily='"'+AD["font-family"].split(",")[0].replace(/^['"]+|['"]+$/g,Ah)+'"'),AD["font-size"]&&(v.fontSize=AD["font-size"]),AD["font-weight"]&&(v.fontWeight=AD["font-weight"]),AD["font-style"]&&(v.fontStyle=AD["font-style"])}"arrow-start" in AD&&M(AC,AD["arrow-start"]),"arrow-end" in AD&&M(AC,AD["arrow-end"],1);if(AD.opacity!=null||AD["stroke-width"]!=null||AD.fill!=null||AD.src!=null||AD.stroke!=null||AD["stroke-width"]!=null||AD["stroke-opacity"]!=null||AD["fill-opacity"]!=null||AD["stroke-dasharray"]!=null||AD["stroke-miterlimit"]!=null||AD["stroke-linejoin"]!=null||AD["stroke-linecap"]!=null){var Ar=AE.getElementsByTagName(Ac),As=!1;Ar=Ar&&Ar[0],!Ar&&(As=Ar=N(Ac)),A.type=="image"&&AD.src&&(Ar.src=AD.src),AD.fill&&(Ar.on=!0);if(Ar.on==null||AD.fill=="none"||AD.fill===null){Ar.on=!1}if(Ar.on&&AD.fill){var z=Al(AD.fill).match(Am._ISURL);if(z){Ar.parentNode==AE&&AE.removeChild(Ar),Ar.rotate=!0,Ar.src=z[1],Ar.type="tile";var Aq=A.getBBox(1);Ar.position=Aq.x+Ag+Aq.y,A._.fillpos=[Aq.x,Aq.y],Am._preload(z[1],function(){A._.fillsize=[this.offsetWidth,this.offsetHeight]})}else{Ar.color=Am.getRGB(AD.fill).hex,Ar.src=Ah,Ar.type="solid",Am.getRGB(AD.fill).error&&(AC.type in {circle:1,ellipse:1}||Al(AD.fill).charAt()!="r")&&L(AC,AD.fill,Ar)&&(AF.fill="none",AF.gradient=AD.fill,Ar.rotate=!1)}}if("fill-opacity" in AD||"opacity" in AD){var g=((+AF["fill-opacity"]+1||2)-1)*((+AF.opacity+1||2)-1)*((+Am.getRGB(AD.fill).o+1||2)-1);g=Ae(Ao(g,0),1),Ar.opacity=g,Ar.src&&(Ar.color="none")}AE.appendChild(Ar);var h=AE.getElementsByTagName("stroke")&&AE.getElementsByTagName("stroke")[0],n=!1;!h&&(n=h=N("stroke"));if(AD.stroke&&AD.stroke!="none"||AD["stroke-width"]||AD["stroke-opacity"]!=null||AD["stroke-dasharray"]||AD["stroke-miterlimit"]||AD["stroke-linejoin"]||AD["stroke-linecap"]){h.on=!0}(AD.stroke=="none"||AD.stroke===null||h.on==null||AD.stroke==0||AD["stroke-width"]==0)&&(h.on=!1);var o=Am.getRGB(AD.stroke);h.on&&AD.stroke&&(h.color=o.hex),g=((+AF["stroke-opacity"]+1||2)-1)*((+AF.opacity+1||2)-1)*((+o.o+1||2)-1);var j=(Ap(AD["stroke-width"])||1)*0.75;g=Ae(Ao(g,0),1),AD["stroke-width"]==null&&(j=AF["stroke-width"]),AD["stroke-width"]&&(h.weight=j),j&&j<1&&(g*=j)&&(h.weight=1),h.opacity=g,AD["stroke-linejoin"]&&(h.joinstyle=AD["stroke-linejoin"]||"miter"),h.miterlimit=AD["stroke-miterlimit"]||8,AD["stroke-linecap"]&&(h.endcap=AD["stroke-linecap"]=="butt"?"flat":AD["stroke-linecap"]=="square"?"square":"round");if(AD["stroke-dasharray"]){var k={"-":"shortdash",".":"shortdot","-.":"shortdashdot","-..":"shortdashdotdot",". ":"dot","- ":"dash","--":"longdash","- .":"dashdot","--.":"longdashdot","--..":"longdashdotdot"};h.dashstyle=k[Ak](AD["stroke-dasharray"])?k[AD["stroke-dasharray"]]:Ah}n&&AE.appendChild(h)}if(AC.type=="text"){AC.paper.canvas.style.display=Ah;var c=AC.paper.span,d=100,b=AF.font&&AF.font.match(/\d+(?:\.\d*)?(?=px)/);AA=c.style,AF.font&&(AA.font=AF.font),AF["font-family"]&&(AA.fontFamily=AF["font-family"]),AF["font-weight"]&&(AA.fontWeight=AF["font-weight"]),AF["font-style"]&&(AA.fontStyle=AF["font-style"]),b=Ap(AF["font-size"]||b&&b[0])||10,AA.fontSize=b*d+"px",AC.textpath.string&&(c.innerHTML=Al(AC.textpath.string).replace(/</g,"&#60;").replace(/&/g,"&#38;").replace(/\n/g,"<br>"));var x=c.getBoundingClientRect();AC.W=AF.w=(x.right-x.left)/d,AC.H=AF.h=(x.bottom-x.top)/d,AC.X=AF.x,AC.Y=AF.y+AC.H/2,("x" in AD||"y" in AD)&&(AC.path.v=Am.format("m{0},{1}l{2},{1}",An(AF.x*Ab),An(AF.y*Ab),An(AF.x*Ab)+1));var f=["x","y","text","font","font-family","font-weight","font-style","font-size"];for(var F=0,a=f.length;F<a;F++){if(f[F] in AD){AC._.dirty=1;break}}switch(AF["text-anchor"]){case"start":AC.textpath.style["v-text-align"]="left",AC.bbx=AC.W/2;break;case"end":AC.textpath.style["v-text-align"]="right",AC.bbx=-AC.W/2;break;default:AC.textpath.style["v-text-align"]="center",AC.bbx=0}AC.textpath.style["v-text-kern"]=!0}},L=function(e,n,o){e.attrs=e.attrs||{};var H=e.attrs,a=Math.pow,F,G,c="linear",d=".5 .5";e.attrs.gradient=n,n=Al(n).replace(Am._radial_gradient,function(h,f,g){c="radial",f&&g&&(f=Ap(f),g=Ap(g),a(f-0.5,2)+a(g-0.5,2)>0.25&&(g=I.sqrt(0.25-a(f-0.5,2))*((g>0.5)*2-1)+0.5),d=f+Ag+g);return Ah}),n=n.split(/\s*\-\s*/);if(c=="linear"){var C=n.shift();C=-Ap(C);if(isNaN(C)){return null}}var D=Am._parseDots(n);if(!D){return null}e=e.shape||e.node;if(D.length){e.removeChild(o),o.on=!0,o.method="none",o.color=D[0].color,o.color2=D[D.length-1].color;var A=[];for(var B=0,E=D.length;B<E;B++){D[B].offset&&A.push(D[B].offset+Ag+D[B].color)}o.colors=A.length?A.join():"0% "+o.color,c=="radial"?(o.type="gradientTitle",o.focus="100%",o.focussize="0 0",o.focusposition=d,o.angle=0):(o.type="gradient",o.angle=(270-C)%360),e.appendChild(o)}return 1},P=function(A,B){this[0]=this.node=A,A.raphael=!0,this.id=Am._oid++,A.raphaelid=this.id,this.X=0,this.Y=0,this.attrs={},this.paper=B,this.matrix=Am.matrix(),this._={transform:[],sx:1,sy:1,dx:0,dy:0,deg:0,dirty:1,dirtyT:1},!B.bottom&&(B.bottom=this),this.prev=B.top,B.top&&(B.top.next=this),B.top=this,this.next=null},Q=Am.el;P.prototype=Q,Q.constructor=P,Q.transform=function(n){if(n==null){return this._.transform}var t=this.paper._viewBoxShift,A=t?"s"+[t.scale,t.scale]+"-1-1t"+[t.dx,t.dy]:Ah,o;t&&(o=n=Al(n).replace(/\.{3}|\u2026/g,this._.transform||Ah)),Am._extractTransform(this,A+n);var s=this.matrix.clone(),G=this.skew,H=this.node,E,F=~Al(this.attrs.fill).indexOf("-"),a=!Al(this.attrs.fill).indexOf("url(");s.translate(-0.5,-0.5);if(a||F||this.type=="image"){G.matrix="1 0 0 1",G.offset="0 0",E=s.split();if(F&&E.noRotation||!E.isSimple){H.style.filter=s.toFilter();var c=this.getBBox(),B=this.getBBox(1),C=c.x-B.x,D=c.y-B.y;H.coordorigin=C*-Ab+Ag+D*-Ab,R(this,1,1,C,D,0)}else{H.style.filter=Ah,R(this,E.scalex,E.scaley,E.dx,E.dy,E.rotate)}}else{H.style.filter=Ah,G.matrix=Al(s),G.offset=s.offset()}o&&(this._.transform=o);return this},Q.rotate=function(C,B,A){if(this.removed){return this}if(C!=null){C=Al(C).split(Ad),C.length-1&&(B=Ap(C[1]),A=Ap(C[2])),C=Ap(C[0]),A==null&&(B=A);if(B==null||A==null){var D=this.getBBox(1);B=D.x+D.width/2,A=D.y+D.height/2}this._.dirtyT=1,this.transform(this._.transform.concat([["r",C,B,A]]));return this}},Q.translate=function(B,A){if(this.removed){return this}B=Al(B).split(Ad),B.length-1&&(A=Ap(B[1])),B=Ap(B[0])||0,A=+A||0,this._.bbox&&(this._.bbox.x+=B,this._.bbox.y+=A),this.transform(this._.transform.concat([["t",B,A]]));return this},Q.scale=function(D,C,B,E){if(this.removed){return this}D=Al(D).split(Ad),D.length-1&&(C=Ap(D[1]),B=Ap(D[2]),E=Ap(D[3]),isNaN(B)&&(B=null),isNaN(E)&&(E=null)),D=Ap(D[0]),C==null&&(C=D),E==null&&(B=E);if(B==null||E==null){var A=this.getBBox(1)}B=B==null?A.x+A.width/2:B,E=E==null?A.y+A.height/2:E,this.transform(this._.transform.concat([["s",D,C,B,E]])),this._.dirtyT=1;return this},Q.hide=function(){!this.removed&&(this.node.style.display="none");return this},Q.show=function(){!this.removed&&(this.node.style.display=Ah);return this},Q._getBBox=function(){if(this.removed){return{}}return{x:this.X+(this.bbx||0)-this.W/2,y:this.Y-this.H,width:this.W,height:this.H}},Q.remove=function(){if(!this.removed&&!!this.node.parentNode){this.paper.__set__&&this.paper.__set__.exclude(this),Am.eve.unbind("raphael.*.*."+this.id),Am._tear(this,this.paper),this.node.parentNode.removeChild(this.node),this.shape&&this.shape.parentNode.removeChild(this.shape);for(var A in this){this[A]=typeof this[A]=="function"?Am._removedFactory(A):null}this.removed=!0}},Q.attr=function(a,k){if(this.removed){return this}if(a==null){var A={};for(var b in this.attrs){this.attrs[Ak](b)&&(A[b]=this.attrs[b])}A.gradient&&A.fill=="none"&&(A.fill=A.gradient)&&delete A.gradient,A.transform=this._.transform;return A}if(k==null&&Am.is(a,"string")){if(a==Ac&&this.attrs.fill=="none"&&this.attrs.gradient){return this.attrs.gradient}var j=a.split(Ad),D={};for(var E=0,H=j.length;E<H;E++){a=j[E],a in this.attrs?D[a]=this.attrs[a]:Am.is(this.paper.customAttributes[a],"function")?D[a]=this.paper.customAttributes[a].def:D[a]=Am._availableAttrs[a]}return H-1?D:D[j[0]]}if(this.attrs&&k==null&&Am.is(a,"array")){D={};for(E=0,H=a.length;E<H;E++){D[a[E]]=this.attr(a[E])}return D}var F;k!=null&&(F={},F[a]=k),k==null&&Am.is(a,"object")&&(F=a);for(var G in F){Ai("raphael.attr."+G+"."+this.id,this,F[G])}if(F){for(G in this.paper.customAttributes){if(this.paper.customAttributes[Ak](G)&&F[Ak](G)&&Am.is(this.paper.customAttributes[G],"function")){var B=this.paper.customAttributes[G].apply(this,[].concat(F[G]));this.attrs[G]=F[G];for(var C in B){B[Ak](C)&&(F[C]=B[C])}}}F.text&&this.type=="text"&&(this.textpath.string=F.text),K(this,F)}return this},Q.toFront=function(){!this.removed&&this.node.parentNode.appendChild(this.node),this.paper&&this.paper.top!=this&&Am._tofront(this,this.paper);return this},Q.toBack=function(){if(this.removed){return this}this.node.parentNode.firstChild!=this.node&&(this.node.parentNode.insertBefore(this.node,this.node.parentNode.firstChild),Am._toback(this,this.paper));return this},Q.insertAfter=function(A){if(this.removed){return this}A.constructor==Am.st.constructor&&(A=A[A.length-1]),A.node.nextSibling?A.node.parentNode.insertBefore(this.node,A.node.nextSibling):A.node.parentNode.appendChild(this.node),Am._insertafter(this,A,this.paper);return this},Q.insertBefore=function(A){if(this.removed){return this}A.constructor==Am.st.constructor&&(A=A[0]),A.node.parentNode.insertBefore(this.node,A.node),Am._insertbefore(this,A,this.paper);return this},Q.blur=function(B){var C=this.node.runtimeStyle,A=C.filter;A=A.replace(U,Ah),+B!==0?(this.attrs.blur=B,C.filter=A+Ag+Aj+".Blur(pixelradius="+(+B||1.5)+")",C.margin=Am.format("-{0}px 0 0 -{0}px",An(+B||1.5))):(C.filter=A,C.margin=0,delete this.attrs.blur)},Am._engine.path=function(E,C){var D=N("shape");D.style.cssText=Aa,D.coordsize=Ab+Ag+Ab,D.coordorigin=C.coordorigin;var A=new P(D,C),B={fill:"none",stroke:"#000"};E&&(B.path=E),A.type="path",A.path=[],A.Path=Ah,K(A,B),C.canvas.appendChild(D);var F=N("skew");F.on=!0,D.appendChild(F),A.skew=F,A.transform(Ah);return A},Am._engine.rect=function(E,F,a,A,G,H){var C=Am._rectPath(F,a,A,G,H),D=E.path(C),B=D.attrs;D.X=B.x=F,D.Y=B.y=a,D.W=B.width=A,D.H=B.height=G,B.r=H,B.path=C,D.type="rect";return D},Am._engine.ellipse=function(F,D,E,B,C){var G=F.path(),A=G.attrs;G.X=D-B,G.Y=E-C,G.W=B*2,G.H=C*2,G.type="ellipse",K(G,{cx:D,cy:E,rx:B,ry:C});return G},Am._engine.circle=function(E,C,D,A){var B=E.path(),F=B.attrs;B.X=C-A,B.Y=D-A,B.W=B.H=A*2,B.type="circle",K(B,{cx:C,cy:D,r:A});return B},Am._engine.image=function(G,H,n,A,a,j){var C=Am._rectPath(n,A,a,j),D=G.path(C).attr({stroke:"none"}),B=D.attrs,E=D.node,F=E.getElementsByTagName(Ac)[0];B.src=H,D.X=B.x=n,D.Y=B.y=A,D.W=B.width=a,D.H=B.height=j,B.path=C,D.type="image",F.parentNode==E&&E.removeChild(F),F.rotate=!0,F.src=H,F.type="tile",D._.fillpos=[n,A],D._.fillsize=[a,j],E.appendChild(F),R(D,1,1,0,0,0);return D},Am._engine.text=function(H,c,A,a){var D=N("shape"),E=N("path"),B=N("textpath");c=c||0,A=A||0,a=a||"",E.v=Am.format("m{0},{1}l{2},{1}",An(c*Ab),An(A*Ab),An(c*Ab)+1),E.textpathok=!0,B.string=Al(a),B.on=!0,D.style.cssText=Aa,D.coordsize=Ab+Ag+Ab,D.coordorigin="0 0";var C=new P(D,H),F={fill:"#000",stroke:"none",font:Am._availableAttrs.font,text:a};C.shape=D,C.path=E,C.textpath=B,C.type="text",C.attrs.text=Al(a),C.attrs.x=c,C.attrs.y=A,C.attrs.w=1,C.attrs.h=1,K(C,F),D.appendChild(B),D.appendChild(E),H.canvas.appendChild(D);var G=N("skew");G.on=!0,D.appendChild(G),C.skew=G,C.transform(Ah);return C},Am._engine.setSize=function(B,C){var A=this.canvas.style;this.width=B,this.height=C,B==+B&&(B+="px"),C==+C&&(C+="px"),A.width=B,A.height=C,A.clip="rect(0 "+B+" "+C+" 0)",this._viewBox&&Am._engine.setViewBox.apply(this,this._viewBox);return this},Am._engine.setViewBox=function(G,H,g,A,a){Am.eve("raphael.setViewBox",this,this._viewBox,[G,H,g,A,a]);var D=this.width,E=this.height,B=1/Ao(g/D,A/E),C,F;a&&(C=E/A,F=D/g,g*C<D&&(G-=(D-g*C)/2/C),A*F<E&&(H-=(E-A*F)/2/F)),this._viewBox=[G,H,g,A,!!a],this._viewBoxShift={dx:-G,dy:-H,scale:B},this.forEach(function(b){b.transform("...")});return this};var N;Am._engine.initWin=function(C){var A=C.document;A.createStyleSheet().addRule(".rvml","behavior:url(#default#VML)");try{!A.namespaces.rvml&&A.namespaces.add("rvml","urn:schemas-microsoft-com:vml"),N=function(D){return A.createElement("<rvml:"+D+' class="rvml">')}}catch(B){N=function(D){return A.createElement("<"+D+' xmlns="urn:schemas-microsoft.com:vml" class="rvml">')}}},Am._engine.initWin(Am._g.win),Am._engine.create=function(){var F=Am._getContainer.apply(0,arguments),G=F.container,l=F.height,A,H=F.width,a=F.x,D=F.y;if(!G){throw new Error("VML container not found.")}var E=new Am._Paper,B=E.canvas=Am._g.doc.createElement("div"),C=B.style;a=a||0,D=D||0,H=H||512,l=l||342,E.width=H,E.height=l,H==+H&&(H+="px"),l==+l&&(l+="px"),E.coordsize=Ab*1000+Ag+Ab*1000,E.coordorigin="0 0",E.span=Am._g.doc.createElement("span"),E.span.style.cssText="position:absolute;left:-9999em;top:-9999em;padding:0;margin:0;line-height:1;",B.appendChild(E.span),C.cssText=Am.format("top:0;left:0;width:{0};height:{1};display:inline-block;position:relative;clip:rect(0 {0} {1} 0);overflow:hidden",H,l),G==1?(Am._g.doc.body.appendChild(B),C.left=a+"px",C.top=D+"px",C.position="absolute"):G.firstChild?G.insertBefore(B,G.firstChild):G.appendChild(B),E.renderfix=function(){};return E},Am.prototype.clear=function(){Am.eve("raphael.clear",this),this.canvas.innerHTML=Ah,this.span=Am._g.doc.createElement("span"),this.span.style.cssText="position:absolute;left:-9999em;top:-9999em;padding:0;margin:0;line-height:1;display:inline;",this.canvas.appendChild(this.span),this.bottom=this.top=null},Am.prototype.remove=function(){Am.eve("raphael.remove",this),this.canvas.parentNode.removeChild(this.canvas);for(var A in this){this[A]=typeof this[A]=="function"?Am._removedFactory(A):null}return !0};var O=Am.st;for(var J in Q){Q[Ak](J)&&!O[Ak](J)&&(O[J]=function(A){return function(){var B=arguments;return this.forEach(function(C){C[A].apply(C,B)})}}(J))}}(window.Raphael);