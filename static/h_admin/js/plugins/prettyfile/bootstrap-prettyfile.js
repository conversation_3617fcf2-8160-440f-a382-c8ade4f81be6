(function(A){A.fn.extend({prettyFile:function(D){var G={text:"选择文件"};var D=A.extend(G,D);var C=this;function B(I,H){I.wrap("<div></div>");I.hide();I.after('				<div class="input-append input-group"">					<span class="input-group-btn">						<button class="btn btn-white" type="button">'+H+'</button>					</span>					<input class="input-large form-control" type="text">				</div>				');return I.parent()}function E(I,H){I.find('input[type="file"]').change(function(){var K=A(this)[0].files,L="";if(K.length==0){return false}if(!H||K.length==1){var J=A(this).val().split("\\");L=J[J.length-1]}else{if(K.length>1){L="已选择了"+K.length+" 个文件"}}I.find(".input-append input").val(L)})}function F(I,H){I.find(".input-append").click(function(J){J.preventDefault();I.find('input[type="file"]').click()})}return C.each(function(){$this=A(this);if($this){var H=$this.attr("multiple");$wrap=B($this,D.text);E($wrap,H);F($wrap)}})}})}(jQuery));