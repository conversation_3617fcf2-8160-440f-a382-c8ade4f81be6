(function(A,B){if(typeof define==="function"&&define.amd){define("simple-module",["jquery"],function(C){return(A.returnExportsGlobal=B(C))})}else{if(typeof exports==="object"){module.exports=B(require("jquery"))}else{A["SimpleModule"]=B(jQuery)}}}(this,function(C){var A,B=[].slice;A=(function(){D.extend=function(F){var E,H,G;if(!((F!=null)&&typeof F==="object")){return}for(E in F){H=F[E];if(E!=="included"&&E!=="extended"){this[E]=H}}return(G=F.extended)!=null?G.call(this):void 0};D.include=function(F){var E,H,G;if(!((F!=null)&&typeof F==="object")){return}for(E in F){H=F[E];if(E!=="included"&&E!=="extended"){this.prototype[E]=H}}return(G=F.included)!=null?G.call(this):void 0};D.connect=function(E){if(typeof E!=="function"){return}if(!E.pluginName){throw new Error("Module.connect: cannot connect plugin without pluginName");return}E.prototype._connected=true;if(!this._connectedClasses){this._connectedClasses=[]}this._connectedClasses.push(E);if(E.pluginName){return this[E.pluginName]=E}};D.prototype.opts={};function D(K){var I,J,E,F,L,H,G;this.opts=C.extend({},this.opts,K);(L=this.constructor)._connectedClasses||(L._connectedClasses=[]);E=(function(){var N,P,O,M;O=this.constructor._connectedClasses;M=[];for(N=0,P=O.length;N<P;N++){I=O[N];F=I.pluginName.charAt(0).toLowerCase()+I.pluginName.slice(1);if(I.prototype._connected){I.prototype._module=this}M.push(this[F]=new I())}return M}).call(this);if(this._connected){this.opts=C.extend({},this.opts,this._module.opts)}else{this._init();for(H=0,G=E.length;H<G;H++){J=E[H];if(typeof J._init==="function"){J._init()}}}this.trigger("initialized")}D.prototype._init=function(){};D.prototype.on=function(){var F,E;F=1<=arguments.length?B.call(arguments,0):[];(E=C(this)).on.apply(E,F);return this};D.prototype.one=function(){var F,E;F=1<=arguments.length?B.call(arguments,0):[];(E=C(this)).one.apply(E,F);return this};D.prototype.off=function(){var F,E;F=1<=arguments.length?B.call(arguments,0):[];(E=C(this)).off.apply(E,F);return this};D.prototype.trigger=function(){var F,E;F=1<=arguments.length?B.call(arguments,0):[];(E=C(this)).trigger.apply(E,F);return this};D.prototype.triggerHandler=function(){var F,E;F=1<=arguments.length?B.call(arguments,0):[];return(E=C(this)).triggerHandler.apply(E,F)};D.prototype._t=function(){var F,E;F=1<=arguments.length?B.call(arguments,0):[];return(E=this.constructor)._t.apply(E,F)};D._t=function(){var H,E,F,G;E=arguments[0],H=2<=arguments.length?B.call(arguments,1):[];F=((G=this.i18n[this.locale])!=null?G[E]:void 0)||"";if(!(H.length>0)){return F}F=F.replace(/([^%]|^)%(?:(\d+)\$)?s/g,function(I,J,K){if(K){return J+H[parseInt(K)-1]}else{return J+H.shift()}});return F.replace(/%%s/g,"%s")};D.i18n={"zh-CN":{}};D.locale="zh-CN";return D})();return A}));