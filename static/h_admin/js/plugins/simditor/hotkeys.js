(function(A,B){if(typeof define==="function"&&define.amd){define("simple-hotkeys",["jquery","simple-module"],function(D,C){return(A.returnExportsGlobal=B(D,C))})}else{if(typeof exports==="object"){module.exports=B(require("jquery"),require("simple-module"))}else{A.simple=A.simple||{};A.simple["hotkeys"]=B(jQuery,SimpleModule)}}}(this,function(F,D){var B,C,E={}.hasOwnProperty,A=function(H,I){for(var G in I){if(E.call(I,G)){H[G]=I[G]}}function J(){this.constructor=H}J.prototype=I.prototype;H.prototype=new J();H.__super__=I.prototype;return H};B=(function(G){A(H,G);function H(){return H.__super__.constructor.apply(this,arguments)}H.count=0;H.keyNameMap={8:"Backspace",9:"Tab",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Esc",32:"Spacebar",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"Left",38:"Up",39:"Right",40:"Down",45:"Insert",46:"Del",91:"Meta",93:"Meta",48:"0",49:"1",50:"2",51:"3",52:"4",53:"5",54:"6",55:"7",56:"8",57:"9",65:"A",66:"B",67:"C",68:"D",69:"E",70:"F",71:"G",72:"H",73:"I",74:"J",75:"K",76:"L",77:"M",78:"N",79:"O",80:"P",81:"Q",82:"R",83:"S",84:"T",85:"U",86:"V",87:"W",88:"X",89:"Y",90:"Z",96:"0",97:"1",98:"2",99:"3",100:"4",101:"5",102:"6",103:"7",104:"8",105:"9",106:"Multiply",107:"Add",109:"Subtract",110:"Decimal",111:"Divide",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",124:"F13",125:"F14",126:"F15",127:"F16",128:"F17",129:"F18",130:"F19",131:"F20",132:"F21",133:"F22",134:"F23",135:"F24",59:";",61:"=",186:";",187:"=",188:",",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"};H.aliases={"escape":"esc","delete":"del","return":"enter","ctrl":"control","space":"spacebar","ins":"insert","cmd":"meta","command":"meta","wins":"meta","windows":"meta"};H.normalize=function(K){var J,I,L,N,M,O;N=K.toLowerCase().replace(/\s+/gi,"").split("+");for(J=M=0,O=N.length;M<O;J=++M){I=N[J];N[J]=this.aliases[I]||I}L=N.pop();N.sort().push(L);return N.join("_")};H.prototype.opts={el:document};H.prototype._init=function(){this.id=++this.constructor.count;this._map={};this._delegate=typeof this.opts.el==="string"?document:this.opts.el;return F(this._delegate).on("keydown.simple-hotkeys-"+this.id,this.opts.el,(function(I){return function(J){var K;return(K=I._getHander(J))!=null?K.call(I,J):void 0}})(this))};H.prototype._getHander=function(I){var J,K;if(!(J=this.constructor.keyNameMap[I.which])){return}K="";if(I.altKey){K+="alt_"}if(I.ctrlKey){K+="control_"}if(I.metaKey){K+="meta_"}if(I.shiftKey){K+="shift_"}K+=J.toLowerCase();return this._map[K]};H.prototype.respondTo=function(I){if(typeof I==="string"){return this._map[this.constructor.normalize(I)]!=null}else{return this._getHander(I)!=null}};H.prototype.add=function(I,J){this._map[this.constructor.normalize(I)]=J;return this};H.prototype.remove=function(I){delete this._map[this.constructor.normalize(I)];return this};H.prototype.destroy=function(){F(this._delegate).off(".simple-hotkeys-"+this.id);this._map={};return this};return H})(D);C=function(G){return new B(G)};return C}));