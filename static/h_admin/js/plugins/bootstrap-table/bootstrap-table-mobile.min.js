!function(G){var E=function(K,L){K.options.columnsHidden.length>0&&G.each(K.columns,function(M,N){-1!==K.options.columnsHidden.indexOf(N.field)&&N.visible!==L&&K.toggleColumn(G.fn.bootstrapTable.utils.getFieldIndex(K.columns,N.field),L,!0)})},F=function(K){(K.options.height||K.options.showFooter)&&setTimeout(K.resetView,1)},J=function(M,L,K){M.options.minHeight?L<=M.options.minWidth&&K<=M.options.minHeight?A(M):L>M.options.minWidth&&K>M.options.minHeight&&H(M):L<=M.options.minWidth?A(M):L>M.options.minWidth&&H(M),F(M)},A=function(K){I(K,!1),E(K,!1)},H=function(K){I(K,!0),E(K,!0)},I=function(L,K){L.options.cardView=K,L.toggleView()},C=function(M,K){var L;return function(){var N=this,O=arguments,P=function(){L=null,M.apply(N,O)};clearTimeout(L),L=setTimeout(P,K)}};G.extend(G.fn.bootstrapTable.defaults,{mobileResponsive:!1,minWidth:562,minHeight:void 0,heightThreshold:100,checkOnInit:!0,columnsHidden:[]});var D=G.fn.bootstrapTable.Constructor,B=D.prototype.init;D.prototype.init=function(){if(B.apply(this,Array.prototype.slice.apply(arguments)),this.options.mobileResponsive&&this.options.minWidth){var L=this,M={width:G(window).width(),height:G(window).height()};if(G(window).on("resize orientationchange",C(function(){var O=G(this).height(),P=G(this).width();(Math.abs(M.height-O)>L.options.heightThreshold||M.width!=P)&&(J(L,P,O),M={width:P,height:O})},200)),this.options.checkOnInit){var K=G(window).height(),N=G(window).width();J(this,N,K),M={width:N,height:K}}}}}(jQuery);