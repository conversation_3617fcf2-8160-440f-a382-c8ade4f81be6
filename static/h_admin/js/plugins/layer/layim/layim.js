!function(B,D){var C={msgurl:"mailbox.html?msg=",chatlogurl:"mailbox.html?user=",aniTime:200,right:-232,api:{friend:"js/plugins/layer/layim/data/friend.json",group:"js/plugins/layer/layim/data/group.json",chatlog:"js/plugins/layer/layim/data/chatlog.json",groups:"js/plugins/layer/layim/data/groups.json",sendurl:""},user:{name:"游客",face:"img/a1.jpg"},autoReplay:["您好，我现在有事不在，一会再和您联系。","你没发错吧？","洗澡中，请勿打扰，偷窥请购票，个体四十，团体八折，订票电话：一般人我不告诉他！","你好，我是主人的美女秘书，有什么事就跟我说吧，等他回来我会转告他的。","我正在拉磨，没法招呼您，因为我们家毛驴去动物保护协会把我告了，说我剥夺它休产假的权利。","<（@￣︶￣@）>","你要和我说话？你真的要和我说话？你确定自己想说吗？你一定非说不可吗？那你说吧，这是自动回复。","主人正在开机自检，键盘鼠标看好机会出去凉快去了，我是他的电冰箱，我打字比较慢，你慢慢说，别急……","(*^__^*) 嘻嘻，是贤心吗？"],chating:{},hosts:(function(){var F=location.href.match(/\:\d+/);F=F?F[0]:"";return"http://"+document.domain+F+"/"})(),json:function(G,I,F,H){return $.ajax({type:"POST",url:G,data:I,dataType:"json",success:F,error:H})},stopMP:function(F){F?F.stopPropagation():F.cancelBubble=true}},A=[$(window),$(document),$("html"),$("body")],E={};E.tabs=function(G){var F=E.node;F.tabs.eq(G).addClass("xxim_tabnow").siblings().removeClass("xxim_tabnow");F.list.eq(G).show().siblings(".xxim_list").hide();if(F.list.eq(G).find("li").length===0){E.getDates(G)}};E.renode=function(){var F=E.node={tabs:$("#xxim_tabs>span"),list:$(".xxim_list"),online:$(".xxim_online"),setonline:$(".xxim_setonline"),onlinetex:$("#xxim_onlinetex"),xximon:$("#xxim_on"),layimFooter:$("#xxim_bottom"),xximHide:$("#xxim_hide"),xximSearch:$("#xxim_searchkey"),searchMian:$("#xxim_searchmain"),closeSearch:$("#xxim_closesearch"),layimMin:$("#layim_min")}};E.expend=function(){var F=E.node;if(E.layimNode.attr("state")!=="1"){E.layimNode.stop().animate({right:C.right},C.aniTime,function(){F.xximon.addClass("xxim_off");try{localStorage.layimState=1}catch(G){}E.layimNode.attr({state:1});F.layimFooter.addClass("xxim_expend").stop().animate({marginLeft:C.right},C.aniTime/2);F.xximHide.addClass("xxim_show")})}else{E.layimNode.stop().animate({right:1},C.aniTime,function(){F.xximon.removeClass("xxim_off");try{localStorage.layimState=2}catch(G){}E.layimNode.removeAttr("state");F.layimFooter.removeClass("xxim_expend");F.xximHide.removeClass("xxim_show")});F.layimFooter.stop().animate({marginLeft:0},C.aniTime)}};E.layinit=function(){var G=E.node;try{if(localStorage.layimState==="1"){E.layimNode.attr({state:1}).css({right:C.right});G.xximon.addClass("xxim_off");G.layimFooter.addClass("xxim_expend").css({marginLeft:C.right});G.xximHide.addClass("xxim_show")}}catch(F){}};E.popchat=function(G){var F=E.node,H={};H.success=function(I){layer.setMove();E.chatbox=I.find("#layim_chatbox");H.chatlist=E.chatbox.find(".layim_chatmore>ul");H.chatlist.html('<li data-id="'+G.id+'" type="'+G.type+'"  id="layim_user'+G.type+G.id+'"><span>'+G.name+"</span><em>×</em></li>");E.tabchat(G,E.chatbox);E.chatbox.find(".layer_setmin").on("click",function(){var J=I.attr("times");I.hide();F.layimMin.text(E.nowchat.name).show()});E.chatbox.find(".layim_close").on("click",function(){var J=I.attr("times");layer.close(J);E.chatbox=null;C.chating={};C.chatings=0});H.chatlist.on("mouseenter","li",function(){$(this).find("em").show()}).on("mouseleave","li",function(){$(this).find("em").hide()});H.chatlist.on("click","li em",function(L){var K=$(this).parent(),O=K.attr("type");var N=K.attr("data-id"),M=K.index();var J=H.chatlist.find("li"),P;C.stopMP(L);delete C.chating[O+N];C.chatings--;K.remove();$("#layim_area"+O+N).remove();if(O==="group"){$("#layim_group"+O+N).remove()}if(K.hasClass("layim_chatnow")){if(M===C.chatings){P=M-1}else{P=M+1}E.tabchat(C.chating[J.eq(P).attr("type")+J.eq(P).attr("data-id")])}if(H.chatlist.find("li").length===1){H.chatlist.parent().hide()}});H.chatlist.on("click","li",function(){var L=$(this),K=L.attr("type"),J=L.attr("data-id");E.tabchat(C.chating[K+J])});H.sendType=$("#layim_sendtype"),H.sendTypes=H.sendType.find("span");$("#layim_enter").on("click",function(J){C.stopMP(J);H.sendType.show()});H.sendTypes.on("click",function(){H.sendTypes.find("i").text("");$(this).find("i").text("√")});E.transmit()};H.html='<div class="layim_chatbox" id="layim_chatbox"><h6><span class="layim_move"></span>    <a href="'+G.url+'" class="layim_face" target="_blank"><img src="'+G.face+'" ></a>    <a href="'+G.url+'" class="layim_names" target="_blank">'+G.name+'</a>    <span class="layim_rightbtn">        <i class="layer_setmin">—</i>        <i class="layim_close">&times;</i>    </span></h6><div class="layim_chatmore" id="layim_chatmore">    <ul class="layim_chatlist"></ul></div><div class="layim_groups" id="layim_groups"></div><div class="layim_chat">    <div class="layim_chatarea" id="layim_chatarea">        <ul class="layim_chatview layim_chatthis"  id="layim_area'+G.type+G.id+'"></ul>    </div>    <div class="layim_tool">        <i class="layim_addface fa fa-meh-o" title="发送表情"></i>        <a href="javascript:;"><i class="layim_addimage fa fa-picture-o" title="上传图片"></i></a>        <a href="javascript:;"><i class="layim_addfile fa fa-paperclip" title="上传附件"></i></a>        <a href="" target="_blank" class="layim_seechatlog"><i class="fa fa-comment-o"></i>聊天记录</a>    </div>    <textarea class="layim_write" id="layim_write"></textarea>    <div class="layim_send">        <div class="layim_sendbtn" id="layim_sendbtn">发送<span class="layim_enter" id="layim_enter"><em class="layim_zero"></em></span></div>        <div class="layim_sendtype" id="layim_sendtype">            <span><i>√</i>按Enter键发送</span>            <span><i></i>按Ctrl+Enter键发送</span>        </div>    </div></div></div>';if(C.chatings<1){$.layer({type:1,border:[0],title:false,shade:[0],area:["620px","493px"],move:".layim_chatbox .layim_move",moveType:1,closeBtn:false,offset:[(($(window).height()-493)/2)+"px",""],page:{html:H.html},success:function(I){H.success(I)}})}else{H.chatmore=E.chatbox.find("#layim_chatmore");H.chatarea=E.chatbox.find("#layim_chatarea");H.chatmore.show();H.chatmore.find("ul>li").removeClass("layim_chatnow");H.chatmore.find("ul").append('<li data-id="'+G.id+'" type="'+G.type+'" id="layim_user'+G.type+G.id+'" class="layim_chatnow"><span>'+G.name+"</span><em>×</em></li>");H.chatarea.find(".layim_chatview").removeClass("layim_chatthis");H.chatarea.append('<ul class="layim_chatview layim_chatthis" id="layim_area'+G.type+G.id+'"></ul>');E.tabchat(G)}H.chatgroup=E.chatbox.find("#layim_groups");if(G.type==="group"){H.chatgroup.find("ul").removeClass("layim_groupthis");H.chatgroup.append('<ul class="layim_groupthis" id="layim_group'+G.type+G.id+'"></ul>');E.getGroups(G)}H.chatgroup.on("click","ul>li",function(){E.popchatbox($(this))})};E.tabchat=function(G){var F=E.node,H={},I=G.type+G.id;E.nowchat=G;E.chatbox.find("#layim_user"+I).addClass("layim_chatnow").siblings().removeClass("layim_chatnow");E.chatbox.find("#layim_area"+I).addClass("layim_chatthis").siblings().removeClass("layim_chatthis");E.chatbox.find("#layim_group"+I).addClass("layim_groupthis").siblings().removeClass("layim_groupthis");E.chatbox.find(".layim_face>img").attr("src",G.face);E.chatbox.find(".layim_face, .layim_names").attr("href",G.href);E.chatbox.find(".layim_names").text(G.name);E.chatbox.find(".layim_seechatlog").attr("href",C.chatlogurl+G.id);H.groups=E.chatbox.find(".layim_groups");if(G.type==="group"){H.groups.show()}else{H.groups.hide()}$("#layim_write").focus()};E.popchatbox=function(K){var H=E.node,J=K.attr("data-id"),I={id:J,type:K.attr("type"),name:K.find(".xxim_onename").text(),face:K.find(".xxim_oneface").attr("src"),href:"profile.html?user="+J},G=I.type+J;if(!C.chating[G]){E.popchat(I);C.chatings++}else{E.tabchat(I)}C.chating[G]=I;var F=$("#layim_chatbox");if(F[0]){H.layimMin.hide();F.parents(".xubox_layer").show()}};E.getGroups=function(F){var H=F.type+F.id,G="",I=E.chatbox.find("#layim_group"+H);I.addClass("loading");C.json(C.api.groups,{},function(L){if(L.status===1){var K=0,J=L.data.length;if(J>0){for(;K<J;K++){G+='<li data-id="'+L.data[K].id+'" type="one"><img src="'+L.data[K].face+'" class="xxim_oneface"><span class="xxim_onename">'+L.data[K].name+"</span></li>"}}else{G='<li class="layim_errors">没有群员</li>'}}else{G='<li class="layim_errors">'+L.msg+"</li>"}I.removeClass("loading");I.html(G)},function(){I.removeClass("loading");I.html('<li class="layim_errors">请求异常</li>')})};E.transmit=function(){var F=E.node,G={};F.sendbtn=$("#layim_sendbtn");F.imwrite=$("#layim_write");G.send=function(){var I={content:F.imwrite.val(),id:E.nowchat.id,sign_key:"",_:+new Date};if(I.content.replace(/\s/g,"")===""){layer.tips("说点啥呗！","#layim_write",2);F.imwrite.focus()}else{var H=E.nowchat.type+E.nowchat.id;G.html=function(K,J){return'<li class="'+(J==="me"?"layim_chateme":"")+'"><div class="layim_chatuser">'+function(){if(J==="me"){return'<span class="layim_chattime">'+K.time+'</span><span class="layim_chatname">'+K.name+'</span><img src="'+K.face+'" >'}else{return'<img src="'+K.face+'" ><span class="layim_chatname">'+K.name+'</span><span class="layim_chattime">'+K.time+"</span>"}}()+'</div><div class="layim_chatsay">'+K.content+'<em class="layim_zero"></em></div></li>'};G.imarea=E.chatbox.find("#layim_area"+H);G.imarea.append(G.html({time:"2014-04-26 0:37",name:C.user.name,face:C.user.face,content:I.content},"me"));F.imwrite.val("").focus();G.imarea.scrollTop(G.imarea[0].scrollHeight);setTimeout(function(){G.imarea.append(G.html({time:"2014-04-26 0:38",name:E.nowchat.name,face:E.nowchat.face,content:C.autoReplay[(Math.random()*C.autoReplay.length)|0]}));G.imarea.scrollTop(G.imarea[0].scrollHeight)},500)}};F.sendbtn.on("click",G.send);F.imwrite.keyup(function(H){if(H.keyCode===13){G.send()}})};E.event=function(){var F=E.node;F.tabs.eq(0).addClass("xxim_tabnow");F.tabs.on("click",function(){var H=$(this),G=H.index();E.tabs(G)});F.list.on("click","h5",function(){var I=$(this),H=I.siblings(".xxim_chatlist"),G=I.find("i");if(G.hasClass("fa-caret-down")){H.hide();G.attr("class","fa fa-caret-right")}else{H.show();G.attr("class","fa fa-caret-down")}});F.online.on("click",function(G){C.stopMP(G);F.setonline.show()});F.setonline.find("span").on("click",function(G){var H=$(this).index();C.stopMP(G);if(H===0){F.onlinetex.html("在线");F.online.removeClass("xxim_offline")}else{if(H===1){F.onlinetex.html("隐身");F.online.addClass("xxim_offline")}}F.setonline.hide()});F.xximon.on("click",E.expend);F.xximHide.on("click",E.expend);F.xximSearch.keyup(function(){var G=$(this).val().replace(/\s/g,"");if(G!==""){F.searchMian.show();F.closeSearch.show();F.list.eq(3).html('<li class="xxim_errormsg">没有符合条件的结果</li>')}else{F.searchMian.hide();F.closeSearch.hide()}});F.closeSearch.on("click",function(){$(this).hide();F.searchMian.hide();F.xximSearch.val("").focus()});C.chatings=0;F.list.on("click",".xxim_childnode",function(){var G=$(this);E.popchatbox(G)});F.layimMin.on("click",function(){$(this).hide();$("#layim_chatbox").parents(".xubox_layer").show()});A[1].on("click",function(){F.setonline.hide();$("#layim_sendtype").hide()})};E.getDates=function(H){var I=[C.api.friend,C.api.group,C.api.chatlog],F=E.node,G=F.list.eq(H);G.addClass("loading");C.json(I[H],{},function(L){if(L.status===1){var J=0,N=L.data.length,M="",K;if(N>1){if(H!==2){for(;J<N;J++){M+='<li data-id="'+L.data[J].id+'" class="xxim_parentnode"><h5><i class="fa fa-caret-right"></i><span class="xxim_parentname">'+L.data[J].name+'</span><em class="xxim_nums">（'+L.data[J].nums+'）</em></h5><ul class="xxim_chatlist">';K=L.data[J].item;for(var O=0;O<K.length;O++){M+='<li data-id="'+K[O].id+'" class="xxim_childnode" type="'+(H===0?"one":"group")+'"><img src="'+K[O].face+'" class="xxim_oneface"><span class="xxim_onename">'+K[O].name+"</span></li>"}M+="</ul></li>"}}else{M+='<li class="xxim_liston"><ul class="xxim_chatlist">';for(;J<N;J++){M+='<li data-id="'+L.data[J].id+'" class="xxim_childnode" type="one"><img src="'+L.data[J].face+'"  class="xxim_oneface"><span  class="xxim_onename">'+L.data[J].name+'</span><em class="xxim_time">'+L.data[J].time+"</em></li>"}M+="</ul></li>"}G.html(M)}else{G.html('<li class="xxim_errormsg">没有任何数据</li>')}G.removeClass("loading")}else{G.html('<li class="xxim_errormsg">'+L.msg+"</li>")}},function(){G.html('<li class="xxim_errormsg">请求失败</li>');G.removeClass("loading")})};E.view=(function(){var F=E.layimNode=$('<div id="xximmm" class="xxim_main"><div class="xxim_top" id="xxim_top">  <div class="xxim_search"><i class="fa fa-search"></i><input id="xxim_searchkey" /><span id="xxim_closesearch">×</span></div>  <div class="xxim_tabs" id="xxim_tabs"><span class="xxim_tabfriend" title="好友"><i class="fa fa-user"></i></span><span class="xxim_tabgroup" title="群组"><i class="fa fa-users"></i></span><span class="xxim_latechat"  title="最近聊天"><i class="fa fa-clock-o"></i></span></div>  <ul class="xxim_list" style="display:block"></ul>  <ul class="xxim_list"></ul>  <ul class="xxim_list"></ul>  <ul class="xxim_list xxim_searchmain" id="xxim_searchmain"></ul></div><ul class="xxim_bottom" id="xxim_bottom"><li class="xxim_online" id="xxim_online"><i class="xxim_nowstate fa fa-check-circle"></i><span id="xxim_onlinetex">在线</span><div class="xxim_setonline"><span><i class="fa fa-check-circle"></i>在线</span><span class="xxim_setoffline"><i class="fa fa-check-circle"></i>隐身</span></div></li><li class="xxim_mymsg" id="xxim_mymsg" title="我的私信"><i class="fa fa-comment"></i><a href="'+C.msgurl+'" target="_blank"></a></li><li class="xxim_seter" id="xxim_seter" title="设置"><i class="fa fa-gear"></i><div></div></li><li class="xxim_hide" id="xxim_hide"><i class="fa fa-exchange"></i></li><li id="xxim_on" class="xxim_icon xxim_on fa fa-ellipsis-v"></li><div class="layim_min" id="layim_min"></div></ul></div>');A[3].append(F);E.renode();E.getDates(0);E.event();E.layinit()}())}(window);