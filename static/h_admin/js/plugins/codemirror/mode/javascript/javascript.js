(function(A){if(typeof exports=="object"&&typeof module=="object"){A(require("../../lib/codemirror"))}else{if(typeof define=="function"&&define.amd){define(["../../lib/codemirror"],A)}else{A(CodeMirror)}}})(function(A){A.defineMode("javascript",function(AB,m){var l=AB.indentUnit;var Ai=m.statementIndent;var i=m.jsonld;var E=m.json||i;var d=m.typescript;var x=m.wordCharacters||/[\w$\xa1-\uffff]/;var An=function(){function AM(AQ){return{type:AQ,style:"keyword"}}var AL=AM("keyword a"),AI=AM("keyword b"),AJ=AM("keyword c");var AH=AM("operator"),AG={type:"atom",style:"atom"};var AP={"if":AM("if"),"while":AL,"with":AL,"else":AI,"do":<PERSON>,"try":<PERSON>,"finally":<PERSON>,"return":<PERSON>,"break":<PERSON>,"continue":<PERSON>,"new":<PERSON>,"delete":<PERSON>,"throw":AJ,"debugger":AJ,"var":AM("var"),"const":AM("var"),"let":AM("var"),"function":AM("function"),"catch":AM("catch"),"for":AM("for"),"switch":AM("switch"),"case":AM("case"),"default":AM("default"),"in":AH,"typeof":AH,"instanceof":AH,"true":AG,"false":AG,"null":AG,"undefined":AG,"NaN":AG,"Infinity":AG,"this":AM("this"),"module":AM("module"),"class":AM("class"),"super":AM("atom"),"yield":AJ,"export":AM("export"),"import":AM("import"),"extends":AJ};if(d){var AO={type:"variable",style:"variable-3"};var AK={"interface":AM("interface"),"extends":AM("extends"),"constructor":AM("constructor"),"public":AM("public"),"private":AM("private"),"protected":AM("protected"),"static":AM("static"),"string":AO,"number":AO,"bool":AO,"any":AO};for(var AN in AK){AP[AN]=AK[AN]}}return AP}();var f=/[+\-*&%=<>!?|~^]/;var u=/^@(context|id|value|language|type|container|list|set|reverse|index|base|vocab|graph)"/;function Af(AJ){var AH=false,AG,AI=false;while((AG=AJ.next())!=null){if(!AH){if(AG=="/"&&!AI){return}if(AG=="["){AI=true}else{if(AI&&AG=="]"){AI=false}}}AH=!AH&&AG=="\\"}}var h,Ay;function j(AI,AH,AG){h=AI;Ay=AG;return AH}function z(AK,AI){var AH=AK.next();if(AH=='"'||AH=="'"){AI.tokenize=C(AH);return AI.tokenize(AK,AI)}else{if(AH=="."&&AK.match(/^\d+(?:[eE][+\-]?\d+)?/)){return j("number","number")}else{if(AH=="."&&AK.match("..")){return j("spread","meta")}else{if(/[\[\]{}\(\),;\:\.]/.test(AH)){return j(AH)}else{if(AH=="="&&AK.eat(">")){return j("=>","operator")}else{if(AH=="0"&&AK.eat(/x/i)){AK.eatWhile(/[\da-f]/i);return j("number","number")}else{if(/\d/.test(AH)){AK.match(/^\d*(?:\.\d*)?(?:[eE][+\-]?\d+)?/);return j("number","number")}else{if(AH=="/"){if(AK.eat("*")){AI.tokenize=AF;return AF(AK,AI)}else{if(AK.eat("/")){AK.skipToEnd();return j("comment","comment")}else{if(AI.lastType=="operator"||AI.lastType=="keyword c"||AI.lastType=="sof"||/^[\[{}\(,;:]$/.test(AI.lastType)){Af(AK);AK.eatWhile(/[gimy]/);return j("regexp","string-2")}else{AK.eatWhile(f);return j("operator","operator",AK.current())}}}}else{if(AH=="`"){AI.tokenize=s;return s(AK,AI)}else{if(AH=="#"){AK.skipToEnd();return j("error","error")}else{if(f.test(AH)){AK.eatWhile(f);return j("operator","operator",AK.current())}else{if(x.test(AH)){AK.eatWhile(x);var AJ=AK.current(),AG=An.propertyIsEnumerable(AJ)&&An[AJ];return(AG&&AI.lastType!=".")?j(AG.type,AG.style,AJ):j("variable","variable",AJ)}}}}}}}}}}}}}function C(AG){return function(AK,AI){var AH=false,AJ;if(i&&AK.peek()=="@"&&AK.match(u)){AI.tokenize=z;return j("jsonld-keyword","meta")}while((AJ=AK.next())!=null){if(AJ==AG&&!AH){break}AH=!AH&&AJ=="\\"}if(!AH){AI.tokenize=z}return j("string","string")}}function AF(AJ,AH){var AI=false,AG;while(AG=AJ.next()){if(AG=="/"&&AI){AH.tokenize=z;break}AI=(AG=="*")}return j("comment","comment")}function s(AJ,AH){var AG=false,AI;while((AI=AJ.next())!=null){if(!AG&&(AI=="`"||AI=="$"&&AJ.eat("{"))){AH.tokenize=z;break}AG=!AG&&AI=="\\"}return j("quasi","string-2",AJ.current())}var Aq="([{}])";function At(AL,AK){if(AK.fatArrowAt){AK.fatArrowAt=null}var AN=AL.string.indexOf("=>",AL.start);if(AN<0){return}var AG=0,AM=false;for(var AH=AN-1;AH>=0;--AH){var AJ=AL.string.charAt(AH);var AI=Aq.indexOf(AJ);if(AI>=0&&AI<3){if(!AG){++AH;break}if(--AG==0){break}}else{if(AI>=3&&AI<6){++AG}else{if(x.test(AJ)){AM=true}else{if(/["'\/]/.test(AJ)){return}else{if(AM&&!AG){++AH;break}}}}}}if(AM&&!AG){AK.fatArrowAt=AH}}var y={"atom":true,"number":true,"variable":true,"string":true,"regexp":true,"this":true,"jsonld-keyword":true};function Aw(AL,AK,AG,AI,AH,AJ){this.indented=AL;this.column=AK;this.type=AG;this.prev=AH;this.info=AJ;if(AI!=null){this.align=AI}}function Aj(AJ,AG){for(var AH=AJ.localVars;AH;AH=AH.next){if(AH.name==AG){return true}}for(var AI=AJ.context;AI;AI=AI.prev){for(var AH=AI.vars;AH;AH=AH.next){if(AH.name==AG){return true}}}}function S(AM,AJ,AG,AK,AI){var AH=AM.cc;U.state=AM;U.stream=AI;U.marked=null,U.cc=AH;U.style=AJ;if(!AM.lexical.hasOwnProperty("align")){AM.lexical.align=true}while(true){var AL=AH.length?AH.pop():E?k:Y;if(AL(AG,AK)){while(AH.length&&AH[AH.length-1].lex){AH.pop()()}if(U.marked){return U.marked}if(AG=="variable"&&Aj(AM,AK)){return"variable-2"}return AJ}}}var U={state:null,column:null,marked:null,cc:null};function As(){for(var AG=arguments.length-1;AG>=0;AG--){U.cc.push(arguments[AG])}}function K(){As.apply(null,arguments);return true}function O(AG){function AH(AK){for(var AJ=AK;AJ;AJ=AJ.next){if(AJ.name==AG){return true}}return false}var AI=U.state;if(AI.context){U.marked="def";if(AH(AI.localVars)){return}AI.localVars={name:AG,next:AI.localVars}}else{if(AH(AI.globalVars)){return}if(m.globalVars){AI.globalVars={name:AG,next:AI.globalVars}}}}var H={name:"this",next:{name:"arguments"}};function L(){U.state.context={prev:U.state.context,vars:U.state.localVars};U.state.localVars=H}function Z(){U.state.localVars=U.state.context.vars;U.state.context=U.state.context.prev}function G(AG,AI){var AH=function(){var AL=U.state,AK=AL.indented;if(AL.lexical.type=="stat"){AK=AL.lexical.indented}else{for(var AJ=AL.lexical;AJ&&AJ.type==")"&&AJ.align;AJ=AJ.prev){AK=AJ.indented}}AL.lexical=new Aw(AK,U.stream.column(),AG,null,AL.lexical,AI)};AH.lex=true;return AH}function Ag(){var AG=U.state;if(AG.lexical.prev){if(AG.lexical.type==")"){AG.indented=AG.lexical.indented}AG.lexical=AG.lexical.prev}}Ag.lex=true;function Aa(AH){function AG(AI){if(AI==AH){return K()}else{if(AH==";"){return As()}else{return K(AG)}}}return AG}function Y(AG,AH){if(AG=="var"){return K(G("vardef",AH.length),r,Aa(";"),Ag)}if(AG=="keyword a"){return K(G("form"),k,Y,Ag)}if(AG=="keyword b"){return K(G("form"),Y,Ag)}if(AG=="{"){return K(G("}"),B,Ag)}if(AG==";"){return K()}if(AG=="if"){if(U.state.lexical.info=="else"&&U.state.cc[U.state.cc.length-1]==Ag){U.state.cc.pop()()}return K(G("form"),k,Y,Ag,Ao)}if(AG=="function"){return K(J)}if(AG=="for"){return K(G("form"),t,Y,Ag)}if(AG=="variable"){return K(G("stat"),M)}if(AG=="switch"){return K(G("form"),k,G("}","switch"),Aa("{"),B,Ag,Ag)}if(AG=="case"){return K(k,Aa(":"))}if(AG=="default"){return K(Aa(":"))}if(AG=="catch"){return K(G("form"),L,Aa("("),q,Aa(")"),Y,Ag,Z)}if(AG=="module"){return K(G("form"),L,Al,Z,Ag)}if(AG=="class"){return K(G("form"),I,Ag)}if(AG=="export"){return K(G("form"),Ah,Ag)}if(AG=="import"){return K(G("form"),Av,Ag)}return As(G("stat"),k,Aa(";"),Ag)}function k(AG){return w(AG,false)}function Ap(AG){return w(AG,true)}function w(AG,AJ){if(U.state.fatArrowAt==U.stream.start){var AH=AJ?Q:AE;if(AG=="("){return K(L,G(")"),AD(AA,")"),Ag,Aa("=>"),AH,Z)}else{if(AG=="variable"){return As(L,AA,Aa("=>"),AH,Z)}}}var AI=AJ?b:Ad;if(y.hasOwnProperty(AG)){return K(AI)}if(AG=="function"){return K(J,AI)}if(AG=="keyword c"){return K(AJ?p:Ak)}if(AG=="("){return K(G(")"),Ak,P,Aa(")"),Ag,AI)}if(AG=="operator"||AG=="spread"){return K(AJ?Ap:k)}if(AG=="["){return K(G("]"),R,Ag,AI)}if(AG=="{"){return n(Ax,"}",null,AI)}if(AG=="quasi"){return As(X,AI)}return K()}function Ak(AG){if(AG.match(/[;\}\)\],]/)){return As()}return As(k)}function p(AG){if(AG.match(/[;\}\)\],]/)){return As()}return As(Ap)}function Ad(AG,AH){if(AG==","){return K(k)}return b(AG,AH,false)}function b(AG,AJ,AK){var AI=AK==false?Ad:b;var AH=AK==false?k:Ap;if(AG=="=>"){return K(L,AK?Q:AE,Z)}if(AG=="operator"){if(/\+\+|--/.test(AJ)){return K(AI)}if(AJ=="?"){return K(k,Aa(":"),AH)}return K(AH)}if(AG=="quasi"){return As(X,AI)}if(AG==";"){return}if(AG=="("){return n(Ap,")","call",AI)}if(AG=="."){return K(v,AI)}if(AG=="["){return K(G("]"),Ak,Aa("]"),Ag,AI)}}function X(AG,AH){if(AG!="quasi"){return As()}if(AH.slice(AH.length-2)!="${"){return K(X)}return K(k,Am)}function Am(AG){if(AG=="}"){U.marked="string-2";U.state.tokenize=s;return K(X)}}function AE(AG){At(U.stream,U.state);return As(AG=="{"?Y:k)}function Q(AG){At(U.stream,U.state);return As(AG=="{"?Y:Ap)}function M(AG){if(AG==":"){return K(Ag,Y)}return As(Ad,Aa(";"),Ag)}function v(AG){if(AG=="variable"){U.marked="property";return K()}}function Ax(AG,AH){if(AG=="variable"||U.style=="keyword"){U.marked="property";if(AH=="get"||AH=="set"){return K(a)}return K(V)}else{if(AG=="number"||AG=="string"){U.marked=i?"property":(U.style+" property");return K(V)}else{if(AG=="jsonld-keyword"){return K(V)}else{if(AG=="["){return K(k,Aa("]"),V)}}}}}function a(AG){if(AG!="variable"){return As(V)}U.marked="property";return K(J)}function V(AG){if(AG==":"){return K(Ap)}if(AG=="("){return As(J)}}function AD(AI,AH){function AG(AJ){if(AJ==","){var AK=U.state.lexical;if(AK.info=="call"){AK.pos=(AK.pos||0)+1}return K(AI,AG)}if(AJ==AH){return K()}return K(Aa(AH))}return function(AJ){if(AJ==AH){return K()}return As(AI,AG)}}function n(AI,AH,AJ){for(var AG=3;AG<arguments.length;AG++){U.cc.push(arguments[AG])}return K(G(AH,AJ),AD(AI,AH),Ag)}function B(AG){if(AG=="}"){return K()}return As(Y,B)}function T(AG){if(d&&AG==":"){return K(Ae)}}function Ae(AG){if(AG=="variable"){U.marked="variable-3";return K()}}function r(){return As(AA,T,c,AC)}function AA(AG,AH){if(AG=="variable"){O(AH);return K()}if(AG=="["){return n(AA,"]")}if(AG=="{"){return n(F,"}")}}function F(AG,AH){if(AG=="variable"&&!U.stream.match(/^\s*:/,false)){O(AH);return K(c)}if(AG=="variable"){U.marked="property"}return K(Aa(":"),AA,c)}function c(AG,AH){if(AH=="="){return K(Ap)}}function AC(AG){if(AG==","){return K(r)}}function Ao(AG,AH){if(AG=="keyword b"&&AH=="else"){return K(G("form","else"),Y,Ag)}}function t(AG){if(AG=="("){return K(G(")"),e,Aa(")"),Ag)}}function e(AG){if(AG=="var"){return K(r,Aa(";"),Az)}if(AG==";"){return K(Az)}if(AG=="variable"){return K(Ab)}return As(k,Aa(";"),Az)}function Ab(AG,AH){if(AH=="in"||AH=="of"){U.marked="keyword";return K(k)}return K(Ad,Az)}function Az(AG,AH){if(AG==";"){return K(Ac)}if(AH=="in"||AH=="of"){U.marked="keyword";return K(k)}return As(k,Aa(";"),Ac)}function Ac(AG){if(AG!=")"){K(k)}}function J(AG,AH){if(AH=="*"){U.marked="keyword";return K(J)}if(AG=="variable"){O(AH);return K(J)}if(AG=="("){return K(L,G(")"),AD(q,")"),Ag,Y,Z)}}function q(AG){if(AG=="spread"){return K(q)}return As(AA,T)}function I(AG,AH){if(AG=="variable"){O(AH);return K(N)}}function N(AG,AH){if(AH=="extends"){return K(k,N)}if(AG=="{"){return K(G("}"),Ar,Ag)}}function Ar(AG,AH){if(AG=="variable"||U.style=="keyword"){U.marked="property";if(AH=="get"||AH=="set"){return K(Au,J,Ar)}return K(J,Ar)}if(AH=="*"){U.marked="keyword";return K(Ar)}if(AG==";"){return K(Ar)}if(AG=="}"){return K()}}function Au(AG){if(AG!="variable"){return As()}U.marked="property";return K()}function Al(AG,AH){if(AG=="string"){return K(Y)}if(AG=="variable"){O(AH);return K(o)}}function Ah(AG,AH){if(AH=="*"){U.marked="keyword";return K(o,Aa(";"))}if(AH=="default"){U.marked="keyword";return K(k,Aa(";"))}return As(Y)}function Av(AG){if(AG=="string"){return K()}return As(W,o)}function W(AG,AH){if(AG=="{"){return n(W,"}")}if(AG=="variable"){O(AH)}return K()}function o(AG,AH){if(AH=="from"){U.marked="keyword";return K(k)}}function R(AG){if(AG=="]"){return K()}return As(Ap,D)}function D(AG){if(AG=="for"){return As(P,Aa("]"))}if(AG==","){return K(AD(p,"]"))}return As(AD(Ap,"]"))}function P(AG){if(AG=="for"){return K(t,P)}if(AG=="if"){return K(k,P)}}function g(AH,AG){return AH.lastType=="operator"||AH.lastType==","||f.test(AG.charAt(0))||/[,.]/.test(AG.charAt(0))}return{startState:function(AG){var AH={tokenize:z,lastType:"sof",cc:[],lexical:new Aw((AG||0)-l,0,"block",false),localVars:m.localVars,context:m.localVars&&{vars:m.localVars},indented:0};if(m.globalVars&&typeof m.globalVars=="object"){AH.globalVars=m.globalVars}return AH},token:function(AI,AH){if(AI.sol()){if(!AH.lexical.hasOwnProperty("align")){AH.lexical.align=false}AH.indented=AI.indentation();At(AI,AH)}if(AH.tokenize!=AF&&AI.eatSpace()){return null}var AG=AH.tokenize(AI,AH);if(h=="comment"){return AG}AH.lastType=h=="operator"&&(Ay=="++"||Ay=="--")?"incdec":h;return S(AH,AG,h,Ay,AI)},indent:function(AI,AG){if(AI.tokenize==AF){return A.Pass}if(AI.tokenize!=z){return 0}var AL=AG&&AG.charAt(0),AN=AI.lexical;if(!/^\s*else\b/.test(AG)){for(var AK=AI.cc.length-1;AK>=0;--AK){var AM=AI.cc[AK];if(AM==Ag){AN=AN.prev}else{if(AM!=Ao){break}}}}if(AN.type=="stat"&&AL=="}"){AN=AN.prev}if(Ai&&AN.type==")"&&AN.prev.type=="stat"){AN=AN.prev}var AH=AN.type,AJ=AL==AH;if(AH=="vardef"){return AN.indented+(AI.lastType=="operator"||AI.lastType==","?AN.info+1:0)}else{if(AH=="form"&&AL=="{"){return AN.indented}else{if(AH=="form"){return AN.indented+l}else{if(AH=="stat"){return AN.indented+(g(AI,AG)?Ai||l:0)}else{if(AN.info=="switch"&&!AJ&&m.doubleIndentSwitch!=false){return AN.indented+(/^(?:case|default)\b/.test(AG)?l:2*l)}else{if(AN.align){return AN.column+(AJ?0:1)}else{return AN.indented+(AJ?0:l)}}}}}}},electricInput:/^\s*(?:case .*?:|default:|\{|\})$/,blockCommentStart:E?null:"/*",blockCommentEnd:E?null:"*/",lineComment:E?null:"//",fold:"brace",helperType:E?"json":"javascript",jsonldMode:i,jsonMode:E}});A.registerHelper("wordChars","javascript",/[\w$]/);A.defineMIME("text/javascript","javascript");A.defineMIME("text/ecmascript","javascript");A.defineMIME("application/javascript","javascript");A.defineMIME("application/x-javascript","javascript");A.defineMIME("application/ecmascript","javascript");A.defineMIME("application/json",{name:"javascript",json:true});A.defineMIME("application/x-json",{name:"javascript",json:true});A.defineMIME("application/ld+json",{name:"javascript",jsonld:true});A.defineMIME("text/typescript",{name:"javascript",typescript:true});A.defineMIME("application/typescript",{name:"javascript",typescript:true})});