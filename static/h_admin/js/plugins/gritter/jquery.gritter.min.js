(function(A){A.gritter={};A.gritter.options={position:"",class_name:"",fade_in_speed:"medium",fade_out_speed:1000,time:6000};A.gritter.add=function(E){try{return B.add(E||{})}catch(C){var D="Gritter Error: "+C;(typeof(console)!="undefined"&&console.error)?console.error(D,E):alert(D)}};A.gritter.remove=function(C,D){B.removeSpecific(C,D||{})};A.gritter.removeAll=function(C){B.stop(C||{})};var B={position:"",fade_in_speed:"",fade_out_speed:"",time:"",_custom_timer:0,_item_count:0,_is_setup:0,_tpl_close:'<a class="gritter-close" href="#" tabindex="1">Close Notification</a>',_tpl_title:'<span class="gritter-title">[[title]]</span>',_tpl_item:'<div id="gritter-item-[[number]]" class="gritter-item-wrapper [[item_class]]" style="display:none" role="alert"><div class="gritter-top"></div><div class="gritter-item">[[close]][[image]]<div class="[[class_name]]">[[title]]<p>[[text]]</p></div><div style="clear:both"></div></div><div class="gritter-bottom"></div></div>',_tpl_wrap:'<div id="gritter-notice-wrapper"></div>',add:function(N){if(typeof(N)=="string"){N={text:N}}if(N.text===null){throw'You must supply "text" parameter.'}if(!this._is_setup){this._runSetup()}var E=N.title,H=N.text,C=N.image||"",J=N.sticky||false,K=N.class_name||A.gritter.options.class_name,D=A.gritter.options.position,O=N.time||"";this._verifyWrapper();this._item_count++;var M=this._item_count,G=this._tpl_item;A(["before_open","after_open","before_close","after_close"]).each(function(P,Q){B["_"+Q+"_"+M]=(A.isFunction(N[Q]))?N[Q]:function(){}});this._custom_timer=0;if(O){this._custom_timer=O}var L=(C!="")?'<img alt="image" src="'+C+'" class="gritter-image" />':"",F=(C!="")?"gritter-with-image":"gritter-without-image";if(E){E=this._str_replace("[[title]]",E,this._tpl_title)}else{E=""}G=this._str_replace(["[[title]]","[[text]]","[[close]]","[[image]]","[[number]]","[[class_name]]","[[item_class]]"],[E,H,this._tpl_close,L,this._item_count,F,K],G);if(this["_before_open_"+M]()===false){return false}A("#gritter-notice-wrapper").addClass(D).append(G);var I=A("#gritter-item-"+this._item_count);I.fadeIn(this.fade_in_speed,function(){B["_after_open_"+M](A(this))});if(!J){this._setFadeTimer(I,M)}A(I).bind("mouseenter mouseleave",function(P){if(P.type=="mouseenter"){if(!J){B._restoreItemIfFading(A(this),M)}}else{if(!J){B._setFadeTimer(A(this),M)}}B._hoverState(A(this),P.type)});A(I).find(".gritter-close").click(function(){B.removeSpecific(M,{},null,true);return false});return M},_countRemoveWrapper:function(D,C,E){C.remove();this["_after_close_"+D](C,E);if(A(".gritter-item-wrapper").length==0){A("#gritter-notice-wrapper").remove()}},_fade:function(C,D,H,I){var H=H||{},E=(typeof(H.fade)!="undefined")?H.fade:true,G=H.speed||this.fade_out_speed,F=I;this["_before_close_"+D](C,F);if(I){C.unbind("mouseenter mouseleave")}if(E){C.animate({opacity:0},G,function(){C.animate({height:0},300,function(){B._countRemoveWrapper(D,C,F)})})}else{this._countRemoveWrapper(D,C)}},_hoverState:function(C,D){if(D=="mouseenter"){C.addClass("hover");C.find(".gritter-close").show()}else{C.removeClass("hover");C.find(".gritter-close").hide()}},removeSpecific:function(E,C,F,D){if(!F){var F=A("#gritter-item-"+E)}this._fade(F,E,C||{},D)},_restoreItemIfFading:function(C,D){clearTimeout(this["_int_id_"+D]);C.stop().css({opacity:"",height:""})},_runSetup:function(){for(opt in A.gritter.options){this[opt]=A.gritter.options[opt]}this._is_setup=1},_setFadeTimer:function(E,C){var D=(this._custom_timer)?this._custom_timer:this.time;this["_int_id_"+C]=setTimeout(function(){B._fade(E,C)},D)},stop:function(D){var E=(A.isFunction(D.before_close))?D.before_close:function(){};var F=(A.isFunction(D.after_close))?D.after_close:function(){};var C=A("#gritter-notice-wrapper");E(C);C.fadeOut(function(){A(this).remove();F()})},_str_replace:function(F,C,L,K){var I=0,J=0,G="",N="",P=0,E=0,M=[].concat(F),O=[].concat(C),H=L,Q=O instanceof Array,D=H instanceof Array;H=[].concat(H);if(K){this.window[K]=0}for(I=0,P=H.length;I<P;I++){if(H[I]===""){continue}for(J=0,E=M.length;J<E;J++){G=H[I]+"";N=Q?(O[J]!==undefined?O[J]:""):O[0];H[I]=(G).split(M[J]).join(N);if(K&&H[I]!==G){this.window[K]+=(G.length-H[I].length)/M[J].length}}}return D?H:H[0]},_verifyWrapper:function(){if(A("#gritter-notice-wrapper").length==0){A("body").append(this._tpl_wrap)}}}})(jQuery);