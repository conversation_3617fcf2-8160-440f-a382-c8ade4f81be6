(function(C){function A(F,G,E){var H={square:function(M,K,L,N,I){var J=N*Math.sqrt(Math.PI)/2;M.rect(K-J,L-J,J+J,J+J)},diamond:function(M,K,L,N,I){var J=N*Math.sqrt(Math.PI/2);<PERSON>.moveTo(K-J,L);M.lineTo(K,L-J);M.lineTo(K+J,L);M.lineTo(K,L+J);M.lineTo(K-J,L)},triangle:function(N,L,M,O,I){var K=O*Math.sqrt(2*Math.PI/Math.sin(Math.PI/3));var J=K*Math.sin(Math.PI/3);N.moveTo(L-K/2,M+J/2);N.lineTo(L+K/2,M+J/2);if(!I){N.lineTo(L,M-J/2);N.lineTo(L-K/2,M+J/2)}},cross:function(M,K,L,N,I){var J=N*Math.sqrt(Math.PI)/2;<PERSON>.moveTo(K-J,L-J);M.lineTo(K+J,L+J);M.moveTo(K-J,L+J);M.lineTo(K+J,L-J)}};var D=G.points.symbol;if(H[D]){G.points.symbol=H[D]}}function B(D){D.hooks.processDatapoints.push(A)}C.plot.plugins.push({init:B,name:"symbols",version:"1.0"})})(jQuery);