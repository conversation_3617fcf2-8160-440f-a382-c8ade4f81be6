/* Javascript plotting library for jQuery, v. 0.7.
 *
 * Released under the MIT license by IOLA, December 2007.
 *
 */
(function(C){C.color={};C.color.make=function(A,I,J,H){var B={};B.r=A||0;B.g=I||0;B.b=J||0;B.a=H!=null?H:1;B.add=function(G,F){for(var E=0;E<G.length;++E){B[G.charAt(E)]+=F}return B.normalize()};B.scale=function(G,F){for(var E=0;E<G.length;++E){B[G.charAt(E)]*=F}return B.normalize()};B.toString=function(){if(B.a>=1){return"rgb("+[B.r,B.g,B.b].join(",")+")"}else{return"rgba("+[B.r,B.g,B.b,B.a].join(",")+")"}};B.normalize=function(){function E(L,F,G){return F<L?L:(F>G?G:F)}B.r=E(0,parseInt(B.r),255);B.g=E(0,parseInt(B.g),255);B.b=E(0,parseInt(B.b),255);B.a=E(0,B.a,1);return B};B.clone=function(){return C.color.make(B.r,B.b,B.g,B.a)};return B.normalize()};C.color.extract=function(A,F){var B;do{B=A.css(F).toLowerCase();if(B!=""&&B!="transparent"){break}A=A.parent()}while(!C.nodeName(A.get(0),"body"));if(B=="rgba(0, 0, 0, 0)"){B="transparent"}return C.color.parse(B)};C.color.parse=function(A){var G,H=C.color.make;if(G=/rgb\(\s*([0-9]{1,3})\s*,\s*([0-9]{1,3})\s*,\s*([0-9]{1,3})\s*\)/.exec(A)){return H(parseInt(G[1],10),parseInt(G[2],10),parseInt(G[3],10))}if(G=/rgba\(\s*([0-9]{1,3})\s*,\s*([0-9]{1,3})\s*,\s*([0-9]{1,3})\s*,\s*([0-9]+(?:\.[0-9]+)?)\s*\)/.exec(A)){return H(parseInt(G[1],10),parseInt(G[2],10),parseInt(G[3],10),parseFloat(G[4]))}if(G=/rgb\(\s*([0-9]+(?:\.[0-9]+)?)\%\s*,\s*([0-9]+(?:\.[0-9]+)?)\%\s*,\s*([0-9]+(?:\.[0-9]+)?)\%\s*\)/.exec(A)){return H(parseFloat(G[1])*2.55,parseFloat(G[2])*2.55,parseFloat(G[3])*2.55)}if(G=/rgba\(\s*([0-9]+(?:\.[0-9]+)?)\%\s*,\s*([0-9]+(?:\.[0-9]+)?)\%\s*,\s*([0-9]+(?:\.[0-9]+)?)\%\s*,\s*([0-9]+(?:\.[0-9]+)?)\s*\)/.exec(A)){return H(parseFloat(G[1])*2.55,parseFloat(G[2])*2.55,parseFloat(G[3])*2.55,parseFloat(G[4]))}if(G=/#([a-fA-F0-9]{2})([a-fA-F0-9]{2})([a-fA-F0-9]{2})/.exec(A)){return H(parseInt(G[1],16),parseInt(G[2],16),parseInt(G[3],16))}if(G=/#([a-fA-F0-9])([a-fA-F0-9])([a-fA-F0-9])/.exec(A)){return H(parseInt(G[1]+G[1],16),parseInt(G[2]+G[2],16),parseInt(G[3]+G[3],16))}var B=C.trim(A).toLowerCase();if(B=="transparent"){return H(255,255,255,0)}else{G=D[B]||[0,0,0];return H(G[0],G[1],G[2])}};var D={aqua:[0,255,255],azure:[240,255,255],beige:[245,245,220],black:[0,0,0],blue:[0,0,255],brown:[165,42,42],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgrey:[169,169,169],darkgreen:[0,100,0],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkviolet:[148,0,211],fuchsia:[255,0,255],gold:[255,215,0],green:[0,128,0],indigo:[75,0,130],khaki:[240,230,140],lightblue:[173,216,230],lightcyan:[224,255,255],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightyellow:[255,255,224],lime:[0,255,0],magenta:[255,0,255],maroon:[128,0,0],navy:[0,0,128],olive:[128,128,0],orange:[255,165,0],pink:[255,192,203],purple:[128,0,128],violet:[128,0,128],red:[255,0,0],silver:[192,192,192],white:[255,255,255],yellow:[255,255,0]}})(jQuery);(function(C){function A(Am,z,I,m){var Aj=[],R={colors:["#edc240","#afd8f8","#cb4b4b","#4da74d","#9440ed"],legend:{show:true,noColumns:1,labelFormatter:null,labelBoxBorderColor:"#ccc",container:null,position:"ne",margin:5,backgroundColor:null,backgroundOpacity:0.85},xaxis:{show:null,position:"bottom",mode:null,color:null,tickColor:null,transform:null,inverseTransform:null,min:null,max:null,autoscaleMargin:null,ticks:null,tickFormatter:null,labelWidth:null,labelHeight:null,reserveSpace:null,tickLength:null,alignTicksWithAxis:null,tickDecimals:null,tickSize:null,minTickSize:null,monthNames:null,timeformat:null,twelveHourClock:false},yaxis:{autoscaleMargin:0.02,position:"left"},xaxes:[],yaxes:[],series:{points:{show:false,radius:3,lineWidth:2,fill:true,fillColor:"#ffffff",symbol:"circle"},lines:{lineWidth:2,fill:false,fillColor:null,steps:false},bars:{show:false,lineWidth:2,barWidth:1,fill:true,fillColor:null,align:"left",horizontal:false},shadowSize:3},grid:{show:true,aboveData:false,color:"#545454",backgroundColor:null,borderColor:null,tickColor:null,labelMargin:5,axisMargin:8,borderWidth:2,minBorderMargin:null,markings:null,markingsColor:"#f4f4f4",markingsLineWidth:2,clickable:false,hoverable:false,autoHighlight:true,mouseActiveRadius:10},hooks:{}},J=null,Aa=null,Ax=null,X=null,S=null,h=[],Ap=[],n={left:0,right:0,top:0,bottom:0},H=0,w=0,Al=0,As=0,k={processOptions:[],processRawData:[],processDatapoints:[],drawSeries:[],draw:[],bindEvents:[],drawOverlay:[],shutdown:[]},Ak=this;Ak.setData=l;Ak.setupGrid=Ar;Ak.draw=L;Ak.getPlaceholder=function(){return Am};Ak.getCanvas=function(){return J};Ak.getPlotOffset=function(){return n};Ak.width=function(){return Al};Ak.height=function(){return As};Ak.offset=function(){var AA=Ax.offset();AA.left+=n.left;AA.top+=n.top;return AA};Ak.getData=function(){return Aj};Ak.getAxes=function(){var AB={},AA;C.each(h.concat(Ap),function(AD,AC){if(AC){AB[AC.direction+(AC.n!=1?AC.n:"")+"axis"]=AC}});return AB};Ak.getXAxes=function(){return h};Ak.getYAxes=function(){return Ap};Ak.c2p=Ao;Ak.p2c=b;Ak.getOptions=function(){return R};Ak.highlight=G;Ak.unhighlight=a;Ak.triggerRedrawOverlay=U;Ak.pointOffset=function(AA){return{left:parseInt(h[g(AA,"x")-1].p2c(+AA.x)+n.left),top:parseInt(Ap[g(AA,"y")-1].p2c(+AA.y)+n.top)}};Ak.shutdown=T;Ak.resize=function(){e();Q(J);Q(Aa)};Ak.hooks=k;y(Ak);Aw(I);Ac();l(z);Ar();L();M();function f(AB,AC){AC=[Ak].concat(AC);for(var AA=0;AA<AB.length;++AA){AB[AA].apply(this,AC)}}function y(){for(var AA=0;AA<m.length;++AA){var AB=m[AA];AB.init(Ak);if(AB.options){C.extend(true,R,AB.options)}}}function Aw(AB){var AA;C.extend(true,R,AB);if(R.xaxis.color==null){R.xaxis.color=R.grid.color}if(R.yaxis.color==null){R.yaxis.color=R.grid.color}if(R.xaxis.tickColor==null){R.xaxis.tickColor=R.grid.tickColor}if(R.yaxis.tickColor==null){R.yaxis.tickColor=R.grid.tickColor}if(R.grid.borderColor==null){R.grid.borderColor=R.grid.color}if(R.grid.tickColor==null){R.grid.tickColor=C.color.parse(R.grid.color).scale("a",0.22).toString()}for(AA=0;AA<Math.max(1,R.xaxes.length);++AA){R.xaxes[AA]=C.extend(true,{},R.xaxis,R.xaxes[AA])}for(AA=0;AA<Math.max(1,R.yaxes.length);++AA){R.yaxes[AA]=C.extend(true,{},R.yaxis,R.yaxes[AA])}if(R.xaxis.noTicks&&R.xaxis.ticks==null){R.xaxis.ticks=R.xaxis.noTicks}if(R.yaxis.noTicks&&R.yaxis.ticks==null){R.yaxis.ticks=R.yaxis.noTicks}if(R.x2axis){R.xaxes[1]=C.extend(true,{},R.xaxis,R.x2axis);R.xaxes[1].position="top"}if(R.y2axis){R.yaxes[1]=C.extend(true,{},R.yaxis,R.y2axis);R.yaxes[1].position="right"}if(R.grid.coloredAreas){R.grid.markings=R.grid.coloredAreas}if(R.grid.coloredAreasColor){R.grid.markingsColor=R.grid.coloredAreasColor}if(R.lines){C.extend(true,R.series.lines,R.lines)}if(R.points){C.extend(true,R.series.points,R.points)}if(R.bars){C.extend(true,R.series.bars,R.bars)}if(R.shadowSize!=null){R.series.shadowSize=R.shadowSize}for(AA=0;AA<R.xaxes.length;++AA){Ae(h,AA+1).options=R.xaxes[AA]}for(AA=0;AA<R.yaxes.length;++AA){Ae(Ap,AA+1).options=R.yaxes[AA]}for(var AC in k){if(R.hooks[AC]&&R.hooks[AC].length){k[AC]=k[AC].concat(R.hooks[AC])}}f(k.processOptions,[R])}function l(AA){Aj=Ag(AA);An();c()}function Ag(AA){var AD=[];for(var AB=0;AB<AA.length;++AB){var AC=C.extend(true,{},R.series);if(AA[AB].data!=null){AC.data=AA[AB].data;delete AA[AB].data;C.extend(true,AC,AA[AB]);AA[AB].data=AC.data}else{AC.data=AA[AB]}AD.push(AC)}return AD}function g(AA,AB){var AC=AA[AB+"axis"];if(typeof AC=="object"){AC=AC.n}if(typeof AC!="number"){AC=1}return AC}function K(){return C.grep(h.concat(Ap),function(AA){return AA})}function Ao(AB){var AC={},AA,AD;for(AA=0;AA<h.length;++AA){AD=h[AA];if(AD&&AD.used){AC["x"+AD.n]=AD.c2p(AB.left)}}for(AA=0;AA<Ap.length;++AA){AD=Ap[AA];if(AD&&AD.used){AC["y"+AD.n]=AD.c2p(AB.top)}}if(AC.x1!==undefined){AC.x=AC.x1}if(AC.y1!==undefined){AC.y=AC.y1}return AC}function b(AC){var AD={},AB,AE,AA;for(AB=0;AB<h.length;++AB){AE=h[AB];if(AE&&AE.used){AA="x"+AE.n;if(AC[AA]==null&&AE.n==1){AA="x"}if(AC[AA]!=null){AD.left=AE.p2c(AC[AA]);break}}}for(AB=0;AB<Ap.length;++AB){AE=Ap[AB];if(AE&&AE.used){AA="y"+AE.n;if(AC[AA]==null&&AE.n==1){AA="y"}if(AC[AA]!=null){AD.top=AE.p2c(AC[AA]);break}}}return AD}function Ae(AA,AB){if(!AA[AB-1]){AA[AB-1]={n:AB,direction:AA==h?"x":"y",options:C.extend(true,{},AA==h?R.xaxis:R.yaxis)}}return AA[AB-1]}function An(){var AG;var AC=Aj.length,AI=[],AD=[];for(AG=0;AG<Aj.length;++AG){var AF=Aj[AG].color;if(AF!=null){--AC;if(typeof AF=="number"){AD.push(AF)}else{AI.push(C.color.parse(Aj[AG].color))}}}for(AG=0;AG<AD.length;++AG){AC=Math.max(AC,AD[AG]+1)}var AH=[],AJ=0;AG=0;while(AH.length<AC){var AL;if(R.colors.length==AG){AL=C.color.make(100,100,100)}else{AL=C.color.parse(R.colors[AG])}var AK=AJ%2==1?-1:1;AL.scale("rgb",1+AK*Math.ceil(AJ/2)*0.2);AH.push(AL);++AG;if(AG>=R.colors.length){AG=0;++AJ}}var AA=0,AB;for(AG=0;AG<Aj.length;++AG){AB=Aj[AG];if(AB.color==null){AB.color=AH[AA].toString();++AA}else{if(typeof AB.color=="number"){AB.color=AH[AB.color].toString()}}if(AB.lines.show==null){var AE,AM=true;for(AE in AB){if(AB[AE]&&AB[AE].show){AM=false;break}}if(AM){AB.lines.show=true}}AB.xaxis=Ae(h,g(AB,"x"));AB.yaxis=Ae(Ap,g(AB,"y"))}}function c(){var AF=Number.POSITIVE_INFINITY,AM=Number.NEGATIVE_INFINITY,AI=Number.MAX_VALUE,AG,AD,AE,AH,AA,AP,AK,AB,AW,AX,AN,AR,AJ,AC;function AO(A2,A0,A1){if(A0<A2.datamin&&A0!=-AI){A2.datamin=A0}if(A1>A2.datamax&&A1!=AI){A2.datamax=A1}}C.each(K(),function(A1,A0){A0.datamin=AF;A0.datamax=AM;A0.used=false});for(AG=0;AG<Aj.length;++AG){AP=Aj[AG];AP.datapoints={points:[]};f(k.processRawData,[AP,AP.data,AP.datapoints])}for(AG=0;AG<Aj.length;++AG){AP=Aj[AG];var AU=AP.data,AQ=AP.datapoints.format;if(!AQ){AQ=[];AQ.push({x:true,number:true,required:true});AQ.push({y:true,number:true,required:true});if(AP.bars.show||(AP.lines.show&&AP.lines.fill)){AQ.push({y:true,number:true,required:false,defaultValue:0});if(AP.bars.horizontal){delete AQ[AQ.length-1].y;AQ[AQ.length-1].x=true}}AP.datapoints.format=AQ}if(AP.datapoints.pointsize!=null){continue}AP.datapoints.pointsize=AQ.length;AB=AP.datapoints.pointsize;AK=AP.datapoints.points;insertSteps=AP.lines.show&&AP.lines.steps;AP.xaxis.used=AP.yaxis.used=true;for(AD=AE=0;AD<AU.length;++AD,AE+=AB){AC=AU[AD];var AS=AC==null;if(!AS){for(AH=0;AH<AB;++AH){AR=AC[AH];AJ=AQ[AH];if(AJ){if(AJ.number&&AR!=null){AR=+AR;if(isNaN(AR)){AR=null}else{if(AR==Infinity){AR=AI}else{if(AR==-Infinity){AR=-AI}}}}if(AR==null){if(AJ.required){AS=true}if(AJ.defaultValue!=null){AR=AJ.defaultValue}}}AK[AE+AH]=AR}}if(AS){for(AH=0;AH<AB;++AH){AR=AK[AE+AH];if(AR!=null){AJ=AQ[AH];if(AJ.x){AO(AP.xaxis,AR,AR)}if(AJ.y){AO(AP.yaxis,AR,AR)}}AK[AE+AH]=null}}else{if(insertSteps&&AE>0&&AK[AE-AB]!=null&&AK[AE-AB]!=AK[AE]&&AK[AE-AB+1]!=AK[AE+1]){for(AH=0;AH<AB;++AH){AK[AE+AB+AH]=AK[AE+AH]}AK[AE+1]=AK[AE-AB+1];AE+=AB}}}}for(AG=0;AG<Aj.length;++AG){AP=Aj[AG];f(k.processDatapoints,[AP,AP.datapoints])}for(AG=0;AG<Aj.length;++AG){AP=Aj[AG];AK=AP.datapoints.points,AB=AP.datapoints.pointsize;var AT=AF,AY=AF,AL=AM,AV=AM;for(AD=0;AD<AK.length;AD+=AB){if(AK[AD]==null){continue}for(AH=0;AH<AB;++AH){AR=AK[AD+AH];AJ=AQ[AH];if(!AJ||AR==AI||AR==-AI){continue}if(AJ.x){if(AR<AT){AT=AR}if(AR>AL){AL=AR}}if(AJ.y){if(AR<AY){AY=AR}if(AR>AV){AV=AR}}}}if(AP.bars.show){var AZ=AP.bars.align=="left"?0:-AP.bars.barWidth/2;if(AP.bars.horizontal){AY+=AZ;AV+=AZ+AP.bars.barWidth}else{AT+=AZ;AL+=AZ+AP.bars.barWidth}}AO(AP.xaxis,AT,AL);AO(AP.yaxis,AY,AV)}C.each(K(),function(A1,A0){if(A0.datamin==AF){A0.datamin=null}if(A0.datamax==AM){A0.datamax=null}})}function D(AA,AB){var AC=document.createElement("canvas");AC.className=AB;AC.width=H;AC.height=w;if(!AA){C(AC).css({position:"absolute",left:0,top:0})}C(AC).appendTo(Am);if(!AC.getContext){AC=window.G_vmlCanvasManager.initElement(AC)}AC.getContext("2d").save();return AC}function e(){H=Am.width();w=Am.height();if(H<=0||w<=0){throw"Invalid dimensions for plot, width = "+H+", height = "+w}}function Q(AB){if(AB.width!=H){AB.width=H}if(AB.height!=w){AB.height=w}var AA=AB.getContext("2d");AA.restore();AA.save()}function Ac(){var AC,AA=Am.children("canvas.base"),AB=Am.children("canvas.overlay");if(AA.length==0||AB==0){Am.html("");Am.css({padding:0});if(Am.css("position")=="static"){Am.css("position","relative")}e();J=D(true,"base");Aa=D(false,"overlay");AC=false}else{J=AA.get(0);Aa=AB.get(0);AC=true}X=J.getContext("2d");S=Aa.getContext("2d");Ax=C([Aa,J]);if(AC){Am.data("plot").shutdown();Ak.resize();S.clearRect(0,0,H,w);Ax.unbind();Am.children().not([J,Aa]).remove()}Am.data("plot",Ak)}function M(){if(R.grid.hoverable){Ax.mousemove(Af);Ax.mouseleave(v)}if(R.grid.clickable){Ax.click(Ai)}f(k.bindEvents,[Ax])}function T(){if(Ab){clearTimeout(Ab)}Ax.unbind("mousemove",Af);Ax.unbind("mouseleave",v);Ax.unbind("click",Ai);f(k.shutdown,[Ax])}function s(AF){function AE(AG){return AG}var AB,AC,AD=AF.options.transform||AE,AA=AF.options.inverseTransform;if(AF.direction=="x"){AB=AF.scale=Al/Math.abs(AD(AF.max)-AD(AF.min));AC=Math.min(AD(AF.max),AD(AF.min))}else{AB=AF.scale=As/Math.abs(AD(AF.max)-AD(AF.min));AB=-AB;AC=Math.max(AD(AF.max),AD(AF.min))}if(AD==AE){AF.p2c=function(AG){return(AG-AC)*AB}}else{AF.p2c=function(AG){return(AD(AG)-AC)*AB}}if(!AA){AF.c2p=function(AG){return AC+AG/AB}}else{AF.c2p=function(AG){return AA(AC+AG/AB)}}}function Ad(AA){var AG=AA.options,AF,AI=AA.ticks||[],AJ=[],AH,AB=AG.labelWidth,AE=AG.labelHeight,AC;function AD(AL,AK){return C('<div style="position:absolute;top:-10000px;'+AK+'font-size:smaller"><div class="'+AA.direction+"Axis "+AA.direction+AA.n+'Axis">'+AL.join("")+"</div></div>").appendTo(Am)}if(AA.direction=="x"){if(AB==null){AB=Math.floor(H/(AI.length>0?AI.length:1))}if(AE==null){AJ=[];for(AF=0;AF<AI.length;++AF){AH=AI[AF].label;if(AH){AJ.push('<div class="tickLabel" style="float:left;width:'+AB+'px">'+AH+"</div>")}}if(AJ.length>0){AJ.push('<div style="clear:left"></div>');AC=AD(AJ,"width:10000px;");AE=AC.height();AC.remove()}}}else{if(AB==null||AE==null){for(AF=0;AF<AI.length;++AF){AH=AI[AF].label;if(AH){AJ.push('<div class="tickLabel">'+AH+"</div>")}}if(AJ.length>0){AC=AD(AJ,"");if(AB==null){AB=AC.children().width()}if(AE==null){AE=AC.find("div.tickLabel").height()}AC.remove()}}}if(AB==null){AB=0}if(AE==null){AE=0}AA.labelWidth=AB;AA.labelHeight=AE}function F(AA){var AK=AA.labelWidth,AE=AA.labelHeight,AG=AA.options.position,AL=AA.options.tickLength,AI=R.grid.axisMargin,AB=R.grid.labelMargin,AD=AA.direction=="x"?h:Ap,AC;var AH=C.grep(AD,function(AM){return AM&&AM.options.position==AG&&AM.reserveSpace});if(C.inArray(AA,AH)==AH.length-1){AI=0}if(AL==null){AL="full"}var AF=C.grep(AD,function(AM){return AM&&AM.reserveSpace});var AJ=C.inArray(AA,AF)==0;if(!AJ&&AL=="full"){AL=5}if(!isNaN(+AL)){AB+=+AL}if(AA.direction=="x"){AE+=AB;if(AG=="bottom"){n.bottom+=AE+AI;AA.box={top:w-n.bottom,height:AE}}else{AA.box={top:n.top+AI,height:AE};n.top+=AE+AI}}else{AK+=AB;if(AG=="left"){AA.box={left:n.left+AI,width:AK};n.left+=AK+AI}else{n.right+=AK+AI;AA.box={left:H-n.right,width:AK}}}AA.position=AG;AA.tickLength=AL;AA.box.padding=AB;AA.innermost=AJ}function d(AA){if(AA.direction=="x"){AA.box.left=n.left;AA.box.width=Al}else{AA.box.top=n.top;AA.box.height=As}}function Ar(){var AB,AA=K();C.each(AA,function(AF,AE){AE.show=AE.options.show;if(AE.show==null){AE.show=AE.used}AE.reserveSpace=AE.show||AE.options.reserveSpace;E(AE)});allocatedAxes=C.grep(AA,function(AE){return AE.reserveSpace});n.left=n.right=n.top=n.bottom=0;if(R.grid.show){C.each(allocatedAxes,function(AF,AE){Z(AE);Ay(AE);P(AE,AE.ticks);Ad(AE)});for(AB=allocatedAxes.length-1;AB>=0;--AB){F(allocatedAxes[AB])}var AD=R.grid.minBorderMargin;if(AD==null){AD=0;for(AB=0;AB<Aj.length;++AB){AD=Math.max(AD,Aj[AB].points.radius+Aj[AB].points.lineWidth/2)}}for(var AC in n){n[AC]+=R.grid.borderWidth;n[AC]=Math.max(AD,n[AC])}}Al=H-n.left-n.right;As=w-n.bottom-n.top;C.each(AA,function(AF,AE){s(AE)});if(R.grid.show){C.each(allocatedAxes,function(AF,AE){d(AE)});p()}At()}function E(AG){var AC=AG.options,AA=+(AC.min!=null?AC.min:AG.datamin),AE=+(AC.max!=null?AC.max:AG.datamax),AF=AE-AA;if(AF==0){var AB=AE==0?1:0.01;if(AC.min==null){AA-=AB}if(AC.max==null||AC.min!=null){AE+=AB}}else{var AD=AC.autoscaleMargin;if(AD!=null){if(AC.min==null){AA-=AF*AD;if(AA<0&&AG.datamin!=null&&AG.datamin>=0){AA=0}}if(AC.max==null){AE+=AF*AD;if(AE>0&&AG.datamax!=null&&AG.datamax<=0){AE=0}}}}AG.min=AA;AG.max=AE}function Z(AO){var AR=AO.options;var AQ;if(typeof AR.ticks=="number"&&AR.ticks>0){AQ=AR.ticks}else{AQ=0.3*Math.sqrt(AO.direction=="x"?H:w)}var AJ=(AO.max-AO.min)/AQ,AE,AB,AS,AC,AG,AN,AD;if(AR.mode=="time"){var AF={"second":1000,"minute":60*1000,"hour":60*60*1000,"day":24*60*60*1000,"month":30*24*60*60*1000,"year":365.2425*24*60*60*1000};var AL=[[1,"second"],[2,"second"],[5,"second"],[10,"second"],[30,"second"],[1,"minute"],[2,"minute"],[5,"minute"],[10,"minute"],[30,"minute"],[1,"hour"],[2,"hour"],[4,"hour"],[8,"hour"],[12,"hour"],[1,"day"],[2,"day"],[3,"day"],[0.25,"month"],[0.5,"month"],[1,"month"],[2,"month"],[3,"month"],[6,"month"],[1,"year"]];var AI=0;if(AR.minTickSize!=null){if(typeof AR.tickSize=="number"){AI=AR.tickSize}else{AI=AR.minTickSize[0]*AF[AR.minTickSize[1]]}}for(var AG=0;AG<AL.length-1;++AG){if(AJ<(AL[AG][0]*AF[AL[AG][1]]+AL[AG+1][0]*AF[AL[AG+1][1]])/2&&AL[AG][0]*AF[AL[AG][1]]>=AI){break}}AE=AL[AG][0];AS=AL[AG][1];if(AS=="year"){AN=Math.pow(10,Math.floor(Math.log(AJ/AF.year)/Math.LN10));AD=(AJ/AF.year)/AN;if(AD<1.5){AE=1}else{if(AD<3){AE=2}else{if(AD<7.5){AE=5}else{AE=10}}}AE*=AN}AO.tickSize=AR.tickSize||[AE,AS];AB=function(AU){var A2=[],A1=AU.tickSize[0],A3=AU.tickSize[1],A4=new Date(AU.min);var AZ=A1*AF[A3];if(A3=="second"){A4.setUTCSeconds(B(A4.getUTCSeconds(),A1))}if(A3=="minute"){A4.setUTCMinutes(B(A4.getUTCMinutes(),A1))}if(A3=="hour"){A4.setUTCHours(B(A4.getUTCHours(),A1))}if(A3=="month"){A4.setUTCMonth(B(A4.getUTCMonth(),A1))}if(A3=="year"){A4.setUTCFullYear(B(A4.getUTCFullYear(),A1))}A4.setUTCMilliseconds(0);if(AZ>=AF.minute){A4.setUTCSeconds(0)}if(AZ>=AF.hour){A4.setUTCMinutes(0)}if(AZ>=AF.day){A4.setUTCHours(0)}if(AZ>=AF.day*4){A4.setUTCDate(1)}if(AZ>=AF.year){A4.setUTCMonth(0)}var AY=0,AW=Number.NaN,A0;do{A0=AW;AW=A4.getTime();A2.push(AW);if(A3=="month"){if(A1<1){A4.setUTCDate(1);var AV=A4.getTime();A4.setUTCMonth(A4.getUTCMonth()+1);var AX=A4.getTime();A4.setTime(AW+AY*AF.hour+(AX-AV)*A1);AY=A4.getUTCHours();A4.setUTCHours(0)}else{A4.setUTCMonth(A4.getUTCMonth()+A1)}}else{if(A3=="year"){A4.setUTCFullYear(A4.getUTCFullYear()+A1)}else{A4.setTime(AW+AZ)}}}while(AW<AU.max&&AW!=A0);return A2};AC=function(AX,AZ){var AU=new Date(AX);if(AR.timeformat!=null){return C.plot.formatDate(AU,AR.timeformat,AR.monthNames)}var AY=AZ.tickSize[0]*AF[AZ.tickSize[1]];var AV=AZ.max-AZ.min;var AW=(AR.twelveHourClock)?" %p":"";if(AY<AF.minute){fmt="%h:%M:%S"+AW}else{if(AY<AF.day){if(AV<2*AF.day){fmt="%h:%M"+AW}else{fmt="%b %d %h:%M"+AW}}else{if(AY<AF.month){fmt="%b %d"}else{if(AY<AF.year){if(AV<AF.year){fmt="%b"}else{fmt="%b %y"}}else{fmt="%y"}}}}return C.plot.formatDate(AU,fmt,AR.monthNames)}}else{var AK=AR.tickDecimals;var AP=-Math.floor(Math.log(AJ)/Math.LN10);if(AK!=null&&AP>AK){AP=AK}AN=Math.pow(10,-AP);AD=AJ/AN;if(AD<1.5){AE=1}else{if(AD<3){AE=2;if(AD>2.25&&(AK==null||AP+1<=AK)){AE=2.5;++AP}}else{if(AD<7.5){AE=5}else{AE=10}}}AE*=AN;if(AR.minTickSize!=null&&AE<AR.minTickSize){AE=AR.minTickSize}AO.tickDecimals=Math.max(0,AK!=null?AK:AP);AO.tickSize=AR.tickSize||AE;AB=function(AZ){var AY=[];var AU=B(AZ.min,AZ.tickSize),AV=0,AX=Number.NaN,AW;do{AW=AX;AX=AU+AV*AZ.tickSize;AY.push(AX);++AV}while(AX<AZ.max&&AX!=AW);return AY};AC=function(AU,AV){return AU.toFixed(AV.tickDecimals)}}if(AR.alignTicksWithAxis!=null){var AM=(AO.direction=="x"?h:Ap)[AR.alignTicksWithAxis-1];if(AM&&AM.used&&AM!=AO){var AH=AB(AO);if(AH.length>0){if(AR.min==null){AO.min=Math.min(AO.min,AH[0])}if(AR.max==null&&AH.length>1){AO.max=Math.max(AO.max,AH[AH.length-1])}}AB=function(AX){var AW=[],AV,AU;for(AU=0;AU<AM.ticks.length;++AU){AV=(AM.ticks[AU].v-AM.min)/(AM.max-AM.min);AV=AX.min+AV*(AX.max-AX.min);AW.push(AV)}return AW};if(AO.mode!="time"&&AR.tickDecimals==null){var AT=Math.max(0,-Math.floor(Math.log(AJ)/Math.LN10)+1),AA=AB(AO);if(!(AA.length>1&&/\..*0$/.test((AA[1]-AA[0]).toFixed(AT)))){AO.tickDecimals=AT}}}}AO.tickGenerator=AB;if(C.isFunction(AR.tickFormatter)){AO.tickFormatter=function(AU,AV){return""+AR.tickFormatter(AU,AV)}}else{AO.tickFormatter=AC}}function Ay(AG){var AE=AG.options.ticks,AF=[];if(AE==null||(typeof AE=="number"&&AE>0)){AF=AG.tickGenerator(AG)}else{if(AE){if(C.isFunction(AE)){AF=AE({min:AG.min,max:AG.max})}else{AF=AE}}}var AB,AC;AG.ticks=[];for(AB=0;AB<AF.length;++AB){var AA=null;var AD=AF[AB];if(typeof AD=="object"){AC=+AD[0];if(AD.length>1){AA=AD[1]}}else{AC=+AD}if(AA==null){AA=AG.tickFormatter(AC,AG)}if(!isNaN(AC)){AG.ticks.push({v:AC,label:AA})}}}function P(AB,AA){if(AB.options.autoscaleMargin&&AA.length>0){if(AB.options.min==null){AB.min=Math.min(AB.min,AA[0].v)}if(AB.options.max==null&&AA.length>1){AB.max=Math.max(AB.max,AA[AA.length-1].v)}}}function L(){X.clearRect(0,0,H,w);var AB=R.grid;if(AB.show&&AB.backgroundColor){x()}if(AB.show&&!AB.aboveData){j()}for(var AA=0;AA<Aj.length;++AA){f(k.drawSeries,[X,Aj[AA]]);o(Aj[AA])}f(k.draw,[X]);if(AB.show&&AB.aboveData){j()}}function u(AC,AE){var AB,AA,AH,AG,AF=K();for(i=0;i<AF.length;++i){AB=AF[i];if(AB.direction==AE){AG=AE+AB.n+"axis";if(!AC[AG]&&AB.n==1){AG=AE+"axis"}if(AC[AG]){AA=AC[AG].from;AH=AC[AG].to;break}}}if(!AC[AG]){AB=AE=="x"?h[0]:Ap[0];AA=AC[AE+"1"];AH=AC[AE+"2"]}if(AA!=null&&AH!=null&&AA>AH){var AD=AA;AA=AH;AH=AD}return{from:AA,to:AH,axis:AB}}function x(){X.save();X.translate(n.left,n.top);X.fillStyle=O(R.grid.backgroundColor,As,0,"rgba(255, 255, 255, 0)");X.fillRect(0,0,Al,As);X.restore()}function j(){var AJ;X.save();X.translate(n.left,n.top);var AB=R.grid.markings;if(AB){if(C.isFunction(AB)){var AK=Ak.getAxes();AK.xmin=AK.xaxis.min;AK.xmax=AK.xaxis.max;AK.ymin=AK.yaxis.min;AK.ymax=AK.yaxis.max;AB=AB(AK)}for(AJ=0;AJ<AB.length;++AJ){var AL=AB[AJ],AO=u(AL,"x"),AD=u(AL,"y");if(AO.from==null){AO.from=AO.axis.min}if(AO.to==null){AO.to=AO.axis.max}if(AD.from==null){AD.from=AD.axis.min}if(AD.to==null){AD.to=AD.axis.max}if(AO.to<AO.axis.min||AO.from>AO.axis.max||AD.to<AD.axis.min||AD.from>AD.axis.max){continue}AO.from=Math.max(AO.from,AO.axis.min);AO.to=Math.min(AO.to,AO.axis.max);AD.from=Math.max(AD.from,AD.axis.min);AD.to=Math.min(AD.to,AD.axis.max);if(AO.from==AO.to&&AD.from==AD.to){continue}AO.from=AO.axis.p2c(AO.from);AO.to=AO.axis.p2c(AO.to);AD.from=AD.axis.p2c(AD.from);AD.to=AD.axis.p2c(AD.to);if(AO.from==AO.to||AD.from==AD.to){X.beginPath();X.strokeStyle=AL.color||R.grid.markingsColor;X.lineWidth=AL.lineWidth||R.grid.markingsLineWidth;X.moveTo(AO.from,AD.from);X.lineTo(AO.to,AD.to);X.stroke()}else{X.fillStyle=AL.color||R.grid.markingsColor;X.fillRect(AO.from,AD.to,AO.to-AO.from,AD.from-AD.to)}}}var AK=K(),AN=R.grid.borderWidth;for(var AG=0;AG<AK.length;++AG){var AE=AK[AG],AI=AE.box,AH=AE.tickLength,AM,AP,AC,AA;if(!AE.show||AE.ticks.length==0){continue}X.strokeStyle=AE.options.tickColor||C.color.parse(AE.options.color).scale("a",0.22).toString();X.lineWidth=1;if(AE.direction=="x"){AM=0;if(AH=="full"){AP=(AE.position=="top"?0:As)}else{AP=AI.top-n.top+(AE.position=="top"?AI.height:0)}}else{AP=0;if(AH=="full"){AM=(AE.position=="left"?0:Al)}else{AM=AI.left-n.left+(AE.position=="left"?AI.width:0)}}if(!AE.innermost){X.beginPath();AC=AA=0;if(AE.direction=="x"){AC=Al}else{AA=As}if(X.lineWidth==1){AM=Math.floor(AM)+0.5;AP=Math.floor(AP)+0.5}X.moveTo(AM,AP);X.lineTo(AM+AC,AP+AA);X.stroke()}X.beginPath();for(AJ=0;AJ<AE.ticks.length;++AJ){var AF=AE.ticks[AJ].v;AC=AA=0;if(AF<AE.min||AF>AE.max||(AH=="full"&&AN>0&&(AF==AE.min||AF==AE.max))){continue}if(AE.direction=="x"){AM=AE.p2c(AF);AA=AH=="full"?-As:AH;if(AE.position=="top"){AA=-AA}}else{AP=AE.p2c(AF);AC=AH=="full"?-Al:AH;if(AE.position=="left"){AC=-AC}}if(X.lineWidth==1){if(AE.direction=="x"){AM=Math.floor(AM)+0.5}else{AP=Math.floor(AP)+0.5}}X.moveTo(AM,AP);X.lineTo(AM+AC,AP+AA)}X.stroke()}if(AN){X.lineWidth=AN;X.strokeStyle=R.grid.borderColor;X.strokeRect(-AN/2,-AN/2,Al+AN,As+AN)}X.restore()}function p(){Am.find(".tickLabels").remove();var AK=['<div class="tickLabels" style="font-size:smaller">'];var AH=K();for(var AC=0;AC<AH.length;++AC){var AA=AH[AC],AE=AA.box;if(!AA.show){continue}AK.push('<div class="'+AA.direction+"Axis "+AA.direction+AA.n+'Axis" style="color:'+AA.options.color+'">');for(var AI=0;AI<AA.ticks.length;++AI){var AF=AA.ticks[AI];if(!AF.label||AF.v<AA.min||AF.v>AA.max){continue}var AB={},AD;if(AA.direction=="x"){AD="center";AB.left=Math.round(n.left+AA.p2c(AF.v)-AA.labelWidth/2);if(AA.position=="bottom"){AB.top=AE.top+AE.padding}else{AB.bottom=w-(AE.top+AE.height-AE.padding)}}else{AB.top=Math.round(n.top+AA.p2c(AF.v)-AA.labelHeight/2);if(AA.position=="left"){AB.right=H-(AE.left+AE.width-AE.padding);AD="right"}else{AB.left=AE.left+AE.padding;AD="left"}}AB.width=AA.labelWidth;var AG=["position:absolute","text-align:"+AD];for(var AJ in AB){AG.push(AJ+":"+AB[AJ]+"px")}AK.push('<div class="tickLabel" style="'+AG.join(";")+'">'+AF.label+"</div>")}AK.push("</div>")}AK.push("</div>");Am.append(AK.join(""))}function o(AA){if(AA.lines.show){Aq(AA)}if(AA.bars.show){Y(AA)}if(AA.points.show){t(AA)}}function Aq(AE){function AF(AQ,AH,AI,AU,AJ){var AS=AQ.points,AN=AQ.pointsize,AP=null,AO=null;X.beginPath();for(var AT=AN;AT<AS.length;AT+=AN){var AR=AS[AT-AN],AK=AS[AT-AN+1],AL=AS[AT],AM=AS[AT+1];if(AR==null||AL==null){continue}if(AK<=AM&&AK<AJ.min){if(AM<AJ.min){continue}AR=(AJ.min-AK)/(AM-AK)*(AL-AR)+AR;AK=AJ.min}else{if(AM<=AK&&AM<AJ.min){if(AK<AJ.min){continue}AL=(AJ.min-AK)/(AM-AK)*(AL-AR)+AR;AM=AJ.min}}if(AK>=AM&&AK>AJ.max){if(AM>AJ.max){continue}AR=(AJ.max-AK)/(AM-AK)*(AL-AR)+AR;AK=AJ.max}else{if(AM>=AK&&AM>AJ.max){if(AK>AJ.max){continue}AL=(AJ.max-AK)/(AM-AK)*(AL-AR)+AR;AM=AJ.max}}if(AR<=AL&&AR<AU.min){if(AL<AU.min){continue}AK=(AU.min-AR)/(AL-AR)*(AM-AK)+AK;AR=AU.min}else{if(AL<=AR&&AL<AU.min){if(AR<AU.min){continue}AM=(AU.min-AR)/(AL-AR)*(AM-AK)+AK;AL=AU.min}}if(AR>=AL&&AR>AU.max){if(AL>AU.max){continue}AK=(AU.max-AR)/(AL-AR)*(AM-AK)+AK;AR=AU.max}else{if(AL>=AR&&AL>AU.max){if(AR>AU.max){continue}AM=(AU.max-AR)/(AL-AR)*(AM-AK)+AK;AL=AU.max}}if(AR!=AP||AK!=AO){X.moveTo(AU.p2c(AR)+AH,AJ.p2c(AK)+AI)}AP=AL;AO=AM;X.lineTo(AU.p2c(AL)+AH,AJ.p2c(AM)+AI)}X.stroke()}function AG(AN,AY,AH){var AP=AN.points,AI=AN.pointsize,AO=Math.min(Math.max(0,AH.min),AH.max),AK=0,AS,AL=false,AM=1,AX=0,AV=0;while(true){if(AI>0&&AK>AP.length+AI){break}AK+=AI;var AT=AP[AK-AI],AU=AP[AK-AI+AM],AW=AP[AK],AR=AP[AK+AM];if(AL){if(AI>0&&AT!=null&&AW==null){AV=AK;AI=-AI;AM=2;continue}if(AI<0&&AK==AX+AI){X.fill();AL=false;AI=-AI;AM=1;AK=AX=AV+AI;continue}}if(AT==null||AW==null){continue}if(AT<=AW&&AT<AY.min){if(AW<AY.min){continue}AU=(AY.min-AT)/(AW-AT)*(AR-AU)+AU;AT=AY.min}else{if(AW<=AT&&AW<AY.min){if(AT<AY.min){continue}AR=(AY.min-AT)/(AW-AT)*(AR-AU)+AU;AW=AY.min}}if(AT>=AW&&AT>AY.max){if(AW>AY.max){continue}AU=(AY.max-AT)/(AW-AT)*(AR-AU)+AU;AT=AY.max}else{if(AW>=AT&&AW>AY.max){if(AT>AY.max){continue}AR=(AY.max-AT)/(AW-AT)*(AR-AU)+AU;AW=AY.max}}if(!AL){X.beginPath();X.moveTo(AY.p2c(AT),AH.p2c(AO));AL=true}if(AU>=AH.max&&AR>=AH.max){X.lineTo(AY.p2c(AT),AH.p2c(AH.max));X.lineTo(AY.p2c(AW),AH.p2c(AH.max));continue}else{if(AU<=AH.min&&AR<=AH.min){X.lineTo(AY.p2c(AT),AH.p2c(AH.min));X.lineTo(AY.p2c(AW),AH.p2c(AH.min));continue}}var AQ=AT,AJ=AW;if(AU<=AR&&AU<AH.min&&AR>=AH.min){AT=(AH.min-AU)/(AR-AU)*(AW-AT)+AT;AU=AH.min}else{if(AR<=AU&&AR<AH.min&&AU>=AH.min){AW=(AH.min-AU)/(AR-AU)*(AW-AT)+AT;AR=AH.min}}if(AU>=AR&&AU>AH.max&&AR<=AH.max){AT=(AH.max-AU)/(AR-AU)*(AW-AT)+AT;AU=AH.max}else{if(AR>=AU&&AR>AH.max&&AU<=AH.max){AW=(AH.max-AU)/(AR-AU)*(AW-AT)+AT;AR=AH.max}}if(AT!=AQ){X.lineTo(AY.p2c(AQ),AH.p2c(AU))}X.lineTo(AY.p2c(AT),AH.p2c(AU));X.lineTo(AY.p2c(AW),AH.p2c(AR));if(AW!=AJ){X.lineTo(AY.p2c(AW),AH.p2c(AR));X.lineTo(AY.p2c(AJ),AH.p2c(AR))}}}X.save();X.translate(n.left,n.top);X.lineJoin="round";var AC=AE.lines.lineWidth,AA=AE.shadowSize;if(AC>0&&AA>0){X.lineWidth=AA;X.strokeStyle="rgba(0,0,0,0.1)";var AD=Math.PI/18;AF(AE.datapoints,Math.sin(AD)*(AC/2+AA/2),Math.cos(AD)*(AC/2+AA/2),AE.xaxis,AE.yaxis);X.lineWidth=AA/2;AF(AE.datapoints,Math.sin(AD)*(AC/2+AA/4),Math.cos(AD)*(AC/2+AA/4),AE.xaxis,AE.yaxis)}X.lineWidth=AC;X.strokeStyle=AE.color;var AB=W(AE.lines,AE.color,0,As);if(AB){X.fillStyle=AB;AG(AE.datapoints,AE.xaxis,AE.yaxis)}if(AC>0){AF(AE.datapoints,0,0,AE.xaxis,AE.yaxis)}X.restore()}function t(AE){function AC(AQ,AO,AI,AJ,AK,AT,AH,AN){var AP=AQ.points,AL=AQ.pointsize;for(var AM=0;AM<AP.length;AM+=AL){var AR=AP[AM],AS=AP[AM+1];if(AR==null||AR<AT.min||AR>AT.max||AS<AH.min||AS>AH.max){continue}X.beginPath();AR=AT.p2c(AR);AS=AH.p2c(AS)+AJ;if(AN=="circle"){X.arc(AR,AS,AO,0,AK?Math.PI:Math.PI*2,false)}else{AN(X,AR,AS,AO,AK)}X.closePath();if(AI){X.fillStyle=AI;X.fill()}X.stroke()}}X.save();X.translate(n.left,n.top);var AB=AE.points.lineWidth,AA=AE.shadowSize,AD=AE.points.radius,AG=AE.points.symbol;if(AB>0&&AA>0){var AF=AA/2;X.lineWidth=AF;X.strokeStyle="rgba(0,0,0,0.1)";AC(AE.datapoints,AD,null,AF+AF/2,true,AE.xaxis,AE.yaxis,AG);X.strokeStyle="rgba(0,0,0,0.2)";AC(AE.datapoints,AD,null,AF/2,true,AE.xaxis,AE.yaxis,AG)}X.lineWidth=AB;X.strokeStyle=AE.color;AC(AE.datapoints,AD,W(AE.points,AE.color),0,false,AE.xaxis,AE.yaxis,AG);X.restore()}function N(AR,AS,AF,AQ,AK,AL,AH,AU,AA,AG,AT,AO){var AP,AC,AJ,AN,AM,AE,AI,AD,AB;if(AT){AD=AE=AI=true;AM=false;AP=AF;AC=AR;AN=AS+AQ;AJ=AS+AK;if(AC<AP){AB=AC;AC=AP;AP=AB;AM=true;AE=false}}else{AM=AE=AI=true;AD=false;AP=AR+AQ;AC=AR+AK;AJ=AF;AN=AS;if(AN<AJ){AB=AN;AN=AJ;AJ=AB;AD=true;AI=false}}if(AC<AU.min||AP>AU.max||AN<AA.min||AJ>AA.max){return}if(AP<AU.min){AP=AU.min;AM=false}if(AC>AU.max){AC=AU.max;AE=false}if(AJ<AA.min){AJ=AA.min;AD=false}if(AN>AA.max){AN=AA.max;AI=false}AP=AU.p2c(AP);AJ=AA.p2c(AJ);AC=AU.p2c(AC);AN=AA.p2c(AN);if(AH){AG.beginPath();AG.moveTo(AP,AJ);AG.lineTo(AP,AN);AG.lineTo(AC,AN);AG.lineTo(AC,AJ);AG.fillStyle=AH(AJ,AN);AG.fill()}if(AO>0&&(AM||AE||AI||AD)){AG.beginPath();AG.moveTo(AP,AJ+AL);if(AM){AG.lineTo(AP,AN+AL)}else{AG.moveTo(AP,AN+AL)}if(AI){AG.lineTo(AC,AN+AL)}else{AG.moveTo(AC,AN+AL)}if(AE){AG.lineTo(AC,AJ+AL)}else{AG.moveTo(AC,AJ+AL)}if(AD){AG.lineTo(AP,AJ+AL)}else{AG.moveTo(AP,AJ+AL)}AG.stroke()}}function Y(AC){function AD(AL,AF,AH,AG,AI,AN,AE){var AM=AL.points,AJ=AL.pointsize;for(var AK=0;AK<AM.length;AK+=AJ){if(AM[AK]==null){continue}N(AM[AK],AM[AK+1],AM[AK+2],AF,AH,AG,AI,AN,AE,X,AC.bars.horizontal,AC.bars.lineWidth)}}X.save();X.translate(n.left,n.top);X.lineWidth=AC.bars.lineWidth;X.strokeStyle=AC.color;var AA=AC.bars.align=="left"?0:-AC.bars.barWidth/2;var AB=AC.bars.fill?function(AF,AE){return W(AC.bars,AC.color,AF,AE)}:null;AD(AC.datapoints,AA,AA+AC.bars.barWidth,0,AB,AC.xaxis,AC.yaxis);X.restore()}function W(AB,AC,AF,AD){var AA=AB.fill;if(!AA){return null}if(AB.fillColor){return O(AB.fillColor,AF,AD,AC)}var AE=C.color.parse(AC);AE.a=typeof AA=="number"?AA:0.4;AE.normalize();return AE.toString()}function At(){Am.find(".legend").remove();if(!R.legend.show){return}var AI=[],AA=false,AK=R.legend.labelFormatter,AC,AH;for(var AF=0;AF<Aj.length;++AF){AC=Aj[AF];AH=AC.label;if(!AH){continue}if(AF%R.legend.noColumns==0){if(AA){AI.push("</tr>")}AI.push("<tr>");AA=true}if(AK){AH=AK(AH,AC)}AI.push('<td class="legendColorBox"><div style="border:1px solid '+R.legend.labelBoxBorderColor+';padding:1px"><div style="width:4px;height:0;border:5px solid '+AC.color+';overflow:hidden"></div></div></td><td class="legendLabel">'+AH+"</td>")}if(AA){AI.push("</tr>")}if(AI.length==0){return}var AM='<table style="font-size:smaller;color:'+R.grid.color+'">'+AI.join("")+"</table>";if(R.legend.container!=null){C(R.legend.container).html(AM)}else{var AG="",AB=R.legend.position,AL=R.legend.margin;if(AL[0]==null){AL=[AL,AL]}if(AB.charAt(0)=="n"){AG+="top:"+(AL[1]+n.top)+"px;"}else{if(AB.charAt(0)=="s"){AG+="bottom:"+(AL[1]+n.bottom)+"px;"}}if(AB.charAt(1)=="e"){AG+="right:"+(AL[0]+n.right)+"px;"}else{if(AB.charAt(1)=="w"){AG+="left:"+(AL[0]+n.left)+"px;"}}var AE=C('<div class="legend">'+AM.replace('style="','style="position:absolute;'+AG+";")+"</div>").appendTo(Am);if(R.legend.backgroundOpacity!=0){var AJ=R.legend.backgroundColor;if(AJ==null){AJ=R.grid.backgroundColor;if(AJ&&typeof AJ=="string"){AJ=C.color.parse(AJ)}else{AJ=C.color.extract(AE,"background-color")}AJ.a=1;AJ=AJ.toString()}var AD=AE.children();C('<div style="position:absolute;width:'+AD.width()+"px;height:"+AD.height()+"px;"+AG+"background-color:"+AJ+';"> </div>').prependTo(AE).css("opacity",R.legend.backgroundOpacity)}}}var r=[],Ab=null;function Az(AK,AV,AC){var AR=R.grid.mouseActiveRadius,AD=AR*AR+1,AE=null,AP=false,AH,AG;for(AH=Aj.length-1;AH>=0;--AH){if(!AC(Aj[AH])){continue}var AQ=Aj[AH],AZ=AQ.xaxis,AA=AQ.yaxis,AM=AQ.datapoints.points,AB=AQ.datapoints.pointsize,AF=AZ.c2p(AK),AO=AA.c2p(AV),AX=AR/AZ.scale,AL=AR/AA.scale;if(AZ.options.inverseTransform){AX=Number.MAX_VALUE}if(AA.options.inverseTransform){AL=Number.MAX_VALUE}if(AQ.lines.show||AQ.points.show){for(AG=0;AG<AM.length;AG+=AB){var AW=AM[AG],AT=AM[AG+1];if(AW==null){continue}if(AW-AF>AX||AW-AF<-AX||AT-AO>AL||AT-AO<-AL){continue}var AI=Math.abs(AZ.p2c(AW)-AK),AU=Math.abs(AA.p2c(AT)-AV),AY=AI*AI+AU*AU;if(AY<AD){AD=AY;AE=[AH,AG/AB]}}}if(AQ.bars.show&&!AE){var AS=AQ.bars.align=="left"?0:-AQ.bars.barWidth/2,AN=AS+AQ.bars.barWidth;for(AG=0;AG<AM.length;AG+=AB){var AW=AM[AG],AT=AM[AG+1],AJ=AM[AG+2];if(AW==null){continue}if(Aj[AH].bars.horizontal?(AF<=Math.max(AJ,AW)&&AF>=Math.min(AJ,AW)&&AO>=AT+AS&&AO<=AT+AN):(AF>=AW+AS&&AF<=AW+AN&&AO>=Math.min(AJ,AT)&&AO<=Math.max(AJ,AT))){AE=[AH,AG/AB]}}}}if(AE){AH=AE[0];AG=AE[1];AB=Aj[AH].datapoints.pointsize;return{datapoint:Aj[AH].datapoints.points.slice(AG*AB,(AG+1)*AB),dataIndex:AG,series:Aj[AH],seriesIndex:AH}}return null}function Af(AA){if(R.grid.hoverable){Ah("plothover",AA,function(AB){return AB["hoverable"]!=false})}}function v(AA){if(R.grid.hoverable){Ah("plothover",AA,function(AB){return false})}}function Ai(AA){Ah("plotclick",AA,function(AB){return AB["clickable"]!=false})}function Ah(AH,AC,AB){var AA=Ax.offset(),AD=AC.pageX-AA.left-n.left,AE=AC.pageY-AA.top-n.top,AG=Ao({left:AD,top:AE});AG.pageX=AC.pageX;AG.pageY=AC.pageY;var AJ=Az(AD,AE,AB);if(AJ){AJ.pageX=parseInt(AJ.series.xaxis.p2c(AJ.datapoint[0])+AA.left+n.left);AJ.pageY=parseInt(AJ.series.yaxis.p2c(AJ.datapoint[1])+AA.top+n.top)}if(R.grid.autoHighlight){for(var AI=0;AI<r.length;++AI){var AF=r[AI];if(AF.auto==AH&&!(AJ&&AF.series==AJ.series&&AF.point[0]==AJ.datapoint[0]&&AF.point[1]==AJ.datapoint[1])){a(AF.series,AF.point)}}if(AJ){G(AJ.series,AJ.datapoint,AH)}}Am.trigger(AH,[AG,AJ])}function U(){if(!Ab){Ab=setTimeout(q,30)}}function q(){Ab=null;S.save();S.clearRect(0,0,H,w);S.translate(n.left,n.top);var AA,AB;for(AA=0;AA<r.length;++AA){AB=r[AA];if(AB.series.bars.show){Av(AB.series,AB.point)}else{V(AB.series,AB.point)}}S.restore();f(k.drawOverlay,[S])}function G(AB,AC,AE){if(typeof AB=="number"){AB=Aj[AB]}if(typeof AC=="number"){var AD=AB.datapoints.pointsize;AC=AB.datapoints.points.slice(AD*AC,AD*(AC+1))}var AA=Au(AB,AC);if(AA==-1){r.push({series:AB,point:AC,auto:AE});U()}else{if(!AE){r[AA].auto=false}}}function a(AB,AC){if(AB==null&&AC==null){r=[];U()}if(typeof AB=="number"){AB=Aj[AB]}if(typeof AC=="number"){AC=AB.data[AC]}var AA=Au(AB,AC);if(AA!=-1){r.splice(AA,1);U()}}function Au(AC,AD){for(var AB=0;AB<r.length;++AB){var AA=r[AB];if(AA.series==AC&&AA.point[0]==AD[0]&&AA.point[1]==AD[1]){return AB}}return -1}function V(AD,AF){var AG=AF[0],AE=AF[1],AH=AD.xaxis,AA=AD.yaxis;if(AG<AH.min||AG>AH.max||AE<AA.min||AE>AA.max){return}var AB=AD.points.radius+AD.points.lineWidth/2;S.lineWidth=AB;S.strokeStyle=C.color.parse(AD.color).scale("a",0.5).toString();var AC=1.5*AB,AG=AH.p2c(AG),AE=AA.p2c(AE);S.beginPath();if(AD.points.symbol=="circle"){S.arc(AG,AE,AC,0,2*Math.PI,false)}else{AD.points.symbol(S,AG,AE,AC,false)}S.closePath();S.stroke()}function Av(AB,AA){S.lineWidth=AB.bars.lineWidth;S.strokeStyle=C.color.parse(AB.color).scale("a",0.5).toString();var AD=C.color.parse(AB.color).scale("a",0.5).toString();var AC=AB.bars.align=="left"?0:-AB.bars.barWidth/2;N(AA[0],AA[1],AA[2]||0,AC,AC+AB.bars.barWidth,0,function(){return AD},AB.xaxis,AB.yaxis,S,AB.bars.horizontal,AB.bars.lineWidth)}function O(AB,AG,AC,AI){if(typeof AB=="string"){return AB}else{var AE=X.createLinearGradient(0,AC,0,AG);for(var AD=0,AF=AB.colors.length;AD<AF;++AD){var AH=AB.colors[AD];if(typeof AH!="string"){var AA=C.color.parse(AI);if(AH.brightness!=null){AA=AA.scale("rgb",AH.brightness)}if(AH.opacity!=null){AA.a*=AH.opacity}AH=AA.toString()}AE.addColorStop(AD/(AF-1),AH)}return AE}}}C.plot=function(E,G,D){var F=new A(C(E),G,D,C.plot.plugins);return F};C.plot.version="0.7";C.plot.plugins=[];C.plot.formatDate=function(F,I,N){var E=function(O){O=""+O;return O.length==1?"0"+O:O};var D=[];var G=false,H=false;var L=F.getUTCHours();var M=L<12;if(N==null){N=["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"]}if(I.search(/%p|%P/)!=-1){if(L>12){L=L-12}else{if(L==0){L=12}}}for(var J=0;J<I.length;++J){var K=I.charAt(J);if(G){switch(K){case"h":K=""+L;break;case"H":K=E(L);break;case"M":K=E(F.getUTCMinutes());break;case"S":K=E(F.getUTCSeconds());break;case"d":K=""+F.getUTCDate();break;case"m":K=""+(F.getUTCMonth()+1);break;case"y":K=""+F.getUTCFullYear();break;case"b":K=""+N[F.getUTCMonth()];break;case"p":K=(M)?("am"):("pm");break;case"P":K=(M)?("AM"):("PM");break;case"0":K="";H=true;break}if(K&&H){K=E(K);H=false}D.push(K);if(!H){G=false}}else{if(K=="%"){G=true}else{D.push(K)}}}return D.join("")};function B(D,E){return E*Math.floor(D/E)}})(jQuery);