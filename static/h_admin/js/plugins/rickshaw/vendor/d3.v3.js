d3=function(){function E9(A){return null!=A&&!isNaN(A)}function E4(A){return A.length}function Dv(A){for(var B=1;A*B%1;){B*=10}return B}function E1(B,D){try{for(var C in D){Object.defineProperty(B.prototype,C,{value:D[C],enumerable:!1})}}catch(A){B.prototype=D}}function E5(){}function E8(){}function Fb(A,C,B){return function(){var D=B.apply(C,arguments);return D===C?A:D}}function Fh(B,D){if(D in B){return D}D=D.charAt(0).toUpperCase()+D.substring(1);for(var C=0,A=Dz.length;A>C;++C){var E=Dz[C]+D;if(E in B){return E}}}function Fg(){}function Dh(){}function C1(B){function D(){for(var G,E=C,H=-1,F=E.length;++H<F;){(G=E[H].on)&&G.apply(this,arguments)}return B}var C=[],A=new E5;return D.on=function(G,H){var E,F=A.get(G);return arguments.length<2?F&&F.on:(F&&(F.on=null,C=C.slice(0,E=C.indexOf(F)).concat(C.slice(E+1)),A.remove(G)),H&&C.push(A.set(G,{on:H})),B)},D}function Fj(){DT.event.preventDefault()}function E7(){for(var A,B=DT.event;A=B.sourceEvent;){B=A}return B}function Fk(B){for(var D=new Dh,C=0,A=arguments.length;++C<A;){D[arguments[C]]=C1(D)}return D.of=function(F,E){return function(H){try{var G=H.sourceEvent=DT.event;H.target=B,DT.event=H,D[H.type].apply(F,E)}finally{DT.event=G}}},D}function E2(A){return Dn(A,C5),A}function Fl(A){return"function"==typeof A?A:function(){return Dj(A,this)}}function C4(A){return"function"==typeof A?A:function(){return Dx(A,this)}}function Fd(F,C){function A(){this.removeAttribute(F)}function B(){this.removeAttributeNS(F.space,F.local)}function D(){this.setAttribute(F,C)}function E(){this.setAttributeNS(F.space,F.local,C)}function G(){var I=C.apply(this,arguments);null==I?this.removeAttribute(F):this.setAttribute(F,I)}function H(){var I=C.apply(this,arguments);null==I?this.removeAttributeNS(F.space,F.local):this.setAttributeNS(F.space,F.local,I)}return F=DT.ns.qualify(F),null==C?F.local?B:A:"function"==typeof C?F.local?H:G:F.local?E:D}function CX(A){return A.trim().replace(/\s+/g," ")}function CN(A){return new RegExp("(?:^|\\s+)"+DT.requote(A)+"(?:\\s+|$)","g")}function CW(B,D){function C(){for(var F=-1;++F<E;){B[F](this,D)}}function A(){for(var G=-1,F=D.apply(this,arguments);++G<E;){B[G](this,F)}}B=B.trim().split(/\s+/).map(Ff);var E=B.length;return"function"==typeof D?A:C}function Ff(A){var B=CN(A);return function(D,C){if(E=D.classList){return C?E.add(A):E.remove(A)}var E=D.getAttribute("class")||"";C?(B.lastIndex=0,B.test(E)||D.setAttribute("class",CX(E+" "+A))):D.setAttribute("class",CX(E.replace(B," ")))}}function Cx(B,E,D){function A(){this.style.removeProperty(B)}function F(){this.style.setProperty(B,E,D)}function C(){var G=E.apply(this,arguments);null==G?this.style.removeProperty(B):this.style.setProperty(B,G,D)}return null==E?A:"function"==typeof E?C:F}function E3(B,D){function C(){delete this[B]}function A(){this[B]=D}function E(){var F=D.apply(this,arguments);null==F?delete this[B]:this[B]=F}return null==D?C:"function"==typeof D?E:A}function Cz(A){return"function"==typeof A?A:(A=DT.ns.qualify(A)).local?function(){return DC.createElementNS(A.space,A.local)}:function(){return DC.createElementNS(this.namespaceURI,A)}}function CU(A){return{__data__:A}}function E6(A){return function(){return Dm(this,A)}}function CQ(A){return arguments.length||(A=DT.ascending),function(C,B){return C&&B?A(C.__data__,B.__data__):!C-!B}}function CK(F,C){for(var A=0,B=F.length;B>A;A++){for(var D,E=F[A],G=0,H=E.length;H>G;G++){(D=E[G])&&C(D,G,A)}}return F}function CE(A){return Dn(A,Dk),A}function C3(A){var C,B;return function(D,H,E){var F,I=A[E].update,G=I.length;for(E!=B&&(B=E,C=0),H>=C&&(C=H+1);!(F=I[C])&&++C<G;){}return F}}function CV(){var A=this.__transition__;A&&++A.active}function CP(G,D,A){function C(){var K=this[H];K&&(this.removeEventListener(G,K,K.$),delete this[H])}function E(){var K=I(D,Dp(arguments));C.call(this),this.addEventListener(G,this[H]=K,K.$=A),K._=D}function F(){var M,L=new RegExp("^__on([^.]+)"+DT.requote(G)+"$");for(var K in this){if(M=K.match(L)){var N=this[K];this.removeEventListener(M[1],N,N.$),delete this[K]}}}var H="__on"+G,J=G.indexOf("."),I=CT;J>0&&(G=G.substring(0,J));var B=CY.get(G);return B&&(G=B,I=C8),J?D?E:C:D?Fg:F}function CT(A,B){return function(D){var C=DT.event;DT.event=D,B[0]=this.__data__;try{A.apply(this,B)}finally{DT.event=C}}}function C8(A,C){var B=CT(A,C);return function(E){var F=this,D=E.relatedTarget;D&&(D===F||8&D.compareDocumentPosition(F))||B.call(F,E)}}function CM(){var F=".dragsuppress-"+ ++CZ,C="touchmove"+F,A="selectstart"+F,B="dragstart"+F,D="click"+F,E=DT.select(C2).on(C,Fj).on(A,Fj).on(B,Fj),G=DU.style,H=G[C9];return G[C9]="none",function(J){function I(){E.on(D,null)}E.on(F,null),G[C9]=H,J&&(E.on(D,function(){Fj(),I()},!0),setTimeout(I,0))}}function CI(B,E){E.changedTouches&&(E=E.changedTouches[0]);var D=B.ownerSVGElement||B;if(D.createSVGPoint){var A=D.createSVGPoint();if(0>EI&&(C2.scrollX||C2.scrollY)){D=DT.select("body").append("svg").style({position:"absolute",top:0,left:0,margin:0,padding:0,border:"none"},"important");var F=D[0][0].getScreenCTM();EI=!(F.f||F.e),D.remove()}return EI?(A.x=E.pageX,A.y=E.pageY):(A.x=E.clientX,A.y=E.clientY),A=A.matrixTransform(B.getScreenCTM().inverse()),[A.x,A.y]}var C=B.getBoundingClientRect();return[E.clientX-C.left-B.clientLeft,E.clientY-C.top-B.clientTop]}function CR(A){return A>0?1:0>A?-1:0}function CA(A){return A>1?0:-1>A?Dq:Math.acos(A)}function CL(A){return A>1?Dg:-1>A?-Dg:Math.asin(A)}function Cy(A){return((A=Math.exp(A))-1/A)/2}function Cw(A){return((A=Math.exp(A))+1/A)/2}function CJ(A){return((A=Math.exp(2*A))-1)/(A+1)}function CF(A){return(A=Math.sin(A/2))*A}function Cs(){}function CC(A,C,B){return new Cv(A,C,B)}function Cv(A,C,B){this.h=A,this.s=C,this.l=B}function D4(B,F,D){function A(H){return H>360?H-=360:0>H&&(H+=360),60>H?C+(E-C)*H/60:180>H?E:240>H?C+(E-C)*(240-H)/60:C}function G(H){return Math.round(255*A(H))}var C,E;return B=isNaN(B)?0:(B%=360)<0?B+360:B,F=isNaN(F)?0:0>F?0:F>1?1:F,D=0>D?0:D>1?1:D,E=0.5>=D?D*(1+F):D+F-D*F,C=2*D-E,H0(G(B+120),G(B),G(B-120))}function CO(A,C,B){return new CD(A,C,B)}function CD(A,C,B){this.h=A,this.c=C,this.l=B}function CG(A,C,B){return isNaN(A)&&(A=0),isNaN(C)&&(C=0),CS(B,Math.cos(A*=Fn)*C,Math.sin(A)*C)}function CS(A,C,B){return new CH(A,C,B)}function CH(A,C,B){this.l=A,this.a=C,this.b=B}function CB(B,E,D){var A=(B+16)/116,F=A+E/500,C=A-D/200;return F=HT(F)*GP,A=HT(A)*G7,C=HT(C)*GY,H0(HN(3.2404542*F-1.5371385*A-0.4985314*C),HN(-0.969266*F+1.8760108*A+0.041556*C),HN(0.0556434*F-0.2040259*A+1.0572252*C))}function HZ(A,C,B){return A>0?CO(Math.atan2(B,C)*G0,Math.sqrt(C*C+B*B),A):CO(0/0,0/0,A)}function HT(A){return A>0.206893034?A*A*A:(A-4/29)/7.787037}function Ib(A){return A>0.008856?Math.pow(A,1/3):7.787037*A+4/29}function HN(A){return Math.round(255*(0.00304>=A?12.92*A:1.055*Math.pow(A,1/2.4)-0.055))}function HU(A){return H0(A>>16,255&A>>8,255&A)}function HY(A){return HU(A)+""}function H0(A,C,B){return new H6(A,C,B)}function H6(A,C,B){this.r=A,this.g=C,this.b=B}function H4(A){return 16>A?"0"+Math.max(0,A).toString(16):Math.min(255,A).toString(16)}function H1(F,C,A){var B,D,E,G=0,I=0,H=0;if(B=/([a-z]+)\((.*)\)/i.exec(F)){switch(D=B[2].split(","),B[1]){case"hsl":return A(parseFloat(D[0]),parseFloat(D[1])/100,parseFloat(D[2])/100);case"rgb":return C(H8(D[0]),H8(D[1]),H8(D[2]))}}return(E=G9.get(F))?C(E.r,E.g,E.b):(null!=F&&"#"===F.charAt(0)&&(4===F.length?(G=F.charAt(1),G+=G,I=F.charAt(2),I+=I,H=F.charAt(3),H+=H):7===F.length&&(G=F.substring(1,3),I=F.substring(3,5),H=F.substring(5,7)),G=parseInt(G,16),I=parseInt(I,16),H=parseInt(H,16)),C(G,I,H))}function HO(F,C,A){var B,D,E=Math.min(F/=255,C/=255,A/=255),G=Math.max(F,C,A),I=G-E,H=(G+E)/2;return I?(D=0.5>H?I/(G+E):I/(2-G-E),B=F==G?(C-A)/I+(A>C?6:0):C==G?(A-F)/I+2:(F-C)/I+4,B*=60):(B=0/0,D=H>0&&1>H?0:B),CC(B,D,H)}function H7(B,E,D){B=HX(B),E=HX(E),D=HX(D);var A=Ib((0.4124564*B+0.3575761*E+0.1804375*D)/GP),F=Ib((0.2126729*B+0.7151522*E+0.072175*D)/G7),C=Ib((0.0193339*B+0.119192*E+0.9503041*D)/GY);return CS(116*F-16,500*(A-F),200*(F-C))}function HX(A){return(A/=255)<=0.04045?A/12.92:Math.pow((A+0.055)/1.055,2.4)}function H8(A){var B=parseFloat(A);return"%"===A.charAt(A.length-1)?Math.round(2.55*B):B}function HP(A){return"function"==typeof A?A:function(){return A}}function H9(A){return A}function HR(A){return function(D,C,B){return 2===arguments.length&&"function"==typeof C&&(B=C,C=null),H2(D,C,A,B)}}function H2(F,C,A,B){function D(){var L,M=I.status;if(!M&&I.responseText||M>=200&&300>M||304===M){try{L=A.call(E,I)}catch(K){return G.error.call(E,K),void 0}G.load.call(E,L)}else{G.error.call(E,I)}}var E={},G=DT.dispatch("beforesend","progress","load","error"),J={},I=new XMLHttpRequest,H=null;return !C2.XDomainRequest||"withCredentials" in I||!/^(http(s)?:)?\/\//.test(F)||(I=new XDomainRequest),"onload" in I?I.onload=I.onerror=D:I.onreadystatechange=function(){I.readyState>3&&D()},I.onprogress=function(K){var L=DT.event;DT.event=K;try{G.progress.call(E,I)}finally{DT.event=L}},E.header=function(K,L){return K=(K+"").toLowerCase(),arguments.length<2?J[K]:(null==L?delete J[K]:J[K]=L+"",E)},E.mimeType=function(K){return arguments.length?(C=null==K?null:K+"",E):C},E.responseType=function(K){return arguments.length?(H=K,E):H},E.response=function(K){return A=K,E},["get","post"].forEach(function(K){E[K]=function(){return E.send.apply(E,[K].concat(Dp(arguments)))}}),E.send=function(L,K,N){if(2===arguments.length&&"function"==typeof K&&(N=K,K=null),I.open(L,F,!0),null==C||"accept" in J||(J.accept=C+",*/*"),I.setRequestHeader){for(var M in J){I.setRequestHeader(M,J[M])}}return null!=C&&I.overrideMimeType&&I.overrideMimeType(C),null!=H&&(I.responseType=H),null!=N&&E.on("error",N).on("load",function(O){N(null,O)}),G.beforesend.call(E,I),I.send(null==K?null:K),E},E.abort=function(){return I.abort(),E},DT.rebind(E,G,"on"),null==B?E:E.get(HM(B))}function HM(A){return 1===A.length?function(C,B){A(null==C?B:null)}:A}function HC(){var A=H3(),B=Hh()-A;B>24?(isFinite(B)&&(clearTimeout(GC),GC=setTimeout(HC,B)),GN=0):(GN=1,G4(HC))}function HL(B,D,C){var A=arguments.length;2>A&&(D=0),3>A&&(C=Date.now()),GM.callback=B,GM.time=C+D}function H3(){var A=Date.now();for(GM=GS;GM;){A>=GM.time&&(GM.flush=GM.callback(A-GM.time)),GM=GM.next}return A}function Hh(){for(var A,C=GS,B=1/0;C;){C.flush?C=A?A.next=C.next:GS=C.next:(C.time<B&&(B=C.time),C=(A=C).next)}return G3=A,B}function HS(A,C){var B=Math.pow(10,3*Math.abs(8-C));return{scale:C>8?function(D){return D/B}:function(D){return D*B},symbol:A}}function Hk(A,B){return B-(A?Math.ceil(Math.log(A)/Math.LN10):1)}function HJ(A){return A+""}function HW(){}function HF(B,E,D){var A=D.s=B+E,F=A-B,C=A-F;D.t=B-C+(E-F)}function Hz(A,B){A&&GJ.hasOwnProperty(A.type)&&GJ[A.type](A,B)}function Hq(B,E,D){var A,F=-1,C=B.length-D;for(E.lineStart();++F<C;){A=B[F],E.point(A[0],A[1],A[2])}E.lineEnd()}function HQ(B,D){var C=-1,A=B.length;for(D.polygonStart();++C<A;){Hq(B[C],D,1)}D.polygonEnd()}function HK(){function B(J,I){J*=Fn,I=I*Fn/2+Dq/4;var G=J-A,K=Math.cos(I),N=Math.sin(I),M=C*N,L=F*K+M*Math.cos(G),H=M*Math.sin(G);Fv.add(Math.atan2(H,L)),A=J,F=K,C=N}var E,D,A,F,C;Bb.point=function(G,H){Bb.point=B,A=(E=G)*Fn,F=Math.cos(H=(D=H)*Fn/2+Dq/4),C=Math.sin(H)},Bb.lineEnd=function(){B(E,D)}}function HE(B){var D=B[0],C=B[1],A=Math.cos(C);return[A*Math.cos(D),A*Math.sin(D),Math.sin(C)]}function HI(A,B){return A[0]*B[0]+A[1]*B[1]+A[2]*B[2]}function HV(A,B){return[A[1]*B[2]-A[2]*B[1],A[2]*B[0]-A[0]*B[2],A[0]*B[1]-A[1]*B[0]]}function HB(A,B){A[0]+=B[0],A[1]+=B[1],A[2]+=B[2]}function Hx(A,B){return[A[0]*B,A[1]*B,A[2]*B]}function HG(A){var B=Math.sqrt(A[0]*A[0]+A[1]*A[1]+A[2]*A[2]);A[0]/=B,A[1]/=B,A[2]/=B}function Hl(A){return[Math.atan2(A[1],A[0]),CL(A[2])]}function HA(A,B){return Math.abs(A[0]-B[0])<Dy&&Math.abs(A[1]-B[1])<Dy}function Hj(A,C){A*=Fn;var B=Math.cos(C*=Fn);Hg(B*Math.cos(A),B*Math.sin(A),Math.sin(C))}function Hg(A,C,B){++Bn,F7+=(A-F7)/Bn,Fx+=(C-Fx)/Bn,Gd+=(B-Gd)/Bn}function Hy(){function B(F,J){F*=Fn;var E=Math.cos(J*=Fn),G=E*Math.cos(F),K=E*Math.sin(F),I=Math.sin(J),H=Math.atan2(Math.sqrt((H=C*I-A*K)*H+(H=A*G-D*I)*H+(H=D*K-C*G)*H),D*G+C*K+A*I);Fz+=H,FG+=H*(D+(D=G)),Fq+=H*(C+(C=K)),Fs+=H*(A+(A=I)),Hg(D,C,A)}var D,C,A;Bk.point=function(G,E){G*=Fn;var F=Math.cos(E*=Fn);D=F*Math.cos(G),C=F*Math.sin(G),A=Math.sin(E),Bk.point=B,Hg(D,C,A)}}function Hs(){Bk.point=Hj}function Hd(){function B(L,J){L*=Fn;var G=Math.cos(J*=Fn),M=G*Math.cos(L),P=G*Math.sin(L),O=Math.sin(J),N=F*O-C*P,H=C*M-A*O,Q=A*P-F*M,K=Math.sqrt(N*N+H*H+Q*Q),R=A*M+F*P+C*O,I=K&&-CA(R)/K,S=Math.atan2(K,R);FA+=I*N,Gb+=I*H,Cn+=I*Q,Fz+=S,FG+=S*(A+(A=M)),Fq+=S*(F+(F=P)),Fs+=S*(C+(C=O)),Hg(A,F,C)}var E,D,A,F,C;Bk.point=function(G,I){E=G,D=I,Bk.point=B,G*=Fn;var H=Math.cos(I*=Fn);A=H*Math.cos(G),F=H*Math.sin(G),C=Math.sin(I),Hg(A,F,C)},Bk.lineEnd=function(){B(E,D),Bk.lineEnd=Hs,Bk.point=Hj}}function Hn(){return !0}function Hf(H,D,A,C,E){var G=[],I=[];if(H.forEach(function(Q){if(!((T=Q.length-1)<=0)){var T,R=Q[0],P=Q[T];if(HA(R,P)){E.lineStart();for(var U=0;T>U;++U){E.point((R=Q[U])[0],R[1])}return E.lineEnd(),void 0}var O={point:R,points:Q,other:null,visited:!1,entry:!0,subject:!0},S={point:R,points:[R],other:O,visited:!1,entry:!1,subject:!1};O.other=S,G.push(O),I.push(S),O={point:P,points:[P],other:null,visited:!1,entry:!1,subject:!0},S={point:P,points:[P],other:O,visited:!1,entry:!0,subject:!1},O.other=S,G.push(O),I.push(S)}}),I.sort(D),IH(G),IH(I),G.length){for(var L=0,K=A,J=I.length;J>L;++L){I[L].entry=K=!K}for(var B,M,F,N=G[0];;){for(B=N;B.visited;){if((B=B.next)===N){return}}M=B.points,E.lineStart();do{if(B.visited=B.other.visited=!0,B.entry){if(B.subject){for(var L=0;L<M.length;L++){E.point((F=M[L])[0],F[1])}}else{C(B.point,B.next.point,1,E)}B=B.next}else{if(B.subject){M=B.prev.points;for(var L=M.length;--L>=0;){E.point((F=M[L])[0],F[1])}}else{C(B.point,B.prev.point,-1,E)}B=B.prev}B=B.other,M=B.points}while(!B.visited);E.lineEnd()}}}function IH(B){if(D=B.length){for(var D,C,A=0,E=B[0];++A<D;){E.next=C=B[A],C.prev=E,E=C}E.next=C=B[0],C.prev=E}}function HD(B,D,C,A){return function(H,J){function K(X,W){var M=H(X,W);B(X=M[0],W=M[1])&&J.point(X,W)}function P(M,X){var W=H(M,X);G.point(W[0],W[1])}function O(){V.point=P,G.lineStart()}function L(){V.point=K,G.lineEnd()}function E(M,X){S.push([M,X]);var W=H(M,X);U.point(W[0],W[1])}function Q(){U.lineStart(),S=[]}function I(){E(S[0][0],S[0][1]),U.lineEnd();var W,Z=U.clean(),Y=T.buffer(),M=Y.length;if(S.pop(),F.push(S),S=null,M){if(1&Z){W=Y[0];var a,M=W.length-1,X=-1;for(J.lineStart();++X<M;){J.point((a=W[X])[0],a[1])}return J.lineEnd(),void 0}M>1&&2&Z&&Y.push(Y.pop().concat(Y.shift())),R.push(Y.filter(Hp))}}var R,F,S,G=D(J),N=H.invert(A[0],A[1]),V={point:K,lineStart:O,lineEnd:L,polygonStart:function(){V.point=E,V.lineStart=Q,V.lineEnd=I,R=[],F=[],J.polygonStart()},polygonEnd:function(){V.point=K,V.lineStart=O,V.lineEnd=L,R=DT.merge(R);var M=Hw(N,F);R.length?Hf(R,HH,M,C,J):M&&(J.lineStart(),C(null,null,1,J),J.lineEnd()),J.polygonEnd(),R=F=null},sphere:function(){J.polygonStart(),J.lineStart(),C(null,null,1,J),J.lineEnd(),J.polygonEnd()}},T=Hv(),U=D(T);return V}}function Hp(A){return A.length>1}function Hv(){var A,B=[];return{lineStart:function(){B.push(A=[])},point:function(D,C){A.push([D,C])},lineEnd:Fg,buffer:function(){var C=B;return B=[],A=null,C},rejoin:function(){B.length>1&&B.push(B.pop().concat(B.shift()))}}}function HH(A,B){return((A=A.point)[0]<0?A[1]-Dg-Dy:Dg-A[1])-((B=B.point)[0]<0?B[1]-Dg-Dy:Dg-B[1])}function Hw(L,G){var X=L[0],A=L[1],H=[Math.sin(X),-Math.cos(X),0],K=0,N=0;Fv.reset();for(var T=0,R=G.length;R>T;++T){var O=G[T],B=O.length;if(B){for(var U=O[0],J=U[0],V=U[1]/2+Dq/4,C=Math.sin(V),W=Math.cos(V),D=1;;){D===B&&(D=0),L=O[D];var P=L[0],z=L[1]/2+Dq/4,Y=Math.sin(z),q=Math.cos(z),Q=P-J,j=Math.abs(Q)>Dq,F=C*Y;if(Fv.add(Math.atan2(F*Math.sin(Q),W*q+F*Math.cos(Q))),K+=j?Q+(Q>=0?2:-2)*Dq:Q,j^J>=X^P>=X){var Aa=HV(HE(U),HE(L));HG(Aa);var Z=HV(H,Aa);HG(Z);var I=(j^Q>=0?-1:1)*CL(Z[2]);(A>I||A===I&&(Aa[0]||Aa[1]))&&(N+=j^Q>=0?1:-1)}if(!D++){break}J=P,C=Y,W=q,U=L}}}return(-Dy>K||Dy>K&&0>Fv)^1&N}function Hm(B){var D,C=0/0,A=0/0,E=0/0;return{lineStart:function(){B.lineStart(),D=1},point:function(F,G){var I=F>0?Dq:-Dq,H=Math.abs(F-C);Math.abs(H-Dq)<Dy?(B.point(C,A=(A+G)/2>0?Dg:-Dg),B.point(E,A),B.lineEnd(),B.lineStart(),B.point(I,A),B.point(F,A),D=0):E!==I&&H>=Dq&&(Math.abs(C-E)<Dy&&(C-=E*Dy),Math.abs(F-I)<Dy&&(F-=I*Dy),A=B8(C,A,F,G),B.point(E,A),B.lineEnd(),B.lineStart(),B.point(I,A),D=0),B.point(C=F,A=G),E=I},lineEnd:function(){B.lineEnd(),C=A=0/0},clean:function(){return 2-D}}}function B8(B,F,D,A){var G,C,E=Math.sin(B-D);return Math.abs(E)>Dy?Math.atan((Math.sin(F)*(C=Math.cos(A))*Math.sin(D)-Math.sin(A)*(G=Math.cos(F))*Math.sin(B))/(G*C*E)):(F+A)/2}function B2(B,E,D,A){var F;if(null==B){F=D*Dg,A.point(-Dq,F),A.point(0,F),A.point(Dq,F),A.point(Dq,0),A.point(Dq,-F),A.point(0,-F),A.point(-Dq,-F),A.point(-Dq,0),A.point(-Dq,F)}else{if(Math.abs(B[0]-E[0])>Dy){var C=(B[0]<E[0]?1:-1)*Dq;F=D*C/2,A.point(-C,F),A.point(0,F),A.point(C,F)}else{A.point(E[0],E[1])}}}function Cm(F){function C(J,K){return Math.cos(J)*Math.cos(K)>E}function A(K){var M,J,O,N,L;return{lineStart:function(){N=O=!1,L=1},point:function(V,P){var R,S=[V,P],Q=C(V,P),T=G?Q?0:D(V,P):Q?D(V+(0>V?Dq:-Dq),P):0;if(!M&&(N=O=Q)&&K.lineStart(),Q!==O&&(R=B(M,S),(HA(M,R)||HA(S,R))&&(S[0]+=Dy,S[1]+=Dy,Q=C(S[0],S[1]))),Q!==O){L=0,Q?(K.lineStart(),R=B(S,M),K.point(R[0],R[1])):(R=B(M,S),K.point(R[0],R[1]),K.lineEnd()),M=R}else{if(I&&M&&G^Q){var U;T&J||!(U=B(S,M,!0))||(L=0,G?(K.lineStart(),K.point(U[0][0],U[0][1]),K.point(U[1][0],U[1][1]),K.lineEnd()):(K.point(U[1][0],U[1][1]),K.lineEnd(),K.lineStart(),K.point(U[0][0],U[0][1])))}}!Q||M&&HA(M,S)||K.point(S[0],S[1]),M=S,O=Q,J=T},lineEnd:function(){O&&K.lineEnd(),M=null},clean:function(){return L|(N&&O)<<1}}}function B(Y,R,Ao){var J=HE(Y),U=HE(R),Z=[1,0,0],Ac=HV(J,U),Aa=HI(Ac,Ac),i=Ac[0],K=Aa-i*i;if(!K){return !Ao&&Y}var Ae=E*Aa/K,W=-E*i/K,Ai=HV(Z,Ac),L=Hx(Z,Ae),Al=Hx(Ac,W);HB(L,Al);var P=Ai,j=HI(L,P),Bi=HI(P,P),At=j*j-Bi*(HI(L,L)-1);if(!(0>At)){var Be=Math.sqrt(At),z=Hx(P,(-j-Be)/Bi);if(HB(z,L),z=Hl(z),!Ao){return z}var Bc,Q=Y[0],Bo=R[0],Ba=Y[1],V=R[1];Q>Bo&&(Bc=Q,Q=Bo,Bo=Bc);var Au=Bo-Q,Ar=Math.abs(Au-Dq)<Dy,X=Ar||Dy>Au;if(!Ar&&Ba>V&&(Bc=Ba,Ba=V,V=Bc),X?Ar?Ba+V>0^z[1]<(Math.abs(z[0]-Q)<Dy?Ba:V):Ba<=z[1]&&z[1]<=V:Au>Dq^(Q<=z[0]&&z[0]<=Bo)){var O=Hx(P,(-j+Be)/Bi);return HB(O,L),[z,Hl(O)]}}}function D(L,K){var J=G?F:Dq-F,M=0;return -J>L?M|=1:L>J&&(M|=2),-J>K?M|=4:K>J&&(M|=8),M}var E=Math.cos(F),G=E>0,I=Math.abs(E)>Dy,H=BC(F,6*Fn);return HD(C,A,H,G?[0,-F]:[-Dq,F-Dq])}function BW(F,C,A,B){function D(I,J){return Math.abs(I[0]-F)<Dy?J>0?0:3:Math.abs(I[0]-A)<Dy?J>0?2:1:Math.abs(I[1]-C)<Dy?J>0?1:0:J>0?3:2}function E(I,J){return G(I.point,J.point)}function G(J,L){var K=D(J,1),I=D(L,1);return K!==I?K-I:0===K?L[1]-J[1]:1===K?J[0]-L[0]:2===K?J[1]-L[1]:L[0]-J[0]}function H(L,I){var J=I[0]-L[0],M=I[1]-L[1],K=[0,1];return Math.abs(J)<Dy&&Math.abs(M)<Dy?F<=L[0]&&L[0]<=A&&C<=L[1]&&L[1]<=B:B3(F-L[0],J,K)&&B3(L[0]-A,-J,K)&&B3(C-L[1],M,K)&&B3(L[1]-B,-M,K)?(K[1]<1&&(I[0]=L[0]+K[1]*J,I[1]=L[1]+K[1]*M),K[0]>0&&(L[0]+=K[0]*J,L[1]+=K[0]*M),!0):!1}return function(Y){function V(d){for(var S=0,M=t.length,N=d[1],T=0;M>T;++T){for(var b,f=1,k=t[T],h=k.length,g=k[0];h>f;++f){b=k[f],g[1]<=N?b[1]>N&&I(g,b,d)>0&&++S:b[1]<=N&&I(g,b,d)<0&&--S,g=b}}return 0!==S}function I(M,S,N){return(S[0]-M[0])*(N[1]-M[1])-(N[0]-M[0])*(S[1]-M[1])}function Z(M,b,T,S){var N=0,d=0;if(null==M||(N=D(M,T))!==(d=D(b,T))||G(M,b)<0^T>0){do{S.point(0===N||3===N?F:A,N>1?B:C)}while((N=(N+T+4)%4)!==d)}else{S.point(b[0],b[1])}}function Q(N,M){return N>=F&&A>=N&&M>=C&&B>=M}function a(M,N){Q(M,N)&&Y.point(M,N)}function J(){K.point=L,t&&t.push(j=[]),P=!0,o=!1,O=u=0/0}function e(){W&&(L(r,X),U&&o&&R.rejoin(),W.push(R.buffer())),K.point=a,o&&Y.lineEnd()}function L(N,T){N=Math.max(-F0,Math.min(F0,N)),T=Math.max(-F0,Math.min(F0,T));var S=Q(N,T);if(t&&j.push([N,T]),P){r=N,X=T,U=S,P=!1,S&&(Y.lineStart(),Y.point(N,T))}else{if(S&&o){Y.point(N,T)}else{var M=[O,u],b=[N,T];H(M,b)?(o||(Y.lineStart(),Y.point(M[0],M[1])),Y.point(b[0],b[1]),S||Y.lineEnd(),n=!1):S&&(Y.lineStart(),Y.point(N,T),n=!1)}}O=N,u=T,o=S}var W,t,j,r,X,U,O,u,o,P,n,i=Y,R=Hv(),K={point:a,lineStart:J,lineEnd:e,polygonStart:function(){Y=R,W=[],t=[],n=!0},polygonEnd:function(){Y=i,W=DT.merge(W);var N=V([F,B]),M=n&&N,S=W.length;(M||S)&&(Y.polygonStart(),M&&(Y.lineStart(),Z(null,null,1,Y),Y.lineEnd()),S&&Hf(W,E,N,Z,Y),Y.polygonEnd()),W=t=j=null}};return K}}function B3(B,D,C){if(Math.abs(D)<Dy){return 0>=B}var A=B/D;if(D>0){if(A>C[1]){return !1}A>C[0]&&(C[0]=A)}else{if(A<C[0]){return !1}A<C[1]&&(C[1]=A)}return !0}function B7(A,C){function B(E,D){return E=A(E,D),C(E[0],E[1])}return A.invert&&C.invert&&(B.invert=function(E,D){return E=C.invert(E,D),E&&A.invert(E[0],E[1])}),B}function B9(B){var D=0,C=Dq/3,A=Iw(B),E=A(D,C);return E.parallels=function(F){return arguments.length?A(D=F[0]*Dq/180,C=F[1]*Dq/180):[180*(D/Dq),180*(C/Dq)]},E}function Ch(B,F){function D(H,J){var I=Math.sqrt(C-2*G*Math.sin(J))/G;return[I*Math.sin(H*=G),E-I*Math.cos(H)]}var A=Math.sin(B),G=(A+Math.sin(F))/2,C=1+A*(2*G-A),E=Math.sqrt(C)/G;return D.invert=function(H,J){var I=E-J;return[Math.atan2(H,I)/G,CL((C-(H*H+I*I)*G*G)/(2*G))]},D}function Cg(){function B(F,G){Ak+=E*F-A*G,A=F,E=G}var D,C,A,E;Jh.point=function(F,G){Jh.point=B,D=A=F,C=E=G},Jh.lineEnd=function(){B(D,C)}}function Cb(A,B){AI>A&&(AI=A),A>Jp&&(Jp=A),Jy>B&&(Jy=B),B>JI&&(JI=B)}function BX(){function F(I,J){G.push("M",I,",",J,E)}function C(I,J){G.push("M",I,",",J),H.point=A}function A(I,J){G.push("L",I,",",J)}function B(){H.point=F}function D(){G.push("Z")}var E=Cj(4.5),G=[],H={point:F,lineStart:function(){H.point=C},lineEnd:B,polygonStart:function(){H.lineEnd=D},polygonEnd:function(){H.lineEnd=B,H.point=F},pointRadius:function(I){return E=Cj(I),H},result:function(){if(G.length){var I=G.join("");return G=[],I}}};return H}function Cj(A){return"m0,"+A+"a"+A+","+A+" 0 1,1 0,"+-2*A+"a"+A+","+A+" 0 1,1 0,"+2*A+"z"}function B6(A,B){F7+=A,Fx+=B,++Gd}function Ck(){function A(E,D){var H=E-C,F=D-B,G=Math.sqrt(H*H+F*F);FG+=G*(C+E)/2,Fq+=G*(B+D)/2,Fs+=G,B6(C=E,B=D)}var C,B;Jx.point=function(D,E){Jx.point=A,B6(C=D,B=E)}}function BY(){Jx.point=B6}function Cl(){function B(G,J){var I=G-A,F=J-E,H=Math.sqrt(I*I+F*F);FG+=H*(A+G)/2,Fq+=H*(E+J)/2,Fs+=H,H=E*G-A*J,FA+=H*(A+G),Gb+=H*(E+J),Cn+=3*H,B6(A=G,E=J)}var D,C,A,E;Jx.point=function(F,G){Jx.point=B,B6(D=A=F,C=E=G)},Jx.lineEnd=function(){B(D,C)}}function B0(F){function C(J,I){F.moveTo(J,I),F.arc(J,I,G,0,Db)}function A(J,I){F.moveTo(J,I),H.point=B}function B(J,I){F.lineTo(J,I)}function D(){H.point=C}function E(){F.closePath()}var G=4.5,H={point:C,lineStart:function(){H.point=A},lineEnd:D,polygonStart:function(){H.lineEnd=E},polygonEnd:function(){H.lineEnd=D,H.point=C},pointRadius:function(I){return G=I,H},result:Fg};return H}function Cd(B){function E(L){function G(S,M){S=B(S,M),L.point(S[0],S[1])}function N(){e=0/0,k.point=Q,L.lineStart()}function Q(M,b){var S=HE([M,b]),c=B(M,b);D(e,i,j,U,P,K,e=c[0],i=c[1],j=M,U=S[0],P=S[1],K=S[2],C,L),L.point(e,i)}function W(){k.point=G,L.lineEnd()}function V(){N(),k.point=R,k.lineEnd=H}function R(M,S){Q(X=M,O=S),Y=e,I=i,Z=U,J=P,T=K,k.point=Q}function H(){D(e,i,j,U,P,K,Y,I,X,Z,J,T,C,L),k.lineEnd=W,W()}var X,O,Y,I,Z,J,T,j,e,i,U,P,K,k={point:G,lineStart:N,lineEnd:W,polygonStart:function(){L.polygonStart(),k.lineStart=V},polygonEnd:function(){L.polygonEnd(),k.lineStart=N}};return k}function D(L,Q,U,Z,Y,V,G,e,P,j,H,n,J,W){var Ao=G-L,u=e-Q,Al=Ao*Ao+u*u;if(Al>4*A&&J--){var X=Z+j,R=Y+H,K=V+n,At=Math.sqrt(X*X+R*R+K*K),Ae=Math.asin(K/=At),O=Math.abs(Math.abs(K)-1)<Dy?(U+P)/2:Math.atan2(R,X),Ac=B(O,Ae),r=Ac[0],Ai=Ac[1],I=r-L,Aa=Ai-Q,Ar=u*I-Ao*Aa;(Ar*Ar/Al>A||Math.abs((Ao*I+u*Aa)/Al-0.5)>0.3||F>Z*j+Y*H+V*n)&&(D(L,Q,U,Z,Y,V,r,Ai,O,X/=At,R/=At,K,J,W),W.point(r,Ai),D(r,Ai,O,X,R,K,G,e,P,j,H,n,J,W))}}var A=0.5,F=Math.cos(30*Fn),C=16;return E.precision=function(G){return arguments.length?(C=(A=G*G)>0&&16,E):Math.sqrt(A)},E}function BV(A){this.stream=A}function BL(A){var B=Cd(function(D,C){return A([D*G0,C*G0])});return function(C){var D=new BV(C=B(C));return D.point=function(F,E){C.point(F*Fn,E*Fn)},D}}function BU(A){return Iw(function(){return A})()}function Iw(J){function F(M){return M=Q(M[0]*Fn,M[1]*Fn),[M[0]*H+P,L-M[1]*H]}function U(M){return M=Q.invert((M[0]-P)/H,(L-M[1])/H),M&&[M[0]*G0,M[1]*G0]}function A(){Q=B7(K=BS(N,Y,V),I);var M=I(T,D);return P=S-M[0]*H,L=C+M[1]*H,G()}function G(){return B&&(B.valid=!1,B=null),F}var I,K,Q,P,L,B,R=Cd(function(M,Z){return M=I(M,Z),[M[0]*H+P,L-M[1]*H]}),H=150,S=480,C=250,T=0,D=0,N=0,Y=0,V=0,X=FB,O=H9,W=null,E=null;return F.stream=function(M){return B&&(B.valid=!1),B=Bv(X(K,R(O(M)))),B.valid=!0,B},F.clipAngle=function(M){return arguments.length?(X=null==M?(W=M,FB):Cm((W=+M)*Fn),G()):W},F.clipExtent=function(M){return arguments.length?(E=M,O=M?BW(M[0][0],M[0][1],M[1][0],M[1][1]):H9,G()):E},F.scale=function(M){return arguments.length?(H=+M,A()):H},F.translate=function(M){return arguments.length?(S=+M[0],C=+M[1],A()):[S,C]},F.center=function(M){return arguments.length?(T=M[0]%360*Fn,D=M[1]%360*Fn,A()):[T*G0,D*G0]},F.rotate=function(M){return arguments.length?(N=M[0]%360*Fn,Y=M[1]%360*Fn,V=M.length>2?M[2]%360*Fn:0,A()):[N*G0,Y*G0,V*G0]},DT.rebind(F,R,"precision"),function(){return I=J.apply(this,arguments),F.invert=I.invert&&U,A()}}function Bv(A){var B=new BV(A);return B.point=function(D,C){A.point(D*Fn,C*Fn)},B}function B1(A,B){return[A,B]}function Bx(A,B){return[A>Dq?A-Db:-Dq>A?A+Db:A,B]}function BS(A,C,B){return A?C||B?B7(BO(A),BI(C,B)):BO(A):C||B?BI(C,B):Bx}function B5(A){return function(C,B){return C+=A,[C>Dq?C-Db:-Dq>C?C+Db:C,B]}}function BO(A){var B=B5(A);return B.invert=B5(-A),B}function BI(B,F){function D(I,M){var K=Math.cos(M),N=Math.cos(I)*K,H=Math.sin(I)*K,L=Math.sin(M),J=L*A+N*G;return[Math.atan2(H*C-J*E,N*A-L*G),CL(J*C+H*E)]}var A=Math.cos(B),G=Math.sin(B),C=Math.cos(F),E=Math.sin(F);return D.invert=function(I,M){var K=Math.cos(M),N=Math.cos(I)*K,H=Math.sin(I)*K,L=Math.sin(M),J=L*C-H*E;return[Math.atan2(H*C+L*E,N*A+J*G),CL(J*A-N*G)]},D}function BC(B,D){var C=Math.cos(B),A=Math.sin(B);return function(J,E,G,K){var I=G*D;null!=J?(J=BZ(C,J),E=BZ(C,E),(G>0?E>J:J>E)&&(J+=G*Db)):(J=B+G*Db,E=B-0.5*I);for(var H,F=J;G>0?F>E:E>F;F-=I){K.point((H=Hl([C,-A*Math.cos(F),-A*Math.sin(F)]))[0],H[1])}}}function BZ(B,D){var C=HE(D);C[0]-=B,HG(C);var A=CA(-C[1]);return((-C[2]<0?-A:A)+2*Math.PI-Dy)%(2*Math.PI)}function BT(B,D,C){var A=DT.range(B,D-Dy,C).concat(D);return function(E){return A.map(function(F){return[E,F]})}}function BN(B,D,C){var A=DT.range(B,D-Dy,C).concat(D);return function(E){return A.map(function(F){return[F,E]})}}function BR(A){return A.source}function B4(A){return A.target}function BK(I,E,A,D){var F=Math.cos(E),H=Math.sin(E),J=Math.cos(D),M=Math.sin(D),L=F*Math.cos(I),K=F*Math.sin(I),B=J*Math.cos(A),N=J*Math.sin(A),G=2*Math.asin(Math.sqrt(CF(D-E)+F*J*CF(A-I))),O=1/Math.sin(G),C=G?function(Q){var T=Math.sin(Q*=G)*O,S=Math.sin(G-Q)*O,P=S*L+T*B,U=S*K+T*N,R=S*H+T*M;return[Math.atan2(U,P)*G0,Math.atan2(R,Math.sqrt(P*P+U*U))*G0]}:function(){return[I*G0,E*G0]};return C.distance=G,C}function BG(){function B(F,I){var E=Math.sin(I*=Fn),G=Math.cos(I),J=Math.abs((F*=Fn)-D),H=Math.cos(J);Jz+=Math.atan2(Math.sqrt((J=G*Math.sin(J))*J+(J=A*E-C*G*H)*J),C*E+A*G*H),D=F,C=E,A=G}var D,C,A;JE.point=function(F,E){D=F*Fn,C=Math.sin(E*=Fn),A=Math.cos(E),JE.point=B},JE.lineEnd=function(){JE.point=JE.lineEnd=Fg}}function BP(A,C){function B(G,E){var D=Math.cos(G),H=Math.cos(E),F=A(D*H);return[F*H*Math.sin(G),F*Math.sin(E)]}return B.invert=function(E,G){var D=Math.sqrt(E*E+G*G),I=C(D),F=Math.sin(I),H=Math.cos(I);return[Math.atan2(E*F,D*H),Math.asin(D&&G*F/D)]},B}function By(B,F){function D(H,J){var I=Math.abs(Math.abs(J)-Dg)<Dy?0:E/Math.pow(G(J),C);return[I*Math.sin(C*H),E-I*Math.cos(C*H)]}var A=Math.cos(B),G=function(H){return Math.tan(Dq/4+H/2)},C=B===F?Math.sin(B):Math.log(A/Math.cos(F))/Math.log(G(F)/G(B)),E=A*Math.pow(G(B),C)/C;return C?(D.invert=function(I,K){var J=E-K,H=CR(C)*Math.sqrt(I*I+J*J);return[Math.atan2(I,J)/C,2*Math.atan(Math.pow(E/H,1/C))-Dg]},D):Bw}function BJ(B,E){function D(G,I){var H=C-I;return[H*Math.sin(F*G),C-H*Math.cos(F*G)]}var A=Math.cos(B),F=B===E?Math.sin(B):(A-Math.cos(E))/(E-B),C=A/F+B;return Math.abs(F)<Dy?B1:(D.invert=function(G,I){var H=C-I;return[Math.atan2(G,H)/F,C-CR(F)*Math.sqrt(G*G+H*H)]},D)}function Bw(A,B){return[A,Math.log(Math.tan(Dq/4+B/2))]}function Bs(B){var E,D=BU(B),A=D.scale,F=D.translate,C=D.clipExtent;return D.scale=function(){var G=A.apply(D,arguments);return G===D?E?D.clipExtent(null):D:G},D.translate=function(){var G=F.apply(D,arguments);return G===D?E?D.clipExtent(null):D:G},D.clipExtent=function(G){var H=C.apply(D,arguments);if(H===D){if(E=null==G){var J=Dq*A(),I=F();C([[I[0]-J,I[1]-J],[I[0]+J,I[1]+J]])}}else{E&&(H=null)}return H},D.clipExtent(null)}function BH(A,C){var B=Math.cos(C)*Math.sin(A);return[Math.log((1+B)/(1-B))/2,Math.atan2(Math.tan(C),Math.cos(A))]}function BD(F){function C(K){function M(){N.push("M",E(F(I),H))}for(var O,N=[],I=[],P=-1,L=K.length,Q=HP(A),J=HP(B);++P<L;){D.call(this,O=K[P],P)?I.push([+Q.call(this,O,P),+J.call(this,O,P)]):I.length&&(M(),I=[])}return I.length&&M(),N.length?N.join(""):null}var A=Bp,B=BA,D=Hn,E=Bq,G=E.key,H=0.7;return C.x=function(I){return arguments.length?(A=I,C):A},C.y=function(I){return arguments.length?(B=I,C):B},C.defined=function(I){return arguments.length?(D=I,C):D},C.interpolate=function(I){return arguments.length?(G="function"==typeof I?E=I:(E=JG.get(I)||Bq).key,C):G},C.tension=function(I){return arguments.length?(H=I,C):H},C}function Bp(A){return A[0]}function BA(A){return A[1]}function Bq(A){return A.join("L")}function Cp(A){return Bq(A)+"Z"}function BM(B){for(var D=0,C=B.length,A=B[0],E=[A[0],",",A[1]];++D<C;){E.push("H",(A[0]+(A=B[D])[0])/2,"V",A[1])}return C>1&&E.push("H",A[0]),E.join("")}function BB(B){for(var D=0,C=B.length,A=B[0],E=[A[0],",",A[1]];++D<C;){E.push("V",(A=B[D])[1],"H",A[0])}return E.join("")}function BE(B){for(var D=0,C=B.length,A=B[0],E=[A[0],",",A[1]];++D<C;){E.push("H",(A=B[D])[0],"V",A[1])}return E.join("")}function BQ(A,B){return A.length<4?Bq(A):A[1]+EQ(A.slice(1,A.length-1),EK(A,B))}function BF(A,B){return A.length<3?Bq(A):A[0]+EQ((A.push(A[0]),A),EK([A[A.length-2]].concat(A,[A[1]]),B))}function Bz(A,B){return A.length<3?Bq(A):A[0]+EQ(A,EK(A,B))}function EQ(G,D){if(D.length<1||G.length!=D.length&&G.length!=D.length+2){return Bq(G)}var A=G.length!=D.length,C="",E=G[0],F=G[1],H=D[0],K=H,J=1;if(A&&(C+="Q"+(F[0]-2*H[0]/3)+","+(F[1]-2*H[1]/3)+","+F[0]+","+F[1],E=G[1],J=2),D.length>1){K=D[1],F=G[J],J++,C+="C"+(E[0]+H[0])+","+(E[1]+H[1])+","+(F[0]-K[0])+","+(F[1]-K[1])+","+F[0]+","+F[1];for(var I=2;I<D.length;I++,J++){F=G[J],K=D[I],C+="S"+(F[0]-K[0])+","+(F[1]-K[1])+","+F[0]+","+F[1]}}if(A){var B=G[J];C+="Q"+(F[0]+2*K[0]/3)+","+(F[1]+2*K[1]/3)+","+B[0]+","+B[1]}return C}function EK(F,C){for(var A,B=[],D=(1-C)/2,E=F[0],G=F[1],I=1,H=F.length;++I<H;){A=E,E=G,G=F[I],B.push([D*(G[0]-A[0]),D*(G[1]-A[1])])}return B}function E0(F){if(F.length<3){return Bq(F)}var C=1,A=F.length,B=F[0],D=B[0],E=B[1],G=[D,D,D,(B=F[1])[0]],I=[E,E,E,B[1]],H=[D,",",E,"L",ER(Jm,G),",",ER(Jm,I)];for(F.push(F[A-1]);++C<=A;){B=F[C],G.shift(),G.push(B[0]),I.shift(),I.push(B[1]),EW(H,G,I)}return F.pop(),H.push("L",B),H.join("")}function EE(B){if(B.length<4){return Bq(B)}for(var F,D=[],A=-1,G=B.length,C=[0],E=[0];++A<3;){F=B[A],C.push(F[0]),E.push(F[1])}for(D.push(ER(Jm,C)+","+ER(Jm,E)),--A;++A<G;){F=B[A],C.shift(),C.push(F[0]),E.shift(),E.push(F[1]),EW(D,C,E)}return D.join("")}function EL(F){for(var C,A,B=-1,D=F.length,E=D+4,G=[],H=[];++B<4;){A=F[B%D],G.push(A[0]),H.push(A[1])}for(C=[ER(Jm,G),",",ER(Jm,H)],--B;++B<E;){A=F[B%D],G.shift(),G.push(A[0]),H.shift(),H.push(A[1]),EW(C,G,H)}return C.join("")}function EP(F,C){var A=F.length-1;if(A){for(var B,D,E=F[0][0],G=F[0][1],J=F[A][0]-E,I=F[A][1]-G,H=-1;++H<=A;){B=F[H],D=H/A,B[0]=C*B[0]+(1-C)*(E+D*J),B[1]=C*B[1]+(1-C)*(G+D*I)}}return E0(F)}function ER(A,B){return A[0]*B[0]+A[1]*B[1]+A[2]*B[2]+A[3]*B[3]}function EW(A,C,B){A.push("C",ER(Jk,C),",",ER(Jk,B),",",ER(JH,C),",",ER(JH,B),",",ER(Jm,C),",",ER(Jm,B))}function EV(A,B){return(B[1]-A[1])/(B[0]-A[0])}function ES(B){for(var F=0,D=B.length-1,A=[],G=B[0],C=B[1],E=A[0]=EV(G,C);++F<D;){A[F]=(E+(E=EV(G=C,C=B[F+1])))/2}return A[F]=E,A}function EF(F){for(var C,A,B,D,E=[],G=ES(F),I=-1,H=F.length-1;++I<H;){C=EV(F[I],F[I+1]),Math.abs(C)<Dy?G[I]=G[I+1]=0:(A=G[I]/C,B=G[I+1]/C,D=A*A+B*B,D>9&&(D=3*C/Math.sqrt(D),G[I]=D*A,G[I+1]=D*B))}for(I=-1;++I<=H;){D=(F[Math.min(H,I+1)][0]-F[Math.max(0,I-1)][0])/(6*(1+G[I]*G[I])),E.push([D||0,G[I]*D||0])}return E}function EX(A){return A.length<3?Bq(A):A[0]+EQ(A,EF(A))}function EO(G,D,A,C){var E,F,H,K,J,I,B;return E=C[G],F=E[0],H=E[1],E=C[D],K=E[0],J=E[1],E=C[A],I=E[0],B=E[1],(B-H)*(K-F)-(J-H)*(I-F)>0}function EY(A,C,B){return(B[0]-C[0])*(A[1]-C[1])<(B[1]-C[1])*(A[0]-C[0])}function EG(H,D,A,C){var E=H[0],G=A[0],I=D[0]-E,L=C[0]-G,K=H[1],J=A[1],B=D[1]-K,M=C[1]-J,F=(L*(K-J)-M*(E-G))/(M*I-L*B);return[E+F*I,K+F*B]}function EZ(A){var C=A[0],B=A[A.length-1];return !(C[0]-B[0]||C[1]-B[1])}function Cq(I,E){var S={list:I.map(function(M,V){return{index:V,x:M[0],y:M[1]}}).sort(function(M,V){return M.y<V.y?-1:M.y>V.y?1:M.x<V.x?-1:M.x>V.x?1:0}),bottomSite:null},A={list:[],leftEnd:null,rightEnd:null,init:function(){A.leftEnd=A.createHalfEdge(null,"l"),A.rightEnd=A.createHalfEdge(null,"l"),A.leftEnd.r=A.rightEnd,A.rightEnd.l=A.leftEnd,A.list.unshift(A.leftEnd,A.rightEnd)},createHalfEdge:function(M,V){return{edge:M,side:V,vertex:null,l:null,r:null}},insert:function(M,V){V.l=M,V.r=M.r,M.r.l=V,M.r=V},leftBound:function(M){var V=A.leftEnd;do{V=V.r}while(V!=A.rightEnd&&F.rightOf(V,M));return V=V.l},del:function(M){M.l.r=M.r,M.r.l=M.l,M.edge=null},right:function(M){return M.r},left:function(M){return M.l},leftRegion:function(M){return null==M.edge?S.bottomSite:M.edge.region[M.side]},rightRegion:function(M){return null==M.edge?S.bottomSite:M.edge.region[Jg[M.side]]}},F={bisect:function(V,Z){var X={region:{l:V,r:Z},ep:{l:null,r:null}},M=Z.x-V.x,a=Z.y-V.y,W=M>0?M:-M,Y=a>0?a:-a;return X.c=V.x*M+V.y*a+0.5*(M*M+a*a),W>Y?(X.a=1,X.b=a/M,X.c/=M):(X.b=1,X.a=M/a,X.c/=a),X},intersect:function(b,X){var M=b.edge,W=X.edge;if(!M||!W||M.region.r==W.region.r){return null}var Y=M.a*W.b-M.b*W.a;if(Math.abs(Y)<1e-10){return null}var Z,d,j=(M.c*W.b-W.c*M.b)/Y,h=(W.c*M.a-M.c*W.a)/Y,g=M.region.r,V=W.region.r;g.y<V.y||g.y==V.y&&g.x<V.x?(Z=b,d=M):(Z=X,d=W);var k=j>=d.region.r.x;return k&&"l"===Z.side||!k&&"r"===Z.side?null:{x:j,y:h}},rightOf:function(d,X){var M=d.edge,W=M.region.r,Y=X.x>W.x;if(Y&&"l"===d.side){return 1}if(!Y&&"r"===d.side){return 0}if(1===M.a){var b=X.y-W.y,j=X.x-W.x,p=0,m=0;if(!Y&&M.b<0||Y&&M.b>=0?m=p=b>=M.b*j:(m=X.x+X.y*M.b>M.c,M.b<0&&(m=!m),m||(p=1)),!p){var k=W.x-M.region.l.x;m=M.b*(j*j-b*b)<k*b*(1+2*j/k+M.b*M.b),M.b<0&&(m=!m)}}else{var V=M.c-M.a*X.x,q=X.y-V,Z=X.x-W.x,v=V-W.y;m=q*q>Z*Z+v*v}return"l"===d.side?m:!m},endPoint:function(V,W,M){V.ep[W]=M,V.ep[Jg[W]]&&E(V)},distance:function(V,X){var W=V.x-X.x,M=V.y-X.y;return Math.sqrt(W*W+M*M)}},H={list:[],insert:function(V,Y,X){V.vertex=Y,V.ystar=Y.y+X;for(var M=0,Z=H.list,W=Z.length;W>M;M++){var b=Z[M];if(!(V.ystar>b.ystar||V.ystar==b.ystar&&Y.x>b.vertex.x)){break}}Z.splice(M,0,V)},del:function(V){for(var X=0,W=H.list,M=W.length;M>X&&W[X]!=V;++X){}W.splice(X,1)},empty:function(){return 0===H.list.length},nextEvent:function(V){for(var X=0,W=H.list,M=W.length;M>X;++X){if(W[X]==V){return W[X+1]}}return null},min:function(){var M=H.list[0];return{x:M.vertex.x,y:M.ystar}},extractMin:function(){return H.list.shift()}};A.init(),S.bottomSite=S.list.shift();for(var J,O,N,K,B,P,G,Q,C,R,D,L,U,T=S.list.shift();;){if(H.empty()||(J=H.min()),T&&(H.empty()||T.y<J.y||T.y==J.y&&T.x<J.x)){O=A.leftBound(T),N=A.right(O),G=A.rightRegion(O),L=F.bisect(G,T),P=A.createHalfEdge(L,"l"),A.insert(O,P),R=F.intersect(O,P),R&&(H.del(O),H.insert(O,R,F.distance(R,T))),O=P,P=A.createHalfEdge(L,"r"),A.insert(O,P),R=F.intersect(P,N),R&&H.insert(P,R,F.distance(R,T)),T=S.list.shift()}else{if(H.empty()){break}O=H.extractMin(),K=A.left(O),N=A.right(O),B=A.right(N),G=A.leftRegion(O),Q=A.rightRegion(N),D=O.vertex,F.endPoint(O.edge,O.side,D),F.endPoint(N.edge,N.side,D),A.del(O),H.del(N),A.del(N),U="l",G.y>Q.y&&(C=G,G=Q,Q=C,U="r"),L=F.bisect(G,Q),P=A.createHalfEdge(L,U),A.insert(K,P),F.endPoint(L,Jg[U],D),R=F.intersect(K,P),R&&(H.del(K),H.insert(K,R,F.distance(R,G))),R=F.intersect(P,B),R&&H.insert(P,R,F.distance(R,G))}}for(O=A.right(A.leftEnd);O!=A.rightEnd;O=A.right(O)){E(O.edge)}}function ET(A){return A.x}function ED(A){return A.y}function Eq(){return{leaf:!0,nodes:[],point:null,x:null,y:null}}function EC(F,C,A,B,D,E){if(!F(C,A,B,D,E)){var G=0.5*(A+D),I=0.5*(B+E),H=C.nodes;H[0]&&EC(F,H[0],A,B,G,I),H[1]&&EC(F,H[1],G,B,D,I),H[2]&&EC(F,H[2],A,I,G,E),H[3]&&EC(F,H[3],G,I,D,E)}}function EU(F,C){F=DT.rgb(F),C=DT.rgb(C);var A=F.r,B=F.g,D=F.b,E=C.r-A,G=C.g-B,H=C.b-D;return function(I){return"#"+H4(Math.round(A+E*I))+H4(Math.round(B+G*I))+H4(Math.round(D+H*I))}}function D3(B,D){var C,A={},E={};for(C in B){C in D?A[C]=EA(B[C],D[C]):E[C]=B[C]}for(C in D){C in B||(E[C]=D[C])}return function(F){for(C in A){E[C]=A[C](F)}return E}}function EJ(A,B){return B-=A=+A,function(C){return A+B*C}}function D6(G,D){var A,C,E,F,H,K=0,J=0,I=[],B=[];for(G+="",D+="",I3.lastIndex=0,C=0;A=I3.exec(D);++C){A.index&&I.push(D.substring(K,J=A.index)),B.push({i:I.length,x:A[0]}),I.push(null),K=I3.lastIndex}for(K<D.length&&I.push(D.substring(K)),C=0,F=B.length;(A=I3.exec(G))&&F>C;++C){if(H=B[C],H.x==A[0]){if(H.i){if(null==I[H.i+1]){for(I[H.i-1]+=H.x,I.splice(H.i,1),E=C+1;F>E;++E){B[E].i--}}else{for(I[H.i-1]+=H.x+I[H.i+1],I.splice(H.i,2),E=C+1;F>E;++E){B[E].i-=2}}}else{if(null==I[H.i+1]){I[H.i]=H.x}else{for(I[H.i]=H.x+I[H.i+1],I.splice(H.i+1,1),E=C+1;F>E;++E){B[E].i--}}}B.splice(C,1),F--,C--}else{H.x=EJ(parseFloat(A[0]),parseFloat(H.x))}}for(;F>C;){H=B.pop(),null==I[H.i+1]?I[H.i]=H.x:(I[H.i]=H.x+I[H.i+1],I.splice(H.i+1,1)),F--}return 1===I.length?null==I[0]?(H=B[0].x,function(L){return H(L)+""}):function(){return D}:function(L){for(C=0;F>C;++C){I[(H=B[C]).i]=H.x(L)}return I.join("")}}function EA(B,D){for(var C,A=DT.interpolators.length;--A>=0&&!(C=DT.interpolators[A](B,D));){}return C}function EN(F,C){var A,B=[],D=[],E=F.length,G=C.length,H=Math.min(F.length,C.length);for(A=0;H>A;++A){B.push(EA(F[A],C[A]))}for(;E>A;++A){D[A]=F[A]}for(;G>A;++A){D[A]=C[A]}return function(I){for(A=0;H>A;++A){D[A]=B[A](I)}return D}}function Ew(A){return function(B){return 0>=B?0:B>=1?1:A(B)}}function Em(A){return function(B){return 1-A(1-B)}}function Ed(A){return function(B){return 0.5*(0.5>B?A(2*B):2-A(2-2*B))}}function EH(A){return A*A}function EB(A){return A*A*A}function Ev(A){if(0>=A){return 0}if(A>=1){return 1}var C=A*A,B=C*A;return 4*(0.5>A?B:3*(A-C)+B-0.75)}function Ez(A){return function(B){return Math.pow(B,A)}}function EM(A){return 1-Math.cos(A*Dg)}function Ep(A){return Math.pow(2,10*(A-1))}function Ej(A){return 1-Math.sqrt(1-A*A)}function Ex(A,C){var B;return arguments.length<2&&(C=0.45),arguments.length?B=C/Db*Math.asin(1/A):(A=1,B=C/4),function(D){return 1+A*Math.pow(2,-10*D)*Math.sin((D-B)*Db/C)}}function D7(A){return A||(A=1.70158),function(B){return B*B*((A+1)*B-A)}}function En(A){return 1/2.75>A?7.5625*A*A:2/2.75>A?7.5625*(A-=1.5/2.75)*A+0.75:2.5/2.75>A?7.5625*(A-=2.25/2.75)*A+0.9375:7.5625*(A-=2.625/2.75)*A+0.984375}function D5(F,C){F=DT.hcl(F),C=DT.hcl(C);var A=F.h,B=F.c,D=F.l,E=C.h-A,G=C.c-B,H=C.l-D;return isNaN(G)&&(G=0,B=isNaN(B)?C.c:B),isNaN(E)?(E=0,A=isNaN(A)?C.h:A):E>180?E-=360:-180>E&&(E+=360),function(I){return CG(A+E*I,B+G*I,D+H*I)+""}}function D2(F,C){F=DT.hsl(F),C=DT.hsl(C);var A=F.h,B=F.s,D=F.l,E=C.h-A,G=C.s-B,H=C.l-D;return isNaN(G)&&(G=0,B=isNaN(B)?C.s:B),isNaN(E)?(E=0,A=isNaN(A)?C.h:A):E>180?E-=360:-180>E&&(E+=360),function(I){return D4(A+E*I,B+G*I,D+H*I)+""}}function Ek(F,C){F=DT.lab(F),C=DT.lab(C);var A=F.l,B=F.a,D=F.b,E=C.l-A,G=C.a-B,H=C.b-D;return function(I){return CB(A+E*I,B+G*I,D+H*I)+""}}function Ef(A,B){return B-=A,function(C){return Math.round(A+B*C)}}function D0(B){var E=[B.a,B.b],D=[B.c,B.d],A=D1(E),F=D9(E,D),C=D1(Fm(D,E,-F))||0;E[0]*D[1]<D[0]*E[1]&&(E[0]*=-1,E[1]*=-1,A*=-1,F*=-1),this.rotate=(A?Math.atan2(E[1],E[0]):Math.atan2(-D[0],D[1]))*G0,this.translate=[B.e,B.f],this.scale=[A,C],this.skew=C?Math.atan2(F,C)*G0:0}function D9(A,B){return A[0]*B[0]+A[1]*B[1]}function D1(A){var B=Math.sqrt(D9(A,A));return B&&(A[0]/=B,A[1]/=B),B}function Fm(A,C,B){return A[0]+=B*C[0],A[1]+=B*C[1],A}function Es(I,E){var A,D=[],F=[],H=DT.transform(I),J=DT.transform(E),M=H.translate,L=J.translate,K=H.rotate,B=J.rotate,N=H.skew,G=J.skew,O=H.scale,C=J.scale;return M[0]!=L[0]||M[1]!=L[1]?(D.push("translate(",null,",",null,")"),F.push({i:1,x:EJ(M[0],L[0])},{i:3,x:EJ(M[1],L[1])})):L[0]||L[1]?D.push("translate("+L+")"):D.push(""),K!=B?(K-B>180?B+=360:B-K>180&&(K+=360),F.push({i:D.push(D.pop()+"rotate(",null,")")-2,x:EJ(K,B)})):B&&D.push(D.pop()+"rotate("+B+")"),N!=G?F.push({i:D.push(D.pop()+"skewX(",null,")")-2,x:EJ(N,G)}):G&&D.push(D.pop()+"skewX("+G+")"),O[0]!=C[0]||O[1]!=C[1]?(A=D.push(D.pop()+"scale(",null,",",null,")"),F.push({i:A-4,x:EJ(O[0],C[0])},{i:A-2,x:EJ(O[1],C[1])})):(1!=C[0]||1!=C[1])&&D.push(D.pop()+"scale("+C+")"),A=F.length,function(Q){for(var R,P=-1;++P<A;){D[(R=F[P]).i]=R.x(Q)}return D.join("")}}function Eb(A,B){return B=B-(A=+A)?1/(B-A):0,function(C){return(C-A)*B}}function Eg(A,B){return B=B-(A=+A)?1/(B-A):0,function(C){return Math.max(0,Math.min(1,(C-A)*B))}}function Ey(B){for(var E=B.source,D=B.target,A=D8(E,D),F=[E];E!==A;){E=E.parent,F.push(E)}for(var C=F.length;D!==A;){F.splice(C,0,D),D=D.parent}return F}function Eh(A){for(var C=[],B=A.parent;null!=B;){C.push(A),A=B,B=B.parent}return C.push(A),C}function D8(B,F){if(B===F){return B}for(var D=Eh(B),A=Eh(F),G=D.pop(),C=A.pop(),E=null;G===C;){E=G,G=D.pop(),C=A.pop()}return E}function A4(A){A.fixed|=2}function AY(A){A.fixed&=-7}function Bj(A){A.fixed|=4,A.px=A.x,A.py=A.y}function AS(A){A.fixed&=-5}function AZ(F,C,A){var B=0,D=0;if(F.charge=0,!F.leaf){for(var E,G=F.nodes,J=G.length,I=-1;++I<J;){E=G[I],null!=E&&(AZ(E,C,A),F.charge+=E.charge,B+=E.charge*E.cx,D+=E.charge*E.cy)}}if(F.point){F.leaf||(F.point.x+=Math.random()-0.5,F.point.y+=Math.random()-0.5);var H=C*A[F.point.index];F.charge+=F.pointCharge=H,B+=H*F.point.x,D+=H*F.point.y}F.cx=B/F.charge,F.cy=D/F.charge}function A3(A,B){return DT.rebind(A,B,"sort","children","value"),A.nodes=A,A.links=A6,A}function A5(A){return A.children}function Bd(A){return A.value}function A9(A,B){return B.value-A.value}function A6(A){return DT.merge(A.map(function(B){return(B.children||[]).map(function(C){return{source:B,target:C}})}))}function AT(A){return A.x}function Bf(A){return A.y}function A2(A,C,B){A.y0=C,A.y=B}function Bg(A){return DT.range(A.length)}function AU(B){for(var D=-1,C=B[0].length,A=[];++D<C;){A[D]=0}return A}function Bh(B){for(var E,D=1,A=0,F=B[0][1],C=B.length;C>D;++D){(E=B[D][1])>F&&(A=D,F=E)}return A}function AW(A){return A.reduce(A7,0)}function A7(A,B){return A+B[1]}function AQ(A,B){return AF(A,Math.ceil(Math.log(B.length)/Math.LN2+1))}function AF(B,E){for(var D=-1,A=+B[0],F=(B[1]-A)/E,C=[];++D<=E;){C[D]=F*D+A}return C}function AP(A){return[DT.min(A),DT.max(A)]}function A8(A,B){return A.parent==B.parent?1:2}function Aj(A){var B=A.children;return B&&B.length?B[0]:A._tree.thread}function AX(A){var C,B=A.children;return B&&(C=B.length)?B[C-1]:A._tree.thread}function An(B,E){var D=B.children;if(D&&(F=D.length)){for(var A,F,C=-1;++C<F;){E(A=An(D[C],E),B)>0&&(B=A)}}return B}function AN(A,B){return A.x-B.x}function A1(A,B){return B.x-A.x}function AJ(A,B){return A.depth-B.depth}function AC(A,C){function B(E,D){var I=E.children;if(I&&(G=I.length)){for(var F,G,J=null,H=-1;++H<G;){F=I[H],B(F,J),J=F}}C(E,D)}B(A,null)}function Aw(B){for(var E,D=0,A=0,F=B.children,C=F.length;--C>=0;){E=F[C]._tree,E.prelim+=D,E.mod+=D,D+=E.shift+(A+=E.change)}}function AV(B,D,C){B=B._tree,D=D._tree;var A=C/(D.number-B.number);B.change+=A,D.change-=A,D.shift+=C,D.prelim+=C,D.mod+=C}function AO(A,C,B){return A._tree.ancestor.parent==C.parent?A._tree.ancestor:B}function AH(A,B){return A.value-B.value}function AM(A,C){var B=A._pack_next;A._pack_next=C,C._pack_prev=A,C._pack_next=B,B._pack_prev=C}function A0(A,B){A._pack_next=B,B._pack_prev=A}function AE(B,D){var C=D.x-B.x,A=D.y-B.y,E=B.r+D.r;return 0.999*E*E>C*C+A*A}function AA(I){function E(M){B=Math.min(M.x-M.r,B),P=Math.max(M.x+M.r,P),G=Math.min(M.y-M.r,G),Q=Math.max(M.y+M.r,Q)}if((S=I.children)&&(K=S.length)){var S,A,F,H,J,O,N,K,B=1/0,P=-1/0,G=1/0,Q=-1/0;if(S.forEach(AK),A=S[0],A.x=-A.r,A.y=0,E(A),K>1&&(F=S[1],F.x=F.r,F.y=0,E(F),K>2)){for(H=S[2],Am(A,F,H),E(H),AM(A,H),A._pack_prev=H,AM(H,F),F=A._pack_next,J=3;K>J;J++){Am(A,F,H=S[J]);var C=0,R=1,D=1;for(O=F._pack_next;O!==F;O=O._pack_next,R++){if(AE(O,H)){C=1;break}}if(1==C){for(N=A._pack_prev;N!==O._pack_prev&&!AE(N,H);N=N._pack_prev,D++){}}C?(D>R||R==D&&F.r<A.r?A0(A,F=O):A0(A=N,F),J--):(AM(A,H),F=H,E(H))}}var L=(B+P)/2,U=(G+Q)/2,T=0;for(J=0;K>J;J++){H=S[J],H.x-=L,H.y-=U,T=Math.max(T,H.r+Math.sqrt(H.x*H.x+H.y*H.y))}I.r=T,S.forEach(Ap)}}function AK(A){A._pack_next=A._pack_prev=A}function Ap(A){delete A._pack_next,delete A._pack_prev}function AD(B,F,D,A){var G=B.children;if(B.x=F+=A*B.x,B.y=D+=A*B.y,B.r*=A,G){for(var C=-1,E=G.length;++C<E;){AD(G[C],F,D,A)}}}function Am(F,C,A){var B=F.r+A.r,D=C.x-F.x,E=C.y-F.y;if(B&&(D||E)){var G=C.r+A.r,J=D*D+E*E;G*=G,B*=B;var I=0.5+(B-G)/(2*J),H=Math.sqrt(Math.max(0,2*G*(B+J)-(B-=J)*B-G*G))/(2*J);A.x=F.x+I*D+H*E,A.y=F.y+I*E-H*D}else{A.x=F.x+B,A.y=F.y}}function Ah(A){return 1+DT.max(A,function(B){return B.y})}function AB(A){return A.reduce(function(B,C){return B+C.x},0)/A.length}function Ax(A){var B=A.children;return B&&B.length?Ax(B[0]):A}function Af(A){var C,B=A.children;return B&&(C=B.length)?Af(B[C-1]):A}function As(A){return{x:A.x,y:A.y,dx:A.dx,dy:A.dy}}function Ag(B,E){var D=B.x+E[3],A=B.y+E[0],F=B.dx-E[1]-E[3],C=B.dy-E[0]-E[2];return 0>F&&(D+=F/2,F=0),0>C&&(A+=C/2,C=0),{x:D,y:A,dx:F,dy:C}}function Cf(A){var C=A[0],B=A[A.length-1];return B>C?[C,B]:[B,C]}function AG(A){return A.rangeExtent?A.rangeExtent():Cf(A.range())}function Av(B,E,D,A){var F=D(B[0],B[1]),C=A(E[0],E[1]);return function(G){return C(F(G))}}function Ay(B,F){var D,A=0,G=B.length-1,C=B[A],E=B[G];return C>E&&(D=A,A=G,G=D,D=C,C=E,E=D),B[A]=F.floor(C),B[G]=F.ceil(E),B}function AL(A){return A?{floor:function(B){return Math.floor(B/A)*A},ceil:function(B){return Math.ceil(B/A)*A}}:IU}function Az(F,C,A,B){var D=[],E=[],G=0,H=Math.min(F.length,C.length)-1;for(F[H]<F[0]&&(F=F.slice().reverse(),C=C.slice().reverse());++G<=H;){D.push(A(F[G-1],F[G])),E.push(B(C[G-1],C[G]))}return function(J){var I=DT.bisect(F,J,1,H)-1;return E[I](D[I](J))}}function Aq(F,C,A,B){function D(){var J=Math.min(F.length,C.length)>2?Az:Av,I=B?Eg:Eb;return G=J(F,C,I,A),H=J(C,F,I,EA),E}function E(I){return G(I)}var G,H;return E.invert=function(I){return H(I)},E.domain=function(I){return arguments.length?(F=I.map(Number),D()):F},E.range=function(I){return arguments.length?(C=I,D()):C},E.rangeRound=function(I){return E.range(I).interpolate(Ef)},E.clamp=function(I){return arguments.length?(B=I,D()):B},E.interpolate=function(I){return arguments.length?(A=I,D()):A},E.ticks=function(I){return Gf(F,I)},E.tickFormat=function(J,I){return Gn(F,J,I)},E.nice=function(I){return Gm(F,I),D()},E.copy=function(){return Aq(F,C,A,B)},D()}function Gw(A,B){return DT.rebind(A,B,"range","rangeRound","interpolate","clamp")}function Gm(A,B){return Ay(A,AL(GH(A,B)[2]))}function GH(B,E){null==E&&(E=10);var D=Cf(B),A=D[1]-D[0],F=Math.pow(10,Math.floor(Math.log(A/E)/Math.LN10)),C=E/A*F;return 0.15>=C?F*=10:0.35>=C?F*=5:0.75>=C&&(F*=2),D[0]=Math.ceil(D[0]/F)*F,D[1]=Math.floor(D[1]/F)*F+0.5*F,D[2]=F,D}function Gf(A,B){return DT.range.apply(DT,GH(A,B))}function Gn(B,D,C){var A=-Math.floor(Math.log(GH(B,D)[2])/Math.LN10+0.01);return DT.format(C?C.replace(Bm,function(J,G,E,H,I,K,N,M,L,F){return[G,E,H,I,K,N,M,L||"."+(A-2*("%"===F)),F].join("")}):",."+A+"f")}function Gv(B,F,D,A){function G(H){return(D?Math.log(0>H?0:H):-Math.log(H>0?0:-H))/Math.log(F)}function C(H){return D?Math.pow(F,H):-Math.pow(F,-H)}function E(H){return B(G(H))}return E.invert=function(H){return C(B.invert(H))},E.domain=function(H){return arguments.length?(D=H[0]>=0,B.domain((A=H.map(Number)).map(G)),E):A},E.base=function(H){return arguments.length?(F=+H,B.domain(A.map(G)),E):F},E.nice=function(){var H=Ay(A.map(G),D?Math:Jd);return B.domain(H),A=H.map(C),E},E.ticks=function(){var J=Cf(A),K=[],N=J[0],M=J[1],L=Math.floor(G(N)),H=Math.ceil(G(M)),O=F%1?2:F;if(isFinite(H-L)){if(D){for(;H>L;L++){for(var I=1;O>I;I++){K.push(C(L)*I)}}K.push(C(L))}else{for(K.push(C(L));L++<H;){for(var I=O-1;I>0;I--){K.push(C(L)*I)}}}for(L=0;K[L]<N;L++){}for(H=K.length;K[H-1]>M;H--){}K=K.slice(L,H)}return K},E.tickFormat=function(J,K){if(!arguments.length){return Jl}arguments.length<2?K=Jl:"function"!=typeof K&&(K=DT.format(K));var I,L=Math.max(0.1,J/E.ticks().length),H=D?(I=1e-12,Math.ceil):(I=-1e-12,Math.floor);return function(M){return M/C(H(G(M)+I))<=L?K(M):""}},E.copy=function(){return Gv(B.copy(),F,D,A)},Gw(E,B)}function Gx(B,E,D){function A(G){return B(F(G))}var F=GD(E),C=GD(1/E);return A.invert=function(G){return C(B.invert(G))},A.domain=function(G){return arguments.length?(B.domain((D=G.map(Number)).map(F)),A):D},A.ticks=function(G){return Gf(D,G)},A.tickFormat=function(G,H){return Gn(D,G,H)},A.nice=function(G){return A.domain(Gm(D,G))},A.exponent=function(G){return arguments.length?(F=GD(E=G),C=GD(1/E),B.domain(D.map(F)),A):E},A.copy=function(){return Gx(B.copy(),E,D)},Gw(A,B)}function GD(A){return function(B){return 0>B?-Math.pow(-B,A):Math.pow(B,A)}}function GB(B,F){function D(H){return E[((C.get(H)||"range"===F.t&&C.set(H,B.push(H)))-1)%E.length]}function A(I,H){return DT.range(B.length).map(function(J){return I+H*J})}var C,E,G;return D.domain=function(H){if(!arguments.length){return B}B=[],C=new E5;for(var I,K=-1,J=H.length;++K<J;){C.has(I=H[K])||C.set(I,B.push(I))}return D[F.t].apply(D,F.a)},D.range=function(H){return arguments.length?(E=H,G=0,F={t:"range",a:arguments},D):E},D.rangePoints=function(L,H){arguments.length<2&&(H=0);var K=L[0],J=L[1],I=(J-K)/(Math.max(1,B.length-1)+H);return E=A(B.length<2?(K+J)/2:K+I*H/2,I),G=0,F={t:"rangePoints",a:arguments},D},D.rangeBands=function(M,I,L){arguments.length<2&&(I=0),arguments.length<3&&(L=I);var K=M[1]<M[0],J=M[K-0],N=M[1-K],H=(N-J)/(B.length-I+2*L);return E=A(J+H*L,H),K&&E.reverse(),G=H*(1-I),F={t:"rangeBands",a:arguments},D},D.rangeRoundBands=function(I,K,M){arguments.length<2&&(K=0),arguments.length<3&&(M=K);var L=I[1]<I[0],H=I[L-0],N=I[1-L],J=Math.floor((N-H)/(B.length-K+2*M)),O=N-H-(B.length-K)*J;return E=A(H+Math.round(O/2),J),L&&E.reverse(),G=Math.round(J*(1-K)),F={t:"rangeRoundBands",a:arguments},D},D.rangeBand=function(){return G},D.rangeExtent=function(){return Cf(F.a[0])},D.copy=function(){return GB(B,F)},D.domain(B)}function Gy(B,D){function C(){var G=0,F=D.length;for(E=[];++G<F;){E[G-1]=DT.quantile(B,G/F)}return A}function A(F){return isNaN(F=+F)?void 0:D[DT.bisect(E,F)]}var E;return A.domain=function(F){return arguments.length?(B=F.filter(function(G){return !isNaN(G)}).sort(DT.ascending),C()):B},A.range=function(F){return arguments.length?(D=F,C()):D},A.quantiles=function(){return E},A.invertExtent=function(F){return F=D.indexOf(F),0>F?[0/0,0/0]:[F>0?E[F-1]:B[0],F<E.length?E[F]:B[B.length-1]]},A.copy=function(){return Gy(B,D)},C()}function Gg(B,F,D){function A(H){return D[Math.max(0,Math.min(E,Math.floor(C*(H-B))))]}function G(){return C=D.length/(F-B),E=D.length-1,A}var C,E;return A.domain=function(H){return arguments.length?(B=+H[0],F=+H[H.length-1],G()):[B,F]},A.range=function(H){return arguments.length?(D=H,G()):D},A.invertExtent=function(H){return H=D.indexOf(H),H=0>H?0/0:H/C+B,[H,H+1/C]},A.copy=function(){return Gg(B,F,D)},G()}function GE(A,C){function B(D){return D>=D?C[DT.bisect(A,D)]:void 0}return B.domain=function(D){return arguments.length?(A=D,B):A},B.range=function(D){return arguments.length?(C=D,B):C},B.invertExtent=function(D){return D=C.indexOf(D),[A[D-1],A[D]]},B.copy=function(){return GE(A,C)},B}function Gs(A){function B(C){return +C}return B.invert=B,B.domain=B.range=function(C){return arguments.length?(A=C.map(B),B):A},B.ticks=function(C){return Gf(A,C)},B.tickFormat=function(D,C){return Gn(A,D,C)},B.copy=function(){return Gs(A)},B}function GF(A){return A.innerRadius}function Gh(A){return A.outerRadius}function GG(A){return A.startAngle}function Gk(A){return A.endAngle}function Gz(B){for(var E,D,A,F=-1,C=B.length;++F<C;){E=B[F],D=E[0],A=E[1]+IY,E[0]=D*Math.cos(A),E[1]=D*Math.sin(A)}return B}function F9(G){function D(Q){function W(){a.push("M",K(G(U),L),B,I(G(O.reverse()),L),"Z")}for(var S,Z,N,a=[],O=[],U=[],X=-1,R=Q.length,Y=HP(A),V=HP(E),T=A===C?function(){return Z}:HP(C),P=E===F?function(){return N}:HP(F);++X<R;){H.call(this,S=Q[X],X)?(O.push([Z=+Y.call(this,S,X),N=+V.call(this,S,X)]),U.push([+T.call(this,S,X),+P.call(this,S,X)])):O.length&&(W(),O=[],U=[])}return O.length&&W(),a.length?a.join(""):null}var A=Bp,C=Bp,E=0,F=BA,H=Hn,K=Bq,J=K.key,I=K,B="L",L=0.7;return D.x=function(M){return arguments.length?(A=C=M,D):C},D.x0=function(M){return arguments.length?(A=M,D):A},D.x1=function(M){return arguments.length?(C=M,D):C},D.y=function(M){return arguments.length?(E=F=M,D):F},D.y0=function(M){return arguments.length?(E=M,D):E},D.y1=function(M){return arguments.length?(F=M,D):F},D.defined=function(M){return arguments.length?(H=M,D):H},D.interpolate=function(M){return arguments.length?(J="function"==typeof M?K=M:(K=JG.get(M)||Bq).key,I=K.reverse||K,B=K.closed?"M":"L",D):J},D.tension=function(M){return arguments.length?(L=M,D):L},D}function FX(A){return A.radius}function F8(A){return[A.x,A.y]}function GA(A){return function(){var D=A.apply(this,arguments),C=D[0],B=D[1]+IY;return[C*Math.cos(B),C*Math.sin(B)]}}function FH(){return 64}function Gl(){return"circle"}function FJ(A){var B=Math.sqrt(A/Dq);return"M0,"+B+"A"+B+","+B+" 0 1,1 0,"+-B+"A"+B+","+B+" 0 1,1 0,"+B+"Z"}function F5(A,B){return Dn(A,IV),A.id=B,A}function Gq(B,D,C,A){var E=B.id;return CK(B,"function"==typeof C?function(G,F,H){G.__transition__[E].tween.set(D,A(C.call(G,G.__data__,F,H)))}:(C=A(C),function(F){F.__transition__[E].tween.set(D,C)}))}function F1(A){return null==A&&(A=""),function(){this.textContent=A}}function FU(B,F,D,A){var C=B.__transition__||(B.__transition__={active:0,count:0}),E=C[D];if(!E){var G=A.time;E=C[D]={tween:new E5,time:G,ease:A.ease,delay:A.delay,duration:A.duration},++C.count,DT.timer(function(H){function K(Q){return C.active>D?M():(C.active=D,E.event&&E.event.start.call(B,I,F),E.tween.forEach(function(S,R){(R=R.call(B,I,F))&&J.push(R)}),N(Q||1)?1:(HL(N,L,G),void 0))}function N(Q){if(C.active!==D){return M()}for(var S=Q/P,T=O(S),R=J.length;R>0;){J[--R].call(B,T)}return S>=1?(E.event&&E.event.end.call(B,I,F),M()):void 0}function M(){return --C.count?delete C[D]:delete B.__transition__,1}var I=B.__data__,O=E.ease,L=E.delay,P=E.duration,J=[];return H>=L?K(H-L):(HL(K,L,G),void 0)},0,G)}}function FO(A,B){A.attr("transform",function(C){return"translate("+B(C)+",0)"})}function Gj(A,B){A.attr("transform",function(C){return"translate(0,"+B(C)+")"})}function F6(){this._=new Date(arguments.length>1?Date.UTC.apply(this,arguments):arguments[0])}function FZ(F,C,A){function B(L){var K=F(L),J=E(K,1);return J-L>L-K?K:J}function D(J){return C(J=F(new IW(J-1)),1),J}function E(J,K){return C(J=new IW(+J),K),J}function G(K,J,L){var M=D(K),N=[];if(L>1){for(;J>M;){A(M)%L||N.push(new Date(+M)),C(M,1)}}else{for(;J>M;){N.push(new Date(+M)),C(M,1)}}return N}function I(K,M,L){try{IW=F6;var J=new F6;return J._=K,G(J,M,L)}finally{IW=Date}}F.floor=F,F.round=B,F.ceil=D,F.offset=E,F.range=G;var H=F.utc=F4(F);return H.floor=H,H.round=F4(B),H.ceil=F4(D),H.offset=F4(E),H.range=I,F}function F4(A){return function(D,C){try{IW=F6;var B=new F6;return B._=D,A(B,C)._}finally{IW=Date}}}function Gp(A){function C(H){for(var E,I,F,G=[],J=-1,D=0;++J<B;){37===A.charCodeAt(J)&&(G.push(A.substring(D,J)),null!=(I=IF[E=A.charAt(++J)])&&(E=A.charAt(++J)),(F=Il[E])&&(E=F(H,null==I?"e"===E?" ":"0":I)),G.push(E),D=J+1)}return G.push(A.substring(D,J)),G.join("")}var B=A.length;return C.parse=function(G){var E={y:1900,m:0,d:1,H:0,M:0,S:0,L:0,Z:null},D=FW(E,A,G,0);if(D!=G.length){return null}"p" in E&&(E.H=E.H%12+12*E.p);var H=null!=E.Z&&IW!==F6,F=new (H?F6:IW);return"j" in E?F.setFullYear(E.y,0,E.j):"w" in E&&("W" in E||"U" in E)?(F.setFullYear(E.y,0,1),F.setFullYear(E.y,0,"W" in E?(E.w+6)%7+7*E.W-(F.getDay()+5)%7:E.w+7*E.U-(F.getDay()+6)%7)):F.setFullYear(E.y,E.m,E.d),F.setHours(E.H+Math.floor(E.Z/100),E.M+E.Z%100,E.S,E.L),H?F._:F},C.toString=function(){return A},C}function FW(F,C,A,B){for(var D,E,G,J=0,I=C.length,H=A.length;I>J;){if(B>=H){return -1}if(D=C.charCodeAt(J++),37===D){if(G=C.charAt(J++),E=Iz[G in IF?C.charAt(J++):G],!E||(B=E(F,A,B))<0){return -1}}else{if(D!=A.charCodeAt(B++)){return -1}}}return B}function FS(A){return new RegExp("^(?:"+A.map(DT.requote).join("|")+")","i")}function F2(B){for(var D=new E5,C=-1,A=B.length;++C<A;){D.set(B[C].toLowerCase(),C)}return D}function FK(B,E,D){var A=0>B?"-":"",F=(A?-B:B)+"",C=F.length;return A+(D>C?new Array(D-C+1).join(E)+F:F)}function FV(B,D,C){IB.lastIndex=0;var A=IB.exec(D.substring(C));return A?(B.w=Iy.get(A[0].toLowerCase()),C+A[0].length):-1}function FI(B,D,C){Ix.lastIndex=0;var A=Ix.exec(D.substring(C));return A?(B.w=IC.get(A[0].toLowerCase()),C+A[0].length):-1}function FF(B,D,C){Ig.lastIndex=0;var A=Ig.exec(D.substring(C,C+1));return A?(B.w=+A[0],C+A[0].length):-1}function FT(B,D,C){Ig.lastIndex=0;var A=Ig.exec(D.substring(C));return A?(B.U=+A[0],C+A[0].length):-1}function FP(B,D,C){Ig.lastIndex=0;var A=Ig.exec(D.substring(C));return A?(B.W=+A[0],C+A[0].length):-1}function FD(B,D,C){Is.lastIndex=0;var A=Is.exec(D.substring(C));return A?(B.m=IE.get(A[0].toLowerCase()),C+A[0].length):-1}function FM(B,D,C){Ij.lastIndex=0;var A=Ij.exec(D.substring(C));return A?(B.m=ID.get(A[0].toLowerCase()),C+A[0].length):-1}function FE(A,C,B){return FW(A,Il.c.toString(),C,B)}function Ab(A,C,B){return FW(A,Il.x.toString(),C,B)}function FY(A,C,B){return FW(A,Il.X.toString(),C,B)}function FN(B,D,C){Ig.lastIndex=0;var A=Ig.exec(D.substring(C,C+4));return A?(B.y=+A[0],C+A[0].length):-1}function FQ(B,D,C){Ig.lastIndex=0;var A=Ig.exec(D.substring(C,C+2));return A?(B.y=FR(+A[0]),C+A[0].length):-1}function F3(A,C,B){return/^[+-]\d{4}$/.test(C=C.substring(B,B+5))?(A.Z=+C,B+5):-1}function FR(A){return A+(A>68?1900:2000)}function FL(B,D,C){Ig.lastIndex=0;var A=Ig.exec(D.substring(C,C+2));return A?(B.m=A[0]-1,C+A[0].length):-1}function DQ(B,D,C){Ig.lastIndex=0;var A=Ig.exec(D.substring(C,C+2));return A?(B.d=+A[0],C+A[0].length):-1}function DK(B,D,C){Ig.lastIndex=0;var A=Ig.exec(D.substring(C,C+3));return A?(B.j=+A[0],C+A[0].length):-1}function DZ(B,D,C){Ig.lastIndex=0;var A=Ig.exec(D.substring(C,C+2));return A?(B.H=+A[0],C+A[0].length):-1}function DE(B,D,C){Ig.lastIndex=0;var A=Ig.exec(D.substring(C,C+2));return A?(B.M=+A[0],C+A[0].length):-1}function DL(B,D,C){Ig.lastIndex=0;var A=Ig.exec(D.substring(C,C+2));return A?(B.S=+A[0],C+A[0].length):-1}function DP(B,D,C){Ig.lastIndex=0;var A=Ig.exec(D.substring(C,C+3));return A?(B.L=+A[0],C+A[0].length):-1}function DR(B,D,C){var A=H5.get(D.substring(C,C+=2).toLowerCase());return null==A?-1:(B.p=A,C)}function DW(B){var D=B.getTimezoneOffset(),C=D>0?"-":"+",A=~~(Math.abs(D)/60),E=Math.abs(D)%60;return C+FK(A,"0",2)+FK(E,"0",2)}function DV(B,D,C){Ik.lastIndex=0;var A=Ik.exec(D.substring(C,C+1));return A?C+A[0].length:-1}function DS(A){function C(D){try{IW=F6;var E=new IW;return E._=D,B(E)}finally{IW=Date}}var B=Gp(A);return C.parse=function(D){try{IW=F6;var E=B.parse(D);return E&&E._}finally{IW=Date}},C.toString=B.toString,C}function DF(A){return A.toISOString()}function DX(B,D,C){function A(F){return B(F)}function E(G,I){var F=G[1]-G[0],J=F/I,H=DT.bisect(IA,J);return H==IA.length?[D.year,GH(G.map(function(K){return K/31536000000}),I)[2]]:H?D[J/IA[H-1]<IA[H]/J?H-1:H]:[Id,GH(G,I)[2]]}return A.invert=function(F){return DO(B.invert(F))},A.domain=function(F){return arguments.length?(B.domain(F),A):B.domain().map(DO)},A.nice=function(G,J){function I(L){return !isNaN(L)&&!G.range(L,DO(+L+1),J).length}var F=A.domain(),H=Cf(F),K=null==G?E(H,10):"number"==typeof G&&E(H,G);return K&&(G=K[0],J=K[1]),A.domain(Ay(F,J>1?{floor:function(L){for(;I(L=G.floor(L));){L=DO(L-1)}return L},ceil:function(L){for(;I(L=G.ceil(L));){L=DO(+L+1)}return L}}:G))},A.ticks=function(G,I){var H=Cf(A.domain()),F=null==G?E(H,10):"number"==typeof G?E(H,G):!G.range&&[{range:G},I];return F&&(G=F[0],I=F[1]),G.range(H[0],DO(+H[1]+1),1>I?1:I)},A.tickFormat=function(){return C},A.copy=function(){return DX(B.copy(),D,C)},Gw(A,B)}function DO(A){return new Date(A)}function DY(A){return function(D){for(var C=A.length-1,B=A[C];!B[1](D);){B=A[--C]}return B[0](D)}}function DG(A){return JSON.parse(A.responseText)}function DI(A){var B=DC.createRange();return B.selectNode(DC.body),B.createContextualFragment(A.responseText)}var DT={version:"3.3.6"};Date.now||(Date.now=function(){return +new Date});var DD=[].slice,Dp=function(A){return DD.call(A)},DC=document,DU=DC.documentElement,C2=window;try{Dp(DU.childNodes)[0].nodeType}catch(DJ){Dp=function(A){for(var C=A.length,B=new Array(C);C--;){B[C]=A[C]}return B}}try{DC.createElement("div").style.setProperty("opacity",0,"")}catch(C6){var DA=C2.Element.prototype,DN=DA.setAttribute,Dw=DA.setAttributeNS,Dl=C2.CSSStyleDeclaration.prototype,Dd=Dl.setProperty;DA.setAttribute=function(A,B){DN.call(this,A,B+"")},DA.setAttributeNS=function(A,C,B){Dw.call(this,A,C,B+"")},Dl.setProperty=function(A,C,B){Dd.call(this,A,C+"",B)}}DT.ascending=function(A,B){return B>A?-1:A>B?1:A>=B?0:0/0},DT.descending=function(A,B){return A>B?-1:B>A?1:B>=A?0:0/0},DT.min=function(B,E){var D,A,F=-1,C=B.length;if(1===arguments.length){for(;++F<C&&!(null!=(D=B[F])&&D>=D);){D=void 0}for(;++F<C;){null!=(A=B[F])&&D>A&&(D=A)}}else{for(;++F<C&&!(null!=(D=E.call(B,B[F],F))&&D>=D);){D=void 0}for(;++F<C;){null!=(A=E.call(B,B[F],F))&&D>A&&(D=A)}}return D},DT.max=function(B,E){var D,A,F=-1,C=B.length;if(1===arguments.length){for(;++F<C&&!(null!=(D=B[F])&&D>=D);){D=void 0}for(;++F<C;){null!=(A=B[F])&&A>D&&(D=A)}}else{for(;++F<C&&!(null!=(D=E.call(B,B[F],F))&&D>=D);){D=void 0}for(;++F<C;){null!=(A=E.call(B,B[F],F))&&A>D&&(D=A)}}return D},DT.extent=function(B,F){var D,A,G,C=-1,E=B.length;if(1===arguments.length){for(;++C<E&&!(null!=(D=G=B[C])&&D>=D);){D=G=void 0}for(;++C<E;){null!=(A=B[C])&&(D>A&&(D=A),A>G&&(G=A))}}else{for(;++C<E&&!(null!=(D=G=F.call(B,B[C],C))&&D>=D);){D=void 0}for(;++C<E;){null!=(A=F.call(B,B[C],C))&&(D>A&&(D=A),A>G&&(G=A))}}return[D,G]},DT.sum=function(B,E){var D,A=0,F=B.length,C=-1;if(1===arguments.length){for(;++C<F;){isNaN(D=+B[C])||(A+=D)}}else{for(;++C<F;){isNaN(D=+E.call(B,B[C],C))||(A+=D)}}return A},DT.mean=function(E,B){var A,F=E.length,C=0,D=-1,G=0;if(1===arguments.length){for(;++D<F;){E9(A=E[D])&&(C+=(A-C)/++G)}}else{for(;++D<F;){E9(A=B.call(E,E[D],D))&&(C+=(A-C)/++G)}}return G?C:void 0},DT.quantile=function(B,E){var D=(B.length-1)*E+1,A=Math.floor(D),F=+B[A-1],C=D-A;return C?F+C*(B[A]-F):F},DT.median=function(B,A){return arguments.length>1&&(B=B.map(A)),B=B.filter(E9),B.length?DT.quantile(B.sort(DT.ascending),0.5):void 0},DT.bisector=function(A){return{left:function(E,C,B,F){for(arguments.length<3&&(B=0),arguments.length<4&&(F=E.length);F>B;){var D=B+F>>>1;A.call(E,E[D],D)<C?B=D+1:F=D}return B},right:function(E,C,B,F){for(arguments.length<3&&(B=0),arguments.length<4&&(F=E.length);F>B;){var D=B+F>>>1;C<A.call(E,E[D],D)?F=D:B=D+1}return B}}};var DH=DT.bisector(function(A){return A});DT.bisectLeft=DH.left,DT.bisect=DT.bisectRight=DH.right,DT.shuffle=function(B){for(var D,C,A=B.length;A;){C=0|Math.random()*A--,D=B[A],B[A]=B[C],B[C]=D}return B},DT.permute=function(B,D){for(var C=D.length,A=new Array(C);C--;){A[C]=B[D[C]]}return A},DT.pairs=function(B){for(var E,D=0,A=B.length-1,F=B[0],C=new Array(0>A?0:A);A>D;){C[D]=[E=F,F=B[++D]]}return C},DT.zip=function(){if(!(F=arguments.length)){return[]}for(var B=-1,D=DT.min(arguments,E4),A=new Array(D);++B<D;){for(var F,C=-1,E=A[B]=new Array(F);++C<F;){E[C]=arguments[C][B]}}return A},DT.transpose=function(A){return DT.zip.apply(DT,A)},DT.keys=function(A){var C=[];for(var B in A){C.push(B)}return C},DT.values=function(A){var C=[];for(var B in A){C.push(A[B])}return C},DT.entries=function(A){var C=[];for(var B in A){C.push({key:B,value:A[B]})}return C},DT.merge=function(A){return Array.prototype.concat.apply([],A)},DT.range=function(B,E,A){if(arguments.length<3&&(A=1,arguments.length<2&&(E=B,B=0)),1/0===(E-B)/A){throw new Error("infinite range")}var F,C=[],D=Dv(Math.abs(A)),G=-1;if(B*=D,E*=D,A*=D,0>A){for(;(F=B+A*++G)>E;){C.push(F/D)}}else{for(;(F=B+A*++G)<E;){C.push(F/D)}}return C},DT.map=function(A){var C=new E5;if(A instanceof E5){A.forEach(function(D,E){C.set(D,E)})}else{for(var B in A){C.set(B,A[B])}}return C},E1(E5,{has:function(A){return DB+A in this},get:function(A){return this[DB+A]},set:function(A,B){return this[DB+A]=B},remove:function(A){return A=DB+A,A in this&&delete this[A]},keys:function(){var A=[];return this.forEach(function(B){A.push(B)}),A},values:function(){var A=[];return this.forEach(function(C,B){A.push(B)}),A},entries:function(){var A=[];return this.forEach(function(C,B){A.push({key:C,value:B})}),A},forEach:function(A){for(var B in this){B.charCodeAt(0)===Ds&&A.call(this,B.substring(1),this[B])}}});var DB="\x00",Ds=DB.charCodeAt(0);DT.nest=function(){function B(K,O,N){if(N>=E.length){return A?A.call(C,O):D?O.sort(D):O}for(var M,H,P,L,Q=-1,I=O.length,R=E[N++],J=new E5;++Q<I;){(L=J.get(M=R(H=O[Q])))?L.push(H):J.set(M,[H])}return K?(H=K(),P=function(T,S){H.set(T,B(K,S,N))}):(H={},P=function(T,S){H[T]=B(K,S,N)}),J.forEach(P),H}function F(I,J){if(J>=E.length){return I}var H=[],K=G[J++];return I.forEach(function(L,M){H.push({key:L,values:F(M,J)})}),K?H.sort(function(L,M){return K(L.key,M.key)}):H}var D,A,C={},E=[],G=[];return C.map=function(I,H){return B(H,I,0)},C.entries=function(H){return F(B(DT.map,H,0),0)},C.key=function(H){return E.push(H),C},C.sortKeys=function(H){return G[E.length-1]=H,C},C.sortValues=function(H){return D=H,C},C.rollup=function(H){return A=H,C},C},DT.set=function(B){var D=new E8;if(B){for(var C=0,A=B.length;A>C;++C){D.add(B[C])}}return D},E1(E8,{has:function(A){return DB+A in this},add:function(A){return this[DB+A]=!0,A},remove:function(A){return A=DB+A,A in this&&delete this[A]},values:function(){var A=[];return this.forEach(function(B){A.push(B)}),A},forEach:function(A){for(var B in this){B.charCodeAt(0)===Ds&&A.call(this,B.substring(1))}}}),DT.behavior={},DT.rebind=function(B,D){for(var C,A=1,E=arguments.length;++A<E;){B[C=arguments[A]]=Fb(B,D,D[C])}return B};var Dz=["webkit","ms","moz","Moz","o","O"];DT.dispatch=function(){for(var A=new Dh,C=-1,B=arguments.length;++C<B;){A[arguments[C]]=C1(A)}return A},Dh.prototype.on=function(B,D){var C=B.indexOf("."),A="";if(C>=0&&(A=B.substring(C+1),B=B.substring(0,C)),B){return arguments.length<2?this[B].on(A):this[B].on(A,D)}if(2===arguments.length){if(null==D){for(B in this){this.hasOwnProperty(B)&&this[B].on(A,null)}}return this}},DT.event=null,DT.requote=function(A){return A.replace(DM,"\\$&")};var DM=/[\\\^\$\*\+\?\|\[\]\(\)\.\{\}]/g,Dn={}.__proto__?function(A,B){A.__proto__=B}:function(A,C){for(var B in C){A[B]=C[B]}},Dj=function(A,B){return B.querySelector(A)},Dx=function(A,B){return B.querySelectorAll(A)},C7=DU[Fh(DU,"matchesSelector")],Dm=function(A,B){return C7.call(A,B)};"function"==typeof Sizzle&&(Dj=function(A,B){return Sizzle(A,B)[0]||null},Dx=function(A,B){return Sizzle.uniqueSort(Sizzle(A,B))},Dm=Sizzle.matchesSelector),DT.selection=function(){return Df};var C5=DT.selection.prototype=[];C5.select=function(F){var C,A,B,D,E=[];F=Fl(F);for(var G=-1,J=this.length;++G<J;){E.push(C=[]),C.parentNode=(B=this[G]).parentNode;for(var I=-1,H=B.length;++I<H;){(D=B[I])?(C.push(A=F.call(D,D.__data__,I,G)),A&&"__data__" in D&&(A.__data__=D.__data__)):C.push(null)}}return E2(E)},C5.selectAll=function(F){var C,A,B=[];F=C4(F);for(var D=-1,E=this.length;++D<E;){for(var G=this[D],I=-1,H=G.length;++I<H;){(A=G[I])&&(B.push(C=Dp(F.call(A,A.__data__,I,D))),C.parentNode=A)}}return E2(B)};var C0={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};DT.ns={prefix:C0,qualify:function(A){var C=A.indexOf(":"),B=A;return C>=0&&(B=A.substring(0,C),A=A.substring(C+1)),C0.hasOwnProperty(B)?{space:C0[B],local:A}:A}},C5.attr=function(A,C){if(arguments.length<2){if("string"==typeof A){var B=this.node();return A=DT.ns.qualify(A),A.local?B.getAttributeNS(A.space,A.local):B.getAttribute(A)}for(C in A){this.each(Fd(C,A[C]))}return this}return this.each(Fd(A,C))},C5.classed=function(B,D){if(arguments.length<2){if("string"==typeof B){var C=this.node(),A=(B=B.trim().split(/^|\s+/g)).length,E=-1;if(D=C.classList){for(;++E<A;){if(!D.contains(B[E])){return !1}}}else{for(D=C.getAttribute("class");++E<A;){if(!CN(B[E]).test(D)){return !1}}}return !0}for(D in B){this.each(CW(D,B[D]))}return this}return this.each(CW(B,D))},C5.style=function(B,D,C){var A=arguments.length;if(3>A){if("string"!=typeof B){2>A&&(D="");for(C in B){this.each(Cx(C,B[C],D))}return this}if(2>A){return C2.getComputedStyle(this.node(),null).getPropertyValue(B)}C=""}return this.each(Cx(B,D,C))},C5.property=function(A,B){if(arguments.length<2){if("string"==typeof A){return this.node()[A]}for(B in A){this.each(E3(B,A[B]))}return this}return this.each(E3(A,B))},C5.text=function(A){return arguments.length?this.each("function"==typeof A?function(){var B=A.apply(this,arguments);this.textContent=null==B?"":B}:null==A?function(){this.textContent=""}:function(){this.textContent=A}):this.node().textContent},C5.html=function(A){return arguments.length?this.each("function"==typeof A?function(){var B=A.apply(this,arguments);this.innerHTML=null==B?"":B}:null==A?function(){this.innerHTML=""}:function(){this.innerHTML=A}):this.node().innerHTML},C5.append=function(A){return A=Cz(A),this.select(function(){return this.appendChild(A.apply(this,arguments))})},C5.insert=function(A,B){return A=Cz(A),B=Fl(B),this.select(function(){return this.insertBefore(A.apply(this,arguments),B.apply(this,arguments))})},C5.remove=function(){return this.each(function(){var A=this.parentNode;A&&A.removeChild(this)})},C5.data=function(F,D){function A(S,K){var N,R,T,W=S.length,X=K.length,Q=Math.min(W,X),Y=new Array(X),L=new Array(X),Z=new Array(W);if(D){var O,U=new E5,V=new E5,P=[];for(N=-1;++N<W;){O=D.call(R=S[N],R.__data__,N),U.has(O)?Z[N]=R:U.set(O,R),P.push(O)}for(N=-1;++N<X;){O=D.call(K,T=K[N],N),(R=U.get(O))?(Y[N]=R,R.__data__=T):V.has(O)||(L[N]=CU(T)),V.set(O,T),U.remove(O)}for(N=-1;++N<W;){U.has(P[N])&&(Z[N]=S[N])}}else{for(N=-1;++N<Q;){R=S[N],T=K[N],R?(R.__data__=T,Y[N]=R):L[N]=CU(T)}for(;X>N;++N){L[N]=CU(K[N])}for(;W>N;++N){Z[N]=S[N]}}L.update=Y,L.parentNode=Y.parentNode=Z.parentNode=S.parentNode,I.push(L),H.push(Y),B.push(Z)}var C,E,G=-1,J=this.length;if(!arguments.length){for(F=new Array(J=(C=this[0]).length);++G<J;){(E=C[G])&&(F[G]=E.__data__)}return F}var I=CE([]),H=E2([]),B=E2([]);if("function"==typeof F){for(;++G<J;){A(C=this[G],F.call(C,C.parentNode.__data__,G))}}else{for(;++G<J;){A(C=this[G],F)}}return H.enter=function(){return I},H.exit=function(){return B},H},C5.datum=function(A){return arguments.length?this.property("__data__",A):this.property("__data__")},C5.filter=function(F){var C,A,B,D=[];"function"!=typeof F&&(F=E6(F));for(var E=0,G=this.length;G>E;E++){D.push(C=[]),C.parentNode=(A=this[E]).parentNode;for(var I=0,H=A.length;H>I;I++){(B=A[I])&&F.call(B,B.__data__,I)&&C.push(B)}}return E2(D)},C5.order=function(){for(var B=-1,E=this.length;++B<E;){for(var D,A=this[B],F=A.length-1,C=A[F];--F>=0;){(D=A[F])&&(C&&C!==D.nextSibling&&C.parentNode.insertBefore(D,C),C=D)}}return this},C5.sort=function(A){A=CQ.apply(this,arguments);for(var C=-1,B=this.length;++C<B;){this[C].sort(A)}return this.order()},C5.each=function(A){return CK(this,function(D,C,B){A.call(D,D.__data__,C,B)})},C5.call=function(A){var B=Dp(arguments);return A.apply(B[0]=this,B),this},C5.empty=function(){return !this.node()},C5.node=function(){for(var B=0,E=this.length;E>B;B++){for(var D=this[B],A=0,F=D.length;F>A;A++){var C=D[A];if(C){return C}}}return null},C5.size=function(){var A=0;return this.each(function(){++A}),A};var Dk=[];DT.selection.enter=CE,DT.selection.enter.prototype=Dk,Dk.append=C5.append,Dk.empty=C5.empty,Dk.node=C5.node,Dk.call=C5.call,Dk.size=C5.size,Dk.select=function(G){for(var D,A,C,E,F,H=[],K=-1,J=this.length;++K<J;){C=(E=this[K]).update,H.push(D=[]),D.parentNode=E.parentNode;for(var I=-1,B=E.length;++I<B;){(F=E[I])?(D.push(C[I]=A=G.call(E.parentNode,F.__data__,I,K)),A.__data__=F.__data__):D.push(null)}}return E2(H)},Dk.insert=function(A,B){return arguments.length<2&&(B=C3(this)),C5.insert.call(this,A,B)},C5.transition=function(){for(var F,C,A=I1||++IK,B=[],D=IO||{time:Date.now(),ease:Ev,delay:0,duration:250},E=-1,G=this.length;++E<G;){B.push(F=[]);for(var J=this[E],I=-1,H=J.length;++I<H;){(C=J[I])&&FU(C,I,A,D),F.push(C)}}return F5(B,A)},C5.interrupt=function(){return this.each(CV)},DT.select=function(A){var B=["string"==typeof A?Dj(A,DC):A];return B.parentNode=DU,E2([B])},DT.selectAll=function(A){var B=Dp("string"==typeof A?Dx(A,DC):A);return B.parentNode=DU,E2([B])};var Df=DT.select(DU);C5.on=function(B,D,C){var A=arguments.length;if(3>A){if("string"!=typeof B){2>A&&(D=!1);for(C in B){this.each(CP(C,B[C],D))}return this}if(2>A){return(A=this.node()["__on"+B])&&A._}C=!1}return this.each(CP(B,D,C))};var CY=DT.map({mouseenter:"mouseover",mouseleave:"mouseout"});CY.forEach(function(A){"on"+A in DC&&CY.remove(A)});var C9=Fh(DU.style,"userSelect"),CZ=0;DT.mouse=function(A){return CI(A,E7())};var EI=/WebKit/.test(C2.navigator.userAgent)?-1:0;DT.touches=function(A,B){return arguments.length<2&&(B=E7().touches),B?Dp(B).map(function(D){var C=CI(A,D);return C.identifier=D.identifier,C}):[]},DT.behavior.drag=function(){function F(){this.on("mousedown.drag",G).on("touchstart.drag",H)}function C(){return DT.event.changedTouches[0].identifier}function A(I,J){return DT.touches(I).filter(function(K){return K.identifier===J})[0]}function B(J,L,K,I){return function(){function Q(){var a=L(M,X),b=a[0]-Y[0],Z=a[1]-Y[1];O|=b|Z,Y=a,W({type:"drag",x:a[0]+T[0],y:a[1]+T[1],dx:b,dy:Z})}function V(){S.on(K+"."+N,null).on(I+"."+N,null),U(O&&DT.event.target===P),W({type:"dragend"})}var T,R=this,M=R.parentNode,W=D.of(R,arguments),P=DT.event.target,X=J(),N=null==X?"drag":"drag-"+X,Y=L(M,X),O=0,S=DT.select(C2).on(K+"."+N,Q).on(I+"."+N,V),U=CM();E?(T=E.apply(R,arguments),T=[T.x-Y[0],T.y-Y[1]]):T=[0,0],W({type:"dragstart"})}}var D=Fk(F,"drag","dragstart","dragend"),E=null,G=B(Fg,DT.mouse,"mousemove","mouseup"),H=B(C,A,"touchmove","touchend");return F.origin=function(I){return arguments.length?(E=I,F):E},DT.rebind(F,D,"on")};var Dq=Math.PI,Db=2*Dq,Dg=Dq/2,Dy=1e-06,Fp=Dy*Dy,Fn=Dq/180,G0=180/Dq,GU=Math.SQRT2,Hb=2,GO=4;DT.interpolateZoom=function(I,E){function R(T){var W=T*S;if(L){var V=Cw(Q),U=H/(Hb*G)*(V*CJ(GU*W+Q)-Cy(Q));return[A+U*K,F+U*B,H*V/Cw(GU*W+Q)]}return[A+T*K,F+T*B,H*Math.exp(GU*W)]}var A=I[0],F=I[1],H=I[2],J=E[0],N=E[1],M=E[2],K=J-A,B=N-F,O=K*K+B*B,G=Math.sqrt(O),P=(M*M-H*H+GO*O)/(2*H*Hb*G),C=(M*M-H*H-GO*O)/(2*M*Hb*G),Q=Math.log(Math.sqrt(P*P+1)-P),D=Math.log(Math.sqrt(C*C+1)-C),L=D-Q,S=(L||Math.log(M/H))/GU;return R.duration=1000*S,R},DT.behavior.zoom=function(){function Q(A){A.on(Ac,U).on(G1+".zoom",O).on(j,F).on("dblclick.zoom",f).on(G,D)}function J(A){return[(A[0]-Ar.x)/Ar.k,(A[1]-Ar.y)/Ar.k]}function g(A){return[A[0]*Ar.k+Ar.x,A[1]*Ar.k+Ar.y]}function B(A){Ar.k=Math.max(L[0],Math.min(L[1],A))}function K(A,C){C=g(C),Ar.x+=A[0]-C[0],Ar.y+=A[1]-C[1]}function P(){W&&W.domain(Al.range().map(function(A){return(A-Ar.x)/Ar.k}).map(Al.invert)),I&&I.domain(Ai.range().map(function(A){return(A-Ar.y)/Ar.k}).map(Ai.invert))}function R(A){A({type:"zoomstart"})}function Y(A){P(),A({type:"zoom",scale:Ar.k,translate:[Ar.x,Ar.y]})}function X(A){A({type:"zoomend"})}function U(){function S(){C=1,K(DT.mouse(E),M),Y(N)}function A(){a.on(j,C2===E?F:null).on(Z,null),b(C&&DT.event.target===T),X(N)}var E=this,N=Aa.of(E,arguments),T=DT.event.target,C=0,a=DT.select(C2).on(j,S).on(Z,A),M=J(DT.mouse(E)),b=CM();CV.call(E),R(N)}function D(){function a(){var E=DT.touches(C);return s=Ar.k,E.forEach(function(b){b.identifier in M&&(M[b.identifier]=J(b))}),E}function A(){for(var m=DT.event.changedTouches,E=0,u=m.length;u>E;++E){M[m[E].identifier]=null}var v=a(),x=Date.now();if(1===v.length){if(500>x-z){var w=v[0],b=M[w.identifier];B(2*Ar.k),K(w,b),Fj(),Y(t)}z=x}else{if(v.length>1){var w=v[0],n=v[1],y=w[0]-n[0],d=w[1]-n[1];l=y*y+d*d}}}function T(){for(var m,d,E,h,p=DT.touches(C),v=0,u=p.length;u>v;++v,h=null){if(E=p[v],h=M[E.identifier]){if(d){break}m=E,d=h}}if(h){var b=(b=E[0]-m[0])*b+(b=E[1]-m[1])*b,w=l&&Math.sqrt(b/l);m=[(m[0]+E[0])/2,(m[1]+E[1])/2],d=[(d[0]+h[0])/2,(d[1]+h[1])/2],B(w*s)}z=null,K(m,d),Y(t)}function S(){if(DT.event.touches.length){for(var d=DT.event.changedTouches,b=0,E=d.length;E>b;++b){delete M[d[b].identifier]}for(var h in M){return void a()}}c.on(q,null).on(o,null),N.on(Ac,U).on(G,D),k(),X(t)}var s,C=this,t=Aa.of(C,arguments),M={},l=0,r=DT.event.changedTouches[0].identifier,q="touchmove.zoom-"+r,o="touchend.zoom-"+r,c=DT.select(C2).on(q,T).on(o,S),N=DT.select(C).on(Ac,null).on(G,A),k=CM();CV.call(C),A(),R(t)}function O(){var A=Aa.of(this,arguments);Ao?clearTimeout(Ao):(CV.call(this),R(A)),Ao=setTimeout(function(){Ao=null,X(A)},50),Fj();var C=V||DT.mouse(this);H||(H=J(C)),B(Math.pow(2,0.002*GV())*Ar.k),K(C,H),Y(A)}function F(){H=null}function f(){var C=Aa.of(this,arguments),E=DT.mouse(this),A=J(E),M=Math.log(Ar.k)/Math.LN2;R(C),B(Math.pow(2,DT.event.shiftKey?Math.ceil(M)-1:Math.floor(M)+1)),K(E,A),Y(C),X(C)}var H,V,Ao,z,Al,W,Ai,I,Ar={x:0,y:0,k:1},Ae=[960,500],L=GZ,Ac="mousedown.zoom",j="mousemove.zoom",Z="mouseup.zoom",G="touchstart.zoom",Aa=Fk(Q,"zoomstart","zoom","zoomend");return Q.event=function(A){A.each(function(){var C=Aa.of(this,arguments),E=Ar;I1?DT.select(this).transition().each("start.zoom",function(){Ar=this.__chart__||{x:0,y:0,k:1},R(C)}).tween("zoom:zoom",function(){var N=Ae[0],M=Ae[1],a=N/2,S=M/2,T=DT.interpolateZoom([(a-Ar.x)/Ar.k,(S-Ar.y)/Ar.k,N/Ar.k],[(a-E.x)/E.k,(S-E.y)/E.k,N/E.k]);return function(d){var b=T(d),e=N/b[2];this.__chart__=Ar={x:a-b[0]*e,y:S-b[1]*e,k:e},Y(C)}}).each("end.zoom",function(){X(C)}):(this.__chart__=Ar,R(C),Y(C),X(C))})},Q.translate=function(A){return arguments.length?(Ar={x:+A[0],y:+A[1],k:Ar.k},P(),Q):[Ar.x,Ar.y]},Q.scale=function(A){return arguments.length?(Ar={x:Ar.x,y:Ar.y,k:+A},P(),Q):Ar.k},Q.scaleExtent=function(A){return arguments.length?(L=null==A?GZ:[+A[0],+A[1]],Q):L},Q.center=function(A){return arguments.length?(V=A&&[+A[0],+A[1]],Q):V},Q.size=function(A){return arguments.length?(Ae=A&&[+A[0],+A[1]],Q):Ae},Q.x=function(A){return arguments.length?(W=A,Al=A.copy(),Ar={x:0,y:0,k:1},Q):W},Q.y=function(A){return arguments.length?(I=A,Ai=A.copy(),Ar={x:0,y:0,k:1},Q):I},DT.rebind(Q,Aa,"on")};var GV,GZ=[0,1/0],G1="onwheel" in DC?(GV=function(){return -DT.event.deltaY*(DT.event.deltaMode?120:1)},"wheel"):"onmousewheel" in DC?(GV=function(){return DT.event.wheelDelta},"mousewheel"):(GV=function(){return -DT.event.detail},"MozMousePixelScroll");Cs.prototype.toString=function(){return this.rgb()+""},DT.hsl=function(A,C,B){return 1===arguments.length?A instanceof Cv?CC(A.h,A.s,A.l):H1(""+A,HO,CC):CC(+A,+C,+B)};var G6=Cv.prototype=new Cs;G6.brighter=function(A){return A=Math.pow(0.7,arguments.length?A:1),CC(this.h,this.s,this.l/A)},G6.darker=function(A){return A=Math.pow(0.7,arguments.length?A:1),CC(this.h,this.s,A*this.l)},G6.rgb=function(){return D4(this.h,this.s,this.l)},DT.hcl=function(A,C,B){return 1===arguments.length?A instanceof CD?CO(A.h,A.c,A.l):A instanceof CH?HZ(A.l,A.a,A.b):HZ((A=H7((A=DT.rgb(A)).r,A.g,A.b)).l,A.a,A.b):CO(+A,+C,+B)};var G5=CD.prototype=new Cs;G5.brighter=function(A){return CO(this.h,this.c,Math.min(100,this.l+G2*(arguments.length?A:1)))},G5.darker=function(A){return CO(this.h,this.c,Math.max(0,this.l-G2*(arguments.length?A:1)))},G5.rgb=function(){return CG(this.h,this.c,this.l).rgb()},DT.lab=function(A,C,B){return 1===arguments.length?A instanceof CH?CS(A.l,A.a,A.b):A instanceof CD?CG(A.l,A.c,A.h):H7((A=DT.rgb(A)).r,A.g,A.b):CS(+A,+C,+B)};var G2=18,GP=0.95047,G7=1,GY=1.08883,G8=CH.prototype=new Cs;G8.brighter=function(A){return CS(Math.min(100,this.l+G2*(arguments.length?A:1)),this.a,this.b)},G8.darker=function(A){return CS(Math.max(0,this.l-G2*(arguments.length?A:1)),this.a,this.b)},G8.rgb=function(){return CB(this.l,this.a,this.b)},DT.rgb=function(A,C,B){return 1===arguments.length?A instanceof H6?H0(A.r,A.g,A.b):H1(""+A,H0,D4):H0(~~A,~~C,~~B)};var GQ=H6.prototype=new Cs;GQ.brighter=function(B){B=Math.pow(0.7,arguments.length?B:1);var D=this.r,C=this.g,A=this.b,E=30;return D||C||A?(D&&E>D&&(D=E),C&&E>C&&(C=E),A&&E>A&&(A=E),H0(Math.min(255,~~(D/B)),Math.min(255,~~(C/B)),Math.min(255,~~(A/B)))):H0(E,E,E)},GQ.darker=function(A){return A=Math.pow(0.7,arguments.length?A:1),H0(~~(A*this.r),~~(A*this.g),~~(A*this.b))},GQ.hsl=function(){return HO(this.r,this.g,this.b)},GQ.toString=function(){return"#"+H4(this.r)+H4(this.g)+H4(this.b)};var G9=DT.map({aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074});G9.forEach(function(A,B){G9.set(A,HU(B))}),DT.functor=HP,DT.xhr=HR(H9),DT.dsv=function(E,C){function A(K,M,J){arguments.length<3&&(J=M,M=null);var L=DT.xhr(K,C,J);return L.row=function(N){return arguments.length?L.response(null==(M=N)?B:D(N)):M},L.row(M)}function B(J){return A.parse(J.responseText)}function D(J){return function(K){return A.parse(K.responseText,J)}}function F(J){return J.map(I).join(E)}function I(J){return H.test(J)?'"'+J.replace(/\"/g,'""')+'"':J}var H=new RegExp('["'+E+"\n]"),G=E.charCodeAt(0);return A.parse=function(K,L){var J;return A.parseRows(K,function(M,N){if(J){return J(M,N-1)}var O=new Function("d","return {"+M.map(function(P,Q){return JSON.stringify(P)+": d["+Q+"]"}).join(",")+"}");J=L?function(P,Q){return L(O(P),Q)}:O})},A.parseRows=function(Q,M){function J(){if(K>=S){return R}if(N){return N=!1,P}var X=K;if(34===Q.charCodeAt(X)){for(var W=X;W++<S;){if(34===Q.charCodeAt(W)){if(34!==Q.charCodeAt(W+1)){break}++W}}K=W+2;var V=Q.charCodeAt(W+1);return 13===V?(N=!0,10===Q.charCodeAt(W+2)&&++K):10===V&&(N=!0),Q.substring(X+1,W).replace(/""/g,'"')}for(;S>K;){var V=Q.charCodeAt(K++),Y=1;if(10===V){N=!0}else{if(13===V){N=!0,10===Q.charCodeAt(K)&&(++K,++Y)}else{if(V!==G){continue}}}return Q.substring(X,K-Y)}return Q.substring(X)}for(var L,N,P={},R={},T=[],S=Q.length,K=0,U=0;(L=J())!==R;){for(var O=[];L!==P&&L!==R;){O.push(L),L=J()}(!M||(O=M(O,U++)))&&T.push(O)}return T},A.format=function(K){if(Array.isArray(K[0])){return A.formatRows(K)}var J=new E8,L=[];return K.forEach(function(M){for(var N in M){J.has(N)||L.push(J.add(N))}}),[L.map(I).join(E)].concat(K.map(function(M){return L.map(function(N){return I(M[N])}).join(E)})).join("\n")},A.formatRows=function(J){return J.map(F).join("\n")},A},DT.csv=DT.dsv(",","text/csv"),DT.tsv=DT.dsv("	","text/tab-separated-values");var GS,G3,GN,GC,GM,G4=C2[Fh(C2,"requestAnimationFrame")]||function(A){setTimeout(A,17)};DT.timer=function(B,E,D){var A=arguments.length;2>A&&(E=0),3>A&&(D=Date.now());var F=D+E,C={callback:B,time:F,next:null};G3?G3.next=C:GS=C,G3=C,GN||(GC=clearTimeout(GC),GN=1,G4(HC))},DT.timer.flush=function(){H3(),Hh()};var Fw=".",GT=",",Fy=[3,3],GK="$",GX=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"].map(HS);DT.formatPrefix=function(A,C){var B=0;return A&&(0>A&&(A*=-1),C&&(A=DT.round(A,Hk(A,C))),B=1+Math.floor(1e-12+Math.log(A)/Math.LN10),B=Math.max(-24,Math.min(24,3*Math.floor((0>=B?B+1:B-1)/3)))),GX[8+B/3]},DT.round=function(A,B){return B?Math.round(A*(B=Math.pow(10,B)))/B:Math.round(A)},DT.format=function(I){var E=Bm.exec(I),A=E[1]||" ",D=E[2]||">",F=E[3]||"",H=E[4]||"",J=E[5],M=+E[6],L=E[7],K=E[8],B=E[9],N=1,G="",O=!1;switch(K&&(K=+K.substring(1)),(J||"0"===A&&"="===D)&&(J=A="0",D="=",L&&(M-=Math.floor((M-1)/4))),B){case"n":L=!0,B="g";break;case"%":N=100,G="%",B="f";break;case"p":N=100,G="%",B="r";break;case"b":case"o":case"x":case"X":"#"===H&&(H="0"+B.toLowerCase());case"c":case"d":O=!0,K=0;break;case"s":N=-1,B="r"}"#"===H?H="":"$"===H&&(H=GK),"r"!=B||K||(B="g"),null!=K&&("g"==B?K=Math.max(1,Math.min(21,K)):("e"==B||"f"==B)&&(K=Math.max(0,Math.min(20,K)))),B=AR.get(B)||HJ;var C=J&&L;return function(S){if(O&&S%1){return""}var Q=0>S||0===S&&0>1/S?(S=-S,"-"):F;if(0>N){var W=DT.formatPrefix(S,K);S=W.scale(S),G=W.symbol}else{S*=N}S=B(S,K);var P=S.lastIndexOf("."),T=0>P?S:S.substring(0,P),V=0>P?"":Fw+S.substring(P+1);!J&&L&&(T=FC(T));var R=H.length+T.length+V.length+(C?0:Q.length),U=M>R?new Array(R=M-R+1).join(A):"";return C&&(T=FC(U+T)),Q+=H,S=T+V,("<"===D?Q+S+U:">"===D?U+Q+S:"^"===D?U.substring(0,R>>=1)+Q+S+U.substring(R):Q+(C?S:U+S))+G}};var Bm=/(?:([^{])?([<>=^]))?([+\- ])?([$#])?(0)?(\d+)?(,)?(\.-?\d+)?([a-z%])?/i,AR=DT.map({b:function(A){return A.toString(2)},c:function(A){return String.fromCharCode(A)},o:function(A){return A.toString(8)},x:function(A){return A.toString(16)},X:function(A){return A.toString(16).toUpperCase()},g:function(A,B){return A.toPrecision(B)},e:function(A,B){return A.toExponential(B)},f:function(A,B){return A.toFixed(B)},r:function(A,B){return(A=DT.round(A,Hk(A,B))).toFixed(Math.max(0,Math.min(20,Hk(A*(1+1e-15),B))))}}),FC=H9;if(Fy){var GR=Fy.length;FC=function(B){for(var D=B.length,C=[],A=0,E=Fy[0];D>0&&E>0;){C.push(B.substring(D-=E,D+E)),E=Fy[A=(A+1)%GR]}return C.reverse().join(GT)}}DT.geo={},HW.prototype={s:0,t:0,add:function(A){HF(A,this.t,GL),HF(GL.s,this.s,this),this.s?this.t+=GL.t:this.s=GL.t},reset:function(){this.s=this.t=0},valueOf:function(){return this.s}};var GL=new HW;DT.geo.stream=function(A,B){A&&Bl.hasOwnProperty(A.type)?Bl[A.type](A,B):Hz(A,B)};var Bl={Feature:function(A,B){Hz(A.geometry,B)},FeatureCollection:function(B,D){for(var C=B.features,A=-1,E=C.length;++A<E;){Hz(C[A].geometry,D)}}},GJ={Sphere:function(A,B){B.sphere()},Point:function(A,B){A=A.coordinates,B.point(A[0],A[1],A[2])},MultiPoint:function(B,D){for(var C=B.coordinates,A=-1,E=C.length;++A<E;){B=C[A],D.point(B[0],B[1],B[2])}},LineString:function(A,B){Hq(A.coordinates,B,0)},MultiLineString:function(B,D){for(var C=B.coordinates,A=-1,E=C.length;++A<E;){Hq(C[A],D,0)}},Polygon:function(A,B){HQ(A.coordinates,B)},MultiPolygon:function(B,D){for(var C=B.coordinates,A=-1,E=C.length;++A<E;){HQ(C[A],D)}},GeometryCollection:function(B,D){for(var C=B.geometries,A=-1,E=C.length;++A<E;){Hz(C[A],D)}}};DT.geo.area=function(A){return GW=0,DT.geo.stream(A,Bb),GW};var GW,Fv=new HW,Bb={sphere:function(){GW+=4*Dq},point:Fg,lineStart:Fg,lineEnd:Fg,polygonStart:function(){Fv.reset(),Bb.lineStart=HK},polygonEnd:function(){var A=2*Fv;GW+=0>A?4*Dq+A:A,Bb.lineStart=Bb.lineEnd=Bb.point=Fg}};DT.geo.bounds=function(){function I(M,X){U.push(V=[B=M,G=M]),Q>X&&(Q=X),X>R&&(R=X)}function E(Z,M){var X=HE([Z*Fn,M*Fn]);if(L){var a=HV(L,X),b=[a[1],-a[0],0],f=HV(b,a);HG(f),f=Hl(f);var h=Z-C,g=h>0?1:-1,k=f[0]*G0*g,Y=Math.abs(h)>180;if(Y^(k>g*C&&g*Z>k)){var j=f[1]*G0;j>R&&(R=j)}else{if(k=(k+360)%360-180,Y^(k>g*C&&g*Z>k)){var j=-f[1]*G0;Q>j&&(Q=j)}else{Q>M&&(Q=M),M>R&&(R=M)}}Y?C>Z?P(B,Z)>P(B,G)&&(G=Z):P(Z,G)>P(B,G)&&(B=Z):G>=B?(B>Z&&(B=Z),Z>G&&(G=Z)):Z>C?P(B,Z)>P(B,G)&&(G=Z):P(Z,G)>P(B,G)&&(B=Z)}else{I(Z,M)}L=X,C=Z}function T(){N.point=E}function A(){V[0]=B,V[1]=G,N.point=I,L=null}function F(X,Y){if(L){var M=X-C;W+=Math.abs(M)>180?M+(M>0?360:-360):M}else{S=X,D=Y}Bb.point(X,Y),E(X,Y)}function H(){Bb.lineStart()}function J(){F(S,D),Bb.lineEnd(),Math.abs(W)>Dy&&(B=-(G=180)),V[0]=B,V[1]=G,L=null}function P(M,X){return(X-=M)<0?X+360:X}function O(M,X){return M[0]-X[0]}function K(M,X){return X[0]<=X[1]?X[0]<=M&&M<=X[1]:M<X[0]||X[1]<M}var B,Q,G,R,C,S,D,L,W,U,V,N={point:I,lineStart:T,lineEnd:A,polygonStart:function(){N.point=F,N.lineStart=H,N.lineEnd=J,W=0,Bb.polygonStart()},polygonEnd:function(){Bb.polygonEnd(),N.point=I,N.lineStart=T,N.lineEnd=A,0>Fv?(B=-(G=180),Q=-(R=90)):W>Dy?R=90:-Dy>W&&(Q=-90),V[0]=B,V[1]=G}};return function(c){R=G=-(B=Q=1/0),U=[],DT.geo.stream(c,N);var Z=U.length;if(Z){U.sort(O);for(var M,Y=1,a=U[0],b=[a];Z>Y;++Y){M=U[Y],K(M[0],a)||K(M[1],a)?(P(a[0],M[1])>P(a[0],a[1])&&(a[1]=M[1]),P(M[0],a[1])>P(a[0],a[1])&&(a[0]=M[0])):b.push(a=M)}for(var d,M,X=-1/0,Z=b.length-1,Y=0,a=b[Z];Z>=Y;a=M,++Y){M=b[Y],(d=P(a[1],M[0]))>X&&(X=d,B=M[0],G=a[1])}}return U=V=null,1/0===B||1/0===Q?[[0/0,0/0],[0/0,0/0]]:[[B,Q],[G,R]]}}(),DT.geo.centroid=function(B){Bn=Fz=F7=Fx=Gd=FG=Fq=Fs=FA=Gb=Cn=0,DT.geo.stream(B,Bk);var D=FA,C=Gb,A=Cn,E=D*D+C*C+A*A;return Fp>E&&(D=FG,C=Fq,A=Fs,Dy>Fz&&(D=F7,C=Fx,A=Gd),E=D*D+C*C+A*A,Fp>E)?[0/0,0/0]:[Math.atan2(C,D)*G0,CL(A/Math.sqrt(E))*G0]};var Bn,Fz,F7,Fx,Gd,FG,Fq,Fs,FA,Gb,Cn,Bk={sphere:Fg,point:Hj,lineStart:Hy,lineEnd:Hs,polygonStart:function(){Bk.lineStart=Hd},polygonEnd:function(){Bk.lineStart=Hy}},FB=HD(Hn,Hm,B2,[-Dq,-Dq/2]),F0=1000000000;DT.geo.clipExtent=function(){var B,F,D,A,G,C,E={stream:function(H){return G&&(G.valid=!1),G=C(H),G.valid=!0,G},extent:function(H){return arguments.length?(C=BW(B=+H[0][0],F=+H[0][1],D=+H[1][0],A=+H[1][1]),G&&(G.valid=!1,G=null),E):[[B,F],[D,A]]}};return E.extent([[0,0],[960,500]])},(DT.geo.conicEqualArea=function(){return B9(Ch)}).raw=Ch,DT.geo.albers=function(){return DT.geo.conicEqualArea().rotate([96,0]).center([-0.6,38.7]).parallels([29.5,45.5]).scale(1070)},DT.geo.albersUsa=function(){function F(K){var J=K[0],L=K[1];return C=null,A(J,L),C||(B(J,L),C)||D(J,L),C}var C,A,B,D,E=DT.geo.albers(),G=DT.geo.conicEqualArea().rotate([154,0]).center([-2,58.5]).parallels([55,65]),I=DT.geo.conicEqualArea().rotate([157,0]).center([-3,19.9]).parallels([8,18]),H={point:function(J,K){C=[J,K]}};return F.invert=function(K){var M=E.scale(),L=E.translate(),J=(K[0]-L[0])/M,N=(K[1]-L[1])/M;return(N>=0.12&&0.234>N&&J>=-0.425&&-0.214>J?G:N>=0.166&&0.234>N&&J>=-0.214&&-0.115>J?I:E).invert(K)},F.stream=function(K){var M=E.stream(K),L=G.stream(K),J=I.stream(K);return{point:function(N,O){M.point(N,O),L.point(N,O),J.point(N,O)},sphere:function(){M.sphere(),L.sphere(),J.sphere()},lineStart:function(){M.lineStart(),L.lineStart(),J.lineStart()},lineEnd:function(){M.lineEnd(),L.lineEnd(),J.lineEnd()},polygonStart:function(){M.polygonStart(),L.polygonStart(),J.polygonStart()},polygonEnd:function(){M.polygonEnd(),L.polygonEnd(),J.polygonEnd()}}},F.precision=function(J){return arguments.length?(E.precision(J),G.precision(J),I.precision(J),F):E.precision()},F.scale=function(J){return arguments.length?(E.scale(J),G.scale(0.35*J),I.scale(J),F.translate(E.translate())):E.scale()},F.translate=function(L){if(!arguments.length){return E.translate()}var K=E.scale(),J=+L[0],M=+L[1];return A=E.translate(L).clipExtent([[J-0.455*K,M-0.238*K],[J+0.455*K,M+0.238*K]]).stream(H).point,B=G.translate([J-0.307*K,M+0.201*K]).clipExtent([[J-0.425*K+Dy,M+0.12*K+Dy],[J-0.214*K-Dy,M+0.234*K-Dy]]).stream(H).point,D=I.translate([J-0.205*K,M+0.212*K]).clipExtent([[J-0.214*K+Dy,M+0.166*K+Dy],[J-0.115*K-Dy,M+0.234*K-Dy]]).stream(H).point,F},F.scale(1070)};var GI,Ak,AI,Jy,Jp,JI,Jh={point:Fg,lineStart:Fg,lineEnd:Fg,polygonStart:function(){Ak=0,Jh.lineStart=Cg},polygonEnd:function(){Jh.lineStart=Jh.lineEnd=Jh.point=Fg,GI+=Math.abs(Ak/2)}},Jq={point:Cb,lineStart:Fg,lineEnd:Fg,polygonStart:Fg,polygonEnd:Fg},Jx={point:B6,lineStart:Ck,lineEnd:BY,polygonStart:function(){Jx.lineStart=Cl},polygonEnd:function(){Jx.point=B6,Jx.lineStart=Ck,Jx.lineEnd=BY}};DT.geo.transform=function(A){return{stream:function(D){var C=new BV(D);for(var B in A){C[B]=A[B]}return C}}},BV.prototype={point:function(A,B){this.stream.point(A,B)},sphere:function(){this.stream.sphere()},lineStart:function(){this.stream.lineStart()},lineEnd:function(){this.stream.lineEnd()},polygonStart:function(){this.stream.polygonStart()},polygonEnd:function(){this.stream.polygonEnd()}},DT.geo.path=function(){function F(I){return I&&("function"==typeof H&&E.pointRadius(+H.apply(this,arguments)),G&&G.valid||(G=D(E)),DT.geo.stream(I,G)),E.result()}function C(){return G=null,F}var A,B,D,E,G,H=4.5;return F.area=function(I){return GI=0,DT.geo.stream(I,D(Jh)),GI},F.centroid=function(I){return F7=Fx=Gd=FG=Fq=Fs=FA=Gb=Cn=0,DT.geo.stream(I,D(Jx)),Cn?[FA/Cn,Gb/Cn]:Fs?[FG/Fs,Fq/Fs]:Gd?[F7/Gd,Fx/Gd]:[0/0,0/0]},F.bounds=function(I){return Jp=JI=-(AI=Jy=1/0),DT.geo.stream(I,D(Jq)),[[AI,Jy],[Jp,JI]]},F.projection=function(I){return arguments.length?(D=(A=I)?I.stream||BL(I):H9,C()):A},F.context=function(I){return arguments.length?(E=null==(B=I)?new BX:new B0(I),"function"!=typeof H&&E.pointRadius(H),C()):B},F.pointRadius=function(I){return arguments.length?(H="function"==typeof I?I:(E.pointRadius(+I),+I),F):H},F.projection(DT.geo.albersUsa()).context(null)},DT.geo.projection=BU,DT.geo.projectionMutator=Iw,(DT.geo.equirectangular=function(){return BU(B1)}).raw=B1.invert=B1,DT.geo.rotation=function(A){function B(C){return C=A(C[0]*Fn,C[1]*Fn),C[0]*=G0,C[1]*=G0,C}return A=BS(A[0]%360*Fn,A[1]*Fn,A.length>2?A[2]*Fn:0),B.invert=function(C){return C=A.invert(C[0]*Fn,C[1]*Fn),C[0]*=G0,C[1]*=G0,C},B},Bx.invert=B1,DT.geo.circle=function(){function B(){var F="function"==typeof A?A.apply(this,arguments):A,G=BS(-F[0]*Fn,-F[1]*Fn,0).invert,H=[];return C(null,null,1,{point:function(I,J){H.push(I=G(I,J)),I[0]*=G0,I[1]*=G0}}),{type:"Polygon",coordinates:[H]}}var D,C,A=[0,0],E=6;return B.origin=function(F){return arguments.length?(A=F,B):A},B.angle=function(F){return arguments.length?(C=BC((D=+F)*Fn,E*Fn),B):D},B.precision=function(F){return arguments.length?(C=BC(D*Fn,(E=+F)*Fn),B):E},B.angle(90)},DT.geo.distance=function(G,D){var A,C=(D[0]-G[0])*Fn,E=G[1]*Fn,F=D[1]*Fn,H=Math.sin(C),K=Math.cos(C),J=Math.sin(E),I=Math.cos(E),B=Math.sin(F),L=Math.cos(F);return Math.atan2(Math.sqrt((A=L*H)*A+(A=I*B-J*L*K)*A),J*B+I*L*K)},DT.geo.graticule=function(){function I(){return{type:"MultiLineString",coordinates:E()}}function E(){return DT.range(Math.ceil(H/D)*D,F,D).map(G).concat(DT.range(Math.ceil(K/L)*L,M,L).map(P)).concat(DT.range(Math.ceil(A/C)*C,R,C).filter(function(T){return Math.abs(T%D)>Dy}).map(B)).concat(DT.range(Math.ceil(N/Q)*Q,J,Q).filter(function(T){return Math.abs(T%L)>Dy}).map(O))}var R,A,F,H,J,N,M,K,B,O,G,P,C=10,Q=C,D=90,L=360,S=2.5;return I.lines=function(){return E().map(function(T){return{type:"LineString",coordinates:T}})},I.outline=function(){return{type:"Polygon",coordinates:[G(H).concat(P(M).slice(1),G(F).reverse().slice(1),P(K).reverse().slice(1))]}},I.extent=function(T){return arguments.length?I.majorExtent(T).minorExtent(T):I.minorExtent()},I.majorExtent=function(T){return arguments.length?(H=+T[0][0],F=+T[1][0],K=+T[0][1],M=+T[1][1],H>F&&(T=H,H=F,F=T),K>M&&(T=K,K=M,M=T),I.precision(S)):[[H,K],[F,M]]},I.minorExtent=function(T){return arguments.length?(A=+T[0][0],R=+T[1][0],N=+T[0][1],J=+T[1][1],A>R&&(T=A,A=R,R=T),N>J&&(T=N,N=J,J=T),I.precision(S)):[[A,N],[R,J]]},I.step=function(T){return arguments.length?I.majorStep(T).minorStep(T):I.minorStep()},I.majorStep=function(T){return arguments.length?(D=+T[0],L=+T[1],I):[D,L]},I.minorStep=function(T){return arguments.length?(C=+T[0],Q=+T[1],I):[C,Q]},I.precision=function(T){return arguments.length?(S=+T,B=BT(N,J,90),O=BN(A,R,S),G=BT(K,M,90),P=BN(H,F,S),I):S},I.majorExtent([[-180,-90+Dy],[180,90-Dy]]).minorExtent([[-180,-80-Dy],[180,80+Dy]])},DT.geo.greatArc=function(){function B(){return{type:"LineString",coordinates:[D||A.apply(this,arguments),C||E.apply(this,arguments)]}}var D,C,A=BR,E=B4;return B.distance=function(){return DT.geo.distance(D||A.apply(this,arguments),C||E.apply(this,arguments))},B.source=function(F){return arguments.length?(A=F,D="function"==typeof F?null:F,B):A},B.target=function(F){return arguments.length?(E=F,C="function"==typeof F?null:F,B):E},B.precision=function(){return arguments.length?B:0},B},DT.geo.interpolate=function(A,B){return BK(A[0]*Fn,A[1]*Fn,B[0]*Fn,B[1]*Fn)},DT.geo.length=function(A){return Jz=0,DT.geo.stream(A,JE),Jz};var Jz,JE={sphere:Fg,point:Fg,lineStart:BG,lineEnd:Fg,polygonStart:Fg,polygonEnd:Fg},JD=BP(function(A){return Math.sqrt(2/(1+A))},function(A){return 2*Math.asin(A/2)});(DT.geo.azimuthalEqualArea=function(){return BU(JD)}).raw=JD;var JA=BP(function(A){var B=Math.acos(A);return B&&B/Math.sin(B)},H9);(DT.geo.azimuthalEquidistant=function(){return BU(JA)}).raw=JA,(DT.geo.conicConformal=function(){return B9(By)}).raw=By,(DT.geo.conicEquidistant=function(){return B9(BJ)}).raw=BJ;var Jj=BP(function(A){return 1/A},Math.atan);(DT.geo.gnomonic=function(){return BU(Jj)}).raw=Jj,Bw.invert=function(A,B){return[A,2*Math.atan(Math.exp(B))-Dg]},(DT.geo.mercator=function(){return Bs(Bw)}).raw=Bw;var JF=BP(function(){return 1},Math.asin);(DT.geo.orthographic=function(){return BU(JF)}).raw=JF;var Jw=BP(function(A){return 1/(1+A)},function(A){return 2*Math.atan(A)});(DT.geo.stereographic=function(){return BU(Jw)}).raw=Jw,BH.invert=function(A,B){return[Math.atan2(Cy(A),Math.cos(B)),CL(Math.sin(B)/Cw(A))]},(DT.geo.transverseMercator=function(){return Bs(BH)}).raw=BH,DT.geom={},DT.svg={},DT.svg.line=function(){return BD(H9)};var JG=DT.map({linear:Bq,"linear-closed":Cp,step:BM,"step-before":BB,"step-after":BE,basis:E0,"basis-open":EE,"basis-closed":EL,bundle:EP,cardinal:Bz,"cardinal-open":BQ,"cardinal-closed":BF,monotone:EX});JG.forEach(function(A,B){B.key=A,B.closed=/-closed$/.test(A)});var Jk=[0,2/3,1/3,0],JH=[0,1/3,2/3,0],Jm=[0,1/6,2/3,1/6];DT.geom.hull=function(B){function D(L){if(L.length<3){return[]}var H,I,K,N,S,R,O,E,T,J,U,F,V=HP(C),G=HP(A),P=L.length,Z=P-1,W=[],Y=[],Q=0;if(V===Bp&&A===BA){H=L}else{for(K=0,H=[];P>K;++K){H.push([+V.call(this,I=L[K],K),+G.call(this,I,K)])}}for(K=1;P>K;++K){(H[K][1]<H[Q][1]||H[K][1]==H[Q][1]&&H[K][0]<H[Q][0])&&(Q=K)}for(K=0;P>K;++K){K!==Q&&(R=H[K][1]-H[Q][1],S=H[K][0]-H[Q][0],W.push({angle:Math.atan2(R,S),index:K}))}for(W.sort(function(M,a){return M.angle-a.angle}),U=W[0].angle,J=W[0].index,T=0,K=1;Z>K;++K){if(N=W[K].index,U==W[K].angle){if(S=H[J][0]-H[Q][0],R=H[J][1]-H[Q][1],O=H[N][0]-H[Q][0],E=H[N][1]-H[Q][1],S*S+R*R>=O*O+E*E){W[K].index=-1;continue}W[T].index=-1}U=W[K].angle,T=K,J=N}for(Y.push(Q),K=0,N=0;2>K;++N){W[N].index>-1&&(Y.push(W[N].index),K++)}for(F=Y.length;Z>N;++N){if(!(W[N].index<0)){for(;!EO(Y[F-2],Y[F-1],W[N].index,H);){--F}Y[F++]=W[N].index}}var X=[];for(K=F-1;K>=0;--K){X.push(L[Y[K]])}return X}var C=Bp,A=BA;return arguments.length?D(B):(D.x=function(E){return arguments.length?(C=E,D):C},D.y=function(E){return arguments.length?(A=E,D):A},D)},DT.geom.polygon=function(A){return Dn(A,JB),A};var JB=DT.geom.polygon.prototype=[];JB.area=function(){for(var B,D=-1,C=this.length,A=this[C-1],E=0;++D<C;){B=A,A=this[D],E+=B[1]*A[0]-B[0]*A[1]}return 0.5*E},JB.centroid=function(F){var C,A,B=-1,D=this.length,E=0,G=0,H=this[D-1];for(arguments.length||(F=-1/(6*this.area()));++B<D;){C=H,H=this[B],A=C[0]*H[1]-H[0]*C[1],E+=(C[0]+H[0])*A,G+=(C[1]+H[1])*A}return[E*F,G*F]},JB.clip=function(G){for(var D,A,C,E,F,H,K=EZ(G),J=-1,I=this.length-EZ(this),B=this[I-1];++J<I;){for(D=G.slice(),G.length=0,E=this[J],F=D[(C=D.length-K)-1],A=-1;++A<C;){H=D[A],EY(H,B,E)?(EY(F,B,E)||G.push(EG(F,H,B,E)),G.push(H)):EY(F,B,E)&&G.push(EG(F,H,B,E)),F=H}K&&G.push(G[0]),B=E}return G},DT.geom.delaunay=function(A){var C=A.map(function(){return[]}),B=[];return Cq(A,function(D){C[D.region.l.index].push(A[D.region.r.index])}),C.forEach(function(H,E){var I=A[E],F=I[0],G=I[1];H.forEach(function(K){K.angle=Math.atan2(K[0]-F,K[1]-G)}),H.sort(function(K,L){return K.angle-L.angle});for(var J=0,D=H.length-1;D>J;J++){B.push([I,H[J],H[J+1]])}}),B},DT.geom.voronoi=function(B){function D(I){var G,H,J,M=I.map(function(){return[]}),L=HP(C),K=HP(A),F=I.length,N=1000000;if(L===Bp&&K===BA){G=I}else{for(G=new Array(F),J=0;F>J;++J){G[J]=[+L.call(this,H=I[J],J),+K.call(this,H,J)]}}if(Cq(G,function(T){var Q,O,P,R,S,U;1===T.a&&T.b>=0?(Q=T.ep.r,O=T.ep.l):(Q=T.ep.l,O=T.ep.r),1===T.a?(S=Q?Q.y:-N,P=T.c-T.b*S,U=O?O.y:N,R=T.c-T.b*U):(P=Q?Q.x:-N,S=T.c-T.a*P,R=O?O.x:N,U=T.c-T.a*R);var W=[P,S],V=[R,U];M[T.region.l.index].push(W,V),M[T.region.r.index].push(W,V)}),M=M.map(function(P,R){var O=G[R][0],T=G[R][1],Q=P.map(function(U){return Math.atan2(U[0]-O,U[1]-T)}),S=DT.range(P.length).sort(function(U,V){return Q[U]-Q[V]});return S.filter(function(U,V){return !V||Q[U]-Q[S[V-1]]>Dy}).map(function(U){return P[U]})}),M.forEach(function(V,k){var O=V.length;if(!O){return V.push([-N,-N],[-N,N],[N,N],[N,-N])}if(!(O>2)){var S=G[k],U=V[0],W=V[1],b=S[0],Z=S[1],X=U[0],P=U[1],T=W[0],f=W[1],Q=Math.abs(T-X),j=f-P;if(Math.abs(j)<Dy){var R=P>Z?-N:N;V.push([-N,R],[N,R])}else{if(Dy>Q){var Y=X>b?-N:N;V.push([Y,-N],[Y,N])}else{var R=(X-b)*(f-P)>(T-X)*(P-Z)?N:-N,q=Math.abs(j)-Q;Math.abs(q)<Dy?V.push([0>j?R:-R,R]):(q>0&&(R*=-1),V.push([-N,R],[N,R]))}}}}),E){for(J=0;F>J;++J){E.clip(M[J])}}for(J=0;F>J;++J){M[J].point=I[J]}return M}var C=Bp,A=BA,E=null;return arguments.length?D(B):(D.x=function(F){return arguments.length?(C=F,D):C},D.y=function(F){return arguments.length?(A=F,D):A},D.clipExtent=function(G){if(!arguments.length){return E&&[E[0],E[2]]}if(null==G){E=null}else{var I=+G[0][0],F=+G[0][1],H=+G[1][0],J=+G[1][1];E=DT.geom.polygon([[I,F],[I,J],[H,J],[H,F]])}return D},D.size=function(F){return arguments.length?D.clipExtent(F&&[[0,0],F]):E&&E[2]},D.links=function(J){var G,H,I,K=J.map(function(){return[]}),N=[],M=HP(C),L=HP(A),F=J.length;if(M===Bp&&L===BA){G=J}else{for(G=new Array(F),I=0;F>I;++I){G[I]=[+M.call(this,H=J[I],I),+L.call(this,H,I)]}}return Cq(G,function(Q){var P=Q.region.l.index,O=Q.region.r.index;K[P][O]||(K[P][O]=K[O][P]=!0,N.push({source:J[P],target:J[O]}))}),N},D.triangles=function(H){if(C===Bp&&A===BA){return DT.geom.delaunay(H)}for(var J,K=new Array(F),G=HP(C),I=HP(A),L=-1,F=H.length;++L<F;){(K[L]=[+G.call(this,J=H[L],L),+I.call(this,J,L)]).data=J}return DT.geom.delaunay(K).map(function(M){return M.map(function(N){return N.data})})},D)};var Jg={l:"r",r:"l"};DT.geom.quadtree=function(F,C,A,B,D){function E(Y){function P(k,d,M,b,g,h,l,p){if(!isNaN(M)&&!isNaN(b)){if(k.leaf){var m=k.x,S=k.y;if(null!=m){if(Math.abs(m-M)+Math.abs(S-b)<0.01){R(k,d,M,b,g,h,l,p)}else{var q=k.point;k.x=k.y=k.point=null,R(k,q,m,S,g,h,l,p),R(k,d,M,b,g,h,l,p)}}else{k.x=M,k.y=b,k.point=d}}else{R(k,d,M,b,g,h,l,p)}}}function R(m,d,M,b,i,p,w,v){var q=0.5*(i+w),S=0.5*(p+v),x=M>=q,k=b>=S,y=(k<<1)+x;m.leaf=!1,m=m.nodes[y]||(m.nodes[y]=Eq()),x?i=q:w=q,k?p=S:v=S,P(m,d,M,b,i,p,w,v)}var J,V,O,W,K,X,L,T,e,Z=HP(I),c=HP(H);if(null!=C){X=C,L=A,T=B,e=D}else{if(T=e=-(X=L=1/0),V=[],O=[],K=Y.length,G){for(W=0;K>W;++W){J=Y[W],J.x<X&&(X=J.x),J.y<L&&(L=J.y),J.x>T&&(T=J.x),J.y>e&&(e=J.y),V.push(J.x),O.push(J.y)}}else{for(W=0;K>W;++W){var U=+Z(J=Y[W],W),Q=+c(J,W);X>U&&(X=U),L>Q&&(L=Q),U>T&&(T=U),Q>e&&(e=Q),V.push(U),O.push(Q)}}}var N=T-X,j=e-L;N>j?e=L+N:T=X+j;var a=Eq();if(a.add=function(M){P(a,M,+Z(M,++W),+c(M,W),X,L,T,e)},a.visit=function(M){EC(M,a,X,L,T,e)},W=-1,null==C){for(;++W<K;){P(a,Y[W],V[W],O[W],X,L,T,e)}--W}else{Y.forEach(a.add)}return V=O=Y=J=null,a}var G,I=Bp,H=BA;return(G=arguments.length)?(I=ET,H=ED,3===G&&(D=A,B=C,A=C=0),E(F)):(E.x=function(J){return arguments.length?(I=J,E):I},E.y=function(J){return arguments.length?(H=J,E):H},E.extent=function(J){return arguments.length?(null==J?C=A=B=D=null:(C=+J[0][0],A=+J[0][1],B=+J[1][0],D=+J[1][1]),E):null==C?null:[[C,A],[B,D]]},E.size=function(J){return arguments.length?(null==J?C=A=B=D=null:(C=A=0,B=+J[0],D=+J[1]),E):null==C?null:[B-C,D-A]},E)},DT.interpolateRgb=EU,DT.interpolateObject=D3,DT.interpolateNumber=EJ,DT.interpolateString=D6;var I3=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g;DT.interpolate=EA,DT.interpolators=[function(A,C){var B=typeof C;return("string"===B?G9.has(C)||/^(#|rgb\(|hsl\()/.test(C)?EU:D6:C instanceof Cs?EU:"object"===B?Array.isArray(C)?EN:D3:EJ)(A,C)}],DT.interpolateArray=EN;var Jf=function(){return H9},JC=DT.map({linear:Jf,poly:Ez,quad:function(){return EH},cubic:function(){return EB},sin:function(){return EM},exp:function(){return Ep},circle:function(){return Ej},elastic:Ex,back:D7,bounce:function(){return En}}),IN=DT.map({"in":H9,out:Em,"in-out":Ed,"out-in":function(A){return Ed(Em(A))}});DT.ease=function(B){var D=B.indexOf("-"),C=D>=0?B.substring(0,D):B,A=D>=0?B.substring(D+1):"in";return C=JC.get(C)||Jf,A=IN.get(A)||H9,Ew(A(C.apply(null,Array.prototype.slice.call(arguments,1))))},DT.interpolateHcl=D5,DT.interpolateHsl=D2,DT.interpolateLab=Ek,DT.interpolateRound=Ef,DT.transform=function(A){var B=DC.createElementNS(DT.ns.prefix.svg,"g");return(DT.transform=function(C){if(null!=C){B.setAttribute("transform",C);var D=B.transform.baseVal.consolidate()}return new D0(D?D.matrix:Jn)})(A)},D0.prototype.toString=function(){return"translate("+this.translate+")rotate("+this.rotate+")skewX("+this.skew+")scale("+this.scale+")"};var Jn={a:1,b:0,c:0,d:1,e:0,f:0};DT.interpolateTransform=Es,DT.layout={},DT.layout.bundle=function(){return function(B){for(var D=[],C=-1,A=B.length;++C<A;){D.push(Ey(B[C]))}return D}},DT.layout.chord=function(){function G(){var T,U,a,Q,c,L={},e=[],N=DT.range(F),V=[];for(A=[],C=[],T=0,Q=-1;++Q<F;){for(U=0,c=-1;++c<F;){U+=E[Q][c]}e.push(U),V.push(DT.range(F)),T+=U}for(H&&N.sort(function(M,S){return H(e[M],e[S])}),K&&V.forEach(function(M,S){M.sort(function(b,d){return K(E[S][b],E[S][d])})}),T=(Db-B*F)/T,U=0,Q=-1;++Q<F;){for(a=U,c=-1;++c<F;){var Y=N[Q],P=V[Y][c],X=E[Y][P],W=U,R=U+=X*T;L[Y+"-"+P]={index:Y,subindex:P,startAngle:W,endAngle:R,value:X}}C[Y]={index:Y,startAngle:a,endAngle:U,value:(U-a)/T},U+=B}for(Q=-1;++Q<F;){for(c=Q-1;++c<F;){var O=L[Q+"-"+c],Z=L[c+"-"+Q];(O.value||Z.value)&&A.push(O.value<Z.value?{source:Z,target:O}:{source:O,target:Z})}}J&&D()}function D(){A.sort(function(L,M){return J((L.source.value+L.target.value)/2,(M.source.value+M.target.value)/2)})}var A,C,E,F,H,K,J,I={},B=0;return I.matrix=function(L){return arguments.length?(F=(E=L)&&E.length,A=C=null,I):E},I.padding=function(L){return arguments.length?(B=L,A=C=null,I):B},I.sortGroups=function(L){return arguments.length?(H=L,A=C=null,I):H},I.sortSubgroups=function(L){return arguments.length?(K=L,A=null,I):K},I.sortChords=function(L){return arguments.length?(J=L,A&&D(),I):J},I.chords=function(){return A||G(),A},I.groups=function(){return C||G(),C},I},DT.layout.force=function(){function I(S){return function(V,T,U,W){if(V.point!==S){var X=V.cx-S.x,Y=V.cy-S.y,b=1/Math.sqrt(X*X+Y*Y);if(Q>(W-T)*b){var Z=V.charge*b*b;return S.px-=X*Z,S.py-=Y*Z,!0}if(V.point&&isFinite(b)){var Z=V.pointCharge*b*b;S.px-=X*Z,S.py-=Y*Z}}return !V.charge}}function E(S){S.px=DT.event.x,S.py=DT.event.y,N.resume()}var R,A,F,H,J,N={},M=DT.dispatch("start","tick","end"),K=[1,1],B=0.9,O=IP,G=Jb,P=-30,C=0.1,Q=0.8,D=[],L=[];return N.tick=function(){if((A*=0.99)<0.005){return M.end({type:"end",alpha:A=0}),!0}var T,S,c,g,V,i,Z,U,Y,X=D.length,W=L.length;for(S=0;W>S;++S){c=L[S],g=c.source,V=c.target,U=V.x-g.x,Y=V.y-g.y,(i=U*U+Y*Y)&&(i=A*H[S]*((i=Math.sqrt(i))-F[S])/i,U*=i,Y*=i,V.x-=U*(Z=g.weight/(V.weight+g.weight)),V.y-=Y*Z,g.x+=U*(Z=1-Z),g.y+=Y*Z)}if((Z=A*C)&&(U=K[0]/2,Y=K[1]/2,S=-1,Z)){for(;++S<X;){c=D[S],c.x+=(U-c.x)*Z,c.y+=(Y-c.y)*Z}}if(P){for(AZ(T=DT.geom.quadtree(D),A,J),S=-1;++S<X;){(c=D[S]).fixed||T.visit(I(c))}}for(S=-1;++S<X;){c=D[S],c.fixed?(c.x=c.px,c.y=c.py):(c.x-=(c.px-(c.px=c.x))*B,c.y-=(c.py-(c.py=c.y))*B)}M.tick({type:"tick",alpha:A})},N.nodes=function(S){return arguments.length?(D=S,N):D},N.links=function(S){return arguments.length?(L=S,N):L},N.size=function(S){return arguments.length?(K=S,N):K},N.linkDistance=function(S){return arguments.length?(O="function"==typeof S?S:+S,N):O},N.distance=N.linkDistance,N.linkStrength=function(S){return arguments.length?(G="function"==typeof S?S:+S,N):G},N.friction=function(S){return arguments.length?(B=+S,N):B},N.charge=function(S){return arguments.length?(P="function"==typeof S?S:+S,N):P},N.gravity=function(S){return arguments.length?(C=+S,N):C},N.theta=function(S){return arguments.length?(Q=+S,N):Q},N.alpha=function(S){return arguments.length?(S=+S,A?A=S>0?S:0:S>0&&(M.start({type:"start",alpha:A=S}),DT.timer(N.tick)),N):A},N.start=function(){function Y(d,c){for(var g,e=W(S),f=-1,h=e.length;++f<h;){if(!isNaN(g=e[f][d])){return g}}return Math.random()*c}function W(){if(!Z){for(Z=[],V=0;U>V;++V){Z[V]=[]}for(V=0;b>V;++V){var c=L[V];Z[c.source.index].push(c.target),Z[c.target.index].push(c.source)}}return Z[S]}var S,V,Z,T,U=D.length,b=L.length,a=K[0],X=K[1];for(S=0;U>S;++S){(T=D[S]).index=S,T.weight=0}for(S=0;b>S;++S){T=L[S],"number"==typeof T.source&&(T.source=D[T.source]),"number"==typeof T.target&&(T.target=D[T.target]),++T.source.weight,++T.target.weight}for(S=0;U>S;++S){T=D[S],isNaN(T.x)&&(T.x=Y("x",a)),isNaN(T.y)&&(T.y=Y("y",X)),isNaN(T.px)&&(T.px=T.x),isNaN(T.py)&&(T.py=T.y)}if(F=[],"function"==typeof O){for(S=0;b>S;++S){F[S]=+O.call(this,L[S],S)}}else{for(S=0;b>S;++S){F[S]=O}}if(H=[],"function"==typeof G){for(S=0;b>S;++S){H[S]=+G.call(this,L[S],S)}}else{for(S=0;b>S;++S){H[S]=G}}if(J=[],"function"==typeof P){for(S=0;U>S;++S){J[S]=+P.call(this,D[S],S)}}else{for(S=0;U>S;++S){J[S]=P}}return N.resume()},N.resume=function(){return N.alpha(0.1)},N.stop=function(){return N.alpha(0)},N.drag=function(){return R||(R=DT.behavior.drag().origin(H9).on("dragstart.force",A4).on("drag.force",E).on("dragend.force",AY)),arguments.length?(this.on("mouseover.force",Bj).on("mouseout.force",AS).call(R),void 0):R},DT.rebind(N,M,"on")};var IP=20,Jb=1;DT.layout.hierarchy=function(){function B(I,K,N){var M=F.call(D,I,K);if(I.depth=K,N.push(I),M&&(L=M.length)){for(var L,G,O=-1,J=I.children=[],P=0,H=K+1;++O<L;){G=B(M[O],H,N),G.parent=I,J.push(G),P+=G.value}A&&J.sort(A),C&&(I.value=P)}else{C&&(I.value=+C.call(D,I,K)||0)}return I}function E(H,G){var L=H.children,I=0;if(L&&(M=L.length)){for(var M,K=-1,J=G+1;++K<M;){I+=E(L[K],J)}}else{C&&(I=+C.call(D,H,G)||0)}return C&&(H.value=I),I}function D(H){var G=[];return B(H,0,G),G}var A=A9,F=A5,C=Bd;return D.sort=function(G){return arguments.length?(A=G,D):A},D.children=function(G){return arguments.length?(F=G,D):F},D.value=function(G){return arguments.length?(C=G,D):C},D.revalue=function(G){return E(G,0),G},D},DT.layout.partition=function(){function B(H,F,G,I){var J=H.children;if(H.x=F,H.y=H.depth*I,H.dx=G,H.dy=I,J&&(K=J.length)){var K,N,M,L=-1;for(G=H.value?G/H.value:0;++L<K;){B(N=J[L],F,M=N.value*G,I),F+=M}}}function D(G){var I=G.children,F=0;if(I&&(J=I.length)){for(var J,H=-1;++H<J;){F=Math.max(F,D(I[H]))}}return 1+F}function C(G,F){var H=A.call(this,G,F);return B(H[0],0,E[0],E[1]/D(H[0])),H}var A=DT.layout.hierarchy(),E=[1,1];return C.size=function(F){return arguments.length?(E=F,C):E},A3(C,A)},DT.layout.pie=function(){function B(F){var H=F.map(function(M,L){return +D.call(B,M,L)}),K=+("function"==typeof A?A.apply(this,arguments):A),J=(("function"==typeof E?E.apply(this,arguments):E)-K)/DT.sum(H),I=DT.range(F.length);null!=C&&I.sort(C===Jv?function(L,M){return H[M]-H[L]}:function(L,M){return C(F[L],F[M])});var G=[];return I.forEach(function(L){var M;G[L]={data:F[L],value:M=H[L],startAngle:K,endAngle:K+=M*J}}),G}var D=Number,C=Jv,A=0,E=Db;return B.value=function(F){return arguments.length?(D=F,B):D},B.sort=function(F){return arguments.length?(C=F,B):C},B.startAngle=function(F){return arguments.length?(A=F,B):A},B.endAngle=function(F){return arguments.length?(E=F,B):E},B};var Jv={};DT.layout.stack=function(){function B(O,N){var L=O.map(function(T,S){return F.call(B,T,S)}),H=L.map(function(S){return S.map(function(U,T){return[C.call(B,U,T),E.call(B,U,T)]})}),P=D.call(B,H,N);L=DT.permute(L,P),H=DT.permute(H,P);var K,Q,I,R=A.call(B,H,N),J=L.length,M=L[0].length;for(Q=0;M>Q;++Q){for(G.call(B,L[0][Q],I=R[Q],H[0][Q][1]),K=1;J>K;++K){G.call(B,L[K][Q],I+=H[K-1][Q][1],H[K][Q][1])}}return O}var F=H9,D=Bg,A=AU,G=A2,C=AT,E=Bf;return B.values=function(H){return arguments.length?(F=H,B):F},B.order=function(H){return arguments.length?(D="function"==typeof H?H:I6.get(H)||Bg,B):D},B.offset=function(H){return arguments.length?(A="function"==typeof H?H:I0.get(H)||AU,B):A},B.x=function(H){return arguments.length?(C=H,B):C},B.y=function(H){return arguments.length?(E=H,B):E},B.out=function(H){return arguments.length?(G=H,B):G},B};var I6=DT.map({"inside-out":function(G){var D,A,C=G.length,E=G.map(Bh),F=G.map(AW),H=DT.range(C).sort(function(L,M){return E[L]-E[M]}),K=0,J=0,I=[],B=[];for(D=0;C>D;++D){A=H[D],J>K?(K+=F[A],I.push(A)):(J+=F[A],B.push(A))}return B.reverse().concat(I)},reverse:function(A){return DT.range(A.length).reverse()},"default":Bg}),I0=DT.map({silhouette:function(F){var C,A,B,D=F.length,E=F[0].length,G=[],I=0,H=[];for(A=0;E>A;++A){for(C=0,B=0;D>C;C++){B+=F[C][A][1]}B>I&&(I=B),G.push(B)}for(A=0;E>A;++A){H[A]=(I-G[A])/2}return H},wiggle:function(H){var D,A,C,E,G,I,L,K,J,B=H.length,M=H[0],F=M.length,N=[];for(N[0]=K=J=0,A=1;F>A;++A){for(D=0,E=0;B>D;++D){E+=H[D][A][1]}for(D=0,G=0,L=M[A][0]-M[A-1][0];B>D;++D){for(C=0,I=(H[D][A][1]-H[D][A-1][1])/(2*L);D>C;++C){I+=(H[C][A][1]-H[C][A-1][1])/L}G+=I*H[D][A][1]}N[A]=K-=E?G/E*L:0,J>K&&(J=K)}for(A=0;F>A;++A){N[A]-=J}return N},expand:function(F){var C,A,B,D=F.length,E=F[0].length,G=1/D,H=[];for(A=0;E>A;++A){for(C=0,B=0;D>C;C++){B+=F[C][A][1]}if(B){for(C=0;D>C;C++){F[C][A][1]/=B}}else{for(C=0;D>C;C++){F[C][A][1]=G}}}for(A=0;E>A;++A){H[A]=0}return H},zero:AU});DT.layout.histogram=function(){function B(J,I){for(var K,N,M=[],L=J.map(C,this),F=A.call(this,L,I),O=E.call(this,F,L,I),I=-1,H=L.length,P=O.length-1,G=D?1:1/H;++I<P;){K=M[I]=[],K.dx=O[I+1]-(K.x=O[I]),K.y=0}if(P>0){for(I=-1;++I<H;){N=L[I],N>=F[0]&&N<=F[1]&&(K=M[DT.bisect(O,N,1,P)-1],K.y+=G,K.push(J[I]))}}return M}var D=!0,C=Number,A=AP,E=AQ;return B.value=function(F){return arguments.length?(C=F,B):C},B.range=function(F){return arguments.length?(A=HP(F),B):A},B.bins=function(F){return arguments.length?(E="number"==typeof F?function(G){return AF(G,F)}:HP(F),B):E},B.frequency=function(F){return arguments.length?(D=!!F,B):D},B},DT.layout.tree=function(){function B(K,J){function L(Y,U){var S=Y.children,V=Y._tree;if(S&&(X=S.length)){for(var X,b,Z,T=S[0],c=T,W=-1;++W<X;){Z=S[W],L(Z,b),c=N(Z,b,c),b=Z}Aw(Y);var d=0.5*(T._tree.prelim+Z._tree.prelim);U?(V.prelim=U._tree.prelim+C(Y,U),V.mod=V.prelim-d):V.prelim=d}else{U&&(V.prelim=U._tree.prelim+C(Y,U))}}function O(T,V){T.x=T._tree.prelim+V;var U=T.children;if(U&&(S=U.length)){var S,W=-1;for(V+=T._tree.mod;++W<S;){O(U[W],V)}}}function N(Y,U,S){if(U){for(var V,X=Y,Z=Y,e=U,d=Y.parent.children[0],b=X._tree.mod,T=Z._tree.mod,g=e._tree.mod,W=d._tree.mod;e=AX(e),X=Aj(X),e&&X;){d=Aj(d),Z=AX(Z),Z._tree.ancestor=Y,V=e._tree.prelim+g-X._tree.prelim-b+C(e,X),V>0&&(AV(AO(e,Y,S),Y,V),b+=V,T+=V),g+=e._tree.mod,b+=X._tree.mod,W+=d._tree.mod,T+=Z._tree.mod}e&&!AX(Z)&&(Z._tree.thread=e,Z._tree.mod+=g-T),X&&!Aj(d)&&(d._tree.thread=X,d._tree.mod+=b-W,S=Y)}return S}var M=D.call(this,K,J),F=M[0];AC(F,function(S,T){S._tree={ancestor:S,prelim:0,mod:0,change:0,shift:0,number:T?T._tree.number+1:0}}),L(F),O(F,-F._tree.prelim);var P=An(F,A1),I=An(F,AN),Q=An(F,AJ),G=P.x-C(P,I)/2,R=I.x+C(I,P)/2,H=Q.depth||1;return AC(F,E?function(S){S.x*=A[0],S.y=S.depth*A[1],delete S._tree}:function(S){S.x=(S.x-G)/(R-G)*A[0],S.y=S.depth/H*A[1],delete S._tree}),M}var D=DT.layout.hierarchy().sort(null).value(null),C=A8,A=[1,1],E=!1;return B.separation=function(F){return arguments.length?(C=F,B):C},B.size=function(F){return arguments.length?(E=null==(A=F),B):E?null:A},B.nodeSize=function(F){return arguments.length?(E=null!=(A=F),B):E?A:null},A3(B,D)},DT.layout.pack=function(){function B(H,G){var I=C.call(this,H,G),L=I[0],K=E[0],J=E[1],F=null==D?Math.sqrt:"function"==typeof D?D:function(){return D};if(L.x=L.y=0,AC(L,function(N){N.r=+F(N.value)}),AC(L,AA),A){var M=A*(D?1:Math.max(2*L.r/K,2*L.r/J))/2;AC(L,function(N){N.r+=M}),AC(L,AA),AC(L,function(N){N.r-=M})}return AD(L,K/2,J/2,D?1:1/Math.max(2*L.r/K,2*L.r/J)),I}var D,C=DT.layout.hierarchy().sort(AH),A=0,E=[1,1];return B.size=function(F){return arguments.length?(E=F,B):E},B.radius=function(F){return arguments.length?(D=null==F||"function"==typeof F?F:+F,B):D},B.padding=function(F){return arguments.length?(A=+F,B):A},A3(B,C)},DT.layout.cluster=function(){function B(I,H){var J,M=D.call(this,I,H),L=M[0],K=0;AC(L,function(P){var Q=P.children;Q&&Q.length?(P.x=AB(Q),P.y=Ah(Q)):(P.x=J?K+=C(P,J):0,P.y=0,J=P)});var F=Ax(L),N=Af(L),G=F.x-C(F,N)/2,O=N.x+C(N,F)/2;return AC(L,E?function(P){P.x=(P.x-L.x)*A[0],P.y=(L.y-P.y)*A[1]}:function(P){P.x=(P.x-G)/(O-G)*A[0],P.y=(1-(L.y?P.y/L.y:1))*A[1]}),M}var D=DT.layout.hierarchy().sort(null).value(null),C=A8,A=[1,1],E=!1;return B.separation=function(F){return arguments.length?(C=F,B):C},B.size=function(F){return arguments.length?(E=null==(A=F),B):E?null:A},B.nodeSize=function(F){return arguments.length?(E=null!=(A=F),B):E?A:null},A3(B,D)},DT.layout.treemap=function(){function I(Q,T){for(var S,P,U=-1,R=Q.length;++U<R;){P=(S=Q[U]).value*(0>T?0:T),S.area=isNaN(P)||0>=P?0:P}}function E(P){var T=P.children;if(T&&T.length){var U,X,W,V=N(P),Q=[],S=T.slice(),R=1/0,Y="slice"===O?V.dx:"dice"===O?V.dy:"slice-dice"===O?1&P.depth?V.dy:V.dx:Math.min(V.dx,V.dy);for(I(S,V.dx*V.dy/P.value),Q.area=0;(W=S.length)>0;){Q.push(U=S[W-1]),Q.area+=U.area,"squarify"!==O||(X=D(Q,Y))<=R?(S.pop(),R=X):(Q.area-=Q.pop().area,F(Q,Y,V,!1),Y=Math.min(V.dx,V.dy),Q.length=Q.area=0,R=1/0)}Q.length&&(F(Q,Y,V,!0),Q.length=Q.area=0),T.forEach(E)}}function A(T){var Q=T.children;if(Q&&Q.length){var R,S=N(T),U=Q.slice(),P=[];for(I(U,S.dx*S.dy/T.value),P.area=0;R=U.pop();){P.push(R),P.area+=R.area,null!=R.z&&(F(P,R.z?S.dx:S.dy,S,!U.length),P.length=P.area=0)}Q.forEach(A)}}function D(U,R){for(var P,Q=U.area,S=0,T=1/0,V=-1,W=U.length;++V<W;){(P=U[V].area)&&(T>P&&(T=P),P>S&&(S=P))}return Q*=Q,R*=R,Q?Math.max(R*S*C/Q,Q/(R*T*C)):1/0}function F(V,S,P,R){var T,U=-1,W=V.length,Y=P.x,X=P.y,Q=S?L(V.area/S):0;if(S==P.dx){for((R||Q>P.dy)&&(Q=P.dy);++U<W;){T=V[U],T.x=Y,T.y=X,T.dy=Q,Y+=T.dx=Math.min(P.x+P.dx-Y,Q?L(T.area/Q):0)}T.z=!0,T.dx+=P.x+P.dx-Y,P.y+=Q,P.dy-=Q}else{for((R||Q>P.dx)&&(Q=P.dx);++U<W;){T=V[U],T.x=Y,T.y=X,T.dx=Q,X+=T.dy=Math.min(P.y+P.dy-X,Q?L(T.area/Q):0)}T.z=!1,T.dy+=P.y+P.dy-X,P.x+=Q,P.dx-=Q}}function H(P){var R=J||M(P),Q=R[0];return Q.x=0,Q.y=0,Q.dx=K[0],Q.dy=K[1],J&&M.revalue(Q),I([Q],Q.dx*Q.dy/Q.value),(J?A:E)(Q),G&&(J=R),R}var J,M=DT.layout.hierarchy(),L=Math.round,K=[1,1],B=null,N=As,G=!1,O="squarify",C=0.5*(1+Math.sqrt(5));return H.size=function(P){return arguments.length?(K=P,H):K},H.padding=function(Q){function S(U){var T=Q.call(H,U,U.depth);return null==T?As(U):Ag(U,"number"==typeof T?[T,T,T,T]:T)}function R(T){return Ag(T,Q)}if(!arguments.length){return B}var P;return N=null==(B=Q)?As:"function"==(P=typeof Q)?S:"number"===P?(Q=[Q,Q,Q,Q],R):R,H},H.round=function(P){return arguments.length?(L=P?Math.round:Number,H):L!=Number},H.sticky=function(P){return arguments.length?(G=P,J=null,H):G},H.ratio=function(P){return arguments.length?(C=P,H):C},H.mode=function(P){return arguments.length?(O=P+"",H):O},A3(H,M)},DT.random={normal:function(A,C){var B=arguments.length;return 2>B&&(C=1),1>B&&(A=0),function(){var E,D,F;do{E=2*Math.random()-1,D=2*Math.random()-1,F=E*E+D*D}while(!F||F>1);return A+C*E*Math.sqrt(-2*Math.log(F)/F)}},logNormal:function(){var A=DT.random.normal.apply(DT,arguments);return function(){return Math.exp(A())}},irwinHall:function(A){return function(){for(var C=0,B=0;A>B;B++){C+=Math.random()}return C/A}}},DT.scale={};var IU={floor:H9,ceil:H9};DT.scale.linear=function(){return Aq([0,1],[0,1],EA,!1)},DT.scale.log=function(){return Gv(DT.scale.linear().domain([0,1]),10,!0,[1,10])};var Jl=DT.format(".0e"),Jd={floor:function(A){return -Math.ceil(-A)},ceil:function(A){return -Math.floor(-A)}};DT.scale.pow=function(){return Gx(DT.scale.linear(),1,[0,1])},DT.scale.sqrt=function(){return DT.scale.pow().exponent(0.5)},DT.scale.ordinal=function(){return GB([],{t:"range",a:[[]]})},DT.scale.category10=function(){return DT.scale.ordinal().range(I5)},DT.scale.category20=function(){return DT.scale.ordinal().range(I9)},DT.scale.category20b=function(){return DT.scale.ordinal().range(Js)},DT.scale.category20c=function(){return DT.scale.ordinal().range(I2)};var I5=[2062260,16744206,2924588,14034728,9725885,9197131,14907330,8355711,12369186,1556175].map(HY),I9=[2062260,11454440,16744206,16759672,2924588,10018698,14034728,16750742,9725885,12955861,9197131,12885140,14907330,16234194,8355711,13092807,12369186,14408589,1556175,10410725].map(HY),Js=[3750777,5395619,7040719,10264286,6519097,9216594,11915115,13556636,9202993,12426809,15186514,15190932,8666169,11356490,14049643,15177372,8077683,10834324,13528509,14589654].map(HY),I2=[3244733,7057110,10406625,13032431,15095053,16616764,16625259,16634018,3253076,7652470,10607003,13101504,7695281,10394312,12369372,14342891,6513507,9868950,12434877,14277081].map(HY);DT.scale.quantile=function(){return Gy([],[])},DT.scale.quantize=function(){return Gg(0,1,[0,1])},DT.scale.threshold=function(){return GE([0.5],[0,1])},DT.scale.identity=function(){return Gs([0,1])},DT.svg.arc=function(){function B(){var I=D.apply(this,arguments),H=C.apply(this,arguments),J=A.apply(this,arguments)+IY,M=E.apply(this,arguments)+IY,L=(J>M&&(L=J,J=M,M=L),M-J),K=Dq>L?"0":"1",F=Math.cos(J),N=Math.sin(J),G=Math.cos(M),O=Math.sin(M);return L>=I7?I?"M0,"+H+"A"+H+","+H+" 0 1,1 0,"+-H+"A"+H+","+H+" 0 1,1 0,"+H+"M0,"+I+"A"+I+","+I+" 0 1,0 0,"+-I+"A"+I+","+I+" 0 1,0 0,"+I+"Z":"M0,"+H+"A"+H+","+H+" 0 1,1 0,"+-H+"A"+H+","+H+" 0 1,1 0,"+H+"Z":I?"M"+H*F+","+H*N+"A"+H+","+H+" 0 "+K+",1 "+H*G+","+H*O+"L"+I*G+","+I*O+"A"+I+","+I+" 0 "+K+",0 "+I*F+","+I*N+"Z":"M"+H*F+","+H*N+"A"+H+","+H+" 0 "+K+",1 "+H*G+","+H*O+"L0,0Z"}var D=GF,C=Gh,A=GG,E=Gk;return B.innerRadius=function(F){return arguments.length?(D=HP(F),B):D},B.outerRadius=function(F){return arguments.length?(C=HP(F),B):C},B.startAngle=function(F){return arguments.length?(A=HP(F),B):A},B.endAngle=function(F){return arguments.length?(E=HP(F),B):E},B.centroid=function(){var G=(D.apply(this,arguments)+C.apply(this,arguments))/2,F=(A.apply(this,arguments)+E.apply(this,arguments))/2+IY;return[Math.cos(F)*G,Math.sin(F)*G]},B};var IY=-Dg,I7=Db-Dy;DT.svg.line.radial=function(){var A=BD(Gz);return A.radius=A.x,delete A.x,A.angle=A.y,delete A.y,A},BB.reverse=BE,BE.reverse=BB,DT.svg.area=function(){return F9(H9)},DT.svg.area.radial=function(){var A=F9(Gz);return A.radius=A.x,delete A.x,A.innerRadius=A.x0,delete A.x0,A.outerRadius=A.x1,delete A.x1,A.angle=A.y,delete A.y,A.startAngle=A.y0,delete A.y0,A.endAngle=A.y1,delete A.y1,A},DT.svg.chord=function(){function F(K,N){var M=C(this,E,K,N),L=C(this,G,K,N);return"M"+M.p0+B(M.r,M.p1,M.a1-M.a0)+(A(M,L)?D(M.r,M.p1,M.r,M.p0):D(M.r,M.p1,L.r,L.p0)+B(L.r,L.p1,L.a1-L.a0)+D(L.r,L.p1,M.r,M.p0))+"Z"}function C(Q,N,K,M){var O=N.call(Q,K,M),P=J.call(Q,O,M),R=I.call(Q,O,M)+IY,L=H.call(Q,O,M)+IY;return{r:P,a0:R,a1:L,p0:[P*Math.cos(R),P*Math.sin(R)],p1:[P*Math.cos(L),P*Math.sin(L)]}}function A(K,L){return K.a0==L.a0&&K.a1==L.a1}function B(K,M,L){return"A"+K+","+K+" 0 "+ +(L>Dq)+",1 "+M}function D(L,N,M,K){return"Q 0,0 "+K}var E=BR,G=B4,J=FX,I=GG,H=Gk;return F.radius=function(K){return arguments.length?(J=HP(K),F):J},F.source=function(K){return arguments.length?(E=HP(K),F):E},F.target=function(K){return arguments.length?(G=HP(K),F):G},F.startAngle=function(K){return arguments.length?(I=HP(K),F):I},F.endAngle=function(K){return arguments.length?(H=HP(K),F):H},F},DT.svg.diagonal=function(){function B(F,I){var E=D.call(this,F,I),G=C.call(this,F,I),J=(E.y+G.y)/2,H=[E,{x:E.x,y:J},{x:G.x,y:J},G];return H=H.map(A),"M"+H[0]+"C"+H[1]+" "+H[2]+" "+H[3]}var D=BR,C=B4,A=F8;return B.source=function(E){return arguments.length?(D=HP(E),B):D},B.target=function(E){return arguments.length?(C=HP(E),B):C},B.projection=function(E){return arguments.length?(A=E,B):A},B},DT.svg.diagonal.radial=function(){var A=DT.svg.diagonal(),C=F8,B=A.projection;return A.projection=function(D){return arguments.length?B(GA(C=D)):C},A},DT.svg.symbol=function(){function A(E,D){return(IQ.get(C.call(this,E,D))||FJ)(B.call(this,E,D))}var C=Gl,B=FH;return A.type=function(D){return arguments.length?(C=HP(D),A):C},A.size=function(D){return arguments.length?(B=HP(D),A):B},A};var IQ=DT.map({circle:FJ,cross:function(A){var B=Math.sqrt(A/5)/2;return"M"+-3*B+","+-B+"H"+-B+"V"+-3*B+"H"+B+"V"+-B+"H"+3*B+"V"+B+"H"+B+"V"+3*B+"H"+-B+"V"+B+"H"+-3*B+"Z"},diamond:function(A){var C=Math.sqrt(A/(2*IZ)),B=C*IZ;return"M0,"+-C+"L"+B+",0 0,"+C+" "+-B+",0Z"},square:function(A){var B=Math.sqrt(A)/2;return"M"+-B+","+-B+"L"+B+","+-B+" "+B+","+B+" "+-B+","+B+"Z"},"triangle-down":function(A){var C=Math.sqrt(A/IM),B=C*IM/2;return"M0,"+B+"L"+C+","+-B+" "+-C+","+-B+"Z"},"triangle-up":function(A){var C=Math.sqrt(A/IM),B=C*IM/2;return"M0,"+-B+"L"+C+","+B+" "+-C+","+B+"Z"}});DT.svg.symbolTypes=IQ.keys();var I1,IO,IM=Math.sqrt(3),IZ=Math.tan(30*Fn),IV=[],IK=0;IV.call=C5.call,IV.empty=C5.empty,IV.node=C5.node,IV.size=C5.size,DT.transition=function(A){return arguments.length?I1?A.transition():A:Df.transition()},DT.transition.prototype=IV,IV.select=function(G){var D,A,C,E=this.id,F=[];G=Fl(G);for(var H=-1,K=this.length;++H<K;){F.push(D=[]);for(var J=this[H],I=-1,B=J.length;++I<B;){(C=J[I])&&(A=G.call(C,C.__data__,I,H))?("__data__" in C&&(A.__data__=C.__data__),FU(A,I,E,C.__transition__[E]),D.push(A)):D.push(null)}}return F5(F,E)},IV.selectAll=function(I){var E,A,D,F,H,J=this.id,M=[];I=C4(I);for(var L=-1,K=this.length;++L<K;){for(var B=this[L],N=-1,G=B.length;++N<G;){if(D=B[N]){H=D.__transition__[J],A=I.call(D,D.__data__,N,L),M.push(E=[]);for(var O=-1,C=A.length;++O<C;){(F=A[O])&&FU(F,O,J,H),E.push(F)}}}}return F5(M,J)},IV.filter=function(F){var C,A,B,D=[];"function"!=typeof F&&(F=E6(F));for(var E=0,G=this.length;G>E;E++){D.push(C=[]);for(var A=this[E],I=0,H=A.length;H>I;I++){(B=A[I])&&F.call(B,B.__data__,I)&&C.push(B)}}return F5(D,this.id)},IV.tween=function(A,C){var B=this.id;return arguments.length<2?this.node().__transition__[B].tween.get(A):CK(this,null==C?function(D){D.__transition__[B].tween.remove(A)}:function(D){D.__transition__[B].tween.set(A,C)})},IV.attr=function(F,C){function A(){this.removeAttribute(H)}function B(){this.removeAttributeNS(H.space,H.local)}function D(I){return null==I?A:(I+="",function(){var K,J=this.getAttribute(H);return J!==I&&(K=G(J,I),function(L){this.setAttribute(H,K(L))})})}function E(I){return null==I?B:(I+="",function(){var K,J=this.getAttributeNS(H.space,H.local);return J!==I&&(K=G(J,I),function(L){this.setAttributeNS(H.space,H.local,K(L))})})}if(arguments.length<2){for(C in F){this.attr(C,F[C])}return this}var G="transform"==F?Es:EA,H=DT.ns.qualify(F);return Gq(this,"attr."+F,C,H.local?E:D)},IV.attrTween=function(B,D){function C(G,H){var F=D.call(this,G,H,this.getAttribute(E));return F&&function(I){this.setAttribute(E,F(I))}}function A(G,H){var F=D.call(this,G,H,this.getAttributeNS(E.space,E.local));return F&&function(I){this.setAttributeNS(E.space,E.local,F(I))}}var E=DT.ns.qualify(B);return this.tween("attr."+B,E.local?A:C)},IV.style=function(B,E,D){function A(){this.style.removeProperty(B)}function F(G){return null==G?A:(G+="",function(){var H,I=C2.getComputedStyle(this,null).getPropertyValue(B);return I!==G&&(H=EA(I,G),function(J){this.style.setProperty(B,H(J),D)})})}var C=arguments.length;if(3>C){if("string"!=typeof B){2>C&&(E="");for(D in B){this.style(D,B[D],E)}return this}D=""}return Gq(this,"style."+B,E,F)},IV.styleTween=function(B,D,C){function A(E,G){var F=D.call(this,E,G,C2.getComputedStyle(this,null).getPropertyValue(B));return F&&function(H){this.style.setProperty(B,F(H),C)}}return arguments.length<3&&(C=""),this.tween("style."+B,A)},IV.text=function(A){return Gq(this,"text",A,F1)},IV.remove=function(){return this.each("end.transition",function(){var A;this.__transition__.count<2&&(A=this.parentNode)&&A.removeChild(this)})},IV.ease=function(A){var B=this.id;return arguments.length<1?this.node().__transition__[B].ease:("function"!=typeof A&&(A=DT.ease.apply(DT,arguments)),CK(this,function(C){C.__transition__[B].ease=A}))},IV.delay=function(A){var B=this.id;return CK(this,"function"==typeof A?function(D,C,E){D.__transition__[B].delay=+A.call(D,D.__data__,C,E)}:(A=+A,function(C){C.__transition__[B].delay=A}))},IV.duration=function(A){var B=this.id;return CK(this,"function"==typeof A?function(D,C,E){D.__transition__[B].duration=Math.max(1,A.call(D,D.__data__,C,E))}:(A=Math.max(1,A),function(C){C.__transition__[B].duration=A}))},IV.each=function(B,D){var C=this.id;if(arguments.length<2){var A=IO,E=I1;I1=C,CK(this,function(G,F,H){IO=G.__transition__[C],B.call(G,G.__data__,F,H)}),IO=A,I1=E}else{CK(this,function(F){var G=F.__transition__[C];(G.event||(G.event=DT.dispatch("start","end"))).on(B,D)})}return this},IV.transition=function(){for(var G,D,A,C,E=this.id,F=++IK,H=[],K=0,J=this.length;J>K;K++){H.push(G=[]);for(var D=this[K],I=0,B=D.length;B>I;I++){(A=D[I])&&(C=Object.create(A.__transition__[E]),C.delay+=C.duration,FU(A,I,F,C)),G.push(A)}}return F5(H,F)},DT.svg.axis=function(){function F(J){J.each(function(){var R,T=DT.select(this),K=this.__chart__||A,W=this.__chart__=A.copy(),Q=null==H?W.ticks?W.ticks.apply(W,I):W.domain():H,X=null==C?W.tickFormat?W.tickFormat.apply(W,I):H9:C,L=T.selectAll(".tick").data(Q,W),Y=L.enter().insert("g",".domain").attr("class","tick").style("opacity",Dy),N=DT.transition(L.exit()).style("opacity",Dy).remove(),U=DT.transition(L).style("opacity",1),j=AG(W),Z=T.selectAll(".domain").data([0]),i=(Z.enter().append("path").attr("class","domain"),DT.transition(Z));Y.append("line"),Y.append("text");var V=Y.select("line"),e=U.select("line"),O=L.select("text").text(X),o=Y.select("text"),c=U.select("text");switch(B){case"bottom":R=FO,V.attr("y2",D),o.attr("y",Math.max(D,0)+G),e.attr("x2",0).attr("y2",D),c.attr("x",0).attr("y",Math.max(D,0)+G),O.attr("dy",".71em").style("text-anchor","middle"),i.attr("d","M"+j[0]+","+E+"V0H"+j[1]+"V"+E);break;case"top":R=FO,V.attr("y2",-D),o.attr("y",-(Math.max(D,0)+G)),e.attr("x2",0).attr("y2",-D),c.attr("x",0).attr("y",-(Math.max(D,0)+G)),O.attr("dy","0em").style("text-anchor","middle"),i.attr("d","M"+j[0]+","+-E+"V0H"+j[1]+"V"+-E);break;case"left":R=Gj,V.attr("x2",-D),o.attr("x",-(Math.max(D,0)+G)),e.attr("x2",-D).attr("y2",0),c.attr("x",-(Math.max(D,0)+G)).attr("y",0),O.attr("dy",".32em").style("text-anchor","end"),i.attr("d","M"+-E+","+j[0]+"H0V"+j[1]+"H"+-E);break;case"right":R=Gj,V.attr("x2",D),o.attr("x",Math.max(D,0)+G),e.attr("x2",D).attr("y2",0),c.attr("x",Math.max(D,0)+G).attr("y",0),O.attr("dy",".32em").style("text-anchor","start"),i.attr("d","M"+E+","+j[0]+"H0V"+j[1]+"H"+E)}if(W.rangeBand){var P=W.rangeBand()/2,a=function(M){return W(M)+P};Y.call(R,a),U.call(R,a)}else{Y.call(R,K),U.call(R,W),N.call(R,W)}})}var C,A=DT.scale.linear(),B=IS,D=6,E=6,G=3,I=[10],H=null;return F.scale=function(J){return arguments.length?(A=J,F):A},F.orient=function(J){return arguments.length?(B=J in IL?J+"":IS,F):B},F.ticks=function(){return arguments.length?(I=arguments,F):I},F.tickValues=function(J){return arguments.length?(H=J,F):H},F.tickFormat=function(J){return arguments.length?(C=J,F):C},F.tickSize=function(K){var J=arguments.length;return J?(D=+K,E=+arguments[J-1],F):D},F.innerTickSize=function(J){return arguments.length?(D=+J,F):D},F.outerTickSize=function(J){return arguments.length?(E=+J,F):E},F.tickPadding=function(J){return arguments.length?(G=+J,F):G},F.tickSubdivide=function(){return arguments.length&&F},F};var IS="bottom",IL={top:1,right:1,bottom:1,left:1};DT.svg.brush=function(){function J(P){P.each(function(){var R=DT.select(this).style("pointer-events","all").style("-webkit-tap-highlight-color","rgba(0,0,0,0)").on("mousedown.brush",G).on("touchstart.brush",G),T=R.selectAll(".background").data([0]);T.enter().append("rect").attr("class","background").style("visibility","hidden").style("cursor","crosshair"),R.selectAll(".extent").data([0]).enter().append("rect").attr("class","extent").style("cursor","move");var U=R.selectAll(".resize").data(E,H9);U.exit().remove(),U.enter().append("g").attr("class",function(W){return"resize "+W}).style("cursor",function(W){return JJ[W]}).append("rect").attr("x",function(W){return/[ew]$/.test(W)?-3:null}).attr("y",function(W){return/^[ns]/.test(W)?-3:null}).attr("width",6).attr("height",6).style("visibility","hidden"),U.style("display",J.empty()?"none":null);var S,V=DT.transition(R),Q=DT.transition(T);M&&(S=AG(M),Q.attr("x",S[0]).attr("width",S[1]-S[0]),A(V)),L&&(S=AG(L),Q.attr("y",S[0]).attr("height",S[1]-S[0]),D(V)),F(V)})}function F(P){P.selectAll(".resize").attr("transform",function(Q){return"translate("+B[+/e$/.test(Q)]+","+H[+/^s/.test(Q)]+")"})}function A(P){P.select(".extent").attr("x",B[0]),P.selectAll(".extent,.n>rect,.s>rect").attr("width",B[1]-B[0])}function D(P){P.select(".extent").attr("y",H[0]),P.selectAll(".extent,.e>rect,.w>rect").attr("height",H[1]-H[0])}function G(){function V(){32==DT.event.keyCode&&(c||(d=null,Q[0]-=B[1],Q[1]-=H[1],c=2),Fj())}function a(){32==DT.event.keyCode&&2==c&&(Q[0]+=B[1],Q[1]+=H[1],c=0,Fj())}function R(){var S=DT.mouse(o),T=!1;l&&(S[0]+=l[0],S[1]+=l[1]),c||(DT.event.altKey?(d||(d=[(B[0]+B[1])/2,(H[0]+H[1])/2]),Q[0]=B[+(S[0]<d[0])],Q[1]=H[+(S[1]<d[1])]):d=null),W&&Y(S,M,0)&&(A(Z),T=!0),f&&Y(S,L,1)&&(D(Z),T=!0),T&&(F(Z),U({type:"brush",mode:c?"move":"resize"}))}function Y(p,k,S){var T,m,w=AG(k),s=w[0],q=w[1],x=Q[S],y=S?H:B,b=y[1]-y[0];return c&&(s-=x,q-=b+x),T=(S?O:C)?Math.max(s,Math.min(q,p[S])):p[S],c?m=(T+=x)+b:(d&&(x=Math.max(s,Math.min(q,2*d[S]-T))),T>x?(m=T,T=x):m=x),y[0]!=T||y[1]!=m?(S?K=null:I=null,y[0]=T,y[1]=m,!0):void 0}function n(){R(),Z.style("pointer-events","all").selectAll(".resize").style("display",J.empty()?"none":null),DT.select("body").style("cursor",null),j.on("mousemove.brush",null).on("mouseup.brush",null).on("touchmove.brush",null).on("touchend.brush",null).on("keydown.brush",null).on("keyup.brush",null),P(),U({type:"brushend"})}var d,l,o=this,X=DT.select(DT.event.target),U=N.of(o,arguments),Z=DT.select(o),i=X.datum(),W=!/^(n|s)$/.test(i)&&M,f=!/^(e|w)$/.test(i)&&L,c=X.classed("extent"),P=CM(),Q=DT.mouse(o),j=DT.select(C2).on("keydown.brush",V).on("keyup.brush",a);if(DT.event.changedTouches?j.on("touchmove.brush",R).on("touchend.brush",n):j.on("mousemove.brush",R).on("mouseup.brush",n),Z.interrupt().selectAll("*").interrupt(),c){Q[0]=B[0]-Q[0],Q[1]=H[0]-Q[1]}else{if(i){var e=+/w$/.test(i),h=+/^n/.test(i);l=[B[1-e]-Q[0],H[1-h]-Q[1]],Q[0]=B[e],Q[1]=H[h]}else{DT.event.altKey&&(d=Q.slice())}}Z.style("pointer-events","none").selectAll(".resize").style("display",null),DT.select("body").style("cursor",X.style("cursor")),U({type:"brushstart"}),R()}var I,K,N=Fk(J,"brushstart","brush","brushend"),M=null,L=null,B=[0,0],H=[0,0],C=!0,O=!0,E=I4[0];return J.event=function(P){P.each(function(){var Q=N.of(this,arguments),S={x:B,y:H,i:I,j:K},R=this.__chart__||S;this.__chart__=S,I1?DT.select(this).transition().each("start.brush",function(){I=R.i,K=R.j,B=R.x,H=R.y,Q({type:"brushstart"})}).tween("brush:brush",function(){var U=EN(B,S.x),T=EN(H,S.y);return I=K=null,function(V){B=S.x=U(V),H=S.y=T(V),Q({type:"brush",mode:"resize"})}}).each("end.brush",function(){I=S.i,K=S.j,Q({type:"brush",mode:"resize"}),Q({type:"brushend"})}):(Q({type:"brushstart"}),Q({type:"brush",mode:"resize"}),Q({type:"brushend"}))})},J.x=function(P){return arguments.length?(M=P,E=I4[!M<<1|!L],J):M},J.y=function(P){return arguments.length?(L=P,E=I4[!M<<1|!L],J):L},J.clamp=function(P){return arguments.length?(M&&L?(C=!!P[0],O=!!P[1]):M?C=!!P:L&&(O=!!P),J):M&&L?[C,O]:M?C:L?O:null},J.extent=function(R){var Q,P,S,T,U;return arguments.length?(M&&(Q=R[0],P=R[1],L&&(Q=Q[0],P=P[0]),I=[Q,P],M.invert&&(Q=M(Q),P=M(P)),Q>P&&(U=Q,Q=P,P=U),(Q!=B[0]||P!=B[1])&&(B=[Q,P])),L&&(S=R[0],T=R[1],M&&(S=S[1],T=T[1]),K=[S,T],L.invert&&(S=L(S),T=L(T)),S>T&&(U=S,S=T,T=U),(S!=H[0]||T!=H[1])&&(H=[S,T])),J):(M&&(I?(Q=I[0],P=I[1]):(Q=B[0],P=B[1],M.invert&&(Q=M.invert(Q),P=M.invert(P)),Q>P&&(U=Q,Q=P,P=U))),L&&(K?(S=K[0],T=K[1]):(S=H[0],T=H[1],L.invert&&(S=L.invert(S),T=L.invert(T)),S>T&&(U=S,S=T,T=U))),M&&L?[[Q,S],[P,T]]:M?[Q,P]:L&&[S,T])},J.clear=function(){return J.empty()||(B=[0,0],H=[0,0],I=K=null),J},J.empty=function(){return !!M&&B[0]==B[1]||!!L&&H[0]==H[1]},DT.rebind(J,N,"on")};var JJ={n:"ns-resize",e:"ew-resize",s:"ns-resize",w:"ew-resize",nw:"nwse-resize",ne:"nesw-resize",se:"nwse-resize",sw:"nesw-resize"},I4=[["n","e","s","w","nw","ne","se","sw"],["e","w"],["n","s"],[]],IT=DT.time={},IW=Date,I8=["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"];F6.prototype={getDate:function(){return this._.getUTCDate()},getDay:function(){return this._.getUTCDay()},getFullYear:function(){return this._.getUTCFullYear()},getHours:function(){return this._.getUTCHours()},getMilliseconds:function(){return this._.getUTCMilliseconds()},getMinutes:function(){return this._.getUTCMinutes()},getMonth:function(){return this._.getUTCMonth()},getSeconds:function(){return this._.getUTCSeconds()},getTime:function(){return this._.getTime()},getTimezoneOffset:function(){return 0},valueOf:function(){return this._.valueOf()},setDate:function(){IX.setUTCDate.apply(this._,arguments)},setDay:function(){IX.setUTCDay.apply(this._,arguments)},setFullYear:function(){IX.setUTCFullYear.apply(this._,arguments)},setHours:function(){IX.setUTCHours.apply(this._,arguments)},setMilliseconds:function(){IX.setUTCMilliseconds.apply(this._,arguments)},setMinutes:function(){IX.setUTCMinutes.apply(this._,arguments)},setMonth:function(){IX.setUTCMonth.apply(this._,arguments)},setSeconds:function(){IX.setUTCSeconds.apply(this._,arguments)},setTime:function(){IX.setTime.apply(this._,arguments)}};var IX=Date.prototype,IR="%a %b %e %X %Y",Ad="%m/%d/%Y",In="%H:%M:%S",IG=["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],Ih=["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],Ip=["January","February","March","April","May","June","July","August","September","October","November","December"],Iv=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];IT.year=FZ(function(A){return A=IT.day(A),A.setMonth(0,1),A},function(A,B){A.setFullYear(A.getFullYear()+B)},function(A){return A.getFullYear()}),IT.years=IT.year.range,IT.years.utc=IT.year.utc.range,IT.day=FZ(function(A){var B=new IW(2000,0);return B.setFullYear(A.getFullYear(),A.getMonth(),A.getDate()),B},function(A,B){A.setDate(A.getDate()+B)},function(A){return A.getDate()-1}),IT.days=IT.day.range,IT.days.utc=IT.day.utc.range,IT.dayOfYear=function(A){var B=IT.year(A);return Math.floor((A-B-60000*(A.getTimezoneOffset()-B.getTimezoneOffset()))/86400000)},I8.forEach(function(A,C){A=A.toLowerCase(),C=7-C;var B=IT[A]=FZ(function(D){return(D=IT.day(D)).setDate(D.getDate()-(D.getDay()+C)%7),D},function(D,E){D.setDate(D.getDate()+7*Math.floor(E))},function(D){var E=IT.year(D).getDay();return Math.floor((IT.dayOfYear(D)+(E+C)%7)/7)-(E!==C)});IT[A+"s"]=B.range,IT[A+"s"].utc=B.utc.range,IT[A+"OfYear"]=function(D){var E=IT.year(D).getDay();return Math.floor((IT.dayOfYear(D)+(E+C)%7)/7)}}),IT.week=IT.sunday,IT.weeks=IT.sunday.range,IT.weeks.utc=IT.sunday.utc.range,IT.weekOfYear=IT.sundayOfYear,IT.format=Gp;var Ix=FS(IG),IC=F2(IG),IB=FS(Ih),Iy=F2(Ih),Ij=FS(Ip),ID=F2(Ip),Is=FS(Iv),IE=F2(Iv),Ik=/^%/,IF={"-":"",_:" ",0:"0"},Il={a:function(A){return Ih[A.getDay()]},A:function(A){return IG[A.getDay()]},b:function(A){return Iv[A.getMonth()]},B:function(A){return Ip[A.getMonth()]},c:Gp(IR),d:function(A,B){return FK(A.getDate(),B,2)},e:function(A,B){return FK(A.getDate(),B,2)},H:function(A,B){return FK(A.getHours(),B,2)},I:function(A,B){return FK(A.getHours()%12||12,B,2)},j:function(A,B){return FK(1+IT.dayOfYear(A),B,3)},L:function(A,B){return FK(A.getMilliseconds(),B,3)},m:function(A,B){return FK(A.getMonth()+1,B,2)},M:function(A,B){return FK(A.getMinutes(),B,2)},p:function(A){return A.getHours()>=12?"PM":"AM"},S:function(A,B){return FK(A.getSeconds(),B,2)},U:function(A,B){return FK(IT.sundayOfYear(A),B,2)},w:function(A){return A.getDay()},W:function(A,B){return FK(IT.mondayOfYear(A),B,2)},x:Gp(Ad),X:Gp(In),y:function(A,B){return FK(A.getFullYear()%100,B,2)},Y:function(A,B){return FK(A.getFullYear()%10000,B,4)},Z:DW,"%":function(){return"%"}},Iz={a:FV,A:FI,b:FD,B:FM,c:FE,d:DQ,e:DQ,H:DZ,I:DZ,j:DK,L:DP,m:FL,M:DE,p:DR,S:DL,U:FT,w:FF,W:FP,x:Ab,X:FY,y:FQ,Y:FN,Z:F3,"%":DV},Ig=/^\s*\d+/,H5=DT.map({am:0,pm:1});Gp.utc=DS;var If=DS("%Y-%m-%dT%H:%M:%S.%LZ");Gp.iso=Date.prototype.toISOString&&+new Date("2000-01-01T00:00:00.000Z")?DF:If,DF.parse=function(A){var B=new Date(A);return isNaN(B)?null:B},DF.toString=If.toString,IT.second=FZ(function(A){return new IW(1000*Math.floor(A/1000))},function(A,B){A.setTime(A.getTime()+1000*Math.floor(B))},function(A){return A.getSeconds()}),IT.seconds=IT.second.range,IT.seconds.utc=IT.second.utc.range,IT.minute=FZ(function(A){return new IW(60000*Math.floor(A/60000))},function(A,B){A.setTime(A.getTime()+60000*Math.floor(B))},function(A){return A.getMinutes()}),IT.minutes=IT.minute.range,IT.minutes.utc=IT.minute.utc.range,IT.hour=FZ(function(A){var B=A.getTimezoneOffset()/60;return new IW(3600000*(Math.floor(A/3600000-B)+B))},function(A,B){A.setTime(A.getTime()+3600000*Math.floor(B))},function(A){return A.getHours()}),IT.hours=IT.hour.range,IT.hours.utc=IT.hour.utc.range,IT.month=FZ(function(A){return A=IT.day(A),A.setDate(1),A},function(A,B){A.setMonth(A.getMonth()+B)},function(A){return A.getMonth()}),IT.months=IT.month.range,IT.months.utc=IT.month.utc.range;var IA=[1000,5000,15000,30000,60000,300000,900000,1800000,3600000,10800000,21600000,43200000,86400000,172800000,604800000,2592000000,7776000000,31536000000],II=[[IT.second,1],[IT.second,5],[IT.second,15],[IT.second,30],[IT.minute,1],[IT.minute,5],[IT.minute,15],[IT.minute,30],[IT.hour,1],[IT.hour,3],[IT.hour,6],[IT.hour,12],[IT.day,1],[IT.day,2],[IT.week,1],[IT.month,1],[IT.month,3],[IT.year,1]],Im=[[Gp("%Y"),Hn],[Gp("%B"),function(A){return A.getMonth()}],[Gp("%b %d"),function(A){return 1!=A.getDate()}],[Gp("%a %d"),function(A){return A.getDay()&&1!=A.getDate()}],[Gp("%I %p"),function(A){return A.getHours()}],[Gp("%I:%M"),function(A){return A.getMinutes()}],[Gp(":%S"),function(A){return A.getSeconds()}],[Gp(".%L"),function(A){return A.getMilliseconds()}]],IJ=DY(Im);II.year=IT.year,IT.scale=function(){return DX(DT.scale.linear(),II,IJ)};var Id={range:function(A,C,B){return DT.range(+A,+C,B).map(DO)}},Iq=II.map(function(A){return[A[0].utc,A[1]]}),JL=[[DS("%Y"),Hn],[DS("%B"),function(A){return A.getUTCMonth()}],[DS("%b %d"),function(A){return 1!=A.getUTCDate()}],[DS("%a %d"),function(A){return A.getUTCDay()&&1!=A.getUTCDate()}],[DS("%I %p"),function(A){return A.getUTCHours()}],[DS("%I:%M"),function(A){return A.getUTCMinutes()}],[DS(":%S"),function(A){return A.getUTCSeconds()}],[DS(".%L"),function(A){return A.getUTCMilliseconds()}]],JK=DY(JL);return Iq.year=IT.year.utc,IT.scale.utc=function(){return DX(DT.scale.linear(),Iq,JK)},DT.text=HR(function(A){return A.responseText}),DT.json=function(A,B){return H2(A,"application/json",DG,B)},DT.html=function(A,B){return H2(A,"text/html",DI,B)},DT.xml=HR(function(A){return A.responseXML}),DT}();