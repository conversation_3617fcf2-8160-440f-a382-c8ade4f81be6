(function(A){if(typeof define==="function"&&define.amd){define(["jquery"],A)}else{if(typeof module!=="undefined"&&module.exports){module.exports=A(require("jquery"))}else{A(jQuery)}}}(function(Q,P){
/*
 * jsTree 4.1.0
 * http://jstree.com/
 *
 * Copyright (c) 2014 <PERSON> (http://vakata.com)
 *
 * Licensed same as jquery - under the terms of the MIT License
 *   http://www.opensource.org/licenses/mit-license.php
 */
/*
 * if using jslint please allow for the jQuery global and use following options:
 * jslint: browser: true, ass: true, bitwise: true, continue: true, nomen: true, plusplus: true, regexp: true, unparam: true, todo: true, white: true
 */
if(Q.jstree){}var K=0,N=false,C=false,B=false,G=[],I=Q("script:last").attr("src"),H=window.document,O=H.createElement("LI"),J,<PERSON>;O.setAttribute("role","treeitem");J=H.createElement("I");J.className="jstree-icon jstree-ocl";J.setAttribute("role","presentation");O.appendChild(J);J=H.createElement("A");J.className="jstree-anchor";J.setAttribute("href","#");J.setAttribute("tabindex","-1");L=H.createElement("I");L.className="jstree-icon jstree-themeicon";L.setAttribute("role","presentation");J.appendChild(L);O.appendChild(J);J=L=null;Q.jstree={version:"4.1.0",defaults:{plugins:[]},plugins:{},path:I&&I.indexOf("/")!==-1?I.replace(/\/[^\/]+$/,""):"",idregex:/[\\:&!^|()\[\]<>@*'+~#";.,=\- \/${}%?`]/g,root:"#"};Q.jstree.create=function(R,T){var S=new Q.jstree.core(++K),U=T;T=Q.extend(true,{},Q.jstree.defaults,T);if(U&&U.plugins){T.plugins=U.plugins}Q.each(T.plugins,function(V,W){if(V!=="core"){S=S.plugin(W,T[W])}});Q(R).data("jstree",S);S.init(R,T);return S};Q.jstree.destroy=function(){Q(".jstree:jstree").jstree("destroy");Q(H).off(".jstree")};Q.jstree.core=function(R){this._id=R;this._cnt=0;this._wrk=null;this._data={core:{themes:{name:false,dots:false,icons:false},selected:[],last_error:{},working:false,worker_queue:[],focused:null}}};Q.jstree.reference=function(R){var S=null,T=null;if(R&&R.id&&(!R.tagName||!R.nodeType)){R=R.id}if(!T||!T.length){try{T=Q(R)}catch(U){}}if(!T||!T.length){try{T=Q("#"+R.replace(Q.jstree.idregex,"\\$&"))}catch(U){}}if(T&&T.length&&(T=T.closest(".jstree")).length&&(T=T.data("jstree"))){S=T}else{Q(".jstree").each(function(){var V=Q(this).data("jstree");if(V&&V._model.data[R]){S=V;return false}})}return S};Q.fn.jstree=function(S){var T=(typeof S==="string"),U=Array.prototype.slice.call(arguments,1),R=null;if(S===true&&!this.length){return false}this.each(function(){var V=Q.jstree.reference(this),W=T&&V?V[S]:null;R=T&&W?W.apply(V,U):null;if(!V&&!T&&(S===P||Q.isPlainObject(S))){Q.jstree.create(this,S)}if((V&&!T)||S===true){R=V||false}if(R!==null&&R!==P){return false}});return R!==null&&R!==P?R:this};Q.expr[":"].jstree=Q.expr.createPseudo(function(R){return function(S){return Q(S).hasClass("jstree")&&Q(S).data("jstree")!==P}});Q.jstree.defaults.core={data:false,strings:false,check_callback:false,error:Q.noop,animation:200,multiple:true,themes:{name:false,url:false,dir:false,dots:true,icons:true,stripes:false,variant:false,responsive:false},expand_selected_onload:true,worker:true,force_text:false,dblclick_toggle:true};Q.jstree.core.prototype={plugin:function(T,R){var S=Q.jstree.plugins[T];if(S){this._data[T]={};S.prototype=this;return new S(R,this)}return this},init:function(R,S){this._model={data:{},changed:[],force_full_redraw:false,redraw_timeout:false,default_state:{loaded:true,opened:false,selected:false,disabled:false}};this._model.data[Q.jstree.root]={id:Q.jstree.root,parent:null,parents:[],children:[],children_d:[],state:{loaded:false}};this.element=Q(R).addClass("jstree jstree-"+this._id);this.settings=S;this._data.core.ready=false;this._data.core.loaded=false;this._data.core.rtl=(this.element.css("direction")==="rtl");this.element[this._data.core.rtl?"addClass":"removeClass"]("jstree-rtl");this.element.attr("role","tree");if(this.settings.core.multiple){this.element.attr("aria-multiselectable",true)}if(!this.element.attr("tabindex")){this.element.attr("tabindex","0")}this.bind();this.trigger("init");this._data.core.original_container_html=this.element.find(" > ul > li").clone(true);this._data.core.original_container_html.find("li").addBack().contents().filter(function(){return this.nodeType===3&&(!this.nodeValue||/^\s+$/.test(this.nodeValue))}).remove();this.element.html("<ul class='jstree-container-ul jstree-children' role='group'><li id='j"+this._id+"_loading' class='jstree-initial-node jstree-loading jstree-leaf jstree-last' role='tree-item'><i class='jstree-icon jstree-ocl'></i><a class='jstree-anchor' href='#'><i class='jstree-icon jstree-themeicon-hidden'></i>"+this.get_string("加载中 ...")+"</a></li></ul>");this.element.attr("aria-activedescendant","j"+this._id+"_loading");this._data.core.li_height=this.get_container_ul().children("li").first().height()||24;this.trigger("loading");this.load_node(Q.jstree.root)},destroy:function(S){if(this._wrk){try{window.URL.revokeObjectURL(this._wrk);this._wrk=null}catch(R){}}if(!S){this.element.empty()}this.teardown()},teardown:function(){this.unbind();this.element.removeClass("jstree").removeData("jstree").find("[class^='jstree']").addBack().attr("class",function(){return this.className.replace(/jstree[^ ]*|$/ig,"")});this.element=null},bind:function(){var T="",R=null,S=0;this.element.on("dblclick.jstree",function(U){if(U.target.tagName&&U.target.tagName.toLowerCase()==="input"){return true}if(H.selection&&H.selection.empty){H.selection.empty()}else{if(window.getSelection){var W=window.getSelection();try{W.removeAllRanges();W.collapse()}catch(V){}}}}).on("mousedown.jstree",Q.proxy(function(U){if(U.target===this.element[0]){U.preventDefault();S=+(new Date())}},this)).on("mousedown.jstree",".jstree-ocl",function(U){U.preventDefault()}).on("click.jstree",".jstree-ocl",Q.proxy(function(U){this.toggle_node(U.target)},this)).on("dblclick.jstree",".jstree-anchor",Q.proxy(function(U){if(U.target.tagName&&U.target.tagName.toLowerCase()==="input"){return true}if(this.settings.core.dblclick_toggle){this.toggle_node(U.target)}},this)).on("click.jstree",".jstree-anchor",Q.proxy(function(U){U.preventDefault();if(U.currentTarget!==H.activeElement){Q(U.currentTarget).focus()}this.activate_node(U.currentTarget,U)},this)).on("keydown.jstree",".jstree-anchor",Q.proxy(function(U){if(U.target.tagName&&U.target.tagName.toLowerCase()==="input"){return true}if(U.which!==32&&U.which!==13&&(U.shiftKey||U.ctrlKey||U.altKey||U.metaKey)){return true}var V=null;if(this._data.core.rtl){if(U.which===37){U.which=39}else{if(U.which===39){U.which=37}}}switch(U.which){case 32:if(U.ctrlKey){U.type="click";Q(U.currentTarget).trigger(U)}break;case 13:U.type="click";Q(U.currentTarget).trigger(U);break;case 37:U.preventDefault();if(this.is_open(U.currentTarget)){this.close_node(U.currentTarget)}else{V=this.get_parent(U.currentTarget);if(V&&V.id!==Q.jstree.root){this.get_node(V,true).children(".jstree-anchor").focus()}}break;case 38:U.preventDefault();V=this.get_prev_dom(U.currentTarget);if(V&&V.length){V.children(".jstree-anchor").focus()}break;case 39:U.preventDefault();if(this.is_closed(U.currentTarget)){this.open_node(U.currentTarget,function(W){this.get_node(W,true).children(".jstree-anchor").focus()})}else{if(this.is_open(U.currentTarget)){V=this.get_node(U.currentTarget,true).children(".jstree-children")[0];if(V){Q(this._firstChild(V)).children(".jstree-anchor").focus()}}}break;case 40:U.preventDefault();V=this.get_next_dom(U.currentTarget);if(V&&V.length){V.children(".jstree-anchor").focus()}break;case 106:this.open_all();break;case 36:U.preventDefault();V=this._firstChild(this.get_container_ul()[0]);if(V){Q(V).children(".jstree-anchor").filter(":visible").focus()}break;case 35:U.preventDefault();this.element.find(".jstree-anchor").filter(":visible").last().focus();break}},this)).on("load_node.jstree",Q.proxy(function(U,V){if(V.status){if(V.node.id===Q.jstree.root&&!this._data.core.loaded){this._data.core.loaded=true;if(this._firstChild(this.get_container_ul()[0])){this.element.attr("aria-activedescendant",this._firstChild(this.get_container_ul()[0]).id)}this.trigger("loaded")}if(!this._data.core.ready){setTimeout(Q.proxy(function(){if(this.element&&!this.get_container_ul().find(".jstree-loading").length){this._data.core.ready=true;if(this._data.core.selected.length){if(this.settings.core.expand_selected_onload){var W=[],X,Y;for(X=0,Y=this._data.core.selected.length;X<Y;X++){W=W.concat(this._model.data[this._data.core.selected[X]].parents)}W=Q.vakata.array_unique(W);for(X=0,Y=W.length;X<Y;X++){this.open_node(W[X],false,0)}}this.trigger("changed",{"action":"ready","selected":this._data.core.selected})}this.trigger("ready")}},this),0)}}},this)).on("keypress.jstree",Q.proxy(function(U){if(U.target.tagName&&U.target.tagName.toLowerCase()==="input"){return true}if(R){clearTimeout(R)}R=setTimeout(function(){T=""},500);var W=String.fromCharCode(U.which).toLowerCase(),X=this.element.find(".jstree-anchor").filter(":visible"),V=X.index(H.activeElement)||0,Y=false;T+=W;if(T.length>1){X.slice(V).each(Q.proxy(function(Z,a){if(Q(a).text().toLowerCase().indexOf(T)===0){Q(a).focus();Y=true;return false}},this));if(Y){return}X.slice(0,V).each(Q.proxy(function(Z,a){if(Q(a).text().toLowerCase().indexOf(T)===0){Q(a).focus();Y=true;return false}},this));if(Y){return}}if(new RegExp("^"+W.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&")+"+$").test(T)){X.slice(V+1).each(Q.proxy(function(Z,a){if(Q(a).text().toLowerCase().charAt(0)===W){Q(a).focus();Y=true;return false}},this));if(Y){return}X.slice(0,V+1).each(Q.proxy(function(Z,a){if(Q(a).text().toLowerCase().charAt(0)===W){Q(a).focus();Y=true;return false}},this));if(Y){return}}},this)).on("init.jstree",Q.proxy(function(){var U=this.settings.core.themes;this._data.core.themes.dots=U.dots;this._data.core.themes.stripes=U.stripes;this._data.core.themes.icons=U.icons;this.set_theme(U.name||"default",U.url);this.set_theme_variant(U.variant)},this)).on("loading.jstree",Q.proxy(function(){this[this._data.core.themes.dots?"show_dots":"hide_dots"]();this[this._data.core.themes.icons?"show_icons":"hide_icons"]();this[this._data.core.themes.stripes?"show_stripes":"hide_stripes"]()},this)).on("blur.jstree",".jstree-anchor",Q.proxy(function(U){this._data.core.focused=null;Q(U.currentTarget).filter(".jstree-hovered").mouseleave();this.element.attr("tabindex","0")},this)).on("focus.jstree",".jstree-anchor",Q.proxy(function(V){var U=this.get_node(V.currentTarget);if(U&&U.id){this._data.core.focused=U.id}this.element.find(".jstree-hovered").not(V.currentTarget).mouseleave();Q(V.currentTarget).mouseenter();this.element.attr("tabindex","-1")},this)).on("focus.jstree",Q.proxy(function(){if(+(new Date())-S>500&&!this._data.core.focused){S=0;var U=this.get_node(this.element.attr("aria-activedescendant"),true);if(U){U.find("> .jstree-anchor").focus()}}},this)).on("mouseenter.jstree",".jstree-anchor",Q.proxy(function(U){this.hover_node(U.currentTarget)},this)).on("mouseleave.jstree",".jstree-anchor",Q.proxy(function(U){this.dehover_node(U.currentTarget)},this))},unbind:function(){this.element.off(".jstree");Q(H).off(".jstree-"+this._id)},trigger:function(R,S){if(!S){S={}}S.instance=this;this.element.triggerHandler(R.replace(".jstree","")+".jstree",S)},get_container:function(){return this.element},get_container_ul:function(){return this.element.children(".jstree-children").first()},get_string:function(R){var S=this.settings.core.strings;if(Q.isFunction(S)){return S.call(this,R)}if(S&&S[R]){return S[R]}return R},_firstChild:function(R){R=R?R.firstChild:null;while(R!==null&&R.nodeType!==1){R=R.nextSibling}return R},_nextSibling:function(R){R=R?R.nextSibling:null;while(R!==null&&R.nodeType!==1){R=R.nextSibling}return R},_previousSibling:function(R){R=R?R.previousSibling:null;while(R!==null&&R.nodeType!==1){R=R.previousSibling}return R},get_node:function(S,R){if(S&&S.id){S=S.id}var U;try{if(this._model.data[S]){S=this._model.data[S]}else{if(typeof S==="string"&&this._model.data[S.replace(/^#/,"")]){S=this._model.data[S.replace(/^#/,"")]}else{if(typeof S==="string"&&(U=Q("#"+S.replace(Q.jstree.idregex,"\\$&"),this.element)).length&&this._model.data[U.closest(".jstree-node").attr("id")]){S=this._model.data[U.closest(".jstree-node").attr("id")]}else{if((U=Q(S,this.element)).length&&this._model.data[U.closest(".jstree-node").attr("id")]){S=this._model.data[U.closest(".jstree-node").attr("id")]}else{if((U=Q(S,this.element)).length&&U.hasClass("jstree")){S=this._model.data[Q.jstree.root]}else{return false}}}}}if(R){S=S.id===Q.jstree.root?this.element:Q("#"+S.id.replace(Q.jstree.idregex,"\\$&"),this.element)}return S}catch(T){return false}},get_path:function(S,U,V){S=S.parents?S:this.get_node(S);if(!S||S.id===Q.jstree.root||!S.parents){return false}var R,W,T=[];T.push(V?S.id:S.text);for(R=0,W=S.parents.length;R<W;R++){T.push(V?S.parents[R]:this.get_text(S.parents[R]))}T=T.reverse().slice(1);return U?T.join(U):T},get_next_dom:function(S,T){var R;S=this.get_node(S,true);if(S[0]===this.element[0]){R=this._firstChild(this.get_container_ul()[0]);while(R&&R.offsetHeight===0){R=this._nextSibling(R)}return R?Q(R):false}if(!S||!S.length){return false}if(T){R=S[0];do{R=this._nextSibling(R)}while(R&&R.offsetHeight===0);return R?Q(R):false}if(S.hasClass("jstree-open")){R=this._firstChild(S.children(".jstree-children")[0]);while(R&&R.offsetHeight===0){R=this._nextSibling(R)}if(R!==null){return Q(R)}}R=S[0];do{R=this._nextSibling(R)}while(R&&R.offsetHeight===0);if(R!==null){return Q(R)}return S.parentsUntil(".jstree",".jstree-node").nextAll(".jstree-node:visible").first()},get_prev_dom:function(S,T){var R;S=this.get_node(S,true);if(S[0]===this.element[0]){R=this.get_container_ul()[0].lastChild;while(R&&R.offsetHeight===0){R=this._previousSibling(R)}return R?Q(R):false}if(!S||!S.length){return false}if(T){R=S[0];do{R=this._previousSibling(R)}while(R&&R.offsetHeight===0);return R?Q(R):false}R=S[0];do{R=this._previousSibling(R)}while(R&&R.offsetHeight===0);if(R!==null){S=Q(R);while(S.hasClass("jstree-open")){S=S.children(".jstree-children").first().children(".jstree-node:visible:last")}return S}R=S[0].parentNode.parentNode;return R&&R.className&&R.className.indexOf("jstree-node")!==-1?Q(R):false},get_parent:function(R){R=this.get_node(R);if(!R||R.id===Q.jstree.root){return false}return R.parent},get_children_dom:function(R){R=this.get_node(R,true);if(R[0]===this.element[0]){return this.get_container_ul().children(".jstree-node")}if(!R||!R.length){return false}return R.children(".jstree-children").children(".jstree-node")},is_parent:function(R){R=this.get_node(R);return R&&(R.state.loaded===false||R.children.length>0)},is_loaded:function(R){R=this.get_node(R);return R&&R.state.loaded},is_loading:function(R){R=this.get_node(R);return R&&R.state&&R.state.loading},is_open:function(R){R=this.get_node(R);return R&&R.state.opened},is_closed:function(R){R=this.get_node(R);return R&&this.is_parent(R)&&!R.state.opened},is_leaf:function(R){return !this.is_parent(R)},load_node:function(T,R){var X,U,S,W,V;if(Q.isArray(T)){this._load_nodes(T.slice(),R);return true}T=this.get_node(T);if(!T){if(R){R.call(this,T,false)}return false}if(T.state.loaded){T.state.loaded=false;for(X=0,U=T.children_d.length;X<U;X++){for(S=0,W=T.parents.length;S<W;S++){this._model.data[T.parents[S]].children_d=Q.vakata.array_remove_item(this._model.data[T.parents[S]].children_d,T.children_d[X])}if(this._model.data[T.children_d[X]].state.selected){V=true;this._data.core.selected=Q.vakata.array_remove_item(this._data.core.selected,T.children_d[X])}delete this._model.data[T.children_d[X]]}T.children=[];T.children_d=[];if(V){this.trigger("changed",{"action":"load_node","node":T,"selected":this._data.core.selected})}}T.state.failed=false;T.state.loading=true;this.get_node(T,true).addClass("jstree-loading").attr("aria-busy",true);this._load_node(T,Q.proxy(function(a){T=this._model.data[T.id];T.state.loading=false;T.state.loaded=a;T.state.failed=!T.state.loaded;var Y=this.get_node(T,true),Z=0,d=0,b=this._model.data,c=false;for(Z=0,d=T.children.length;Z<d;Z++){if(b[T.children[Z]]&&!b[T.children[Z]].state.hidden){c=true;break}}if(T.state.loaded&&!c&&Y&&Y.length&&!Y.hasClass("jstree-leaf")){Y.removeClass("jstree-closed jstree-open").addClass("jstree-leaf")}Y.removeClass("jstree-loading").attr("aria-busy",false);this.trigger("load_node",{"node":T,"status":a});if(R){R.call(this,T,a)}},this));return true},_load_nodes:function(Z,S,Y){var R=true,X=function(){this._load_nodes(Z,S,true)},W=this._model.data,V,T,U=[];for(V=0,T=Z.length;V<T;V++){if(W[Z[V]]&&((!W[Z[V]].state.loaded&&!W[Z[V]].state.failed)||!Y)){if(!this.is_loading(Z[V])){this.load_node(Z[V],X)}R=false}}if(R){for(V=0,T=Z.length;V<T;V++){if(W[Z[V]]&&W[Z[V]].state.loaded){U.push(Z[V])}}if(S&&!S.done){S.call(this,U);S.done=true}}},load_all:function(T,R){if(!T){T=Q.jstree.root}T=this.get_node(T);if(!T){return false}var S=[],V=this._model.data,W=V[T.id].children_d,U,X;if(T.state&&!T.state.loaded){S.push(T.id)}for(U=0,X=W.length;U<X;U++){if(V[W[U]]&&V[W[U]].state&&!V[W[U]].state.loaded){S.push(W[U])}}if(S.length){this._load_nodes(S,function(){this.load_all(T,R)})}else{if(R){R.call(this,T)}this.trigger("load_all",{"node":T})}},_load_node:function(S,R){var T=this.settings.core.data,U;if(!T){if(S.id===Q.jstree.root){return this._append_html_data(S,this._data.core.original_container_html.clone(true),function(V){R.call(this,V)})}else{return R.call(this,false)}}if(Q.isFunction(T)){return T.call(this,S,Q.proxy(function(V){if(V===false){R.call(this,false)}this[typeof V==="string"?"_append_html_data":"_append_json_data"](S,typeof V==="string"?Q(Q.parseHTML(V)).filter(function(){return this.nodeType!==3}):V,function(W){R.call(this,W)})},this))}if(typeof T==="object"){if(T.url){T=Q.extend(true,{},T);if(Q.isFunction(T.url)){T.url=T.url.call(this,S)}if(Q.isFunction(T.data)){T.data=T.data.call(this,S)}return Q.ajax(T).done(Q.proxy(function(W,Y,X){var V=X.getResponseHeader("Content-Type");if((V&&V.indexOf("json")!==-1)||typeof W==="object"){return this._append_json_data(S,W,function(Z){R.call(this,Z)})}if((V&&V.indexOf("html")!==-1)||typeof W==="string"){return this._append_html_data(S,Q(Q.parseHTML(W)).filter(function(){return this.nodeType!==3}),function(Z){R.call(this,Z)})}this._data.core.last_error={"error":"ajax","plugin":"core","id":"core_04","reason":"Could not load node","data":JSON.stringify({"id":S.id,"xhr":X})};this.settings.core.error.call(this,this._data.core.last_error);return R.call(this,false)},this)).fail(Q.proxy(function(V){R.call(this,false);this._data.core.last_error={"error":"ajax","plugin":"core","id":"core_04","reason":"Could not load node","data":JSON.stringify({"id":S.id,"xhr":V})};this.settings.core.error.call(this,this._data.core.last_error)},this))}U=(Q.isArray(T)||Q.isPlainObject(T))?JSON.parse(JSON.stringify(T)):T;if(S.id===Q.jstree.root){return this._append_json_data(S,U,function(V){R.call(this,V)})}else{this._data.core.last_error={"error":"nodata","plugin":"core","id":"core_05","reason":"Could not load node","data":JSON.stringify({"id":S.id})};this.settings.core.error.call(this,this._data.core.last_error);return R.call(this,false)}}if(typeof T==="string"){if(S.id===Q.jstree.root){return this._append_html_data(S,Q(Q.parseHTML(T)).filter(function(){return this.nodeType!==3}),function(V){R.call(this,V)})}else{this._data.core.last_error={"error":"nodata","plugin":"core","id":"core_06","reason":"Could not load node","data":JSON.stringify({"id":S.id})};this.settings.core.error.call(this,this._data.core.last_error);return R.call(this,false)}}return R.call(this,false)},_node_changed:function(R){R=this.get_node(R);if(R){this._model.changed.push(R.id)}},_append_html_data:function(d,U,T){d=this.get_node(d);d.children=[];d.children_d=[];var b=U.is("ul")?U.children():U,W=d.id,Z=[],V=[],c=this._model.data,S=c[W],R=this._data.core.selected.length,Y,a,X;b.each(Q.proxy(function(e,f){Y=this._parse_model_from_html(Q(f),W,S.parents.concat());if(Y){Z.push(Y);V.push(Y);if(c[Y].children_d.length){V=V.concat(c[Y].children_d)}}},this));S.children=Z;S.children_d=V;for(a=0,X=S.parents.length;a<X;a++){c[S.parents[a]].children_d=c[S.parents[a]].children_d.concat(V)}this.trigger("model",{"nodes":V,"parent":W});if(W!==Q.jstree.root){this._node_changed(W);this.redraw()}else{this.get_container_ul().children(".jstree-initial-node").remove();this.redraw(true)}if(this._data.core.selected.length!==R){this.trigger("changed",{"action":"model","selected":this._data.core.selected})}T.call(this,true)},_append_json_data:function(Y,U,T,R){if(this.element===null){return}Y=this.get_node(Y);Y.children=[];Y.children_d=[];if(U.d){U=U.d;if(typeof U==="string"){U=JSON.parse(U)}}if(!Q.isArray(U)){U=[U]}var V=null,S={"df":this._model.default_state,"dat":U,"par":Y.id,"m":this._model.data,"t_id":this._id,"t_cnt":this._cnt,"sel":this._data.core.selected},Z=function(t,w){if(t.data){t=t.data}var v=t.dat,q=t.par,g=[],c=[],s=[],u=t.df,n=t.t_id,l=t.t_cnt,h=t.m,a=h[q],r=t.sel,b,f,e,d,o=function(Ae,x,Aa){if(!Aa){Aa=[]}else{Aa=Aa.concat()}if(x){Aa.unshift(x)}var Ad=Ae.id.toString(),Ab,y,Ac,m,z={id:Ad,text:Ae.text||"",icon:Ae.icon!==w?Ae.icon:true,parent:x,parents:Aa,children:Ae.children||[],children_d:Ae.children_d||[],data:Ae.data,state:{},li_attr:{id:false},a_attr:{href:"#"},original:false};for(Ab in u){if(u.hasOwnProperty(Ab)){z.state[Ab]=u[Ab]}}if(Ae&&Ae.data&&Ae.data.jstree&&Ae.data.jstree.icon){z.icon=Ae.data.jstree.icon}if(z.icon===w||z.icon===null||z.icon===""){z.icon=true}if(Ae&&Ae.data){z.data=Ae.data;if(Ae.data.jstree){for(Ab in Ae.data.jstree){if(Ae.data.jstree.hasOwnProperty(Ab)){z.state[Ab]=Ae.data.jstree[Ab]}}}}if(Ae&&typeof Ae.state==="object"){for(Ab in Ae.state){if(Ae.state.hasOwnProperty(Ab)){z.state[Ab]=Ae.state[Ab]}}}if(Ae&&typeof Ae.li_attr==="object"){for(Ab in Ae.li_attr){if(Ae.li_attr.hasOwnProperty(Ab)){z.li_attr[Ab]=Ae.li_attr[Ab]}}}if(!z.li_attr.id){z.li_attr.id=Ad}if(Ae&&typeof Ae.a_attr==="object"){for(Ab in Ae.a_attr){if(Ae.a_attr.hasOwnProperty(Ab)){z.a_attr[Ab]=Ae.a_attr[Ab]}}}if(Ae&&Ae.children&&Ae.children===true){z.state.loaded=false;z.children=[];z.children_d=[]}h[z.id]=z;for(Ab=0,y=z.children.length;Ab<y;Ab++){Ac=o(h[z.children[Ab]],z.id,Aa);m=h[Ac];z.children_d.push(Ac);if(m.children_d.length){z.children_d=z.children_d.concat(m.children_d)}}delete Ae.data;delete Ae.children;h[z.id].original=Ae;if(z.state.selected){s.push(z.id)}return z.id},k=function(Ae,x,Aa){if(!Aa){Aa=[]}else{Aa=Aa.concat()}if(x){Aa.unshift(x)}var Ad=false,Ab,y,Ac,m,z;do{Ad="j"+n+"_"+(++l)}while(h[Ad]);z={id:false,text:typeof Ae==="string"?Ae:"",icon:typeof Ae==="object"&&Ae.icon!==w?Ae.icon:true,parent:x,parents:Aa,children:[],children_d:[],data:null,state:{},li_attr:{id:false},a_attr:{href:"#"},original:false};for(Ab in u){if(u.hasOwnProperty(Ab)){z.state[Ab]=u[Ab]}}if(Ae&&Ae.id){z.id=Ae.id.toString()}if(Ae&&Ae.text){z.text=Ae.text}if(Ae&&Ae.data&&Ae.data.jstree&&Ae.data.jstree.icon){z.icon=Ae.data.jstree.icon}if(z.icon===w||z.icon===null||z.icon===""){z.icon=true}if(Ae&&Ae.data){z.data=Ae.data;if(Ae.data.jstree){for(Ab in Ae.data.jstree){if(Ae.data.jstree.hasOwnProperty(Ab)){z.state[Ab]=Ae.data.jstree[Ab]}}}}if(Ae&&typeof Ae.state==="object"){for(Ab in Ae.state){if(Ae.state.hasOwnProperty(Ab)){z.state[Ab]=Ae.state[Ab]}}}if(Ae&&typeof Ae.li_attr==="object"){for(Ab in Ae.li_attr){if(Ae.li_attr.hasOwnProperty(Ab)){z.li_attr[Ab]=Ae.li_attr[Ab]}}}if(z.li_attr.id&&!z.id){z.id=z.li_attr.id.toString()}if(!z.id){z.id=Ad}if(!z.li_attr.id){z.li_attr.id=z.id}if(Ae&&typeof Ae.a_attr==="object"){for(Ab in Ae.a_attr){if(Ae.a_attr.hasOwnProperty(Ab)){z.a_attr[Ab]=Ae.a_attr[Ab]}}}if(Ae&&Ae.children&&Ae.children.length){for(Ab=0,y=Ae.children.length;Ab<y;Ab++){Ac=k(Ae.children[Ab],z.id,Aa);m=h[Ac];z.children.push(Ac);if(m.children_d.length){z.children_d=z.children_d.concat(m.children_d)}}z.children_d=z.children_d.concat(z.children)}if(Ae&&Ae.children&&Ae.children===true){z.state.loaded=false;z.children=[];z.children_d=[]}delete Ae.data;delete Ae.children;z.original=Ae;h[z.id]=z;if(z.state.selected){s.push(z.id)}return z.id};if(v.length&&v[0].id!==w&&v[0].parent!==w){for(f=0,e=v.length;f<e;f++){if(!v[f].children){v[f].children=[]}h[v[f].id.toString()]=v[f]}for(f=0,e=v.length;f<e;f++){h[v[f].parent.toString()].children.push(v[f].id.toString());a.children_d.push(v[f].id.toString())}for(f=0,e=a.children.length;f<e;f++){b=o(h[a.children[f]],q,a.parents.concat());c.push(b);if(h[b].children_d.length){c=c.concat(h[b].children_d)}}for(f=0,e=a.parents.length;f<e;f++){h[a.parents[f]].children_d=h[a.parents[f]].children_d.concat(c)}d={"cnt":l,"mod":h,"sel":r,"par":q,"dpc":c,"add":s}}else{for(f=0,e=v.length;f<e;f++){b=k(v[f],q,a.parents.concat());if(b){g.push(b);c.push(b);if(h[b].children_d.length){c=c.concat(h[b].children_d)}}}a.children=g;a.children_d=c;for(f=0,e=a.parents.length;f<e;f++){h[a.parents[f]].children_d=h[a.parents[f]].children_d.concat(c)}d={"cnt":l,"mod":h,"sel":r,"par":q,"dpc":c,"add":s}}if(typeof window==="undefined"||typeof window.document==="undefined"){postMessage(d)}else{return d}},X=function(f,k){if(this.element===null){return}this._cnt=f.cnt;this._model.data=f.mod;if(k){var e,d,h=f.add,b=f.sel,c=this._data.core.selected.slice(),g=this._model.data;if(b.length!==c.length||Q.vakata.array_unique(b.concat(c)).length!==b.length){for(e=0,d=b.length;e<d;e++){if(Q.inArray(b[e],h)===-1&&Q.inArray(b[e],c)===-1){g[b[e]].state.selected=false}}for(e=0,d=c.length;e<d;e++){if(Q.inArray(c[e],b)===-1){g[c[e]].state.selected=true}}}}if(f.add.length){this._data.core.selected=this._data.core.selected.concat(f.add)}this.trigger("model",{"nodes":f.dpc,"parent":f.par});if(f.par!==Q.jstree.root){this._node_changed(f.par);this.redraw()}else{this.redraw(true)}if(f.add.length){this.trigger("changed",{"action":"model","selected":this._data.core.selected})}T.call(this,true)};if(this.settings.core.worker&&window.Blob&&window.URL&&window.Worker){try{if(this._wrk===null){this._wrk=window.URL.createObjectURL(new window.Blob(["self.onmessage = "+Z.toString()],{type:"text/javascript"}))}if(!this._data.core.working||R){this._data.core.working=true;V=new window.Worker(this._wrk);V.onmessage=Q.proxy(function(a){X.call(this,a.data,true);try{V.terminate();V=null}catch(b){}if(this._data.core.worker_queue.length){this._append_json_data.apply(this,this._data.core.worker_queue.shift())}else{this._data.core.working=false}},this);if(!S.par){if(this._data.core.worker_queue.length){this._append_json_data.apply(this,this._data.core.worker_queue.shift())}else{this._data.core.working=false}}else{V.postMessage(S)}}else{this._data.core.worker_queue.push([Y,U,T,true])}}catch(W){X.call(this,Z(S),false);if(this._data.core.worker_queue.length){this._append_json_data.apply(this,this._data.core.worker_queue.shift())}else{this._data.core.working=false}}}else{X.call(this,Z(S),false)}},_parse_model_from_html:function(a,S,V){if(!V){V=[]}else{V=[].concat(V)}if(S){V.unshift(S)}var Y,R,X=this._model.data,T={id:false,text:false,icon:true,parent:S,parents:V,children:[],children_d:[],data:null,state:{},li_attr:{id:false},a_attr:{href:"#"},original:false},W,U,Z;for(W in this._model.default_state){if(this._model.default_state.hasOwnProperty(W)){T.state[W]=this._model.default_state[W]}}U=Q.vakata.attributes(a,true);Q.each(U,function(b,c){c=Q.trim(c);if(!c.length){return true}T.li_attr[b]=c;if(b==="id"){T.id=c.toString()}});U=a.children("a").first();if(U.length){U=Q.vakata.attributes(U,true);Q.each(U,function(b,c){c=Q.trim(c);if(c.length){T.a_attr[b]=c}})}U=a.children("a").first().length?a.children("a").first().clone():a.clone();U.children("ins, i, ul").remove();U=U.html();U=Q("<div />").html(U);T.text=this.settings.core.force_text?U.text():U.html();U=a.data();T.data=U?Q.extend(true,{},U):null;T.state.opened=a.hasClass("jstree-open");T.state.selected=a.children("a").hasClass("jstree-clicked");T.state.disabled=a.children("a").hasClass("jstree-disabled");if(T.data&&T.data.jstree){for(W in T.data.jstree){if(T.data.jstree.hasOwnProperty(W)){T.state[W]=T.data.jstree[W]}}}U=a.children("a").children(".jstree-themeicon");if(U.length){T.icon=U.hasClass("jstree-themeicon-hidden")?false:U.attr("rel")}if(T.state.icon!==P){T.icon=T.state.icon}if(T.icon===P||T.icon===null||T.icon===""){T.icon=true}U=a.children("ul").children("li");do{Z="j"+this._id+"_"+(++this._cnt)}while(X[Z]);T.id=T.li_attr.id?T.li_attr.id.toString():Z;if(U.length){U.each(Q.proxy(function(b,c){Y=this._parse_model_from_html(Q(c),T.id,V);R=this._model.data[Y];T.children.push(Y);if(R.children_d.length){T.children_d=T.children_d.concat(R.children_d)}},this));T.children_d=T.children_d.concat(T.children)}else{if(a.hasClass("jstree-closed")){T.state.loaded=false}}if(T.li_attr["class"]){T.li_attr["class"]=T.li_attr["class"].replace("jstree-closed","").replace("jstree-open","")}if(T.a_attr["class"]){T.a_attr["class"]=T.a_attr["class"].replace("jstree-clicked","").replace("jstree-disabled","")}X[T.id]=T;if(T.state.selected){this._data.core.selected.push(T.id)}return T.id},_parse_model_from_flat_json:function(b,S,W){if(!W){W=[]}else{W=W.concat()}if(S){W.unshift(S)}var a=b.id.toString(),Y=this._model.data,T=this._model.default_state,X,U,Z,R,V={id:a,text:b.text||"",icon:b.icon!==P?b.icon:true,parent:S,parents:W,children:b.children||[],children_d:b.children_d||[],data:b.data,state:{},li_attr:{id:false},a_attr:{href:"#"},original:false};for(X in T){if(T.hasOwnProperty(X)){V.state[X]=T[X]}}if(b&&b.data&&b.data.jstree&&b.data.jstree.icon){V.icon=b.data.jstree.icon}if(V.icon===P||V.icon===null||V.icon===""){V.icon=true}if(b&&b.data){V.data=b.data;if(b.data.jstree){for(X in b.data.jstree){if(b.data.jstree.hasOwnProperty(X)){V.state[X]=b.data.jstree[X]}}}}if(b&&typeof b.state==="object"){for(X in b.state){if(b.state.hasOwnProperty(X)){V.state[X]=b.state[X]}}}if(b&&typeof b.li_attr==="object"){for(X in b.li_attr){if(b.li_attr.hasOwnProperty(X)){V.li_attr[X]=b.li_attr[X]}}}if(!V.li_attr.id){V.li_attr.id=a}if(b&&typeof b.a_attr==="object"){for(X in b.a_attr){if(b.a_attr.hasOwnProperty(X)){V.a_attr[X]=b.a_attr[X]}}}if(b&&b.children&&b.children===true){V.state.loaded=false;V.children=[];V.children_d=[]}Y[V.id]=V;for(X=0,U=V.children.length;X<U;X++){Z=this._parse_model_from_flat_json(Y[V.children[X]],V.id,W);R=Y[Z];V.children_d.push(Z);if(R.children_d.length){V.children_d=V.children_d.concat(R.children_d)}}delete b.data;delete b.children;Y[V.id].original=b;if(V.state.selected){this._data.core.selected.push(V.id)}return V.id},_parse_model_from_json:function(b,S,W){if(!W){W=[]}else{W=W.concat()}if(S){W.unshift(S)}var a=false,X,U,Z,R,Y=this._model.data,T=this._model.default_state,V;do{a="j"+this._id+"_"+(++this._cnt)}while(Y[a]);V={id:false,text:typeof b==="string"?b:"",icon:typeof b==="object"&&b.icon!==P?b.icon:true,parent:S,parents:W,children:[],children_d:[],data:null,state:{},li_attr:{id:false},a_attr:{href:"#"},original:false};for(X in T){if(T.hasOwnProperty(X)){V.state[X]=T[X]}}if(b&&b.id){V.id=b.id.toString()}if(b&&b.text){V.text=b.text}if(b&&b.data&&b.data.jstree&&b.data.jstree.icon){V.icon=b.data.jstree.icon}if(V.icon===P||V.icon===null||V.icon===""){V.icon=true}if(b&&b.data){V.data=b.data;if(b.data.jstree){for(X in b.data.jstree){if(b.data.jstree.hasOwnProperty(X)){V.state[X]=b.data.jstree[X]}}}}if(b&&typeof b.state==="object"){for(X in b.state){if(b.state.hasOwnProperty(X)){V.state[X]=b.state[X]}}}if(b&&typeof b.li_attr==="object"){for(X in b.li_attr){if(b.li_attr.hasOwnProperty(X)){V.li_attr[X]=b.li_attr[X]}}}if(V.li_attr.id&&!V.id){V.id=V.li_attr.id.toString()}if(!V.id){V.id=a}if(!V.li_attr.id){V.li_attr.id=V.id}if(b&&typeof b.a_attr==="object"){for(X in b.a_attr){if(b.a_attr.hasOwnProperty(X)){V.a_attr[X]=b.a_attr[X]}}}if(b&&b.children&&b.children.length){for(X=0,U=b.children.length;X<U;X++){Z=this._parse_model_from_json(b.children[X],V.id,W);R=Y[Z];V.children.push(Z);if(R.children_d.length){V.children_d=V.children_d.concat(R.children_d)}}V.children_d=V.children_d.concat(V.children)}if(b&&b.children&&b.children===true){V.state.loaded=false;V.children=[];V.children_d=[]}delete b.data;delete b.children;V.original=b;Y[V.id]=V;if(V.state.selected){this._data.core.selected.push(V.id)}return V.id},_redraw:function(){var U=this._model.force_full_redraw?this._model.data[Q.jstree.root].children.concat([]):this._model.changed.concat([]),W=H.createElement("UL"),R,S,V,T=this._data.core.focused;for(S=0,V=U.length;S<V;S++){R=this.redraw_node(U[S],true,this._model.force_full_redraw);if(R&&this._model.force_full_redraw){W.appendChild(R)}}if(this._model.force_full_redraw){W.className=this.get_container_ul()[0].className;W.setAttribute("role","group");this.element.empty().append(W)}if(T!==null){R=this.get_node(T,true);if(R&&R.length&&R.children(".jstree-anchor")[0]!==H.activeElement){R.children(".jstree-anchor").focus()}else{this._data.core.focused=null}}this._model.force_full_redraw=false;this._model.changed=[];this.trigger("redraw",{"nodes":U})},redraw:function(R){if(R){this._model.force_full_redraw=true}this._redraw()},draw_children:function(U){var S=this.get_node(U),R=false,V=false,W=false,T=H;if(!S){return false}if(S.id===Q.jstree.root){return this.redraw(true)}U=this.get_node(U,true);if(!U||!U.length){return false}U.children(".jstree-children").remove();U=U[0];if(S.children.length&&S.state.loaded){W=T.createElement("UL");W.setAttribute("role","group");W.className="jstree-children";for(R=0,V=S.children.length;R<V;R++){W.appendChild(this.redraw_node(S.children[R],true,true))}U.appendChild(W)}},redraw_node:function(u,v,r,o){var Y=this.get_node(u),p=false,U=false,q=false,X=false,V=false,W=false,e="",h=H,a=this._model.data,g=false,R=false,S=null,T=0,Z=0,n=false,b=false;if(!Y){return false}if(Y.id===Q.jstree.root){return this.redraw(true)}v=v||Y.children.length===0;u=!H.querySelector?H.getElementById(Y.id):this.element[0].querySelector("#"+("0123456789".indexOf(Y.id[0])!==-1?"\\3"+Y.id[0]+" "+Y.id.substr(1).replace(Q.jstree.idregex,"\\$&"):Y.id.replace(Q.jstree.idregex,"\\$&")));if(!u){v=true;if(!r){p=Y.parent!==Q.jstree.root?Q("#"+Y.parent.replace(Q.jstree.idregex,"\\$&"),this.element)[0]:null;if(p!==null&&(!p||!a[Y.parent].state.opened)){return false}U=Q.inArray(Y.id,p===null?a[Q.jstree.root].children:a[Y.parent].children)}}else{u=Q(u);if(!r){p=u.parent().parent()[0];if(p===this.element[0]){p=null}U=u.index()}if(!v&&Y.children.length&&!u.children(".jstree-children").length){v=true}if(!v){q=u.children(".jstree-children")[0]}g=u.children(".jstree-anchor")[0]===H.activeElement;u.remove()}u=O.cloneNode(true);e="jstree-node ";for(X in Y.li_attr){if(Y.li_attr.hasOwnProperty(X)){if(X==="id"){continue}if(X!=="class"){u.setAttribute(X,Y.li_attr[X])}else{e+=Y.li_attr[X]}}}if(!Y.a_attr.id){Y.a_attr.id=Y.id+"_anchor"}u.setAttribute("aria-selected",!!Y.state.selected);u.setAttribute("aria-level",Y.parents.length);u.setAttribute("aria-labelledby",Y.a_attr.id);if(Y.state.disabled){u.setAttribute("aria-disabled",true)}for(X=0,V=Y.children.length;X<V;X++){if(!a[Y.children[X]].state.hidden){n=true;break}}if(Y.parent!==null&&a[Y.parent]&&!Y.state.hidden){X=Q.inArray(Y.id,a[Y.parent].children);b=Y.id;if(X!==-1){X++;for(V=a[Y.parent].children.length;X<V;X++){if(!a[a[Y.parent].children[X]].state.hidden){b=a[Y.parent].children[X]}if(b!==Y.id){break}}}}if(Y.state.hidden){e+=" jstree-hidden"}if(Y.state.loaded&&!n){e+=" jstree-leaf"}else{e+=Y.state.opened&&Y.state.loaded?" jstree-open":" jstree-closed";u.setAttribute("aria-expanded",(Y.state.opened&&Y.state.loaded))}if(b===Y.id){e+=" jstree-last"}u.id=Y.id;u.className=e;e=(Y.state.selected?" jstree-clicked":"")+(Y.state.disabled?" jstree-disabled":"");for(V in Y.a_attr){if(Y.a_attr.hasOwnProperty(V)){if(V==="href"&&Y.a_attr[V]==="#"){continue}if(V!=="class"){u.childNodes[1].setAttribute(V,Y.a_attr[V])}else{e+=" "+Y.a_attr[V]}}}if(e.length){u.childNodes[1].className="jstree-anchor "+e}if((Y.icon&&Y.icon!==true)||Y.icon===false){if(Y.icon===false){u.childNodes[1].childNodes[0].className+=" jstree-themeicon-hidden"}else{if(Y.icon.indexOf("/")===-1&&Y.icon.indexOf(".")===-1){u.childNodes[1].childNodes[0].className+=" "+Y.icon+" jstree-themeicon-custom"}else{u.childNodes[1].childNodes[0].style.backgroundImage="url("+Y.icon+")";u.childNodes[1].childNodes[0].style.backgroundPosition="center center";u.childNodes[1].childNodes[0].style.backgroundSize="auto";u.childNodes[1].childNodes[0].className+=" jstree-themeicon-custom"}}}if(this.settings.core.force_text){u.childNodes[1].appendChild(h.createTextNode(Y.text))}else{u.childNodes[1].innerHTML+=Y.text}if(v&&Y.children.length&&(Y.state.opened||o)&&Y.state.loaded){W=h.createElement("UL");W.setAttribute("role","group");W.className="jstree-children";for(X=0,V=Y.children.length;X<V;X++){W.appendChild(this.redraw_node(Y.children[X],v,true))}u.appendChild(W)}if(q){u.appendChild(q)}if(!r){if(!p){p=this.element[0]}for(X=0,V=p.childNodes.length;X<V;X++){if(p.childNodes[X]&&p.childNodes[X].className&&p.childNodes[X].className.indexOf("jstree-children")!==-1){S=p.childNodes[X];break}}if(!S){S=h.createElement("UL");S.setAttribute("role","group");S.className="jstree-children";p.appendChild(S)}p=S;if(U<p.childNodes.length){p.insertBefore(u,p.childNodes[U])}else{p.appendChild(u)}if(g){T=this.element[0].scrollTop;Z=this.element[0].scrollLeft;u.childNodes[1].focus();this.element[0].scrollTop=T;this.element[0].scrollLeft=Z}}if(Y.state.opened&&!Y.state.loaded){Y.state.opened=false;setTimeout(Q.proxy(function(){this.open_node(Y.id,false,0)},this),0)}return u},open_node:function(T,R,S){var X,V,U,W;if(Q.isArray(T)){T=T.slice();for(X=0,V=T.length;X<V;X++){this.open_node(T[X],R,S)}return true}T=this.get_node(T);if(!T||T.id===Q.jstree.root){return false}S=S===P?this.settings.core.animation:S;if(!this.is_closed(T)){if(R){R.call(this,T,false)}return false}if(!this.is_loaded(T)){if(this.is_loading(T)){return setTimeout(Q.proxy(function(){this.open_node(T,R,S)},this),500)}this.load_node(T,function(Z,Y){return Y?this.open_node(Z,R,S):(R?R.call(this,Z,false):false)})}else{U=this.get_node(T,true);W=this;if(U.length){if(S&&U.children(".jstree-children").length){U.children(".jstree-children").stop(true,true)}if(T.children.length&&!this._firstChild(U.children(".jstree-children")[0])){this.draw_children(T)}if(!S){this.trigger("before_open",{"node":T});U[0].className=U[0].className.replace("jstree-closed","jstree-open");U[0].setAttribute("aria-expanded",true)}else{this.trigger("before_open",{"node":T});U.children(".jstree-children").css("display","none").end().removeClass("jstree-closed").addClass("jstree-open").attr("aria-expanded",true).children(".jstree-children").stop(true,true).slideDown(S,function(){this.style.display="";W.trigger("after_open",{"node":T})})}}T.state.opened=true;if(R){R.call(this,T,true)}if(!U.length){this.trigger("before_open",{"node":T})}this.trigger("open_node",{"node":T});if(!S||!U.length){this.trigger("after_open",{"node":T})}}},_open_to:function(S){S=this.get_node(S);if(!S||S.id===Q.jstree.root){return false}var R,U,T=S.parents;for(R=0,U=T.length;R<U;R+=1){if(R!==Q.jstree.root){this.open_node(T[R],false,0)}}return Q("#"+S.id.replace(Q.jstree.idregex,"\\$&"),this.element)},close_node:function(S,R){var W,U,V,T;if(Q.isArray(S)){S=S.slice();for(W=0,U=S.length;W<U;W++){this.close_node(S[W],R)}return true}S=this.get_node(S);if(!S||S.id===Q.jstree.root){return false}if(this.is_closed(S)){return false}R=R===P?this.settings.core.animation:R;V=this;T=this.get_node(S,true);if(T.length){if(!R){T[0].className=T[0].className.replace("jstree-open","jstree-closed");T.attr("aria-expanded",false).children(".jstree-children").remove()}else{T.children(".jstree-children").attr("style","display:block !important").end().removeClass("jstree-open").addClass("jstree-closed").attr("aria-expanded",false).children(".jstree-children").stop(true,true).slideUp(R,function(){this.style.display="";T.children(".jstree-children").remove();V.trigger("after_close",{"node":S})})}}S.state.opened=false;this.trigger("close_node",{"node":S});if(!R||!T.length){this.trigger("after_close",{"node":S})}},toggle_node:function(S){var T,R;if(Q.isArray(S)){S=S.slice();for(T=0,R=S.length;T<R;T++){this.toggle_node(S[T])}return true}if(this.is_closed(S)){return this.open_node(S)}if(this.is_open(S)){return this.close_node(S)}},open_all:function(T,S,U){if(!T){T=Q.jstree.root}T=this.get_node(T);if(!T){return false}var R=T.id===Q.jstree.root?this.get_container_ul():this.get_node(T,true),V,X,W;if(!R.length){for(V=0,X=T.children_d.length;V<X;V++){if(this.is_closed(this._model.data[T.children_d[V]])){this._model.data[T.children_d[V]].state.opened=true}}return this.trigger("open_all",{"node":T})}U=U||R;W=this;R=this.is_closed(T)?R.find(".jstree-closed").addBack():R.find(".jstree-closed");R.each(function(){W.open_node(this,function(Y,Z){if(Z&&this.is_parent(Y)){this.open_all(Y,S,U)}},S||0)});if(U.find(".jstree-closed").length===0){this.trigger("open_all",{"node":this.get_node(U)})}},close_all:function(T,S){if(!T){T=Q.jstree.root}T=this.get_node(T);if(!T){return false}var R=T.id===Q.jstree.root?this.get_container_ul():this.get_node(T,true),U=this,V,W;if(R.length){R=this.is_open(T)?R.find(".jstree-open").addBack():R.find(".jstree-open");Q(R.get().reverse()).each(function(){U.close_node(this,S||0)})}for(V=0,W=T.children_d.length;V<W;V++){this._model.data[T.children_d[V]].state.opened=false}this.trigger("close_all",{"node":T})},is_disabled:function(R){R=this.get_node(R);return R&&R.state&&R.state.disabled},enable_node:function(S){var T,R;if(Q.isArray(S)){S=S.slice();for(T=0,R=S.length;T<R;T++){this.enable_node(S[T])}return true}S=this.get_node(S);if(!S||S.id===Q.jstree.root){return false}S.state.disabled=false;this.get_node(S,true).children(".jstree-anchor").removeClass("jstree-disabled").attr("aria-disabled",false);this.trigger("enable_node",{"node":S})},disable_node:function(S){var T,R;if(Q.isArray(S)){S=S.slice();for(T=0,R=S.length;T<R;T++){this.disable_node(S[T])}return true}S=this.get_node(S);if(!S||S.id===Q.jstree.root){return false}S.state.disabled=true;this.get_node(S,true).children(".jstree-anchor").addClass("jstree-disabled").attr("aria-disabled",true);this.trigger("disable_node",{"node":S})},hide_node:function(S,U){var T,R;if(Q.isArray(S)){S=S.slice();for(T=0,R=S.length;T<R;T++){this.hide_node(S[T],true)}this.redraw();return true}S=this.get_node(S);if(!S||S.id===Q.jstree.root){return false}if(!S.state.hidden){S.state.hidden=true;this._node_changed(S.parent);if(!U){this.redraw()}this.trigger("hide_node",{"node":S})}},show_node:function(S,U){var T,R;if(Q.isArray(S)){S=S.slice();for(T=0,R=S.length;T<R;T++){this.show_node(S[T],true)}this.redraw();return true}S=this.get_node(S);if(!S||S.id===Q.jstree.root){return false}if(S.state.hidden){S.state.hidden=false;this._node_changed(S.parent);if(!U){this.redraw()}this.trigger("show_node",{"node":S})}},hide_all:function(S){var R,T=this._model.data,U=[];for(R in T){if(T.hasOwnProperty(R)&&R!==Q.jstree.root&&!T[R].state.hidden){T[R].state.hidden=true;U.push(R)}}this._model.force_full_redraw=true;this.redraw();this.trigger("hide_all",{"nodes":U});return U},show_all:function(S){var R,T=this._model.data,U=[];for(R in T){if(T.hasOwnProperty(R)&&R!==Q.jstree.root&&T[R].state.hidden){T[R].state.hidden=false;U.push(R)}}this._model.force_full_redraw=true;this.redraw();this.trigger("show_all",{"nodes":U});return U},activate_node:function(X,R){if(this.is_disabled(X)){return false}if(!R||typeof R!=="object"){R={}}this._data.core.last_clicked=this._data.core.last_clicked&&this._data.core.last_clicked.id!==P?this.get_node(this._data.core.last_clicked.id):null;if(this._data.core.last_clicked&&!this._data.core.last_clicked.state.selected){this._data.core.last_clicked=null}if(!this._data.core.last_clicked&&this._data.core.selected.length){this._data.core.last_clicked=this.get_node(this._data.core.selected[this._data.core.selected.length-1])}if(!this.settings.core.multiple||(!R.metaKey&&!R.ctrlKey&&!R.shiftKey)||(R.shiftKey&&(!this._data.core.last_clicked||!this.get_parent(X)||this.get_parent(X)!==this._data.core.last_clicked.parent))){if(!this.settings.core.multiple&&(R.metaKey||R.ctrlKey||R.shiftKey)&&this.is_selected(X)){this.deselect_node(X,false,R)}else{this.deselect_all(true);this.select_node(X,false,false,R);this._data.core.last_clicked=this.get_node(X)}}else{if(R.shiftKey){var V=this.get_node(X).id,W=this._data.core.last_clicked.id,S=this.get_node(this._data.core.last_clicked.parent).children,Y=false,U,T;for(U=0,T=S.length;U<T;U+=1){if(S[U]===V){Y=!Y}if(S[U]===W){Y=!Y}if(!this.is_disabled(S[U])&&(Y||S[U]===V||S[U]===W)){this.select_node(S[U],true,false,R)}else{this.deselect_node(S[U],true,R)}}this.trigger("changed",{"action":"select_node","node":this.get_node(X),"selected":this._data.core.selected,"event":R})}else{if(!this.is_selected(X)){this.select_node(X,false,false,R)}else{this.deselect_node(X,false,R)}}}this.trigger("activate_node",{"node":this.get_node(X),"event":R})},hover_node:function(R){R=this.get_node(R,true);if(!R||!R.length||R.children(".jstree-hovered").length){return false}var S=this.element.find(".jstree-hovered"),T=this.element;if(S&&S.length){this.dehover_node(S)}R.children(".jstree-anchor").addClass("jstree-hovered");this.trigger("hover_node",{"node":this.get_node(R)});setTimeout(function(){T.attr("aria-activedescendant",R[0].id)},0)},dehover_node:function(R){R=this.get_node(R,true);if(!R||!R.length||!R.children(".jstree-hovered").length){return false}R.children(".jstree-anchor").removeClass("jstree-hovered");this.trigger("dehover_node",{"node":this.get_node(R)})},select_node:function(X,R,U,S){var T,V,W,Y;if(Q.isArray(X)){X=X.slice();for(V=0,W=X.length;V<W;V++){this.select_node(X[V],R,U,S)}return true}X=this.get_node(X);if(!X||X.id===Q.jstree.root){return false}T=this.get_node(X,true);if(!X.state.selected){X.state.selected=true;this._data.core.selected.push(X.id);if(!U){T=this._open_to(X)}if(T&&T.length){T.attr("aria-selected",true).children(".jstree-anchor").addClass("jstree-clicked")}this.trigger("select_node",{"node":X,"selected":this._data.core.selected,"event":S});if(!R){this.trigger("changed",{"action":"select_node","node":X,"selected":this._data.core.selected,"event":S})}}},deselect_node:function(T,V,U){var W,S,R;if(Q.isArray(T)){T=T.slice();for(W=0,S=T.length;W<S;W++){this.deselect_node(T[W],V,U)}return true}T=this.get_node(T);if(!T||T.id===Q.jstree.root){return false}R=this.get_node(T,true);if(T.state.selected){T.state.selected=false;this._data.core.selected=Q.vakata.array_remove_item(this._data.core.selected,T.id);if(R.length){R.attr("aria-selected",false).children(".jstree-anchor").removeClass("jstree-clicked")}this.trigger("deselect_node",{"node":T,"selected":this._data.core.selected,"event":U});if(!V){this.trigger("changed",{"action":"deselect_node","node":T,"selected":this._data.core.selected,"event":U})}}},select_all:function(T){var R=this._data.core.selected.concat([]),S,U;this._data.core.selected=this._model.data[Q.jstree.root].children_d.concat();for(S=0,U=this._data.core.selected.length;S<U;S++){if(this._model.data[this._data.core.selected[S]]){this._model.data[this._data.core.selected[S]].state.selected=true}}this.redraw(true);this.trigger("select_all",{"selected":this._data.core.selected});if(!T){this.trigger("changed",{"action":"select_all","selected":this._data.core.selected,"old_selection":R})}},deselect_all:function(T){var R=this._data.core.selected.concat([]),S,U;for(S=0,U=this._data.core.selected.length;S<U;S++){if(this._model.data[this._data.core.selected[S]]){this._model.data[this._data.core.selected[S]].state.selected=false}}this._data.core.selected=[];this.element.find(".jstree-clicked").removeClass("jstree-clicked").parent().attr("aria-selected",false);this.trigger("deselect_all",{"selected":this._data.core.selected,"node":R});if(!T){this.trigger("changed",{"action":"deselect_all","selected":this._data.core.selected,"old_selection":R})}},is_selected:function(R){R=this.get_node(R);if(!R||R.id===Q.jstree.root){return false}return R.state.selected},get_selected:function(R){return R?Q.map(this._data.core.selected,Q.proxy(function(S){return this.get_node(S)},this)):this._data.core.selected.slice()},get_top_selected:function(W){var R=this.get_selected(true),T={},S,X,U,V;for(S=0,X=R.length;S<X;S++){T[R[S].id]=R[S]}for(S=0,X=R.length;S<X;S++){for(U=0,V=R[S].children_d.length;U<V;U++){if(T[R[S].children_d[U]]){delete T[R[S].children_d[U]]}}}R=[];for(S in T){if(T.hasOwnProperty(S)){R.push(S)}}return W?Q.map(R,Q.proxy(function(Y){return this.get_node(Y)},this)):R},get_bottom_selected:function(U){var R=this.get_selected(true),T=[],S,V;for(S=0,V=R.length;S<V;S++){if(!R[S].children.length){T.push(R[S].id)}}return U?Q.map(T,Q.proxy(function(W){return this.get_node(W)},this)):T},get_state:function(){var S={"core":{"open":[],"scroll":{"left":this.element.scrollLeft(),"top":this.element.scrollTop()},
/*
					'themes' : {
						'name' : this.get_theme(),
						'icons' : this._data.core.themes.icons,
						'dots' : this._data.core.themes.dots
					},
					*/
"selected":[]}},R;for(R in this._model.data){if(this._model.data.hasOwnProperty(R)){if(R!==Q.jstree.root){if(this._model.data[R].state.opened){S.core.open.push(R)}if(this._model.data[R].state.selected){S.core.selected.push(R)}}}}return S},set_state:function(X,R){if(X){if(X.core){var V,T,W,S,U;if(X.core.open){if(!Q.isArray(X.core.open)||!X.core.open.length){delete X.core.open;this.set_state(X,R)}else{this._load_nodes(X.core.open,function(Y){this.open_node(Y,false,0);delete X.core.open;this.set_state(X,R)},true)}return false}if(X.core.scroll){if(X.core.scroll&&X.core.scroll.left!==P){this.element.scrollLeft(X.core.scroll.left)}if(X.core.scroll&&X.core.scroll.top!==P){this.element.scrollTop(X.core.scroll.top)}delete X.core.scroll;this.set_state(X,R);return false}if(X.core.selected){S=this;this.deselect_all();Q.each(X.core.selected,function(Y,Z){S.select_node(Z,false,true)});delete X.core.selected;this.set_state(X,R);return false}for(U in X){if(X.hasOwnProperty(U)&&U!=="core"&&Q.inArray(U,this.settings.plugins)===-1){delete X[U]}}if(Q.isEmptyObject(X.core)){delete X.core;this.set_state(X,R);return false}}if(Q.isEmptyObject(X)){X=null;if(R){R.call(this)}this.trigger("set_state");return false}return true}return false},refresh:function(S,R){this._data.core.state=R===true?{}:this.get_state();if(R&&Q.isFunction(R)){this._data.core.state=R.call(this,this._data.core.state)}this._cnt=0;this._model.data={};this._model.data[Q.jstree.root]={id:Q.jstree.root,parent:null,parents:[],children:[],children_d:[],state:{loaded:false}};this._data.core.selected=[];this._data.core.last_clicked=null;this._data.core.focused=null;var T=this.get_container_ul()[0].className;if(!S){this.element.html("<ul class='"+T+"' role='group'><li class='jstree-initial-node jstree-loading jstree-leaf jstree-last' role='treeitem' id='j"+this._id+"_loading'><i class='jstree-icon jstree-ocl'></i><a class='jstree-anchor' href='#'><i class='jstree-icon jstree-themeicon-hidden'></i>"+this.get_string("Loading ...")+"</a></li></ul>");this.element.attr("aria-activedescendant","j"+this._id+"_loading")}this.load_node(Q.jstree.root,function(V,U){if(U){this.get_container_ul()[0].className=T;if(this._firstChild(this.get_container_ul()[0])){this.element.attr("aria-activedescendant",this._firstChild(this.get_container_ul()[0]).id)}this.set_state(Q.extend(true,{},this._data.core.state),function(){this.trigger("refresh")})}this._data.core.state=null})},refresh_node:function(S){S=this.get_node(S);if(!S||S.id===Q.jstree.root){return false}var U=[],R=[],T=this._data.core.selected.concat([]);R.push(S.id);if(S.state.opened===true){U.push(S.id)}this.get_node(S,true).find(".jstree-open").each(function(){U.push(this.id)});this._load_nodes(R,Q.proxy(function(V){this.open_node(U,false,0);this.select_node(this._data.core.selected);this.trigger("refresh_node",{"node":S,"nodes":V})},this))},set_id:function(S,V){S=this.get_node(S);if(!S||S.id===Q.jstree.root){return false}var R,U,T=this._model.data;V=V.toString();T[S.parent].children[Q.inArray(S.id,T[S.parent].children)]=V;for(R=0,U=S.parents.length;R<U;R++){T[S.parents[R]].children_d[Q.inArray(S.id,T[S.parents[R]].children_d)]=V}for(R=0,U=S.children.length;R<U;R++){T[S.children[R]].parent=V}for(R=0,U=S.children_d.length;R<U;R++){T[S.children_d[R]].parents[Q.inArray(S.id,T[S.children_d[R]].parents)]=V}R=Q.inArray(S.id,this._data.core.selected);if(R!==-1){this._data.core.selected[R]=V}R=this.get_node(S.id,true);if(R){R.attr("id",V).children(".jstree-anchor").attr("id",V+"_anchor").end().attr("aria-labelledby",V+"_anchor");if(this.element.attr("aria-activedescendant")===S.id){this.element.attr("aria-activedescendant",V)}}delete T[S.id];S.id=V;S.li_attr.id=V;T[V]=S;return true},get_text:function(R){R=this.get_node(R);return(!R||R.id===Q.jstree.root)?false:R.text},set_text:function(S,T){var U,R;if(Q.isArray(S)){S=S.slice();for(U=0,R=S.length;U<R;U++){this.set_text(S[U],T)}return true}S=this.get_node(S);if(!S||S.id===Q.jstree.root){return false}S.text=T;if(this.get_node(S,true).length){this.redraw_node(S.id)}this.trigger("set_text",{"obj":S,"text":T});return true},get_json:function(T,V,U){T=this.get_node(T||Q.jstree.root);if(!T){return false}if(V&&V.flat&&!U){U=[]}var R={"id":T.id,"text":T.text,"icon":this.get_icon(T),"li_attr":Q.extend(true,{},T.li_attr),"a_attr":Q.extend(true,{},T.a_attr),"state":{},"data":V&&V.no_data?false:Q.extend(true,{},T.data)},S,W;if(V&&V.flat){R.parent=T.parent}else{R.children=[]}if(!V||!V.no_state){for(S in T.state){if(T.state.hasOwnProperty(S)){R.state[S]=T.state[S]}}}if(V&&V.no_id){delete R.id;if(R.li_attr&&R.li_attr.id){delete R.li_attr.id}if(R.a_attr&&R.a_attr.id){delete R.a_attr.id}}if(V&&V.flat&&T.id!==Q.jstree.root){U.push(R)}if(!V||!V.no_children){for(S=0,W=T.children.length;S<W;S++){if(V&&V.flat){this.get_json(T.children[S],V,U)}else{R.children.push(this.get_json(T.children[S],V))}}}return V&&V.flat?U:(T.id===Q.jstree.root?R.children:R)},create_node:function(T,X,Y,R,Z){if(T===null){T=Q.jstree.root}T=this.get_node(T);if(!T){return false}Y=Y===P?"last":Y;if(!Y.toString().match(/^(before|after)$/)&&!Z&&!this.is_loaded(T)){return this.load_node(T,function(){this.create_node(T,X,Y,R,true)})}if(!X){X={"text":this.get_string("New node")}}if(typeof X==="string"){X={"text":X}}if(X.text===P){X.text=this.get_string("New node")}var V,S,W,U;if(T.id===Q.jstree.root){if(Y==="before"){Y="first"}if(Y==="after"){Y="last"}}switch(Y){case"before":V=this.get_node(T.parent);Y=Q.inArray(T.id,V.children);T=V;break;case"after":V=this.get_node(T.parent);Y=Q.inArray(T.id,V.children)+1;T=V;break;case"inside":case"first":Y=0;break;case"last":Y=T.children.length;break;default:if(!Y){Y=0}break}if(Y>T.children.length){Y=T.children.length}if(!X.id){X.id=true}if(!this.check("create_node",X,T,Y)){this.settings.core.error.call(this,this._data.core.last_error);return false}if(X.id===true){delete X.id}X=this._parse_model_from_json(X,T.id,T.parents.concat());if(!X){return false}V=this.get_node(X);S=[];S.push(X);S=S.concat(V.children_d);this.trigger("model",{"nodes":S,"parent":T.id});T.children_d=T.children_d.concat(S);for(W=0,U=T.parents.length;W<U;W++){this._model.data[T.parents[W]].children_d=this._model.data[T.parents[W]].children_d.concat(S)}X=V;V=[];for(W=0,U=T.children.length;W<U;W++){V[W>=Y?W+1:W]=T.children[W]}V[Y]=X.id;T.children=V;this.redraw_node(T,true);if(R){R.call(this,this.get_node(X))}this.trigger("create_node",{"node":this.get_node(X),"parent":T.id,"position":Y});return X.id},rename_node:function(S,U){alert("aaaa");var V,R,T;if(Q.isArray(S)){S=S.slice();for(V=0,R=S.length;V<R;V++){this.rename_node(S[V],U)}return true}S=this.get_node(S);if(!S||S.id===Q.jstree.root){return false}T=S.text;alert("bbb");if(!this.check("rename_node",S,this.get_parent(S),U)){alert("mmm");this.settings.core.error.call(this,this._data.core.last_error);return false}alert("ccc");this.set_text(S,U);this.trigger("rename_node",{"node":S,"text":U,"old":T});return true},delete_node:function(d){var Z,b,S,X,U,V,T,W,a,e,R,Y;if(Q.isArray(d)){d=d.slice();for(Z=0,b=d.length;Z<b;Z++){this.delete_node(d[Z])}return true}d=this.get_node(d);if(!d||d.id===Q.jstree.root){return false}S=this.get_node(d.parent);X=Q.inArray(d.id,S.children);e=false;if(!this.check("delete_node",d,S,X)){this.settings.core.error.call(this,this._data.core.last_error);return false}if(X!==-1){S.children=Q.vakata.array_remove(S.children,X)}U=d.children_d.concat([]);U.push(d.id);for(W=0,a=U.length;W<a;W++){for(V=0,T=d.parents.length;V<T;V++){X=Q.inArray(U[W],this._model.data[d.parents[V]].children_d);if(X!==-1){this._model.data[d.parents[V]].children_d=Q.vakata.array_remove(this._model.data[d.parents[V]].children_d,X)}}if(this._model.data[U[W]].state.selected){e=true;X=Q.inArray(U[W],this._data.core.selected);if(X!==-1){this._data.core.selected=Q.vakata.array_remove(this._data.core.selected,X)}}}this.trigger("delete_node",{"node":d,"parent":S.id});if(e){this.trigger("changed",{"action":"delete_node","node":d,"selected":this._data.core.selected,"parent":S.id})}for(W=0,a=U.length;W<a;W++){delete this._model.data[U[W]]}if(Q.inArray(this._data.core.focused,U)!==-1){this._data.core.focused=null;R=this.element[0].scrollTop;Y=this.element[0].scrollLeft;if(S.id===Q.jstree.root){this.get_node(this._model.data[Q.jstree.root].children[0],true).children(".jstree-anchor").focus()}else{this.get_node(S,true).children(".jstree-anchor").focus()}this.element[0].scrollTop=R;this.element[0].scrollLeft=Y}this.redraw_node(S,true);return true},check:function(S,U,W,X,V){U=U&&U.id?U:this.get_node(U);W=W&&W.id?W:this.get_node(W);var R=S.match(/^move_node|copy_node|create_node$/i)?W:U,T=this.settings.core.check_callback;if(S==="move_node"||S==="copy_node"){if((!V||!V.is_multi)&&(U.id===W.id||Q.inArray(U.id,W.children)===X||Q.inArray(W.id,U.children_d)!==-1)){this._data.core.last_error={"error":"check","plugin":"core","id":"core_01","reason":"Moving parent inside child","data":JSON.stringify({"chk":S,"pos":X,"obj":U&&U.id?U.id:false,"par":W&&W.id?W.id:false})};return false}}if(R&&R.data){R=R.data}if(R&&R.functions&&(R.functions[S]===false||R.functions[S]===true)){if(R.functions[S]===false){this._data.core.last_error={"error":"check","plugin":"core","id":"core_02","reason":"Node data prevents function: "+S,"data":JSON.stringify({"chk":S,"pos":X,"obj":U&&U.id?U.id:false,"par":W&&W.id?W.id:false})}}return R.functions[S]}if(T===false||(Q.isFunction(T)&&T.call(this,S,U,W,X,V)===false)||(T&&T[S]===false)){this._data.core.last_error={"error":"check","plugin":"core","id":"core_03","reason":"User config for core.check_callback prevents function: "+S,"data":JSON.stringify({"chk":S,"pos":X,"obj":U&&U.id?U.id:false,"par":W&&W.id?W.id:false})};return false}return true},last_error:function(){return this._data.core.last_error},move_node:function(Z,e,o,T,b,S,h){var c,X,m,f,d,q,V,U,R,Y,W,n,a,g;e=this.get_node(e);o=o===P?0:o;if(!e){return false}if(!o.toString().match(/^(before|after)$/)&&!b&&!this.is_loaded(e)){return this.load_node(e,function(){this.move_node(Z,e,o,T,true,false,h)})}if(Q.isArray(Z)){if(Z.length===1){Z=Z[0]}else{for(c=0,X=Z.length;c<X;c++){if((R=this.move_node(Z[c],e,o,T,b,false,h))){e=R;o="after"}}this.redraw();return true}}Z=Z&&Z.id?Z:this.get_node(Z);if(!Z||Z.id===Q.jstree.root){return false}m=(Z.parent||Q.jstree.root).toString();d=(!o.toString().match(/^(before|after)$/)||e.id===Q.jstree.root)?e:this.get_node(e.parent);q=h?h:(this._model.data[Z.id]?this:Q.jstree.reference(Z.id));V=!q||!q._id||(this._id!==q._id);f=q&&q._id&&m&&q._model.data[m]&&q._model.data[m].children?Q.inArray(Z.id,q._model.data[m].children):-1;if(q&&q._id){Z=q._model.data[Z.id]}if(V){if((R=this.copy_node(Z,e,o,T,b,false,h))){if(q){q.delete_node(Z)}return R}return false}if(e.id===Q.jstree.root){if(o==="before"){o="first"}if(o==="after"){o="last"}}switch(o){case"before":o=Q.inArray(e.id,d.children);break;case"after":o=Q.inArray(e.id,d.children)+1;break;case"inside":case"first":o=0;break;case"last":o=d.children.length;break;default:if(!o){o=0}break}if(o>d.children.length){o=d.children.length}if(!this.check("move_node",Z,d,o,{"core":true,"origin":h,"is_multi":(q&&q._id&&q._id!==this._id),"is_foreign":(!q||!q._id)})){this.settings.core.error.call(this,this._data.core.last_error);return false}if(Z.parent===d.id){U=d.children.concat();R=Q.inArray(Z.id,U);if(R!==-1){U=Q.vakata.array_remove(U,R);if(o>R){o--}}R=[];for(Y=0,W=U.length;Y<W;Y++){R[Y>=o?Y+1:Y]=U[Y]}R[o]=Z.id;d.children=R;this._node_changed(d.id);this.redraw(d.id===Q.jstree.root)}else{R=Z.children_d.concat();R.push(Z.id);for(Y=0,W=Z.parents.length;Y<W;Y++){U=[];g=q._model.data[Z.parents[Y]].children_d;for(n=0,a=g.length;n<a;n++){if(Q.inArray(g[n],R)===-1){U.push(g[n])}}q._model.data[Z.parents[Y]].children_d=U}q._model.data[m].children=Q.vakata.array_remove_item(q._model.data[m].children,Z.id);for(Y=0,W=d.parents.length;Y<W;Y++){this._model.data[d.parents[Y]].children_d=this._model.data[d.parents[Y]].children_d.concat(R)}U=[];for(Y=0,W=d.children.length;Y<W;Y++){U[Y>=o?Y+1:Y]=d.children[Y]}U[o]=Z.id;d.children=U;d.children_d.push(Z.id);d.children_d=d.children_d.concat(Z.children_d);Z.parent=d.id;R=d.parents.concat();R.unshift(d.id);g=Z.parents.length;Z.parents=R;R=R.concat();for(Y=0,W=Z.children_d.length;Y<W;Y++){this._model.data[Z.children_d[Y]].parents=this._model.data[Z.children_d[Y]].parents.slice(0,g*-1);Array.prototype.push.apply(this._model.data[Z.children_d[Y]].parents,R)}if(m===Q.jstree.root||d.id===Q.jstree.root){this._model.force_full_redraw=true}if(!this._model.force_full_redraw){this._node_changed(m);this._node_changed(d.id)}if(!S){this.redraw()}}if(T){T.call(this,Z,d,o)}this.trigger("move_node",{"node":Z,"parent":d.id,"position":o,"old_parent":m,"old_position":f,"is_multi":(q&&q._id&&q._id!==this._id),"is_foreign":(!q||!q._id),"old_instance":q,"new_instance":this});return Z.id},copy_node:function(Z,d,g,T,a,S,V){var b,X,U,R,Y,W,e,f,c,h,k;d=this.get_node(d);g=g===P?0:g;if(!d){return false}if(!g.toString().match(/^(before|after)$/)&&!a&&!this.is_loaded(d)){return this.load_node(d,function(){this.copy_node(Z,d,g,T,true,false,V)})}if(Q.isArray(Z)){if(Z.length===1){Z=Z[0]}else{for(b=0,X=Z.length;b<X;b++){if((R=this.copy_node(Z[b],d,g,T,a,true,V))){d=R;g="after"}}this.redraw();return true}}Z=Z&&Z.id?Z:this.get_node(Z);if(!Z||Z.id===Q.jstree.root){return false}f=(Z.parent||Q.jstree.root).toString();c=(!g.toString().match(/^(before|after)$/)||d.id===Q.jstree.root)?d:this.get_node(d.parent);h=V?V:(this._model.data[Z.id]?this:Q.jstree.reference(Z.id));k=!h||!h._id||(this._id!==h._id);if(h&&h._id){Z=h._model.data[Z.id]}if(d.id===Q.jstree.root){if(g==="before"){g="first"}if(g==="after"){g="last"}}switch(g){case"before":g=Q.inArray(d.id,c.children);break;case"after":g=Q.inArray(d.id,c.children)+1;break;case"inside":case"first":g=0;break;case"last":g=c.children.length;break;default:if(!g){g=0}break}if(g>c.children.length){g=c.children.length}if(!this.check("copy_node",Z,c,g,{"core":true,"origin":V,"is_multi":(h&&h._id&&h._id!==this._id),"is_foreign":(!h||!h._id)})){this.settings.core.error.call(this,this._data.core.last_error);return false}e=h?h.get_json(Z,{no_id:true,no_data:true,no_state:true}):Z;if(!e){return false}if(e.id===true){delete e.id}e=this._parse_model_from_json(e,c.id,c.parents.concat());if(!e){return false}R=this.get_node(e);if(Z&&Z.state&&Z.state.loaded===false){R.state.loaded=false}U=[];U.push(e);U=U.concat(R.children_d);this.trigger("model",{"nodes":U,"parent":c.id});for(Y=0,W=c.parents.length;Y<W;Y++){this._model.data[c.parents[Y]].children_d=this._model.data[c.parents[Y]].children_d.concat(U)}U=[];for(Y=0,W=c.children.length;Y<W;Y++){U[Y>=g?Y+1:Y]=c.children[Y]}U[g]=R.id;c.children=U;c.children_d.push(R.id);c.children_d=c.children_d.concat(R.children_d);if(c.id===Q.jstree.root){this._model.force_full_redraw=true}if(!this._model.force_full_redraw){this._node_changed(c.id)}if(!S){this.redraw(c.id===Q.jstree.root)}if(T){T.call(this,R,c,g)}this.trigger("copy_node",{"node":R,"original":Z,"parent":c.id,"position":g,"old_parent":f,"old_position":h&&h._id&&f&&h._model.data[f]&&h._model.data[f].children?Q.inArray(Z.id,h._model.data[f].children):-1,"is_multi":(h&&h._id&&h._id!==this._id),"is_foreign":(!h||!h._id),"old_instance":h,"new_instance":this});return R.id},cut:function(T){if(!T){T=this._data.core.selected.concat()}if(!Q.isArray(T)){T=[T]}if(!T.length){return false}var R=[],U,V,S;for(V=0,S=T.length;V<S;V++){U=this.get_node(T[V]);if(U&&U.id&&U.id!==Q.jstree.root){R.push(U)}}if(!R.length){return false}N=R;B=this;C="move_node";this.trigger("cut",{"node":T})},copy:function(T){if(!T){T=this._data.core.selected.concat()}if(!Q.isArray(T)){T=[T]}if(!T.length){return false}var R=[],U,V,S;for(V=0,S=T.length;V<S;V++){U=this.get_node(T[V]);if(U&&U.id&&U.id!==Q.jstree.root){R.push(U)}}if(!R.length){return false}N=R;B=this;C="copy_node";this.trigger("copy",{"node":T})},get_buffer:function(){return{"mode":C,"node":N,"inst":B}},can_paste:function(){return C!==false&&N!==false},paste:function(R,S){R=this.get_node(R);if(!R||!C||!C.match(/^(copy_node|move_node)$/)||!N){return false}if(this[C](N,R,S,false,false,false,B)){this.trigger("paste",{"parent":R.id,"node":N,"mode":C})}N=false;C=false;B=false},clear_buffer:function(){N=false;C=false;B=false;this.trigger("clear_buffer")},edit:function(b,Y,T){var c,U,d,S,V,e,R,Z,X,W=false;b=this.get_node(b);if(!b){return false}if(this.settings.core.check_callback===false){this._data.core.last_error={"error":"check","plugin":"core","id":"core_07","reason":"Could not edit node because of check_callback"};this.settings.core.error.call(this,this._data.core.last_error);return false}X=b;Y=typeof Y==="string"?Y:b.text;this.set_text(b,"");b=this._open_to(b);X.text=Y;c=this._data.core.rtl;U=this.element.width();this._data.core.focused=X.id;d=b.children(".jstree-anchor").focus();S=Q("<span>");
/*
			oi = obj.children("i:visible"),
			ai = a.children("i:visible"),
			w1 = oi.width() * oi.length,
			w2 = ai.width() * ai.length,
			*/
V=Y;e=Q("<div />",{css:{"position":"absolute","top":"-200px","left":(c?"0px":"-1000px"),"visibility":"hidden"}}).appendTo("body");R=Q("<input />",{"value":V,"class":"jstree-rename-input","css":{"padding":"0","border":"1px solid silver","box-sizing":"border-box","display":"inline-block","height":(this._data.core.li_height)+"px","lineHeight":(this._data.core.li_height)+"px","width":"150px"},"blur":Q.proxy(function(g){g.stopImmediatePropagation();g.preventDefault();var a=S.children(".jstree-rename-input"),h=a.val(),k=this.settings.core.force_text,j;if(h===""){h=V}e.remove();S.replaceWith(d);S.remove();V=k?V:Q("<div></div>").append(Q.parseHTML(V)).html();this.set_text(b,V);j=!!this.rename_node(b,k?Q("<div></div>").text(h).text():Q("<div></div>").append(Q.parseHTML(h)).html());if(!j){this.set_text(b,V)}this._data.core.focused=X.id;setTimeout(Q.proxy(function(){var f=this.get_node(X.id,true);if(f.length){this._data.core.focused=X.id;f.children(".jstree-anchor").focus()}},this),0);if(T){T.call(this,X,j,W)}},this),"keydown":function(f){var a=f.which;if(a===27){W=true;this.value=V}if(a===27||a===13||a===37||a===38||a===39||a===40||a===32){f.stopImmediatePropagation()}if(a===27||a===13){f.preventDefault();this.blur()}},"click":function(a){a.stopImmediatePropagation()},"mousedown":function(a){a.stopImmediatePropagation()},"keyup":function(a){R.width(Math.min(e.text("pW"+this.value).width(),U))},"keypress":function(a){if(a.which===13){return false}}});Z={fontFamily:d.css("fontFamily")||"",fontSize:d.css("fontSize")||"",fontWeight:d.css("fontWeight")||"",fontStyle:d.css("fontStyle")||"",fontStretch:d.css("fontStretch")||"",fontVariant:d.css("fontVariant")||"",letterSpacing:d.css("letterSpacing")||"",wordSpacing:d.css("wordSpacing")||""};S.attr("class",d.attr("class")).append(d.contents().clone()).append(R);d.replaceWith(S);e.css(Z);R.css(Z).width(Math.min(e.text("pW"+R[0].value).width(),U))[0].select()},set_theme:function(T,S){if(!T){return false}if(S===true){var R=this.settings.core.themes.dir;if(!R){R=Q.jstree.path+"/themes"}S=R+"/"+T+"/style.css"}if(S&&Q.inArray(S,G)===-1){Q("head").append('<link rel="stylesheet" href="'+S+'" type="text/css" />');G.push(S)}if(this._data.core.themes.name){this.element.removeClass("jstree-"+this._data.core.themes.name)}this._data.core.themes.name=T;this.element.addClass("jstree-"+T);this.element[this.settings.core.themes.responsive?"addClass":"removeClass"]("jstree-"+T+"-responsive");this.trigger("set_theme",{"theme":T})},get_theme:function(){return this._data.core.themes.name},set_theme_variant:function(R){if(this._data.core.themes.variant){this.element.removeClass("jstree-"+this._data.core.themes.name+"-"+this._data.core.themes.variant)}this._data.core.themes.variant=R;if(R){this.element.addClass("jstree-"+this._data.core.themes.name+"-"+this._data.core.themes.variant)}},get_theme_variant:function(){return this._data.core.themes.variant},show_stripes:function(){this._data.core.themes.stripes=true;this.get_container_ul().addClass("jstree-striped")},hide_stripes:function(){this._data.core.themes.stripes=false;this.get_container_ul().removeClass("jstree-striped")},toggle_stripes:function(){if(this._data.core.themes.stripes){this.hide_stripes()}else{this.show_stripes()}},show_dots:function(){this._data.core.themes.dots=true;this.get_container_ul().removeClass("jstree-no-dots")},hide_dots:function(){this._data.core.themes.dots=false;this.get_container_ul().addClass("jstree-no-dots")},toggle_dots:function(){if(this._data.core.themes.dots){this.hide_dots()}else{this.show_dots()}},show_icons:function(){this._data.core.themes.icons=true;this.get_container_ul().removeClass("jstree-no-icons")},hide_icons:function(){this._data.core.themes.icons=false;this.get_container_ul().addClass("jstree-no-icons")},toggle_icons:function(){if(this._data.core.themes.icons){this.hide_icons()}else{this.show_icons()}},set_icon:function(T,V){var W,S,R,U;if(Q.isArray(T)){T=T.slice();for(W=0,S=T.length;W<S;W++){this.set_icon(T[W],V)}return true}T=this.get_node(T);if(!T||T.id===Q.jstree.root){return false}U=T.icon;T.icon=V===true||V===null||V===P||V===""?true:V;R=this.get_node(T,true).children(".jstree-anchor").children(".jstree-themeicon");if(V===false){this.hide_icon(T)}else{if(V===true||V===null||V===P||V===""){R.removeClass("jstree-themeicon-custom "+U).css("background","").removeAttr("rel");if(U===false){this.show_icon(T)}}else{if(V.indexOf("/")===-1&&V.indexOf(".")===-1){R.removeClass(U).css("background","");R.addClass(V+" jstree-themeicon-custom").attr("rel",V);if(U===false){this.show_icon(T)}}else{R.removeClass(U).css("background","");R.addClass("jstree-themeicon-custom").css("background","url('"+V+"') center center no-repeat").attr("rel",V);if(U===false){this.show_icon(T)}}}}return true},get_icon:function(R){R=this.get_node(R);return(!R||R.id===Q.jstree.root)?false:R.icon},hide_icon:function(S){var T,R;if(Q.isArray(S)){S=S.slice();for(T=0,R=S.length;T<R;T++){this.hide_icon(S[T])}return true}S=this.get_node(S);if(!S||S===Q.jstree.root){return false}S.icon=false;this.get_node(S,true).children(".jstree-anchor").children(".jstree-themeicon").addClass("jstree-themeicon-hidden");return true},show_icon:function(T){var U,S,R;if(Q.isArray(T)){T=T.slice();for(U=0,S=T.length;U<S;U++){this.show_icon(T[U])}return true}T=this.get_node(T);if(!T||T===Q.jstree.root){return false}R=this.get_node(T,true);T.icon=R.length?R.children(".jstree-anchor").children(".jstree-themeicon").attr("rel"):true;if(!T.icon){T.icon=true}R.children(".jstree-anchor").children(".jstree-themeicon").removeClass("jstree-themeicon-hidden");return true}};Q.vakata={};Q.vakata.attributes=function(R,T){R=Q(R)[0];var S=T?{}:[];if(R&&R.attributes){Q.each(R.attributes,function(U,V){if(Q.inArray(V.name.toLowerCase(),["style","contenteditable","hasfocus","tabindex"])!==-1){return}if(V.value!==null&&Q.trim(V.value)!==""){if(T){S[V.name]=V.value}else{S.push(V.name)}}})}return S};Q.vakata.array_unique=function(R){var V=[],S,W,U,T={};for(S=0,U=R.length;S<U;S++){if(T[R[S]]===P){V.push(R[S]);T[R[S]]=true}}return V};Q.vakata.array_remove=function(R,S,T){var U=R.slice((T||S)+1||R.length);R.length=S<0?R.length+S:S;R.push.apply(R,U);return R};Q.vakata.array_remove_item=function(S,T){var R=Q.inArray(T,S);return R!==-1?Q.vakata.array_remove(S,R):S};Q.jstree.plugins.changed=function(R,T){var S=[];this.trigger=function(X,Y){var V,U;if(!Y){Y={}}if(X.replace(".jstree","")==="changed"){Y.changed={selected:[],deselected:[]};var W={};for(V=0,U=S.length;V<U;V++){W[S[V]]=1}for(V=0,U=Y.selected.length;V<U;V++){if(!W[Y.selected[V]]){Y.changed.selected.push(Y.selected[V])}else{W[Y.selected[V]]=2}}for(V=0,U=S.length;V<U;V++){if(W[S[V]]===1){Y.changed.deselected.push(S[V])}}S=Y.selected.slice()}T.trigger.call(this,X,Y)};this.refresh=function(V,U){S=[];return T.refresh.apply(this,arguments)}};var F=H.createElement("I");F.className="jstree-icon jstree-checkbox";F.setAttribute("role","presentation");Q.jstree.defaults.checkbox={visible:true,three_state:true,whole_node:true,keep_selected_style:true,cascade:"",tie_selection:true};Q.jstree.plugins.checkbox=function(R,S){this.bind=function(){S.bind.call(this);this._data.checkbox.uto=false;this._data.checkbox.selected=[];if(this.settings.checkbox.three_state){this.settings.checkbox.cascade="up+down+undetermined"}this.element.on("init.jstree",Q.proxy(function(){this._data.checkbox.visible=this.settings.checkbox.visible;if(!this.settings.checkbox.keep_selected_style){this.element.addClass("jstree-checkbox-no-clicked")}if(this.settings.checkbox.tie_selection){this.element.addClass("jstree-checkbox-selection")}},this)).on("loading.jstree",Q.proxy(function(){this[this._data.checkbox.visible?"show_checkboxes":"hide_checkboxes"]()},this));if(this.settings.checkbox.cascade.indexOf("undetermined")!==-1){this.element.on("changed.jstree uncheck_node.jstree check_node.jstree uncheck_all.jstree check_all.jstree move_node.jstree copy_node.jstree redraw.jstree open_node.jstree",Q.proxy(function(){if(this._data.checkbox.uto){clearTimeout(this._data.checkbox.uto)}this._data.checkbox.uto=setTimeout(Q.proxy(this._undetermined,this),50)},this))}if(!this.settings.checkbox.tie_selection){this.element.on("model.jstree",Q.proxy(function(U,Z){var X=this._model.data,V=X[Z.parent],W=Z.nodes,T,Y;for(T=0,Y=W.length;T<Y;T++){X[W[T]].state.checked=X[W[T]].state.checked||(X[W[T]].original&&X[W[T]].original.state&&X[W[T]].original.state.checked);if(X[W[T]].state.checked){this._data.checkbox.selected.push(W[T])}}},this))}if(this.settings.checkbox.cascade.indexOf("up")!==-1||this.settings.checkbox.cascade.indexOf("down")!==-1){this.element.on("model.jstree",Q.proxy(function(T,W){var h=this._model.data,V=h[W.parent],X=W.nodes,b=[],n,d,Z,a,g,Y,U=this.settings.checkbox.cascade,f=this.settings.checkbox.tie_selection;if(U.indexOf("down")!==-1){if(V.state[f?"selected":"checked"]){for(d=0,Z=X.length;d<Z;d++){h[X[d]].state[f?"selected":"checked"]=true}this._data[f?"core":"checkbox"].selected=this._data[f?"core":"checkbox"].selected.concat(X)}else{for(d=0,Z=X.length;d<Z;d++){if(h[X[d]].state[f?"selected":"checked"]){for(a=0,g=h[X[d]].children_d.length;a<g;a++){h[h[X[d]].children_d[a]].state[f?"selected":"checked"]=true}this._data[f?"core":"checkbox"].selected=this._data[f?"core":"checkbox"].selected.concat(h[X[d]].children_d)}}}}if(U.indexOf("up")!==-1){for(d=0,Z=V.children_d.length;d<Z;d++){if(!h[V.children_d[d]].children.length){b.push(h[V.children_d[d]].parent)}}b=Q.vakata.array_unique(b);for(a=0,g=b.length;a<g;a++){V=h[b[a]];while(V&&V.id!==Q.jstree.root){n=0;for(d=0,Z=V.children.length;d<Z;d++){n+=h[V.children[d]].state[f?"selected":"checked"]}if(n===Z){V.state[f?"selected":"checked"]=true;this._data[f?"core":"checkbox"].selected.push(V.id);Y=this.get_node(V,true);if(Y&&Y.length){Y.attr("aria-selected",true).children(".jstree-anchor").addClass(f?"jstree-clicked":"jstree-checked")}}else{break}V=this.get_node(V.parent)}}}this._data[f?"core":"checkbox"].selected=Q.vakata.array_unique(this._data[f?"core":"checkbox"].selected)},this)).on(this.settings.checkbox.tie_selection?"select_node.jstree":"check_node.jstree",Q.proxy(function(T,W){var f=W.node,d=this._model.data,Y=this.get_node(f.parent),V=this.get_node(f,true),b,Z,g,a,U=this.settings.checkbox.cascade,X=this.settings.checkbox.tie_selection;if(U.indexOf("down")!==-1){this._data[X?"core":"checkbox"].selected=Q.vakata.array_unique(this._data[X?"core":"checkbox"].selected.concat(f.children_d));for(b=0,Z=f.children_d.length;b<Z;b++){a=d[f.children_d[b]];a.state[X?"selected":"checked"]=true;if(a&&a.original&&a.original.state&&a.original.state.undetermined){a.original.state.undetermined=false}}}if(U.indexOf("up")!==-1){while(Y&&Y.id!==Q.jstree.root){g=0;for(b=0,Z=Y.children.length;b<Z;b++){g+=d[Y.children[b]].state[X?"selected":"checked"]}if(g===Z){Y.state[X?"selected":"checked"]=true;this._data[X?"core":"checkbox"].selected.push(Y.id);a=this.get_node(Y,true);if(a&&a.length){a.attr("aria-selected",true).children(".jstree-anchor").addClass(X?"jstree-clicked":"jstree-checked")}}else{break}Y=this.get_node(Y.parent)}}if(U.indexOf("down")!==-1&&V.length){V.find(".jstree-anchor").addClass(X?"jstree-clicked":"jstree-checked").parent().attr("aria-selected",true)}},this)).on(this.settings.checkbox.tie_selection?"deselect_all.jstree":"uncheck_all.jstree",Q.proxy(function(V,Z){var W=this.get_node(Q.jstree.root),X=this._model.data,U,Y,T;for(U=0,Y=W.children_d.length;U<Y;U++){T=X[W.children_d[U]];if(T&&T.original&&T.original.state&&T.original.state.undetermined){T.original.state.undetermined=false}}},this)).on(this.settings.checkbox.tie_selection?"deselect_node.jstree":"uncheck_node.jstree",Q.proxy(function(T,W){var b=W.node,V=this.get_node(b,true),a,Y,Z,U=this.settings.checkbox.cascade,X=this.settings.checkbox.tie_selection;if(b&&b.original&&b.original.state&&b.original.state.undetermined){b.original.state.undetermined=false}if(U.indexOf("down")!==-1){for(a=0,Y=b.children_d.length;a<Y;a++){Z=this._model.data[b.children_d[a]];Z.state[X?"selected":"checked"]=false;if(Z&&Z.original&&Z.original.state&&Z.original.state.undetermined){Z.original.state.undetermined=false}}}if(U.indexOf("up")!==-1){for(a=0,Y=b.parents.length;a<Y;a++){Z=this._model.data[b.parents[a]];Z.state[X?"selected":"checked"]=false;if(Z&&Z.original&&Z.original.state&&Z.original.state.undetermined){Z.original.state.undetermined=false}Z=this.get_node(b.parents[a],true);if(Z&&Z.length){Z.attr("aria-selected",false).children(".jstree-anchor").removeClass(X?"jstree-clicked":"jstree-checked")}}}Z=[];for(a=0,Y=this._data[X?"core":"checkbox"].selected.length;a<Y;a++){if((U.indexOf("down")===-1||Q.inArray(this._data[X?"core":"checkbox"].selected[a],b.children_d)===-1)&&(U.indexOf("up")===-1||Q.inArray(this._data[X?"core":"checkbox"].selected[a],b.parents)===-1)){Z.push(this._data[X?"core":"checkbox"].selected[a])}}this._data[X?"core":"checkbox"].selected=Q.vakata.array_unique(Z);if(U.indexOf("down")!==-1&&V.length){V.find(".jstree-anchor").removeClass(X?"jstree-clicked":"jstree-checked").parent().attr("aria-selected",false)}},this))}if(this.settings.checkbox.cascade.indexOf("up")!==-1){this.element.on("delete_node.jstree",Q.proxy(function(T,V){var U=this.get_node(V.parent),a=this._model.data,Z,X,b,Y,W=this.settings.checkbox.tie_selection;while(U&&U.id!==Q.jstree.root){b=0;for(Z=0,X=U.children.length;Z<X;Z++){b+=a[U.children[Z]].state[W?"selected":"checked"]}if(b===X){U.state[W?"selected":"checked"]=true;this._data[W?"core":"checkbox"].selected.push(U.id);Y=this.get_node(U,true);if(Y&&Y.length){Y.attr("aria-selected",true).children(".jstree-anchor").addClass(W?"jstree-clicked":"jstree-checked")}}else{break}U=this.get_node(U.parent)}},this)).on("move_node.jstree",Q.proxy(function(T,W){var g=W.is_multi,U=W.old_parent,a=this.get_node(W.parent),d=this._model.data,V,f,b,Y,Z,X=this.settings.checkbox.tie_selection;if(!g){V=this.get_node(U);while(V&&V.id!==Q.jstree.root){f=0;for(b=0,Y=V.children.length;b<Y;b++){f+=d[V.children[b]].state[X?"selected":"checked"]}if(f===Y){V.state[X?"selected":"checked"]=true;this._data[X?"core":"checkbox"].selected.push(V.id);Z=this.get_node(V,true);if(Z&&Z.length){Z.attr("aria-selected",true).children(".jstree-anchor").addClass(X?"jstree-clicked":"jstree-checked")}}else{break}V=this.get_node(V.parent)}}V=a;while(V&&V.id!==Q.jstree.root){f=0;for(b=0,Y=V.children.length;b<Y;b++){f+=d[V.children[b]].state[X?"selected":"checked"]}if(f===Y){if(!V.state[X?"selected":"checked"]){V.state[X?"selected":"checked"]=true;this._data[X?"core":"checkbox"].selected.push(V.id);Z=this.get_node(V,true);if(Z&&Z.length){Z.attr("aria-selected",true).children(".jstree-anchor").addClass(X?"jstree-clicked":"jstree-checked")}}}else{if(V.state[X?"selected":"checked"]){V.state[X?"selected":"checked"]=false;this._data[X?"core":"checkbox"].selected=Q.vakata.array_remove_item(this._data[X?"core":"checkbox"].selected,V.id);Z=this.get_node(V,true);if(Z&&Z.length){Z.attr("aria-selected",false).children(".jstree-anchor").removeClass(X?"jstree-clicked":"jstree-checked")}}else{break}}V=this.get_node(V.parent)}},this))}};this._undetermined=function(){if(this.element===null){return}var Z,X,Y,b,a={},c=this._model.data,W=this.settings.checkbox.tie_selection,T=this._data[W?"core":"checkbox"].selected,U=[],V=this;for(Z=0,X=T.length;Z<X;Z++){if(c[T[Z]]&&c[T[Z]].parents){for(Y=0,b=c[T[Z]].parents.length;Y<b;Y++){if(a[c[T[Z]].parents[Y]]===P&&c[T[Z]].parents[Y]!==Q.jstree.root){a[c[T[Z]].parents[Y]]=true;U.push(c[T[Z]].parents[Y])}}}}this.element.find(".jstree-closed").not(":has(.jstree-children)").each(function(){var d=V.get_node(this),e;if(!d.state.loaded){if(d.original&&d.original.state&&d.original.state.undetermined&&d.original.state.undetermined===true){if(a[d.id]===P&&d.id!==Q.jstree.root){a[d.id]=true;U.push(d.id)}for(Y=0,b=d.parents.length;Y<b;Y++){if(a[d.parents[Y]]===P&&d.parents[Y]!==Q.jstree.root){a[d.parents[Y]]=true;U.push(d.parents[Y])}}}}else{for(Z=0,X=d.children_d.length;Z<X;Z++){e=c[d.children_d[Z]];if(!e.state.loaded&&e.original&&e.original.state&&e.original.state.undetermined&&e.original.state.undetermined===true){if(a[e.id]===P&&e.id!==Q.jstree.root){a[e.id]=true;U.push(e.id)}for(Y=0,b=e.parents.length;Y<b;Y++){if(a[e.parents[Y]]===P&&e.parents[Y]!==Q.jstree.root){a[e.parents[Y]]=true;U.push(e.parents[Y])}}}}}});this.element.find(".jstree-undetermined").removeClass("jstree-undetermined");for(Z=0,X=U.length;Z<X;Z++){if(!c[U[Z]].state[W?"selected":"checked"]){T=this.get_node(U[Z],true);if(T&&T.length){T.children(".jstree-anchor").children(".jstree-checkbox").addClass("jstree-undetermined")}}}};this.redraw_node=function(Y,Z,a,T){Y=S.redraw_node.apply(this,arguments);if(Y){var W,U,V=null,X=null;for(W=0,U=Y.childNodes.length;W<U;W++){if(Y.childNodes[W]&&Y.childNodes[W].className&&Y.childNodes[W].className.indexOf("jstree-anchor")!==-1){V=Y.childNodes[W];break}}if(V){if(!this.settings.checkbox.tie_selection&&this._model.data[Y.id].state.checked){V.className+=" jstree-checked"}X=F.cloneNode(false);if(this._model.data[Y.id].state.checkbox_disabled){X.className+=" jstree-checkbox-disabled"}V.insertBefore(X,V.childNodes[0])}}if(!a&&this.settings.checkbox.cascade.indexOf("undetermined")!==-1){if(this._data.checkbox.uto){clearTimeout(this._data.checkbox.uto)}this._data.checkbox.uto=setTimeout(Q.proxy(this._undetermined,this),50)}return Y};this.show_checkboxes=function(){this._data.core.themes.checkboxes=true;this.get_container_ul().removeClass("jstree-no-checkboxes")};this.hide_checkboxes=function(){this._data.core.themes.checkboxes=false;this.get_container_ul().addClass("jstree-no-checkboxes")};this.toggle_checkboxes=function(){if(this._data.core.themes.checkboxes){this.hide_checkboxes()}else{this.show_checkboxes()}};this.is_undetermined=function(U){U=this.get_node(U);var V=this.settings.checkbox.cascade,T,Z,Y=this.settings.checkbox.tie_selection,W=this._data[Y?"core":"checkbox"].selected,X=this._model.data;if(!U||U.state[Y?"selected":"checked"]===true||V.indexOf("undetermined")===-1||(V.indexOf("down")===-1&&V.indexOf("up")===-1)){return false}if(!U.state.loaded&&U.original.state.undetermined===true){return true}for(T=0,Z=U.children_d.length;T<Z;T++){if(Q.inArray(U.children_d[T],W)!==-1||(!X[U.children_d[T]].state.loaded&&X[U.children_d[T]].original.state.undetermined)){return true}}return false};this.disable_checkbox=function(V){var W,U,T;if(Q.isArray(V)){V=V.slice();for(W=0,U=V.length;W<U;W++){this.disable_checkbox(V[W])}return true}V=this.get_node(V);if(!V||V.id===Q.jstree.root){return false}T=this.get_node(V,true);if(!V.state.checkbox_disabled){V.state.checkbox_disabled=true;if(T&&T.length){T.children(".jstree-anchor").children(".jstree-checkbox").addClass("jstree-checkbox-disabled")}this.trigger("disable_checkbox",{"node":V})}};this.enable_checkbox=function(V){var W,U,T;if(Q.isArray(V)){V=V.slice();for(W=0,U=V.length;W<U;W++){this.enable_checkbox(V[W])}return true}V=this.get_node(V);if(!V||V.id===Q.jstree.root){return false}T=this.get_node(V,true);if(V.state.checkbox_disabled){V.state.checkbox_disabled=false;if(T&&T.length){T.children(".jstree-anchor").children(".jstree-checkbox").removeClass("jstree-checkbox-disabled")}this.trigger("enable_checkbox",{"node":V})}};this.activate_node=function(T,U){if(Q(U.target).hasClass("jstree-checkbox-disabled")){return false}if(this.settings.checkbox.tie_selection&&(this.settings.checkbox.whole_node||Q(U.target).hasClass("jstree-checkbox"))){U.ctrlKey=true}if(this.settings.checkbox.tie_selection||(!this.settings.checkbox.whole_node&&!Q(U.target).hasClass("jstree-checkbox"))){return S.activate_node.call(this,T,U)}if(this.is_disabled(T)){return false}if(this.is_checked(T)){this.uncheck_node(T,U)}else{this.check_node(T,U)}this.trigger("activate_node",{"node":this.get_node(T)})};this.check_node=function(V,W){if(this.settings.checkbox.tie_selection){return this.select_node(V,false,true,W)}var T,Y,U,X;if(Q.isArray(V)){V=V.slice();for(Y=0,U=V.length;Y<U;Y++){this.check_node(V[Y],W)}return true}V=this.get_node(V);if(!V||V.id===Q.jstree.root){return false}T=this.get_node(V,true);if(!V.state.checked){V.state.checked=true;this._data.checkbox.selected.push(V.id);if(T&&T.length){T.children(".jstree-anchor").addClass("jstree-checked")}this.trigger("check_node",{"node":V,"selected":this._data.checkbox.selected,"event":W})}};this.uncheck_node=function(V,W){if(this.settings.checkbox.tie_selection){return this.deselect_node(V,false,W)}var X,U,T;if(Q.isArray(V)){V=V.slice();for(X=0,U=V.length;X<U;X++){this.uncheck_node(V[X],W)}return true}V=this.get_node(V);if(!V||V.id===Q.jstree.root){return false}T=this.get_node(V,true);if(V.state.checked){V.state.checked=false;this._data.checkbox.selected=Q.vakata.array_remove_item(this._data.checkbox.selected,V.id);if(T.length){T.children(".jstree-anchor").removeClass("jstree-checked")}this.trigger("uncheck_node",{"node":V,"selected":this._data.checkbox.selected,"event":W})}};this.check_all=function(){if(this.settings.checkbox.tie_selection){return this.select_all()}var T=this._data.checkbox.selected.concat([]),U,V;this._data.checkbox.selected=this._model.data[Q.jstree.root].children_d.concat();for(U=0,V=this._data.checkbox.selected.length;U<V;U++){if(this._model.data[this._data.checkbox.selected[U]]){this._model.data[this._data.checkbox.selected[U]].state.checked=true}}this.redraw(true);this.trigger("check_all",{"selected":this._data.checkbox.selected})};this.uncheck_all=function(){if(this.settings.checkbox.tie_selection){return this.deselect_all()}var T=this._data.checkbox.selected.concat([]),U,V;for(U=0,V=this._data.checkbox.selected.length;U<V;U++){if(this._model.data[this._data.checkbox.selected[U]]){this._model.data[this._data.checkbox.selected[U]].state.checked=false}}this._data.checkbox.selected=[];this.element.find(".jstree-checked").removeClass("jstree-checked");this.trigger("uncheck_all",{"selected":this._data.checkbox.selected,"node":T})};this.is_checked=function(T){if(this.settings.checkbox.tie_selection){return this.is_selected(T)}T=this.get_node(T);if(!T||T.id===Q.jstree.root){return false}return T.state.checked};this.get_checked=function(T){if(this.settings.checkbox.tie_selection){return this.get_selected(T)}return T?Q.map(this._data.checkbox.selected,Q.proxy(function(U){return this.get_node(U)},this)):this._data.checkbox.selected};this.get_top_checked=function(Y){if(this.settings.checkbox.tie_selection){return this.get_top_selected(Y)}var T=this.get_checked(true),V={},U,Z,W,X;for(U=0,Z=T.length;U<Z;U++){V[T[U].id]=T[U]}for(U=0,Z=T.length;U<Z;U++){for(W=0,X=T[U].children_d.length;W<X;W++){if(V[T[U].children_d[W]]){delete V[T[U].children_d[W]]}}}T=[];for(U in V){if(V.hasOwnProperty(U)){T.push(U)}}return Y?Q.map(T,Q.proxy(function(a){return this.get_node(a)},this)):T};this.get_bottom_checked=function(W){if(this.settings.checkbox.tie_selection){return this.get_bottom_selected(W)}var T=this.get_checked(true),V=[],U,X;for(U=0,X=T.length;U<X;U++){if(!T[U].children.length){V.push(T[U].id)}}return W?Q.map(V,Q.proxy(function(Y){return this.get_node(Y)},this)):V};this.load_node=function(Z,T){var W,Y,X,V,a,U;if(!Q.isArray(Z)&&!this.settings.checkbox.tie_selection){U=this.get_node(Z);if(U&&U.state.loaded){for(W=0,Y=U.children_d.length;W<Y;W++){if(this._model.data[U.children_d[W]].state.checked){a=true;this._data.checkbox.selected=Q.vakata.array_remove_item(this._data.checkbox.selected,U.children_d[W])}}}}return S.load_node.apply(this,arguments)};this.get_state=function(){var T=S.get_state.apply(this,arguments);if(this.settings.checkbox.tie_selection){return T}T.checkbox=this._data.checkbox.selected.slice();return T};this.set_state=function(W,T){var V=S.set_state.apply(this,arguments);if(V&&W.checkbox){if(!this.settings.checkbox.tie_selection){this.uncheck_all();var U=this;Q.each(W.checkbox,function(X,Y){U.check_node(Y)})}delete W.checkbox;this.set_state(W,T);return false}return V};this.refresh=function(U,T){if(!this.settings.checkbox.tie_selection){this._data.checkbox.selected=[]}return S.refresh.apply(this,arguments)}};Q.jstree.defaults.conditionalselect=function(){return true};Q.jstree.plugins.conditionalselect=function(R,S){this.activate_node=function(T,U){if(this.settings.conditionalselect.call(this,this.get_node(T),U)){S.activate_node.call(this,T,U)}}};Q.jstree.defaults.contextmenu={select_node:true,show_at_node:true,items:function(S,R){return{"create":{"separator_before":false,"separator_after":true,"_disabled":false,"label":"Create","action":function(V){var U=Q.jstree.reference(V.reference),T=U.get_node(V.reference);U.create_node(T,{},"last",function(W){setTimeout(function(){U.edit(W)},0)})}},"rename":{"separator_before":false,"separator_after":false,"_disabled":false,"label":"Rename","action":function(V){var U=Q.jstree.reference(V.reference),T=U.get_node(V.reference);U.edit(T)}},"remove":{"separator_before":false,"icon":false,"separator_after":false,"_disabled":false,"label":"Delete","action":function(V){var U=Q.jstree.reference(V.reference),T=U.get_node(V.reference);if(U.is_selected(T)){U.delete_node(U.get_selected())}else{U.delete_node(T)}}},"ccp":{"separator_before":true,"icon":false,"separator_after":false,"label":"Edit","action":false,"submenu":{"cut":{"separator_before":false,"separator_after":false,"label":"Cut","action":function(V){var U=Q.jstree.reference(V.reference),T=U.get_node(V.reference);if(U.is_selected(T)){U.cut(U.get_top_selected())}else{U.cut(T)}}},"copy":{"separator_before":false,"icon":false,"separator_after":false,"label":"Copy","action":function(V){var U=Q.jstree.reference(V.reference),T=U.get_node(V.reference);if(U.is_selected(T)){U.copy(U.get_top_selected())}else{U.copy(T)}}},"paste":{"separator_before":false,"icon":false,"_disabled":function(T){return !Q.jstree.reference(T.reference).can_paste()},"separator_after":false,"label":"Paste","action":function(V){var U=Q.jstree.reference(V.reference),T=U.get_node(V.reference);U.paste(T)}}}}}}};Q.jstree.plugins.contextmenu=function(R,S){this.bind=function(){S.bind.call(this);var V=0,W=null,U,T;this.element.on("contextmenu.jstree",".jstree-anchor",Q.proxy(function(X,Y){X.preventDefault();V=X.ctrlKey?+new Date():0;if(Y||W){V=(+new Date())+10000}if(W){clearTimeout(W)}if(!this.is_loading(X.currentTarget)){this.show_contextmenu(X.currentTarget,X.pageX,X.pageY,X)}},this)).on("click.jstree",".jstree-anchor",Q.proxy(function(X){if(this._data.contextmenu.visible&&(!V||(+new Date())-V>250)){Q.vakata.context.hide()}V=0},this)).on("touchstart.jstree",".jstree-anchor",function(X){if(!X.originalEvent||!X.originalEvent.changedTouches||!X.originalEvent.changedTouches[0]){return}U=X.pageX;T=X.pageY;W=setTimeout(function(){Q(X.currentTarget).trigger("contextmenu",true)},750)}).on("touchmove.vakata.jstree",function(X){if(W&&X.originalEvent&&X.originalEvent.changedTouches&&X.originalEvent.changedTouches[0]&&(Math.abs(U-X.pageX)>50||Math.abs(T-X.pageY)>50)){clearTimeout(W)}}).on("touchend.vakata.jstree",function(X){if(W){clearTimeout(W)}});Q(H).on("context_hide.vakata.jstree",Q.proxy(function(){this._data.contextmenu.visible=false},this))};this.teardown=function(){if(this._data.contextmenu.visible){Q.vakata.context.hide()}S.teardown.call(this)};this.show_contextmenu=function(X,Y,Z,T){X=this.get_node(X);if(!X||X.id===Q.jstree.root){return false}var U=this.settings.contextmenu,c=this.get_node(X,true),b=c.children(".jstree-anchor"),W=false,V=false;if(U.show_at_node||Y===P||Z===P){W=b.offset();Y=W.left;Z=W.top+this._data.core.li_height}if(this.settings.contextmenu.select_node&&!this.is_selected(X)){this.activate_node(X,T)}V=U.items;if(Q.isFunction(V)){V=V.call(this,X,Q.proxy(function(a){this._show_contextmenu(X,Y,Z,a)},this))}if(Q.isPlainObject(V)){this._show_contextmenu(X,Y,Z,V)}};this._show_contextmenu=function(U,W,X,T){var V=this.get_node(U,true),Y=V.children(".jstree-anchor");Q(H).one("context_show.vakata.jstree",Q.proxy(function(Z,b){var a="jstree-contextmenu jstree-"+this.get_theme()+"-contextmenu";Q(b.element).addClass(a)},this));this._data.contextmenu.visible=true;Q.vakata.context.show(Y,{"x":W,"y":X},T);this.trigger("show_contextmenu",{"node":U,"x":W,"y":X})}};(function(T){var S=false,R={element:false,reference:false,position_x:0,position_y:0,items:[],html:"",is_visible:false};T.vakata.context={settings:{hide_onmouseleave:0,icons:true},_trigger:function(U){T(H).triggerHandler("context_"+U+".vakata",{"reference":R.reference,"element":R.element,"position":{"x":R.position_x,"y":R.position_y}})},_execute:function(U){U=R.items[U];return U&&(!U._disabled||(T.isFunction(U._disabled)&&!U._disabled({"item":U,"reference":R.reference,"element":R.element})))&&U.action?U.action.call(null,{"item":U,"reference":R.reference,"element":R.element,"position":{"x":R.position_x,"y":R.position_y}}):false},_parse:function(V,X){if(!V){return false}if(!X){R.html="";R.items=[]}var Y="",W=false,U;if(X){Y+="<ul>"}T.each(V,function(Z,a){if(!a){return true}R.items.push(a);if(!W&&a.separator_before){Y+="<li class='vakata-context-separator'><a href='#' "+(T.vakata.context.settings.icons?"":'style="margin-left:0px;"')+">&#160;</a></li>"}W=false;Y+="<li class='"+(a._class||"")+(a._disabled===true||(T.isFunction(a._disabled)&&a._disabled({"item":a,"reference":R.reference,"element":R.element}))?" vakata-contextmenu-disabled ":"")+"' "+(a.shortcut?" data-shortcut='"+a.shortcut+"' ":"")+">";Y+="<a href='#' rel='"+(R.items.length-1)+"'>";if(T.vakata.context.settings.icons){Y+="<i ";if(a.icon){if(a.icon.indexOf("/")!==-1||a.icon.indexOf(".")!==-1){Y+=" style='background:url(\""+a.icon+"\") center center no-repeat' "}else{Y+=" class='"+a.icon+"' "}}Y+="></i><span class='vakata-contextmenu-sep'>&#160;</span>"}Y+=(T.isFunction(a.label)?a.label({"item":Z,"reference":R.reference,"element":R.element}):a.label)+(a.shortcut?' <span class="vakata-contextmenu-shortcut vakata-contextmenu-shortcut-'+a.shortcut+'">'+(a.shortcut_label||"")+"</span>":"")+"</a>";if(a.submenu){U=T.vakata.context._parse(a.submenu,true);if(U){Y+=U}}Y+="</li>";if(a.separator_after){Y+="<li class='vakata-context-separator'><a href='#' "+(T.vakata.context.settings.icons?"":'style="margin-left:0px;"')+">&#160;</a></li>";W=true}});Y=Y.replace(/<li class\='vakata-context-separator'\><\/li\>$/,"");if(X){Y+="</ul>"}if(!X){R.html=Y;T.vakata.context._trigger("parse")}return Y.length>10?Y:false},_show_submenu:function(Y){Y=T(Y);if(!Y.length||!Y.children("ul").length){return}var U=Y.children("ul"),a=Y.offset().left+Y.outerWidth(),b=Y.offset().top,W=U.width(),X=U.height(),V=T(window).width()+T(window).scrollLeft(),Z=T(window).height()+T(window).scrollTop();if(S){Y[a-(W+10+Y.outerWidth())<0?"addClass":"removeClass"]("vakata-context-left")}else{Y[a+W+10>V?"addClass":"removeClass"]("vakata-context-right")}if(b+X+10>Z){U.css("bottom","-1px")}U.show()},show:function(W,b,X){var a,U,d,f,Y,Z,V,c,g=true;if(R.element&&R.element.length){R.element.width("")}switch(g){case (!b&&!W):return false;case (!!b&&!!W):R.reference=W;R.position_x=b.x;R.position_y=b.y;break;case (!b&&!!W):R.reference=W;a=W.offset();R.position_x=a.left+W.outerHeight();R.position_y=a.top;break;case (!!b&&!W):R.position_x=b.x;R.position_y=b.y;break}if(!!W&&!X&&T(W).data("vakata_contextmenu")){X=T(W).data("vakata_contextmenu")}if(T.vakata.context._parse(X)){R.element.html(R.html)}if(R.items.length){R.element.appendTo("body");U=R.element;d=R.position_x;f=R.position_y;Y=U.width();Z=U.height();V=T(window).width()+T(window).scrollLeft();c=T(window).height()+T(window).scrollTop();if(S){d-=(U.outerWidth()-T(W).outerWidth());if(d<T(window).scrollLeft()+20){d=T(window).scrollLeft()+20}}if(d+Y+20>V){d=V-(Y+20)}if(f+Z+20>c){f=c-(Z+20)}R.element.css({"left":d,"top":f}).show().find("a").first().focus().parent().addClass("vakata-context-hover");R.is_visible=true;T.vakata.context._trigger("show")}},hide:function(){if(R.is_visible){R.element.hide().find("ul").hide().end().find(":focus").blur().end().detach();R.is_visible=false;T.vakata.context._trigger("hide")}}};T(function(){S=T("body").css("direction")==="rtl";var U=false;R.element=T("<ul class='vakata-context'></ul>");R.element.on("mouseenter","li",function(V){V.stopImmediatePropagation();if(T.contains(this,V.relatedTarget)){return}if(U){clearTimeout(U)}R.element.find(".vakata-context-hover").removeClass("vakata-context-hover").end();T(this).siblings().find("ul").hide().end().end().parentsUntil(".vakata-context","li").addBack().addClass("vakata-context-hover");T.vakata.context._show_submenu(this)}).on("mouseleave","li",function(V){if(T.contains(this,V.relatedTarget)){return}T(this).find(".vakata-context-hover").addBack().removeClass("vakata-context-hover")}).on("mouseleave",function(V){T(this).find(".vakata-context-hover").removeClass("vakata-context-hover");if(T.vakata.context.settings.hide_onmouseleave){U=setTimeout((function(W){return function(){T.vakata.context.hide()}}(this)),T.vakata.context.settings.hide_onmouseleave)}}).on("click","a",function(V){V.preventDefault();if(!T(this).blur().parent().hasClass("vakata-context-disabled")&&T.vakata.context._execute(T(this).attr("rel"))!==false){T.vakata.context.hide()}}).on("keydown","a",function(V){var W=null;switch(V.which){case 13:case 32:V.type="mouseup";V.preventDefault();T(V.currentTarget).trigger(V);break;case 37:if(R.is_visible){R.element.find(".vakata-context-hover").last().closest("li").first().find("ul").hide().find(".vakata-context-hover").removeClass("vakata-context-hover").end().end().children("a").focus();V.stopImmediatePropagation();V.preventDefault()}break;case 38:if(R.is_visible){W=R.element.find("ul:visible").addBack().last().children(".vakata-context-hover").removeClass("vakata-context-hover").prevAll("li:not(.vakata-context-separator)").first();if(!W.length){W=R.element.find("ul:visible").addBack().last().children("li:not(.vakata-context-separator)").last()}W.addClass("vakata-context-hover").children("a").focus();V.stopImmediatePropagation();V.preventDefault()}break;case 39:if(R.is_visible){R.element.find(".vakata-context-hover").last().children("ul").show().children("li:not(.vakata-context-separator)").removeClass("vakata-context-hover").first().addClass("vakata-context-hover").children("a").focus();V.stopImmediatePropagation();V.preventDefault()}break;case 40:if(R.is_visible){W=R.element.find("ul:visible").addBack().last().children(".vakata-context-hover").removeClass("vakata-context-hover").nextAll("li:not(.vakata-context-separator)").first();if(!W.length){W=R.element.find("ul:visible").addBack().last().children("li:not(.vakata-context-separator)").first()}W.addClass("vakata-context-hover").children("a").focus();V.stopImmediatePropagation();V.preventDefault()}break;case 27:T.vakata.context.hide();V.preventDefault();break;default:break}}).on("keydown",function(V){V.preventDefault();var W=R.element.find(".vakata-contextmenu-shortcut-"+V.which).parent();if(W.parent().not(".vakata-context-disabled")){W.click()}});T(H).on("mousedown.vakata.jstree",function(V){if(R.is_visible&&!T.contains(R.element[0],V.target)){T.vakata.context.hide()}}).on("context_show.vakata.jstree",function(V,W){R.element.find("li:has(ul)").children("a").addClass("vakata-context-parent");if(S){R.element.addClass("vakata-context-rtl").css("direction","rtl")}R.element.find("ul").hide().end()})})}(Q));Q.jstree.defaults.dnd={copy:true,open_timeout:500,is_draggable:true,check_while_dragging:true,always_copy:false,inside_pos:0,drag_selection:true,touch:true,large_drop_target:false,large_drag_target:false};Q.jstree.plugins.dnd=function(R,S){this.bind=function(){S.bind.call(this);this.element.on("mousedown.jstree touchstart.jstree",this.settings.dnd.large_drag_target?".jstree-node":".jstree-anchor",Q.proxy(function(T){if(this.settings.dnd.large_drag_target&&Q(T.target).closest(".jstree-node")[0]!==T.currentTarget){return true}if(T.type==="touchstart"&&(!this.settings.dnd.touch||(this.settings.dnd.touch==="selected"&&!Q(T.currentTarget).closest(".jstree-node").children(".jstree-anchor").hasClass("jstree-clicked")))){return true}var U=this.get_node(T.target),V=this.is_selected(U)&&this.settings.dnd.drag_selection?this.get_top_selected().length:1,W=(V>1?V+" "+this.get_string("nodes"):this.get_text(T.currentTarget));if(this.settings.core.force_text){W=Q.vakata.html.escape(W)}if(U&&U.id&&U.id!==Q.jstree.root&&(T.which===1||T.type==="touchstart")&&(this.settings.dnd.is_draggable===true||(Q.isFunction(this.settings.dnd.is_draggable)&&this.settings.dnd.is_draggable.call(this,(V>1?this.get_top_selected(true):[U]),T)))){this.element.trigger("mousedown.jstree");return Q.vakata.dnd.start(T,{"jstree":true,"origin":this,"obj":this.get_node(U,true),"nodes":V>1?this.get_top_selected():[U.id]},'<div id="jstree-dnd" class="jstree-'+this.get_theme()+" jstree-"+this.get_theme()+"-"+this.get_theme_variant()+" "+(this.settings.core.themes.responsive?" jstree-dnd-responsive":"")+'"><i class="jstree-icon jstree-er"></i>'+W+'<ins class="jstree-copy" style="display:none;">+</ins></div>')}},this))}};Q(function(){var R=false,S=false,T=false,V=false,U=Q('<div id="jstree-marker">&#160;</div>').hide();Q(H).on("dnd_start.vakata.jstree",function(W,X){R=false;T=false;if(!X||!X.data||!X.data.jstree){return}U.appendTo("body")}).on("dnd_move.vakata.jstree",function(v,w){if(V){clearTimeout(V)}if(!w||!w.data||!w.data.jstree){return}if(w.event.target.id&&w.event.target.id==="jstree-marker"){return}T=w.event;var r=Q.jstree.reference(w.event.target),q=false,g=false,j=false,Y,f,Z,b,x,c,d,m,X,a,u,W,n,s,k;if(r&&r._data&&r._data.dnd){U.attr("class","jstree-"+r.get_theme()+(r.settings.core.themes.responsive?" jstree-dnd-responsive":""));w.helper.children().attr("class","jstree-"+r.get_theme()+" jstree-"+r.get_theme()+"-"+r.get_theme_variant()+" "+(r.settings.core.themes.responsive?" jstree-dnd-responsive":"")).find(".jstree-copy").first()[w.data.origin&&(w.data.origin.settings.dnd.always_copy||(w.data.origin.settings.dnd.copy&&(w.event.metaKey||w.event.ctrlKey)))?"show":"hide"]();if((w.event.target===r.element[0]||w.event.target===r.get_container_ul()[0])&&r.get_container_ul().children().length===0){m=true;for(X=0,a=w.data.nodes.length;X<a;X++){m=m&&r.check((w.data.origin&&(w.data.origin.settings.dnd.always_copy||(w.data.origin.settings.dnd.copy&&(w.event.metaKey||w.event.ctrlKey)))?"copy_node":"move_node"),(w.data.origin&&w.data.origin!==r?w.data.origin.get_node(w.data.nodes[X]):w.data.nodes[X]),Q.jstree.root,"last",{"dnd":true,"ref":r.get_node(Q.jstree.root),"pos":"i","origin":w.data.origin,"is_multi":(w.data.origin&&w.data.origin!==r),"is_foreign":(!w.data.origin)});if(!m){break}}if(m){R={"ins":r,"par":Q.jstree.root,"pos":"last"};U.hide();w.helper.find(".jstree-icon").first().removeClass("jstree-er").addClass("jstree-ok");return}}else{q=r.settings.dnd.large_drop_target?Q(w.event.target).closest(".jstree-node").children(".jstree-anchor"):Q(w.event.target).closest(".jstree-anchor");if(q&&q.length&&q.parent().is(".jstree-closed, .jstree-open, .jstree-leaf")){g=q.offset();j=w.event.pageY-g.top;b=q.outerHeight();if(j<b/3){d=["b","i","a"]}else{if(j>b-b/3){d=["a","i","b"]}else{d=j>b/2?["i","a","b"]:["i","b","a"]}}Q.each(d,function(h,e){switch(e){case"b":f=g.left-6;Z=g.top;x=r.get_parent(q);c=q.parent().index();break;case"i":s=r.settings.dnd.inside_pos;k=r.get_node(q.parent());f=g.left-2;Z=g.top+b/2+1;x=k.id;c=s==="first"?0:(s==="last"?k.children.length:Math.min(s,k.children.length));break;case"a":f=g.left-6;Z=g.top+b;x=r.get_parent(q);c=q.parent().index()+1;break}m=true;for(X=0,a=w.data.nodes.length;X<a;X++){u=w.data.origin&&(w.data.origin.settings.dnd.always_copy||(w.data.origin.settings.dnd.copy&&(w.event.metaKey||w.event.ctrlKey)))?"copy_node":"move_node";W=c;if(u==="move_node"&&e==="a"&&(w.data.origin&&w.data.origin===r)&&x===r.get_parent(w.data.nodes[X])){n=r.get_node(x);if(W>Q.inArray(w.data.nodes[X],n.children)){W-=1}}m=m&&((r&&r.settings&&r.settings.dnd&&r.settings.dnd.check_while_dragging===false)||r.check(u,(w.data.origin&&w.data.origin!==r?w.data.origin.get_node(w.data.nodes[X]):w.data.nodes[X]),x,W,{"dnd":true,"ref":r.get_node(q.parent()),"pos":e,"origin":w.data.origin,"is_multi":(w.data.origin&&w.data.origin!==r),"is_foreign":(!w.data.origin)}));if(!m){if(r&&r.last_error){S=r.last_error()}break}}if(e==="i"&&q.parent().is(".jstree-closed")&&r.settings.dnd.open_timeout){V=setTimeout((function(l,i){return function(){l.open_node(i)}}(r,q)),r.settings.dnd.open_timeout)}if(m){R={"ins":r,"par":x,"pos":e==="i"&&s==="last"&&c===0&&!r.is_loaded(k)?"last":c};U.css({"left":f+"px","top":Z+"px"}).show();w.helper.find(".jstree-icon").first().removeClass("jstree-er").addClass("jstree-ok");S={};d=true;return false}});if(d===true){return}}}}R=false;w.helper.find(".jstree-icon").removeClass("jstree-ok").addClass("jstree-er");U.hide()}).on("dnd_scroll.vakata.jstree",function(W,X){if(!X||!X.data||!X.data.jstree){return}U.hide();R=false;T=false;X.helper.find(".jstree-icon").first().removeClass("jstree-ok").addClass("jstree-er")}).on("dnd_stop.vakata.jstree",function(X,a){if(V){clearTimeout(V)}if(!a||!a.data||!a.data.jstree){return}U.hide().detach();var W,Z,Y=[];if(R){for(W=0,Z=a.data.nodes.length;W<Z;W++){Y[W]=a.data.origin?a.data.origin.get_node(a.data.nodes[W]):a.data.nodes[W]}R.ins[a.data.origin&&(a.data.origin.settings.dnd.always_copy||(a.data.origin.settings.dnd.copy&&(a.event.metaKey||a.event.ctrlKey)))?"copy_node":"move_node"](Y,R.par,R.pos,false,false,false,a.data.origin)}else{W=Q(a.event.target).closest(".jstree");if(W.length&&S&&S.error&&S.error==="check"){W=W.jstree(true);if(W){W.settings.core.error.call(this,S)}}}T=false;R=false}).on("keyup.jstree keydown.jstree",function(W,X){X=Q.vakata.dnd._get();if(X&&X.data&&X.data.jstree){X.helper.find(".jstree-copy").first()[X.data.origin&&(X.data.origin.settings.dnd.always_copy||(X.data.origin.settings.dnd.copy&&(W.metaKey||W.ctrlKey)))?"show":"hide"]();if(T){T.metaKey=W.metaKey;T.ctrlKey=W.ctrlKey;Q.vakata.dnd._trigger("move",T)}}})});(function(S){S.vakata.html={div:S("<div />"),escape:function(T){return S.vakata.html.div.text(T).html()},strip:function(T){return S.vakata.html.div.empty().append(S.parseHTML(T)).text()}};var R={element:false,target:false,is_down:false,is_drag:false,helper:false,helper_w:0,data:false,init_x:0,init_y:0,scroll_l:0,scroll_t:0,scroll_e:false,scroll_i:false,is_touch:false};S.vakata.dnd={settings:{scroll_speed:10,scroll_proximity:20,helper_left:5,helper_top:10,threshold:5,threshold_touch:50},_trigger:function(V,T){var U=S.vakata.dnd._get();U.event=T;S(H).triggerHandler("dnd_"+V+".vakata",U)},_get:function(){return{"data":R.data,"element":R.element,"helper":R.helper}},_clean:function(){if(R.helper){R.helper.remove()}if(R.scroll_i){clearInterval(R.scroll_i);R.scroll_i=false}R={element:false,target:false,is_down:false,is_drag:false,helper:false,helper_w:0,data:false,init_x:0,init_y:0,scroll_l:0,scroll_t:0,scroll_e:false,scroll_i:false,is_touch:false};S(H).off("mousemove.vakata.jstree touchmove.vakata.jstree",S.vakata.dnd.drag);S(H).off("mouseup.vakata.jstree touchend.vakata.jstree",S.vakata.dnd.stop)},_scroll:function(T){if(!R.scroll_e||(!R.scroll_l&&!R.scroll_t)){if(R.scroll_i){clearInterval(R.scroll_i);R.scroll_i=false}return false}if(!R.scroll_i){R.scroll_i=setInterval(S.vakata.dnd._scroll,100);return false}if(T===true){return false}var U=R.scroll_e.scrollTop(),V=R.scroll_e.scrollLeft();R.scroll_e.scrollTop(U+R.scroll_t*S.vakata.dnd.settings.scroll_speed);R.scroll_e.scrollLeft(V+R.scroll_l*S.vakata.dnd.settings.scroll_speed);if(U!==R.scroll_e.scrollTop()||V!==R.scroll_e.scrollLeft()){S.vakata.dnd._trigger("scroll",R.scroll_e)}},start:function(U,W,T){if(U.type==="touchstart"&&U.originalEvent&&U.originalEvent.changedTouches&&U.originalEvent.changedTouches[0]){U.pageX=U.originalEvent.changedTouches[0].pageX;U.pageY=U.originalEvent.changedTouches[0].pageY;U.target=H.elementFromPoint(U.originalEvent.changedTouches[0].pageX-window.pageXOffset,U.originalEvent.changedTouches[0].pageY-window.pageYOffset)}if(R.is_drag){S.vakata.dnd.stop({})}try{U.currentTarget.unselectable="on";U.currentTarget.onselectstart=function(){return false};if(U.currentTarget.style){U.currentTarget.style.MozUserSelect="none"}}catch(V){}R.init_x=U.pageX;R.init_y=U.pageY;R.data=W;R.is_down=true;R.element=U.currentTarget;R.target=U.target;R.is_touch=U.type==="touchstart";if(T!==false){R.helper=S("<div id='vakata-dnd'></div>").html(T).css({"display":"block","margin":"0","padding":"0","position":"absolute","top":"-2000px","lineHeight":"16px","zIndex":"10000"})}S(H).on("mousemove.vakata.jstree touchmove.vakata.jstree",S.vakata.dnd.drag);S(H).on("mouseup.vakata.jstree touchend.vakata.jstree",S.vakata.dnd.stop);return false},drag:function(T){if(T.type==="touchmove"&&T.originalEvent&&T.originalEvent.changedTouches&&T.originalEvent.changedTouches[0]){T.pageX=T.originalEvent.changedTouches[0].pageX;T.pageY=T.originalEvent.changedTouches[0].pageY;T.target=H.elementFromPoint(T.originalEvent.changedTouches[0].pageX-window.pageXOffset,T.originalEvent.changedTouches[0].pageY-window.pageYOffset)}if(!R.is_down){return}if(!R.is_drag){if(Math.abs(T.pageX-R.init_x)>(R.is_touch?S.vakata.dnd.settings.threshold_touch:S.vakata.dnd.settings.threshold)||Math.abs(T.pageY-R.init_y)>(R.is_touch?S.vakata.dnd.settings.threshold_touch:S.vakata.dnd.settings.threshold)){if(R.helper){R.helper.appendTo("body");R.helper_w=R.helper.outerWidth()}R.is_drag=true;S.vakata.dnd._trigger("start",T)}else{return}}var f=false,V=false,Z=false,c=false,U=false,X=false,W=false,Y=false,a=false,b=false;R.scroll_t=0;R.scroll_l=0;R.scroll_e=false;S(S(T.target).parentsUntil("body").addBack().get().reverse()).filter(function(){return(/^auto|scroll$/).test(S(this).css("overflow"))&&(this.scrollHeight>this.offsetHeight||this.scrollWidth>this.offsetWidth)}).each(function(){var e=S(this),d=e.offset();if(this.scrollHeight>this.offsetHeight){if(d.top+e.height()-T.pageY<S.vakata.dnd.settings.scroll_proximity){R.scroll_t=1}if(T.pageY-d.top<S.vakata.dnd.settings.scroll_proximity){R.scroll_t=-1}}if(this.scrollWidth>this.offsetWidth){if(d.left+e.width()-T.pageX<S.vakata.dnd.settings.scroll_proximity){R.scroll_l=1}if(T.pageX-d.left<S.vakata.dnd.settings.scroll_proximity){R.scroll_l=-1}}if(R.scroll_t||R.scroll_l){R.scroll_e=S(this);return false}});if(!R.scroll_e){f=S(H);V=S(window);Z=f.height();c=V.height();U=f.width();X=V.width();W=f.scrollTop();Y=f.scrollLeft();if(Z>c&&T.pageY-W<S.vakata.dnd.settings.scroll_proximity){R.scroll_t=-1}if(Z>c&&c-(T.pageY-W)<S.vakata.dnd.settings.scroll_proximity){R.scroll_t=1}if(U>X&&T.pageX-Y<S.vakata.dnd.settings.scroll_proximity){R.scroll_l=-1}if(U>X&&X-(T.pageX-Y)<S.vakata.dnd.settings.scroll_proximity){R.scroll_l=1}if(R.scroll_t||R.scroll_l){R.scroll_e=f}}if(R.scroll_e){S.vakata.dnd._scroll(true)}if(R.helper){a=parseInt(T.pageY+S.vakata.dnd.settings.helper_top,10);b=parseInt(T.pageX+S.vakata.dnd.settings.helper_left,10);if(Z&&a+25>Z){a=Z-50}if(U&&b+R.helper_w>U){b=U-(R.helper_w+2)}R.helper.css({left:b+"px",top:a+"px"})}S.vakata.dnd._trigger("move",T);return false},stop:function(T){if(T.type==="touchend"&&T.originalEvent&&T.originalEvent.changedTouches&&T.originalEvent.changedTouches[0]){T.pageX=T.originalEvent.changedTouches[0].pageX;T.pageY=T.originalEvent.changedTouches[0].pageY;T.target=H.elementFromPoint(T.originalEvent.changedTouches[0].pageX-window.pageXOffset,T.originalEvent.changedTouches[0].pageY-window.pageYOffset)}if(R.is_drag){S.vakata.dnd._trigger("stop",T)}else{if(T.type==="touchend"&&T.target===R.target){var U=setTimeout(function(){S(T.target).click()},100);S(T.target).one("click",function(){if(U){clearTimeout(U)}})}}S.vakata.dnd._clean();return false}}}(Q));Q.jstree.defaults.massload=null;Q.jstree.plugins.massload=function(R,S){this.init=function(T,U){S.init.call(this,T,U);this._data.massload={}};this._load_nodes=function(W,T,V){var U=this.settings.massload;if(V&&!Q.isEmptyObject(this._data.massload)){return S._load_nodes.call(this,W,T,V)}if(Q.isFunction(U)){return U.call(this,W,Q.proxy(function(Y){if(Y){for(var X in Y){if(Y.hasOwnProperty(X)){this._data.massload[X]=Y[X]}}}S._load_nodes.call(this,W,T,V)},this))}if(typeof U==="object"&&U&&U.url){U=Q.extend(true,{},U);if(Q.isFunction(U.url)){U.url=U.url.call(this,W)}if(Q.isFunction(U.data)){U.data=U.data.call(this,W)}return Q.ajax(U).done(Q.proxy(function(a,Z,Y){if(a){for(var X in a){if(a.hasOwnProperty(X)){this._data.massload[X]=a[X]}}}S._load_nodes.call(this,W,T,V)},this)).fail(Q.proxy(function(X){S._load_nodes.call(this,W,T,V)},this))}return S._load_nodes.call(this,W,T,V)};this._load_node=function(V,T){var U=this._data.massload[V.id];if(U){return this[typeof U==="string"?"_append_html_data":"_append_json_data"](V,typeof U==="string"?Q(Q.parseHTML(U)).filter(function(){return this.nodeType!==3}):U,function(W){T.call(this,W);delete this._data.massload[V.id]})}return S._load_node.call(this,V,T)}};Q.jstree.defaults.search={ajax:false,fuzzy:false,case_sensitive:false,show_only_matches:false,show_only_matches_children:false,close_opened_onclear:true,search_leaves_only:false,search_callback:false};Q.jstree.plugins.search=function(R,S){this.bind=function(){S.bind.call(this);this._data.search.str="";this._data.search.dom=Q();this._data.search.res=[];this._data.search.opn=[];this._data.search.som=false;this._data.search.smc=false;this._data.search.hdn=[];this.element.on("search.jstree",Q.proxy(function(U,Y){if(this._data.search.som&&Y.res.length){var W=this._model.data,T,X,V=[];for(T=0,X=Y.res.length;T<X;T++){if(W[Y.res[T]]&&!W[Y.res[T]].state.hidden){V.push(Y.res[T]);V=V.concat(W[Y.res[T]].parents);if(this._data.search.smc){V=V.concat(W[Y.res[T]].children_d)}}}V=Q.vakata.array_remove_item(Q.vakata.array_unique(V),Q.jstree.root);this._data.search.hdn=this.hide_all();this.show_node(V)}},this)).on("clear_search.jstree",Q.proxy(function(T,U){if(this._data.search.som&&U.res.length){this.show_node(this._data.search.hdn)}},this))};this.search=function(Y,X,b,e,g,W){if(Y===false||Q.trim(Y.toString())===""){return this.clear_search()}e=this.get_node(e);e=e&&e.id?e.id:null;Y=Y.toString();var U=this.settings.search,h=U.ajax?U.ajax:false,d=this._model.data,k=null,T=[],V=[],Z,c;if(this._data.search.res.length&&!g){this.clear_search()}if(b===P){b=U.show_only_matches}if(W===P){W=U.show_only_matches_children}if(!X&&h!==false){if(Q.isFunction(h)){return h.call(this,Y,Q.proxy(function(a){if(a&&a.d){a=a.d}this._load_nodes(!Q.isArray(a)?[]:Q.vakata.array_unique(a),function(){this.search(Y,true,b,e,g)},true)},this),e)}else{h=Q.extend({},h);if(!h.data){h.data={}}h.data.str=Y;if(e){h.data.inside=e}return Q.ajax(h).fail(Q.proxy(function(){this._data.core.last_error={"error":"ajax","plugin":"search","id":"search_01","reason":"Could not load search parents","data":JSON.stringify(h)};this.settings.core.error.call(this,this._data.core.last_error)},this)).done(Q.proxy(function(a){if(a&&a.d){a=a.d}this._load_nodes(!Q.isArray(a)?[]:Q.vakata.array_unique(a),function(){this.search(Y,true,b,e,g)},true)},this))}}if(!g){this._data.search.str=Y;this._data.search.dom=Q();this._data.search.res=[];this._data.search.opn=[];this._data.search.som=b;this._data.search.smc=W}k=new Q.vakata.search(Y,true,{caseSensitive:U.case_sensitive,fuzzy:U.fuzzy});Q.each(d[e?e:Q.jstree.root].children_d,function(f,a){var j=d[a];if(j.text&&(!U.search_leaves_only||(j.state.loaded&&j.children.length===0))&&((U.search_callback&&U.search_callback.call(this,Y,j))||(!U.search_callback&&k.search(j.text).isMatch))){T.push(a);V=V.concat(j.parents)}});if(T.length){V=Q.vakata.array_unique(V);this._search_open(V);if(!g){this._data.search.dom=Q(this.element[0].querySelectorAll("#"+Q.map(T,function(a){return"0123456789".indexOf(a[0])!==-1?"\\3"+a[0]+" "+a.substr(1).replace(Q.jstree.idregex,"\\$&"):a.replace(Q.jstree.idregex,"\\$&")}).join(", #")));this._data.search.res=T}else{this._data.search.dom=this._data.search.dom.add(Q(this.element[0].querySelectorAll("#"+Q.map(T,function(a){return"0123456789".indexOf(a[0])!==-1?"\\3"+a[0]+" "+a.substr(1).replace(Q.jstree.idregex,"\\$&"):a.replace(Q.jstree.idregex,"\\$&")}).join(", #"))));this._data.search.res=Q.vakata.array_unique(this._data.search.res.concat(T))}this._data.search.dom.children(".jstree-anchor").addClass("jstree-search")}this.trigger("search",{nodes:this._data.search.dom,str:Y,res:this._data.search.res,show_only_matches:b})};this.clear_search=function(){if(this.settings.search.close_opened_onclear){this.close_node(this._data.search.opn,0)}this.trigger("clear_search",{"nodes":this._data.search.dom,str:this._data.search.str,res:this._data.search.res});if(this._data.search.res.length){this._data.search.dom=Q(this.element[0].querySelectorAll("#"+Q.map(this._data.search.res,function(T){return"0123456789".indexOf(T[0])!==-1?"\\3"+T[0]+" "+T.substr(1).replace(Q.jstree.idregex,"\\$&"):T.replace(Q.jstree.idregex,"\\$&")}).join(", #")));this._data.search.dom.children(".jstree-anchor").removeClass("jstree-search")}this._data.search.str="";this._data.search.res=[];this._data.search.opn=[];this._data.search.dom=Q()};this._search_open=function(T){var U=this;Q.each(T.concat([]),function(V,X){if(X===Q.jstree.root){return true}try{X=Q("#"+X.replace(Q.jstree.idregex,"\\$&"),U.element)}catch(W){}if(X&&X.length){if(U.is_closed(X)){U._data.search.opn.push(X[0].id);U.open_node(X,function(){U._search_open(T)},0)}}})};this.redraw_node=function(V,W,T,X){V=S.redraw_node.apply(this,arguments);if(V){if(Q.inArray(V.id,this._data.search.res)!==-1){var U,Z,Y=null;for(U=0,Z=V.childNodes.length;U<Z;U++){if(V.childNodes[U]&&V.childNodes[U].className&&V.childNodes[U].className.indexOf("jstree-anchor")!==-1){Y=V.childNodes[U];break}}if(Y){Y.className+=" jstree-search"}}}return V}};(function(R){R.vakata.search=function(W,Y,a){a=a||{};a=R.extend({},R.vakata.search.defaults,a);if(a.fuzzy!==false){a.fuzzy=true}W=a.caseSensitive?W:W.toLowerCase();var U=a.location,Z=a.distance,c=a.threshold,b=W.length,S,V,T,X;if(b>32){a.fuzzy=false}if(a.fuzzy){S=1<<(b-1);V=(function(){var e={},d=0;for(d=0;d<b;d++){e[W.charAt(d)]=0}for(d=0;d<b;d++){e[W.charAt(d)]|=1<<(b-d-1)}return e}());T=function(d,f){var h=d/b,g=Math.abs(U-f);if(!Z){return g?1:h}return h+(g/Z)}}X=function(p){p=a.caseSensitive?p:p.toLowerCase();if(W===p||p.indexOf(W)!==-1){return{isMatch:true,score:0}}if(!a.fuzzy){return{isMatch:false,score:1}}var q,l,n=p.length,u=c,h=p.indexOf(W,U),f,m,k=b+n,o,e,s,r,g,t=1,d=[];if(h!==-1){u=Math.min(T(0,h),u);h=p.lastIndexOf(W,U+b);if(h!==-1){u=Math.min(T(0,h),u)}}h=-1;for(q=0;q<b;q++){f=0;m=k;while(f<m){if(T(q,U+m)<=u){f=m}else{k=m}m=Math.floor((k-f)/2+f)}k=m;e=Math.max(1,U-m+1);s=Math.min(U+m,n)+b;r=new Array(s+2);r[s+1]=(1<<q)-1;for(l=s;l>=e;l--){g=V[p.charAt(l-1)];if(q===0){r[l]=((r[l+1]<<1)|1)&g}else{r[l]=((r[l+1]<<1)|1)&g|(((o[l+1]|o[l])<<1)|1)|o[l+1]}if(r[l]&S){t=T(q,l-1);if(t<=u){u=t;h=l-1;d.push(h);if(h>U){e=Math.max(1,2*U-h)}else{break}}}}if(T(q+1,U)>u){break}o=r}return{isMatch:h>=0,score:t}};return Y===true?{"search":X}:X(Y)};R.vakata.search.defaults={location:0,distance:100,threshold:0.6,fuzzy:false,caseSensitive:false}}(Q));Q.jstree.defaults.sort=function(S,R){return this.get_text(S)>this.get_text(R)?1:-1};Q.jstree.plugins.sort=function(R,S){this.bind=function(){S.bind.call(this);this.element.on("model.jstree",Q.proxy(function(T,U){this.sort(U.parent,true)},this)).on("rename_node.jstree create_node.jstree",Q.proxy(function(T,U){this.sort(U.parent||U.node.parent,false);this.redraw_node(U.parent||U.node.parent,true)},this)).on("move_node.jstree copy_node.jstree",Q.proxy(function(T,U){this.sort(U.parent,false);this.redraw_node(U.parent,true)},this))};this.sort=function(U,V){var T,W;U=this.get_node(U);if(U&&U.children&&U.children.length){U.children.sort(Q.proxy(this.settings.sort,this));if(V){for(T=0,W=U.children_d.length;T<W;T++){this.sort(U.children_d[T],false)}}}}};var A=false;Q.jstree.defaults.state={key:"jstree",events:"changed.jstree open_node.jstree close_node.jstree check_node.jstree uncheck_node.jstree",ttl:false,filter:false};Q.jstree.plugins.state=function(R,S){this.bind=function(){S.bind.call(this);var T=Q.proxy(function(){this.element.on(this.settings.state.events,Q.proxy(function(){if(A){clearTimeout(A)}A=setTimeout(Q.proxy(function(){this.save_state()},this),100)},this));this.trigger("state_ready")},this);this.element.on("ready.jstree",Q.proxy(function(U,V){this.element.one("restore_state.jstree",T);if(!this.restore_state()){T()}},this))};this.save_state=function(){var T={"state":this.get_state(),"ttl":this.settings.state.ttl,"sec":+(new Date())};Q.vakata.storage.set(this.settings.state.key,JSON.stringify(T))};this.restore_state=function(){var U=Q.vakata.storage.get(this.settings.state.key);if(!!U){try{U=JSON.parse(U)}catch(T){return false}}if(!!U&&U.ttl&&U.sec&&+(new Date())-U.sec>U.ttl){return false}if(!!U&&U.state){U=U.state}if(!!U&&Q.isFunction(this.settings.state.filter)){U=this.settings.state.filter.call(this,U)}if(!!U){this.element.one("set_state.jstree",function(V,W){W.instance.trigger("restore_state",{"state":Q.extend(true,{},U)})});this.set_state(U);return true}return false};this.clear_state=function(){return Q.vakata.storage.del(this.settings.state.key)}};(function(S,R){S.vakata.storage={set:function(T,U){return window.localStorage.setItem(T,U)},get:function(T){return window.localStorage.getItem(T)},del:function(T){return window.localStorage.removeItem(T)}}}(Q));Q.jstree.defaults.types={"default":{}};Q.jstree.defaults.types[Q.jstree.root]={};Q.jstree.plugins.types=function(R,S){this.init=function(T,V){var U,W;if(V&&V.types&&V.types["default"]){for(U in V.types){if(U!=="default"&&U!==Q.jstree.root&&V.types.hasOwnProperty(U)){for(W in V.types["default"]){if(V.types["default"].hasOwnProperty(W)&&V.types[U][W]===P){V.types[U][W]=V.types["default"][W]}}}}}S.init.call(this,T,V);this._model.data[Q.jstree.root].type=Q.jstree.root};this.refresh=function(U,T){S.refresh.call(this,U,T);this._model.data[Q.jstree.root].type=Q.jstree.root};this.bind=function(){this.element.on("model.jstree",Q.proxy(function(T,U){var Z=this._model.data,X=U.nodes,V=this.settings.types,Y,W,a="default";for(Y=0,W=X.length;Y<W;Y++){a="default";if(Z[X[Y]].original&&Z[X[Y]].original.type&&V[Z[X[Y]].original.type]){a=Z[X[Y]].original.type}if(Z[X[Y]].data&&Z[X[Y]].data.jstree&&Z[X[Y]].data.jstree.type&&V[Z[X[Y]].data.jstree.type]){a=Z[X[Y]].data.jstree.type}Z[X[Y]].type=a;if(Z[X[Y]].icon===true&&V[a].icon!==P){Z[X[Y]].icon=V[a].icon}}Z[Q.jstree.root].type=Q.jstree.root},this));S.bind.call(this)};this.get_json=function(Z,X,a){var W,U,Y=this._model.data,T=X?Q.extend(true,{},X,{no_id:false}):{},V=S.get_json.call(this,Z,T,a);if(V===false){return false}if(Q.isArray(V)){for(W=0,U=V.length;W<U;W++){V[W].type=V[W].id&&Y[V[W].id]&&Y[V[W].id].type?Y[V[W].id].type:"default";if(X&&X.no_id){delete V[W].id;if(V[W].li_attr&&V[W].li_attr.id){delete V[W].li_attr.id}if(V[W].a_attr&&V[W].a_attr.id){delete V[W].a_attr.id}}}}else{V.type=V.id&&Y[V.id]&&Y[V.id].type?Y[V.id].type:"default";if(X&&X.no_id){V=this._delete_ids(V)}}return V};this._delete_ids=function(T){if(Q.isArray(T)){for(var U=0,V=T.length;U<V;U++){T[U]=this._delete_ids(T[U])}return T}delete T.id;if(T.li_attr&&T.li_attr.id){delete T.li_attr.id}if(T.a_attr&&T.a_attr.id){delete T.a_attr.id}if(T.children&&Q.isArray(T.children)){T.children=this._delete_ids(T.children)}return T};this.check=function(c,b,V,Z,T){if(S.check.call(this,c,b,V,Z,T)===false){return false}b=b&&b.id?b:this.get_node(b);V=V&&V.id?V:this.get_node(V);var a=b&&b.id?(T&&T.origin?T.origin:Q.jstree.reference(b.id)):null,X,U,Y,W;a=a&&a._model&&a._model.data?a._model.data:null;switch(c){case"create_node":case"move_node":case"copy_node":if(c!=="move_node"||Q.inArray(b.id,V.children)===-1){X=this.get_rules(V);if(X.max_children!==P&&X.max_children!==-1&&X.max_children===V.children.length){this._data.core.last_error={"error":"check","plugin":"types","id":"types_01","reason":"max_children prevents function: "+c,"data":JSON.stringify({"chk":c,"pos":Z,"obj":b&&b.id?b.id:false,"par":V&&V.id?V.id:false})};return false}if(X.valid_children!==P&&X.valid_children!==-1&&Q.inArray((b.type||"default"),X.valid_children)===-1){this._data.core.last_error={"error":"check","plugin":"types","id":"types_02","reason":"valid_children prevents function: "+c,"data":JSON.stringify({"chk":c,"pos":Z,"obj":b&&b.id?b.id:false,"par":V&&V.id?V.id:false})};return false}if(a&&b.children_d&&b.parents){U=0;for(Y=0,W=b.children_d.length;Y<W;Y++){U=Math.max(U,a[b.children_d[Y]].parents.length)}U=U-b.parents.length+1}if(U<=0||U===P){U=1}do{if(X.max_depth!==P&&X.max_depth!==-1&&X.max_depth<U){this._data.core.last_error={"error":"check","plugin":"types","id":"types_03","reason":"max_depth prevents function: "+c,"data":JSON.stringify({"chk":c,"pos":Z,"obj":b&&b.id?b.id:false,"par":V&&V.id?V.id:false})};return false}V=this.get_node(V.parent);X=this.get_rules(V);U++}while(V)}break}return true};this.get_rules=function(U){U=this.get_node(U);if(!U){return false}var T=this.get_type(U,true);if(T.max_depth===P){T.max_depth=-1}if(T.max_children===P){T.max_children=-1}if(T.valid_children===P){T.valid_children=-1}return T};this.get_type=function(T,U){T=this.get_node(T);return(!T)?false:(U?Q.extend({"type":T.type},this.settings.types[T.type]):T.type)};this.set_type=function(V,T){var X,Z,U,W,Y;if(Q.isArray(V)){V=V.slice();for(Z=0,U=V.length;Z<U;Z++){this.set_type(V[Z],T)}return true}X=this.settings.types;V=this.get_node(V);if(!X[T]||!V){return false}W=V.type;Y=this.get_icon(V);V.type=T;if(Y===true||(X[W]&&X[W].icon!==P&&Y===X[W].icon)){this.set_icon(V,X[T].icon!==P?X[T].icon:true)}return true}};Q.jstree.defaults.unique={case_sensitive:false,duplicate:function(S,R){return S+" ("+R+")"}};Q.jstree.plugins.unique=function(R,S){this.check=function(e,b,V,Z,T){if(S.check.call(this,e,b,V,Z,T)===false){return false}b=b&&b.id?b:this.get_node(b);V=V&&V.id?V:this.get_node(V);if(!V||!V.children){return true}var Y=e==="rename_node"?Z:b.text,d=[],U=this.settings.unique.case_sensitive,a=this._model.data,X,W;for(X=0,W=V.children.length;X<W;X++){d.push(U?a[V.children[X]].text:a[V.children[X]].text.toLowerCase())}if(!U){Y=Y.toLowerCase()}switch(e){case"delete_node":return true;case"rename_node":X=(Q.inArray(Y,d)===-1||(b.text&&b.text[U?"toString":"toLowerCase"]()===Y));if(!X){this._data.core.last_error={"error":"check","plugin":"unique","id":"unique_01","reason":"Child with name "+Y+" already exists. Preventing: "+e,"data":JSON.stringify({"chk":e,"pos":Z,"obj":b&&b.id?b.id:false,"par":V&&V.id?V.id:false})}}return X;case"create_node":X=(Q.inArray(Y,d)===-1);if(!X){this._data.core.last_error={"error":"check","plugin":"unique","id":"unique_04","reason":"Child with name "+Y+" already exists. Preventing: "+e,"data":JSON.stringify({"chk":e,"pos":Z,"obj":b&&b.id?b.id:false,"par":V&&V.id?V.id:false})}}return X;case"copy_node":X=(Q.inArray(Y,d)===-1);if(!X){this._data.core.last_error={"error":"check","plugin":"unique","id":"unique_02","reason":"Child with name "+Y+" already exists. Preventing: "+e,"data":JSON.stringify({"chk":e,"pos":Z,"obj":b&&b.id?b.id:false,"par":V&&V.id?V.id:false})}}return X;case"move_node":X=((b.parent===V.id&&(!T||!T.is_multi))||Q.inArray(Y,d)===-1);if(!X){this._data.core.last_error={"error":"check","plugin":"unique","id":"unique_03","reason":"Child with name "+Y+" already exists. Preventing: "+e,"data":JSON.stringify({"chk":e,"pos":Z,"obj":b&&b.id?b.id:false,"par":V&&V.id?V.id:false})}}return X}return true};this.create_node=function(X,c,d,U,W){if(!c||c.text===P){if(X===null){X=Q.jstree.root}X=this.get_node(X);if(!X){return S.create_node.call(this,X,c,d,U,W)}d=d===P?"last":d;if(!d.toString().match(/^(before|after)$/)&&!W&&!this.is_loaded(X)){return S.create_node.call(this,X,c,d,U,W)}if(!c){c={}}var Z,b,a,f,Y,e=this._model.data,T=this.settings.unique.case_sensitive,V=this.settings.unique.duplicate;b=Z=this.get_string("New node");a=[];for(f=0,Y=X.children.length;f<Y;f++){a.push(T?e[X.children[f]].text:e[X.children[f]].text.toLowerCase())}f=1;while(Q.inArray(T?b:b.toLowerCase(),a)!==-1){b=V.call(this,Z,(++f)).toString()}c.text=b}return S.create_node.call(this,X,c,d,U,W)}};var E=H.createElement("DIV");E.setAttribute("unselectable","on");E.setAttribute("role","presentation");E.className="jstree-wholerow";E.innerHTML="&#160;";Q.jstree.plugins.wholerow=function(R,S){this.bind=function(){S.bind.call(this);this.element.on("ready.jstree set_state.jstree",Q.proxy(function(){this.hide_dots()},this)).on("init.jstree loading.jstree ready.jstree",Q.proxy(function(){this.get_container_ul().addClass("jstree-wholerow-ul")},this)).on("deselect_all.jstree",Q.proxy(function(T,U){this.element.find(".jstree-wholerow-clicked").removeClass("jstree-wholerow-clicked")},this)).on("changed.jstree",Q.proxy(function(V,X){this.element.find(".jstree-wholerow-clicked").removeClass("jstree-wholerow-clicked");var T=false,U,W;for(U=0,W=X.selected.length;U<W;U++){T=this.get_node(X.selected[U],true);if(T&&T.length){T.children(".jstree-wholerow").addClass("jstree-wholerow-clicked")}}},this)).on("open_node.jstree",Q.proxy(function(T,U){this.get_node(U.node,true).find(".jstree-clicked").parent().children(".jstree-wholerow").addClass("jstree-wholerow-clicked")},this)).on("hover_node.jstree dehover_node.jstree",Q.proxy(function(T,U){if(T.type==="hover_node"&&this.is_disabled(U.node)){return}this.get_node(U.node,true).children(".jstree-wholerow")[T.type==="hover_node"?"addClass":"removeClass"]("jstree-wholerow-hovered")},this)).on("contextmenu.jstree",".jstree-wholerow",Q.proxy(function(U){U.preventDefault();var T=Q.Event("contextmenu",{metaKey:U.metaKey,ctrlKey:U.ctrlKey,altKey:U.altKey,shiftKey:U.shiftKey,pageX:U.pageX,pageY:U.pageY});Q(U.currentTarget).closest(".jstree-node").children(".jstree-anchor").first().trigger(T)},this))
/*
				.on("mousedown.jstree touchstart.jstree", ".jstree-wholerow", function (e) {
						if(e.target === e.currentTarget) {
							var a = $(e.currentTarget).closest(".jstree-node").children(".jstree-anchor");
							e.target = a[0];
							a.trigger(e);
						}
					})
				*/
.on("click.jstree",".jstree-wholerow",function(U){U.stopImmediatePropagation();var T=Q.Event("click",{metaKey:U.metaKey,ctrlKey:U.ctrlKey,altKey:U.altKey,shiftKey:U.shiftKey});Q(U.currentTarget).closest(".jstree-node").children(".jstree-anchor").first().trigger(T).focus()}).on("click.jstree",".jstree-leaf > .jstree-ocl",Q.proxy(function(U){U.stopImmediatePropagation();var T=Q.Event("click",{metaKey:U.metaKey,ctrlKey:U.ctrlKey,altKey:U.altKey,shiftKey:U.shiftKey});Q(U.currentTarget).closest(".jstree-node").children(".jstree-anchor").first().trigger(T).focus()},this)).on("mouseover.jstree",".jstree-wholerow, .jstree-icon",Q.proxy(function(T){T.stopImmediatePropagation();if(!this.is_disabled(T.currentTarget)){this.hover_node(T.currentTarget)}return false},this)).on("mouseleave.jstree",".jstree-node",Q.proxy(function(T){this.dehover_node(T.currentTarget)},this))};this.teardown=function(){if(this.settings.wholerow){this.element.find(".jstree-wholerow").remove()}S.teardown.call(this)};this.redraw_node=function(U,V,T,W){U=S.redraw_node.apply(this,arguments);if(U){var X=E.cloneNode(true);if(Q.inArray(U.id,this._data.core.selected)!==-1){X.className+=" jstree-wholerow-clicked"}if(this._data.core.focused&&this._data.core.focused===U.id){X.className+=" jstree-wholerow-hovered"}U.insertBefore(X,U.childNodes[0])}return U}};if(H.registerElement&&Object&&Object.create){var D=Object.create(HTMLElement.prototype);D.createdCallback=function(){var S={core:{},plugins:[]},R;for(R in Q.jstree.plugins){if(Q.jstree.plugins.hasOwnProperty(R)&&this.attributes[R]){S.plugins.push(R);if(this.getAttribute(R)&&JSON.parse(this.getAttribute(R))){S[R]=JSON.parse(this.getAttribute(R))}}}for(R in Q.jstree.defaults.core){if(Q.jstree.defaults.core.hasOwnProperty(R)&&this.attributes[R]){S.core[R]=JSON.parse(this.getAttribute(R))||this.getAttribute(R)}}Q(this).jstree(S)};try{H.registerElement("vakata-jstree",{prototype:D})}catch(M){}}return Q.fn.jstree}));