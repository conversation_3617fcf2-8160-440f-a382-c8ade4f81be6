!function(A){"function"==typeof define&&define.amd?define(["jquery"],A):window.jQuery&&!window.jQuery.fn.colorpicker&&A(window.jQuery)}(function(D){var B=function(E){this.value={h:0,s:0,b:0,a:1},this.origFormat=null,E&&(void 0!==E.toLowerCase?this.setColor(E):void 0!==E.h&&(this.value=E))};B.prototype={constructor:B,colors:{aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgreen:"#006400",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",gold:"#ffd700",goldenrod:"#daa520",gray:"#808080",green:"#008000",greenyellow:"#adff2f",honeydew:"#f0fff0",hotpink:"#ff69b4","indianred ":"#cd5c5c","indigo ":"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",lavender:"#e6e6fa",lavenderblush:"#fff0f5",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrodyellow:"#fafad2",lightgrey:"#d3d3d3",lightgreen:"#90ee90",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370d8",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#d87093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",snow:"#fffafa",springgreen:"#00ff7f",steelblue:"#4682b4",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",tomato:"#ff6347",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32"},_sanitizeNumber:function(E){return"number"==typeof E?E:isNaN(E)||null===E||""===E||void 0===E?1:void 0!==E.toLowerCase?parseFloat(E):1},setColor:function(E){E=E.toLowerCase(),this.value=this.stringToHSB(E)||{h:0,s:0,b:0,a:1}},stringToHSB:function(F){F=F.toLowerCase();var G=this,E=!1;return D.each(this.stringParsers,function(K,J){var L=J.re.exec(F),H=L&&J.parse.apply(G,[L]),I=J.format||"rgba";return H?(E=I.match(/hsla?/)?G.RGBtoHSB.apply(G,G.HSLtoRGB.apply(G,H)):G.RGBtoHSB.apply(G,H),G.origFormat=I,!1):!0}),E},setHue:function(E){this.value.h=1-E},setSaturation:function(E){this.value.s=E},setBrightness:function(E){this.value.b=1-E},setAlpha:function(E){this.value.a=parseInt(100*(1-E),10)/100},toRGB:function(J,H,I,M){J||(J=this.value.h,H=this.value.s,I=this.value.b),J*=360;var E,K,L,F,G;return J=J%360/60,G=I*H,F=G*(1-Math.abs(J%2-1)),E=K=L=I-G,J=~~J,E+=[G,F,0,0,F,G][J],K+=[F,G,G,F,0,0][J],L+=[0,0,F,G,G,F][J],{r:Math.round(255*E),g:Math.round(255*K),b:Math.round(255*L),a:M||this.value.a}},toHex:function(I,G,H,E){var F=this.toRGB(I,G,H,E);return"#"+(1<<24|parseInt(F.r)<<16|parseInt(F.g)<<8|parseInt(F.b)).toString(16).substr(1)},toHSL:function(J,H,I,F){J=J||this.value.h,H=H||this.value.s,I=I||this.value.b,F=F||this.value.a;var G=J,K=(2-H)*I,E=H*I;return E/=K>0&&1>=K?K:2-K,K/=2,E>1&&(E=1),{h:isNaN(G)?0:G,s:isNaN(E)?0:E,l:isNaN(K)?0:K,a:isNaN(F)?0:F}},toAlias:function(I,G,H,E){var F=this.toHex(I,G,H,E);for(var J in this.colors){if(this.colors[J]==F){return J}}return !1},RGBtoHSB:function(I,G,H,L){I/=255,G/=255,H/=255;var E,J,K,F;return K=Math.max(I,G,H),F=K-Math.min(I,G,H),E=0===F?null:K===I?(G-H)/F:K===G?(H-I)/F+2:(I-G)/F+4,E=(E+360)%6*60/360,J=0===F?0:F/K,{h:this._sanitizeNumber(E),s:J,b:K,a:this._sanitizeNumber(L)}},HueToRGB:function(G,E,F){return 0>F?F+=1:F>1&&(F-=1),1>6*F?G+(E-G)*F*6:1>2*F?E:2>3*F?G+(E-G)*(2/3-F)*6:G},HSLtoRGB:function(M,K,L,P){0>K&&(K=0);var E;E=0.5>=L?L*(1+K):L+K-L*K;var N=2*L-E,O=M+1/3,H=M,I=M-1/3,F=Math.round(255*this.HueToRGB(N,E,O)),G=Math.round(255*this.HueToRGB(N,E,H)),J=Math.round(255*this.HueToRGB(N,E,I));return[F,G,J,this._sanitizeNumber(P)]},toString:function(G){switch(G=G||"rgba"){case"rgb":var E=this.toRGB();return"rgb("+E.r+","+E.g+","+E.b+")";case"rgba":var E=this.toRGB();return"rgba("+E.r+","+E.g+","+E.b+","+E.a+")";case"hsl":var F=this.toHSL();return"hsl("+Math.round(360*F.h)+","+Math.round(100*F.s)+"%,"+Math.round(100*F.l)+"%)";case"hsla":var F=this.toHSL();return"hsla("+Math.round(360*F.h)+","+Math.round(100*F.s)+"%,"+Math.round(100*F.l)+"%,"+F.a+")";case"hex":return this.toHex();case"alias":return this.toAlias()||this.toHex();default:return !1}},stringParsers:[{re:/#([a-fA-F0-9]{2})([a-fA-F0-9]{2})([a-fA-F0-9]{2})/,format:"hex",parse:function(E){return[parseInt(E[1],16),parseInt(E[2],16),parseInt(E[3],16),1]}},{re:/#([a-fA-F0-9])([a-fA-F0-9])([a-fA-F0-9])/,format:"hex",parse:function(E){return[parseInt(E[1]+E[1],16),parseInt(E[2]+E[2],16),parseInt(E[3]+E[3],16),1]}},{re:/rgb\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*?\)/,format:"rgb",parse:function(E){return[E[1],E[2],E[3],1]}},{re:/rgb\(\s*(\d+(?:\.\d+)?)\%\s*,\s*(\d+(?:\.\d+)?)\%\s*,\s*(\d+(?:\.\d+)?)\%\s*?\)/,format:"rgb",parse:function(E){return[2.55*E[1],2.55*E[2],2.55*E[3],1]}},{re:/rgba\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*(?:,\s*(\d+(?:\.\d+)?)\s*)?\)/,format:"rgba",parse:function(E){return[E[1],E[2],E[3],E[4]]}},{re:/rgba\(\s*(\d+(?:\.\d+)?)\%\s*,\s*(\d+(?:\.\d+)?)\%\s*,\s*(\d+(?:\.\d+)?)\%\s*(?:,\s*(\d+(?:\.\d+)?)\s*)?\)/,format:"rgba",parse:function(E){return[2.55*E[1],2.55*E[2],2.55*E[3],E[4]]}},{re:/hsl\(\s*(\d+(?:\.\d+)?)\s*,\s*(\d+(?:\.\d+)?)\%\s*,\s*(\d+(?:\.\d+)?)\%\s*?\)/,format:"hsl",parse:function(E){return[E[1]/360,E[2]/100,E[3]/100,E[4]]}},{re:/hsla\(\s*(\d+(?:\.\d+)?)\s*,\s*(\d+(?:\.\d+)?)\%\s*,\s*(\d+(?:\.\d+)?)\%\s*(?:,\s*(\d+(?:\.\d+)?)\s*)?\)/,format:"hsla",parse:function(E){return[E[1]/360,E[2]/100,E[3]/100,E[4]]}},{re:/^([a-z]{3,})$/,format:"alias",parse:function(H){var F=this.colorNameToHex(H[0])||"#000000",G=this.stringParsers[0].re.exec(F),E=G&&this.stringParsers[0].parse.apply(this,[G]);return E}}],colorNameToHex:function(E){return"undefined"!=typeof this.colors[E.toLowerCase()]?this.colors[E.toLowerCase()]:!1}};var C={horizontal:!1,inline:!1,color:!1,format:!1,input:"input",container:!1,component:".add-on, .input-group-addon",sliders:{saturation:{maxLeft:100,maxTop:100,callLeft:"setSaturation",callTop:"setBrightness"},hue:{maxLeft:0,maxTop:100,callLeft:!1,callTop:"setHue"},alpha:{maxLeft:0,maxTop:100,callLeft:!1,callTop:"setAlpha"}},slidersHorz:{saturation:{maxLeft:100,maxTop:100,callLeft:"setSaturation",callTop:"setBrightness"},hue:{maxLeft:100,maxTop:0,callLeft:"setHue",callTop:!1},alpha:{maxLeft:100,maxTop:0,callLeft:"setAlpha",callTop:!1}},template:'<div class="colorpicker dropdown-menu"><div class="colorpicker-saturation"><i><b></b></i></div><div class="colorpicker-hue"><i></i></div><div class="colorpicker-alpha"><i></i></div><div class="colorpicker-color"><div /></div></div>'},A=function(E,F){this.element=D(E).addClass("colorpicker-element"),this.options=D.extend({},C,this.element.data(),F),this.component=this.options.component,this.component=this.component!==!1?this.element.find(this.component):!1,this.component&&0===this.component.length&&(this.component=!1),this.container=this.options.container===!0?this.element:this.options.container,this.container=this.container!==!1?D(this.container):!1,this.input=this.element.is("input")?this.element:this.options.input?this.element.find(this.options.input):!1,this.input&&0===this.input.length&&(this.input=!1),this.color=new B(this.options.color!==!1?this.options.color:this.getValue()),this.format=this.options.format!==!1?this.options.format:this.color.origFormat,this.picker=D(this.options.template),this.picker.addClass(this.options.inline?"colorpicker-inline colorpicker-visible":"colorpicker-hidden"),this.options.horizontal&&this.picker.addClass("colorpicker-horizontal"),("rgba"===this.format||"hsla"===this.format)&&this.picker.addClass("colorpicker-with-alpha"),this.picker.on("mousedown.colorpicker",D.proxy(this.mousedown,this)),this.picker.appendTo(this.container?this.container:D("body")),this.input!==!1&&(this.input.on({"keyup.colorpicker":D.proxy(this.keyup,this)}),this.component===!1&&this.element.on({"focus.colorpicker":D.proxy(this.show,this)}),this.options.inline===!1&&this.element.on({"focusout.colorpicker":D.proxy(this.hide,this)})),this.component!==!1&&this.component.on({"click.colorpicker":D.proxy(this.show,this)}),this.input===!1&&this.component===!1&&this.element.on({"click.colorpicker":D.proxy(this.show,this)}),this.update(),D(D.proxy(function(){this.element.trigger("create")},this))};A.version="2.0.0-beta",A.Color=B,A.prototype={constructor:A,destroy:function(){this.picker.remove(),this.element.removeData("colorpicker").off(".colorpicker"),this.input!==!1&&this.input.off(".colorpicker"),this.component!==!1&&this.component.off(".colorpicker"),this.element.removeClass("colorpicker-element"),this.element.trigger({type:"destroy"})},reposition:function(){if(this.options.inline!==!1){return !1}var F=this.container&&this.container[0]!==document.body?"position":"offset",E=this.component?this.component[F]():this.element[F]();this.picker.css({top:E.top+(this.component?this.component.outerHeight():this.element.outerHeight()),left:E.left})},show:function(E){return this.isDisabled()?!1:(this.picker.addClass("colorpicker-visible").removeClass("colorpicker-hidden"),this.reposition(),D(window).on("resize.colorpicker",D.proxy(this.reposition,this)),!this.hasInput()&&E&&E.stopPropagation&&E.preventDefault&&(E.stopPropagation(),E.preventDefault()),this.options.inline===!1&&D(window.document).on({"mousedown.colorpicker":D.proxy(this.hide,this)}),void this.element.trigger({type:"showPicker",color:this.color}))},hide:function(){this.picker.addClass("colorpicker-hidden").removeClass("colorpicker-visible"),D(window).off("resize.colorpicker",this.reposition),D(document).off({"mousedown.colorpicker":this.hide}),this.update(),this.element.trigger({type:"hidePicker",color:this.color})},updateData:function(E){return E=E||this.color.toString(this.format),this.element.data("color",E),E},updateInput:function(E){return E=E||this.color.toString(this.format),this.input!==!1&&this.input.prop("value",E),E},updatePicker:function(G){void 0!==G&&(this.color=new B(G));var F=this.options.horizontal===!1?this.options.sliders:this.options.slidersHorz,E=this.picker.find("i");return 0!==E.length?(this.options.horizontal===!1?(F=this.options.sliders,E.eq(1).css("top",F.hue.maxTop*(1-this.color.value.h)).end().eq(2).css("top",F.alpha.maxTop*(1-this.color.value.a))):(F=this.options.slidersHorz,E.eq(1).css("left",F.hue.maxLeft*(1-this.color.value.h)).end().eq(2).css("left",F.alpha.maxLeft*(1-this.color.value.a))),E.eq(0).css({top:F.saturation.maxTop-this.color.value.b*F.saturation.maxTop,left:this.color.value.s*F.saturation.maxLeft}),this.picker.find(".colorpicker-saturation").css("backgroundColor",this.color.toHex(this.color.value.h,1,1,1)),this.picker.find(".colorpicker-alpha").css("backgroundColor",this.color.toHex()),this.picker.find(".colorpicker-color, .colorpicker-color div").css("backgroundColor",this.color.toString(this.format)),G):void 0},updateComponent:function(F){if(F=F||this.color.toString(this.format),this.component!==!1){var E=this.component.find("i").eq(0);E.length>0?E.css({backgroundColor:F}):this.component.css({backgroundColor:F})}return F},update:function(F){var E=this.updateComponent();return(this.getValue(!1)!==!1||F===!0)&&(this.updateInput(E),this.updateData(E)),this.updatePicker(),E},setValue:function(E){this.color=new B(E),this.update(),this.element.trigger({type:"changeColor",color:this.color,value:E})},getValue:function(F){F=void 0===F?"#000000":F;var E;return E=this.hasInput()?this.input.val():this.element.data("color"),(void 0===E||""===E||null===E)&&(E=F),E},hasInput:function(){return this.input!==!1},isDisabled:function(){return this.hasInput()?this.input.prop("disabled")===!0:!1},disable:function(){return this.hasInput()?(this.input.prop("disabled",!0),!0):!1},enable:function(){return this.hasInput()?(this.input.prop("disabled",!1),!0):!1},currentSlider:null,mousePointer:{left:0,top:0},mousedown:function(G){G.stopPropagation(),G.preventDefault();var H=D(G.target),E=H.closest("div"),F=this.options.horizontal?this.options.slidersHorz:this.options.sliders;if(!E.is(".colorpicker")){if(E.is(".colorpicker-saturation")){this.currentSlider=D.extend({},F.saturation)}else{if(E.is(".colorpicker-hue")){this.currentSlider=D.extend({},F.hue)}else{if(!E.is(".colorpicker-alpha")){return !1}this.currentSlider=D.extend({},F.alpha)}}var I=E.offset();this.currentSlider.guide=E.find("i")[0].style,this.currentSlider.left=G.pageX-I.left,this.currentSlider.top=G.pageY-I.top,this.mousePointer={left:G.pageX,top:G.pageY},D(document).on({"mousemove.colorpicker":D.proxy(this.mousemove,this),"mouseup.colorpicker":D.proxy(this.mouseup,this)}).trigger("mousemove")}return !1},mousemove:function(G){G.stopPropagation(),G.preventDefault();var E=Math.max(0,Math.min(this.currentSlider.maxLeft,this.currentSlider.left+((G.pageX||this.mousePointer.left)-this.mousePointer.left))),F=Math.max(0,Math.min(this.currentSlider.maxTop,this.currentSlider.top+((G.pageY||this.mousePointer.top)-this.mousePointer.top)));return this.currentSlider.guide.left=E+"px",this.currentSlider.guide.top=F+"px",this.currentSlider.callLeft&&this.color[this.currentSlider.callLeft].call(this.color,E/100),this.currentSlider.callTop&&this.color[this.currentSlider.callTop].call(this.color,F/100),this.update(!0),this.element.trigger({type:"changeColor",color:this.color}),!1},mouseup:function(E){return E.stopPropagation(),E.preventDefault(),D(document).off({"mousemove.colorpicker":this.mousemove,"mouseup.colorpicker":this.mouseup}),!1},keyup:function(F){if(38===F.keyCode){this.color.value.a<1&&(this.color.value.a=Math.round(100*(this.color.value.a+0.01))/100),this.update(!0)}else{if(40===F.keyCode){this.color.value.a>0&&(this.color.value.a=Math.round(100*(this.color.value.a-0.01))/100),this.update(!0)}else{var E=this.input.val();this.color=new B(E),this.getValue(!1)!==!1&&(this.updateData(),this.updateComponent(),this.updatePicker())}}this.element.trigger({type:"changeColor",color:this.color,value:E})}},D.colorpicker=A,D.fn.colorpicker=function(E){var F=arguments;return this.each(function(){var H=D(this),I=H.data("colorpicker"),G="object"==typeof E?E:{};I||"string"==typeof E?"string"==typeof E&&I[E].apply(I,Array.prototype.slice.call(F,1)):H.data("colorpicker",new A(this,G))})},D.fn.colorpicker.constructor=A});