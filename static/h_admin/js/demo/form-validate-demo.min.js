$.validator.setDefaults({highlight:function(A){$(A).closest(".form-group").removeClass("has-success").addClass("has-error")},success:function(A){A.closest(".form-group").removeClass("has-error").addClass("has-success")},errorElement:"span",errorPlacement:function(B,A){B.appendTo(A.is(":radio")||A.is(":checkbox")?A.parent().parent().parent():A.parent())},errorClass:"help-block m-b-none",validClass:"help-block m-b-none"}),$().ready(function(){$("#commentForm").validate();var A="<i class='fa fa-times-circle'></i> ";$("#signupForm").validate({rules:{firstname:"required",lastname:"required",username:{required:!0,minlength:2},password:{required:!0,minlength:5},confirm_password:{required:!0,minlength:5,equalTo:"#password"},email:{required:!0,email:!0},topic:{required:"#newsletter:checked",minlength:2},agree:"required"},messages:{firstname:A+"请输入你的姓",lastname:A+"请输入您的名字",username:{required:A+"请输入您的用户名",minlength:A+"用户名必须两个字符以上"},password:{required:A+"请输入您的密码",minlength:A+"密码必须5个字符以上"},confirm_password:{required:A+"请再次输入密码",minlength:A+"密码必须5个字符以上",equalTo:A+"两次输入的密码不一致"},email:A+"请输入您的E-mail",agree:{required:A+"必须同意协议后才能注册",element:"#agree-error"}}}),$("#username").focus(function(){var C=$("#firstname").val(),B=$("#lastname").val();C&&B&&!this.value&&(this.value=C+"."+B)})});