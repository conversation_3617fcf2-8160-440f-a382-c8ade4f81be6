(function(){var A=window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.oRequestAnimationFrame||window.msRequestAnimationFrame||function(G){window.setTimeout(G,1000/60)};var C=(function(){var J={};var I=document.createElement("div").style;var G=(function(){var O=["t","webkitT","MozT","msT","OT"],P,M=0,N=O.length;for(;M<N;M++){P=O[M]+"ransform";if(P in I){return O[M].substr(0,O[M].length-1)}}return false})();function L(M){if(G===false){return false}if(G===""){return M}return G+M.charAt(0).toUpperCase()+M.substr(1)}J.getTime=Date.now||function H(){return new Date().getTime()};J.extend=function(O,N){for(var M in N){O[M]=N[M]}};J.addEvent=function(M,P,O,N){M.addEventListener(P,O,!!N)};J.removeEvent=function(M,P,O,N){M.removeEventListener(P,O,!!N)};J.prefixPointerEvent=function(M){return window.MSPointerEvent?"MSPointer"+M.charAt(7).toUpperCase()+M.substr(8):M};J.momentum=function(P,O,N,R,V,U){var S=P-O,Q=Math.abs(S)/N,T,M;U=U===undefined?0.0006:U;T=P+(Q*Q)/(2*U)*(S<0?-1:1);M=Q/U;if(T<R){T=V?R-(V/2.5*(Q/8)):R;S=Math.abs(T-P);M=S/Q}else{if(T>0){T=V?V/2.5*(Q/8):0;S=Math.abs(P)+T;M=S/Q}}return{destination:Math.round(T),duration:M}};var K=L("transform");J.extend(J,{hasTransform:K!==false,hasPerspective:L("perspective") in I,hasTouch:"ontouchstart" in window,hasPointer:!!(window.PointerEvent||window.MSPointerEvent),hasTransition:L("transition") in I});J.isBadAndroid=(function(){var M=window.navigator.appVersion;if(/Android/.test(M)&&!(/Chrome\/\d/.test(M))){var N=M.match(/Safari\/(\d+.\d)/);if(N&&typeof N==="object"&&N.length>=2){return parseFloat(N[1])<535.19}else{return true}}else{return false}})();J.extend(J.style={},{transform:K,transitionTimingFunction:L("transitionTimingFunction"),transitionDuration:L("transitionDuration"),transitionDelay:L("transitionDelay"),transformOrigin:L("transformOrigin")});J.hasClass=function(N,O){var M=new RegExp("(^|\\s)"+O+"(\\s|$)");return M.test(N.className)};J.addClass=function(M,N){if(J.hasClass(M,N)){return}var O=M.className.split(" ");O.push(N);M.className=O.join(" ")};J.removeClass=function(N,O){if(!J.hasClass(N,O)){return}var M=new RegExp("(^|\\s)"+O+"(\\s|$)","g");N.className=N.className.replace(M," ")};J.offset=function(M){var N=-M.offsetLeft,O=-M.offsetTop;while(M=M.offsetParent){N-=M.offsetLeft;O-=M.offsetTop}return{left:N,top:O}};J.preventDefaultException=function(M,O){for(var N in O){if(O[N].test(M[N])){return true}}return false};J.extend(J.eventType={},{touchstart:1,touchmove:1,touchend:1,mousedown:2,mousemove:2,mouseup:2,pointerdown:3,pointermove:3,pointerup:3,MSPointerDown:3,MSPointerMove:3,MSPointerUp:3});J.extend(J.ease={},{quadratic:{style:"cubic-bezier(0.25, 0.46, 0.45, 0.94)",fn:function(M){return M*(2-M)}},circular:{style:"cubic-bezier(0.1, 0.57, 0.1, 1)",fn:function(M){return Math.sqrt(1-(--M*M))}},back:{style:"cubic-bezier(0.175, 0.885, 0.32, 1.275)",fn:function(N){var M=4;return(N=N-1)*N*((M+1)*N+M)+1}},bounce:{style:"",fn:function(M){if((M/=1)<(1/2.75)){return 7.5625*M*M}else{if(M<(2/2.75)){return 7.5625*(M-=(1.5/2.75))*M+0.75}else{if(M<(2.5/2.75)){return 7.5625*(M-=(2.25/2.75))*M+0.9375}else{return 7.5625*(M-=(2.625/2.75))*M+0.984375}}}}},elastic:{style:"",fn:function(O){var M=0.22,N=0.4;if(O===0){return 0}if(O==1){return 1}return(N*Math.pow(2,-10*O)*Math.sin((O-M/4)*(2*Math.PI)/M)+1)}}});J.tap=function(M,N){var O=document.createEvent("Event");O.initEvent(N,true,true);O.pageX=M.pageX;O.pageY=M.pageY;M.target.dispatchEvent(O)};J.click=function(M){var N=M.target,O;if(!(/(SELECT|INPUT|TEXTAREA)/i).test(N.tagName)){O=document.createEvent(window.MouseEvent?"MouseEvents":"Event");O.initEvent("click",true,true);O.view=M.view||window;O.detail=1;O.screenX=N.screenX||0;O.screenY=N.screenY||0;O.clientX=N.clientX||0;O.clientY=N.clientY||0;O.ctrlKey=!!M.ctrlKey;O.altKey=!!M.altKey;O.shiftKey=!!M.shiftKey;O.metaKey=!!M.metaKey;O.button=0;O.relatedTarget=null;O._constructed=true;N.dispatchEvent(O)}};return J})();function B(G,I){this.wrapper=typeof G=="string"?document.querySelector(G):G;this.scroller=this.wrapper.children[0];this.scrollerStyle=this.scroller.style;this.options={disablePointer:true,disableTouch:!C.hasTouch,disableMouse:C.hasTouch,startX:0,startY:0,scrollY:true,directionLockThreshold:5,momentum:true,bounce:true,bounceTime:600,bounceEasing:"",preventDefault:true,preventDefaultException:{tagName:/^(INPUT|TEXTAREA|BUTTON|SELECT)$/},HWCompositing:true,useTransition:true,useTransform:true,bindToWrapper:typeof window.onmousedown==="undefined"};for(var H in I){this.options[H]=I[H]}this.translateZ=this.options.HWCompositing&&C.hasPerspective?" translateZ(0)":"";this.options.useTransition=C.hasTransition&&this.options.useTransition;this.options.useTransform=C.hasTransform&&this.options.useTransform;this.options.eventPassthrough=this.options.eventPassthrough===true?"vertical":this.options.eventPassthrough;this.options.preventDefault=!this.options.eventPassthrough&&this.options.preventDefault;this.options.scrollY=this.options.eventPassthrough=="vertical"?false:this.options.scrollY;this.options.scrollX=this.options.eventPassthrough=="horizontal"?false:this.options.scrollX;this.options.freeScroll=this.options.freeScroll&&!this.options.eventPassthrough;this.options.directionLockThreshold=this.options.eventPassthrough?0:this.options.directionLockThreshold;this.options.bounceEasing=typeof this.options.bounceEasing=="string"?C.ease[this.options.bounceEasing]||C.ease.circular:this.options.bounceEasing;this.options.resizePolling=this.options.resizePolling===undefined?60:this.options.resizePolling;if(this.options.tap===true){this.options.tap="tap"}if(!this.options.useTransition&&!this.options.useTransform){if(!(/relative|absolute/i).test(this.scrollerStyle.position)){this.scrollerStyle.position="relative"}}if(this.options.shrinkScrollbars=="scale"){this.options.useTransition=false}this.options.invertWheelDirection=this.options.invertWheelDirection?-1:1;if(this.options.probeType==3){this.options.useTransition=false}this.x=0;this.y=0;this.directionX=0;this.directionY=0;this._events={};this._init();this.refresh();this.scrollTo(this.options.startX,this.options.startY);this.enable()}B.prototype={version:"1.0.0",_init:function(){this._initEvents()},destroy:function(){this._initEvents(true);clearTimeout(this.resizeTimeout);this.resizeTimeout=null;this._execEvent("destroy")},_transitionEnd:function(G){if(G.target!=this.scroller||!this.isInTransition){return}this._transitionTime();if(!this.resetPosition(this.options.bounceTime)){this.isInTransition=false;this._execEvent("scrollEnd")}},_start:function(H){if(C.eventType[H.type]!=1){var G;if(!H.which){G=(H.button<2)?0:((H.button==4)?1:2)}else{G=H.button}if(G!==0){return}}if(!this.enabled||(this.initiated&&C.eventType[H.type]!==this.initiated)){return}if(this.options.preventDefault&&!C.isBadAndroid&&!C.preventDefaultException(H.target,this.options.preventDefaultException)){H.preventDefault()}var I=H.touches?H.touches[0]:H,J;this.initiated=C.eventType[H.type];this.moved=false;this.distX=0;this.distY=0;this.directionX=0;this.directionY=0;this.directionLocked=0;this.startTime=C.getTime();if(this.options.useTransition&&this.isInTransition){this._transitionTime();this.isInTransition=false;J=this.getComputedPosition();this._translate(Math.round(J.x),Math.round(J.y));this._execEvent("scrollEnd")}else{if(!this.options.useTransition&&this.isAnimating){this.isAnimating=false;this._execEvent("scrollEnd")}}this.startX=this.x;this.startY=this.y;this.absStartX=this.x;this.absStartY=this.y;this.pointX=I.pageX;this.pointY=I.pageY;this._execEvent("beforeScrollStart")},_move:function(G){if(!this.enabled||C.eventType[G.type]!==this.initiated){return}if(this.options.preventDefault){}var I=G.touches?G.touches[0]:G,N=I.pageX-this.pointX,H=I.pageY-this.pointY,M=C.getTime(),O,L,K,J;this.pointX=I.pageX;this.pointY=I.pageY;this.distX+=N;this.distY+=H;K=Math.abs(this.distX);J=Math.abs(this.distY);if(M-this.endTime>300&&(K<10&&J<10)){return}if(!this.directionLocked&&!this.options.freeScroll){if(K>J+this.options.directionLockThreshold){this.directionLocked="h"}else{if(J>=K+this.options.directionLockThreshold){this.directionLocked="v"}else{this.directionLocked="n"}}}if(this.directionLocked=="h"){if(this.options.eventPassthrough=="vertical"){G.preventDefault()}else{if(this.options.eventPassthrough=="horizontal"){this.initiated=false;return}}H=0}else{if(this.directionLocked=="v"){if(this.options.eventPassthrough=="horizontal"){G.preventDefault()}else{if(this.options.eventPassthrough=="vertical"){this.initiated=false;return}}N=0}}N=this.hasHorizontalScroll?N:0;H=this.hasVerticalScroll?H:0;O=this.x+N;L=this.y+H;if(O>0||O<this.maxScrollX){O=this.options.bounce?this.x+N/3:O>0?0:this.maxScrollX}if(L>0||L<this.maxScrollY){L=this.options.bounce?this.y+H/3:L>0?0:this.maxScrollY}this.directionX=N>0?-1:N<0?1:0;this.directionY=H>0?-1:H<0?1:0;if(!this.moved){this._execEvent("scrollStart")}this.moved=true;this._translate(O,L);if(M-this.startTime>300){this.startTime=M;this.startX=this.x;this.startY=this.y;if(this.options.probeType==1){this._execEvent("scroll")}}if(this.options.probeType>1){this._execEvent("scroll")}},_end:function(K){if(!this.enabled||C.eventType[K.type]!==this.initiated){return}if(this.options.preventDefault&&!C.preventDefaultException(K.target,this.options.preventDefaultException)){K.preventDefault()}var M=K.changedTouches?K.changedTouches[0]:K,G,P,N=C.getTime()-this.startTime,R=Math.round(this.x),Q=Math.round(this.y),L=Math.abs(R-this.startX),J=Math.abs(Q-this.startY),H=0,O="";this.isInTransition=0;this.initiated=0;this.endTime=C.getTime();if(this.resetPosition(this.options.bounceTime)){return}this.scrollTo(R,Q);if(!this.moved){if(this.options.tap){C.tap(K,this.options.tap)}if(this.options.click){C.click(K)}this._execEvent("scrollCancel");return}if(this._events.flick&&N<200&&L<100&&J<100){this._execEvent("flick");return}if(this.options.momentum&&N<300){G=this.hasHorizontalScroll?C.momentum(this.x,this.startX,N,this.maxScrollX,this.options.bounce?this.wrapperWidth:0,this.options.deceleration):{destination:R,duration:0};P=this.hasVerticalScroll?C.momentum(this.y,this.startY,N,this.maxScrollY,this.options.bounce?this.wrapperHeight:0,this.options.deceleration):{destination:Q,duration:0};R=G.destination;Q=P.destination;H=Math.max(G.duration,P.duration);this.isInTransition=1}if(this.options.snap){var I=this._nearestSnap(R,Q);this.currentPage=I;H=this.options.snapSpeed||Math.max(Math.max(Math.min(Math.abs(R-I.x),1000),Math.min(Math.abs(Q-I.y),1000)),300);R=I.x;Q=I.y;this.directionX=0;this.directionY=0;O=this.options.bounceEasing}if(R!=this.x||Q!=this.y){if(R>0||R<this.maxScrollX||Q>0||Q<this.maxScrollY){O=C.ease.quadratic}this.scrollTo(R,Q,H,O);return}this._execEvent("scrollEnd")},_resize:function(){var G=this;clearTimeout(this.resizeTimeout);this.resizeTimeout=setTimeout(function(){G.refresh()},this.options.resizePolling)},resetPosition:function(G){var H=this.x,I=this.y;G=G||0;if(!this.hasHorizontalScroll||this.x>0){H=0}else{if(this.x<this.maxScrollX){H=this.maxScrollX}}if(!this.hasVerticalScroll||this.y>0){I=0}else{if(this.y<this.maxScrollY){I=this.maxScrollY}}if(H==this.x&&I==this.y){return false}this.scrollTo(H,I,G,this.options.bounceEasing);return true},disable:function(){this.enabled=false},enable:function(){this.enabled=true},refresh:function(){var G=this.wrapper.offsetHeight;this.wrapperWidth=this.wrapper.clientWidth;this.wrapperHeight=this.wrapper.clientHeight;this.scrollerWidth=this.scroller.offsetWidth;this.scrollerHeight=this.scroller.offsetHeight;this.maxScrollX=this.wrapperWidth-this.scrollerWidth;this.maxScrollY=this.wrapperHeight-this.scrollerHeight;this.hasHorizontalScroll=this.options.scrollX&&this.maxScrollX<0;this.hasVerticalScroll=this.options.scrollY&&this.maxScrollY<0;if(!this.hasHorizontalScroll){this.maxScrollX=0;this.scrollerWidth=this.wrapperWidth}if(!this.hasVerticalScroll){this.maxScrollY=0;this.scrollerHeight=this.wrapperHeight}this.endTime=0;this.directionX=0;this.directionY=0;this.wrapperOffset=C.offset(this.wrapper);this._execEvent("refresh");this.resetPosition()},on:function(G,H){if(!this._events[G]){this._events[G]=[]}this._events[G].push(H)},off:function(G,H){if(!this._events[G]){return}var I=this._events[G].indexOf(H);if(I>-1){this._events[G].splice(I,1)}},_execEvent:function(G){if(!this._events[G]){return}var H=0,I=this._events[G].length;if(!I){return}for(;H<I;H++){this._events[G][H].apply(this,[].slice.call(arguments,1))}},scrollTo:function(I,J,K,H){H=H||C.ease.circular;this.isInTransition=this.options.useTransition&&K>0;var G=this.options.useTransition&&H.style;if(!K||G){if(G){this._transitionTimingFunction(H.style);this._transitionTime(K)}this._translate(I,J)}else{this._animate(I,J,K,H.fn)}},scrollToElement:function(G,K,H,I,J){G=G.nodeType?G:this.scroller.querySelector(G);if(!G){return}var L=C.offset(G);L.left-=this.wrapperOffset.left;L.top-=this.wrapperOffset.top;if(H===true){H=Math.round(G.offsetWidth/2-this.wrapper.offsetWidth/2)}if(I===true){I=Math.round(G.offsetHeight/2-this.wrapper.offsetHeight/2)}L.left-=H||0;L.top-=I||0;L.left=L.left>0?0:L.left<this.maxScrollX?this.maxScrollX:L.left;L.top=L.top>0?0:L.top<this.maxScrollY?this.maxScrollY:L.top;K=K===undefined||K===null||K==="auto"?Math.max(Math.abs(this.x-L.left),Math.abs(this.y-L.top)):K;this.scrollTo(L.left,L.top,K,J)},_transitionTime:function(H){if(!this.options.useTransition){return}H=H||0;var G=C.style.transitionDuration;if(!G){return}this.scrollerStyle[G]=H+"ms";if(!H&&C.isBadAndroid){this.scrollerStyle[G]="0.0001ms";var I=this;A(function(){if(I.scrollerStyle[G]==="0.0001ms"){I.scrollerStyle[G]="0s"}})}},_transitionTimingFunction:function(G){this.scrollerStyle[C.style.transitionTimingFunction]=G},_translate:function(G,H){if(this.options.useTransform){this.scrollerStyle[C.style.transform]="translate("+G+"px,"+H+"px)"+this.translateZ}else{G=Math.round(G);H=Math.round(H);this.scrollerStyle.left=G+"px";this.scrollerStyle.top=H+"px"}this.x=G;this.y=H},_initEvents:function(G){var H=G?C.removeEvent:C.addEvent,I=this.options.bindToWrapper?this.wrapper:window;H(window,"orientationchange",this);H(window,"resize",this);if(this.options.click){H(this.wrapper,"click",this,true)}if(!this.options.disableMouse){H(this.wrapper,"mousedown",this);H(I,"mousemove",this);H(I,"mousecancel",this);H(I,"mouseup",this)}if(C.hasPointer&&!this.options.disablePointer){H(this.wrapper,C.prefixPointerEvent("pointerdown"),this);H(I,C.prefixPointerEvent("pointermove"),this);H(I,C.prefixPointerEvent("pointercancel"),this);H(I,C.prefixPointerEvent("pointerup"),this)}if(C.hasTouch&&!this.options.disableTouch){H(this.wrapper,"touchstart",this);H(I,"touchmove",this);H(I,"touchcancel",this);H(I,"touchend",this)}H(this.scroller,"transitionend",this);H(this.scroller,"webkitTransitionEnd",this);H(this.scroller,"oTransitionEnd",this);H(this.scroller,"MSTransitionEnd",this)},getComputedPosition:function(){var G=window.getComputedStyle(this.scroller,null),H,I;if(this.options.useTransform){G=G[C.style.transform].split(")")[0].split(", ");H=+(G[12]||G[4]);I=+(G[13]||G[5])}else{H=+G.left.replace(/[^-\d.]/g,"");I=+G.top.replace(/[^-\d.]/g,"")}return{x:H,y:I}},_animate:function(H,G,J,O){var L=this,K=this.x,M=this.y,P=C.getTime(),N=P+J;function I(){var T=C.getTime(),Q,S,R;if(T>=N){L.isAnimating=false;L._translate(H,G);if(!L.resetPosition(L.options.bounceTime)){L._execEvent("scrollEnd")}return}T=(T-P)/J;R=O(T);Q=(H-K)*R+K;S=(G-M)*R+M;L._translate(Q,S);if(L.isAnimating){A(I)}if(L.options.probeType==3){L._execEvent("scroll")}}this.isAnimating=true;I()},handleEvent:function(G){switch(G.type){case"touchstart":case"pointerdown":case"MSPointerDown":case"mousedown":this._start(G);break;case"touchmove":case"pointermove":case"MSPointerMove":case"mousemove":this._move(G);break;case"touchend":case"pointerup":case"MSPointerUp":case"mouseup":case"touchcancel":case"pointercancel":case"MSPointerCancel":case"mousecancel":this._end(G);break;case"orientationchange":case"resize":this._resize();break;case"transitionend":case"webkitTransitionEnd":case"oTransitionEnd":case"MSTransitionEnd":this._transitionEnd(G);break;case"click":if(this.enabled&&!G._constructed){G.preventDefault();G.stopPropagation()}break}}};B.utils=C;var F={isArray:function(G){return Object.prototype.toString.call(G)==="[object Array]"},isFunction:function(G){return typeof G==="function"},attrToData:function(G,J){var H={};for(var I in G.dataset){H[I]=G.dataset[I]}H["dom"]=G;H["atindex"]=J;return H},attrToHtml:function(H){var G="";for(var I in H){G+="data-"+I+'="'+H[I]+'"'}return G}};function E(G,H){if(!(this instanceof E)){return new E(G,H)}this.html=G;this.opts=H;var I=document.createElement("div");I.className="olay";var J=document.createElement("div");J.className="layer";this.el=I;this.layer_el=J;this.init()}E.prototype={init:function(){this.layer_el.innerHTML=this.html;if(this.opts.container&&document.querySelector(this.opts.container)){document.querySelector(this.opts.container).appendChild(this.el)}else{document.body.appendChild(this.el)}this.el.appendChild(this.layer_el);this.el.style.height=Math.max(document.documentElement.getBoundingClientRect().height,window.innerHeight);if(this.opts.className){this.el.className+=" "+this.opts.className}this.bindEvent()},bindEvent:function(){var I=this.el.querySelectorAll(".sure");var G=this.el.querySelectorAll(".close");var H=this;this.el.addEventListener("click",function(J){H.close();H.opts.maskCallback&&H.opts.maskCallback()});this.layer_el.addEventListener("click",function(J){J.stopPropagation()});Array.prototype.slice.call(I).forEach(function(J,K){J.addEventListener("click",function(){H.close()})});Array.prototype.slice.call(G).forEach(function(J,K){J.addEventListener("click",function(){H.close();H.opts.fallback&&H.opts.fallback()})})},close:function(){var G=this;if(G.el){if(G.opts.showAnimate){G.el.className+=" fadeOutDown";setTimeout(function(){G.removeDom()},500)}else{G.removeDom()}}},removeDom:function(){this.el.parentNode.removeChild(this.el);this.el=null;if(document.documentElement.classList.contains("ios-select-body-class")){document.documentElement.classList.remove("ios-select-body-class")}}};function D(H,J,I){if(!F.isArray(J)||J.length===0){throw new TypeError("the data must be a non-empty array!");return}if([1,2,3,4,5,6].indexOf(H)==-1){throw new RangeError("the level parameter must be one of 1,2,3,4,5,6!");return}this.data=J;this.level=H||1;this.options=I;this.typeBox="one-level-box";if(this.level===1){this.typeBox="one-level-box"}else{if(this.level===2){this.typeBox="two-level-box"}else{if(this.level===3){this.typeBox="three-level-box"}else{if(this.level===4){this.typeBox="four-level-box"}else{if(this.level===5){this.typeBox="five-level-box"}else{if(this.level===6){this.typeBox="six-level-box"}}}}}}this.title=I.title||"";this.options.itemHeight=I.itemHeight||35;this.options.itemShowCount=[3,5,7,9].indexOf(I.itemShowCount)!==-1?I.itemShowCount:7;this.options.coverArea1Top=Math.floor(this.options.itemShowCount/2);this.options.coverArea2Top=Math.ceil(this.options.itemShowCount/2);this.options.headerHeight=I.headerHeight||44;this.options.relation=F.isArray(this.options.relation)?this.options.relation:[];this.options.oneTwoRelation=this.options.relation[0];this.options.twoThreeRelation=this.options.relation[1];this.options.threeFourRelation=this.options.relation[2];this.options.fourFiveRelation=this.options.relation[3];this.options.fiveSixRelation=this.options.relation[4];if(this.options.cssUnit!=="px"&&this.options.cssUnit!=="rem"){this.options.cssUnit="px"}var G=this;this.selectOneObj={id:G.options.oneLevelId};this.selectTwoObj={id:G.options.twoLevelId};this.selectThreeObj={id:G.options.threeLevelId};this.selectFourObj={id:G.options.fourLevelId};this.selectFiveObj={id:G.options.fiveLevelId};this.selectSixObj={id:G.options.sixLevelId};this.setBase();this.init()}D.prototype={init:function(){this.initLayer();this.setLevelData(1,this.options.oneLevelId,this.options.twoLevelId,this.options.threeLevelId,this.options.fourLevelId,this.options.fiveLevelId,this.options.sixLevelId)},initLayer:function(){var H=this;var K=this.options.sureText||"确定";var J=this.options.closeText||"取消";var I=this.options.headerHeight+this.options.cssUnit;var G=['<header style="height: '+I+"; line-height: "+I+'" class="iosselect-header">','<a style="height: '+I+"; line-height: "+I+'" href="javascript:void(0)" class="close">'+J+"</a>",'<a style="height: '+I+"; line-height: "+I+'" href="javascript:void(0)" class="sure">'+K+"</a>",'<h2 id="iosSelectTitle"></h2>',"</header>",'<section class="iosselect-box">','<div class="one-level-contain" id="oneLevelContain">','<ul class="select-one-level">',"</ul>","</div>",'<div class="two-level-contain" id="twoLevelContain">','<ul class="select-two-level">',"</ul>","</div>",'<div class="three-level-contain" id="threeLevelContain">','<ul class="select-three-level">',"</ul>","</div>",'<div class="four-level-contain" id="fourLevelContain">','<ul class="select-four-level">',"</ul>","</div>",'<div class="five-level-contain" id="fiveLevelContain">','<ul class="select-five-level">',"</ul>","</div>",'<div class="six-level-contain" id="sixLevelContain">','<ul class="select-six-level">',"</ul>","</div>","</section>",'<hr class="cover-area1"/>','<hr class="cover-area2"/>','<div class="ios-select-loading-box" id="iosSelectLoadingBox">','<div class="ios-select-loading"></div>',"</div>"].join("\r\n");this.iosSelectLayer=new E(G,{className:"ios-select-widget-box "+this.typeBox+(this.options.addClassName?" "+this.options.addClassName:"")+(this.options.showAnimate?" fadeInUp":""),container:this.options.container||"",showAnimate:this.options.showAnimate,fallback:this.options.fallback,maskCallback:this.options.maskCallback});this.iosSelectTitleDom=this.iosSelectLayer.el.querySelector("#iosSelectTitle");this.iosSelectLoadingBoxDom=this.iosSelectLayer.el.querySelector("#iosSelectLoadingBox");this.iosSelectTitleDom.innerHTML=this.title;if(this.options.headerHeight&&this.options.itemHeight){this.coverArea1Dom=this.iosSelectLayer.el.querySelector(".cover-area1");this.coverArea1Dom.style.top=this.options.headerHeight+this.options.itemHeight*this.options.coverArea1Top+this.options.cssUnit;this.coverArea2Dom=this.iosSelectLayer.el.querySelector(".cover-area2");this.coverArea2Dom.style.top=this.options.headerHeight+this.options.itemHeight*this.options.coverArea2Top+this.options.cssUnit}this.oneLevelContainDom=this.iosSelectLayer.el.querySelector("#oneLevelContain");this.twoLevelContainDom=this.iosSelectLayer.el.querySelector("#twoLevelContain");this.threeLevelContainDom=this.iosSelectLayer.el.querySelector("#threeLevelContain");this.fourLevelContainDom=this.iosSelectLayer.el.querySelector("#fourLevelContain");this.fiveLevelContainDom=this.iosSelectLayer.el.querySelector("#fiveLevelContain");this.sixLevelContainDom=this.iosSelectLayer.el.querySelector("#sixLevelContain");this.oneLevelUlContainDom=this.iosSelectLayer.el.querySelector(".select-one-level");this.twoLevelUlContainDom=this.iosSelectLayer.el.querySelector(".select-two-level");this.threeLevelUlContainDom=this.iosSelectLayer.el.querySelector(".select-three-level");this.fourLevelUlContainDom=this.iosSelectLayer.el.querySelector(".select-four-level");this.fiveLevelUlContainDom=this.iosSelectLayer.el.querySelector(".select-five-level");this.sixLevelUlContainDom=this.iosSelectLayer.el.querySelector(".select-six-level");this.iosSelectLayer.el.querySelector(".layer").style.height=this.options.itemHeight*this.options.itemShowCount+this.options.headerHeight+this.options.cssUnit;this.oneLevelContainDom.style.height=this.options.itemHeight*this.options.itemShowCount+this.options.cssUnit;document.documentElement.classList.add("ios-select-body-class");this.scrollOne=new B("#oneLevelContain",{probeType:3,bounce:false});this.setScorllEvent(this.scrollOne,1);if(this.level>=2){this.twoLevelContainDom.style.height=this.options.itemHeight*this.options.itemShowCount+this.options.cssUnit;this.scrollTwo=new B("#twoLevelContain",{probeType:3,bounce:false});this.setScorllEvent(this.scrollTwo,2)}if(this.level>=3){this.threeLevelContainDom.style.height=this.options.itemHeight*this.options.itemShowCount+this.options.cssUnit;this.scrollThree=new B("#threeLevelContain",{probeType:3,bounce:false});this.setScorllEvent(this.scrollThree,3)}if(this.level>=4){this.fourLevelContainDom.style.height=this.options.itemHeight*this.options.itemShowCount+this.options.cssUnit;this.scrollFour=new B("#fourLevelContain",{probeType:3,bounce:false});this.setScorllEvent(this.scrollFour,4)}if(this.level>=5){this.fiveLevelContainDom.style.height=this.options.itemHeight*this.options.itemShowCount+this.options.cssUnit;this.scrollFive=new B("#fiveLevelContain",{probeType:3,bounce:false});this.setScorllEvent(this.scrollFive,5)}if(this.level>=6){this.sixLevelContainDom.style.height=this.options.itemHeight*this.options.itemShowCount+this.options.cssUnit;this.scrollSix=new B("#sixLevelContain",{probeType:3,bounce:false});this.setScorllEvent(this.scrollSix,6)}this.selectBtnDom=this.iosSelectLayer.el.querySelector(".sure");this.selectBtnDom.addEventListener("click",function(L){H.options.callback&&H.options.callback(H.selectOneObj,H.selectTwoObj,H.selectThreeObj,H.selectFourObj,H.selectFiveObj,H.selectSixObj)})},mapKeyByIndex:function(I){var H=this;var G={index:1,levelContain:H.oneLevelContainDom,relation:H.options.oneTwoRelation};if(I===2){G={index:2,levelContain:H.twoLevelContainDom,relation:H.options.twoThreeRelation}}else{if(I===3){G={index:3,levelContain:H.threeLevelContainDom,relation:H.options.threeFourRelation}}else{if(I===4){G={index:4,levelContain:H.fourLevelContainDom,relation:H.options.fourFiveRelation}}else{if(I===5){G={index:5,levelContain:H.fiveLevelContainDom,relation:H.options.fiveSixRelation}}else{if(I===6){G={index:6,levelContain:H.sixLevelContainDom,relation:0}}}}}}return G},setScorllEvent:function(H,I){var G=this;var J=G.mapKeyByIndex(I);H.on("scrollStart",function(){G.toggleClassList(J.levelContain)});H.on("scroll",function(){if(isNaN(this.y)){return}var L=Math.abs(this.y/G.baseSize)/G.options.itemHeight;var K=1;K=Math.round(L)+1;G.toggleClassList(J.levelContain);G.changeClassName(J.levelContain,K)});H.on("scrollEnd",function(){var N=Math.abs(this.y/G.baseSize)/G.options.itemHeight;var K=1;var M=0;if(Math.ceil(N)===Math.round(N)){M=Math.ceil(N)*G.options.itemHeight*G.baseSize;K=Math.ceil(N)+1}else{M=Math.floor(N)*G.options.itemHeight*G.baseSize;K=Math.floor(N)+1}H.scrollTo(0,-M,0);G.toggleClassList(J.levelContain);var O=G.changeClassName(J.levelContain,K);var L=F.attrToData(O,K);G.setSelectObj(I,L);if(G.level>I){if((J.relation===1&&F.isArray(G.data[I]))||F.isFunction(G.data[I])){G.setLevelData(I+1,G.selectOneObj.id,G.selectTwoObj.id,G.selectThreeObj.id,G.selectFourObj.id,G.selectFiveObj.id,G.selectSixObj.id)}}});H.on("scrollCancel",function(){var N=Math.abs(this.y/G.baseSize)/G.options.itemHeight;var K=1;var M=0;if(Math.ceil(N)===Math.round(N)){M=Math.ceil(N)*G.options.itemHeight*G.baseSize;K=Math.ceil(N)+1}else{M=Math.floor(N)*G.options.itemHeight*G.baseSize;K=Math.floor(N)+1}H.scrollTo(0,-M,0);G.toggleClassList(J.levelContain);var O=G.changeClassName(J.levelContain,K);var L=F.attrToData(O,K);G.setSelectObj(I,L);if(G.level>I){if((J.relation===1&&F.isArray(G.data[I]))||F.isFunction(G.data[I])){G.setLevelData(I+1,G.selectOneObj.id,G.selectTwoObj.id,G.selectThreeObj.id,G.selectFourObj.id,G.selectFiveObj.id,G.selectSixObj.id)}}})},loadingShow:function(){this.options.showLoading&&(this.iosSelectLoadingBoxDom.style.display="block")},loadingHide:function(){this.iosSelectLoadingBoxDom.style.display="none"},mapRenderByIndex:function(I){var H=this;var G={index:1,relation:0,levelUlContainDom:H.oneLevelUlContainDom,scrollInstance:H.scrollOne,levelContainDom:H.oneLevelContainDom};if(I===2){G={index:2,relation:H.options.oneTwoRelation,levelUlContainDom:H.twoLevelUlContainDom,scrollInstance:H.scrollTwo,levelContainDom:H.twoLevelContainDom}}else{if(I===3){G={index:3,relation:H.options.twoThreeRelation,levelUlContainDom:H.threeLevelUlContainDom,scrollInstance:H.scrollThree,levelContainDom:H.threeLevelContainDom}}else{if(I===4){G={index:4,relation:H.options.threeFourRelation,levelUlContainDom:H.fourLevelUlContainDom,scrollInstance:H.scrollFour,levelContainDom:H.fourLevelContainDom}}else{if(I===5){G={index:5,relation:H.options.fourFiveRelation,levelUlContainDom:H.fiveLevelUlContainDom,scrollInstance:H.scrollFive,levelContainDom:H.fiveLevelContainDom}}else{if(I===6){G={index:6,relation:H.options.fiveSixRelation,levelUlContainDom:H.sixLevelUlContainDom,scrollInstance:H.scrollSix,levelContainDom:H.sixLevelContainDom}}}}}}return G},getLevelData:function(O,L,I,K,M,G){var H=[];var J=this.mapRenderByIndex(O);if(O===1){H=this.data[0]}else{if(J.relation===1){var N=arguments[O-1];this.data[O-1].forEach(function(R,P,Q){if(R["parentId"]==N){H.push(R)}})}else{H=this.data[O-1]}}return H},setLevelData:function(N,K,I,J,L,G,M){if(F.isArray(this.data[N-1])){var H=this.getLevelData(N,K,I,J,L);this.renderLevel(N,K,I,J,L,G,M,H)}else{if(F.isFunction(this.data[N-1])){this.loadingShow();this.data[N-1].apply(this,[K,I,J,L,G].slice(0,N-1).concat(function(O){this.loadingHide();this.renderLevel(N,K,I,J,L,G,M,O)}.bind(this)))}else{throw new Error("data format error")}}},renderLevel:function(T,P,I,O,Q,G,R,H){var K=0;var L=arguments[T];var J=H.some(function(Y,W,X){return Y.id==L});if(!J){L=H[0]["id"]}var N="";var U=this.options.itemHeight+this.options.cssUnit;N+=this.getWhiteItem();H.forEach(function(Y,W,X){if(Y.id==L){N+='<li style="height: '+U+"; line-height: "+U+';"'+F.attrToHtml(Y)+' class="at">'+Y.value+"</li>";K=W+1}else{N+='<li style="height: '+U+"; line-height: "+U+';"'+F.attrToHtml(Y)+">"+Y.value+"</li>"}});N+=this.getWhiteItem();var M=this.mapRenderByIndex(T);M.levelUlContainDom.innerHTML=N;M.scrollInstance.refresh();M.scrollInstance.scrollToElement(":nth-child("+K+")",0);var V=this.changeClassName(M.levelContainDom,K);var S=F.attrToData(V,K);this.setSelectObj(T,S);if(this.level>T){this.setLevelData(T+1,this.selectOneObj.id,this.selectTwoObj.id,this.selectThreeObj.id,this.selectFourObj.id,this.selectFiveObj.id,this.selectSixObj.id)}},setSelectObj:function(H,G){if(H===1){this.selectOneObj=G}else{if(H===2){this.selectTwoObj=G}else{if(H===3){this.selectThreeObj=G}else{if(H===4){this.selectFourObj=G}else{if(H===5){this.selectFiveObj=G}else{if(H===6){this.selectSixObj=G}}}}}}},getWhiteItem:function(){var G="";var H=this.options.itemHeight+this.options.cssUnit;var I='<li style="height: '+H+"; line-height: "+H+'"></li>';G+=I;if(this.options.itemShowCount>3){G+=I}if(this.options.itemShowCount>5){G+=I}if(this.options.itemShowCount>7){G+=I}return G},changeClassName:function(I,G){var H;if(this.options.itemShowCount===3){H=I.querySelector("li:nth-child("+(G+1)+")");H.classList.add("at")}else{if(this.options.itemShowCount===5){H=I.querySelector("li:nth-child("+(G+2)+")");H.classList.add("at");I.querySelector("li:nth-child("+(G+1)+")").classList.add("side1");I.querySelector("li:nth-child("+(G+3)+")").classList.add("side1")}else{if(this.options.itemShowCount===7){H=I.querySelector("li:nth-child("+(G+3)+")");H.classList.add("at");I.querySelector("li:nth-child("+(G+2)+")").classList.add("side1");I.querySelector("li:nth-child("+(G+1)+")").classList.add("side2");I.querySelector("li:nth-child("+(G+4)+")").classList.add("side1");I.querySelector("li:nth-child("+(G+5)+")").classList.add("side2")}else{if(this.options.itemShowCount===9){H=I.querySelector("li:nth-child("+(G+4)+")");H.classList.add("at");I.querySelector("li:nth-child("+(G+3)+")").classList.add("side1");I.querySelector("li:nth-child("+(G+2)+")").classList.add("side2");I.querySelector("li:nth-child("+(G+5)+")").classList.add("side1");I.querySelector("li:nth-child("+(G+6)+")").classList.add("side2")}}}}return H},setBase:function(){if(this.options.cssUnit==="rem"){var G=document.documentElement;var H=window.getComputedStyle(G,null);var J=H.fontSize;try{this.baseSize=/\d+(?:\.\d+)?/.exec(J)[0]}catch(I){this.baseSize=1}}else{this.baseSize=1}},toggleClassList:function(G){Array.prototype.slice.call(G.querySelectorAll("li")).forEach(function(H){if(H.classList.contains("at")){H.classList.remove("at")}else{if(H.classList.contains("side1")){H.classList.remove("side1")}else{if(H.classList.contains("side2")){H.classList.remove("side2")}}}})}};if(typeof module!="undefined"&&module.exports){module.exports=D}else{if(typeof define=="function"&&define.amd){define(function(){return D})}else{window.IosSelect=D}}})();