(function(F){if(!String.prototype.includes){(function(){var I={}.toString;var J=(function(){try{var L={};var M=Object.defineProperty;var K=M(L,L,L)&&M}catch(N){}return K}());var H="".indexOf;var G=function(O){if(this==null){throw new TypeError()}var P=String(this);if(O&&I.call(O)=="[object RegExp]"){throw new TypeError()}var Q=P.length;var K=String(O);var M=K.length;var R=arguments.length>1?arguments[1]:undefined;var N=R?Number(R):0;if(N!=N){N=0}var L=Math.min(Math.max(N,0),Q);if(M+L>Q){return false}return H.call(P,K,N)!=-1};if(J){J(String.prototype,"includes",{"value":G,"configurable":true,"writable":true})}else{String.prototype.includes=G}}())}if(!String.prototype.startsWith){(function(){var I=(function(){try{var K={};var L=Object.defineProperty;var J=L(K,K,K)&&L}catch(M){}return J}());var H={}.toString;var G=function(N){if(this==null){throw new TypeError()}var O=String(this);if(N&&H.call(N)=="[object RegExp]"){throw new TypeError()}var P=O.length;var J=String(N);var L=J.length;var Q=arguments.length>1?arguments[1]:undefined;var M=Q?Number(Q):0;if(M!=M){M=0}var K=Math.min(Math.max(M,0),P);if(L+K>P){return false}var R=-1;while(++R<L){if(O.charCodeAt(K+R)!=J.charCodeAt(R)){return false}}return true};if(I){I(String.prototype,"startsWith",{"value":G,"configurable":true,"writable":true})}else{String.prototype.startsWith=G}}())}if(!Object.keys){Object.keys=function(H,I,G){G=[];for(I in H){G.hasOwnProperty.call(H,I)&&G.push(I)}return G}}F.fn.triggerNative=function(H){var G=this[0],I;if(G.dispatchEvent){if(typeof Event==="function"){I=new Event(H,{bubbles:true})}else{I=document.createEvent("Event");I.initEvent(H,true,false)}G.dispatchEvent(I)}else{if(G.fireEvent){I=document.createEventObject();I.eventType=H;G.fireEvent("on"+H,I)}this.trigger(H)}};F.expr[":"].icontains=function(H,K,J){var G=F(H);var I=(G.data("tokens")||G.text()).toUpperCase();return I.includes(J[3].toUpperCase())};F.expr[":"].ibegins=function(H,K,J){var G=F(H);var I=(G.data("tokens")||G.text()).toUpperCase();return I.startsWith(J[3].toUpperCase())};F.expr[":"].aicontains=function(H,K,J){var G=F(H);var I=(G.data("tokens")||G.data("normalizedText")||G.text()).toUpperCase();return I.includes(J[3].toUpperCase())};F.expr[":"].aibegins=function(H,K,J){var G=F(H);var I=(G.data("tokens")||G.data("normalizedText")||G.text()).toUpperCase();return I.startsWith(J[3].toUpperCase())};function B(G){var H=[{re:/[\xC0-\xC6]/g,ch:"A"},{re:/[\xE0-\xE6]/g,ch:"a"},{re:/[\xC8-\xCB]/g,ch:"E"},{re:/[\xE8-\xEB]/g,ch:"e"},{re:/[\xCC-\xCF]/g,ch:"I"},{re:/[\xEC-\xEF]/g,ch:"i"},{re:/[\xD2-\xD6]/g,ch:"O"},{re:/[\xF2-\xF6]/g,ch:"o"},{re:/[\xD9-\xDC]/g,ch:"U"},{re:/[\xF9-\xFC]/g,ch:"u"},{re:/[\xC7-\xE7]/g,ch:"c"},{re:/[\xD1]/g,ch:"N"},{re:/[\xF1]/g,ch:"n"}];F.each(H,function(){G=G.replace(this.re,this.ch)});return G}function D(G){var I={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#x27;","`":"&#x60;"};var H="(?:"+Object.keys(I).join("|")+")",K=new RegExp(H),L=new RegExp(H,"g"),J=G==null?"":""+G;return K.test(J)?J.replace(L,function(M){return I[M]}):J}var E=function(I,H,G){if(G){G.stopPropagation();G.preventDefault()}this.$element=F(I);this.$newElement=null;this.$button=null;this.$menu=null;this.$lis=null;this.options=H;if(this.options.title===null){this.options.title=this.$element.attr("title")}this.val=E.prototype.val;this.render=E.prototype.render;this.refresh=E.prototype.refresh;this.setStyle=E.prototype.setStyle;this.selectAll=E.prototype.selectAll;this.deselectAll=E.prototype.deselectAll;this.destroy=E.prototype.destroy;this.remove=E.prototype.remove;this.show=E.prototype.show;this.hide=E.prototype.hide;this.init()};E.VERSION="1.10.0";E.DEFAULTS={noneSelectedText:"Nothing selected",noneResultsText:"No results matched {0}",countSelectedText:function(G,H){return(G==1)?"{0} item selected":"{0} items selected"},maxOptionsText:function(G,H){return[(G==1)?"Limit reached ({n} item max)":"Limit reached ({n} items max)",(H==1)?"Group limit reached ({n} item max)":"Group limit reached ({n} items max)"]},selectAllText:"Select All",deselectAllText:"Deselect All",doneButton:false,doneButtonText:"Close",multipleSeparator:", ",styleBase:"btn",style:"btn-default",size:"auto",title:null,selectedTextFormat:"values",width:false,container:false,hideDisabled:false,showSubtext:false,showIcon:true,showContent:true,dropupAuto:true,header:false,liveSearch:false,liveSearchPlaceholder:null,liveSearchNormalize:false,liveSearchStyle:"contains",actionsBox:false,iconBase:"glyphicon",tickIcon:"glyphicon-ok",showTick:false,template:{caret:'<span class="caret"></span>'},maxOptions:false,mobile:false,selectOnTab:false,dropdownAlignRight:false};E.prototype={constructor:E,init:function(){var G=this,H=this.$element.attr("id");this.$element.addClass("bs-select-hidden");this.liObj={};this.multiple=this.$element.prop("multiple");this.autofocus=this.$element.prop("autofocus");this.$newElement=this.createView();this.$element.after(this.$newElement).appendTo(this.$newElement);this.$button=this.$newElement.children("button");this.$menu=this.$newElement.children(".dropdown-menu");this.$menuInner=this.$menu.children(".inner");this.$searchbox=this.$menu.find("input");this.$element.removeClass("bs-select-hidden");if(this.options.dropdownAlignRight){this.$menu.addClass("dropdown-menu-right")}if(typeof H!=="undefined"){this.$button.attr("data-id",H);F('label[for="'+H+'"]').click(function(I){I.preventDefault();G.$button.focus()})}this.checkDisabled();this.clickListener();if(this.options.liveSearch){this.liveSearchListener()}this.render();this.setStyle();this.setWidth();if(this.options.container){this.selectPosition()}this.$menu.data("this",this);this.$newElement.data("this",this);if(this.options.mobile){this.mobile()}this.$newElement.on({"hide.bs.dropdown":function(I){G.$element.trigger("hide.bs.select",I)},"hidden.bs.dropdown":function(I){G.$element.trigger("hidden.bs.select",I)},"show.bs.dropdown":function(I){G.$element.trigger("show.bs.select",I)},"shown.bs.dropdown":function(I){G.$element.trigger("shown.bs.select",I)}});if(G.$element[0].hasAttribute("required")){this.$element.on("invalid",function(){G.$button.addClass("bs-invalid").focus();G.$element.on({"focus.bs.select":function(){G.$button.focus();G.$element.off("focus.bs.select")},"shown.bs.select":function(){G.$element.val(G.$element.val()).off("shown.bs.select")},"rendered.bs.select":function(){if(this.validity.valid){G.$button.removeClass("bs-invalid")}G.$element.off("rendered.bs.select")}})})}setTimeout(function(){G.$element.trigger("loaded.bs.select")})},createDropdown:function(){var N=(this.multiple||this.options.showTick)?" show-tick":"",H=this.$element.parent().hasClass("input-group")?" input-group-btn":"",K=this.autofocus?" autofocus":"";var M=this.options.header?'<div class="popover-title"><button type="button" class="close" aria-hidden="true">&times;</button>'+this.options.header+"</div>":"";var G=this.options.liveSearch?'<div class="bs-searchbox"><input type="text" class="form-control" autocomplete="off"'+(null===this.options.liveSearchPlaceholder?"":' placeholder="'+D(this.options.liveSearchPlaceholder)+'"')+"></div>":"";var J=this.multiple&&this.options.actionsBox?'<div class="bs-actionsbox"><div class="btn-group btn-group-sm btn-block"><button type="button" class="actions-btn bs-select-all btn btn-default">'+this.options.selectAllText+'</button><button type="button" class="actions-btn bs-deselect-all btn btn-default">'+this.options.deselectAllText+"</button></div></div>":"";var I=this.multiple&&this.options.doneButton?'<div class="bs-donebutton"><div class="btn-group btn-block"><button type="button" class="btn btn-sm btn-default">'+this.options.doneButtonText+"</button></div></div>":"";var L='<div class="btn-group bootstrap-select'+N+H+'"><button type="button" class="'+this.options.styleBase+' dropdown-toggle" data-toggle="dropdown"'+K+'><span class="filter-option pull-left"></span>&nbsp;<span class="bs-caret">'+this.options.template.caret+'</span></button><div class="dropdown-menu open">'+M+G+J+'<ul class="dropdown-menu inner" role="menu"></ul>'+I+"</div></div>";return F(L)},createView:function(){var G=this.createDropdown(),H=this.createLi();G.find("ul")[0].innerHTML=H;return G},reloadLi:function(){this.destroyLi();var G=this.createLi();this.$menuInner[0].innerHTML=G},destroyLi:function(){this.$menu.find("li").remove()},createLi:function(){var L=this,K=[],I=0,J=document.createElement("option"),G=-1;var M=function(R,Q,O,P){return"<li"+((typeof O!=="undefined"&""!==O)?' class="'+O+'"':"")+((typeof Q!=="undefined"&null!==Q)?' data-original-index="'+Q+'"':"")+((typeof P!=="undefined"&null!==P)?'data-optgroup="'+P+'"':"")+">"+R+"</li>"};var N=function(P,R,O,Q){return'<a tabindex="0"'+(typeof R!=="undefined"?' class="'+R+'"':"")+(typeof O!=="undefined"?' style="'+O+'"':"")+(L.options.liveSearchNormalize?' data-normalized-text="'+B(D(P))+'"':"")+(typeof Q!=="undefined"||Q!==null?' data-tokens="'+Q+'"':"")+">"+P+'<span class="'+L.options.iconBase+" "+L.options.tickIcon+' check-mark"></span></a>'};if(this.options.title&&!this.multiple){G--;if(!this.$element.find(".bs-title-option").length){var H=this.$element[0];J.className="bs-title-option";J.appendChild(document.createTextNode(this.options.title));J.value="";H.insertBefore(J,H.firstChild);if(F(H.options[H.selectedIndex]).attr("selected")===undefined){J.selected=true}}}this.$element.find("option").each(function(P){var Z=F(this);G++;if(Z.hasClass("bs-title-option")){return}var Y=this.className||"",Q=this.style.cssText,V=Z.data("content")?Z.data("content"):Z.html(),T=Z.data("tokens")?Z.data("tokens"):null,R=typeof Z.data("subtext")!=="undefined"?'<small class="text-muted">'+Z.data("subtext")+"</small>":"",U=typeof Z.data("icon")!=="undefined"?'<span class="'+L.options.iconBase+" "+Z.data("icon")+'"></span> ':"",O=this.parentNode.tagName==="OPTGROUP",a=this.disabled||(O&&this.parentNode.disabled);if(U!==""&&a){U="<span>"+U+"</span>"}if(L.options.hideDisabled&&a&&!O){G--;return}if(!Z.data("content")){V=U+'<span class="text">'+V+R+"</span>"}if(O&&Z.data("divider")!==true){var W=" "+this.parentNode.className||"";if(Z.index()===0){I+=1;var X=this.parentNode.label,S=typeof Z.parent().data("subtext")!=="undefined"?'<small class="text-muted">'+Z.parent().data("subtext")+"</small>":"",b=Z.parent().data("icon")?'<span class="'+L.options.iconBase+" "+Z.parent().data("icon")+'"></span> ':"";X=b+'<span class="text">'+X+S+"</span>";if(P!==0&&K.length>0){G++;K.push(M("",null,"divider",I+"div"))}G++;K.push(M(X,null,"dropdown-header"+W,I))}if(L.options.hideDisabled&&a){G--;return}K.push(M(N(V,"opt "+Y+W,Q,T),P,"",I))}else{if(Z.data("divider")===true){K.push(M("",P,"divider"))}else{if(Z.data("hidden")===true){K.push(M(N(V,Y,Q,T),P,"hidden is-hidden"))}else{if(this.previousElementSibling&&this.previousElementSibling.tagName==="OPTGROUP"){G++;K.push(M("",null,"divider",I+"div"))}K.push(M(N(V,Y,Q,T),P))}}}L.liObj[P]=G});if(!this.multiple&&this.$element.find("option:selected").length===0&&!this.options.title){this.$element.find("option").eq(0).prop("selected",true).attr("selected","selected")}return K.join("")},findLis:function(){if(this.$lis==null){this.$lis=this.$menu.find("li")}return this.$lis},render:function(G){var J=this,K;if(G!==false){this.$element.find("option").each(function(P){var O=J.findLis().eq(J.liObj[P]);J.setDisabled(P,this.disabled||this.parentNode.tagName==="OPTGROUP"&&this.parentNode.disabled,O);J.setSelected(P,this.selected,O)})}this.tabIndex();var M=this.$element.find("option").map(function(){if(this.selected){if(J.options.hideDisabled&&(this.disabled||this.parentNode.tagName==="OPTGROUP"&&this.parentNode.disabled)){return}var O=F(this),P=O.data("icon")&&J.options.showIcon?'<i class="'+J.options.iconBase+" "+O.data("icon")+'"></i> ':"",Q;if(J.options.showSubtext&&O.data("subtext")&&!J.multiple){Q=' <small class="text-muted">'+O.data("subtext")+"</small>"}else{Q=""}if(typeof O.attr("title")!=="undefined"){return O.attr("title")}else{if(O.data("content")&&J.options.showContent){return O.data("content")}else{return P+O.html()+Q}}}}).toArray();var L=!this.multiple?M[0]:M.join(this.options.multipleSeparator);if(this.multiple&&this.options.selectedTextFormat.indexOf("count")>-1){var H=this.options.selectedTextFormat.split(">");if((H.length>1&&M.length>H[1])||(H.length==1&&M.length>=2)){K=this.options.hideDisabled?", [disabled]":"";var N=this.$element.find("option").not('[data-divider="true"], [data-hidden="true"]'+K).length,I=(typeof this.options.countSelectedText==="function")?this.options.countSelectedText(M.length,N):this.options.countSelectedText;L=I.replace("{0}",M.length.toString()).replace("{1}",N.toString())}}if(this.options.title==undefined){this.options.title=this.$element.attr("title")}if(this.options.selectedTextFormat=="static"){L=this.options.title}if(!L){L=typeof this.options.title!=="undefined"?this.options.title:this.options.noneSelectedText}this.$button.attr("title",F.trim(L.replace(/<[^>]*>?/g,"")));this.$button.children(".filter-option").html(L);this.$element.trigger("rendered.bs.select")},setStyle:function(H,I){if(this.$element.attr("class")){this.$newElement.addClass(this.$element.attr("class").replace(/selectpicker|mobile-device|bs-select-hidden|validate\[.*\]/gi,""))}var G=H?H:this.options.style;if(I=="add"){this.$button.addClass(G)}else{if(I=="remove"){this.$button.removeClass(G)}else{this.$button.removeClass(this.options.style);this.$button.addClass(G)}}},liHeight:function(N){if(!N&&(this.options.size===false||this.sizeInfo)){return}var I=document.createElement("div"),c=document.createElement("div"),P=document.createElement("ul"),G=document.createElement("li"),M=document.createElement("li"),Q=document.createElement("a"),S=document.createElement("span"),K=this.options.header&&this.$menu.find(".popover-title").length>0?this.$menu.find(".popover-title")[0].cloneNode(true):null,L=this.options.liveSearch?document.createElement("div"):null,H=this.options.actionsBox&&this.multiple&&this.$menu.find(".bs-actionsbox").length>0?this.$menu.find(".bs-actionsbox")[0].cloneNode(true):null,b=this.options.doneButton&&this.multiple&&this.$menu.find(".bs-donebutton").length>0?this.$menu.find(".bs-donebutton")[0].cloneNode(true):null;S.className="text";I.className=this.$menu[0].parentNode.className+" open";c.className="dropdown-menu open";P.className="dropdown-menu inner";G.className="divider";S.appendChild(document.createTextNode("Inner text"));Q.appendChild(S);M.appendChild(Q);P.appendChild(M);P.appendChild(G);if(K){c.appendChild(K)}if(L){var R=document.createElement("span");L.className="bs-searchbox";R.className="form-control";L.appendChild(R);c.appendChild(L)}if(H){c.appendChild(H)}c.appendChild(P);if(b){c.appendChild(b)}I.appendChild(c);document.body.appendChild(I);var X=Q.offsetHeight,d=K?K.offsetHeight:0,Y=L?L.offsetHeight:0,Z=H?H.offsetHeight:0,V=b?b.offsetHeight:0,O=F(G).outerHeight(true),J=typeof getComputedStyle==="function"?getComputedStyle(c):false,U=J?null:F(c),W=parseInt(J?J.paddingTop:U.css("paddingTop"))+parseInt(J?J.paddingBottom:U.css("paddingBottom"))+parseInt(J?J.borderTopWidth:U.css("borderTopWidth"))+parseInt(J?J.borderBottomWidth:U.css("borderBottomWidth")),T=W+parseInt(J?J.marginTop:U.css("marginTop"))+parseInt(J?J.marginBottom:U.css("marginBottom"))+2;document.body.removeChild(I);this.sizeInfo={liHeight:X,headerHeight:d,searchHeight:Y,actionsHeight:Z,doneButtonHeight:V,dividerHeight:O,menuPadding:W,menuExtras:T}},setSize:function(){this.findLis();this.liHeight();if(this.options.header){this.$menu.css("padding-top",0)}if(this.options.size===false){return}var a=this,T=this.$menu,H=this.$menuInner,b=F(window),J=this.$newElement[0].offsetHeight,U=this.sizeInfo["liHeight"],Z=this.sizeInfo["headerHeight"],G=this.sizeInfo["searchHeight"],W=this.sizeInfo["actionsHeight"],V=this.sizeInfo["doneButtonHeight"],Q=this.sizeInfo["dividerHeight"],S=this.sizeInfo["menuPadding"],P=this.sizeInfo["menuExtras"],R=this.options.hideDisabled?".disabled":"",M,I,Y,O,L=function(){Y=a.$newElement.offset().top-b.scrollTop();O=b.height()-Y-J};L();if(this.options.size==="auto"){var N=function(){var g,c=function(h,i){return function(j){if(i){return(j.classList?j.classList.contains(h):F(j).hasClass(h))}else{return !(j.classList?j.classList.contains(h):F(j).hasClass(h))}}},d=a.$menuInner[0].getElementsByTagName("li"),f=Array.prototype.filter?Array.prototype.filter.call(d,c("hidden",false)):a.$lis.not(".hidden"),e=Array.prototype.filter?Array.prototype.filter.call(f,c("dropdown-header",true)):f.filter(".dropdown-header");L();M=O-P;if(a.options.container){if(!T.data("height")){T.data("height",T.height())}I=T.data("height")}else{I=T.height()}if(a.options.dropupAuto){a.$newElement.toggleClass("dropup",Y>O&&(M-P)<I)}if(a.$newElement.hasClass("dropup")){M=Y-P}if((f.length+e.length)>3){g=U*3+P-2}else{g=0}T.css({"max-height":M+"px","overflow":"hidden","min-height":g+Z+G+W+V+"px"});H.css({"max-height":M-Z-G-W-V-S+"px","overflow-y":"auto","min-height":Math.max(g-S,0)+"px"})};N();this.$searchbox.off("input.getSize propertychange.getSize").on("input.getSize propertychange.getSize",N);b.off("resize.getSize scroll.getSize").on("resize.getSize scroll.getSize",N)}else{if(this.options.size&&this.options.size!="auto"&&this.$lis.not(R).length>this.options.size){var X=this.$lis.not(".divider").not(R).children().slice(0,this.options.size).last().parent().index(),K=this.$lis.slice(0,X+1).filter(".divider").length;M=U*this.options.size+K*Q+S;if(a.options.container){if(!T.data("height")){T.data("height",T.height())}I=T.data("height")}else{I=T.height()}if(a.options.dropupAuto){this.$newElement.toggleClass("dropup",Y>O&&(M-P)<I)}T.css({"max-height":M+Z+G+W+V+"px","overflow":"hidden","min-height":""});H.css({"max-height":M-S+"px","overflow-y":"auto","min-height":""})}}},setWidth:function(){if(this.options.width==="auto"){this.$menu.css("min-width","0");var H=this.$menu.parent().clone().appendTo("body"),J=this.options.container?this.$newElement.clone().appendTo("body"):H,I=H.children(".dropdown-menu").outerWidth(),G=J.css("width","auto").children("button").outerWidth();H.remove();J.remove();this.$newElement.css("width",Math.max(I,G)+"px")}else{if(this.options.width==="fit"){this.$menu.css("min-width","");this.$newElement.css("width","").addClass("fit-width")}else{if(this.options.width){this.$menu.css("min-width","");this.$newElement.css("width",this.options.width)}else{this.$menu.css("min-width","");this.$newElement.css("width","")}}}if(this.$newElement.hasClass("fit-width")&&this.options.width!=="fit"){this.$newElement.removeClass("fit-width")}},selectPosition:function(){this.$bsContainer=F('<div class="bs-container" />');var G=this,H,J,I=function(K){G.$bsContainer.addClass(K.attr("class").replace(/form-control|fit-width/gi,"")).toggleClass("dropup",K.hasClass("dropup"));H=K.offset();J=K.hasClass("dropup")?0:K[0].offsetHeight;G.$bsContainer.css({"top":H.top+J,"left":H.left,"width":K[0].offsetWidth})};this.$button.on("click",function(){var K=F(this);if(G.isDisabled()){return}I(G.$newElement);G.$bsContainer.appendTo(G.options.container).toggleClass("open",!K.hasClass("open")).append(G.$menu)});F(window).on("resize scroll",function(){I(G.$newElement)});this.$element.on("hide.bs.select",function(){G.$menu.data("height",G.$menu.height());G.$bsContainer.detach()})},setSelected:function(H,I,G){if(!G){G=this.findLis().eq(this.liObj[H])}G.toggleClass("selected",I)},setDisabled:function(H,I,G){if(!G){G=this.findLis().eq(this.liObj[H])}if(I){G.addClass("disabled").children("a").attr("href","#").attr("tabindex",-1)}else{G.removeClass("disabled").children("a").removeAttr("href").attr("tabindex",0)}},isDisabled:function(){return this.$element[0].disabled},checkDisabled:function(){var G=this;if(this.isDisabled()){this.$newElement.addClass("disabled");this.$button.addClass("disabled").attr("tabindex",-1)}else{if(this.$button.hasClass("disabled")){this.$newElement.removeClass("disabled");this.$button.removeClass("disabled")}if(this.$button.attr("tabindex")==-1&&!this.$element.data("tabindex")){this.$button.removeAttr("tabindex")}}this.$button.click(function(){return !G.isDisabled()})},tabIndex:function(){if(this.$element.data("tabindex")!==this.$element.attr("tabindex")&&(this.$element.attr("tabindex")!==-98&&this.$element.attr("tabindex")!=="-98")){this.$element.data("tabindex",this.$element.attr("tabindex"));this.$button.attr("tabindex",this.$element.data("tabindex"))}this.$element.attr("tabindex",-98)},clickListener:function(){var G=this,H=F(document);this.$newElement.on("touchstart.dropdown",".dropdown-menu",function(I){I.stopPropagation()});H.data("spaceSelect",false);this.$button.on("keyup",function(I){if(/(32)/.test(I.keyCode.toString(10))&&H.data("spaceSelect")){I.preventDefault();H.data("spaceSelect",false)}});this.$button.on("click",function(){G.setSize()});this.$element.on("shown.bs.select",function(){if(!G.options.liveSearch&&!G.multiple){G.$menuInner.find(".selected a").focus()}else{if(!G.multiple){var J=G.liObj[G.$element[0].selectedIndex];if(typeof J!=="number"||G.options.size===false){return}var I=G.$lis.eq(J)[0].offsetTop-G.$menuInner[0].offsetTop;I=I-G.$menuInner[0].offsetHeight/2+G.sizeInfo.liHeight/2;G.$menuInner[0].scrollTop=I}}});this.$menuInner.on("click","li a",function(M){var S=F(this),N=S.parent().data("originalIndex"),J=G.$element.val(),K=G.$element.prop("selectedIndex");if(G.multiple){M.stopPropagation()}M.preventDefault();if(!G.isDisabled()&&!S.parent().hasClass("disabled")){var P=G.$element.find("option"),T=P.eq(N),O=T.prop("selected"),I=T.parent("optgroup"),V=G.options.maxOptions,Z=I.data("maxOptions")||false;if(!G.multiple){P.prop("selected",false);T.prop("selected",true);G.$menuInner.find(".selected").removeClass("selected");G.setSelected(N,true)}else{T.prop("selected",!O);G.setSelected(N,!O);S.blur();if(V!==false||Z!==false){var U=V<P.filter(":selected").length,W=Z<I.find("option:selected").length;if((V&&U)||(Z&&W)){if(V&&V==1){P.prop("selected",false);T.prop("selected",true);G.$menuInner.find(".selected").removeClass("selected");G.setSelected(N,true)}else{if(Z&&Z==1){I.find("option:selected").prop("selected",false);T.prop("selected",true);var Q=S.parent().data("optgroup");G.$menuInner.find('[data-optgroup="'+Q+'"]').removeClass("selected");G.setSelected(N,true)}else{var L=(typeof G.options.maxOptionsText==="function")?G.options.maxOptionsText(V,Z):G.options.maxOptionsText,R=L[0].replace("{n}",V),X=L[1].replace("{n}",Z),Y=F('<div class="notify"></div>');if(L[2]){R=R.replace("{var}",L[2][V>1?0:1]);X=X.replace("{var}",L[2][Z>1?0:1])}T.prop("selected",false);G.$menu.append(Y);if(V&&U){Y.append(F("<div>"+R+"</div>"));G.$element.trigger("maxReached.bs.select")}if(Z&&W){Y.append(F("<div>"+X+"</div>"));G.$element.trigger("maxReachedGrp.bs.select")}setTimeout(function(){G.setSelected(N,false)},10);Y.delay(750).fadeOut(300,function(){F(this).remove()})}}}}}if(!G.multiple){G.$button.focus()}else{if(G.options.liveSearch){G.$searchbox.focus()}}if((J!=G.$element.val()&&G.multiple)||(K!=G.$element.prop("selectedIndex")&&!G.multiple)){G.$element.trigger("changed.bs.select",[N,T.prop("selected"),O]).triggerNative("change")}}});this.$menu.on("click","li.disabled a, .popover-title, .popover-title :not(.close)",function(I){if(I.currentTarget==this){I.preventDefault();I.stopPropagation();if(G.options.liveSearch&&!F(I.target).hasClass("close")){G.$searchbox.focus()}else{G.$button.focus()}}});this.$menuInner.on("click",".divider, .dropdown-header",function(I){I.preventDefault();I.stopPropagation();if(G.options.liveSearch){G.$searchbox.focus()}else{G.$button.focus()}});this.$menu.on("click",".popover-title .close",function(){G.$button.click()});this.$searchbox.on("click",function(I){I.stopPropagation()});this.$menu.on("click",".actions-btn",function(I){if(G.options.liveSearch){G.$searchbox.focus()}else{G.$button.focus()}I.preventDefault();I.stopPropagation();if(F(this).hasClass("bs-select-all")){G.selectAll()}else{G.deselectAll()}});this.$element.change(function(){G.render(false)})},liveSearchListener:function(){var G=this,H=F('<li class="no-results"></li>');this.$button.on("click.dropdown.data-api touchstart.dropdown.data-api",function(){G.$menuInner.find(".active").removeClass("active");if(!!G.$searchbox.val()){G.$searchbox.val("");G.$lis.not(".is-hidden").removeClass("hidden");if(!!H.parent().length){H.remove()}}if(!G.multiple){G.$menuInner.find(".selected").addClass("active")}setTimeout(function(){G.$searchbox.focus()},10)});this.$searchbox.on("click.dropdown.data-api focus.dropdown.data-api touchend.dropdown.data-api",function(I){I.stopPropagation()});this.$searchbox.on("input propertychange",function(){if(G.$searchbox.val()){var I=G.$lis.not(".is-hidden").removeClass("hidden").children("a");if(G.options.liveSearchNormalize){I=I.not(":a"+G._searchStyle()+'("'+B(G.$searchbox.val())+'")')}else{I=I.not(":"+G._searchStyle()+'("'+G.$searchbox.val()+'")')}I.parent().addClass("hidden");G.$lis.filter(".dropdown-header").each(function(){var K=F(this),L=K.data("optgroup");if(G.$lis.filter("[data-optgroup="+L+"]").not(K).not(".hidden").length===0){K.addClass("hidden");G.$lis.filter("[data-optgroup="+L+"div]").addClass("hidden")}});var J=G.$lis.not(".hidden");J.each(function(L){var K=F(this);if(K.hasClass("divider")&&(K.index()===J.first().index()||K.index()===J.last().index()||J.eq(L+1).hasClass("divider"))){K.addClass("hidden")}});if(!G.$lis.not(".hidden, .no-results").length){if(!!H.parent().length){H.remove()}H.html(G.options.noneResultsText.replace("{0}",'"'+D(G.$searchbox.val())+'"')).show();G.$menuInner.append(H)}else{if(!!H.parent().length){H.remove()}}}else{G.$lis.not(".is-hidden").removeClass("hidden");if(!!H.parent().length){H.remove()}}G.$lis.filter(".active").removeClass("active");if(G.$searchbox.val()){G.$lis.not(".hidden, .divider, .dropdown-header").eq(0).addClass("active").children("a").focus()}F(this).focus()})},_searchStyle:function(){var G={begins:"ibegins",startsWith:"ibegins"};return G[this.options.liveSearchStyle]||"icontains"},val:function(G){if(typeof G!=="undefined"){this.$element.val(G);this.render();return this.$element}else{return this.$element.val()}},changeAll:function(J){if(typeof J==="undefined"){J=true}this.findLis();var G=this.$element.find("option"),L=this.$lis.not(".divider, .dropdown-header, .disabled, .hidden").toggleClass("selected",J),M=L.length,I=[];for(var H=0;H<M;H++){var K=L[H].getAttribute("data-original-index");I[I.length]=G.eq(K)[0]}F(I).prop("selected",J);this.render(false);this.$element.trigger("changed.bs.select").triggerNative("change")},selectAll:function(){return this.changeAll(true)},deselectAll:function(){return this.changeAll(false)},toggle:function(G){G=G||window.event;if(G){G.stopPropagation()}this.$button.trigger("click")},keydown:function(Q){var R=F(this),J=R.is("input")?R.parent().parent():R.parent(),G,X=J.data("this"),K,U,I,W,V,S,H,Y,M=":not(.disabled, .hidden, .dropdown-header, .divider)",P={32:" ",48:"0",49:"1",50:"2",51:"3",52:"4",53:"5",54:"6",55:"7",56:"8",57:"9",59:";",65:"a",66:"b",67:"c",68:"d",69:"e",70:"f",71:"g",72:"h",73:"i",74:"j",75:"k",76:"l",77:"m",78:"n",79:"o",80:"p",81:"q",82:"r",83:"s",84:"t",85:"u",86:"v",87:"w",88:"x",89:"y",90:"z",96:"0",97:"1",98:"2",99:"3",100:"4",101:"5",102:"6",103:"7",104:"8",105:"9"};if(X.options.liveSearch){J=R.parent().parent()}if(X.options.container){J=X.$menu}G=F("[role=menu] li",J);Y=X.$newElement.hasClass("open");if(!Y&&(Q.keyCode>=48&&Q.keyCode<=57||Q.keyCode>=96&&Q.keyCode<=105||Q.keyCode>=65&&Q.keyCode<=90)){if(!X.options.container){X.setSize();X.$menu.parent().addClass("open");Y=true}else{X.$button.trigger("click")}X.$searchbox.focus()}if(X.options.liveSearch){if(/(^9$|27)/.test(Q.keyCode.toString(10))&&Y&&X.$menu.find(".active").length===0){Q.preventDefault();X.$menu.parent().removeClass("open");if(X.options.container){X.$newElement.removeClass("open")}X.$button.focus()}G=F("[role=menu] li"+M,J);if(!R.val()&&!/(38|40)/.test(Q.keyCode.toString(10))){if(G.filter(".active").length===0){G=X.$menuInner.find("li");if(X.options.liveSearchNormalize){G=G.filter(":a"+X._searchStyle()+"("+B(P[Q.keyCode])+")")}else{G=G.filter(":"+X._searchStyle()+"("+P[Q.keyCode]+")")}}}}if(!G.length){return}if(/(38|40)/.test(Q.keyCode.toString(10))){K=G.index(G.find("a").filter(":focus").parent());I=G.filter(M).first().index();W=G.filter(M).last().index();U=G.eq(K).nextAll(M).eq(0).index();V=G.eq(K).prevAll(M).eq(0).index();S=G.eq(U).prevAll(M).eq(0).index();if(X.options.liveSearch){G.each(function(Z){if(!F(this).hasClass("disabled")){F(this).data("index",Z)}});K=G.index(G.filter(".active"));I=G.first().data("index");W=G.last().data("index");U=G.eq(K).nextAll().eq(0).data("index");V=G.eq(K).prevAll().eq(0).data("index");S=G.eq(U).prevAll().eq(0).data("index")}H=R.data("prevIndex");if(Q.keyCode==38){if(X.options.liveSearch){K--}if(K!=S&&K>V){K=V}if(K<I){K=I}if(K==H){K=W}}else{if(Q.keyCode==40){if(X.options.liveSearch){K++}if(K==-1){K=0}if(K!=S&&K<U){K=U}if(K>W){K=W}if(K==H){K=I}}}R.data("prevIndex",K);if(!X.options.liveSearch){G.eq(K).children("a").focus()}else{Q.preventDefault();if(!R.hasClass("dropdown-toggle")){G.removeClass("active").eq(K).addClass("active").children("a").focus();R.focus()}}}else{if(!R.is("input")){var T=[],L,O;G.each(function(){if(!F(this).hasClass("disabled")){if(F.trim(F(this).children("a").text().toLowerCase()).substring(0,1)==P[Q.keyCode]){T.push(F(this).index())}}});L=F(document).data("keycount");L++;F(document).data("keycount",L);O=F.trim(F(":focus").text().toLowerCase()).substring(0,1);if(O!=P[Q.keyCode]){L=1;F(document).data("keycount",L)}else{if(L>=T.length){F(document).data("keycount",0);if(L>T.length){L=1}}}G.eq(T[L-1]).children("a").focus()}}if((/(13|32)/.test(Q.keyCode.toString(10))||(/(^9$)/.test(Q.keyCode.toString(10))&&X.options.selectOnTab))&&Y){if(!/(32)/.test(Q.keyCode.toString(10))){Q.preventDefault()}if(!X.options.liveSearch){var N=F(":focus");N.click();N.focus();Q.preventDefault();F(document).data("spaceSelect",true)}else{if(!/(32)/.test(Q.keyCode.toString(10))){X.$menuInner.find(".active a").click();R.focus()}}F(document).data("keycount",0)}if((/(^9$|27)/.test(Q.keyCode.toString(10))&&Y&&(X.multiple||X.options.liveSearch))||(/(27)/.test(Q.keyCode.toString(10))&&!Y)){X.$menu.parent().removeClass("open");if(X.options.container){X.$newElement.removeClass("open")}X.$button.focus()}},mobile:function(){this.$element.addClass("mobile-device")},refresh:function(){this.$lis=null;this.liObj={};this.reloadLi();this.render();this.checkDisabled();this.liHeight(true);this.setStyle();this.setWidth();if(this.$lis){this.$searchbox.trigger("propertychange")}this.$element.trigger("refreshed.bs.select")},hide:function(){this.$newElement.hide()},show:function(){this.$newElement.show()},remove:function(){this.$newElement.remove();this.$element.remove()},destroy:function(){this.$newElement.before(this.$element).remove();if(this.$bsContainer){this.$bsContainer.remove()}else{this.$menu.remove()}this.$element.off(".bs.select").removeData("selectpicker").removeClass("bs-select-hidden selectpicker")}};function C(H,K){var M=arguments;var L=H,I=K;[].shift.apply(M);var G;var J=this.each(function(){var O=F(this);if(O.is("select")){var R=O.data("selectpicker"),P=typeof L=="object"&&L;if(!R){var Q=F.extend({},E.DEFAULTS,F.fn.selectpicker.defaults||{},O.data(),P);Q.template=F.extend({},E.DEFAULTS.template,(F.fn.selectpicker.defaults?F.fn.selectpicker.defaults.template:{}),O.data().template,P.template);O.data("selectpicker",(R=new E(this,Q,I)))}else{if(P){for(var N in P){if(P.hasOwnProperty(N)){R.options[N]=P[N]}}}}if(typeof L=="string"){if(R[L] instanceof Function){G=R[L].apply(R,M)}else{G=R.options[L]}}}});if(typeof G!=="undefined"){return G}else{return J}}var A=F.fn.selectpicker;F.fn.selectpicker=C;F.fn.selectpicker.Constructor=E;F.fn.selectpicker.noConflict=function(){F.fn.selectpicker=A;return this};F(document).data("keycount",0).on("keydown.bs.select",'.bootstrap-select [data-toggle=dropdown], .bootstrap-select [role="menu"], .bs-searchbox input',E.prototype.keydown).on("focusin.modal",'.bootstrap-select [data-toggle=dropdown], .bootstrap-select [role="menu"], .bs-searchbox input',function(G){G.stopPropagation()});F(window).on("load.bs.select.data-api",function(){F(".selectpicker").each(function(){var G=F(this);C.call(G,G.data())})})})(jQuery);