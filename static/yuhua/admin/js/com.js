$(".menu-wrapper .menu-item .con").click(function(){if($(this).hasClass("ac")){$(this).removeClass("ac");$(this).parent().find(".menu-li-wrapper").stop().slideUp(300)}else{$(".menu-wrapper .menu-item .con").removeClass("ac");$(".menu-item .menu-li-wrapper").stop().slideUp(300);$(this).addClass("ac");$(this).parent().find(".menu-li-wrapper").stop().slideDown(300)}});$(".left-wrapper .menu-item").click(function(){if($(this).find(".menu-li").length==0){$(".left-wrapper .menu-item .menu-li").removeClass("on");$(this).addClass("on").siblings().removeClass("on")}if($(this).children(".menu-li-wrapper").length<=0){$("#sunday_index_right_title").text($(this).find(".text").text())}});$(".left-wrapper .menu-li").click(function(){$(".left-wrapper .menu-item .menu-li").removeClass("on");$(".left-wrapper .menu-item").removeClass("on");$(this).addClass("on");var A=$(this).text();var B=$(this).parent().parent().find(".text").text();$("#sunday_index_right_title").text(B+" > "+A)});function radio(B,A){$(B).on("click",A,function(){$(this).addClass("on").siblings().removeClass("on")})}function checkbox(B,A){$(B).on("click",A,function(){if($(this).hasClass("on")){$(this).removeClass("on")}else{$(this).addClass("on")}})}function checkAll(B,A){$(B).click(function(){if($(this).hasClass("on")){$(this).removeClass("on");$(A).removeClass("on")}else{$(this).addClass("on");if($(B+".on").size()==$(B).size()){$(A).addClass("on")}}});$(A).click(function(){if($(this).hasClass("on")){$(this).removeClass("on");$(B).removeClass("on")}else{$(this).addClass("on");$(B).addClass("on")}})};