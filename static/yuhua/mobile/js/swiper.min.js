!function(){function B(H){H.fn.swiper=function(J){var I;return H(this).each(function(){var K=new F(this,J);I||(I=K)}),I}}var G,F=function(J,Ap){function Ai(H){return Math.floor(H)}function Aq(){Au.autoplayTimeoutId=setTimeout(function(){Au.params.loop?(Au.fixLoop(),Au._slideNext(),Au.emit("onAutoplay",Au)):Au.isEnd?Ap.autoplayStopOnLast?Au.stopAutoplay():(Au._slideTo(0),Au.emit("onAutoplay",Au)):(Au._slideNext(),Au.emit("onAutoplay",Au))},Au.params.autoplay)}function Ar(I,M){var H=G(I.target);if(!H.is(M)){if("string"==typeof M){H=H.parents(M)}else{if(M.nodeType){var L;return H.parents().each(function(N,O){O===M&&(L=M)}),L?M:void 0}}}if(0!==H.length){return H[0]}}function As(I,M){M=M||{};var L=window.MutationObserver||window.WebkitMutationObserver,H=new L(function(N){N.forEach(function(O){Au.onResize(!0),Au.emit("onObserverUpdate",Au,O)})});H.observe(I,{attributes:"undefined"==typeof M.attributes?!0:M.attributes,childList:"undefined"==typeof M.childList?!0:M.childList,characterData:"undefined"==typeof M.characterData?!0:M.characterData}),Au.observers.push(H)}function Aj(H){H.originalEvent&&(H=H.originalEvent);var T=H.keyCode||H.charCode;if(!Au.params.allowSwipeToNext&&(Au.isHorizontal()&&39===T||!Au.isHorizontal()&&40===T)){return !1}if(!Au.params.allowSwipeToPrev&&(Au.isHorizontal()&&37===T||!Au.isHorizontal()&&38===T)){return !1}if(!(H.shiftKey||H.altKey||H.ctrlKey||H.metaKey||document.activeElement&&document.activeElement.nodeName&&("input"===document.activeElement.nodeName.toLowerCase()||"textarea"===document.activeElement.nodeName.toLowerCase()))){if(37===T||39===T||38===T||40===T){var N=!1;if(Au.container.parents(".swiper-slide").length>0&&0===Au.container.parents(".swiper-slide-active").length){return}var M={left:window.pageXOffset,top:window.pageYOffset},O=window.innerWidth,I=window.innerHeight,P=Au.container.offset();Au.rtl&&(P.left=P.left-Au.container[0].scrollLeft);for(var R=[[P.left,P.top],[P.left+Au.width,P.top],[P.left,P.top+Au.height],[P.left+Au.width,P.top+Au.height]],S=0;S<R.length;S++){var L=R[S];L[0]>=M.left&&L[0]<=M.left+O&&L[1]>=M.top&&L[1]<=M.top+I&&(N=!0)}if(!N){return}}Au.isHorizontal()?((37===T||39===T)&&(H.preventDefault?H.preventDefault():H.returnValue=!1),(39===T&&!Au.rtl||37===T&&Au.rtl)&&Au.slideNext(),(37===T&&!Au.rtl||39===T&&Au.rtl)&&Au.slidePrev()):((38===T||40===T)&&(H.preventDefault?H.preventDefault():H.returnValue=!1),40===T&&Au.slideNext(),38===T&&Au.slidePrev())}}function Ay(I){I.originalEvent&&(I=I.originalEvent);var P=Au.mousewheel.event,O=0,H=Au.rtl?-1:1;if("mousewheel"===P){if(Au.params.mousewheelForceToAxis){if(Au.isHorizontal()){if(!(Math.abs(I.wheelDeltaX)>Math.abs(I.wheelDeltaY))){return}O=I.wheelDeltaX*H}else{if(!(Math.abs(I.wheelDeltaY)>Math.abs(I.wheelDeltaX))){return}O=I.wheelDeltaY}}else{O=Math.abs(I.wheelDeltaX)>Math.abs(I.wheelDeltaY)?-I.wheelDeltaX*H:-I.wheelDeltaY}}else{if("DOMMouseScroll"===P){O=-I.detail}else{if("wheel"===P){if(Au.params.mousewheelForceToAxis){if(Au.isHorizontal()){if(!(Math.abs(I.deltaX)>Math.abs(I.deltaY))){return}O=-I.deltaX*H}else{if(!(Math.abs(I.deltaY)>Math.abs(I.deltaX))){return}O=-I.deltaY}}else{O=Math.abs(I.deltaX)>Math.abs(I.deltaY)?-I.deltaX*H:-I.deltaY}}}}if(0!==O){if(Au.params.mousewheelInvert&&(O=-O),Au.params.freeMode){var L=Au.getWrapperTranslate()+O*Au.params.mousewheelSensitivity,M=Au.isBeginning,N=Au.isEnd;if(L>=Au.minTranslate()&&(L=Au.minTranslate()),L<=Au.maxTranslate()&&(L=Au.maxTranslate()),Au.setWrapperTransition(0),Au.setWrapperTranslate(L),Au.updateProgress(),Au.updateActiveIndex(),(!M&&Au.isBeginning||!N&&Au.isEnd)&&Au.updateClasses(),Au.params.freeModeSticky?(clearTimeout(Au.mousewheel.timeout),Au.mousewheel.timeout=setTimeout(function(){Au.slideReset()},300)):Au.params.lazyLoading&&Au.lazy&&Au.lazy.load(),0===L||L===Au.maxTranslate()){return}}else{if((new window.Date).getTime()-Au.mousewheel.lastScrollTime>60){if(0>O){if(Au.isEnd&&!Au.params.loop||Au.animating){if(Au.params.mousewheelReleaseOnEdges){return !0}}else{Au.slideNext()}}else{if(Au.isBeginning&&!Au.params.loop||Au.animating){if(Au.params.mousewheelReleaseOnEdges){return !0}}else{Au.slidePrev()}}}Au.mousewheel.lastScrollTime=(new window.Date).getTime()}return Au.params.autoplay&&Au.stopAutoplay(),I.preventDefault?I.preventDefault():I.returnValue=!1,!1}}function Am(I,O){I=G(I);var H,L,M,N=Au.rtl?-1:1;H=I.attr("data-swiper-parallax")||"0",L=I.attr("data-swiper-parallax-x"),M=I.attr("data-swiper-parallax-y"),L||M?(L=L||"0",M=M||"0"):Au.isHorizontal()?(L=H,M="0"):(M=H,L="0"),L=L.indexOf("%")>=0?parseInt(L,10)*O*N+"%":L*O*N+"px",M=M.indexOf("%")>=0?parseInt(M,10)*O+"%":M*O+"px",I.transform("translate3d("+L+", "+M+",0px)")}function Av(H){return 0!==H.indexOf("on")&&(H=H[0]!==H[0].toUpperCase()?"on"+H[0].toUpperCase()+H.substring(1):"on"+H),H}if(!(this instanceof F)){return new F(J,Ap)}var At={direction:"horizontal",touchEventsTarget:"container",initialSlide:0,speed:300,autoplay:!1,autoplayDisableOnInteraction:!0,autoplayStopOnLast:!1,iOSEdgeSwipeDetection:!1,iOSEdgeSwipeThreshold:20,freeMode:!1,freeModeMomentum:!0,freeModeMomentumRatio:1,freeModeMomentumBounce:!0,freeModeMomentumBounceRatio:1,freeModeSticky:!1,freeModeMinimumVelocity:0.02,autoHeight:!1,setWrapperSize:!1,virtualTranslate:!1,effect:"slide",coverflow:{rotate:50,stretch:0,depth:100,modifier:1,slideShadows:!0},flip:{slideShadows:!0,limitRotation:!0},cube:{slideShadows:!0,shadow:!0,shadowOffset:20,shadowScale:0.94},fade:{crossFade:!1},parallax:!1,scrollbar:null,scrollbarHide:!0,scrollbarDraggable:!1,scrollbarSnapOnRelease:!1,keyboardControl:!1,mousewheelControl:!1,mousewheelReleaseOnEdges:!1,mousewheelInvert:!1,mousewheelForceToAxis:!1,mousewheelSensitivity:1,hashnav:!1,breakpoints:void 0,spaceBetween:0,slidesPerView:1,slidesPerColumn:1,slidesPerColumnFill:"column",slidesPerGroup:1,centeredSlides:!1,slidesOffsetBefore:0,slidesOffsetAfter:0,roundLengths:!1,touchRatio:1,touchAngle:45,simulateTouch:!0,shortSwipes:!0,longSwipes:!0,longSwipesRatio:0.5,longSwipesMs:300,followFinger:!0,onlyExternal:!1,threshold:0,touchMoveStopPropagation:!0,uniqueNavElements:!0,pagination:null,paginationElement:"span",paginationClickable:!1,paginationHide:!1,paginationBulletRender:null,paginationProgressRender:null,paginationFractionRender:null,paginationCustomRender:null,paginationType:"bullets",resistance:!0,resistanceRatio:0.85,nextButton:null,prevButton:null,watchSlidesProgress:!1,watchSlidesVisibility:!1,grabCursor:!1,preventClicks:!0,preventClicksPropagation:!0,slideToClickedSlide:!1,lazyLoading:!1,lazyLoadingInPrevNext:!1,lazyLoadingInPrevNextAmount:1,lazyLoadingOnTransitionStart:!1,preloadImages:!0,updateOnImagesReady:!0,loop:!1,loopAdditionalSlides:0,loopedSlides:null,control:void 0,controlInverse:!1,controlBy:"slide",allowSwipeToPrev:!0,allowSwipeToNext:!0,swipeHandler:null,noSwiping:!0,noSwipingClass:"swiper-no-swiping",slideClass:"swiper-slide",slideActiveClass:"swiper-slide-active",slideVisibleClass:"swiper-slide-visible",slideDuplicateClass:"swiper-slide-duplicate",slideNextClass:"swiper-slide-next",slidePrevClass:"swiper-slide-prev",wrapperClass:"swiper-wrapper",bulletClass:"swiper-pagination-bullet",bulletActiveClass:"swiper-pagination-bullet-active",buttonDisabledClass:"swiper-button-disabled",paginationCurrentClass:"swiper-pagination-current",paginationTotalClass:"swiper-pagination-total",paginationHiddenClass:"swiper-pagination-hidden",paginationProgressbarClass:"swiper-pagination-progressbar",observer:!1,observeParents:!1,a11y:!1,prevSlideMessage:"Previous slide",nextSlideMessage:"Next slide",firstSlideMessage:"This is the first slide",lastSlideMessage:"This is the last slide",paginationBulletMessage:"Go to slide {{index}}",runCallbacksOnInit:!0},Ao=Ap&&Ap.virtualTranslate;Ap=Ap||{};var Aw={};for(var Ax in Ap){if("object"!=typeof Ap[Ax]||null===Ap[Ax]||(Ap[Ax].nodeType||Ap[Ax]===window||Ap[Ax]===document||"undefined"!=typeof A&&Ap[Ax] instanceof A||"undefined"!=typeof jQuery&&Ap[Ax] instanceof jQuery)){Aw[Ax]=Ap[Ax]}else{Aw[Ax]={};for(var Ak in Ap[Ax]){Aw[Ax][Ak]=Ap[Ax][Ak]}}}for(var Al in At){if("undefined"==typeof Ap[Al]){Ap[Al]=At[Al]}else{if("object"==typeof Ap[Al]){for(var Ah in At[Al]){"undefined"==typeof Ap[Al][Ah]&&(Ap[Al][Ah]=At[Al][Ah])}}}}var Au=this;if(Au.params=Ap,Au.originalParams=Aw,Au.classNames=[],"undefined"!=typeof G&&"undefined"!=typeof A&&(G=A),("undefined"!=typeof G||(G="undefined"==typeof A?window.Dom7||window.Zepto||window.jQuery:A))&&(Au.$=G,Au.currentBreakpoint=void 0,Au.getActiveBreakpoint=function(){if(!Au.params.breakpoints){return !1}var I,M=!1,L=[];for(I in Au.params.breakpoints){Au.params.breakpoints.hasOwnProperty(I)&&L.push(I)}L.sort(function(N,O){return parseInt(N,10)>parseInt(O,10)});for(var H=0;H<L.length;H++){I=L[H],I>=window.innerWidth&&!M&&(M=I)}return M||"max"},Au.setBreakpoint=function(){var I=Au.getActiveBreakpoint();if(I&&Au.currentBreakpoint!==I){var M=I in Au.params.breakpoints?Au.params.breakpoints[I]:Au.originalParams,L=Au.params.loop&&M.slidesPerView!==Au.params.slidesPerView;for(var H in M){Au.params[H]=M[H]}Au.currentBreakpoint=I,L&&Au.destroyLoop&&Au.reLoop(!0)}},Au.params.breakpoints&&Au.setBreakpoint(),Au.container=G(J),0!==Au.container.length)){if(Au.container.length>1){var Ag=[];return Au.container.each(function(){Ag.push(new F(this,Ap))}),Ag}Au.container[0].swiper=Au,Au.container.data("swiper",Au),Au.classNames.push("swiper-container-"+Au.params.direction),Au.params.freeMode&&Au.classNames.push("swiper-container-free-mode"),Au.support.flexbox||(Au.classNames.push("swiper-container-no-flexbox"),Au.params.slidesPerColumn=1),Au.params.autoHeight&&Au.classNames.push("swiper-container-autoheight"),(Au.params.parallax||Au.params.watchSlidesVisibility)&&(Au.params.watchSlidesProgress=!0),["cube","coverflow","flip"].indexOf(Au.params.effect)>=0&&(Au.support.transforms3d?(Au.params.watchSlidesProgress=!0,Au.classNames.push("swiper-container-3d")):Au.params.effect="slide"),"slide"!==Au.params.effect&&Au.classNames.push("swiper-container-"+Au.params.effect),"cube"===Au.params.effect&&(Au.params.resistanceRatio=0,Au.params.slidesPerView=1,Au.params.slidesPerColumn=1,Au.params.slidesPerGroup=1,Au.params.centeredSlides=!1,Au.params.spaceBetween=0,Au.params.virtualTranslate=!0,Au.params.setWrapperSize=!1),("fade"===Au.params.effect||"flip"===Au.params.effect)&&(Au.params.slidesPerView=1,Au.params.slidesPerColumn=1,Au.params.slidesPerGroup=1,Au.params.watchSlidesProgress=!0,Au.params.spaceBetween=0,Au.params.setWrapperSize=!1,"undefined"==typeof Ao&&(Au.params.virtualTranslate=!0)),Au.params.grabCursor&&Au.support.touch&&(Au.params.grabCursor=!1),Au.wrapper=Au.container.children("."+Au.params.wrapperClass),Au.params.pagination&&(Au.paginationContainer=G(Au.params.pagination),Au.params.uniqueNavElements&&"string"==typeof Au.params.pagination&&Au.paginationContainer.length>1&&1===Au.container.find(Au.params.pagination).length&&(Au.paginationContainer=Au.container.find(Au.params.pagination)),"bullets"===Au.params.paginationType&&Au.params.paginationClickable?Au.paginationContainer.addClass("swiper-pagination-clickable"):Au.params.paginationClickable=!1,Au.paginationContainer.addClass("swiper-pagination-"+Au.params.paginationType)),(Au.params.nextButton||Au.params.prevButton)&&(Au.params.nextButton&&(Au.nextButton=G(Au.params.nextButton),Au.params.uniqueNavElements&&"string"==typeof Au.params.nextButton&&Au.nextButton.length>1&&1===Au.container.find(Au.params.nextButton).length&&(Au.nextButton=Au.container.find(Au.params.nextButton))),Au.params.prevButton&&(Au.prevButton=G(Au.params.prevButton),Au.params.uniqueNavElements&&"string"==typeof Au.params.prevButton&&Au.prevButton.length>1&&1===Au.container.find(Au.params.prevButton).length&&(Au.prevButton=Au.container.find(Au.params.prevButton)))),Au.isHorizontal=function(){return"horizontal"===Au.params.direction},Au.rtl=Au.isHorizontal()&&("rtl"===Au.container[0].dir.toLowerCase()||"rtl"===Au.container.css("direction")),Au.rtl&&Au.classNames.push("swiper-container-rtl"),Au.rtl&&(Au.wrongRTL="-webkit-box"===Au.wrapper.css("display")),Au.params.slidesPerColumn>1&&Au.classNames.push("swiper-container-multirow"),Au.device.android&&Au.classNames.push("swiper-container-android"),Au.container.addClass(Au.classNames.join(" ")),Au.translate=0,Au.progress=0,Au.velocity=0,Au.lockSwipeToNext=function(){Au.params.allowSwipeToNext=!1},Au.lockSwipeToPrev=function(){Au.params.allowSwipeToPrev=!1},Au.lockSwipes=function(){Au.params.allowSwipeToNext=Au.params.allowSwipeToPrev=!1},Au.unlockSwipeToNext=function(){Au.params.allowSwipeToNext=!0},Au.unlockSwipeToPrev=function(){Au.params.allowSwipeToPrev=!0},Au.unlockSwipes=function(){Au.params.allowSwipeToNext=Au.params.allowSwipeToPrev=!0},Au.params.grabCursor&&(Au.container[0].style.cursor="move",Au.container[0].style.cursor="-webkit-grab",Au.container[0].style.cursor="-moz-grab",Au.container[0].style.cursor="grab"),Au.imagesToLoad=[],Au.imagesLoaded=0,Au.loadImage=function(I,P,O,H,L){function M(){L&&L()}var N;I.complete&&H?M():P?(N=new window.Image,N.onload=M,N.onerror=M,O&&(N.srcset=O),P&&(N.src=P)):M()},Au.preloadImages=function(){function H(){"undefined"!=typeof Au&&null!==Au&&(void 0!==Au.imagesLoaded&&Au.imagesLoaded++,Au.imagesLoaded===Au.imagesToLoad.length&&(Au.params.updateOnImagesReady&&Au.update(),Au.emit("onImagesReady",Au)))}Au.imagesToLoad=Au.container.find("img");for(var I=0;I<Au.imagesToLoad.length;I++){Au.loadImage(Au.imagesToLoad[I],Au.imagesToLoad[I].currentSrc||Au.imagesToLoad[I].getAttribute("src"),Au.imagesToLoad[I].srcset||Au.imagesToLoad[I].getAttribute("srcset"),!0,H)}},Au.autoplayTimeoutId=void 0,Au.autoplaying=!1,Au.autoplayPaused=!1,Au.startAutoplay=function(){return"undefined"!=typeof Au.autoplayTimeoutId?!1:Au.params.autoplay?Au.autoplaying?!1:(Au.autoplaying=!0,Au.emit("onAutoplayStart",Au),void Aq()):!1},Au.stopAutoplay=function(H){Au.autoplayTimeoutId&&(Au.autoplayTimeoutId&&clearTimeout(Au.autoplayTimeoutId),Au.autoplaying=!1,Au.autoplayTimeoutId=void 0,Au.emit("onAutoplayStop",Au))},Au.pauseAutoplay=function(H){Au.autoplayPaused||(Au.autoplayTimeoutId&&clearTimeout(Au.autoplayTimeoutId),Au.autoplayPaused=!0,0===H?(Au.autoplayPaused=!1,Aq()):Au.wrapper.transitionEnd(function(){Au&&(Au.autoplayPaused=!1,Au.autoplaying?Aq():Au.stopAutoplay())}))},Au.minTranslate=function(){return -Au.snapGrid[0]},Au.maxTranslate=function(){return -Au.snapGrid[Au.snapGrid.length-1]},Au.updateAutoHeight=function(){var H=Au.slides.eq(Au.activeIndex)[0];if("undefined"!=typeof H){var I=H.offsetHeight;I&&Au.wrapper.css("height",I+"px")}},Au.updateContainerSize=function(){var H,I;H="undefined"!=typeof Au.params.width?Au.params.width:Au.container[0].clientWidth,I="undefined"!=typeof Au.params.height?Au.params.height:Au.container[0].clientHeight,0===H&&Au.isHorizontal()||0===I&&!Au.isHorizontal()||(H=H-parseInt(Au.container.css("padding-left"),10)-parseInt(Au.container.css("padding-right"),10),I=I-parseInt(Au.container.css("padding-top"),10)-parseInt(Au.container.css("padding-bottom"),10),Au.width=H,Au.height=I,Au.size=Au.isHorizontal()?Au.width:Au.height)},Au.updateSlidesSize=function(){Au.slides=Au.wrapper.children("."+Au.params.slideClass),Au.snapGrid=[],Au.slidesGrid=[],Au.slidesSizesGrid=[];var H,g=Au.params.spaceBetween,M=-Au.params.slidesOffsetBefore,L=0,P=0;if("undefined"!=typeof Au.size){"string"==typeof g&&g.indexOf("%")>=0&&(g=parseFloat(g.replace("%",""))/100*Au.size),Au.virtualSize=-g,Au.rtl?Au.slides.css({marginLeft:"",marginTop:""}):Au.slides.css({marginRight:"",marginBottom:""});var R;Au.params.slidesPerColumn>1&&(R=Math.floor(Au.slides.length/Au.params.slidesPerColumn)===Au.slides.length/Au.params.slidesPerColumn?Au.slides.length:Math.ceil(Au.slides.length/Au.params.slidesPerColumn)*Au.params.slidesPerColumn,"auto"!==Au.params.slidesPerView&&"row"===Au.params.slidesPerColumnFill&&(R=Math.max(R,Au.params.slidesPerView*Au.params.slidesPerColumn)));var S,T=Au.params.slidesPerColumn,I=R/T,s=I-(Au.params.slidesPerColumn*I-Au.slides.length);for(H=0;H<Au.slides.length;H++){S=0;var N=Au.slides.eq(H);if(Au.params.slidesPerColumn>1){var b,W,O;"column"===Au.params.slidesPerColumnFill?(W=Math.floor(H/T),O=H-W*T,(W>s||W===s&&O===T-1)&&++O>=T&&(O=0,W++),b=W+O*R/T,N.css({"-webkit-box-ordinal-group":b,"-moz-box-ordinal-group":b,"-ms-flex-order":b,"-webkit-order":b,order:b})):(O=Math.floor(H/I),W=H-O*I),N.css({"margin-top":0!==O&&Au.params.spaceBetween&&Au.params.spaceBetween+"px"}).attr("data-swiper-column",W).attr("data-swiper-row",O)}"none"!==N.css("display")&&("auto"===Au.params.slidesPerView?(S=Au.isHorizontal()?N.outerWidth(!0):N.outerHeight(!0),Au.params.roundLengths&&(S=Ai(S))):(S=(Au.size-(Au.params.slidesPerView-1)*g)/Au.params.slidesPerView,Au.params.roundLengths&&(S=Ai(S)),Au.isHorizontal()?Au.slides[H].style.width=S+"px":Au.slides[H].style.height=S+"px"),Au.slides[H].swiperSlideSize=S,Au.slidesSizesGrid.push(S),Au.params.centeredSlides?(M=M+S/2+L/2+g,0===H&&(M=M-Au.size/2-g),Math.abs(M)<0.001&&(M=0),P%Au.params.slidesPerGroup===0&&Au.snapGrid.push(M),Au.slidesGrid.push(M)):(P%Au.params.slidesPerGroup===0&&Au.snapGrid.push(M),Au.slidesGrid.push(M),M=M+S+g),Au.virtualSize+=S+g,L=S,P++)}Au.virtualSize=Math.max(Au.virtualSize,Au.size)+Au.params.slidesOffsetAfter;var k;if(Au.rtl&&Au.wrongRTL&&("slide"===Au.params.effect||"coverflow"===Au.params.effect)&&Au.wrapper.css({width:Au.virtualSize+Au.params.spaceBetween+"px"}),(!Au.support.flexbox||Au.params.setWrapperSize)&&(Au.isHorizontal()?Au.wrapper.css({width:Au.virtualSize+Au.params.spaceBetween+"px"}):Au.wrapper.css({height:Au.virtualSize+Au.params.spaceBetween+"px"})),Au.params.slidesPerColumn>1&&(Au.virtualSize=(S+Au.params.spaceBetween)*R,Au.virtualSize=Math.ceil(Au.virtualSize/Au.params.slidesPerColumn)-Au.params.spaceBetween,Au.wrapper.css({width:Au.virtualSize+Au.params.spaceBetween+"px"}),Au.params.centeredSlides)){for(k=[],H=0;H<Au.snapGrid.length;H++){Au.snapGrid[H]<Au.virtualSize+Au.snapGrid[0]&&k.push(Au.snapGrid[H])}Au.snapGrid=k}if(!Au.params.centeredSlides){for(k=[],H=0;H<Au.snapGrid.length;H++){Au.snapGrid[H]<=Au.virtualSize-Au.size&&k.push(Au.snapGrid[H])}Au.snapGrid=k,Math.floor(Au.virtualSize-Au.size)-Math.floor(Au.snapGrid[Au.snapGrid.length-1])>1&&Au.snapGrid.push(Au.virtualSize-Au.size)}0===Au.snapGrid.length&&(Au.snapGrid=[0]),0!==Au.params.spaceBetween&&(Au.isHorizontal()?Au.rtl?Au.slides.css({marginLeft:g+"px"}):Au.slides.css({marginRight:g+"px"}):Au.slides.css({marginBottom:g+"px"})),Au.params.watchSlidesProgress&&Au.updateSlidesOffset()}},Au.updateSlidesOffset=function(){for(var H=0;H<Au.slides.length;H++){Au.slides[H].swiperSlideOffset=Au.isHorizontal()?Au.slides[H].offsetLeft:Au.slides[H].offsetTop}},Au.updateSlidesProgress=function(H){if("undefined"==typeof H&&(H=Au.translate||0),0!==Au.slides.length){"undefined"==typeof Au.slides[0].swiperSlideOffset&&Au.updateSlidesOffset();var R=-H;Au.rtl&&(R=H),Au.slides.removeClass(Au.params.slideVisibleClass);for(var M=0;M<Au.slides.length;M++){var L=Au.slides[M],N=(R-L.swiperSlideOffset)/(L.swiperSlideSize+Au.params.spaceBetween);if(Au.params.watchSlidesVisibility){var I=-(R-L.swiperSlideOffset),O=I+Au.slidesSizesGrid[M],P=I>=0&&I<Au.size||O>0&&O<=Au.size||0>=I&&O>=Au.size;P&&Au.slides.eq(M).addClass(Au.params.slideVisibleClass)}L.progress=Au.rtl?-N:N}}},Au.updateProgress=function(I){"undefined"==typeof I&&(I=Au.translate||0);var M=Au.maxTranslate()-Au.minTranslate(),L=Au.isBeginning,H=Au.isEnd;0===M?(Au.progress=0,Au.isBeginning=Au.isEnd=!0):(Au.progress=(I-Au.minTranslate())/M,Au.isBeginning=Au.progress<=0,Au.isEnd=Au.progress>=1),Au.isBeginning&&!L&&Au.emit("onReachBeginning",Au),Au.isEnd&&!H&&Au.emit("onReachEnd",Au),Au.params.watchSlidesProgress&&Au.updateSlidesProgress(I),Au.emit("onProgress",Au,Au.progress)},Au.updateActiveIndex=function(){var I,M,L,H=Au.rtl?Au.translate:-Au.translate;for(M=0;M<Au.slidesGrid.length;M++){"undefined"!=typeof Au.slidesGrid[M+1]?H>=Au.slidesGrid[M]&&H<Au.slidesGrid[M+1]-(Au.slidesGrid[M+1]-Au.slidesGrid[M])/2?I=M:H>=Au.slidesGrid[M]&&H<Au.slidesGrid[M+1]&&(I=M+1):H>=Au.slidesGrid[M]&&(I=M)}(0>I||"undefined"==typeof I)&&(I=0),L=Math.floor(I/Au.params.slidesPerGroup),L>=Au.snapGrid.length&&(L=Au.snapGrid.length-1),I!==Au.activeIndex&&(Au.snapIndex=L,Au.previousIndex=Au.activeIndex,Au.activeIndex=I,Au.updateClasses())},Au.updateClasses=function(){Au.slides.removeClass(Au.params.slideActiveClass+" "+Au.params.slideNextClass+" "+Au.params.slidePrevClass);var H=Au.slides.eq(Au.activeIndex);H.addClass(Au.params.slideActiveClass);var M=H.next("."+Au.params.slideClass).addClass(Au.params.slideNextClass);Au.params.loop&&0===M.length&&Au.slides.eq(0).addClass(Au.params.slideNextClass);var L=H.prev("."+Au.params.slideClass).addClass(Au.params.slidePrevClass);if(Au.params.loop&&0===L.length&&Au.slides.eq(-1).addClass(Au.params.slidePrevClass),Au.paginationContainer&&Au.paginationContainer.length>0){var N,I=Au.params.loop?Math.ceil((Au.slides.length-2*Au.loopedSlides)/Au.params.slidesPerGroup):Au.snapGrid.length;if(Au.params.loop?(N=Math.ceil((Au.activeIndex-Au.loopedSlides)/Au.params.slidesPerGroup),N>Au.slides.length-1-2*Au.loopedSlides&&(N-=Au.slides.length-2*Au.loopedSlides),N>I-1&&(N-=I),0>N&&"bullets"!==Au.params.paginationType&&(N=I+N)):N="undefined"!=typeof Au.snapIndex?Au.snapIndex:Au.activeIndex||0,"bullets"===Au.params.paginationType&&Au.bullets&&Au.bullets.length>0&&(Au.bullets.removeClass(Au.params.bulletActiveClass),Au.paginationContainer.length>1?Au.bullets.each(function(){G(this).index()===N&&G(this).addClass(Au.params.bulletActiveClass)}):Au.bullets.eq(N).addClass(Au.params.bulletActiveClass)),"fraction"===Au.params.paginationType&&(Au.paginationContainer.find("."+Au.params.paginationCurrentClass).text(N+1),Au.paginationContainer.find("."+Au.params.paginationTotalClass).text(I)),"progress"===Au.params.paginationType){var O=(N+1)/I,P=O,R=1;Au.isHorizontal()||(R=O,P=1),Au.paginationContainer.find("."+Au.params.paginationProgressbarClass).transform("translate3d(0,0,0) scaleX("+P+") scaleY("+R+")").transition(Au.params.speed)}"custom"===Au.params.paginationType&&Au.params.paginationCustomRender&&(Au.paginationContainer.html(Au.params.paginationCustomRender(Au,N+1,I)),Au.emit("onPaginationRendered",Au,Au.paginationContainer[0]))}Au.params.loop||(Au.params.prevButton&&Au.prevButton&&Au.prevButton.length>0&&(Au.isBeginning?(Au.prevButton.addClass(Au.params.buttonDisabledClass),Au.params.a11y&&Au.a11y&&Au.a11y.disable(Au.prevButton)):(Au.prevButton.removeClass(Au.params.buttonDisabledClass),Au.params.a11y&&Au.a11y&&Au.a11y.enable(Au.prevButton))),Au.params.nextButton&&Au.nextButton&&Au.nextButton.length>0&&(Au.isEnd?(Au.nextButton.addClass(Au.params.buttonDisabledClass),Au.params.a11y&&Au.a11y&&Au.a11y.disable(Au.nextButton)):(Au.nextButton.removeClass(Au.params.buttonDisabledClass),Au.params.a11y&&Au.a11y&&Au.a11y.enable(Au.nextButton))))},Au.updatePagination=function(){if(Au.params.pagination&&Au.paginationContainer&&Au.paginationContainer.length>0){var H="";if("bullets"===Au.params.paginationType){for(var L=Au.params.loop?Math.ceil((Au.slides.length-2*Au.loopedSlides)/Au.params.slidesPerGroup):Au.snapGrid.length,I=0;L>I;I++){H+=Au.params.paginationBulletRender?Au.params.paginationBulletRender(I,Au.params.bulletClass):"<"+Au.params.paginationElement+' class="'+Au.params.bulletClass+'"></'+Au.params.paginationElement+">"}Au.paginationContainer.html(H),Au.bullets=Au.paginationContainer.find("."+Au.params.bulletClass),Au.params.paginationClickable&&Au.params.a11y&&Au.a11y&&Au.a11y.initPagination()}"fraction"===Au.params.paginationType&&(H=Au.params.paginationFractionRender?Au.params.paginationFractionRender(Au,Au.params.paginationCurrentClass,Au.params.paginationTotalClass):'<span class="'+Au.params.paginationCurrentClass+'"></span> / <span class="'+Au.params.paginationTotalClass+'"></span>',Au.paginationContainer.html(H)),"progress"===Au.params.paginationType&&(H=Au.params.paginationProgressRender?Au.params.paginationProgressRender(Au,Au.params.paginationProgressbarClass):'<span class="'+Au.params.paginationProgressbarClass+'"></span>',Au.paginationContainer.html(H)),"custom"!==Au.params.paginationType&&Au.emit("onPaginationRendered",Au,Au.paginationContainer[0])}},Au.update=function(I){function M(){H=Math.min(Math.max(Au.translate,Au.maxTranslate()),Au.minTranslate()),Au.setWrapperTranslate(H),Au.updateActiveIndex(),Au.updateClasses()}if(Au.updateContainerSize(),Au.updateSlidesSize(),Au.updateProgress(),Au.updatePagination(),Au.updateClasses(),Au.params.scrollbar&&Au.scrollbar&&Au.scrollbar.set(),I){var L,H;Au.controller&&Au.controller.spline&&(Au.controller.spline=void 0),Au.params.freeMode?(M(),Au.params.autoHeight&&Au.updateAutoHeight()):(L=("auto"===Au.params.slidesPerView||Au.params.slidesPerView>1)&&Au.isEnd&&!Au.params.centeredSlides?Au.slideTo(Au.slides.length-1,0,!1,!0):Au.slideTo(Au.activeIndex,0,!1,!0),L||M())}else{Au.params.autoHeight&&Au.updateAutoHeight()}},Au.onResize=function(I){Au.params.breakpoints&&Au.setBreakpoint();var N=Au.params.allowSwipeToPrev,M=Au.params.allowSwipeToNext;Au.params.allowSwipeToPrev=Au.params.allowSwipeToNext=!0,Au.updateContainerSize(),Au.updateSlidesSize(),("auto"===Au.params.slidesPerView||Au.params.freeMode||I)&&Au.updatePagination(),Au.params.scrollbar&&Au.scrollbar&&Au.scrollbar.set(),Au.controller&&Au.controller.spline&&(Au.controller.spline=void 0);var H=!1;if(Au.params.freeMode){var L=Math.min(Math.max(Au.translate,Au.maxTranslate()),Au.minTranslate());Au.setWrapperTranslate(L),Au.updateActiveIndex(),Au.updateClasses(),Au.params.autoHeight&&Au.updateAutoHeight()}else{Au.updateClasses(),H=("auto"===Au.params.slidesPerView||Au.params.slidesPerView>1)&&Au.isEnd&&!Au.params.centeredSlides?Au.slideTo(Au.slides.length-1,0,!1,!0):Au.slideTo(Au.activeIndex,0,!1,!0)}Au.params.lazyLoading&&!H&&Au.lazy&&Au.lazy.load(),Au.params.allowSwipeToPrev=N,Au.params.allowSwipeToNext=M};var X=["mousedown","mousemove","mouseup"];window.navigator.pointerEnabled?X=["pointerdown","pointermove","pointerup"]:window.navigator.msPointerEnabled&&(X=["MSPointerDown","MSPointerMove","MSPointerUp"]),Au.touchEvents={start:Au.support.touch||!Au.params.simulateTouch?"touchstart":X[0],move:Au.support.touch||!Au.params.simulateTouch?"touchmove":X[1],end:Au.support.touch||!Au.params.simulateTouch?"touchend":X[2]},(window.navigator.pointerEnabled||window.navigator.msPointerEnabled)&&("container"===Au.params.touchEventsTarget?Au.container:Au.wrapper).addClass("swiper-wp8-"+Au.params.direction),Au.initEvents=function(I){var O=I?"off":"on",N=I?"removeEventListener":"addEventListener",H="container"===Au.params.touchEventsTarget?Au.container[0]:Au.wrapper[0],L=Au.support.touch?H:document,M=Au.params.nested?!0:!1;Au.browser.ie?(H[N](Au.touchEvents.start,Au.onTouchStart,!1),L[N](Au.touchEvents.move,Au.onTouchMove,M),L[N](Au.touchEvents.end,Au.onTouchEnd,!1)):(Au.support.touch&&(H[N](Au.touchEvents.start,Au.onTouchStart,!1),H[N](Au.touchEvents.move,Au.onTouchMove,M),H[N](Au.touchEvents.end,Au.onTouchEnd,!1)),!Ap.simulateTouch||Au.device.ios||Au.device.android||(H[N]("mousedown",Au.onTouchStart,!1),document[N]("mousemove",Au.onTouchMove,M),document[N]("mouseup",Au.onTouchEnd,!1))),window[N]("resize",Au.onResize),Au.params.nextButton&&Au.nextButton&&Au.nextButton.length>0&&(Au.nextButton[O]("click",Au.onClickNext),Au.params.a11y&&Au.a11y&&Au.nextButton[O]("keydown",Au.a11y.onEnterKey)),Au.params.prevButton&&Au.prevButton&&Au.prevButton.length>0&&(Au.prevButton[O]("click",Au.onClickPrev),Au.params.a11y&&Au.a11y&&Au.prevButton[O]("keydown",Au.a11y.onEnterKey)),Au.params.pagination&&Au.params.paginationClickable&&(Au.paginationContainer[O]("click","."+Au.params.bulletClass,Au.onClickIndex),Au.params.a11y&&Au.a11y&&Au.paginationContainer[O]("keydown","."+Au.params.bulletClass,Au.a11y.onEnterKey)),(Au.params.preventClicks||Au.params.preventClicksPropagation)&&H[N]("click",Au.preventClicks,!0)},Au.attachEvents=function(){Au.initEvents()},Au.detachEvents=function(){Au.initEvents(!0)},Au.allowClick=!0,Au.preventClicks=function(H){Au.allowClick||(Au.params.preventClicks&&H.preventDefault(),Au.params.preventClicksPropagation&&Au.animating&&(H.stopPropagation(),H.stopImmediatePropagation()))},Au.onClickNext=function(H){H.preventDefault(),(!Au.isEnd||Au.params.loop)&&Au.slideNext()},Au.onClickPrev=function(H){H.preventDefault(),(!Au.isBeginning||Au.params.loop)&&Au.slidePrev()},Au.onClickIndex=function(H){H.preventDefault();var I=G(this).index()*Au.params.slidesPerGroup;Au.params.loop&&(I+=Au.loopedSlides),Au.slideTo(I)},Au.updateClickedSlide=function(I){var O=Ar(I,"."+Au.params.slideClass),H=!1;if(O){for(var L=0;L<Au.slides.length;L++){Au.slides[L]===O&&(H=!0)}}if(!O||!H){return Au.clickedSlide=void 0,void (Au.clickedIndex=void 0)}if(Au.clickedSlide=O,Au.clickedIndex=G(O).index(),Au.params.slideToClickedSlide&&void 0!==Au.clickedIndex&&Au.clickedIndex!==Au.activeIndex){var M,N=Au.clickedIndex;if(Au.params.loop){if(Au.animating){return}M=G(Au.clickedSlide).attr("data-swiper-slide-index"),Au.params.centeredSlides?N<Au.loopedSlides-Au.params.slidesPerView/2||N>Au.slides.length-Au.loopedSlides+Au.params.slidesPerView/2?(Au.fixLoop(),N=Au.wrapper.children("."+Au.params.slideClass+'[data-swiper-slide-index="'+M+'"]:not(.swiper-slide-duplicate)').eq(0).index(),setTimeout(function(){Au.slideTo(N)},0)):Au.slideTo(N):N>Au.slides.length-Au.params.slidesPerView?(Au.fixLoop(),N=Au.wrapper.children("."+Au.params.slideClass+'[data-swiper-slide-index="'+M+'"]:not(.swiper-slide-duplicate)').eq(0).index(),setTimeout(function(){Au.slideTo(N)},0)):Au.slideTo(N)}else{Au.slideTo(N)}}};var Q,Aa,Af,r,Ae,U,An,Z,q,t,Ad="input, select, textarea, button",Y=Date.now(),Ab=[];Au.animating=!1,Au.touches={startX:0,startY:0,currentX:0,currentY:0,diff:0};var Ac,j;if(Au.onTouchStart=function(I){if(I.originalEvent&&(I=I.originalEvent),Ac="touchstart"===I.type,Ac||!("which" in I)||3!==I.which){if(Au.params.noSwiping&&Ar(I,"."+Au.params.noSwipingClass)){return void (Au.allowClick=!0)}if(!Au.params.swipeHandler||Ar(I,Au.params.swipeHandler)){var M=Au.touches.currentX="touchstart"===I.type?I.targetTouches[0].pageX:I.pageX,H=Au.touches.currentY="touchstart"===I.type?I.targetTouches[0].pageY:I.pageY;if(!(Au.device.ios&&Au.params.iOSEdgeSwipeDetection&&M<=Au.params.iOSEdgeSwipeThreshold)){if(Q=!0,Aa=!1,Af=!0,Ae=void 0,j=void 0,Au.touches.startX=M,Au.touches.startY=H,r=Date.now(),Au.allowClick=!0,Au.updateContainerSize(),Au.swipeDirection=void 0,Au.params.threshold>0&&(Z=!1),"touchstart"!==I.type){var L=!0;G(I.target).is(Ad)&&(L=!1),document.activeElement&&G(document.activeElement).is(Ad)&&document.activeElement.blur(),L&&I.preventDefault()}Au.emit("onTouchStart",Au,I)}}}},Au.onTouchMove=function(I){if(I.originalEvent&&(I=I.originalEvent),!Ac||"mousemove"!==I.type){if(I.preventedByNestedSwiper){return Au.touches.startX="touchmove"===I.type?I.targetTouches[0].pageX:I.pageX,void (Au.touches.startY="touchmove"===I.type?I.targetTouches[0].pageY:I.pageY)}if(Au.params.onlyExternal){return Au.allowClick=!1,void (Q&&(Au.touches.startX=Au.touches.currentX="touchmove"===I.type?I.targetTouches[0].pageX:I.pageX,Au.touches.startY=Au.touches.currentY="touchmove"===I.type?I.targetTouches[0].pageY:I.pageY,r=Date.now()))}if(Ac&&document.activeElement&&I.target===document.activeElement&&G(I.target).is(Ad)){return Aa=!0,void (Au.allowClick=!1)}if(Af&&Au.emit("onTouchMove",Au,I),!(I.targetTouches&&I.targetTouches.length>1)){if(Au.touches.currentX="touchmove"===I.type?I.targetTouches[0].pageX:I.pageX,Au.touches.currentY="touchmove"===I.type?I.targetTouches[0].pageY:I.pageY,"undefined"==typeof Ae){var M=180*Math.atan2(Math.abs(Au.touches.currentY-Au.touches.startY),Math.abs(Au.touches.currentX-Au.touches.startX))/Math.PI;Ae=Au.isHorizontal()?M>Au.params.touchAngle:90-M>Au.params.touchAngle}if(Ae&&Au.emit("onTouchMoveOpposite",Au,I),"undefined"==typeof j&&Au.browser.ieTouch&&(Au.touches.currentX!==Au.touches.startX||Au.touches.currentY!==Au.touches.startY)&&(j=!0),Q){if(Ae){return void (Q=!1)}if(j||!Au.browser.ieTouch){Au.allowClick=!1,Au.emit("onSliderMove",Au,I),I.preventDefault(),Au.params.touchMoveStopPropagation&&!Au.params.nested&&I.stopPropagation(),Aa||(Ap.loop&&Au.fixLoop(),An=Au.getWrapperTranslate(),Au.setWrapperTransition(0),Au.animating&&Au.wrapper.trigger("webkitTransitionEnd transitionend oTransitionEnd MSTransitionEnd msTransitionEnd"),Au.params.autoplay&&Au.autoplaying&&(Au.params.autoplayDisableOnInteraction?Au.stopAutoplay():Au.pauseAutoplay()),t=!1,Au.params.grabCursor&&(Au.container[0].style.cursor="move",Au.container[0].style.cursor="-webkit-grabbing",Au.container[0].style.cursor="-moz-grabbin",Au.container[0].style.cursor="grabbing")),Aa=!0;var H=Au.touches.diff=Au.isHorizontal()?Au.touches.currentX-Au.touches.startX:Au.touches.currentY-Au.touches.startY;H*=Au.params.touchRatio,Au.rtl&&(H=-H),Au.swipeDirection=H>0?"prev":"next",U=H+An;var L=!0;if(H>0&&U>Au.minTranslate()?(L=!1,Au.params.resistance&&(U=Au.minTranslate()-1+Math.pow(-Au.minTranslate()+An+H,Au.params.resistanceRatio))):0>H&&U<Au.maxTranslate()&&(L=!1,Au.params.resistance&&(U=Au.maxTranslate()+1-Math.pow(Au.maxTranslate()-An-H,Au.params.resistanceRatio))),L&&(I.preventedByNestedSwiper=!0),!Au.params.allowSwipeToNext&&"next"===Au.swipeDirection&&An>U&&(U=An),!Au.params.allowSwipeToPrev&&"prev"===Au.swipeDirection&&U>An&&(U=An),Au.params.followFinger){if(Au.params.threshold>0){if(!(Math.abs(H)>Au.params.threshold||Z)){return void (U=An)}if(!Z){return Z=!0,Au.touches.startX=Au.touches.currentX,Au.touches.startY=Au.touches.currentY,U=An,void (Au.touches.diff=Au.isHorizontal()?Au.touches.currentX-Au.touches.startX:Au.touches.currentY-Au.touches.startY)}}(Au.params.freeMode||Au.params.watchSlidesProgress)&&Au.updateActiveIndex(),Au.params.freeMode&&(0===Ab.length&&Ab.push({position:Au.touches[Au.isHorizontal()?"startX":"startY"],time:r}),Ab.push({position:Au.touches[Au.isHorizontal()?"currentX":"currentY"],time:(new window.Date).getTime()})),Au.updateProgress(U),Au.setWrapperTranslate(U)}}}}}},Au.onTouchEnd=function(AC){if(AC.originalEvent&&(AC=AC.originalEvent),Af&&Au.emit("onTouchEnd",Au,AC),Af=!1,Q){Au.params.grabCursor&&Aa&&Q&&(Au.container[0].style.cursor="move",Au.container[0].style.cursor="-webkit-grab",Au.container[0].style.cursor="-moz-grab",Au.container[0].style.cursor="grab");var O=Date.now(),H=O-r;if(Au.allowClick&&(Au.updateClickedSlide(AC),Au.emit("onTap",Au,AC),300>H&&O-Y>300&&(q&&clearTimeout(q),q=setTimeout(function(){Au&&(Au.params.paginationHide&&Au.paginationContainer.length>0&&!G(AC.target).hasClass(Au.params.bulletClass)&&Au.paginationContainer.toggleClass(Au.params.paginationHiddenClass),Au.emit("onClick",Au,AC))},300)),300>H&&300>O-Y&&(q&&clearTimeout(q),Au.emit("onDoubleTap",Au,AC))),Y=Date.now(),setTimeout(function(){Au&&(Au.allowClick=!0)},0),!Q||!Aa||!Au.swipeDirection||0===Au.touches.diff||U===An){return void (Q=Aa=!1)}Q=Aa=!1;var S;if(S=Au.params.followFinger?Au.rtl?Au.translate:-Au.translate:-U,Au.params.freeMode){if(S<-Au.minTranslate()){return void Au.slideTo(Au.activeIndex)}if(S>-Au.maxTranslate()){return void (Au.slides.length<Au.snapGrid.length?Au.slideTo(Au.snapGrid.length-1):Au.slideTo(Au.slides.length-1))}if(Au.params.freeModeMomentum){if(Ab.length>1){var I=Ab.pop(),T=Ab.pop(),W=I.position-T.position,b=I.time-T.time;Au.velocity=W/b,Au.velocity=Au.velocity/2,Math.abs(Au.velocity)<Au.params.freeModeMinimumVelocity&&(Au.velocity=0),(b>150||(new window.Date).getTime()-I.time>300)&&(Au.velocity=0)}else{Au.velocity=0}Ab.length=0;var L=1000*Au.params.freeModeMomentumRatio,AB=Au.velocity*L,P=Au.translate+AB;Au.rtl&&(P=-P);var z,k=!1,R=20*Math.abs(Au.velocity)*Au.params.freeModeMomentumBounceRatio;if(P<Au.maxTranslate()){Au.params.freeModeMomentumBounce?(P+Au.maxTranslate()<-R&&(P=Au.maxTranslate()-R),z=Au.maxTranslate(),k=!0,t=!0):P=Au.maxTranslate()}else{if(P>Au.minTranslate()){Au.params.freeModeMomentumBounce?(P-Au.minTranslate()>R&&(P=Au.minTranslate()+R),z=Au.minTranslate(),k=!0,t=!0):P=Au.minTranslate()}else{if(Au.params.freeModeSticky){var Az,AA=0;for(AA=0;AA<Au.snapGrid.length;AA+=1){if(Au.snapGrid[AA]>-P){Az=AA;break}}P=Math.abs(Au.snapGrid[Az]-P)<Math.abs(Au.snapGrid[Az-1]-P)||"next"===Au.swipeDirection?Au.snapGrid[Az]:Au.snapGrid[Az-1],Au.rtl||(P=-P)}}}if(0!==Au.velocity){L=Au.rtl?Math.abs((-P-Au.translate)/Au.velocity):Math.abs((P-Au.translate)/Au.velocity)}else{if(Au.params.freeModeSticky){return void Au.slideReset()}}Au.params.freeModeMomentumBounce&&k?(Au.updateProgress(z),Au.setWrapperTransition(L),Au.setWrapperTranslate(P),Au.onTransitionStart(),Au.animating=!0,Au.wrapper.transitionEnd(function(){Au&&t&&(Au.emit("onMomentumBounce",Au),Au.setWrapperTransition(Au.params.speed),Au.setWrapperTranslate(z),Au.wrapper.transitionEnd(function(){Au&&Au.onTransitionEnd()}))})):Au.velocity?(Au.updateProgress(P),Au.setWrapperTransition(L),Au.setWrapperTranslate(P),Au.onTransitionStart(),Au.animating||(Au.animating=!0,Au.wrapper.transitionEnd(function(){Au&&Au.onTransitionEnd()}))):Au.updateProgress(P),Au.updateActiveIndex()}return void ((!Au.params.freeModeMomentum||H>=Au.params.longSwipesMs)&&(Au.updateProgress(),Au.updateActiveIndex()))}var M,N=0,AE=Au.slidesSizesGrid[0];for(M=0;M<Au.slidesGrid.length;M+=Au.params.slidesPerGroup){"undefined"!=typeof Au.slidesGrid[M+Au.params.slidesPerGroup]?S>=Au.slidesGrid[M]&&S<Au.slidesGrid[M+Au.params.slidesPerGroup]&&(N=M,AE=Au.slidesGrid[M+Au.params.slidesPerGroup]-Au.slidesGrid[M]):S>=Au.slidesGrid[M]&&(N=M,AE=Au.slidesGrid[Au.slidesGrid.length-1]-Au.slidesGrid[Au.slidesGrid.length-2])}var AD=(S-Au.slidesGrid[N])/AE;if(H>Au.params.longSwipesMs){if(!Au.params.longSwipes){return void Au.slideTo(Au.activeIndex)}"next"===Au.swipeDirection&&(AD>=Au.params.longSwipesRatio?Au.slideTo(N+Au.params.slidesPerGroup):Au.slideTo(N)),"prev"===Au.swipeDirection&&(AD>1-Au.params.longSwipesRatio?Au.slideTo(N+Au.params.slidesPerGroup):Au.slideTo(N))}else{if(!Au.params.shortSwipes){return void Au.slideTo(Au.activeIndex)}"next"===Au.swipeDirection&&Au.slideTo(N+Au.params.slidesPerGroup),"prev"===Au.swipeDirection&&Au.slideTo(N)}}},Au._slideTo=function(H,I){return Au.slideTo(H,I,!0,!0)},Au.slideTo=function(I,O,N,H){"undefined"==typeof N&&(N=!0),"undefined"==typeof I&&(I=0),0>I&&(I=0),Au.snapIndex=Math.floor(I/Au.params.slidesPerGroup),Au.snapIndex>=Au.snapGrid.length&&(Au.snapIndex=Au.snapGrid.length-1);var L=-Au.snapGrid[Au.snapIndex];Au.params.autoplay&&Au.autoplaying&&(H||!Au.params.autoplayDisableOnInteraction?Au.pauseAutoplay(O):Au.stopAutoplay()),Au.updateProgress(L);for(var M=0;M<Au.slidesGrid.length;M++){-Math.floor(100*L)>=Math.floor(100*Au.slidesGrid[M])&&(I=M)}return !Au.params.allowSwipeToNext&&L<Au.translate&&L<Au.minTranslate()?!1:!Au.params.allowSwipeToPrev&&L>Au.translate&&L>Au.maxTranslate()&&(Au.activeIndex||0)!==I?!1:("undefined"==typeof O&&(O=Au.params.speed),Au.previousIndex=Au.activeIndex||0,Au.activeIndex=I,Au.rtl&&-L===Au.translate||!Au.rtl&&L===Au.translate?(Au.params.autoHeight&&Au.updateAutoHeight(),Au.updateClasses(),"slide"!==Au.params.effect&&Au.setWrapperTranslate(L),!1):(Au.updateClasses(),Au.onTransitionStart(N),0===O?(Au.setWrapperTranslate(L),Au.setWrapperTransition(0),Au.onTransitionEnd(N)):(Au.setWrapperTranslate(L),Au.setWrapperTransition(O),Au.animating||(Au.animating=!0,Au.wrapper.transitionEnd(function(){Au&&Au.onTransitionEnd(N)}))),!0))},Au.onTransitionStart=function(H){"undefined"==typeof H&&(H=!0),Au.params.autoHeight&&Au.updateAutoHeight(),Au.lazy&&Au.lazy.onTransitionStart(),H&&(Au.emit("onTransitionStart",Au),Au.activeIndex!==Au.previousIndex&&(Au.emit("onSlideChangeStart",Au),Au.activeIndex>Au.previousIndex?Au.emit("onSlideNextStart",Au):Au.emit("onSlidePrevStart",Au)))},Au.onTransitionEnd=function(H){Au.animating=!1,Au.setWrapperTransition(0),"undefined"==typeof H&&(H=!0),Au.lazy&&Au.lazy.onTransitionEnd(),H&&(Au.emit("onTransitionEnd",Au),Au.activeIndex!==Au.previousIndex&&(Au.emit("onSlideChangeEnd",Au),Au.activeIndex>Au.previousIndex?Au.emit("onSlideNextEnd",Au):Au.emit("onSlidePrevEnd",Au))),Au.params.hashnav&&Au.hashnav&&Au.hashnav.setHash()},Au.slideNext=function(H,L,I){if(Au.params.loop){if(Au.animating){return !1}Au.fixLoop();Au.container[0].clientLeft;return Au.slideTo(Au.activeIndex+Au.params.slidesPerGroup,L,H,I)}return Au.slideTo(Au.activeIndex+Au.params.slidesPerGroup,L,H,I)},Au._slideNext=function(H){return Au.slideNext(!0,H,!0)},Au.slidePrev=function(H,L,I){if(Au.params.loop){if(Au.animating){return !1}Au.fixLoop();Au.container[0].clientLeft;return Au.slideTo(Au.activeIndex-1,L,H,I)}return Au.slideTo(Au.activeIndex-1,L,H,I)},Au._slidePrev=function(H){return Au.slidePrev(!0,H,!0)},Au.slideReset=function(H,L,I){return Au.slideTo(Au.activeIndex,L,H)},Au.setWrapperTransition=function(H,I){Au.wrapper.transition(H),"slide"!==Au.params.effect&&Au.effects[Au.params.effect]&&Au.effects[Au.params.effect].setTransition(H),Au.params.parallax&&Au.parallax&&Au.parallax.setTransition(H),Au.params.scrollbar&&Au.scrollbar&&Au.scrollbar.setTransition(H),Au.params.control&&Au.controller&&Au.controller.setTransition(H,I),Au.emit("onSetTransition",Au,H)},Au.setWrapperTranslate=function(H,R,L){var I=0,M=0,N=0;Au.isHorizontal()?I=Au.rtl?-H:H:M=H,Au.params.roundLengths&&(I=Ai(I),M=Ai(M)),Au.params.virtualTranslate||(Au.support.transforms3d?Au.wrapper.transform("translate3d("+I+"px, "+M+"px, "+N+"px)"):Au.wrapper.transform("translate("+I+"px, "+M+"px)")),Au.translate=Au.isHorizontal()?I:M;var O,P=Au.maxTranslate()-Au.minTranslate();O=0===P?0:(H-Au.minTranslate())/P,O!==Au.progress&&Au.updateProgress(H),R&&Au.updateActiveIndex(),"slide"!==Au.params.effect&&Au.effects[Au.params.effect]&&Au.effects[Au.params.effect].setTranslate(Au.translate),Au.params.parallax&&Au.parallax&&Au.parallax.setTranslate(Au.translate),Au.params.scrollbar&&Au.scrollbar&&Au.scrollbar.setTranslate(Au.translate),Au.params.control&&Au.controller&&Au.controller.setTranslate(Au.translate,L),Au.emit("onSetTranslate",Au,Au.translate)},Au.getTranslate=function(I,O){var N,H,L,M;return"undefined"==typeof O&&(O="x"),Au.params.virtualTranslate?Au.rtl?-Au.translate:Au.translate:(L=window.getComputedStyle(I,null),window.WebKitCSSMatrix?(H=L.transform||L.webkitTransform,H.split(",").length>6&&(H=H.split(", ").map(function(P){return P.replace(",",".")}).join(", ")),M=new window.WebKitCSSMatrix("none"===H?"":H)):(M=L.MozTransform||L.OTransform||L.MsTransform||L.msTransform||L.transform||L.getPropertyValue("transform").replace("translate(","matrix(1, 0, 0, 1,"),N=M.toString().split(",")),"x"===O&&(H=window.WebKitCSSMatrix?M.m41:16===N.length?parseFloat(N[12]):parseFloat(N[4])),"y"===O&&(H=window.WebKitCSSMatrix?M.m42:16===N.length?parseFloat(N[13]):parseFloat(N[5])),Au.rtl&&H&&(H=-H),H||0)},Au.getWrapperTranslate=function(H){return"undefined"==typeof H&&(H=Au.isHorizontal()?"x":"y"),Au.getTranslate(Au.wrapper[0],H)},Au.observers=[],Au.initObservers=function(){if(Au.params.observeParents){for(var H=Au.container.parents(),I=0;I<H.length;I++){As(H[I])}}As(Au.container[0],{childList:!1}),As(Au.wrapper[0],{attributes:!1})},Au.disconnectObservers=function(){for(var H=0;H<Au.observers.length;H++){Au.observers[H].disconnect()}Au.observers=[]},Au.createLoop=function(){Au.wrapper.children("."+Au.params.slideClass+"."+Au.params.slideDuplicateClass).remove();var I=Au.wrapper.children("."+Au.params.slideClass);"auto"!==Au.params.slidesPerView||Au.params.loopedSlides||(Au.params.loopedSlides=I.length),Au.loopedSlides=parseInt(Au.params.loopedSlides||Au.params.slidesPerView,10),Au.loopedSlides=Au.loopedSlides+Au.params.loopAdditionalSlides,Au.loopedSlides>I.length&&(Au.loopedSlides=I.length);var M,H=[],L=[];for(I.each(function(P,N){var O=G(this);P<Au.loopedSlides&&L.push(N),P<I.length&&P>=I.length-Au.loopedSlides&&H.push(N),O.attr("data-swiper-slide-index",P)}),M=0;M<L.length;M++){Au.wrapper.append(G(L[M].cloneNode(!0)).addClass(Au.params.slideDuplicateClass))}for(M=H.length-1;M>=0;M--){Au.wrapper.prepend(G(H[M].cloneNode(!0)).addClass(Au.params.slideDuplicateClass))}},Au.destroyLoop=function(){Au.wrapper.children("."+Au.params.slideClass+"."+Au.params.slideDuplicateClass).remove(),Au.slides.removeAttr("data-swiper-slide-index")},Au.reLoop=function(H){var I=Au.activeIndex-Au.loopedSlides;Au.destroyLoop(),Au.createLoop(),Au.updateSlidesSize(),H&&Au.slideTo(I+Au.loopedSlides,0,!1)},Au.fixLoop=function(){var H;Au.activeIndex<Au.loopedSlides?(H=Au.slides.length-3*Au.loopedSlides+Au.activeIndex,H+=Au.loopedSlides,Au.slideTo(H,0,!1,!0)):("auto"===Au.params.slidesPerView&&Au.activeIndex>=2*Au.loopedSlides||Au.activeIndex>Au.slides.length-2*Au.params.slidesPerView)&&(H=-Au.slides.length+Au.activeIndex+Au.loopedSlides,H+=Au.loopedSlides,Au.slideTo(H,0,!1,!0))},Au.appendSlide=function(H){if(Au.params.loop&&Au.destroyLoop(),"object"==typeof H&&H.length){for(var I=0;I<H.length;I++){H[I]&&Au.wrapper.append(H[I])}}else{Au.wrapper.append(H)}Au.params.loop&&Au.createLoop(),Au.params.observer&&Au.support.observer||Au.update(!0)},Au.prependSlide=function(H){Au.params.loop&&Au.destroyLoop();var L=Au.activeIndex+1;if("object"==typeof H&&H.length){for(var I=0;I<H.length;I++){H[I]&&Au.wrapper.prepend(H[I])}L=Au.activeIndex+H.length}else{Au.wrapper.prepend(H)}Au.params.loop&&Au.createLoop(),Au.params.observer&&Au.support.observer||Au.update(!0),Au.slideTo(L,0,!1)},Au.removeSlide=function(I){Au.params.loop&&(Au.destroyLoop(),Au.slides=Au.wrapper.children("."+Au.params.slideClass));var M,L=Au.activeIndex;if("object"==typeof I&&I.length){for(var H=0;H<I.length;H++){M=I[H],Au.slides[M]&&Au.slides.eq(M).remove(),L>M&&L--}L=Math.max(L,0)}else{M=I,Au.slides[M]&&Au.slides.eq(M).remove(),L>M&&L--,L=Math.max(L,0)}Au.params.loop&&Au.createLoop(),Au.params.observer&&Au.support.observer||Au.update(!0),Au.params.loop?Au.slideTo(L+Au.loopedSlides,0,!1):Au.slideTo(L,0,!1)},Au.removeAllSlides=function(){for(var H=[],I=0;I<Au.slides.length;I++){H.push(I)}Au.removeSlide(H)},Au.effects={fade:{setTranslate:function(){for(var I=0;I<Au.slides.length;I++){var O=Au.slides.eq(I),N=O[0].swiperSlideOffset,H=-N;Au.params.virtualTranslate||(H-=Au.translate);var L=0;Au.isHorizontal()||(L=H,H=0);var M=Au.params.fade.crossFade?Math.max(1-Math.abs(O[0].progress),0):1+Math.min(Math.max(O[0].progress,-1),0);O.css({opacity:M}).transform("translate3d("+H+"px, "+L+"px, 0px)")}},setTransition:function(H){if(Au.slides.transition(H),Au.params.virtualTranslate&&0!==H){var I=!1;Au.slides.transitionEnd(function(){if(!I&&Au){I=!0,Au.animating=!1;for(var L=["webkitTransitionEnd","transitionend","oTransitionEnd","MSTransitionEnd","msTransitionEnd"],M=0;M<L.length;M++){Au.wrapper.trigger(L[M])}}})}}},flip:{setTranslate:function(){for(var H=0;H<Au.slides.length;H++){var N=Au.slides.eq(H),M=N[0].progress;Au.params.flip.limitRotation&&(M=Math.max(Math.min(N[0].progress,1),-1));var P=N[0].swiperSlideOffset,I=-180*M,R=I,S=0,T=-P,L=0;if(Au.isHorizontal()?Au.rtl&&(R=-R):(L=T,T=0,S=-R,R=0),N[0].style.zIndex=-Math.abs(Math.round(M))+Au.slides.length,Au.params.flip.slideShadows){var W=Au.isHorizontal()?N.find(".swiper-slide-shadow-left"):N.find(".swiper-slide-shadow-top"),O=Au.isHorizontal()?N.find(".swiper-slide-shadow-right"):N.find(".swiper-slide-shadow-bottom");0===W.length&&(W=G('<div class="swiper-slide-shadow-'+(Au.isHorizontal()?"left":"top")+'"></div>'),N.append(W)),0===O.length&&(O=G('<div class="swiper-slide-shadow-'+(Au.isHorizontal()?"right":"bottom")+'"></div>'),N.append(O)),W.length&&(W[0].style.opacity=Math.max(-M,0)),O.length&&(O[0].style.opacity=Math.max(M,0))}N.transform("translate3d("+T+"px, "+L+"px, 0px) rotateX("+S+"deg) rotateY("+R+"deg)")}},setTransition:function(H){if(Au.slides.transition(H).find(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").transition(H),Au.params.virtualTranslate&&0!==H){var I=!1;Au.slides.eq(Au.activeIndex).transitionEnd(function(){if(!I&&Au&&G(this).hasClass(Au.params.slideActiveClass)){I=!0,Au.animating=!1;for(var M=["webkitTransitionEnd","transitionend","oTransitionEnd","MSTransitionEnd","msTransitionEnd"],L=0;L<M.length;L++){Au.wrapper.trigger(M[L])}}})}}},cube:{setTranslate:function(){var AB,O=0;Au.params.cube.shadow&&(Au.isHorizontal()?(AB=Au.wrapper.find(".swiper-cube-shadow"),0===AB.length&&(AB=G('<div class="swiper-cube-shadow"></div>'),Au.wrapper.append(AB)),AB.css({height:Au.width+"px"})):(AB=Au.container.find(".swiper-cube-shadow"),0===AB.length&&(AB=G('<div class="swiper-cube-shadow"></div>'),Au.container.append(AB))));for(var H=0;H<Au.slides.length;H++){var S=Au.slides.eq(H),I=90*H,T=Math.floor(I/360);Au.rtl&&(I=-I,T=Math.floor(-I/360));var W=Math.max(Math.min(S[0].progress,1),-1),b=0,L=0,AA=0;H%4===0?(b=4*-T*Au.size,AA=0):(H-1)%4===0?(b=0,AA=4*-T*Au.size):(H-2)%4===0?(b=Au.size+4*T*Au.size,AA=Au.size):(H-3)%4===0&&(b=-Au.size,AA=3*Au.size+4*Au.size*T),Au.rtl&&(b=-b),Au.isHorizontal()||(L=b,b=0);var P="rotateX("+(Au.isHorizontal()?0:-I)+"deg) rotateY("+(Au.isHorizontal()?I:0)+"deg) translate3d("+b+"px, "+L+"px, "+AA+"px)";if(1>=W&&W>-1&&(O=90*H+90*W,Au.rtl&&(O=90*-H-90*W)),S.transform(P),Au.params.cube.slideShadows){var x=Au.isHorizontal()?S.find(".swiper-slide-shadow-left"):S.find(".swiper-slide-shadow-top"),k=Au.isHorizontal()?S.find(".swiper-slide-shadow-right"):S.find(".swiper-slide-shadow-bottom");0===x.length&&(x=G('<div class="swiper-slide-shadow-'+(Au.isHorizontal()?"left":"top")+'"></div>'),S.append(x)),0===k.length&&(k=G('<div class="swiper-slide-shadow-'+(Au.isHorizontal()?"right":"bottom")+'"></div>'),S.append(k)),x.length&&(x[0].style.opacity=Math.max(-W,0)),k.length&&(k[0].style.opacity=Math.max(W,0))}}if(Au.wrapper.css({"-webkit-transform-origin":"50% 50% -"+Au.size/2+"px","-moz-transform-origin":"50% 50% -"+Au.size/2+"px","-ms-transform-origin":"50% 50% -"+Au.size/2+"px","transform-origin":"50% 50% -"+Au.size/2+"px"}),Au.params.cube.shadow){if(Au.isHorizontal()){AB.transform("translate3d(0px, "+(Au.width/2+Au.params.cube.shadowOffset)+"px, "+-Au.width/2+"px) rotateX(90deg) rotateZ(0deg) scale("+Au.params.cube.shadowScale+")")}else{var R=Math.abs(O)-90*Math.floor(Math.abs(O)/90),z=1.5-(Math.sin(2*R*Math.PI/360)/2+Math.cos(2*R*Math.PI/360)/2),Az=Au.params.cube.shadowScale,M=Au.params.cube.shadowScale/z,N=Au.params.cube.shadowOffset;AB.transform("scale3d("+Az+", 1, "+M+") translate3d(0px, "+(Au.height/2+N)+"px, "+-Au.height/2/M+"px) rotateX(-90deg)")}}var AC=Au.isSafari||Au.isUiWebView?-Au.size/2:0;Au.wrapper.transform("translate3d(0px,0,"+AC+"px) rotateX("+(Au.isHorizontal()?0:O)+"deg) rotateY("+(Au.isHorizontal()?-O:0)+"deg)")},setTransition:function(H){Au.slides.transition(H).find(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").transition(H),Au.params.cube.shadow&&!Au.isHorizontal()&&Au.container.find(".swiper-cube-shadow").transition(H)}},coverflow:{setTranslate:function(){for(var AB=Au.translate,O=Au.isHorizontal()?-AB+Au.width/2:-AB+Au.height/2,H=Au.isHorizontal()?Au.params.coverflow.rotate:-Au.params.coverflow.rotate,S=Au.params.coverflow.depth,I=0,T=Au.slides.length;T>I;I++){var W=Au.slides.eq(I),b=Au.slidesSizesGrid[I],L=W[0].swiperSlideOffset,AA=(O-L-b/2)/b*Au.params.coverflow.modifier,P=Au.isHorizontal()?H*AA:0,x=Au.isHorizontal()?0:H*AA,k=-S*Math.abs(AA),R=Au.isHorizontal()?0:Au.params.coverflow.stretch*AA,z=Au.isHorizontal()?Au.params.coverflow.stretch*AA:0;Math.abs(z)<0.001&&(z=0),Math.abs(R)<0.001&&(R=0),Math.abs(k)<0.001&&(k=0),Math.abs(P)<0.001&&(P=0),Math.abs(x)<0.001&&(x=0);var Az="translate3d("+z+"px,"+R+"px,"+k+"px)  rotateX("+x+"deg) rotateY("+P+"deg)";if(W.transform(Az),W[0].style.zIndex=-Math.abs(Math.round(AA))+1,Au.params.coverflow.slideShadows){var M=Au.isHorizontal()?W.find(".swiper-slide-shadow-left"):W.find(".swiper-slide-shadow-top"),N=Au.isHorizontal()?W.find(".swiper-slide-shadow-right"):W.find(".swiper-slide-shadow-bottom");0===M.length&&(M=G('<div class="swiper-slide-shadow-'+(Au.isHorizontal()?"left":"top")+'"></div>'),W.append(M)),0===N.length&&(N=G('<div class="swiper-slide-shadow-'+(Au.isHorizontal()?"right":"bottom")+'"></div>'),W.append(N)),M.length&&(M[0].style.opacity=AA>0?AA:0),N.length&&(N[0].style.opacity=-AA>0?-AA:0)}}if(Au.browser.ie){var AC=Au.wrapper[0].style;AC.perspectiveOrigin=O+"px 50%"}},setTransition:function(H){Au.slides.transition(H).find(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").transition(H)}}},Au.lazy={initialImageLoaded:!1,loadImageInSlide:function(I,M){if("undefined"!=typeof I&&("undefined"==typeof M&&(M=!0),0!==Au.slides.length)){var H=Au.slides.eq(I),L=H.find(".swiper-lazy:not(.swiper-lazy-loaded):not(.swiper-lazy-loading)");!H.hasClass("swiper-lazy")||H.hasClass("swiper-lazy-loaded")||H.hasClass("swiper-lazy-loading")||(L=L.add(H[0])),0!==L.length&&L.each(function(){var O=G(this);O.addClass("swiper-lazy-loading");var N=O.attr("data-background"),P=O.attr("data-src"),R=O.attr("data-srcset");Au.loadImage(O[0],P||N,R,!1,function(){if(N?(O.css("background-image",'url("'+N+'")'),O.removeAttr("data-background")):(R&&(O.attr("srcset",R),O.removeAttr("data-srcset")),P&&(O.attr("src",P),O.removeAttr("data-src"))),O.addClass("swiper-lazy-loaded").removeClass("swiper-lazy-loading"),H.find(".swiper-lazy-preloader, .preloader").remove(),Au.params.loop&&M){var W=H.attr("data-swiper-slide-index");if(H.hasClass(Au.params.slideDuplicateClass)){var S=Au.wrapper.children('[data-swiper-slide-index="'+W+'"]:not(.'+Au.params.slideDuplicateClass+")");Au.lazy.loadImageInSlide(S.index(),!1)}else{var T=Au.wrapper.children("."+Au.params.slideDuplicateClass+'[data-swiper-slide-index="'+W+'"]');Au.lazy.loadImageInSlide(T.index(),!1)}}Au.emit("onLazyImageReady",Au,H[0],O[0])}),Au.emit("onLazyImageLoad",Au,H[0],O[0])})}},load:function(){var I;if(Au.params.watchSlidesVisibility){Au.wrapper.children("."+Au.params.slideVisibleClass).each(function(){Au.lazy.loadImageInSlide(G(this).index())})}else{if(Au.params.slidesPerView>1){for(I=Au.activeIndex;I<Au.activeIndex+Au.params.slidesPerView;I++){Au.slides[I]&&Au.lazy.loadImageInSlide(I)}}else{Au.lazy.loadImageInSlide(Au.activeIndex)}}if(Au.params.lazyLoadingInPrevNext){if(Au.params.slidesPerView>1||Au.params.lazyLoadingInPrevNextAmount&&Au.params.lazyLoadingInPrevNextAmount>1){var P=Au.params.lazyLoadingInPrevNextAmount,H=Au.params.slidesPerView,L=Math.min(Au.activeIndex+H+Math.max(P,H),Au.slides.length),M=Math.max(Au.activeIndex-Math.max(H,P),0);for(I=Au.activeIndex+Au.params.slidesPerView;L>I;I++){Au.slides[I]&&Au.lazy.loadImageInSlide(I)}for(I=M;I<Au.activeIndex;I++){Au.slides[I]&&Au.lazy.loadImageInSlide(I)}}else{var N=Au.wrapper.children("."+Au.params.slideNextClass);N.length>0&&Au.lazy.loadImageInSlide(N.index());var O=Au.wrapper.children("."+Au.params.slidePrevClass);O.length>0&&Au.lazy.loadImageInSlide(O.index())}}},onTransitionStart:function(){Au.params.lazyLoading&&(Au.params.lazyLoadingOnTransitionStart||!Au.params.lazyLoadingOnTransitionStart&&!Au.lazy.initialImageLoaded)&&Au.lazy.load()},onTransitionEnd:function(){Au.params.lazyLoading&&!Au.params.lazyLoadingOnTransitionStart&&Au.lazy.load()}},Au.scrollbar={isTouched:!1,setDragPosition:function(I){var O=Au.scrollbar,N=Au.isHorizontal()?"touchstart"===I.type||"touchmove"===I.type?I.targetTouches[0].pageX:I.pageX||I.clientX:"touchstart"===I.type||"touchmove"===I.type?I.targetTouches[0].pageY:I.pageY||I.clientY,H=N-O.track.offset()[Au.isHorizontal()?"left":"top"]-O.dragSize/2,L=-Au.minTranslate()*O.moveDivider,M=-Au.maxTranslate()*O.moveDivider;L>H?H=L:H>M&&(H=M),H=-H/O.moveDivider,Au.updateProgress(H),Au.setWrapperTranslate(H,!0)},dragStart:function(H){var I=Au.scrollbar;I.isTouched=!0,H.preventDefault(),H.stopPropagation(),I.setDragPosition(H),clearTimeout(I.dragTimeout),I.track.transition(0),Au.params.scrollbarHide&&I.track.css("opacity",1),Au.wrapper.transition(100),I.drag.transition(100),Au.emit("onScrollbarDragStart",Au)},dragMove:function(H){var I=Au.scrollbar;I.isTouched&&(H.preventDefault?H.preventDefault():H.returnValue=!1,I.setDragPosition(H),Au.wrapper.transition(0),I.track.transition(0),I.drag.transition(0),Au.emit("onScrollbarDragMove",Au))},dragEnd:function(H){var I=Au.scrollbar;I.isTouched&&(I.isTouched=!1,Au.params.scrollbarHide&&(clearTimeout(I.dragTimeout),I.dragTimeout=setTimeout(function(){I.track.css("opacity",0),I.track.transition(400)},1000)),Au.emit("onScrollbarDragEnd",Au),Au.params.scrollbarSnapOnRelease&&Au.slideReset())},enableDraggable:function(){var H=Au.scrollbar,I=Au.support.touch?H.track:document;G(H.track).on(Au.touchEvents.start,H.dragStart),G(I).on(Au.touchEvents.move,H.dragMove),G(I).on(Au.touchEvents.end,H.dragEnd)},disableDraggable:function(){var H=Au.scrollbar,I=Au.support.touch?H.track:document;G(H.track).off(Au.touchEvents.start,H.dragStart),G(I).off(Au.touchEvents.move,H.dragMove),G(I).off(Au.touchEvents.end,H.dragEnd)},set:function(){if(Au.params.scrollbar){var H=Au.scrollbar;H.track=G(Au.params.scrollbar),Au.params.uniqueNavElements&&"string"==typeof Au.params.scrollbar&&H.track.length>1&&1===Au.container.find(Au.params.scrollbar).length&&(H.track=Au.container.find(Au.params.scrollbar)),H.drag=H.track.find(".swiper-scrollbar-drag"),0===H.drag.length&&(H.drag=G('<div class="swiper-scrollbar-drag"></div>'),H.track.append(H.drag)),H.drag[0].style.width="",H.drag[0].style.height="",H.trackSize=Au.isHorizontal()?H.track[0].offsetWidth:H.track[0].offsetHeight,H.divider=Au.size/Au.virtualSize,H.moveDivider=H.divider*(H.trackSize/Au.size),H.dragSize=H.trackSize*H.divider,Au.isHorizontal()?H.drag[0].style.width=H.dragSize+"px":H.drag[0].style.height=H.dragSize+"px",H.divider>=1?H.track[0].style.display="none":H.track[0].style.display="",Au.params.scrollbarHide&&(H.track[0].style.opacity=0)}},setTranslate:function(){if(Au.params.scrollbar){var H,L=Au.scrollbar,I=(Au.translate||0,L.dragSize);H=(L.trackSize-L.dragSize)*Au.progress,Au.rtl&&Au.isHorizontal()?(H=-H,H>0?(I=L.dragSize-H,H=0):-H+L.dragSize>L.trackSize&&(I=L.trackSize+H)):0>H?(I=L.dragSize+H,H=0):H+L.dragSize>L.trackSize&&(I=L.trackSize-H),Au.isHorizontal()?(Au.support.transforms3d?L.drag.transform("translate3d("+H+"px, 0, 0)"):L.drag.transform("translateX("+H+"px)"),L.drag[0].style.width=I+"px"):(Au.support.transforms3d?L.drag.transform("translate3d(0px, "+H+"px, 0)"):L.drag.transform("translateY("+H+"px)"),L.drag[0].style.height=I+"px"),Au.params.scrollbarHide&&(clearTimeout(L.timeout),L.track[0].style.opacity=1,L.timeout=setTimeout(function(){L.track[0].style.opacity=0,L.track.transition(400)},1000))}},setTransition:function(H){Au.params.scrollbar&&Au.scrollbar.drag.transition(H)}},Au.controller={LinearSpline:function(I,N){this.x=I,this.y=N,this.lastIndex=I.length-1;var M,H;this.x.length;this.interpolate=function(O){return O?(H=L(this.x,O),M=H-1,(O-this.x[M])*(this.y[H]-this.y[M])/(this.x[H]-this.x[M])+this.y[M]):0};var L=function(){var O,R,P;return function(S,T){for(R=-1,O=S.length;O-R>1;){S[P=O+R>>1]<=T?R=P:O=P}return O}}()},getInterpolateFunction:function(H){Au.controller.spline||(Au.controller.spline=Au.params.loop?new Au.controller.LinearSpline(Au.slidesGrid,H.slidesGrid):new Au.controller.LinearSpline(Au.snapGrid,H.snapGrid))},setTranslate:function(I,P){function H(R){I=R.rtl&&"horizontal"===R.params.direction?-Au.translate:Au.translate,"slide"===Au.params.controlBy&&(Au.controller.getInterpolateFunction(R),M=-Au.controller.spline.interpolate(-I)),M&&"container"!==Au.params.controlBy||(L=(R.maxTranslate()-R.minTranslate())/(Au.maxTranslate()-Au.minTranslate()),M=(I-Au.minTranslate())*L+R.minTranslate()),Au.params.controlInverse&&(M=R.maxTranslate()-M),R.updateProgress(M),R.setWrapperTranslate(M,!1,Au),R.updateActiveIndex()}var L,M,N=Au.params.control;if(Au.isArray(N)){for(var O=0;O<N.length;O++){N[O]!==P&&N[O] instanceof F&&H(N[O])}}else{N instanceof F&&P!==N&&H(N)}},setTransition:function(I,N){function H(O){O.setWrapperTransition(I,Au),0!==I&&(O.onTransitionStart(),O.wrapper.transitionEnd(function(){M&&(O.params.loop&&"slide"===Au.params.controlBy&&O.fixLoop(),O.onTransitionEnd())}))}var L,M=Au.params.control;if(Au.isArray(M)){for(L=0;L<M.length;L++){M[L]!==N&&M[L] instanceof F&&H(M[L])}}else{M instanceof F&&N!==M&&H(M)}}},Au.hashnav={init:function(){if(Au.params.hashnav){Au.hashnav.initialized=!0;var I=document.location.hash.replace("#","");if(I){for(var P=0,O=0,H=Au.slides.length;H>O;O++){var L=Au.slides.eq(O),M=L.attr("data-hash");if(M===I&&!L.hasClass(Au.params.slideDuplicateClass)){var N=L.index();Au.slideTo(N,P,Au.params.runCallbacksOnInit,!0)}}}}},setHash:function(){Au.hashnav.initialized&&Au.params.hashnav&&(document.location.hash=Au.slides.eq(Au.activeIndex).attr("data-hash")||"")}},Au.disableKeyboardControl=function(){Au.params.keyboardControl=!1,G(document).off("keydown",Aj)},Au.enableKeyboardControl=function(){Au.params.keyboardControl=!0,G(document).on("keydown",Aj)},Au.mousewheel={event:!1,lastScrollTime:(new window.Date).getTime()},Au.params.mousewheelControl){try{new window.WheelEvent("wheel"),Au.mousewheel.event="wheel"}catch(a){(window.WheelEvent||Au.container[0]&&"wheel" in Au.container[0])&&(Au.mousewheel.event="wheel")}!Au.mousewheel.event&&window.WheelEvent,Au.mousewheel.event||void 0===document.onmousewheel||(Au.mousewheel.event="mousewheel"),Au.mousewheel.event||(Au.mousewheel.event="DOMMouseScroll")}Au.disableMousewheelControl=function(){return Au.mousewheel.event?(Au.container.off(Au.mousewheel.event,Ay),!0):!1},Au.enableMousewheelControl=function(){return Au.mousewheel.event?(Au.container.on(Au.mousewheel.event,Ay),!0):!1},Au.parallax={setTranslate:function(){Au.container.children("[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y]").each(function(){Am(this,Au.progress)}),Au.slides.each(function(){var H=G(this);H.find("[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y]").each(function(){var I=Math.min(Math.max(H[0].progress,-1),1);Am(this,I)})})},setTransition:function(H){"undefined"==typeof H&&(H=Au.params.speed),Au.container.find("[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y]").each(function(){var L=G(this),I=parseInt(L.attr("data-swiper-parallax-duration"),10)||H;0===H&&(I=0),L.transition(I)})}},Au._plugins=[];for(var K in Au.plugins){var V=Au.plugins[K](Au,Au.params[K]);V&&Au._plugins.push(V)}return Au.callPlugins=function(H){for(var I=0;I<Au._plugins.length;I++){H in Au._plugins[I]&&Au._plugins[I][H](arguments[1],arguments[2],arguments[3],arguments[4],arguments[5])}},Au.emitterEventListeners={},Au.emit=function(H){Au.params[H]&&Au.params[H](arguments[1],arguments[2],arguments[3],arguments[4],arguments[5]);var I;if(Au.emitterEventListeners[H]){for(I=0;I<Au.emitterEventListeners[H].length;I++){Au.emitterEventListeners[H][I](arguments[1],arguments[2],arguments[3],arguments[4],arguments[5])}}Au.callPlugins&&Au.callPlugins(H,arguments[1],arguments[2],arguments[3],arguments[4],arguments[5])},Au.on=function(H,I){return H=Av(H),Au.emitterEventListeners[H]||(Au.emitterEventListeners[H]=[]),Au.emitterEventListeners[H].push(I),Au},Au.off=function(H,L){var I;if(H=Av(H),"undefined"==typeof L){return Au.emitterEventListeners[H]=[],Au}if(Au.emitterEventListeners[H]&&0!==Au.emitterEventListeners[H].length){for(I=0;I<Au.emitterEventListeners[H].length;I++){Au.emitterEventListeners[H][I]===L&&Au.emitterEventListeners[H].splice(I,1)}return Au}},Au.once=function(H,L){H=Av(H);var I=function(){L(arguments[0],arguments[1],arguments[2],arguments[3],arguments[4]),Au.off(H,I)};return Au.on(H,I),Au},Au.a11y={makeFocusable:function(H){return H.attr("tabIndex","0"),H},addRole:function(H,I){return H.attr("role",I),H},addLabel:function(H,I){return H.attr("aria-label",I),H},disable:function(H){return H.attr("aria-disabled",!0),H},enable:function(H){return H.attr("aria-disabled",!1),H},onEnterKey:function(H){13===H.keyCode&&(G(H.target).is(Au.params.nextButton)?(Au.onClickNext(H),Au.isEnd?Au.a11y.notify(Au.params.lastSlideMessage):Au.a11y.notify(Au.params.nextSlideMessage)):G(H.target).is(Au.params.prevButton)&&(Au.onClickPrev(H),Au.isBeginning?Au.a11y.notify(Au.params.firstSlideMessage):Au.a11y.notify(Au.params.prevSlideMessage)),G(H.target).is("."+Au.params.bulletClass)&&G(H.target)[0].click())},liveRegion:G('<span class="swiper-notification" aria-live="assertive" aria-atomic="true"></span>'),notify:function(H){var I=Au.a11y.liveRegion;0!==I.length&&(I.html(""),I.html(H))},init:function(){Au.params.nextButton&&Au.nextButton&&Au.nextButton.length>0&&(Au.a11y.makeFocusable(Au.nextButton),Au.a11y.addRole(Au.nextButton,"button"),Au.a11y.addLabel(Au.nextButton,Au.params.nextSlideMessage)),Au.params.prevButton&&Au.prevButton&&Au.prevButton.length>0&&(Au.a11y.makeFocusable(Au.prevButton),Au.a11y.addRole(Au.prevButton,"button"),Au.a11y.addLabel(Au.prevButton,Au.params.prevSlideMessage)),G(Au.container).append(Au.a11y.liveRegion)},initPagination:function(){Au.params.pagination&&Au.params.paginationClickable&&Au.bullets&&Au.bullets.length&&Au.bullets.each(function(){var H=G(this);Au.a11y.makeFocusable(H),Au.a11y.addRole(H,"button"),Au.a11y.addLabel(H,Au.params.paginationBulletMessage.replace(/{{index}}/,H.index()+1))})},destroy:function(){Au.a11y.liveRegion&&Au.a11y.liveRegion.length>0&&Au.a11y.liveRegion.remove()}},Au.init=function(){Au.params.loop&&Au.createLoop(),Au.updateContainerSize(),Au.updateSlidesSize(),Au.updatePagination(),Au.params.scrollbar&&Au.scrollbar&&(Au.scrollbar.set(),Au.params.scrollbarDraggable&&Au.scrollbar.enableDraggable()),"slide"!==Au.params.effect&&Au.effects[Au.params.effect]&&(Au.params.loop||Au.updateProgress(),Au.effects[Au.params.effect].setTranslate()),Au.params.loop?Au.slideTo(Au.params.initialSlide+Au.loopedSlides,0,Au.params.runCallbacksOnInit):(Au.slideTo(Au.params.initialSlide,0,Au.params.runCallbacksOnInit),0===Au.params.initialSlide&&(Au.parallax&&Au.params.parallax&&Au.parallax.setTranslate(),Au.lazy&&Au.params.lazyLoading&&(Au.lazy.load(),Au.lazy.initialImageLoaded=!0))),Au.attachEvents(),Au.params.observer&&Au.support.observer&&Au.initObservers(),Au.params.preloadImages&&!Au.params.lazyLoading&&Au.preloadImages(),Au.params.autoplay&&Au.startAutoplay(),Au.params.keyboardControl&&Au.enableKeyboardControl&&Au.enableKeyboardControl(),Au.params.mousewheelControl&&Au.enableMousewheelControl&&Au.enableMousewheelControl(),Au.params.hashnav&&Au.hashnav&&Au.hashnav.init(),Au.params.a11y&&Au.a11y&&Au.a11y.init(),Au.emit("onInit",Au)},Au.cleanupStyles=function(){Au.container.removeClass(Au.classNames.join(" ")).removeAttr("style"),Au.wrapper.removeAttr("style"),Au.slides&&Au.slides.length&&Au.slides.removeClass([Au.params.slideVisibleClass,Au.params.slideActiveClass,Au.params.slideNextClass,Au.params.slidePrevClass].join(" ")).removeAttr("style").removeAttr("data-swiper-column").removeAttr("data-swiper-row"),Au.paginationContainer&&Au.paginationContainer.length&&Au.paginationContainer.removeClass(Au.params.paginationHiddenClass),Au.bullets&&Au.bullets.length&&Au.bullets.removeClass(Au.params.bulletActiveClass),Au.params.prevButton&&G(Au.params.prevButton).removeClass(Au.params.buttonDisabledClass),Au.params.nextButton&&G(Au.params.nextButton).removeClass(Au.params.buttonDisabledClass),Au.params.scrollbar&&Au.scrollbar&&(Au.scrollbar.track&&Au.scrollbar.track.length&&Au.scrollbar.track.removeAttr("style"),Au.scrollbar.drag&&Au.scrollbar.drag.length&&Au.scrollbar.drag.removeAttr("style"))},Au.destroy=function(H,I){Au.detachEvents(),Au.stopAutoplay(),Au.params.scrollbar&&Au.scrollbar&&Au.params.scrollbarDraggable&&Au.scrollbar.disableDraggable(),Au.params.loop&&Au.destroyLoop(),I&&Au.cleanupStyles(),Au.disconnectObservers(),Au.params.keyboardControl&&Au.disableKeyboardControl&&Au.disableKeyboardControl(),Au.params.mousewheelControl&&Au.disableMousewheelControl&&Au.disableMousewheelControl(),Au.params.a11y&&Au.a11y&&Au.a11y.destroy(),Au.emit("onDestroy"),H!==!1&&(Au=null)},Au.init(),Au}};F.prototype={isSafari:function(){var H=navigator.userAgent.toLowerCase();return H.indexOf("safari")>=0&&H.indexOf("chrome")<0&&H.indexOf("android")<0}(),isUiWebView:/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(navigator.userAgent),isArray:function(H){return"[object Array]"===Object.prototype.toString.apply(H)},browser:{ie:window.navigator.pointerEnabled||window.navigator.msPointerEnabled,ieTouch:window.navigator.msPointerEnabled&&window.navigator.msMaxTouchPoints>1||window.navigator.pointerEnabled&&window.navigator.maxTouchPoints>1},device:function(){var I=navigator.userAgent,L=I.match(/(Android);?[\s\/]+([\d.]+)?/),K=I.match(/(iPad).*OS\s([\d_]+)/),H=I.match(/(iPod)(.*OS\s([\d_]+))?/),J=!K&&I.match(/(iPhone\sOS)\s([\d_]+)/);return{ios:K||J||H,android:L}}(),support:{touch:window.Modernizr&&Modernizr.touch===!0||function(){return !!("ontouchstart" in window||window.DocumentTouch&&document instanceof DocumentTouch)}(),transforms3d:window.Modernizr&&Modernizr.csstransforms3d===!0||function(){var H=document.createElement("div").style;return"webkitPerspective" in H||"MozPerspective" in H||"OPerspective" in H||"MsPerspective" in H||"perspective" in H}(),flexbox:function(){for(var H=document.createElement("div").style,J="alignItems webkitAlignItems webkitBoxAlign msFlexAlign mozBoxAlign webkitFlexDirection msFlexDirection mozBoxDirection mozBoxOrient webkitBoxDirection webkitBoxOrient".split(" "),I=0;I<J.length;I++){if(J[I] in H){return !0}}}(),observer:function(){return"MutationObserver" in window||"WebkitMutationObserver" in window}()},plugins:{}};for(var A=(function(){var H=function(J){var L=this,K=0;for(K=0;K<J.length;K++){L[K]=J[K]}return L.length=J.length,this},I=function(Q,L){var J=[],M=0;if(Q&&!L&&Q instanceof H){return Q}if(Q){if("string"==typeof Q){var K,N,O=Q.trim();if(O.indexOf("<")>=0&&O.indexOf(">")>=0){var P="div";for(0===O.indexOf("<li")&&(P="ul"),0===O.indexOf("<tr")&&(P="tbody"),(0===O.indexOf("<td")||0===O.indexOf("<th"))&&(P="tr"),0===O.indexOf("<tbody")&&(P="table"),0===O.indexOf("<option")&&(P="select"),N=document.createElement(P),N.innerHTML=Q,M=0;M<N.childNodes.length;M++){J.push(N.childNodes[M])}}else{for(K=L||"#"!==Q[0]||Q.match(/[ .<>:~]/)?(L||document).querySelectorAll(Q):[document.getElementById(Q.split("#")[1])],M=0;M<K.length;M++){K[M]&&J.push(K[M])}}}else{if(Q.nodeType||Q===window||Q===document){J.push(Q)}else{if(Q.length>0&&Q[0].nodeType){for(M=0;M<Q.length;M++){J.push(Q[M])}}}}}return new H(J)};return H.prototype={addClass:function(K){if("undefined"==typeof K){return this}for(var M=K.split(" "),L=0;L<M.length;L++){for(var J=0;J<this.length;J++){this[J].classList.add(M[L])}}return this},removeClass:function(K){for(var M=K.split(" "),L=0;L<M.length;L++){for(var J=0;J<this.length;J++){this[J].classList.remove(M[L])}}return this},hasClass:function(J){return this[0]?this[0].classList.contains(J):!1},toggleClass:function(K){for(var M=K.split(" "),L=0;L<M.length;L++){for(var J=0;J<this.length;J++){this[J].classList.toggle(M[L])}}return this},attr:function(K,M){if(1===arguments.length&&"string"==typeof K){return this[0]?this[0].getAttribute(K):void 0}for(var L=0;L<this.length;L++){if(2===arguments.length){this[L].setAttribute(K,M)}else{for(var J in K){this[L][J]=K[J],this[L].setAttribute(J,K[J])}}}return this},removeAttr:function(J){for(var K=0;K<this.length;K++){this[K].removeAttribute(J)}return this},data:function(K,N){if("undefined"!=typeof N){for(var M=0;M<this.length;M++){var J=this[M];J.dom7ElementDataStorage||(J.dom7ElementDataStorage={}),J.dom7ElementDataStorage[K]=N}return this}if(this[0]){var L=this[0].getAttribute("data-"+K);return L?L:this[0].dom7ElementDataStorage&&K in this[0].dom7ElementDataStorage?this[0].dom7ElementDataStorage[K]:void 0}},transform:function(J){for(var L=0;L<this.length;L++){var K=this[L].style;K.webkitTransform=K.MsTransform=K.msTransform=K.MozTransform=K.OTransform=K.transform=J}return this},transition:function(J){"string"!=typeof J&&(J+="ms");for(var L=0;L<this.length;L++){var K=this[L].style;K.webkitTransitionDuration=K.MsTransitionDuration=K.msTransitionDuration=K.MozTransitionDuration=K.OTransitionDuration=K.transitionDuration=J}return this},on:function(J,M,L,N){function K(S){var R=S.target;if(I(R).is(M)){L.call(R,S)}else{for(var T=I(R).parents(),U=0;U<T.length;U++){I(T[U]).is(M)&&L.call(T[U],S)}}}var O,P,Q=J.split(" ");for(O=0;O<this.length;O++){if("function"==typeof M||M===!1){for("function"==typeof M&&(L=arguments[1],N=arguments[2]||!1),P=0;P<Q.length;P++){this[O].addEventListener(Q[P],L,N)}}else{for(P=0;P<Q.length;P++){this[O].dom7LiveListeners||(this[O].dom7LiveListeners=[]),this[O].dom7LiveListeners.push({listener:L,liveListener:K}),this[O].addEventListener(Q[P],K,N)}}}return this},off:function(J,Q,M,L){for(var N=J.split(" "),K=0;K<N.length;K++){for(var O=0;O<this.length;O++){if("function"==typeof Q||Q===!1){"function"==typeof Q&&(M=arguments[1],L=arguments[2]||!1),this[O].removeEventListener(N[K],M,L)}else{if(this[O].dom7LiveListeners){for(var P=0;P<this[O].dom7LiveListeners.length;P++){this[O].dom7LiveListeners[P].listener===M&&this[O].removeEventListener(N[K],this[O].dom7LiveListeners[P].liveListener,L)}}}}}return this},once:function(K,O,N,J){function L(P){N(P),M.off(K,O,L,J)}var M=this;"function"==typeof O&&(O=!1,N=arguments[1],J=arguments[2]),M.on(K,O,L,J)},trigger:function(K,N){for(var M=0;M<this.length;M++){var J;try{J=new window.CustomEvent(K,{detail:N,bubbles:!0,cancelable:!0})}catch(L){J=document.createEvent("Event"),J.initEvent(K,!0,!0),J.detail=N}this[M].dispatchEvent(J)}return this},transitionEnd:function(K){function N(O){if(O.target===this){for(K.call(this,O),M=0;M<J.length;M++){L.off(J[M],N)}}}var M,J=["webkitTransitionEnd","transitionend","oTransitionEnd","MSTransitionEnd","msTransitionEnd"],L=this;if(K){for(M=0;M<J.length;M++){L.on(J[M],N)}}return this},width:function(){return this[0]===window?window.innerWidth:this.length>0?parseFloat(this.css("width")):null},outerWidth:function(J){return this.length>0?J?this[0].offsetWidth+parseFloat(this.css("margin-right"))+parseFloat(this.css("margin-left")):this[0].offsetWidth:null},height:function(){return this[0]===window?window.innerHeight:this.length>0?parseFloat(this.css("height")):null},outerHeight:function(J){return this.length>0?J?this[0].offsetHeight+parseFloat(this.css("margin-top"))+parseFloat(this.css("margin-bottom")):this[0].offsetHeight:null},offset:function(){if(this.length>0){var K=this[0],P=K.getBoundingClientRect(),O=document.body,J=K.clientTop||O.clientTop||0,L=K.clientLeft||O.clientLeft||0,M=window.pageYOffset||K.scrollTop,N=window.pageXOffset||K.scrollLeft;return{top:P.top+M-J,left:P.left+N-L}}return null},css:function(K,M){var L;if(1===arguments.length){if("string"!=typeof K){for(L=0;L<this.length;L++){for(var J in K){this[L].style[J]=K[J]}}return this}if(this[0]){return window.getComputedStyle(this[0],null).getPropertyValue(K)}}if(2===arguments.length&&"string"==typeof K){for(L=0;L<this.length;L++){this[L].style[K]=M}return this}return this},each:function(J){for(var K=0;K<this.length;K++){J.call(this[K],K,this[K])}return this},html:function(J){if("undefined"==typeof J){return this[0]?this[0].innerHTML:void 0}for(var K=0;K<this.length;K++){this[K].innerHTML=J}return this},text:function(J){if("undefined"==typeof J){return this[0]?this[0].textContent.trim():null}for(var K=0;K<this.length;K++){this[K].textContent=J}return this},is:function(M){if(!this[0]){return !1}var J,K;if("string"==typeof M){var L=this[0];if(L===document){return M===document}if(L===window){return M===window}if(L.matches){return L.matches(M)}if(L.webkitMatchesSelector){return L.webkitMatchesSelector(M)}if(L.mozMatchesSelector){return L.mozMatchesSelector(M)}if(L.msMatchesSelector){return L.msMatchesSelector(M)}for(J=I(M),K=0;K<J.length;K++){if(J[K]===this[0]){return !0}}return !1}if(M===document){return this[0]===document}if(M===window){return this[0]===window}if(M.nodeType||M instanceof H){for(J=M.nodeType?[M]:M,K=0;K<J.length;K++){if(J[K]===this[0]){return !0}}return !1}return !1},index:function(){if(this[0]){for(var J=this[0],K=0;null!==(J=J.previousSibling);){1===J.nodeType&&K++}return K}},eq:function(L){if("undefined"==typeof L){return this}var K,J=this.length;return L>J-1?new H([]):0>L?(K=J+L,new H(0>K?[]:[this[K]])):new H([this[L]])},append:function(M){var L,J;for(L=0;L<this.length;L++){if("string"==typeof M){var K=document.createElement("div");for(K.innerHTML=M;K.firstChild;){this[L].appendChild(K.firstChild)}}else{if(M instanceof H){for(J=0;J<M.length;J++){this[L].appendChild(M[J])}}else{this[L].appendChild(M)}}}return this},prepend:function(M){var L,J;for(L=0;L<this.length;L++){if("string"==typeof M){var K=document.createElement("div");for(K.innerHTML=M,J=K.childNodes.length-1;J>=0;J--){this[L].insertBefore(K.childNodes[J],this[L].childNodes[0])}}else{if(M instanceof H){for(J=0;J<M.length;J++){this[L].insertBefore(M[J],this[L].childNodes[0])}}else{this[L].insertBefore(M,this[L].childNodes[0])}}}return this},insertBefore:function(K){for(var M=I(K),J=0;J<this.length;J++){if(1===M.length){M[0].parentNode.insertBefore(this[J],M[0])}else{if(M.length>1){for(var L=0;L<M.length;L++){M[L].parentNode.insertBefore(this[J].cloneNode(!0),M[L])}}}}},insertAfter:function(K){for(var M=I(K),J=0;J<this.length;J++){if(1===M.length){M[0].parentNode.insertBefore(this[J],M[0].nextSibling)}else{if(M.length>1){for(var L=0;L<M.length;L++){M[L].parentNode.insertBefore(this[J].cloneNode(!0),M[L].nextSibling)}}}}},next:function(J){return new H(this.length>0?J?this[0].nextElementSibling&&I(this[0].nextElementSibling).is(J)?[this[0].nextElementSibling]:[]:this[0].nextElementSibling?[this[0].nextElementSibling]:[]:[])},nextAll:function(M){var J=[],K=this[0];if(!K){return new H([])}for(;K.nextElementSibling;){var L=K.nextElementSibling;M?I(L).is(M)&&J.push(L):J.push(L),K=L}return new H(J)},prev:function(J){return new H(this.length>0?J?this[0].previousElementSibling&&I(this[0].previousElementSibling).is(J)?[this[0].previousElementSibling]:[]:this[0].previousElementSibling?[this[0].previousElementSibling]:[]:[])},prevAll:function(M){var J=[],K=this[0];if(!K){return new H([])}for(;K.previousElementSibling;){var L=K.previousElementSibling;M?I(L).is(M)&&J.push(L):J.push(L),K=L}return new H(J)},parent:function(K){for(var L=[],J=0;J<this.length;J++){K?I(this[J].parentNode).is(K)&&L.push(this[J].parentNode):L.push(this[J].parentNode)}return I(I.unique(L))},parents:function(K){for(var M=[],J=0;J<this.length;J++){for(var L=this[J].parentNode;L;){K?I(L).is(K)&&M.push(L):M.push(L),L=L.parentNode}}return I(I.unique(M))},find:function(N){for(var M=[],J=0;J<this.length;J++){for(var K=this[J].querySelectorAll(N),L=0;L<K.length;L++){M.push(K[L])}}return new H(M)},children:function(N){for(var J=[],K=0;K<this.length;K++){for(var L=this[K].childNodes,M=0;M<L.length;M++){N?1===L[M].nodeType&&I(L[M]).is(N)&&J.push(L[M]):1===L[M].nodeType&&J.push(L[M])}}return new H(I.unique(J))},remove:function(){for(var J=0;J<this.length;J++){this[J].parentNode&&this[J].parentNode.removeChild(this[J])}return this},add:function(){var K,M,J=this;for(K=0;K<arguments.length;K++){var L=I(arguments[K]);for(M=0;M<L.length;M++){J[J.length]=L[M],J.length++}}return J}},I.fn=H.prototype,I.unique=function(J){for(var L=[],K=0;K<J.length;K++){-1===L.indexOf(J[K])&&L.push(J[K])}return L},I}()),C=["jQuery","Zepto","Dom7"],D=0;D<C.length;D++){window[C[D]]&&B(window[C[D]])}var E;E="undefined"==typeof A?window.Dom7||window.Zepto||window.jQuery:A,E&&("transitionEnd" in E.fn||(E.fn.transitionEnd=function(I){function L(M){if(M.target===this){for(I.call(this,M),K=0;K<H.length;K++){J.off(H[K],L)}}}var K,H=["webkitTransitionEnd","transitionend","oTransitionEnd","MSTransitionEnd","msTransitionEnd"],J=this;if(I){for(K=0;K<H.length;K++){J.on(H[K],L)}}return this}),"transform" in E.fn||(E.fn.transform=function(H){for(var J=0;J<this.length;J++){var I=this[J].style;I.webkitTransform=I.MsTransform=I.msTransform=I.MozTransform=I.OTransform=I.transform=H}return this}),"transition" in E.fn||(E.fn.transition=function(H){"string"!=typeof H&&(H+="ms");for(var J=0;J<this.length;J++){var I=this[J].style;I.webkitTransitionDuration=I.MsTransitionDuration=I.msTransitionDuration=I.MozTransitionDuration=I.OTransitionDuration=I.transitionDuration=H}return this})),window.Swiper=F}(),"undefined"!=typeof module?module.exports=window.Swiper:"function"==typeof define&&define.amd&&define([],function(){return window.Swiper});