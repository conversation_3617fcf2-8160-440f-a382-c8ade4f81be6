package org.itboys.admin.entity.lasted;

import org.itboys.mongodb.entity.BaseMongoEntity;
import org.mongodb.morphia.annotations.Entity;
/**
 * 作者：jiangxiong
 * 日期：2017年2月27日
 * 联系方式 ：<EMAIL>
 * 描述：系统字典_实体_，志强重新写的
 */

@Entity(value = "SystemDictionary", noClassnameStored = true)
public class SystemDictionary extends BaseMongoEntity {

	private static final long serialVersionUID = 7144157080083147958L;
	private String key;//分组标识
	private String name;//显示名称
	private String value;//实际保存名称
	private String desc;//备注
	private String image;//图片

//	private Integer isDeleted = 0;// 逻辑删除标记 0有效 1删除

	public SystemDictionary() {
	}

	public SystemDictionary(String key, String name, String value, String desc, String image) {
		this.key = key;
		this.name = name;
		this.value = value;
		this.desc = desc;
		this.image = image;
	}

	public String getKey() {
		return key;
	}

	public void setKey(String key) {
		this.key = key;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getValue()
	{
		return value;
	}


	public void setValue(String value) {
		this.value = value;
	}

	public String getDesc() {
		return desc;
	}

	public void setDesc(String desc) {
		this.desc = desc;
	}

	public String getImage() {
		return image;
	}

	public void setImage(String image) {
		this.image = image;
	}
}
