package org.itboys.admin.entity;

import org.mongodb.morphia.annotations.Entity;

/**
 * 邮件模版
 * 
 * <AUTHOR>
 *
 */
@Entity(value = "SysEmailTemplate", noClassnameStored = true)
public class SysEmailTemplate extends BaseAdminEntity {

	private static final long serialVersionUID = 9003177984268743638L;
	private String title; // 邮件名称
	private String emailContent; // 邮件内容
	private String type; // 邮件类型
	private String sender; // 邮件发送人id
	private String receiver; // 邮件接收人id

	private Integer isDeleted = 0;// 逻辑删除标记 0有效 1删除

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getEmailContent() {
		return emailContent;
	}

	public void setEmailContent(String emailContent) {
		this.emailContent = emailContent;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getSender() {
		return sender;
	}

	public void setSender(String sender) {
		this.sender = sender;
	}

	public String getReceiver() {
		return receiver;
	}

	public void setReceiver(String receiver) {
		this.receiver = receiver;
	}

	public Integer getIsDeleted() {
		return isDeleted;
	}

	public void setIsDeleted(Integer isDeleted) {
		this.isDeleted = isDeleted;
	}

}
