package org.itboys.admin.entity.lasted;

import org.itboys.mongodb.entity.BaseMongoEntity;
import org.mongodb.morphia.annotations.Entity;

import java.util.List;
/**
 * 作者：jiangxiong
 * 日期：2017年2月27日
 * 联系方式 ：<EMAIL>
 * 描述：系统菜单(以及关联功能)_实体_，江雄重新写的
 */
@Entity(value = "SystemMenu", noClassnameStored = true)
public class SystemMenu extends BaseMongoEntity {

	private static final long serialVersionUID = 1107598724736065982L;
	
	private String name;
	private String url;
	private Integer sequence;
	private Long parentId;
	//private Integer isDeleted=0;
	private String menuClass;// 菜单样式
	private String remark;//备注。

	//2017年2月27日江雄定义
	private List<SystemMenu> children;
	private Integer isMenu;//是否菜单 1菜单，0操作
	//private Integer isSystem;//是否系统菜单。系统功能不授权给任何用户（只授予查询和选择器权限）。
	//private String operateName;//方法名称


	public SystemMenu() {

	}



	public SystemMenu(String name,String url,Integer sequence, Long parentId, Integer isMenu) {
		this.name = name;
		this.url=url;
		this.sequence = sequence;
		this.parentId = parentId;
		this.isMenu = isMenu;

	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}


	public Integer getSequence() {
		return sequence;
	}

	public void setSequence(Integer sequence) {
		this.sequence = sequence;
	}

	public Long getParentId() {
		return parentId;
	}

	public void setParentId(Long parentId) {
		this.parentId = parentId;
	}

	//public Integer getIsDeleted() {
	//	return isDeleted;
	//}

	//public void setIsDeleted(Integer isDeleted) {
	//	this.isDeleted = isDeleted;
	//}

	public String getMenuClass() {
		return menuClass;
	}

	public void setMenuClass(String menuClass) {
		this.menuClass = menuClass;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public List<SystemMenu> getChildren() {
		return children;
	}

	public void setChildren(List<SystemMenu> children) {
		this.children = children;
	}

	public  boolean getExpanded(){
		if(children!=null&&children.size()>0){
			return true;
		}else{
			return false;
		}
	}


	public Integer getIsMenu() {
		return isMenu;
	}

	public void setIsMenu(Integer isMenu) {
		this.isMenu = isMenu;
	}



}
