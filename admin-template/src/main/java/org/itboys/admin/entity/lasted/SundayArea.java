package org.itboys.admin.entity.lasted;
import org.itboys.mongodb.entity.BaseMySqlEntity;

import java.util.List;


/**
 * 包名：com.hz.sunday.tianjie.entity.mysql.product
 * 作者 : 江雄
 * Email: <EMAIL>
 * 时间：2016年3月10日 下午2:52:46 
 * 描述:百睿_省（直辖市）/市/区or县/街道or乡镇_实体。四级行政区划
 *
 *
 */

public class SundayArea extends BaseMySqlEntity {

	private Long parentId;//父ID
	private String name;//名称
	private String py;//拼音
	private String pyszm;//拼音首字母
	private Integer grade;//1,省或直辖市，2，市，3区县，4，街道或乡镇
	private List<SundayArea> children;
	private Integer sequence;

	public SundayArea() {
	}

	public Long getParentId() {
		return parentId;
	}

	public void setParentId(Long parentId) {
		this.parentId = parentId;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getPy() {
		return py;
	}

	public void setPy(String py) {
		this.py = py;
	}

	public String getPyszm() {
		return pyszm;
	}

	public void setPyszm(String pyszm) {
		this.pyszm = pyszm;
	}

	public Integer getGrade() {
		return grade;
	}

	public void setGrade(Integer grade) {
		this.grade = grade;
	}

	public String  getGradeStr(){
		String gradeStr= "";
		if(grade==null)return gradeStr;
		switch (grade) {
			case 1:
				gradeStr="省/直辖市";
				break;
			case 2:
				gradeStr="市";
				break;
			case 3:
				gradeStr="区县";
				break;
			case 4:
				gradeStr="乡镇";
				break;
			default:
				break;
		}
		return gradeStr;
	}
	public List<SundayArea> getChildren() {
		return children;
	}

	public void setChildren(List<SundayArea> children) {
		this.children = children;
	}

	public Integer getSequence() {
		return sequence;
	}

	public void setSequence(Integer sequence) {
		this.sequence = sequence;
	}
	//bootstrap下，树形菜单需要用到此属性
	public  boolean getExpanded(){
		if(children!=null&&children.size()>0){
			return true;
		}else{
			return false;
		}
	}
}
