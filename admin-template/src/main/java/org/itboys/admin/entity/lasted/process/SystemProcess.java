package org.itboys.admin.entity.lasted.process;

import org.itboys.mongodb.entity.BaseMongoEntity;
import org.mongodb.morphia.annotations.Entity;

/**
 * 作者：jiangxiong
 * 日期：2017年2月27日
 * 联系方式 ：<EMAIL>
 * 描述：系统字典_审核_主流程，
 */

@Entity(value = "SystemProcess", noClassnameStored = true)
public class SystemProcess extends BaseMongoEntity {

	private static final long serialVersionUID = 7144157080083147958L;

	private String number;//编号
	//private Integer sequence;//序号
	private Long templateId;//流程模板ID
	private String title;//名称
	private String desc;//备注
	private Integer type;//类型。1，项目审核。2，融资方案审核（根据自己的业务逻辑去配置），
	private String outClassName;//关联业务实体的类名
	private Long outClassId;//关联业务实体的类ID
	private Integer status;//1，进行中，2已结束
	private Long currentUserId;//当前节点用户ID;
	private String currentUserName;//当前节点用户名称;
	public SystemProcess() {
	}

	public SystemProcess(String number, Long templateId, String title, String desc, Integer type, String outClassName, Long outClassId, Integer status,Long currentUserId) {
		this.number = number;
		this.templateId = templateId;
		this.title = title;
		this.desc = desc;
		this.type = type;
		this.outClassName = outClassName;
		this.outClassId = outClassId;
		this.status = status;
		this.currentUserId=currentUserId;
	}

	public static long getSerialVersionUID() {
		return serialVersionUID;
	}

	public String getNumber() {
		return number;
	}

	public void setNumber(String number) {
		this.number = number;
	}

	public Long getTemplateId() {
		return templateId;
	}

	public void setTemplateId(Long templateId) {
		this.templateId = templateId;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getDesc() {
		return desc;
	}

	public void setDesc(String desc) {
		this.desc = desc;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public String getOutClassName() {
		return outClassName;
	}

	public void setOutClassName(String outClassName) {
		this.outClassName = outClassName;
	}

	public Long getOutClassId() {
		return outClassId;
	}

	public void setOutClassId(Long outClassId) {
		this.outClassId = outClassId;
	}

	public Integer getStatus() {
		return status;
	}
	public String getStatusStr() {
		String statusStr="";
		if(status==null) {
            return statusStr;
        }

		switch (status) {
			case 1:
				statusStr="进行中";
				break;
			case 2:
				statusStr="已完成";
				break;
			default:
				break;
		}
		return statusStr;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}

	public Long getCurrentUserId() {
		return currentUserId;
	}

	public void setCurrentUserId(Long currentUserId) {
		this.currentUserId = currentUserId;
	}

	public String getCurrentUserName() {
		return currentUserName;
	}

	public void setCurrentUserName(String currentUserName) {
		this.currentUserName = currentUserName;
	}
}
