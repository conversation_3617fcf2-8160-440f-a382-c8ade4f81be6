package org.itboys.admin.entity;

import org.mongodb.morphia.annotations.Entity;

/**
 * 参数模版
 * 
 * <AUTHOR>
 *
 */
@Entity(value = "SysParamTemplate", noClassnameStored = true)
public class SysParamTemplate extends BaseAdminEntity {

	private static final long serialVersionUID = -5624076839630645944L;
	private String app_key;
	private String key;
	private String value;
	private String desc;
	private Integer isDeleted = 0;// 逻辑删除标记 0有效 1删除

	public String getApp_key() {
		return app_key;
	}

	public void setApp_key(String app_key) {
		this.app_key = app_key;
	}

	public String getKey() {
		return key;
	}

	public void setKey(String key) {
		this.key = key;
	}

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}

	public String getDesc() {
		return desc;
	}

	public void setDesc(String desc) {
		this.desc = desc;
	}

	public Integer getIsDeleted() {
		return isDeleted;
	}

	public void setIsDeleted(Integer isDeleted) {
		this.isDeleted = isDeleted;
	}

}
