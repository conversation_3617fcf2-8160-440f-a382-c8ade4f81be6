package org.itboys.admin.interceptor;

import org.itboys.framework.resource.ResourceHolder;
import org.itboys.framework.spring.interceptor.ServletContextInterceptor;
import org.springframework.beans.factory.annotation.Autowired;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
/**
 * 登陆验证拦截器
 * <AUTHOR>
 *
 */
public class LoginIntercepter extends ServletContextInterceptor{
	
	@Autowired
	private ResourceHolder resourceHolder;

	@Override
	public boolean preHandle(HttpServletRequest request,
			HttpServletResponse response, Object handler) throws Exception {
		
		request.setAttribute("staticRoot", resourceHolder.getStringValue("staticRoot"));
		request.setAttribute("adminRoot", resourceHolder.getStringValue("adminRoot"));
		request.setAttribute("imgRoot", resourceHolder.getStringValue("imgRoot"));
		
		String url = request.getRequestURI();
		System.out.println("请求URL="+url);
		if("/admin/doLogin".equals(url) || "/yzm".equals(url) || url.contains("/") ){
			return super.preHandle(request, response, handler);
		}
		
		return super.preHandle(request, response, handler);

	}



}
