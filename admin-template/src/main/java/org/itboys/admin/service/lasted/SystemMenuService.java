package org.itboys.admin.service.lasted;

import org.itboys.admin.entity.lasted.SystemMenu;
import org.itboys.commons.utils.tools.ListUtil;
import org.itboys.mongodb.core.MongoDataSource;
import org.itboys.mongodb.service.BaseMongoService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 作者：jiangxiong
 * 日期：2017年2月27日
 * 联系方式 ：<EMAIL>
 * 描述：系统菜单_Service_，江雄重新写的
 */
@Service
public class SystemMenuService extends BaseMongoService<SystemMenu> {



	@Resource(name = "adminDS")
	private MongoDataSource ds;

	@Override
	protected MongoDataSource getMongoDataSource() {
		return ds;
	}

	@Override
	protected Class<SystemMenu> getEntityClass() {
		return SystemMenu.class;
	}

	/**
	 * ----------------------雄哥自定义-----2017年2月27日--
	 */
	/**
     * 获取菜单树结构。
	 * @return
     */
	public List<SystemMenu> getTree(List<Long> menuIds){
			Map<String,Object> param =new HashMap<String, Object>();
			param.put("isDeleted",0);
			param.put("sort","sequence");
			param.put("order","ASC");
			if(ListUtil.isNotNull(menuIds)){
				param.put("id in",menuIds);
			}
			List<SystemMenu> all= super.list(param);
			List<SystemMenu> parent = new ArrayList<SystemMenu>();
			List<SystemMenu> children = new ArrayList<SystemMenu>();

			for(SystemMenu c:all){
				if(c.getParentId()!=null&&c.getParentId()==0L){
					parent.add(c);
				}else{
					children.add(c);
				}
			}
			//组装TREE
			for(SystemMenu p:parent){
				List<SystemMenu> child = new ArrayList<SystemMenu>();
				for(SystemMenu c:children){
					if(c.getParentId()!=null&&c.getParentId()==p.getId()){
						child.add(c);
					}
				}
				p.setChildren(child);
			}
			return parent;

		//return null;
	}

	/**
	 * 修改或新增
	 * @param menu
	 */
	public void saveMemnu(SystemMenu menu){
		if(menu.getId()==0L){
			//2017年3月16日
			menu.setIsMenu(1);
			super.save(menu);
		}else{
			super.updateExceptEmpty(menu.getId(),menu);
		}
		//return null;
	}

	/**
	 *删除
	 * @param id
	 */
	public void deleteMenu(long id){
		SystemMenu parent = super.getById(id);
		super.update(id,"isDeleted",1);

		if(parent.getParentId().longValue()==0L){
			Map<String,Object> param = new HashMap<String,Object>();
			param.put("parentId",id);
			param.put("isDeleted",0);
			//手动控制mongo事务操作
			try{
				super.delete(param);
			}catch (Exception e){
				e.printStackTrace();
				//回滚父类删除操作
				super.update(id,"isDeleted",0);
			}

		}
	}


}
