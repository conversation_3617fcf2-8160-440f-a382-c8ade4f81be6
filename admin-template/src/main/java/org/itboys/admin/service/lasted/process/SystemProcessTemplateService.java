package org.itboys.admin.service.lasted.process;


import org.itboys.admin.entity.lasted.process.SystemProcessTemplate;
import org.itboys.admin.entity.lasted.process.SystemProcessTemplateNode;
import org.itboys.commons.utils.tools.ListUtil;
import org.itboys.commons.utils.tools.LongUtil;
import org.itboys.mongodb.core.MongoDataSource;
import org.itboys.mongodb.service.BaseMongoService;
import org.itboys.param.PageResult;
import org.itboys.param.ParamUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 作者：jiangxiong
 * 日期：2017年2月27日
 * 联系方式 ：<EMAIL>
 * 描述：系统_流程_审核_模板_service
 */
@Service
public class SystemProcessTemplateService extends BaseMongoService<SystemProcessTemplate> {
    @Autowired
    private SystemProcessTemplateNodeService templateNodeService;



    @Resource(name = "adminDS")
    private MongoDataSource ds;

    @Override
    protected MongoDataSource getMongoDataSource() {
        return ds;
    }

    @Override
    protected Class<SystemProcessTemplate> getEntityClass() {
        return SystemProcessTemplate.class;
    }
    /**
     * 修改或新增
     * @param template
     */
    public Integer  saveTemplate(SystemProcessTemplate template){
        
        Map<String,Object> param = new HashMap<String,Object>();
        if(template.getId()==0L){
            //校验key是否重复
            param.clear();
            param.put("isDeleted",0);
            param.put("key",template.getKey());
            if(ListUtil.isNotNull(super.list(param))){
                    return -2;
            }
            super.save(template);
        }else{
            param.clear();
            param.put("isDeleted",0);
            param.put("key",template.getKey());
            param.put("id !=",template.getId());

            if(ListUtil.isNotNull(super.list(param))){
                return -2;
            }
            super.updateExceptEmpty(template.getId(),template);
        }
        return 0;
    }

    /**
     * 分页获取
     * @param request
     * @return
     */
    public PageResult<SystemProcessTemplate> selectTemplate(HttpServletRequest request){
        Map<String,Object> param =new HashMap<String, Object>();
        param.put("isDeleted", 0);
        Map<String,String> containsparam = ParamUtil.packageMongoVagueParam(request,"name","key");
        PageResult<SystemProcessTemplate> result=super.containpageQuery(request,containsparam,param);
        param.put("isDeleted",0);

        List<SystemProcessTemplateNode> nodes = templateNodeService.list(param);
        for(SystemProcessTemplate template:result.getData()){
            int i=0;
                for(SystemProcessTemplateNode node:nodes){
                    if(LongUtil.isNotZreo(node.getTemplateId())&&node.getTemplateId().longValue()==template.getId()){
                            i++;
                    }

                }
            template.setNodes(i);

        }
        return result;
    }
    /**
     * 单个查询
     * @param id
     * @return
     */
    public  SystemProcessTemplate findOne(Long id){
       if(LongUtil.isNotZreo(id)){
           return super.getById(id);
       }else{
          return new SystemProcessTemplate(id);

       }
    }

    /**
     * 删除流程（审核）模板
     * @param id
     */
    public void deleteTemplate(Long id){
            Map<String,Object> param = new HashMap<String,Object>();
            super.delete(id);
            //删除关联的节点
            param.clear();
             param.put("templateId",id);
             //mongo手动控制事务
             try {
                 templateNodeService.delete(param);
             }catch (Exception e){
                 super.update(id,"isDeleted",0);
             }

    }





}
