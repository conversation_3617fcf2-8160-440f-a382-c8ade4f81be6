<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.itboys.admin.dao.CityMapper">

	<resultMap id="cityRM" type="org.itboys.admin.entity.City">
		<result property="id" column="id" />
		<result property="name" column="Name" />
		<result property="parentId" column="ParentId" />
		<result property="shortName" column="ShortName" />
		<result property="levelType" column="LevelType" />
		<result property="cityCode" column="CityCode" />
		<result property="zipCode" column="ZipCode" />
		<result property="mergerName" column="MergerName" />
		<result property="lng" column="lng" />
		<result property="lat" column="Lat" />
		<result property="pinyin" column="Pinyin" />
	</resultMap>

	<select id="getAll" resultMap="cityRM">
		select * from citys
	</select>

</mapper>