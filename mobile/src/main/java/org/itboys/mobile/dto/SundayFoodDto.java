
package org.itboys.mobile.dto;

import org.itboys.mobile.common.MobileBaseDto;

/**
 * 作者：jiangxiong
 * 日期：2017年5月8日
 * 描述：百睿_一周食谱_dto。
 */

public class SundayFoodDto extends MobileBaseDto {
    private Integer type ;//类型。1上午点心，2课间水果，3餐前营养汤,4午餐，5下午点心
    private String name;//食谱名称
    private String image;//食谱图片
    private String imageHalf;//食谱图片半链接

    public SundayFoodDto(){
    }


    public SundayFoodDto(long id, String createTime, String createDate, Integer type, String name, String image,String imageHalf) {
        super(id, createTime, createDate);
        this.type = type;
        this.name = name;
        this.image = image;
        this.imageHalf =imageHalf;
    }

    public String getInfo(){
        return "字段说明：类型。1上午点心，2课间水果，3餐前营养汤,4午餐，5下午点心,name;//食谱名称image;//食谱图片(全链接，不要提交)imageHalf;//食谱图片半链接（提交此链接）";
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getImage() {
        return image;
    }

    public void setImage(String image) {
        this.image = image;
    }

    public String getImageHalf() {
        return imageHalf;
    }

    public void setImageHalf(String imageHalf) {
        this.imageHalf = imageHalf;
    }
}
