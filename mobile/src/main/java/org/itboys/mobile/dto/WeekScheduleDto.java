package org.itboys.mobile.dto;

import org.itboys.mobile.entity.mongo.schedule.ScheduleDto;
import org.itboys.mobile.entity.mongo.schedule.SundayScheduleEntity;

import java.util.List;

/**
 * @version V1.0
 * @created with IntelliJ IDEA.
 * @Title: entity
 * @author: shumlinmeng
 * @description:
 */
public class WeekScheduleDto {

    private Integer weekDay;

    List<ScheduleDto> schedules;

    public Integer getWeekDay() {
        return weekDay;
    }

    public void setWeekDay(Integer weekDay) {
        this.weekDay = weekDay;
    }

    public List<ScheduleDto> getSchedules() {
        return schedules;
    }

    public void setSchedules(List<ScheduleDto> schedules) {
        this.schedules = schedules;
    }
}
