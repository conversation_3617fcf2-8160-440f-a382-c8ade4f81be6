package org.itboys.mobile.util;


import org.apache.commons.lang3.StringUtils;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;


/**
 * 包名：com.hz.sunday.xlcl.utils
 * 作者 : 江雄
 * Email: <EMAIL>
 * 时间：2016年1月3日 下午4:10:43
 * 描述:AgeUtil。
 */

public class AgeUtil {
    /**
     *
     * @param birthDate
     * @return
     */
    public static String countAge(String todayDate,String birthDate){

        Calendar todayCal = Calendar.getInstance();
        Calendar birthDateCal = Calendar.getInstance();
        if(StringUtils.isNoneEmpty(todayDate)){
            try {
                todayCal.setTime( new SimpleDateFormat("yyyy-MM-dd").parse(todayDate));
            }catch(Exception exception){

            }
        }

        try {
            birthDateCal.setTime( new SimpleDateFormat("yyyy-MM-dd").parse(birthDate));
        }catch(Exception exception){

        }
        if(todayCal.getTimeInMillis() < birthDateCal.getTimeInMillis()) return "0岁";
        int year1 = todayCal.get(Calendar.YEAR);
        int year2 = birthDateCal.get(Calendar.YEAR);
        int month1 = todayCal.get(Calendar.MONTH);
        int month2 = birthDateCal.get(Calendar.MONTH);
        int day1 = todayCal.get(Calendar.DAY_OF_MONTH);
        int day2 = birthDateCal.get(Calendar.DAY_OF_MONTH);
        // 获取年的差值 假设 birthDateCal = 2015-8-16  d2 = 2011-9-30
        int yearInterval = year1 - year2;
        // 如果 birthDateCal的 月-日 小于 d2的 月-日 那么 yearInterval-- 这样就得到了相差的年数
        if(month1 < month2 || month1 == month2 && day1 < day2) yearInterval --;
        // 获取月数差值
        int monthInterval =  (month1 + 12) - month2  ;
        if(day1 < day2) monthInterval --;
        monthInterval %= 12;
        //return yearInterval * 12 + monthInterval;

        return yearInterval+"岁"+monthInterval+"月";
    }

}

