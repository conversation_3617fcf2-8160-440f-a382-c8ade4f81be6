package org.itboys.mobile.util.SMS;

import org.itboys.commons.utils.encryption.Digests;
import org.itboys.mobile.util.HttpHelper;

import java.util.HashMap;
import java.util.Map;

/**
 * 发送消息
 *
 * <AUTHOR>
 */
public class YuHuaSmsUtil {
    public  static String URL = "http://114.55.53.120/smsJson.aspx";
    public  static String NAME = "yhjy";
    public  static String PASSWORD = "yhjy001";
    private String response;

    public static void sendSMS(String mobile,String code){
        Map<String, Object> map = new HashMap<String, Object>();

        map.put("account", NAME);
        //密码加密处理
        map.put("password", Digests.md5(PASSWORD));
        map.put("mobile", mobile);
        //根据type发送不同内容
        String content = "【奇妙园在线】您的验证码是："+code+"。15分钟内有效，切勿将验证码泄露于他。";
        map.put("content",content);
        map.put("action", "send");
        String response = HttpHelper.doPostWithUtf8(URL,map);
        System.out.print("---------"+response);
    }
    public static void sendSMS2(String mobile,String name){
        Map<String, Object> map = new HashMap<String, Object>();

        map.put("account", NAME);
        //密码加密处理
        map.put("password", Digests.md5(PASSWORD));
        map.put("mobile", mobile);
        //根据type发送不同内容

        //String content = "【奇妙园在线】您的验证码是："+code+"。15分钟内有效，切勿将验证码泄露于他。";
        //String content = "【奇妙园在线】"+name+"家长你好，手机号："+mobile+"将自动绑定为“奇妙园在线”的用户登录账号，您的初始密码为：123456";


        String content = "【绿城育华】亲爱的"+name+"家长，您好！欢迎使用“奇妙园在线”APP，登录后您可在线查看宝贝成长记录，作品集和班级圈，更多惊喜请安装后查看哦~您的登录账号："+mobile+"手机号，初始密码：123456，请您点击http://t.cn/EMDEm7L 下载安装";
        map.put("content",content);
        map.put("action", "send");
        String response = HttpHelper.doPostWithUtf8(URL,map);
        System.out.print("---------"+response);
    }
    public static void main(String[] args) {
        sendSMS2("18968173081","张三");
    }

}