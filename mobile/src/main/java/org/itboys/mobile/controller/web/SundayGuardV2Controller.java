package org.itboys.mobile.controller.web;

import org.apache.commons.lang.StringUtils;
import org.itboys.admin.tools.AdminSessionHolder;
import org.itboys.commons.CommonConstants;
import org.itboys.framework.spring.controller.BaseController;
import org.itboys.mobile.common.SundayCommonConstants;
import org.itboys.mobile.entity.mongo.SundayGuard;
import org.itboys.mobile.service.mongo.web.SundayGuardService;
import org.itboys.mobile.util.ResponseMessage;
import org.itboys.param.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping("/sunday/web/guard/v2")
public class SundayGuardV2Controller extends BaseController {
    public static	Map<String,Object> search_param_temp;
    static{
        search_param_temp=new HashMap();
    }
    @Autowired
    private SundayGuardService guardService;

    /**
     * 园区主页
     * @param request
     * @param response
     * @param model
     * @return
     */
    @RequestMapping("/index" )
    public  String index(HttpServletRequest request,
                         HttpServletResponse response,
                         @RequestParam(value="changeType",required=false)Integer changeType,
                         Model model){
        model.addAttribute("changeType", changeType==null?1:changeType);
        boolean isClean = StringUtils.isNotEmpty(request.getParameter("isClean"))?false:true;
        logger.info("是否清除查询参数="+isClean);
        if(isClean){
            search_param_temp.clear();
        }
        model.addAttribute("param",search_param_temp);
        return "/sunday/guard/v2/index";
    }

    @RequestMapping(value="/select",method={RequestMethod.POST})
    public  String select(HttpServletRequest request,
                          HttpServletResponse response,
                          @RequestParam(value="changeType",required=true)Integer changeType,
                          Model model) {
        try{
            PageResult<SundayGuard> result=guardService.selectGuardV2(request, AdminSessionHolder.getGuardIds(),AdminSessionHolder.getBunchIds());


            model.addAttribute("result",result);
            //2018年10月18日，增加分页需要从参数
            packagePageParam(model,result.getTotal(),request);
            model.addAttribute("guards",result.getData());
            model.addAttribute("changeType", changeType);

            //2018年11月27日，捕获查询参数
            packageSearchParam(request,search_param_temp);
            return "/sunday/guard/v2/data";
        }catch (Exception e){
            logger.error("/sunday/guard/web/v2/select----e="+e.getMessage());
            e.printStackTrace();
            return "/sunday/error";
        }
    }


    /**
     * 不分页查询
     * @param request
     * @param response
     */
    @RequestMapping("/selectNoPage")
    public void selectNoPage(HttpServletRequest request,
                       HttpServletResponse response

    ) {
        try {
            List<SundayGuard> result=guardService.selectGuardNoPage(request, AdminSessionHolder.getGuardIds(),AdminSessionHolder.getBunchIds());
            ResponseMessage.success(result);
        } catch (Exception e) {
            e.printStackTrace();
            ResponseMessage.error(CommonConstants.FAIL_CODE,CommonConstants.ERRORMSG);
        }
    }
    /**
     * 详情
     * @param request
     * @param id
     * @param id
     * @return
     */
    @RequestMapping("/input")
    public String input(
            HttpServletRequest request,
            HttpServletResponse response,
            @RequestParam(value="id",required=true)Long id,
            Model model){
        Map<String,Object> result = guardService.findOne(id);
        model.addAttribute("result", result);
        return "/sunday/guard/v2/input";
    }


    /**
     * 新增或修改
     * @param request
     * @param response
     * @param guard
     */
    @RequestMapping("/save")
    public  void save(HttpServletRequest request,
                      HttpServletResponse response,
                      @ModelAttribute SundayGuard guard
                     /* @RequestParam(value="bunchIds",required=false)List<Long> bunchIds,
                      @RequestParam(value="bunchNames",required=false)List<String> bunchNames,
                      @RequestParam(value="bunchAgeStarts",required=false) List<String> bunchAgeStarts,
                      @RequestParam(value="bunchAgeEnds",required=false) List<String> bunchAgeEnds,
                      @RequestParam(value="bunchRemarks",required=false)List<String> bunchRemarks*/
    ) {
        try {
            guardService.saveGuard(guard);
            ResponseMessage.success();
        } catch (Exception e) {
            String msg = "";
            if (SundayCommonConstants.ERROR_EXCEPTION.equalsIgnoreCase(e.getClass().getSimpleName())) {
                msg = e.getMessage();
            } else {
                msg = CommonConstants.ERRORMSG;
                logger.error("/sunday/web/guard/v2/save------" + e.getMessage());
            }
            e.printStackTrace();

            ResponseMessage.error(CommonConstants.FAIL_CODE,msg);
        }
    }
    @RequestMapping("/change")
    public  void change(HttpServletRequest request,
                      HttpServletResponse response,
                        @RequestParam(value="id",required=false)Long id,
                      @RequestParam(value="status",required=false)Integer status

    ) {
        try {
            guardService.changeGuard(id,status);
            ResponseMessage.success();
        } catch (Exception e) {
            String msg = "";
            if (SundayCommonConstants.ERROR_EXCEPTION.equalsIgnoreCase(e.getClass().getSimpleName())) {
                msg = e.getMessage();
            } else {
                msg = CommonConstants.ERRORMSG;
                logger.error("/sunday/web/guard/v2/change------" + e.getMessage());
            }
            e.printStackTrace();

            ResponseMessage.error(CommonConstants.FAIL_CODE,msg);
        }
    }
    @RequestMapping("/getCharData")
    public void getCharData(HttpServletRequest request,
                             HttpServletResponse response,
                            @RequestParam(value="id",required=false)Long id

    ) {
        try {
            Map<String,Object> result=guardService.getCharData(id);
            ResponseMessage.success(result);
        } catch (Exception e) {
            e.printStackTrace();
            ResponseMessage.error(CommonConstants.FAIL_CODE,CommonConstants.ERRORMSG);
        }
    }
    /**
     * 选择器
     * @param request
     * @param isSingle
     * @param param
     * @param model
     * @return
     */
    @RequestMapping("/choose")
    public String choose(HttpServletRequest request,
                         @RequestParam(value = "isSingle", required = true) Integer isSingle,//是否单选
                         @RequestParam(value = "callBack", required = false) String callBack,//回调参数
                         @RequestParam(value = "param", required = false) String param,//查询参数
                         Model model) {
        model.addAttribute("param", param);
        model.addAttribute("callBack", callBack);
        if (isSingle == null) {
            return "/sunday/guard/choose/choose_erro";
        } else if (isSingle == 1) {
            return "/sunday/guard/choose/choose_single";
        } else if (isSingle == 0) {
            return "/sunday/guard/choose/choose_mulit";
        }

        return "/finance/guard/choose/choose_erro";
    }
}
