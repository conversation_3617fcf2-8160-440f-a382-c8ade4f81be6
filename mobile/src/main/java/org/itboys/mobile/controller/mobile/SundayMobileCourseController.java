package org.itboys.mobile.controller.mobile;

import com.wordnik.swagger.annotations.Api;
import com.wordnik.swagger.annotations.ApiOperation;
import com.wordnik.swagger.annotations.ApiParam;
import org.itboys.commons.CommonConstants;
import org.itboys.framework.spring.controller.BaseController;
import org.itboys.mobile.common.SundayCommonConstants;
import org.itboys.mobile.dto.SundayCourseDto;
import org.itboys.mobile.entity.mongo.course.SundayCourse;
import org.itboys.mobile.service.mongo.front.SundayFrontCourseService;
import org.itboys.mobile.util.AccountIdHoldUtils;
import org.itboys.mobile.util.MobileSignUtils;
import org.itboys.mobile.util.ResponseMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 作者：jiangxiong
 * 日期：2017年2月8日
 * 联系方式 ：<EMAIL>
 * 描述：百睿_课程_controller。
 */
@Controller
@RequestMapping("/sunday/mobile/course")
@Api(value = "course", description = "课程接口")
public class SundayMobileCourseController extends BaseController {
    @Autowired
    private SundayFrontCourseService courseService;


    @RequestMapping(value="/getCourse", method = {RequestMethod.POST})
    @ApiOperation(value="【家长端】获取全部课程。不分页")
    public void getCourse(HttpServletRequest request,
                      HttpServletResponse response,
                    @ApiParam(required=true, name="type", value="【参与签名】课程类型。1家长课程，2亲子教育课程")@RequestParam(value = "type", required = true)Integer type,
                    @ApiParam(value = "随机数。【参与签名】")@RequestParam(value = "nonce", required = true) String nonce,
                    @ApiParam(value = "签名")@RequestParam(value = "sign", required = true) String sign
    ){
        try{
            Map<String, Object> param =new HashMap<String,Object>();
            param.put("nonce", nonce);
            param.put("sign", sign);
            param.put("type", type);
            boolean signSer = MobileSignUtils.signCheck(param, sign);
            if(!signSer){
                logger.warn("签名校验失败");
            }
            Long memberId = AccountIdHoldUtils.getMemberId();
            List<SundayCourseDto> result = courseService.getCourse(memberId,null,type);
            ResponseMessage.success(result);
        }catch (Exception e){
            String msg = CommonConstants.ERRORMSG;
            Integer msgCode= CommonConstants.FAIL_CODE;
            if(e.getClass().getSimpleName().equalsIgnoreCase("ErrorException")){
                msg=e.getMessage();
            }else  if(e.getClass().getSimpleName().equalsIgnoreCase("TokenException")){
                msgCode= CommonConstants.TOKEN_CODE;
                msg= CommonConstants.TOKENMSG;
            }else{
                logger.error("/sunday/mobile/course/getCourse----e="+e.getMessage());
            }
            e.printStackTrace();
            ResponseMessage.error(msgCode,msg);
        }
    }
    @RequestMapping(value="/getHistory", method = {RequestMethod.POST})
    @ApiOperation(value="【家长端】获取课程历史学习记录(不分页)")
    public void getHistory(HttpServletRequest request,
                          HttpServletResponse response,
                          @ApiParam(required=true, name="type", value="【参与签名】课程类型。1家长课程，2亲子教育课程，99全部")@RequestParam(value = "type", required = true)Integer type,
                          @ApiParam(value = "随机数。【参与签名】")@RequestParam(value = "nonce", required = true) String nonce,
                          @ApiParam(value = "签名")@RequestParam(value = "sign", required = true) String sign
    ){
        try{
            Map<String, Object> param =new HashMap<String,Object>();
            param.put("nonce", nonce);
            param.put("sign", sign);
            param.put("type", type);
            boolean signSer = MobileSignUtils.signCheck(param, sign);
            if(!signSer){
                logger.warn("签名校验失败");
            }
            Long memberId = AccountIdHoldUtils.getMemberId();
            Map<String,Object> result = courseService.getHistory(memberId,type);
            ResponseMessage.success(result);
        }catch (Exception e){
            String msg = CommonConstants.ERRORMSG;
            Integer msgCode= CommonConstants.FAIL_CODE;
            if(e.getClass().getSimpleName().equalsIgnoreCase("ErrorException")){
                msg=e.getMessage();
            }else  if(e.getClass().getSimpleName().equalsIgnoreCase("TokenException")){
                msgCode= CommonConstants.TOKEN_CODE;
                msg= CommonConstants.TOKENMSG;
            }else{
                logger.error("/sunday/mobile/course/getHistory----e="+e.getMessage());
            }
            e.printStackTrace();
            ResponseMessage.error(msgCode,msg);
        }
    }
    @RequestMapping(value="/getDetail", method = {RequestMethod.POST})
    @ApiOperation(value="【家长端】获取课程详情（含章节，评论）")
    public void getDetail(HttpServletRequest request,
                      HttpServletResponse response,
                      @ApiParam(required=true, name="courseId", value="课程Id")@RequestParam(value = "courseId", required = true)Long courseId,
                      @ApiParam(required=false, name="chapterId", value="章节ID。默认0或空,自动对应章节的的视频或音频")@RequestParam(value = "chapterId", required = true)Long chapterId,
                      @ApiParam(value = "随机数。【参与签名】")@RequestParam(value = "nonce", required = true) String nonce,
                      @ApiParam(value = "签名")@RequestParam(value = "sign", required = true) String sign
    ){
        try{
            Map<String, Object> param =new HashMap<String,Object>();
            param.put("nonce", nonce);
            param.put("sign", sign);

            boolean signSer = MobileSignUtils.signCheck(param, sign);
            if(!signSer){
                logger.warn("签名校验失败");
            }
            Long memberId = AccountIdHoldUtils.getMemberId();
            SundayCourseDto result = courseService.getDetail(courseId,memberId,chapterId);
            ResponseMessage.success(result);
        }catch (Exception e){
            String msg = CommonConstants.ERRORMSG;
            Integer msgCode= CommonConstants.FAIL_CODE;
            if(e.getClass().getSimpleName().equalsIgnoreCase("ErrorException")){
                msg=e.getMessage();
            }else  if(e.getClass().getSimpleName().equalsIgnoreCase("TokenException")){
                msgCode= CommonConstants.TOKEN_CODE;
                msg= CommonConstants.TOKENMSG;
            }else{
                logger.error("/sunday/mobile/course/getDetail----e="+e.getMessage());
            }
            e.printStackTrace();
            ResponseMessage.error(msgCode,msg);
        }
    }
    @RequestMapping(value="/study", method = {RequestMethod.POST})
    @ApiOperation(value="【家长端】记录学习进度（仅对家长课程有效）")
    public void study(HttpServletRequest request,
                          HttpServletResponse response,
                          @ApiParam(required=true, name="courseId", value="课程ID")@RequestParam(value = "courseId", required = true)Long courseId,
                          @ApiParam(required=true, name="chapterId", value="章节ID。")@RequestParam(value = "chapterId", required = true)Long chapterId,
                          @ApiParam(required=true, name="second", value="当前学习时间（秒）。")@RequestParam(value = "second", required = true)Integer second,

                          @ApiParam(value = "随机数。【参与签名】")@RequestParam(value = "nonce", required = true) String nonce,
                          @ApiParam(value = "签名")@RequestParam(value = "sign", required = true) String sign
    ){
        try{
            Map<String, Object> param =new HashMap<String,Object>();
            param.put("nonce", nonce);
            param.put("sign", sign);

            boolean signSer = MobileSignUtils.signCheck(param, sign);
            if(!signSer){
                logger.warn("签名校验失败");
            }
            Long memberId = AccountIdHoldUtils.getMemberId();
            courseService.study(memberId,courseId,chapterId,second);
            ResponseMessage.success();
        }catch (Exception e){
            String msg = CommonConstants.ERRORMSG;
            Integer msgCode= CommonConstants.FAIL_CODE;
            if(e.getClass().getSimpleName().equalsIgnoreCase("ErrorException")){
                msg=e.getMessage();
            }else  if(e.getClass().getSimpleName().equalsIgnoreCase("TokenException")){
                msgCode= CommonConstants.TOKEN_CODE;
                msg= CommonConstants.TOKENMSG;
            }else{
                logger.error("/sunday/mobile/course/getDetail----e="+e.getMessage());
            }
            e.printStackTrace();
            ResponseMessage.error(msgCode,msg);
        }
    }
    @RequestMapping(value="/comment/save", method = {RequestMethod.POST})
    @ApiOperation(value="【家长端】课程评论（学习心得）提交")
    public void comment_save(HttpServletRequest request,
                      HttpServletResponse response,
                    @ApiParam(required=true, name="courseId", value="课程Id")@RequestParam(value = "courseId", required = true)Long courseId,
                    @ApiParam(required=true, name="content", value="内容")@RequestParam(value = "content", required = true)String content,
                    @ApiParam(required=true, name="funScore", value="乐趣评分（1-5分默认5分）")@RequestParam(value = "funScore", required = true)Integer funScore,
                    @ApiParam(required=true, name="gainScore", value="收获评分（1-5分默认5分）")@RequestParam(value = "gainScore", required = true)Integer gainScore,

                             @ApiParam(value = "随机数。【参与签名】")@RequestParam(value = "nonce", required = true) String nonce,
                      @ApiParam(value = "签名")@RequestParam(value = "sign", required = true) String sign
    ){
        try{
            Map<String, Object> param =new HashMap<String,Object>();
            param.put("nonce", nonce);
            param.put("sign", sign);

            boolean signSer = MobileSignUtils.signCheck(param, sign);
            if(!signSer){
                logger.warn("签名校验失败");
            }
            Long memberId = AccountIdHoldUtils.getMemberId();
             courseService.saveComment( courseId, memberId, content, funScore, gainScore);
            ResponseMessage.success();
        }catch (Exception e){
            String msg = CommonConstants.ERRORMSG;
            Integer msgCode= CommonConstants.FAIL_CODE;
            if(e.getClass().getSimpleName().equalsIgnoreCase("ErrorException")){
                msg=e.getMessage();
            }else  if(e.getClass().getSimpleName().equalsIgnoreCase("TokenException")){
                msgCode= CommonConstants.TOKEN_CODE;
                msg= CommonConstants.TOKENMSG;
            }else{
                logger.error("/sunday/mobile/course/comment/save----e="+e.getMessage());
            }
            e.printStackTrace();
            ResponseMessage.error(msgCode,msg);
        }
    }
    @RequestMapping(value="/comment/zan", method = {RequestMethod.POST})
    @ApiOperation(value="【家长端】课程评论（学习心得）点赞/或取消点赞")
    public void comment_zan(HttpServletRequest request,
                             HttpServletResponse response,
                             @ApiParam(required=true, name="commentId", value="评论ID")@RequestParam(value = "commentId", required = true)Long commentId,
                             @ApiParam(value = "随机数。【参与签名】")@RequestParam(value = "nonce", required = true) String nonce,
                             @ApiParam(value = "签名")@RequestParam(value = "sign", required = true) String sign
    ){
        try{
            Map<String, Object> param =new HashMap<String,Object>();
            param.put("nonce", nonce);
            param.put("sign", sign);

            boolean signSer = MobileSignUtils.signCheck(param, sign);
            if(!signSer){
                logger.warn("签名校验失败");
            }
            Long memberId = AccountIdHoldUtils.getMemberId();
            courseService.saveZan(commentId,memberId);
            ResponseMessage.success();
        }catch (Exception e){
            String msg = CommonConstants.ERRORMSG;
            Integer msgCode= CommonConstants.FAIL_CODE;
            if(e.getClass().getSimpleName().equalsIgnoreCase("ErrorException")){
                msg=e.getMessage();
            }else  if(e.getClass().getSimpleName().equalsIgnoreCase("TokenException")){
                msgCode= CommonConstants.TOKEN_CODE;
                msg= CommonConstants.TOKENMSG;
            }else{
                logger.error("/sunday/mobile/course/comment/zan----e="+e.getMessage());
            }
            e.printStackTrace();
            ResponseMessage.error(msgCode,msg);
        }
    }
    @RequestMapping(value="/h5/getDetail",method={RequestMethod.GET})
    @ApiOperation(value="【家长端】获取详情(html5)，网页url在getDetail接口中有返回。")
    public  String h5_getResult(HttpServletRequest request,
                             HttpServletResponse response,
                             Model model,
                             @ApiParam(required = true, name = "courseId", value = "课程ID，") @RequestParam(value = "courseId", required = true) Long courseId){
        try{
            Long memberId  = AccountIdHoldUtils.getMemberId();
         SundayCourse course = courseService.getDetailH5(courseId,memberId);
         model.addAttribute("course",course);
         if(course.getType()== SundayCommonConstants.course_type_1){
             return "/yuhua/mobile/course/detail1";
         }
         if(course.getType()== SundayCommonConstants.course_type_2){
             return "/yuhua/mobile/course/detail2";
         }
        return "";

        }catch (Exception e){
            logger.error("/sunday/mobile/course/h5/getDetail----e="+e.getMessage());
            e.printStackTrace();
            return "/yuhua/mobile/error";
        }
    }
}
