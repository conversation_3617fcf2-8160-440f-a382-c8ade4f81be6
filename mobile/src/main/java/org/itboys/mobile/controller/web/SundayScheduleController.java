package org.itboys.mobile.controller.web;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.itboys.admin.tools.AdminSessionHolder;
import org.itboys.commons.CommonConstants;
import org.itboys.commons.utils.sms.SmsConstants;
import org.itboys.commons.utils.time.TimeUtils;
import org.itboys.commons.utils.tools.LongUtil;
import org.itboys.framework.spring.controller.BaseController;
import org.itboys.mobile.common.SundayCommonConstants;
import org.itboys.mobile.entity.mongo.SundayBunch;
import org.itboys.mobile.entity.mongo.SundayGuard;
import org.itboys.mobile.entity.mongo.schedule.SundayScheduleCategory;
import org.itboys.mobile.entity.mongo.schedule.SundayScheduleEntity;
import org.itboys.mobile.entity.mysql.schedule.SundaySchedule;
import org.itboys.mobile.entity.mysql.schedule.SundayScheduleTemporary;
import org.itboys.mobile.service.mongo.web.*;
import org.itboys.mobile.util.DatetUtil;
import org.itboys.mobile.util.ResponseMessage;
import org.itboys.param.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@Controller
@RequestMapping("/sunday/web/schedule")
public class SundayScheduleController extends BaseController {
    public static Map<String,Object> search_param_temp;
    static{
        search_param_temp=new HashMap();
    }
    @Autowired
    private SundayScheduleService scheduleService;

    @Autowired
    private SundayScheduleCategoryService categoryService;

    @Autowired
    private SundayGuardService guardService;

    @Autowired
    private SundayBunchService bunchService;

    //课表
    @RequestMapping("/index")
    public String index(HttpServletRequest request,Model model) {
        model.addAttribute("param",search_param_temp);
        boolean isClean = StringUtils.isNotEmpty(request.getParameter("isClean"))?false:true;
        logger.info("是否清除查询参数="+isClean);
        if(isClean){
            search_param_temp.clear();
        }
        List<SundayGuard> guards = guardService.selectGuardNoPage(request, AdminSessionHolder.getGuardIds(),AdminSessionHolder.getBunchIds());
        model.addAttribute("guards",guards);
        return "/sunday/schedule/index";
    }

    //排课
    @RequestMapping("/index2")
    public String index2(HttpServletRequest request, @RequestParam(value = "type")Integer type,Model model) {
        String url = "";
        if(type == 1){
            url = "/sunday/schedule/index2";
        }else if(type==2){
            url = "sunday/schedule/waitSchedule";
        }
        model.addAttribute("param",search_param_temp);
        boolean isClean = StringUtils.isNotEmpty(request.getParameter("isClean"))?false:true;
        logger.info("是否清除查询参数="+isClean);
        if(isClean){
            search_param_temp.clear();
        }
        List<SundayGuard> guards = guardService.selectGuardNoPage(request, AdminSessionHolder.getGuardIds(),AdminSessionHolder.getBunchIds());
        model.addAttribute("guards",guards);
        return url;
    }

    @RequestMapping(value = "/top")
    public String top(@RequestParam(value = "type") Integer type, Model model) {
        model.addAttribute("type",type);
        return "/sunday/schedule/top";
    }

    /**
     * 新增课表或修改
     */
    @RequestMapping("/saveSch")
    public void save(@ModelAttribute SundayScheduleEntity scheduleEntity) {
        try {
            scheduleEntity.setCreator(AdminSessionHolder.getAdminUserId());
            scheduleService.saveSchedule(scheduleEntity);
            ResponseMessage.success();
        } catch (Exception e) {
            e.printStackTrace();
            String msg ="";
            if(e.getClass().getSimpleName().equalsIgnoreCase("ErrorException")){
                msg=e.getMessage();
            }else{
                msg= CommonConstants.ERRORMSG;
                logger.error("/sunday/web/schedule/saveSch------"+e.getMessage());
            }
            e.printStackTrace();
            ResponseMessage.error(CommonConstants.FAIL_CODE,msg);
        }
    }

    /**
     * 批量删除课表
     */
    @RequestMapping("/batchDeleteSchedule")
    public  void batchDeleteSchedule(@RequestParam(value="scheduleIds",required=true)List<Long> scheduleIds) {
        try {
            scheduleService.batchDeleteSchedule(scheduleIds);
            ResponseMessage.success();
        } catch (Exception e) {
            String msg = "";
            if (SundayCommonConstants.ERROR_EXCEPTION.equalsIgnoreCase(e.getClass().getSimpleName())) {
                msg = e.getMessage();
            } else {
                msg = CommonConstants.ERRORMSG;
                logger.error("/sunday/web/schedule/batchDeleteSchedule------" + e.getMessage());
            }
            e.printStackTrace();

            ResponseMessage.error(CommonConstants.FAIL_CODE,msg);
        }
    }

    /**
     * 删除课表
     */
    @RequestMapping("/deleteSchedule")
    public  void deleteSchedule(@RequestParam(value="scheduleId",required=true)Long scheduleId) {
        try {
            scheduleService.deleteSchedule(scheduleId);
            ResponseMessage.success();
        } catch (Exception e) {
            String msg = "";
            if (SundayCommonConstants.ERROR_EXCEPTION.equalsIgnoreCase(e.getClass().getSimpleName())) {
                msg = e.getMessage();
            } else {
                msg = CommonConstants.ERRORMSG;
                logger.error("/sunday/web/schedule/batchDeleteSchedule------" + e.getMessage());
            }
            e.printStackTrace();

            ResponseMessage.error(CommonConstants.FAIL_CODE,msg);
        }
    }

    /**
     * 删除父课表
     */
    @RequestMapping("/deleteFatherSchedule")
    public  void deleteFatherSchedule(@RequestParam(value="scheduleId",required=true)Long scheduleId) {
        try {
            scheduleService.deleteFatherSchedule(scheduleId);
            ResponseMessage.success();
        } catch (Exception e) {
            String msg = "";
            if (SundayCommonConstants.ERROR_EXCEPTION.equalsIgnoreCase(e.getClass().getSimpleName())) {
                msg = e.getMessage();
            } else {
                msg = CommonConstants.ERRORMSG;
                logger.error("/sunday/web/schedule/batchDeleteSchedule------" + e.getMessage());
            }
            e.printStackTrace();

            ResponseMessage.error(CommonConstants.FAIL_CODE,msg);
        }
    }



    /**
     * 编辑重复
     */
    @RequestMapping("/editSchedule")
    public void editSchedule(@ModelAttribute SundayScheduleEntity scheduleEntity) {
        try {
            scheduleService.editSchedule(scheduleEntity);
            ResponseMessage.success();
        } catch (Exception e) {
            e.printStackTrace();
            String msg ="";
            if(e.getClass().getSimpleName().equalsIgnoreCase("ErrorException")){
                msg=e.getMessage();
            }else{
                msg= CommonConstants.ERRORMSG;
                logger.error("/sunday/web/schedule/editSchedule------"+e.getMessage());
            }
            e.printStackTrace();
            ResponseMessage.error(CommonConstants.FAIL_CODE,msg);
        }
    }

    /**
     * 单次编辑课程表
     * @param scheduleEntity
     */
    @RequestMapping("/editScheduleByOnce")
    public void editScheduleByOnce(@ModelAttribute SundayScheduleEntity scheduleEntity) {
        try {
            scheduleService.editScheduleByOnce(scheduleEntity);
            ResponseMessage.success();
        } catch (Exception e) {

            e.printStackTrace();
            String msg ="";
            if(e.getClass().getSimpleName().equalsIgnoreCase("ErrorException")){
                msg=e.getMessage();
            }else{
                msg= CommonConstants.ERRORMSG;
                logger.error("/sunday/web/schedule/editScheduleByOnce------"+e.getMessage());
            }
            e.printStackTrace();
            ResponseMessage.error(CommonConstants.FAIL_CODE,msg);
        }
    }

    /**
     * 获取课程父表
     */
    @RequestMapping("/getFatherSchedule")
    public  void getFatherSchedule(@RequestParam(value="fatherId",required=true)Long fatherId) {
        try {
            SundayScheduleEntity schedule = scheduleService.getById(fatherId);
            ResponseMessage.success(schedule);
        } catch (Exception e) {
            String msg = "";
            if (SundayCommonConstants.ERROR_EXCEPTION.equalsIgnoreCase(e.getClass().getSimpleName())) {
                msg = e.getMessage();
            } else {
                msg = CommonConstants.ERRORMSG;
                logger.error("/sunday/web/schedule/getFatherSchedule------" + e.getMessage());
            }
            e.printStackTrace();

            ResponseMessage.error(CommonConstants.FAIL_CODE,msg);
        }
    }

    /**
     * 获取课表
     */
    @RequestMapping("/getScheduleById")
    public  void getScheduleById(@RequestParam(value="scheduleId",required=true)Long scheduleId) {
        try {
            SundayScheduleEntity schedule = scheduleService.getById(scheduleId);
            ResponseMessage.success(schedule);
        } catch (Exception e) {
            String msg = "";
            if (SundayCommonConstants.ERROR_EXCEPTION.equalsIgnoreCase(e.getClass().getSimpleName())) {
                msg = e.getMessage();
            } else {
                msg = CommonConstants.ERRORMSG;
                logger.error("/sunday/web/schedule/getScheduleById------" + e.getMessage());
            }
            e.printStackTrace();

            ResponseMessage.error(CommonConstants.FAIL_CODE,msg);
        }
    }


    /**
     * 停课
     */
    @RequestMapping("/stopSchedule")
    public  void stopSchedule(@RequestParam(value="scheduleId",required=true)Long scheduleId) {
        try {
            scheduleService.stopScheduleByType(scheduleId);
            ResponseMessage.success();
        } catch (Exception e) {
            String msg = "";
            if (SundayCommonConstants.ERROR_EXCEPTION.equalsIgnoreCase(e.getClass().getSimpleName())) {
                msg = e.getMessage();
            } else {
                msg = CommonConstants.ERRORMSG;
                logger.error("/sunday/web/schedule/batchDeleteSchedule------" + e.getMessage());
            }
            e.printStackTrace();

            ResponseMessage.error(CommonConstants.FAIL_CODE,msg);
        }
    }

    /**
     * 恢复课表
     */
    @RequestMapping("/recoverSchedule")
    public  void recoverSchedule(@RequestParam(value="scheduleId",required=true)Long scheduleId) {
        try {
            scheduleService.recoverSchedule(scheduleId);
            ResponseMessage.success();
        } catch (Exception e) {
            String msg = "";
            if (SundayCommonConstants.ERROR_EXCEPTION.equalsIgnoreCase(e.getClass().getSimpleName())) {
                msg = e.getMessage();
            } else {
                msg = CommonConstants.ERRORMSG;
                logger.error("/sunday/web/schedule/recoverSchedule------" + e.getMessage());
            }
            e.printStackTrace();

            ResponseMessage.error(CommonConstants.FAIL_CODE,msg);
        }
    }


    /**
     * 批量停课
     */
    @RequestMapping("/batchStopSchedule")
    public  void batchStopSchedule(@RequestParam(value="scheduleIds",required=true)String scheduleIds) {
        try {
            scheduleService.batchStopScheduleByType(scheduleIds);
            ResponseMessage.success();
        } catch (Exception e) {
            String msg = "";
            if (SundayCommonConstants.ERROR_EXCEPTION.equalsIgnoreCase(e.getClass().getSimpleName())) {
                msg = e.getMessage();
            } else {
                msg = CommonConstants.ERRORMSG;
                logger.error("/sunday/web/schedule/batchStopSchedule------" + e.getMessage());
            }
            e.printStackTrace();

            ResponseMessage.error(CommonConstants.FAIL_CODE,msg);
        }
    }


    /**
     *
     * @param request
     * @param model
     * @return 本周课表
     */
    @RequestMapping(value="/selectScheduleByWeek",method={RequestMethod.POST})
    public String selectScheduleByWeek(HttpServletRequest request,Model model) {
        try{
            List<SundayScheduleEntity> result=scheduleService.selectScheduleByWeek(request, AdminSessionHolder.getGuardIds());
            Collections.sort(result,new Comparator(){
                @Override
                public int compare(Object o1, Object o2) {
                    SundayScheduleEntity u1 = (SundayScheduleEntity)o1;
                    SundayScheduleEntity u2 = (SundayScheduleEntity)o2;
                    boolean i = u1.getBeginTime().before(u2.getBeginTime());
                    boolean k = u2.getBeginTime().before(u1.getBeginTime());
                    int flag=0;
                    if(i){
                        flag=-1;
                    }else if(k){
                        flag=1;
                    }else{
                        flag=scheduleService.compare(u1,u2);
                    }
                    return flag;
                }
            });
            model.addAttribute("schedule",result);
            String nextFlag = request.getParameter("next");
            int next = 0;
            if(StringUtils.isNotBlank(nextFlag)){
                next = Integer.parseInt(nextFlag);
            }
            String begin = DatetUtil.getWeekStartDate();
            String end = DatetUtil.getWeekEndDate();
            String beginTime = TimeUtils.formatToString(DatetUtil.getNextWeekDay(begin,next));
            String endTime = TimeUtils.formatToString(DatetUtil.getNextWeekDay(end,next));
            model.addAttribute("beginTime", beginTime);
            model.addAttribute("endTime",endTime);
            model.addAttribute("week",DatetUtil.getWeek(beginTime));
            model.addAttribute("now",DatetUtil.dayForWeek(DatetUtil.formatDate(CommonConstants.DATE.FORMAT_YYYY_MM_dd,new Date())));
            return "/sunday/schedule/dataNew";
        }catch (Exception e){
            logger.error("/sunday/schedule/selectScheduleByWeek----e="+e.getMessage());
            e.printStackTrace();
            return "/sunday/error";
        }
    }

    /**
     *
     * @param request
     * @param model
     * @return 本周课表(学生)
     */
    @RequestMapping(value="/selectScheduleByStudent",method={RequestMethod.POST})
    public String selectScheduleByStudent(HttpServletRequest request, @RequestParam(value="studentId")Long studentId, Model model) {
        try{
            String nextFlag = request.getParameter("next");
            int next = 0;
            if(StringUtils.isNotBlank(nextFlag)){
                next = Integer.parseInt(nextFlag);
            }
            String begin = DatetUtil.getWeekStartDate();
            String end = DatetUtil.getWeekEndDate();

            List<SundayScheduleEntity> result=scheduleService.findWeekScheduleByStudentV2(studentId,next);
            String beginTime = TimeUtils.formatToString(DatetUtil.getNextWeekDay(begin,next));
            String endTime = TimeUtils.formatToString(DatetUtil.getNextWeekDay(end,next));
            Collections.sort(result,new Comparator(){
                @Override
                public int compare(Object o1, Object o2) {
                    SundayScheduleEntity u1 = (SundayScheduleEntity)o1;
                    SundayScheduleEntity u2 = (SundayScheduleEntity)o2;
                    boolean i = u1.getBeginTime().before(u2.getBeginTime());
                    boolean k = u2.getBeginTime().before(u1.getBeginTime());
                    int flag=0;
                    if(i){
                        flag=-1;
                    }else if(k){
                        flag=1;
                    }else{
                        flag=scheduleService.compare(u1,u2);
                    }
                    return flag;
                }
            });
            model.addAttribute("schedule",result);
            model.addAttribute("beginTime", beginTime);
            model.addAttribute("endTime",endTime);
            model.addAttribute("week",DatetUtil.getWeek(beginTime));
            model.addAttribute("now",DatetUtil.dayForWeek(DatetUtil.formatDate(CommonConstants.DATE.FORMAT_YYYY_MM_dd,new Date())));
//            return "/sunday/schedule/studentSchedule";
            return "/sunday/schedule/studentScheduleNew";
        }catch (Exception e){
            logger.error("/sunday/schedule/selectScheduleByStudent----e="+e.getMessage());
            e.printStackTrace();
            return "/sunday/error";
        }
    }



    /**
     *
     * @param request
     * @param model
     * @return 本周课表(排课)
     */
    @RequestMapping(value="/selectScheduleByWeekForPaiKe",method={RequestMethod.POST})
    public  String selectScheduleByWeekForPaiKe(HttpServletRequest request,Model model) {
        try{
            List<SundayScheduleEntity> result=scheduleService.selectScheduleByWeek(request, AdminSessionHolder.getGuardIds());

            /*Collections.sort(result, new Comparator<SundayScheduleEntity>() {
                public int compare(SundayScheduleEntity u1, SundayScheduleEntity u2) {
                    long diff = u1.getBeginTimeLong() - u2.getBeginTimeLong();
                    if (diff > 0) {
                        return 1;
                    } else if (diff < 0) {
                        return -1;
                    }
                    if(diff == 0){
                  *//*      String[] u1_1=u1.getStartInterval().split(":");
                        String[] u1_2=u1.getEndInterval().split(":");
                        String[] u2_1=u2.getStartInterval().split(":");*//*
                    }
                    return 0;
                }
            });*/
            /*
            Collections.sort(result,
                    (o1,o2)->o1.getBeginTime().before(o2.getBeginTime())?-1:o2.getBeginTime().before(o1.getBeginTime())?1:scheduleService.compare(o1,o2));*/
            Collections.sort(result,new Comparator(){
                @Override
                public int compare(Object o1, Object o2) {
                    SundayScheduleEntity u1 = (SundayScheduleEntity)o1;
                    SundayScheduleEntity u2 = (SundayScheduleEntity)o2;
                    boolean i = u1.getBeginTime().before(u2.getBeginTime());
                    boolean k = u2.getBeginTime().before(u1.getBeginTime());
                    int flag=0;
                    if(i){
                        flag=-1;
                    }else if(k){
                        flag=1;
                    }else{
                        flag=scheduleService.compare(u1,u2);
                    }
                    return flag;
                }
            });

            model.addAttribute("schedule",result);
            String nextFlag = request.getParameter("next");
            int next = 0;
            if(StringUtils.isNotBlank(nextFlag)){
                next = Integer.parseInt(nextFlag);
            }
            String begin = DatetUtil.getWeekStartDate();
            String end = DatetUtil.getWeekEndDate();
            String beginTime = TimeUtils.formatToString(DatetUtil.getNextWeekDay(begin,next));
            String endTime = TimeUtils.formatToString(DatetUtil.getNextWeekDay(end,next));
            model.addAttribute("beginTime", beginTime);
            model.addAttribute("endTime",endTime);
            model.addAttribute("week",DatetUtil.getWeek(beginTime));
            model.addAttribute("now",DatetUtil.dayForWeek(DatetUtil.formatDate(CommonConstants.DATE.FORMAT_YYYY_MM_dd,new Date())));
            return "/sunday/schedule/data1New";
        }catch (Exception e){
            logger.error("/sunday/schedule/selectScheduleByWeekForPaiKe----e="+e.getMessage());
            e.printStackTrace();
            return "/sunday/error";
        }
    }

    @RequestMapping(value="/selectBunchWaitSchedule",method={RequestMethod.POST})
    public  String selectBunchWaitSchedule(HttpServletRequest request,Model model) {
        try{
            PageResult<SundayBunch> result=bunchService.selectBunchWaitSchedule(request, AdminSessionHolder.getGuardIds(),AdminSessionHolder.getBunchIds());
            packagePageParam(model,result.getTotal(),request);
            model.addAttribute("bunches",result.getData());
            packageSearchParam(request,search_param_temp);
            return "/sunday/schedule/data2";
        }catch (Exception e){
            logger.error("/sunday/bunch/web/selectBunchWaitSchedule----e="+e.getMessage());
            e.printStackTrace();
            return "/sunday/error";
        }
    }

    //课类(废弃)
    @RequestMapping("/category")
    public String category(HttpServletRequest request,Model model) {
        model.addAttribute("param",search_param_temp);
        boolean isClean = StringUtils.isNotEmpty(request.getParameter("isClean"))?false:true;
        logger.info("是否清除查询参数="+isClean);
        if(isClean){
            search_param_temp.clear();
        }
        return "/sunday/schedule/category/index";
    }

    /**
     * 新增或修改课类(废弃)
     * @param request
     * @param response
     */
    /*@RequestMapping("/saveCategory")
    public void saveCategory(HttpServletRequest request,
                     HttpServletResponse response,
                     @ModelAttribute SundayScheduleCategory category

    ) {
        try {
            category.setCreator(AdminSessionHolder.getAdminUserId());
            if(LongUtil.isNotZreo(category.getId())){
                categoryService.update(category.getId(),"category",category.getCategory());
                categoryService.update(category.getId(),"categoryIcon",category.getCategoryIcon());
            }else{
                //SundayBunch bunch =bunchService.findSimpleQinzi(category.getBunchId());
                //category.setLessonId(bunch.getBunchQinzi().getLessonId());
                categoryService.saveCategory(category,AdminSessionHolder.getAdminUserId());
            }
            ResponseMessage.success();
        } catch (Exception e) {

            e.printStackTrace();
            String msg ="";
            if(e.getClass().getSimpleName().equalsIgnoreCase("ErrorException")){
                msg=e.getMessage();
            }else{
                msg= CommonConstants.ERRORMSG;
                logger.error("/sunday/web/schedule/saveSch------"+e.getMessage());
            }
            e.printStackTrace();
            ResponseMessage.error(CommonConstants.FAIL_CODE,msg);
        }
    }*/

    /**
     * 课类新增课表(废弃)
     * @param request
     * @param response
     */
    @RequestMapping("/addToSchedule")
    public void addToSchedule(HttpServletRequest request,
                             HttpServletResponse response,
                              @RequestParam(value="categoryData",required=true)String categoryData

    ) {
        try {
            scheduleService.addToSchedule(categoryData);
            ResponseMessage.success();
        } catch (Exception e) {

            e.printStackTrace();
            String msg ="";
            if(e.getClass().getSimpleName().equalsIgnoreCase("ErrorException")){
                msg=e.getMessage();
            }else{
                msg= CommonConstants.ERRORMSG;
                logger.error("/sunday/web/schedule/addToSchedule------"+e.getMessage());
            }
            e.printStackTrace();
            ResponseMessage.error(CommonConstants.FAIL_CODE,msg);
        }
    }



    /**
     * 删除课类(废弃)
     * @param request
     * @param response
     */
    /*@RequestMapping("/deleteCategory")
    public void deleteCategory(HttpServletRequest request,
                             HttpServletResponse response,
                               @RequestParam(value="categoryIds",required=true)String categoryIds

    ) {
        try {

            categoryService.batchDeleteCategory(categoryIds);
            ResponseMessage.success();
        } catch (Exception e) {

            e.printStackTrace();
            String msg ="";
            if(e.getClass().getSimpleName().equalsIgnoreCase("ErrorException")){
                msg=e.getMessage();
            }else{
                msg= CommonConstants.ERRORMSG;
                logger.error("/sunday/web/schedule/saveSch------"+e.getMessage());
            }
            e.printStackTrace();
            ResponseMessage.error(CommonConstants.FAIL_CODE,msg);
        }
    }*/

    /**
     * 移出该课类(废弃)
     * @param request
     * @param response
     */
    @RequestMapping("/deleteScheduleByCategory")
    public void deleteScheduleByCategory(HttpServletRequest request,
                               HttpServletResponse response,
                               @RequestParam(value="scheduleId",required=true)Long scheduleId

    ) {
        try {

            scheduleService.update(scheduleId,"categoryId",null);
            ResponseMessage.success();
        } catch (Exception e) {

            e.printStackTrace();
            String msg ="";
            if(e.getClass().getSimpleName().equalsIgnoreCase("ErrorException")){
                msg=e.getMessage();
            }else{
                msg= CommonConstants.ERRORMSG;
                logger.error("/sunday/web/schedule/deleteScheduleByCategory------"+e.getMessage());
            }
            e.printStackTrace();
            ResponseMessage.error(CommonConstants.FAIL_CODE,msg);
        }
    }


    //分页查询课类
    /*@RequestMapping(value="/selectCategoryByPage",method={RequestMethod.POST})
    public  String selectCategoryByPage(HttpServletRequest request,
                                           HttpServletResponse response,
                                           Model model) {
        try{

            PageResult<SundayScheduleCategory> result=categoryService.selectCategoryByPage(request, AdminSessionHolder.getGuardIds(),AdminSessionHolder.getBunchIds());
            //model.addAttribute("result",result);
            //2018年10月18日，增加分页需要从参数
            packagePageParam(model,result.getTotal(),request);
            model.addAttribute("categories",result.getData());
            //2018年11月27日，捕获查询参数
            packageSearchParam(request,search_param_temp);
            return "/sunday/schedule/category/data";
        }catch (Exception e){
            logger.error("/sunday/bunch/web/selectCategoryByPage----e="+e.getMessage());
            e.printStackTrace();
            return "/sunday/error";
        }
    }*/

    //查询课类下的课表(废弃)
    @RequestMapping(value="/selectScheduleByCategory",method={RequestMethod.GET})
    public  String selectScheduleByCategory(HttpServletRequest request,
                                        HttpServletResponse response,
                                        @RequestParam(value="categoryId",required=true)Long categoryId,
                                        Model model) {
        try{

            List<SundayScheduleEntity> result=scheduleService.selectScheduleByCategory(categoryId);
            //model.addAttribute("result",result);
            //2018年10月18日，增加分页需要从参数
            model.addAttribute("schedules",result);
            //2018年11月27日，捕获查询参数
            packageSearchParam(request,search_param_temp);
            return "/sunday/schedule/category/scheduleData";
        }catch (Exception e){
            logger.error("/sunday/bunch/web/selectScheduleByCategory----e="+e.getMessage());
            e.printStackTrace();
            return "/sunday/error";
        }
    }



    //课表编辑页
    @RequestMapping("/scheduleIndex")
    public String scheduleIndex(HttpServletRequest request,Model model,
                                @RequestParam(value="bunchId",required=true)Long bunchId) {
        model.addAttribute("param",search_param_temp);
        boolean isClean = StringUtils.isNotEmpty(request.getParameter("isClean"))?false:true;
        logger.info("是否清除查询参数="+isClean);
        if(isClean){
            search_param_temp.clear();
        }
        SundayBunch bunch = bunchService.findSimpleQinzi(bunchId);
        model.addAttribute("bunch",bunch);
        model.addAttribute("status",1);
        return "/sunday/schedule/index/scheduleIndex";
    }




    //查询班级下的课表
    @RequestMapping(value="/selectScheduleByBunchId",method={RequestMethod.GET})
    public  String selectScheduleByBunchId(HttpServletRequest request,
                                            HttpServletResponse response,
                                            @RequestParam(value="bunchId",required=true)Long bunchId,
                                            @RequestParam(value="status",required=false)String status,
                                            Model model) {
        try{

            List<SundayScheduleEntity> result=scheduleService.selectScheduleByBunchId(bunchId,status);
            //model.addAttribute("result",result);
            //2018年10月18日，增加分页需要从参数
            model.addAttribute("schedules",result);
            model.addAttribute("status",status);
            //2018年11月27日，捕获查询参数
            packageSearchParam(request,search_param_temp);
            return "/sunday/schedule/index/scheduleData";
        }catch (Exception e){
            logger.error("/sunday/bunch/web/selectScheduleByBunchId----e="+e.getMessage());
            e.printStackTrace();
            return "/sunday/error";
        }
    }

    //课表导出
    @RequestMapping("/batchDaoChu")
    public void batchDaoChu(HttpServletRequest request,
                                 HttpServletResponse response,
                                 @RequestParam(value = "guardId", required = true) Long guardId,
                                 @RequestParam(value = "bunchId", required = false) Long bunchId) {
        try {



            String resultPath = scheduleService.batchDaoChu(guardId,bunchId);
            ResponseMessage.success(resultPath);

        } catch (Exception e) {
            String msg;
            if (CommonConstants.ERROR_EXCEPTION.equalsIgnoreCase(e.getClass().getSimpleName())) {
                msg = e.getMessage();
            } else {
                msg = CommonConstants.ERRORMSG;
                logger.error("/sunday/web/schedule/batchDaoChu------" + e.getMessage());
            }
            e.printStackTrace();

            ResponseMessage.error(CommonConstants.FAIL_CODE, msg);
        }
    }

    //课表导出
    @RequestMapping("/batchDaoChuByGuardIds")
    public void batchDaoChuByGuardIds(HttpServletRequest request,
                            HttpServletResponse response,
                            @RequestParam(value = "guardIds", required = true) String guardIds,
                            @RequestParam(value = "bunchId", required = false) Long bunchId) {
        try {



            String resultPath = scheduleService.batchDaoChuByGuardIds(guardIds);
            ResponseMessage.success(resultPath);

        } catch (Exception e) {
            String msg;
            if (CommonConstants.ERROR_EXCEPTION.equalsIgnoreCase(e.getClass().getSimpleName())) {
                msg = e.getMessage();
            } else {
                msg = CommonConstants.ERRORMSG;
                logger.error("/sunday/web/schedule/batchDaoChuByGuardIds------" + e.getMessage());
            }
            e.printStackTrace();

            ResponseMessage.error(CommonConstants.FAIL_CODE, msg);
        }
    }
}
