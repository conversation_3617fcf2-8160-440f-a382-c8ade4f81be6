package org.itboys.mobile.controller.mobile;

import com.mangofactory.swagger.annotations.ApiIgnore;
import com.wordnik.swagger.annotations.Api;
import com.wordnik.swagger.annotations.ApiOperation;
import com.wordnik.swagger.annotations.ApiParam;
import org.itboys.commons.CommonConstants;
import org.itboys.framework.spring.controller.BaseController;
import org.itboys.mobile.entity.mongo.attendance.SundayLessonMaterialDto;
import org.itboys.mobile.service.mongo.web.SundayLessonMaterialService;
import org.itboys.mobile.util.MobileSignUtils;
import org.itboys.mobile.util.ResponseMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping("/sunday/mobile/lession/material")
@Api(value = "course", description = "亲子素材接口")
public class SundayMobileLessonMaterialController extends BaseController {
    @Autowired
    private SundayLessonMaterialService materialService;

    @RequestMapping(value = "/getmaterialByList", method = {RequestMethod.GET})
    @ApiOperation(value = "获取亲子材料列表")
    @ApiIgnore
    public void getLevelByList(HttpServletRequest request,
                               @ApiParam(value = "素材名") @RequestParam(value = "materialName",required = false)String materialName,
                               @ApiParam(value = "随机数。[参与签名]") @RequestParam(value = "nonce") String nonce,
                               @ApiParam(value = "签名") @RequestParam(value = "sign") String sign) {
        try {
            Map<String, Object> param = new HashMap();
            param.put("nonce", nonce);
            param.put("sign", sign);
            boolean signSer = MobileSignUtils.signCheck(param, sign);
            if (!signSer) {
                logger.warn("签名校验失败");
            }
            List<SundayLessonMaterialDto> result = materialService.getMaterialByList(request,materialName);
            ResponseMessage.success(result);
        } catch (Exception e) {
            logger.error("/sunday/mobile/lession/material/getmaterialByList----e=" + e.getMessage());
            e.printStackTrace();
            ResponseMessage.error(CommonConstants.FAIL_CODE,CommonConstants.ERRORMSG);
        }
    }
}