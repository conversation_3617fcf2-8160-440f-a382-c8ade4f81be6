package org.itboys.mobile.controller.web;

import org.apache.commons.lang3.StringUtils;
import org.itboys.admin.entity.lasted.SystemRole;
import org.itboys.admin.tools.AdminSessionHolder;
import org.itboys.commons.CommonConstants;
import org.itboys.framework.query.JsonPageUtils;
import org.itboys.framework.spring.controller.BaseController;
import org.itboys.mobile.entity.mongo.SundayCamera;
import org.itboys.mobile.entity.mongo.SundayDaily;
import org.itboys.mobile.service.mongo.web.SundayCameraService;
import org.itboys.mobile.util.AccountIdHoldUtils;
import org.itboys.mobile.util.ResponseMessage;
import org.itboys.param.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

@Controller
@RequestMapping("/sunday/web/camera")
public class SundayCameraController extends BaseController {
    public static Map<String, Object> search_param_temp;

    static {
        search_param_temp = new HashMap();
    }

    @Autowired
    private SundayCameraService cameraService;

    @RequestMapping("/index")
    public String index(HttpServletRequest request, @RequestParam(value = "guardId") Long guardId, Model model) {
        model.addAttribute("param", search_param_temp);
        boolean isClean = StringUtils.isNotEmpty(request.getParameter("isClean")) ? false : true;
        logger.info("是否清除查询参数=" + isClean);
        if (isClean) {
            search_param_temp.clear();
        }
        model.addAttribute("guardId", guardId);
        return "/sunday/camera/index";
    }

    @RequestMapping(value = "/select", method = {RequestMethod.POST})
    public String select(HttpServletRequest request,
                         HttpServletResponse response,

                         Model model) {
        try {
            PageResult<SundayCamera> result = cameraService.selectCamera(request, AdminSessionHolder.getGuardIds(), AdminSessionHolder.getBunchIds());
            //model.addAttribute("result",result);
            //2018年10月18日，增加分页需要从参数
            packagePageParam(model, result.getTotal(), request);
            model.addAttribute("cameras", result.getData());
            //2018年11月27日，捕获查询参数
            packageSearchParam(request, search_param_temp);
            return "/sunday/camera/data";
        } catch (Exception e) {
            logger.error("/sunday/camera/web/select----e=" + e.getMessage());
            e.printStackTrace();
            return "/sunday/error";
        }
    }

    /**
     * 商品详情页
     *
     * @param request
     * @param id
     * @param model
     * @return
     */
    @RequestMapping("/input")
    public String input(
            HttpServletRequest request,
            @RequestParam(value = "id", required = true) Long id,
            @RequestParam(value = "guardId", required = true) Long guardId,
            Model model) {
        SundayCamera camera = cameraService.findOne(id, guardId);
        model.addAttribute("camera", camera);
        return "/sunday/camera/input";
    }


    @RequestMapping("/save")
    public void save(HttpServletRequest request,
                     HttpServletResponse response,
                     @ModelAttribute SundayCamera camera

    ) {
        try {
            if (camera != null && camera.getNumber() != null) {
                camera.setNumber(camera.getNumber().trim());
            }
            cameraService.saveCamera(camera);
            ResponseMessage.success();
        } catch (Exception e) {
            String msg = "";
            if (CommonConstants.ERROR_EXCEPTION.equalsIgnoreCase(e.getClass().getSimpleName())) {
                msg = e.getMessage();
            } else {
                msg = CommonConstants.ERRORMSG;
                logger.error("/sunday/web/camera/save------" + e.getMessage());
            }
            e.printStackTrace();
            if (camera.getNumber() == null) {
                ResponseMessage.error(-100, "请配置摄像头");
            } else {
                ResponseMessage.error(CommonConstants.FAIL_CODE, msg);
            }

        }
    }


    @RequestMapping("/delete")
    public void delete(HttpServletRequest request,
                       HttpServletResponse response,
                       @RequestParam(value = "id", required = true) Long id

    ) {
        try {
            cameraService.delete(id);
            ResponseMessage.success();
        } catch (Exception e) {
            String msg = "";
            if (CommonConstants.ERROR_EXCEPTION.equalsIgnoreCase(e.getClass().getSimpleName())) {
                msg = e.getMessage();
            } else {
                msg = CommonConstants.ERRORMSG;
                logger.error("/sunday/web/camera/delete------" + e.getMessage());
            }
            e.printStackTrace();

            ResponseMessage.error(CommonConstants.FAIL_CODE, msg);
        }
    }


}
