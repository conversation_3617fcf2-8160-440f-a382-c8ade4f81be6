package org.itboys.mobile.controller.mobile;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.itboys.framework.spring.controller.BaseController;
import org.itboys.mobile.dao.SchoolStudentBodyDefecationMapper;
import org.itboys.mobile.dao.SchoolStudentBodyDrinkMapper;
import org.itboys.mobile.dao.SchoolStudentBodyTemperatureMapper;
import org.itboys.mobile.entity.mongo.SundayGuard;
import org.itboys.mobile.entity.mongo.SundayStudent;
import org.itboys.mobile.entity.mysql.body.SchoolStudentBodyDefecation;
import org.itboys.mobile.entity.mysql.body.SchoolStudentBodyDrink;
import org.itboys.mobile.entity.mysql.body.SchoolStudentBodyTemperature;
import org.itboys.mobile.service.mongo.web.SundayGuardService;
import org.itboys.mobile.service.mongo.web.SundayStudentService;
import org.itboys.mobile.service.mysql.SchoolStudentBodyEmotionService;
import org.itboys.mobile.service.mysql.SchoolStudentBodyFoodService;
import org.itboys.mobile.service.mysql.SchoolStudentBodySiestaService;
import org.itboys.mobile.service.mysql.SchoolStudentBodyStudyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import com.mangofactory.swagger.annotations.ApiIgnore;
import com.wordnik.swagger.annotations.Api;
import com.wordnik.swagger.annotations.ApiOperation;

/**
 * 
 * 日报
 * <AUTHOR>
 *
 */
@Controller
@RequestMapping("/sunday/mobile/h5/rb")
@Api(value = "report", description = "日报")
public class SundayMobileReportRbController extends BaseController {

    @Autowired
    private SchoolStudentBodyDefecationMapper defecationMapper;
	@Autowired
	private SchoolStudentBodyDrinkMapper schoolStudentBodyDrinkMapper;
	@Autowired
	private SchoolStudentBodyFoodService schoolStudentBodyFoodService;
	@Autowired
    private SchoolStudentBodyTemperatureMapper temperatureMapper;
	@Autowired
	private SchoolStudentBodySiestaService schoolStudentBodySiestaService;
	@Autowired
	private SchoolStudentBodyEmotionService schoolStudentBodyEmotionService;
	@Autowired
	private SchoolStudentBodyStudyService schoolStudentBodyStudyService;
	@Autowired
	private SundayStudentService sundayStudentService;
	@Autowired
	private SundayGuardService sundayGuardService;
	
	@RequestMapping(value = "/list", method = { RequestMethod.GET })
	@ApiOperation(value = "日报列表")
	@ApiIgnore
	public String list(@RequestParam(value = "studentId", required = true) Long studentId, 
			HttpServletRequest request, HttpServletResponse response, Model model) {
		try {
			
			//列表暂时只展示3天的数据
			List<String> dateList = new ArrayList<>();
			dateList.add(LocalDate.now().minusDays(0).toString());
			dateList.add(LocalDate.now().minusDays(1).toString());
			dateList.add(LocalDate.now().minusDays(2).toString());

			model.addAttribute("studentId", studentId);
			model.addAttribute("dateList", dateList);
			
			return "/yuhua/mobile/h5/report/rb/list";
		} catch (Exception e) {
			logger.error("/sunday/mobile/h5/rb/list----e=" + e.getMessage());
			e.printStackTrace();
			return "/yuhua/mobile/error";
		}
	}
	
	@RequestMapping(value = "/detail", method = { RequestMethod.GET })
	@ApiOperation(value = "日报详情")
	@ApiIgnore
	public String detail(@RequestParam(value = "studentId", required = true) Long studentId, 
			@RequestParam(value = "recordDate", required = true) String recordDate, 
			HttpServletRequest request, HttpServletResponse response, Model model) {
		try {
			int isQmy = -1; //是否奇妙园 1=是；0=否
			SundayStudent sundayStudent = sundayStudentService.findOne(studentId);
			if(sundayStudent != null) {
				SundayGuard guard = sundayGuardService.getById(sundayStudent.getGuardId());
				if(guard != null) {
					if(guard.getName().contains("奇妙园")) {
						isQmy = 1;
					}else {
						isQmy = 0;//托育园
					}
				}
			}
			model.addAttribute("isQmy", isQmy);
			int isAbsence = 0;//0=缺勤;1=在校
			//排泄情况（便便）
			Map<String, Object> defeMap = new HashMap<>();
			defeMap.put("recordTime", recordDate);
			defeMap.put("studentId", studentId);
			defeMap.put("checkType", 1);
			defeMap.put("deleted", 0);
			SchoolStudentBodyDefecation defecation = defecationMapper.getBody(defeMap);
			//喝水
			Map<String,Object> param = new HashMap<>();
			param.put("studentId", studentId);
			param.put("recordTime", recordDate);
			SchoolStudentBodyDrink schoolStudentBodyDrink = schoolStudentBodyDrinkMapper.getBody(param);
			//用餐
			Map<String, Object> foodMap = schoolStudentBodyFoodService.getBodyFoodData(recordDate, studentId);
			//午睡情况
			Map<String, Object> siestaMap = schoolStudentBodySiestaService.getBodySiestaData(recordDate, studentId);
			//体温和嘱托
			Map<String, Object> temperatureParam = new HashMap<>();
			temperatureParam.put("recordTime", recordDate);
			temperatureParam.put("studentId", studentId);
			temperatureParam.put("deleted", 0);
			temperatureParam.put("checkType", 1);//检查类型 1：晨检、2：午检 3:晚检
			SchoolStudentBodyTemperature temperature1 = temperatureMapper.getBody(temperatureParam);
			temperatureParam.put("checkType", 2);//检查类型 1：晨检、2：午检 3:晚检
			SchoolStudentBodyTemperature temperature2 = temperatureMapper.getBody(temperatureParam);
			temperatureParam.put("checkType", 3);//检查类型 1：晨检、2：午检 3:晚检
			SchoolStudentBodyTemperature temperature3 = temperatureMapper.getBody(temperatureParam);
			//情绪：1=平静放松；2=焦虑不安；3=悲伤难过；4=愤怒暴躁；5=传染病预警[待处理]
			int emotion = 0;
			String emotionRemarks = null;
			Map<String, Object> bodyEmotion = schoolStudentBodyEmotionService.getBodyEmotionData(recordDate, studentId);
			if(bodyEmotion != null && !bodyEmotion.isEmpty()) {
				emotion =  (int) bodyEmotion.get("value");
				if(bodyEmotion.get("remarks") != null) {
					emotionRemarks = String.valueOf(bodyEmotion.get("remarks")).replaceAll("\n", " ");
				}
				isAbsence = 1; //在校
			}
			model.addAttribute("emotion", emotion);
			if(StringUtils.isNotBlank(emotionRemarks)) {
				model.addAttribute("emotionRemarks", emotionRemarks.replaceAll("\n", " "));
			}
			//晨检：1=正常；2=异常
			int morning = 0;
			String morningRemarks = null;
			//午检：1=正常；2=异常
			int noon = 0;
			String noonRemarks = null;
			//晚检：1=正常；2=异常	
			int late = 0;
			String lateRemarks = null;
			//检查嘱托：0=无；1=需要观察；2=委托吃药；3=传染病预警
			int entrust = -1;
			String entrustRemarks = null;
			if(temperature1 != null) {
				if(temperature1.getCheckType().compareTo(1) == 0) {//检查类型 1：晨检、2：午检 3:晚检
					if(temperature1.getSituation() != null) {//体温情况 1-正常 2-异常
						if(temperature1.getSituation().compareTo(1) == 0) {
							morning = 1;
						}else {
							morning = 2;
						}
						morningRemarks = temperature1.getRemarks();
					}
					Integer state = temperature1.getState();//1：正常 2：需要观察 3：委托吃药 4：传染病预警
					if(state != null) {
						if(state.compareTo(2) == 0) {
							entrust = 1;
						} else if(state.compareTo(3) == 0) {
							entrust = 2;
						} else if(state.compareTo(4) == 0) {
							entrust = 3;
						} else {
							entrust = 0;
						}
						entrustRemarks = temperature1.getRemarks();
					}
				}
				isAbsence = 1; //在校
			}  
			if(temperature2 != null) {
				if(temperature2.getCheckType().compareTo(2) == 0) {//检查类型 1：晨检、2：午检 3:晚检
					if(temperature2.getSituation() != null) {//体温情况 1-正常 2-异常
						if(temperature2.getSituation().compareTo(1) == 0) {
							noon = 1;
						}else {
							noon = 2;
						}
						noonRemarks = temperature2.getRemarks();
					}
					Integer state = temperature2.getState();//1：正常 2：需要观察 3：委托吃药 4：传染病预警
					if(state != null) {
						if(state.compareTo(2) == 0) {
							entrust = 1;
						} else if(state.compareTo(3) == 0) {
							entrust = 2;
						} else if(state.compareTo(4) == 0) {
							entrust = 3;
						} else {
							entrust = 0;
						}
						entrustRemarks = temperature2.getRemarks();
					}
				}
				isAbsence = 1; //在校
			}
			if(temperature3 != null) {
				if(temperature3.getCheckType().compareTo(3) == 0) {//检查类型 1：晨检、2：午检 3:晚检
					if(temperature3.getSituation() != null) {//体温情况 1-正常 2-异常
						if(temperature3.getSituation().compareTo(1) == 0) {
							late = 1;
						}else {
							late = 2;
						}
						lateRemarks = temperature3.getRemarks();
					}
					Integer state = temperature3.getState();//1：正常 2：需要观察 3：委托吃药 4：传染病预警
					if(state != null) {
						if(state.compareTo(2) == 0) {
							entrust = 1;
						} else if(state.compareTo(3) == 0) {
							entrust = 2;
						} else if(state.compareTo(4) == 0) {
							entrust = 3;
						} else {
							entrust = 0;
						}
						entrustRemarks = temperature3.getRemarks();
					}
				}
				isAbsence = 1; //在校
			}
			model.addAttribute("morning", morning);
			if(StringUtils.isNotBlank(morningRemarks)) {
				model.addAttribute("morningRemarks", morningRemarks.replaceAll("\n", " "));
			}
			model.addAttribute("noon", noon);
			if(StringUtils.isNotBlank(noonRemarks)) {
				model.addAttribute("noonRemarks", noonRemarks.replaceAll("\n", " "));
			}
			model.addAttribute("late", late);
			if(StringUtils.isNotBlank(lateRemarks)) {
				model.addAttribute("lateRemarks", lateRemarks.replaceAll("\n", " "));
			}
			model.addAttribute("entrust", entrust);
			if(StringUtils.isNotBlank(entrustRemarks)) {
				model.addAttribute("entrustRemarks", entrustRemarks.replaceAll("\n", " "));
			}
			
			//早点：1=适量；2=多；3=少；0=无
			int breakfast = -1;
			String breakfastRemarks = null;
			//午餐-主食：1=适量；2=多；3=少；0=无
			int staple = -1;
			String stapleRemarks = null;
			//午餐-菜：1=适量；2=多；3=少；0=无
			int dish = -1;
			String dishRemarks = null;
			//午点：1=适量；2=多；3=少；0=无
			int snack = -1;
			String snackRemarks = null;
			if(foodMap != null) {
				if(foodMap.get("breakfast") != null) { 
					int breakFast = (int) foodMap.get("breakfast");//早点1:少量2：适量3：多 默认适量
					if(breakFast == 1) breakfast = 3;
					else if(breakFast == 2) breakfast = 1;
					else if(breakFast == 3) breakfast = 2;
					else breakfast = 0;
					if(foodMap.get("remarks") != null) {
						breakfastRemarks = String.valueOf(foodMap.get("remarks")).replaceAll("\n", " ");
					}
					isAbsence = 1; //在校
				}
				if(foodMap.get("stapleFood") != null) { 
					int stapleFood = (int) foodMap.get("stapleFood");//主食1:少量2：适量3：多 默认适量
					if(stapleFood == 1) staple = 3;
					else if(stapleFood == 2) staple = 1;
					else if(stapleFood == 3) staple = 2;
					else staple = 0;
					if(foodMap.get("remarks") != null) {
						stapleRemarks = String.valueOf(foodMap.get("remarks")).replaceAll("\n", " ");
					}
					isAbsence = 1; //在校
				}
				if(foodMap.get("food") != null) { 
					int food = (int) foodMap.get("food");//菜1:少量2：适量3：多 默认适量
					if(food == 1) dish = 3;
					else if(food == 2) dish = 1;
					else if(food == 3) dish = 2;
					else dish = 0;
					if(foodMap.get("remarks") != null) {
						dishRemarks = String.valueOf(foodMap.get("remarks")).replaceAll("\n", " ");
					}
					isAbsence = 1; //在校
				}
				if(foodMap.get("snack") != null) { 
					int snack2 = (int) foodMap.get("snack");//午点1:少量2：适量3：多 默认适量
					if(snack2 == 1) snack = 3;
					else if(snack2 == 2) snack = 1;
					else if(snack2 == 3) snack = 2;
					else snack = 0;
					if(foodMap.get("remarks") != null) {
						snackRemarks = String.valueOf(foodMap.get("remarks")).replaceAll("\n", " ");
					}
					isAbsence = 1; //在校
				}
			}
			model.addAttribute("breakfast", breakfast);
			if(StringUtils.isNotBlank(breakfastRemarks)) {
				model.addAttribute("breakfastRemarks", breakfastRemarks.replaceAll("\n", " "));
			}
			model.addAttribute("staple", staple);
			if(StringUtils.isNotBlank(stapleRemarks)) {
				model.addAttribute("stapleRemarks", stapleRemarks.replaceAll("\n", " "));
			}
			model.addAttribute("dish", dish);
			if(StringUtils.isNotBlank(dishRemarks)) {
				model.addAttribute("dishRemarks", dishRemarks.replaceAll("\n", " "));
			}
			model.addAttribute("snack", snack);
			if(StringUtils.isNotBlank(snackRemarks)) {
				model.addAttribute("snackRemarks", snackRemarks.replaceAll("\n", " "));
			}
			//午睡：1=2小时左右；2=1-2小时左右；3=低于1小时；0=无
			int siesta = -1;
			String siestaRemarks = null;
			if(siestaMap != null) {
				if(siestaMap.get("value") != null) {
					int value = (int) siestaMap.get("value");//1：一小时左右 2：一个半小时左右 3：两个小时左右
					if(value == 1) siesta = 3;
					else if(value == 2) siesta = 2;
					else if(value == 3) siesta = 1;
					else siesta = 0;
					if(siestaMap.get("remarks") != null) {
						siestaRemarks = String.valueOf(siestaMap.get("remarks")).replaceAll("\n", " ");
					}
					isAbsence = 1; //在校
				}
			}
			model.addAttribute("siesta", siesta);
			if(StringUtils.isNotBlank(siestaRemarks)) {
				model.addAttribute("siestaRemarks", siestaRemarks.replaceAll("\n", " "));
			}
			//喝水：1=正常；2=较多；3=较少；0=无
			int drink = -1;//默认：无
			String drinkRemarks = null;
			if(schoolStudentBodyDrink != null) {
				int value = schoolStudentBodyDrink.getValue();//1是正常，2是少量 3是较多
				if(value == 1) drink = 1;
				else if(value == 2) drink = 3;
				else if(value == 3) drink = 2;
				else drink = 0;
				drinkRemarks = schoolStudentBodyDrink.getRemarks();
				isAbsence = 1; //在校
			}
			model.addAttribute("drink", drink);
			if(StringUtils.isNotBlank(drinkRemarks)) {
				model.addAttribute("drinkRemarks", drinkRemarks.replaceAll("\n", " "));
			}
			//便便：1=有（正常）；2=有（干）；3=有（稀）；0=无
			int shit = -1;//默认：无
			String shitRemarks = null;
			if(defecation != null ) {
				if(defecation.getShitShape() != null) {
					int shitShape = defecation.getShitShape();//1：干硬 2：软 3：稀
					if(shitShape == 1) shit = 2;
					else if(shitShape == 2) shit = 1;
					else if(shitShape == 3) shit = 3;
					else shit = 0;
					shitRemarks = defecation.getRemarks();
					isAbsence = 1; //在校
				}
			}
			model.addAttribute("shit", shit);
			if(StringUtils.isNotBlank(shitRemarks)) {
				model.addAttribute("shitRemarks", shitRemarks.replaceAll("\n", " "));
			}
			//蒙氏课程：1=积极；2=被动；3=未参加[待处理]
			int ms = 0;
			String msRemarks = null;
			//学习力课程：1=积极；2=被动；3=未参加[待处理]
			int xxl = 0;
			String xxlRemarks = null;
			//语言课程：1=积极；2=被动；3=未参加[待处理]
			int yy = 0;
			String yyRemarks = null;
			//SPARK专项：1=积极；2=被动；3=未参加[待处理]
			int spark = 0;
			String sparkRemarks = null;
			//SPARK拓展：1=积极；2=被动；3=未参加[待处理]
			int spark2 = 0;
			String spark2Remarks = null;
			//SEL情感：1=积极；2=被动；3=未参加[待处理]
			int sel = 0;
			String selRemarks = null;
			Map<String, Object> studyData = schoolStudentBodyStudyService.getBodyStudyData(recordDate, studentId);
			if(studyData != null && !studyData.isEmpty()) {
				ms = (int) studyData.get("msValue");
				xxl = (int) studyData.get("xxlValue");
				yy = (int) studyData.get("yyValue");
				spark = (int) studyData.get("sparkValue");
				spark2 = (int) studyData.get("spark2Value");
				sel = (int) studyData.get("selValue");

				if(studyData.get("remarks") != null) {
					msRemarks = String.valueOf(studyData.get("remarks")).replaceAll("\n", " ");
					xxlRemarks = String.valueOf(studyData.get("remarks")).replaceAll("\n", " ");
					yyRemarks = String.valueOf(studyData.get("remarks")).replaceAll("\n", " ");
					sparkRemarks = String.valueOf(studyData.get("remarks")).replaceAll("\n", " ");
					spark2Remarks = String.valueOf(studyData.get("remarks")).replaceAll("\n", " ");
					selRemarks = String.valueOf(studyData.get("remarks")).replaceAll("\n", " ");
				}
				isAbsence = 1; //在校
			}
			model.addAttribute("ms", ms);
			if(StringUtils.isNotBlank(msRemarks)) {
				model.addAttribute("msRemarks", msRemarks.replaceAll("\n", " "));
			}
			model.addAttribute("xxl", xxl);
			if(StringUtils.isNotBlank(msRemarks)) {
				model.addAttribute("xxlRemarks", xxlRemarks.replaceAll("\n", " "));
			}
			model.addAttribute("yy", yy);
			if(StringUtils.isNotBlank(yyRemarks)) {
				model.addAttribute("yyRemarks", yyRemarks.replaceAll("\n", " "));
			}
			model.addAttribute("spark", spark);
			if(StringUtils.isNotBlank(sparkRemarks)) {
				model.addAttribute("sparkRemarks", sparkRemarks.replaceAll("\n", " "));
			}
			model.addAttribute("spark2", spark2);
			if(StringUtils.isNotBlank(spark2Remarks)) {
				model.addAttribute("spark2Remarks", spark2Remarks.replaceAll("\n", " "));
			}
			model.addAttribute("sel", sel);
			if(StringUtils.isNotBlank(selRemarks)) {
				model.addAttribute("selRemarks", selRemarks.replaceAll("\n", " "));
			}
			model.addAttribute("isAbsence", isAbsence);//判断是否缺勤
			
			return "/yuhua/mobile/h5/report/rb/detail";
		} catch (Exception e) {
			logger.error("/sunday/mobile/h5/rb/detail----e=" + e.getMessage());
			e.printStackTrace();
			return "/yuhua/mobile/error";
		}
	}
    
}
