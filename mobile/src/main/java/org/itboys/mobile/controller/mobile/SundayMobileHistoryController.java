package org.itboys.mobile.controller.mobile;

import com.wordnik.swagger.annotations.Api;
import com.wordnik.swagger.annotations.ApiOperation;
import com.wordnik.swagger.annotations.ApiParam;
import org.itboys.commons.CommonConstants;
import org.itboys.commons.ErrorException;
import org.itboys.commons.utils.tools.ListUtil;
import org.itboys.framework.spring.controller.BaseController;
import org.itboys.mobile.dto.history.SundayHistoryDto;
import org.itboys.mobile.entity.mongo.history.SundayHistory;
import org.itboys.mobile.service.mongo.SundayHistoryTipService;
import org.itboys.mobile.service.mongo.front.SundayFrontDynamicService;
import org.itboys.mobile.service.mongo.front.SundayFrontHistoryService;
import org.itboys.mobile.util.AccountIdHoldUtils;
import org.itboys.mobile.util.MobileSignUtils;
import org.itboys.mobile.util.ResponseMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 作者：jiangxiong
 * 日期：2017年2月8日
 * 联系方式 ：<EMAIL>
 * 描述：百睿_成长记录_controller。
 */
@Controller
@RequestMapping("/sunday/mobile/history")
@Api(value = "history", description = "成长记录")
public class SundayMobileHistoryController extends BaseController {
    @Autowired
    private SundayFrontHistoryService historyService;
    @Autowired
    private SundayHistoryTipService tipService;

    @RequestMapping(value = "/getHistory", method = {RequestMethod.POST})
    @ApiOperation(value = "【家长端和教师端】获取成长记录数据。分页")
    public void getHistory(HttpServletRequest request,
                           HttpServletResponse response,
                           @ApiParam(required = false, name = "historyIds", value = "成长记录ID集合。通过成长记录消息提示进入传入的参数。消息提示已经返回此参数") @RequestParam(value = "historyIds", required = false) List<Long> historyIds,
                           @ApiParam(required = false, name = "childId", value = "学生宝贝ID。教师端传入，家长端不传入") @RequestParam(value = "childId", required = false) Long childId,
                           @ApiParam(required = true, name = "pageSize", value = "分页参数。每页数量。默认20") @RequestParam(value = "pageSize", required = true) String pageSize,
                           @ApiParam(required = true, name = "pageNumber", value = "分页参数。第几页。默认1") @RequestParam(value = "pageNumber", required = true) String pageNumber,
                           @ApiParam(required = false, name = "sort", value = "排序参数。字段名称。默认为空") @RequestParam(value = "sort", required = false) String sort,
                           @ApiParam(required = false, name = "order", value = "排序参数。默认为空。ASC正序/DESC倒序") @RequestParam(value = "order", required = false) String order,
                           @ApiParam(value = "随机数。【参与签名】") @RequestParam(value = "nonce", required = true) String nonce,
                           @ApiParam(value = "签名") @RequestParam(value = "sign", required = true) String sign
    ) {
        try {
            Map<String, Object> param = new HashMap<String, Object>();
            param.put("nonce", nonce);
            param.put("sign", sign);
            boolean signSer = MobileSignUtils.signCheck(param, sign);
            if (!signSer) {
                logger.warn("签名校验失败");
            }
            Long memberId = AccountIdHoldUtils.getMemberId();
            Long bunchId = AccountIdHoldUtils.getBunchId();
            Long guardId = AccountIdHoldUtils.getGuardId();
            List<SundayHistoryDto> result = historyService.getHistory(request, memberId, childId, bunchId, guardId, historyIds);
            ResponseMessage.success(result);
        } catch (Exception e) {
            String msg = CommonConstants.ERRORMSG;
            Integer msgCode = CommonConstants.FAIL_CODE;
            if (e.getClass().getSimpleName().equalsIgnoreCase("ErrorException")) {
                msg = e.getMessage();
            } else if (e.getClass().getSimpleName().equalsIgnoreCase("TokenException")) {
                msgCode = CommonConstants.TOKEN_CODE;
                msg = CommonConstants.TOKENMSG;
            } else {
                logger.error("/sunday/mobile/history/getHistory----e=" + e.getMessage());
            }
            e.printStackTrace();
            ResponseMessage.error(msgCode, msg);
        }
    }

    @RequestMapping(value = "/operate", method = {RequestMethod.POST})
    @ApiOperation(value = "【家长端和教师端】1，点赞或取消点赞操作，2评论操作")
    public void operate(HttpServletRequest request,
                        HttpServletResponse response,
                        @ApiParam(required = true, name = "type", value = "类型，1点赞或取消点赞操作，2评论操作") @RequestParam(value = "type", required = true) Integer type,
                        @ApiParam(required = true, name = "historyId", value = "成长记录ID") @RequestParam(value = "historyId", required = true) Long historyId,
                        @ApiParam(required = false, name = "oldMemberId", value = "被回复人ID。评论操作有效") @RequestParam(value = "oldMemberId", required = false) Long oldMemberId,
                        @ApiParam(required = false, name = "oldMemberType", value = "被回复人类型。评论操作有效") @RequestParam(value = "oldMemberType", required = false) Integer oldMemberType,

                        @ApiParam(required = false, name = "content", value = "评论内容") @RequestParam(value = "content", required = false) String content,
                        @ApiParam(value = "随机数。【参与签名】") @RequestParam(value = "nonce", required = true) String nonce,
                        @ApiParam(value = "签名") @RequestParam(value = "sign", required = true) String sign
    ) {
        try {
            Map<String, Object> param = new HashMap<String, Object>();
            param.put("nonce", nonce);
            param.put("sign", sign);

            boolean signSer = MobileSignUtils.signCheck(param, sign);
            if (!signSer) {
                logger.warn("签名校验失败");
            }
            Long memberId = AccountIdHoldUtils.getMemberId();
            Long bunchId = AccountIdHoldUtils.getBunchId();
            historyService.operateHistory(historyId, memberId, bunchId, type, content, oldMemberId, oldMemberType);
            ResponseMessage.success();
        } catch (Exception e) {
            String msg = CommonConstants.ERRORMSG;
            Integer msgCode = CommonConstants.FAIL_CODE;
            if (e.getClass().getSimpleName().equalsIgnoreCase("ErrorException")) {
                msg = e.getMessage();
            } else if (e.getClass().getSimpleName().equalsIgnoreCase("TokenException")) {
                msgCode = CommonConstants.TOKEN_CODE;
                msg = CommonConstants.TOKENMSG;
            } else {
                logger.error("/sunday/mobile/history/operate----e=" + e.getMessage());
            }
            e.printStackTrace();
            ResponseMessage.error(msgCode, msg);
        }
    }

    @RequestMapping(value = "/save", method = {RequestMethod.POST})
    @ApiOperation(value = "【家长端和教师端】发布成长记录")
    public void save(HttpServletRequest request,
                     HttpServletResponse response,
                     @ApiParam(required = false, name = "childId", value = "学生宝贝ID。教师端发布时传入，家长端不传入") @RequestParam(value = "childId", required = false) Long childId,

                     @ApiParam(required = false, name = "content", value = "内容") @RequestParam(value = "content", required = false) String content,
                     @ApiParam(required = false, name = "images", value = "图片半链接集合（最大9张)。例如：images=1,2,3") @RequestParam(value = "images", required = false) List<String> images,
                     @ApiParam(required = false, name = "tags", value = "标签名称集合（最大3条),标签从字典接口中获取。例如：images=1,2,3") @RequestParam(value = "tags", required = false) List<String> tags,
                     @ApiParam(required = false, name = "sourceType", value = "类型:0图片，1视频") @RequestParam(value = "sourceType", required = false) Integer sourceType,
                     @ApiParam(required = false, name = "sourceImage", value = "视频封面") @RequestParam(value = "sourceImage", required = false) String sourceImage,

                     @ApiParam(value = "随机数。【参与签名】") @RequestParam(value = "nonce", required = true) String nonce,
                     @ApiParam(value = "签名") @RequestParam(value = "sign", required = true) String sign
    ) {
        try {
            Map<String, Object> param = new HashMap<String, Object>();
            param.put("nonce", nonce);
            param.put("sign", sign);

            boolean signSer = MobileSignUtils.signCheck(param, sign);
            if (!signSer) {
                logger.warn("签名校验失败");
            }
            Long memberId = AccountIdHoldUtils.getMemberId();
            Long bunchId = AccountIdHoldUtils.getBunchId();
            historyService.saveHistory(memberId, childId, bunchId, content, images, tags, sourceType, sourceImage);
            ResponseMessage.success();
        } catch (Exception e) {
            String msg = CommonConstants.ERRORMSG;
            Integer msgCode = CommonConstants.FAIL_CODE;
            if (e.getClass().getSimpleName().equalsIgnoreCase("ErrorException")) {
                msg = e.getMessage();
            } else if (e.getClass().getSimpleName().equalsIgnoreCase("TokenException")) {
                msgCode = CommonConstants.TOKEN_CODE;
                msg = CommonConstants.TOKENMSG;
            } else {
                logger.error("/sunday/mobile/history/save----e=" + e.getMessage());
            }
            e.printStackTrace();
            ResponseMessage.error(msgCode, msg);
        }
    }

    @RequestMapping(value = "/delete", method = {RequestMethod.POST})
    @ApiOperation(value = "【家长端和教师端】删除成长记录")
    public void delete(HttpServletRequest request,
                       HttpServletResponse response,
                       @ApiParam(value = "成长记录ID") @RequestParam(value = "historyId", required = true) Long historyId,
                       @ApiParam(value = "随机数。【参与签名】") @RequestParam(value = "nonce", required = true) String nonce,
                       @ApiParam(value = "签名") @RequestParam(value = "sign", required = true) String sign
    ) {
        try {
            Map<String, Object> param = new HashMap<String, Object>();
            param.put("nonce", nonce);
            param.put("sign", sign);
            boolean signSer = MobileSignUtils.signCheck(param, sign);
            if (!signSer) {
                logger.warn("签名校验失败");
            }
            historyService.delete(historyId);
            ResponseMessage.success();
        } catch (Exception e) {
            String msg = CommonConstants.ERRORMSG;
            Integer msgCode = CommonConstants.FAIL_CODE;
            if (e.getClass().getSimpleName().equalsIgnoreCase("ErrorException")) {
                msg = e.getMessage();
            } else if (e.getClass().getSimpleName().equalsIgnoreCase("TokenException")) {
                msgCode = CommonConstants.TOKEN_CODE;
                msg = CommonConstants.TOKENMSG;
            } else {
                logger.error("/sunday/mobile/history/save----e=" + e.getMessage());
            }
            e.printStackTrace();
            ResponseMessage.error(msgCode, msg);
        }
    }


    @RequestMapping(value = "/getTip", method = {RequestMethod.POST})
    @ApiOperation(value = "【家长端和教师端】。获取成长记录提示")
    public void getTip(HttpServletRequest request,
                       HttpServletResponse response,
                       @ApiParam(value = "随机数。【参与签名】") @RequestParam(value = "nonce", required = true) String nonce,
                       @ApiParam(value = "签名") @RequestParam(value = "sign", required = true) String sign
    ) {
        try {
            Map<String, Object> param = new HashMap<String, Object>();
            param.put("nonce", nonce);
            param.put("sign", sign);

            boolean signSer = MobileSignUtils.signCheck(param, sign);
            if (!signSer) {
                logger.warn("签名校验失败");
            }
            Long memberId = AccountIdHoldUtils.getMemberId();
            Long bunchId = AccountIdHoldUtils.getBunchId();
            Map<String, Object> result = tipService.getTip(memberId, bunchId);
            ResponseMessage.success(result);
        } catch (Exception e) {
            String msg = CommonConstants.ERRORMSG;
            Integer msgCode = CommonConstants.FAIL_CODE;
            if (e.getClass().getSimpleName().equalsIgnoreCase("ErrorException")) {
                msg = e.getMessage();
            } else if (e.getClass().getSimpleName().equalsIgnoreCase("TokenException")) {
                msgCode = CommonConstants.TOKEN_CODE;
                msg = CommonConstants.TOKENMSG;
            } else {
                logger.error("/sunday/mobile/history/getTip----e=" + e.getMessage());
            }
            e.printStackTrace();
            ResponseMessage.error(msgCode, msg);
        }
    }


}
