package org.itboys.mobile.controller.web;

import org.apache.commons.lang3.StringUtils;
import org.itboys.admin.tools.AdminSessionHolder;
import org.itboys.commons.CommonConstants;
import org.itboys.framework.spring.controller.BaseController;
import org.itboys.mobile.common.SundayCommonConstants;
import org.itboys.mobile.entity.mongo.history.SundayHistory;
import org.itboys.mobile.service.mongo.web.SundayHistoryService;
import org.itboys.mobile.util.AccountIdHoldUtils;
import org.itboys.mobile.util.ResponseMessage;
import org.itboys.param.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

@Controller
@RequestMapping("/sunday/web/history/v2")
public class SundayHistoryV2Controller extends BaseController {
    public static	Map<String,Object> search_param_temp;
    static{
        search_param_temp=new HashMap();
    }
    @Autowired
    private SundayHistoryService historyService;

    /**
     * 园区主页
     * @param request
     * @param model
     * @return
     */
    @RequestMapping("/index" )
    public  String index(HttpServletRequest request,Model model){
        boolean isClean = StringUtils.isNotEmpty(request.getParameter("isClean"))?false:true;
        logger.info("是否清除查询参数="+isClean);
        if(isClean){
            search_param_temp.clear();
        }
        model.addAttribute("param",search_param_temp);
        return "/sunday/history/v2/index";
    }

    @RequestMapping(value="/select",method={RequestMethod.POST})
    public  String select(HttpServletRequest request,
                          HttpServletResponse response,
                          @RequestParam(value="status",required=true)Integer status,
                          Model model) {
        try{
            PageResult<SundayHistory> result=historyService.selectHistory(request, AdminSessionHolder.getGuardIds(),AdminSessionHolder.getBunchIds(),status);
            //model.addAttribute("result",result);
            //2018年10月18日，增加分页需要从参数
            packagePageParam(model,result.getTotal(),request);
            model.addAttribute("histories",result.getData());
            //2018年11月27日，捕获查询参数
            packageSearchParam(request,search_param_temp);
            return "/sunday/history/v2/data";
        }catch (Exception e){
            logger.error("/sunday/history/web/v2/select----e="+e.getMessage());
            e.printStackTrace();
            return "/sunday/error";
        }
    }


    /**
     * 详情
     * @param request
     * @param id
     * @param id
     * @return
     */
    @RequestMapping("/input")
    public String input(
            HttpServletRequest request,
            HttpServletResponse response,
            @RequestParam(value="id",required=true)Long id,
            Model model){
        Map<String,Object> result = historyService.findOne(id);
        model.addAttribute("result", result);
        return "/sunday/history/v2/input";
    }


    @RequestMapping("/delete")
    public  void delete(HttpServletRequest request,
                      HttpServletResponse response,
                      @RequestParam(value="id",required=true)Long id
    ) {
        try {
            historyService.delete(id);
            ResponseMessage.success();
        } catch (Exception e) {
            String msg = "";
            if (SundayCommonConstants.ERROR_EXCEPTION.equalsIgnoreCase(e.getClass().getSimpleName())) {
                msg = e.getMessage();
            } else {
                msg = CommonConstants.ERRORMSG;
                logger.error("/sunday/web/history/v2/delete------" + e.getMessage());
            }
            e.printStackTrace();

            ResponseMessage.error(CommonConstants.FAIL_CODE,msg);
        }
    }
    @RequestMapping("/handle")
    public  void handle(HttpServletRequest request,
                        HttpServletResponse response,
                        @RequestParam(value="id",required=true)Long id
    ) {
        try {
            historyService.handle(id);
            ResponseMessage.success();
        } catch (Exception e) {
            String msg = "";
            if (SundayCommonConstants.ERROR_EXCEPTION.equalsIgnoreCase(e.getClass().getSimpleName())) {
                msg = e.getMessage();
            } else {
                msg = CommonConstants.ERRORMSG;
                logger.error("/sunday/web/history/v2/handle------" + e.getMessage());
            }
            e.printStackTrace();

            ResponseMessage.error(CommonConstants.FAIL_CODE,msg);
        }
    }
}
