package org.itboys.mobile.controller.mobile;

import com.wordnik.swagger.annotations.Api;
import com.wordnik.swagger.annotations.ApiOperation;
import com.wordnik.swagger.annotations.ApiParam;
import org.itboys.commons.CommonConstants;
import org.itboys.framework.spring.controller.BaseController;
import org.itboys.mobile.dto.SundayNoticeDto;
import org.itboys.mobile.dto.SundayOpusDto;
import org.itboys.mobile.entity.mongo.SundayNotice;
import org.itboys.mobile.service.mongo.SundayNoticeRecordService;
import org.itboys.mobile.service.mongo.front.SundayFrontNoticeService;
import org.itboys.mobile.util.AccountIdHoldUtils;
import org.itboys.mobile.util.MobileSignUtils;
import org.itboys.mobile.util.ResponseMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 作者：jiangxiong
 * 日期：2017年2月8日
 * 联系方式 ：<EMAIL>
 * 描述：百睿_宝贝作品集_controller。
 */
@Controller
@RequestMapping("/sunday/mobile/notice")
@Api(value = "notice", description = "通知")
public class SundayMobileNoticeController extends BaseController {
    @Autowired
    private SundayFrontNoticeService noticeService;

    @Autowired
    private SundayNoticeRecordService recordService;

    @RequestMapping(value="/getNotice", method = {RequestMethod.POST})
    @ApiOperation(value="【家长端和教师端】获取通知列表。（分页）")
    public void getNotice(HttpServletRequest request,
                      HttpServletResponse response,
                     @ApiParam(required = true, name = "pageSize", value = "分页参数。每页数量。默认20") @RequestParam(value = "pageSize", required = true) String pageSize,
                     @ApiParam(required = true, name = "pageNumber", value = "分页参数。第几页。默认1") @RequestParam(value = "pageNumber", required = true) String pageNumber,
                     @ApiParam(required = false, name = "sort", value = "排序参数。字段名称。默认为空") @RequestParam(value = "sort", required = false) String sort,
                     @ApiParam(required = false, name = "order", value = "排序参数。。默认为空。ASC正序/DESC倒序") @RequestParam(value = "order", required = false) String order,
                     @ApiParam(value = "随机数。【参与签名】")@RequestParam(value = "nonce", required = true) String nonce,
                    @ApiParam(value = "签名")@RequestParam(value = "sign", required = true) String sign
    ){
        try{
            Map<String, Object> param =new HashMap<String,Object>();
            param.put("nonce", nonce);
            param.put("sign", sign);
            boolean signSer = MobileSignUtils.signCheck(param, sign);
            if(!signSer){
                logger.warn("签名校验失败");
            }
            Long memberId = AccountIdHoldUtils.getMemberId();
            Long bunchId = AccountIdHoldUtils.getBunchId();
           List<SundayNoticeDto> dtos= noticeService.getNotice(request,memberId,bunchId);
            ResponseMessage.success(dtos);
        }catch (Exception e){
            String msg = CommonConstants.ERRORMSG;
            Integer msgCode= CommonConstants.FAIL_CODE;
            if(e.getClass().getSimpleName().equalsIgnoreCase("ErrorException")){
                msg=e.getMessage();
            }else  if(e.getClass().getSimpleName().equalsIgnoreCase("TokenException")){
                msgCode= CommonConstants.TOKEN_CODE;
                msg= CommonConstants.TOKENMSG;
            }else{
                logger.error("/sunday/mobile/notice/getNotice----e="+e.getMessage());
            }
            e.printStackTrace();
            ResponseMessage.error(msgCode,msg);
        }
    }


    @RequestMapping(value = "/getNoBrowseNotice", method = {RequestMethod.GET})
    @ApiOperation(value = "【家长端和教师端】获取未浏览的通知(红点)")
    public void getNoBrowseNotice(HttpServletRequest request,
                                @ApiParam(value = "随机数。[参与签名]") @RequestParam(value = "nonce") String nonce,
                                @ApiParam(value = "签名") @RequestParam(value = "sign") String sign) {
        try {
            Map<String, Object> param = new HashMap();
            param.put("nonce", nonce);
            param.put("sign", sign);
            boolean signSer = MobileSignUtils.signCheck(param, sign);
            if (!signSer) {
                logger.warn("签名校验失败");
            }
            Long bunchId = AccountIdHoldUtils.getBunchId();
            Long memberId = AccountIdHoldUtils.getMemberId();
            long count = recordService.countNoBrowseNotice(request,memberId,bunchId);
            ResponseMessage.success();
        } catch (Exception e) {
            String msg = CommonConstants.ERRORMSG;
            Integer msgCode = CommonConstants.FAIL_CODE;
            if (e.getClass().getSimpleName().equalsIgnoreCase("ErrorException")) {
                msg = e.getMessage();
            } else if (e.getClass().getSimpleName().equalsIgnoreCase("TokenException")) {
                msgCode = CommonConstants.TOKEN_CODE;
                msg = CommonConstants.TOKENMSG;
            } else {
                logger.error("/sunday/mobile/notice/getNoBrowseNotice----e=" + e.getMessage());
            }
            e.printStackTrace();
            ResponseMessage.error(msgCode, msg);
        }
    }

}
