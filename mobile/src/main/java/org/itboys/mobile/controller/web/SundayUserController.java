package org.itboys.mobile.controller.web;

import org.apache.commons.lang3.StringUtils;
import org.itboys.admin.entity.lasted.SystemRole;
import org.itboys.admin.entity.lasted.SystemUser;
import org.itboys.admin.tools.AdminSessionHolder;
import org.itboys.commons.CommonConstants;
import org.itboys.framework.spring.controller.BaseController;
import org.itboys.mobile.common.SundayCommonConstants;
import org.itboys.mobile.service.mongo.web.SundayParentService;
import org.itboys.mobile.service.mongo.web.SundayUserService;
import org.itboys.mobile.util.AccountIdHoldUtils;
import org.itboys.mobile.util.ResponseMessage;
import org.itboys.param.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 作者：江兄
 * 日期：2018年7月31日
 * 描述：百睿_系统用户_controller
 */
@Controller
@RequestMapping("/sunday/web/user")
public class SundayUserController extends BaseController {
    public static	Map<String,Object> search_param_temp;
    static{
        search_param_temp=new HashMap<String, Object>();
    }
    @Autowired
    private SundayUserService userService;

    @RequestMapping("/index" )
    public  String index(HttpServletRequest request,
                         HttpServletResponse response,
                         Model model){
        boolean isClean = StringUtils.isNotEmpty(request.getParameter("isClean"))?false:true;
        logger.info("是否清除查询参数="+isClean);
        if(isClean){
            search_param_temp.clear();
        }
        //上一次的查询参数
        model.addAttribute("param",search_param_temp);
        return "/sunday/user/index";
    }

    @RequestMapping(value="/select",method={RequestMethod.POST})
    public  String select(HttpServletRequest request,
                          HttpServletResponse response,
                          Model model) {
        try{

            PageResult<SystemUser> result=userService.selectUser(request,AdminSessionHolder.getGuardIds(),AdminSessionHolder.getBunchIds());
            //model.addAttribute("result",result);
            //2018年10月18日，增加分页需要从参数
            packagePageParam(model,result.getTotal(),request);
            model.addAttribute("users",result.getData());
            //2018年11月27日，捕获查询参数
            packageSearchParam(request,search_param_temp);
            return "/sunday/user/data";
        }catch (Exception e){
            logger.error("/sunday/user/web/select----e="+e.getMessage());
            e.printStackTrace();
            return "/sunday/error";
        }
    }
    @RequestMapping("/role/select")
    public void role_select(HttpServletRequest request,
                       HttpServletResponse response

    ) {
        try {
            List<SystemRole> result=userService.selectRoleNoPage();
            ResponseMessage.success(result);
        } catch (Exception e) {
            e.printStackTrace();
            ResponseMessage.error(CommonConstants.FAIL_CODE,CommonConstants.ERRORMSG);
        }
    }
    /**
     * 详情
     * @param request
     * @param id
     * @param id
     * @return
     */
    @RequestMapping("/input")
    public String input(
            HttpServletRequest request,
            HttpServletResponse response,
            @RequestParam(value="id",required=true)Long id,
            Model model){
    /*    SystemUser user = userService.findOne(id);
        model.addAttribute("user", user);
        //2018年10月25日，去除所有园区
        List<SundayGuard> guards = guardService.selectGuardNoPage(request,AdminSessionHolder.getPlatformMemberId());
        model.addAttribute("guards",guards);*/
        //2018年11月6日
        Map<String,Object> result = userService.findOne(id,AdminSessionHolder.getGuardIds(),AdminSessionHolder.getBunchIds(),AdminSessionHolder.getAdminUserId());
        model.addAttribute("result", result);

        return "/sunday/user/input";
    }


    /**
     * 新增或修改
     * @param request
     * @param response
     * @param user

     */
    @RequestMapping("/save")
    public  void save(HttpServletRequest request,
                      HttpServletResponse response,
                      @ModelAttribute SystemUser user

    ) {
        try {
            userService.saveUser(user);
            ResponseMessage.success();
        } catch (Exception e) {
            String msg = "";
            if (SundayCommonConstants.ERROR_EXCEPTION.equalsIgnoreCase(e.getClass().getSimpleName())) {
                msg = e.getMessage();
            } else {
                msg = CommonConstants.ERRORMSG;
                logger.error("/sunday/web/user/save------" + e.getMessage());
            }
            e.printStackTrace();

            ResponseMessage.error(CommonConstants.FAIL_CODE,msg);
        }
    }


    @RequestMapping("/delete")
    public  void delete(HttpServletRequest request,
                      HttpServletResponse response,
                      @RequestParam(value = "id", required = true) Long id

    ) {
        try {
            userService.delete(id);
            ResponseMessage.success();
        } catch (Exception e) {
            String msg = "";
            if (SundayCommonConstants.ERROR_EXCEPTION.equalsIgnoreCase(e.getClass().getSimpleName())) {
                msg = e.getMessage();
            } else {
                msg = CommonConstants.ERRORMSG;
                logger.error("/sunday/web/user/delete------" + e.getMessage());
            }
            e.printStackTrace();

            ResponseMessage.error(CommonConstants.FAIL_CODE,msg);
        }
    }


}
