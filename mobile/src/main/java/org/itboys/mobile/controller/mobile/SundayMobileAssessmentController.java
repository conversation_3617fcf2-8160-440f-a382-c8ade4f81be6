package org.itboys.mobile.controller.mobile;

import com.wordnik.swagger.annotations.ApiOperation;
import com.wordnik.swagger.annotations.ApiParam;
import org.itboys.commons.CommonConstants;
import org.itboys.commons.utils.tools.LongUtil;
import org.itboys.framework.spring.controller.BaseController;
import org.itboys.mobile.dto.SundayAssessmentDto;
import org.itboys.mobile.service.mongo.front.SundayFrontAssessmentService;
import org.itboys.mobile.util.AccountIdHoldUtils;
import org.itboys.mobile.util.MobileSignUtils;
import org.itboys.mobile.util.ResponseMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 作者：jiangxiong
 * 日期：2017年2月8日
 * 联系方式 ：<EMAIL>
 * 描述：百睿_评测_controller。
 */
@Controller
@RequestMapping("/sunday/mobile/assessment")
public class SundayMobileAssessmentController extends BaseController {

    @Autowired
    private SundayFrontAssessmentService assessmentService;

    @RequestMapping(value="/getIndexData", method={ RequestMethod.POST})
    @ApiOperation(value="【家长端】获取评测首页所有数据。")
    public void getIndexData(
            HttpServletRequest request,
            HttpServletResponse response,
            @ApiParam(value = "随机数。【参与签名】") @RequestParam(value = "nonce", required = true) String nonce,
            @ApiParam(value = "签名") @RequestParam(value = "sign", required = true) String sign
    ) {
        try {
            Map<String, Object> param =new HashMap<String,Object>();
            param.put("nonce", nonce);
            param.put("sign", sign);

            boolean signSer = MobileSignUtils.signCheck(param, sign);
            if(!signSer){
                logger.warn("签名校验失败");
            }
            Long memberId = AccountIdHoldUtils.getMemberId();
            Map<String,Object> result = assessmentService.getIndexData(request,memberId);
            ResponseMessage.success(result);

        } catch (Exception e) {
            String msg ="";
            if(e.getClass().getSimpleName().equalsIgnoreCase("ErrorException")){
                msg=e.getMessage();
            }else{
                msg= CommonConstants.ERRORMSG;
                logger.info("/sunday/mobile/assessment/list--------"+e);
                e.printStackTrace();
            }
            ResponseMessage.error(CommonConstants.FAIL_CODE,msg);

        }
    }

        @RequestMapping(value="/getData", method={ RequestMethod.POST})
        @ApiOperation(value="【家长端】获取评测主体列表(分页)。")
        public void getData(
                        HttpServletRequest request,
                        HttpServletResponse response,
                        @ApiParam(required = true, name = "type", value = "类型：1入园测评，2注意力测评,999全部，【参与签名】") @RequestParam(value = "type", required = true) Integer type,
                        @ApiParam(required = true, name = "pageSize", value = "分页参数。每页数量。默认20") @RequestParam(value = "pageSize", required = true) String pageSize,
                        @ApiParam(required = true, name = "pageNumber", value = "分页参数。第几页。默认1") @RequestParam(value = "pageNumber", required = true) String pageNumber,
                        @ApiParam(required = false, name = "sort", value = "排序参数。字段名称。默认为空") @RequestParam(value = "sort", required = false) String sort,
                        @ApiParam(required = false, name = "order", value = "排序参数。。默认为空。ASC正序/DESC倒序") @RequestParam(value = "order", required = false) String order,
                        @ApiParam(value = "随机数。【参与签名】") @RequestParam(value = "nonce", required = true) String nonce,
                        @ApiParam(value = "签名") @RequestParam(value = "sign", required = true) String sign
                    ) {
            try {
                Map<String, Object> param =new HashMap<String,Object>();
                param.put("nonce", nonce);
                param.put("sign", sign);
                param.put("type",type);
                boolean signSer = MobileSignUtils.signCheck(param, sign);
                if(!signSer){
                    logger.warn("签名校验失败");
                }
                Long memberId = AccountIdHoldUtils.getMemberId();
                List<SundayAssessmentDto> result = assessmentService.getData(request,memberId,type);
                ResponseMessage.success(result);

            } catch (Exception e) {
                String msg ="";
                if(e.getClass().getSimpleName().equalsIgnoreCase("ErrorException")){
                    msg=e.getMessage();
                }else{
                    msg= CommonConstants.ERRORMSG;
                    logger.info("/sunday/mobile/assessment/list--------"+e);
                    e.printStackTrace();
                }
                ResponseMessage.error(CommonConstants.FAIL_CODE,msg);

            }
        }

    /**
     *
     * @param request
     * @param response
     * @param assessmentId
     * @param nonce
     * @param sign
     */
    @RequestMapping(value="/getDetail", method={ RequestMethod.POST})
    @ApiOperation(value="【家长端】获取评测详情（含评测所有问题和选项）。")
    public void getDetail(
            HttpServletRequest request,
            HttpServletResponse response,
            @ApiParam(required = true, name = "assessmentId", value = "评测主体ID，") @RequestParam(value = "assessmentId", required = true) Long assessmentId,
            @ApiParam(value = "随机数。【参与签名】") @RequestParam(value = "nonce", required = true) String nonce,
            @ApiParam(value = "签名") @RequestParam(value = "sign", required = true) String sign
    ) {
        try {
            // SundayApkInfo apkInfo=  apkInfoService.getApkInfo(versionCode, type);
            Map<String, Object> param =new HashMap<String,Object>();
            param.put("nonce", nonce);
            param.put("sign", sign);
            param.put("assessmentId",assessmentId);
            boolean signSer = MobileSignUtils.signCheck(param, sign);
            if(!signSer){
                logger.warn("签名校验失败");
            }
            Long memberId = AccountIdHoldUtils.getMemberId();
            SundayAssessmentDto result = assessmentService.getDetail(assessmentId,memberId);
            ResponseMessage.success(result);

        } catch (Exception e) {
            String msg ="";
            if(e.getClass().getSimpleName().equalsIgnoreCase("ErrorException")){
                msg=e.getMessage();
            }else{
                msg= CommonConstants.ERRORMSG;
                logger.info("/sunday/mobile/assessment/getDetail--------"+e);
                e.printStackTrace();
            }
            ResponseMessage.error(CommonConstants.FAIL_CODE,msg);

        }
    }
    @RequestMapping(value="/submit", method={ RequestMethod.POST})
    @ApiOperation(value="【家长端】提交评测结果。")
    public void submit(
            HttpServletRequest request,
            HttpServletResponse response,
            @ApiParam(required = true, name = "assessmentId", value = "评测主体ID。【参与签名】") @RequestParam(value = "assessmentId", required = true) Long assessmentId,
            @ApiParam(required = true, name = "questionIds", value = "评测问题ID集合(字符串)。例如：questionIds=1,2,3,4。集合大小、顺序和评测选项ID集合一致") @RequestParam(value = "questionIds", required = true) List<Long> questionIds,
            @ApiParam(required = true, name = "optionIds", value = "评测选项ID集合(字符串)。例如：optionIds=1,2,3,4。集合大小、顺序和评测问题ID集合一致") @RequestParam(value = "optionIds", required = true) List<Long> optionIds,
            @ApiParam(value = "随机数。【参与签名】") @RequestParam(value = "nonce", required = true) String nonce,
            @ApiParam(value = "签名") @RequestParam(value = "sign", required = true) String sign
    ) {
        try {
            Map<String, Object> param =new HashMap<String,Object>();
            param.put("nonce", nonce);
            param.put("sign", sign);
            param.put("assessmentId",assessmentId);
            boolean signSer = MobileSignUtils.signCheck(param, sign);
            if(!signSer){
                logger.warn("签名校验失败");
            }
            Long memberId = AccountIdHoldUtils.getMemberId();
            assessmentService.submit(memberId,assessmentId,questionIds,optionIds);
            ResponseMessage.success();

        } catch (Exception e) {
            String msg ="";
            if(e.getClass().getSimpleName().equalsIgnoreCase("ErrorException")){
                msg=e.getMessage();
            }else{
                msg= CommonConstants.ERRORMSG;
                logger.info("/sunday/mobile/assessment/submit--------"+e);
                e.printStackTrace();
            }
            ResponseMessage.error(CommonConstants.FAIL_CODE,msg);

        }
    }
    /**
     *
     * @param request
     * @param response
     * @param model
     * @param assessmentId
     * @return
     */
    @RequestMapping(value="/h5/getResult",method={RequestMethod.GET})
    @ApiOperation(value="【家长端】获取评测结果（h5页面）直接加载该URL。")
    public  String h5_getResult(HttpServletRequest request,
                            HttpServletResponse response,
                            Model model,
                            @ApiParam(required = false, name = "assessmentId", value = "评测主体ID，") @RequestParam(value = "assessmentId", required = false) Long assessmentId,
                            @ApiParam(required = false, name = "type", value = "评测类型.1入院测试，2注意评测 ") @RequestParam(value = "type", required = false) Integer type,
                                @ApiParam(required = false, name = "boolean", value = "是否首页进入") @RequestParam(value = "isIndex", required = false) boolean isIndex){
            try{
                Long memberId = AccountIdHoldUtils.getMemberId();
                Map<String,Object> result = assessmentService.getResult(assessmentId,type,memberId,isIndex);
                model.addAttribute("result",result);
                if(LongUtil.isNotZreo((Long)result.get("id"))){
                    type =(Integer)result.get("type");
                    if(type == 1){
                        return  "/yuhua/mobile/assessment/result1";
                    }
                    if(type == 2){
                        return  "/yuhua/mobile/assessment/result2";
                    }
                }else{
                    return "/yuhua/mobile/assessment/result_empty";
                }

                return "";
            }catch (Exception e){
                logger.error("/sunday/mobile/assessment/h5/getResult----e="+e.getMessage());
                e.printStackTrace();
                return "/yuhua/mobile/error";
            }
    }

}
