package org.itboys.mobile.controller.web;

import org.apache.commons.lang3.StringUtils;
import org.itboys.admin.tools.AdminSessionHolder;
import org.itboys.commons.CommonConstants;
import org.itboys.framework.query.JsonPageUtils;
import org.itboys.framework.spring.controller.BaseController;
import org.itboys.mobile.common.SundayCommonConstants;
import org.itboys.mobile.entity.mongo.SundayEnglish;
import org.itboys.mobile.entity.mongo.SundayOpus;
import org.itboys.mobile.entity.mongo.SundayPlan;
import org.itboys.mobile.service.mongo.web.SundayOpusService;
import org.itboys.mobile.util.AccountIdHoldUtils;
import org.itboys.mobile.util.ResponseMessage;
import org.itboys.param.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

@Controller
@RequestMapping("/sunday/web/opus")
public class SundayOpusController extends BaseController {
    public static Map<String,Object> search_param_temp;
    static{
        search_param_temp=new HashMap<String, Object>();
    }
    @Autowired
    private SundayOpusService opusService;

    /**
     * 园区主页
     * @param request
     * @param model
     * @return
     */
    @RequestMapping("/index" )
    public  String index(HttpServletRequest request,Model model){
        boolean isClean = StringUtils.isNotEmpty(request.getParameter("isClean"))?false:true;
        logger.info("是否清除查询参数="+isClean);
        if(isClean){
            search_param_temp.clear();
        }
        model.addAttribute("param",search_param_temp);
        return "/sunday/opus/index";
    }

    @RequestMapping(value="/select",method={RequestMethod.POST})
    public  String select(HttpServletRequest request,
                          HttpServletResponse response,

                          Model model) {
        try{

            PageResult<SundayOpus> result=opusService.selectOpus(request,AdminSessionHolder.getGuardIds(),AdminSessionHolder.getBunchIds());
            //2018年10月18日，增加分页需要从参数
            packagePageParam(model,result.getTotal(),request);
            model.addAttribute("opuses",result.getData());
            //2018年11月27日，捕获查询参数
            packageSearchParam(request,search_param_temp);
            return "/sunday/opus/data";
        }catch (Exception e){
            logger.error("/sunday/opus/web/select----e="+e.getMessage());
            e.printStackTrace();
            return "/sunday/error";
        }
    }
    /**
     * 商品详情页
     *
     * @param request
     * @param id
     * @param model
     * @return
     */
    @RequestMapping("/input")
    public String input(
            HttpServletRequest request,
            HttpServletResponse response,
            @RequestParam(value="id",required=true)Long id,
            Model model){
        SundayOpus opus = opusService.findOne(id);

        model.addAttribute("opus", opus);
        return "/sunday/opus/input";
    }


    /**
     * 新增或修改
     *
     * @param request
     * @param response
     * @param opus


     */
    @RequestMapping("/save")
    public  void save(HttpServletRequest request,
                      HttpServletResponse response,
                      @ModelAttribute SundayOpus opus

    ) {
        try {
            opusService.saveOpus(opus);
            ResponseMessage.success();
        } catch (Exception e) {
            String msg = "";
            if (CommonConstants.ERROR_EXCEPTION.equalsIgnoreCase(e.getClass().getSimpleName())) {
                msg = e.getMessage();
            } else {
                msg = CommonConstants.ERRORMSG;
                logger.error("/sunday/web/opus/save------" + e.getMessage());
            }
            e.printStackTrace();

            ResponseMessage.error(CommonConstants.FAIL_CODE, msg);
        }
    }

    @RequestMapping("/delete")
    public  void delete(HttpServletRequest request,
                      HttpServletResponse response,
                      @RequestParam(value="id",required=true)Long id
    ) {
        try {
            opusService.delete(id);
            ResponseMessage.success();
        } catch (Exception e) {
            String msg = "";
            if (SundayCommonConstants.ERROR_EXCEPTION.equalsIgnoreCase(e.getClass().getSimpleName())) {
                msg = e.getMessage();
            } else {
                msg = CommonConstants.ERRORMSG;
                logger.error("/sunday/web/opus/delete------" + e.getMessage());
            }
            e.printStackTrace();

            ResponseMessage.error(CommonConstants.FAIL_CODE,msg);
        }
    }

}
