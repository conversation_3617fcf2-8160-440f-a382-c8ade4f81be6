package org.itboys.mobile.controller.web;

import org.itboys.admin.tools.AdminSessionHolder;
import org.itboys.commons.utils.tools.LongUtil;
import org.itboys.framework.spring.controller.BaseController;
import org.itboys.mobile.service.SundayDesktopService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 作者：jiangxiong
 * 日期：2017年5月8日
 * 联系方式 ：<EMAIL>
 * 描述：百睿_后台首页_controller
 */
@Controller
@RequestMapping("/sunday/web/desktop")
public class SundayDesktopController extends BaseController {

    @Autowired
    private SundayDesktopService desktopService;

    /**
     *   主页
     * @param request
     * @param model
     * @return
     */
    @RequestMapping("/index" )
    public  String index(HttpServletRequest request,
                         HttpServletResponse response,
                           Model model){
      /*  //2018年3月14日，判断登陆用户是后台管理员还是商户自己
        if(LongUtil.isNotZreo(AdminSessionHolder.getPlatformMemberId())){
            //返回商户主页
            return "/sunday/desktop/index2";
        }else{
            //返回系统主页
            return "/sunday/desktop/index";
        }*/
        return "/sunday/desktop/index";

    }

}
