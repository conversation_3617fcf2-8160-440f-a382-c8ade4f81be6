package org.itboys.mobile.controller.web;

import com.wordnik.swagger.annotations.ApiOperation;
import com.wordnik.swagger.annotations.ApiParam;
import org.itboys.commons.CommonConstants;
import org.itboys.commons.dto.ResultPageDOHelper;
import org.itboys.commons.utils.ajax.AjaxUtils;
import org.itboys.framework.query.JsonPageUtils;
import org.itboys.framework.spring.controller.BaseController;
import org.itboys.mobile.entity.mongo.SundayApkInfo;
import org.itboys.mobile.service.mongo.web.SundayApkInfoService;
import org.itboys.param.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @author：jiangxiong 日期：2017年2月8日
 * 联系方式 ：<EMAIL>
 * 描述：百睿_更新APK_controller。
 */
@Controller
@RequestMapping("/sunday/web/apk")
public class SundayApkController extends BaseController {
    @Autowired
    private SundayApkInfoService apkInfoService;

    /**
     * app管理
     *
     * @param request
     * @param model
     * @return
     */
    @RequestMapping("/index")
    public String index(HttpServletRequest request,
                        HttpServletResponse response,
                        Model model) {
        return "/sunday/apk/index";
    }

    /**
     * 上传包
     *
     * @param response
     * @param file
     * @param log
     * @param type
     * @param versionCode
     */
    @RequestMapping(value = "/upload", method = RequestMethod.POST)
    @ApiOperation(value = "上传最新的包文件，苹果版本不需要上传包。")
    public void upload(HttpServletRequest request,
                        HttpServletResponse response,
                       @ApiParam(required = false, name = "file", value = "附件流") @RequestParam(value = "file", required = false) MultipartFile file,
                       @ApiParam(required = false, name = "log", value = "更新日志") @RequestParam(value = "log", required = true) String log,
                       @ApiParam(required = true, name = "type", value = "1，安卓家长版本，3，苹果家长版，") @RequestParam(value = "type", required = true) Integer type,
                       @ApiParam(required = true, name = "forceUpdate", value = "0:正常更新 1:强制更新") @RequestParam(value = "forceUpdate", required = true) Integer forceUpdate,
                       @ApiParam(required = false, name = "versionCode", value = "版本号。【苹果专用】") @RequestParam(value = "versionCode", required = false) Integer versionCode,
                       @ApiParam(required = false, name = "downloadUrl", value = "下载地址。【苹果专用】") @RequestParam(value = "downloadUrl", required = false) String downloadUrl
    ) {
        try {
            apkInfoService.uploadApk2(log, type, file, versionCode, downloadUrl,forceUpdate);

            AjaxUtils.renderJsonporJson(response, null,
                    ResultPageDOHelper.getMsgResultDO(CommonConstants.SUCCESS_CODE, CommonConstants.SUCCESS));

        } catch (Exception e) {

            String msg = "";
            if ("errorException".equalsIgnoreCase(e.getClass().getSimpleName())) {
                msg = e.getMessage();
            } else {
                msg = CommonConstants.ERRORMSG;
                logger.info("/sunday/web/apk/upload--------" + e);
                e.printStackTrace();
            }
            AjaxUtils.renderJsonporJson(response, null,
                    ResultPageDOHelper.getMsgCodeResultDO(
                            CommonConstants.FAIL_CODE, msg));
        }
    }

    /**
     * 分页获取版本信息
     *
     * @param request
     * @param response
     */
    @RequestMapping("/select")
    public void select(HttpServletRequest request,
                       HttpServletResponse response
    ) {
        try {
            PageResult<SundayApkInfo> result = apkInfoService.select(request);
            JsonPageUtils.renderJsonPage(result.getTotal(), result.getData(), response);
        } catch (Exception e) {
            e.printStackTrace();
            AjaxUtils.renderJsonporJson(response, null,
                    ResultPageDOHelper.getMsgCodeResultDO(CommonConstants.FAIL_CODE, CommonConstants.FAIL));
        }
    }

    /**
     * 删除
     *
     * @param response
     * @param id
     */
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    public void delete(HttpServletResponse response,
                       @RequestParam(value = "id", required = true) Long id) {
        try {
            apkInfoService.delete(id);

            AjaxUtils.renderJsonporJson(response, null,
                    ResultPageDOHelper.getMsgResultDO(CommonConstants.SUCCESS_CODE, CommonConstants.SUCCESS));

        } catch (Exception e) {

            String msg = "";
            if ("errorException".equalsIgnoreCase(e.getClass().getSimpleName())) {
                msg = e.getMessage();
            } else {
                msg = CommonConstants.ERRORMSG;
                logger.info("/sunday/web/apk/delete--------" + e);
                e.printStackTrace();
            }
            AjaxUtils.renderJsonporJson(response, null,
                    ResultPageDOHelper.getMsgCodeResultDO(
                            CommonConstants.FAIL_CODE, msg));
        }
    }
}
