package org.itboys.mobile.controller.web;

import org.apache.commons.lang3.StringUtils;
import org.itboys.admin.entity.lasted.SystemUser;
import org.itboys.admin.tools.AdminSessionHolder;
import org.itboys.commons.CommonConstants;
import org.itboys.framework.spring.controller.BaseController;
import org.itboys.mobile.common.SundayCommonConstants;
import org.itboys.mobile.dto.SundayUserDto;
import org.itboys.mobile.service.mongo.web.SundayParentService;
import org.itboys.mobile.service.mongo.web.SundayVisitorService;
import org.itboys.mobile.util.ResponseMessage;
import org.itboys.param.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

@Controller
@RequestMapping("/sunday/web/parent")
public class SundayParentController extends BaseController{
    public static Map<String,Object> search_param_temp;
    static{
        search_param_temp=new HashMap();
    }

    @Autowired
    private SundayParentService parentService;

    @RequestMapping("/userCenter")
    public  String userCenter(HttpServletRequest request,Model model){
        boolean isClean = StringUtils.isNotEmpty(request.getParameter("isClean"))?false:true;
        logger.info("是否清除查询参数="+isClean);
        if(isClean){
            search_param_temp.clear();
        }
        model.addAttribute("param",search_param_temp);
        return "/sunday/parent/userCenter";
    }

    @RequestMapping(value="/selectUserCenter",method={RequestMethod.POST})
    public  String selectUserCenter(HttpServletRequest request,
                                    HttpServletResponse response,
                                    Model model) {
        try{

            PageResult<SundayUserDto> result=parentService.selectUserCenter(request,AdminSessionHolder.getGuardIds());
            //model.addAttribute("result",result);
            //2018年10月18日，增加分页需要从参数
            packagePageParam(model,result.getTotal(),request);
            model.addAttribute("users",result.getData());
            //2018年11月27日，捕获查询参数
            packageSearchParam(request,search_param_temp);
            return "/sunday/parent/data";
        }catch (Exception e){
            logger.error("/sunday/parent/web/selectUserCenter----e="+e.getMessage());
            e.printStackTrace();
            return "/sunday/error";
        }
    }



    @RequestMapping(value = "forbidden",method={RequestMethod.POST})
    public  void forbidden(HttpServletRequest request,
                        HttpServletResponse response,
                        @RequestParam(value="userId",required=true)Long userId,
                        @RequestParam(value="accountType",required=true)String accountType


    ) {
        try {
            parentService.forbidden(userId,accountType);
            ResponseMessage.success();
        } catch (Exception e) {
            String msg = "";
            if (SundayCommonConstants.ERROR_EXCEPTION.equalsIgnoreCase(e.getClass().getSimpleName())) {
                msg = e.getMessage();
            } else {
                msg = CommonConstants.ERRORMSG;
                logger.error("/sunday/web/parent/forbidden------" + e.getMessage());
            }
            e.printStackTrace();

            ResponseMessage.error(CommonConstants.FAIL_CODE,msg);
        }
    }
    @RequestMapping(value = "recover",method={RequestMethod.POST})
    public  void recover(HttpServletRequest request,
                           HttpServletResponse response,
                           @RequestParam(value="userId",required=true)Long userId,
                           @RequestParam(value="accountType",required=true)String accountType

    ) {
        try {
            parentService.recover(userId,accountType);
            ResponseMessage.success();
        } catch (Exception e) {
            String msg = "";
            if (SundayCommonConstants.ERROR_EXCEPTION.equalsIgnoreCase(e.getClass().getSimpleName())) {
                msg = e.getMessage();
            } else {
                msg = CommonConstants.ERRORMSG;
                logger.error("/sunday/web/parent/recover------" + e.getMessage());
            }
            e.printStackTrace();

            ResponseMessage.error(CommonConstants.FAIL_CODE,msg);
        }
    }


}
