package org.itboys.mobile.controller.web;

import org.apache.commons.lang.StringUtils;
import org.itboys.admin.tools.AdminSessionHolder;
import org.itboys.commons.CommonConstants;
import org.itboys.commons.utils.time.TimeUtils;
import org.itboys.framework.spring.controller.BaseController;
import org.itboys.mobile.common.SundayCommonConstants;
import org.itboys.mobile.entity.mongo.SundayNotice;
import org.itboys.mobile.service.mongo.web.SundayNoticeService;
import org.itboys.mobile.util.AccountIdHoldUtils;
import org.itboys.mobile.util.ResponseMessage;
import org.itboys.param.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 作者：江兄
 * 日期：2018年7月31日
 * 描述：百睿_通知_controller
 */
@Controller
@RequestMapping("/sunday/web/notice")
public class SundayNoticeController extends BaseController {
    public static	Map<String,Object> search_param_temp;
    static{
        search_param_temp=new HashMap();
    }
    @Autowired
    private SundayNoticeService noticeService;

    /**
     * 主页
     * @param request
     * @param response
     * @param model
     * @return
     */
    @RequestMapping("/index" )
    public  String index(HttpServletRequest request,Model model){
        boolean isClean = StringUtils.isNotEmpty(request.getParameter("isClean"))?false:true;
        logger.info("是否清除查询参数="+isClean);
        if(isClean){
            search_param_temp.clear();
        }
        model.addAttribute("param",search_param_temp);
        String status = request.getParameter("status");
        model.addAttribute("status", StringUtils.isNotEmpty(status)?status:1);
        return "/sunday/notice/index";
    }




    @RequestMapping(value="/select",method={RequestMethod.POST})
    public  String select(HttpServletRequest request,
                          HttpServletResponse response,

                          Model model) {
        try{
            Long memberId = AccountIdHoldUtils.getMemberId();
            PageResult<SundayNotice> result=noticeService.selectNotice(request,AdminSessionHolder.getGuardIds(),AdminSessionHolder.getBunchIds());
            //model.addAttribute("result",result);
            //2018年10月18日，增加分页需要从参数
            packagePageParam(model,result.getTotal(),request);
            model.addAttribute("notices",result.getData());
            //2018年11月27日，捕获查询参数
            packageSearchParam(request,search_param_temp);
            return "/sunday/notice/data";
        }catch (Exception e){
            logger.error("/sunday/notice/web/select----e="+e.getMessage());
            e.printStackTrace();
            return "/sunday/error";
        }
    }
    /**
     * 详情
     * @param request
     * @param id
     * @param id
     * @return
     */
    @RequestMapping("/input")
    public String input(
            HttpServletRequest request,
            HttpServletResponse response,
            @RequestParam(value="id",required=true)Long id,
            //2018年10月28日配合素材新增
            @RequestParam(value="materialId",required=false)Long materialId,
            Model model){
        SundayNotice notice =noticeService.findOneV2(id,materialId,AdminSessionHolder.getGuardIds(),AdminSessionHolder.getBunchIds());
        model.addAttribute("notice", notice);

        return "/sunday/notice/input";
    }

    /**
     * 新增或修改
     * @param request
     * @param response
     * @param notice

     */
    @RequestMapping("/save")
    public  void save(HttpServletRequest request,
                      HttpServletResponse response,
                      @ModelAttribute SundayNotice notice) {
        try {
            noticeService.saveNotice(notice);
            ResponseMessage.success();
        } catch (Exception e) {
            String msg = "";
            if (SundayCommonConstants.ERROR_EXCEPTION.equalsIgnoreCase(e.getClass().getSimpleName())) {
                msg = e.getMessage();
            } else {
                msg = CommonConstants.ERRORMSG;
                logger.error("/sunday/web/notice/save------" + e.getMessage());
            }
            e.printStackTrace();

            ResponseMessage.error(CommonConstants.FAIL_CODE,msg);
        }
    }

    /**
     * 删除
     * @param request
     * @param response
     * @param id
     */
    @RequestMapping("/delete")
    public  void delete(HttpServletRequest request,
                      HttpServletResponse response,
                        @RequestParam(value="id",required=true)Long id
    ) {
        try {
            noticeService.delete(id);
            ResponseMessage.success();
        } catch (Exception e) {
            String msg = "";
            if (SundayCommonConstants.ERROR_EXCEPTION.equalsIgnoreCase(e.getClass().getSimpleName())) {
                msg = e.getMessage();
            } else {
                msg = CommonConstants.ERRORMSG;
                logger.error("/sunday/web/notice/delete------" + e.getMessage());
            }
            e.printStackTrace();

            ResponseMessage.error(CommonConstants.FAIL_CODE,msg);
        }
    }
}
