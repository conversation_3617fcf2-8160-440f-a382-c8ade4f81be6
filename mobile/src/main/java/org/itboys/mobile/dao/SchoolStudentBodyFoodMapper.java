package org.itboys.mobile.dao;

import java.util.List;
import java.util.Map;

import org.itboys.mobile.entity.mysql.body.SchoolStudentBodyFood;
import org.itboys.mysql.dao.BaseMySqlMapper;

/**
 * 宝宝进餐
 */
public interface SchoolStudentBodyFoodMapper extends BaseMySqlMapper<SchoolStudentBodyFood> {
    SchoolStudentBodyFood getBody(Map<String,Object> param);

    SchoolStudentBodyFood getById(Long id);

    List<SchoolStudentBodyFood> selectChartList(Map<String,Object> param);
    
    int deleteBodyFood(Map<String,Object> param);
}
