package org.itboys.mobile.dao;

import org.apache.ibatis.annotations.Param;
import org.itboys.mobile.entity.mysql.body.SchoolStudentBodyTemperature;
import org.itboys.mysql.dao.BaseMySqlMapper;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface SchoolStudentBodyTemperatureMapper extends BaseMySqlMapper<SchoolStudentBodyTemperature> {

    SchoolStudentBodyTemperature getBody(Map<String, Object> param);

    SchoolStudentBodyTemperature getById(Long id);

    List<SchoolStudentBodyTemperature> selectChartList(Map<String, Object> param);

    SchoolStudentBodyTemperature getTemperature(@Param("checkType") Integer checkType, @Param("recordTime") Date recordTime, @Param("studentId") Long studentId);
}
