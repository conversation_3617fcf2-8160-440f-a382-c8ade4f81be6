package org.itboys.mobile.entity.mysql.online;

import org.itboys.mongodb.entity.BaseMySqlEntity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class SundayOnlineCourseDto{

    private Long id;

    private String courseName;  //课程名称

    private String shortName;   //短标题

    private String image;       // 课程图片

    private Integer courseType;     //课程类型 0图文 1音频 2视频

    private Integer sort;           //更新排序

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCourseName() {
        return courseName;
    }

    public void setCourseName(String courseName) {
        this.courseName = courseName;
    }

    public String getImage() {
        return image;
    }

    public void setImage(String image) {
        this.image = image;
    }

    public Integer getCourseType() {
        return courseType;
    }

    public void setCourseType(Integer courseType) {
        this.courseType = courseType;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getShortName() {
        return shortName;
    }

    public void setShortName(String shortName) {
        this.shortName = shortName;
    }

    public static void main(String[] args) {
        List<Long> arr = new ArrayList<>();
        int num = 5;
        arr.add(1L);
        arr.add(2L);
        arr.add(3L);
        arr.add(4L);
        arr.add(5L);
        List<Long> arr2 = arr.subList(0,num);

    }
}
