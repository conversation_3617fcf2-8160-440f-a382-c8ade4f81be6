package org.itboys.mobile.entity.mysql.report;

import org.itboys.mongodb.entity.BaseMySqlEntity;

/**
 *  周报内容
 * <AUTHOR>
 */
public class SundayReportZb extends BaseMySqlEntity {
	
	private Long id;            //
	//课程编码
    private String code;
    //课程名称
    private String course; 
    //年份
    private Integer year; 
    //月份
    private Integer month;
    //周
    private Integer week;
    //主题
    private String theme;
    //主题歌/运动教学[数组]
    private String sportsTeaching;
    private String[] sportsTeachingArray;
    //教学目的[数组]
    private String teachingObjectives;
    private String[] teachingObjectiveArray;
    //关键字/目标描述
    private String keyWords;
    //对话/亲子时光/在家试一试[数组]
    private String tryAtHome;
    private String[] tryAtHomeArray;
    //状态：0=待发布；1=已发布
    private Integer status;
    //是否存在：1=存在
    private Integer isExists;
    
    //班级ID
    private Long zbBunchId;
    //开始日期
    private String startDate;
    //结束日期
    private String endDate;
    
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getCourse() {
		return course;
	}
	public void setCourse(String course) {
		this.course = course;
	}
	public Integer getYear() {
		return year;
	}
	public void setYear(Integer year) {
		this.year = year;
	}
	public Integer getMonth() {
		return month;
	}
	public void setMonth(Integer month) {
		this.month = month;
	}
	public Integer getWeek() {
		return week;
	}
	public void setWeek(Integer week) {
		this.week = week;
	}
	public String getTheme() {
		return theme;
	}
	public void setTheme(String theme) {
		this.theme = theme;
	}
	public String getSportsTeaching() {
		return sportsTeaching;
	}
	public void setSportsTeaching(String sportsTeaching) {
		this.sportsTeaching = sportsTeaching;
	}
	public String getTeachingObjectives() {
		return teachingObjectives;
	}
	public void setTeachingObjectives(String teachingObjectives) {
		this.teachingObjectives = teachingObjectives;
	}
	public String getKeyWords() {
		return keyWords;
	}
	public void setKeyWords(String keyWords) {
		this.keyWords = keyWords;
	}
	public String getTryAtHome() {
		return tryAtHome;
	}
	public void setTryAtHome(String tryAtHome) {
		this.tryAtHome = tryAtHome;
	}
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	public String[] getSportsTeachingArray() {
		return sportsTeachingArray;
	}
	public void setSportsTeachingArray(String[] sportsTeachingArray) {
		this.sportsTeachingArray = sportsTeachingArray;
	}
	public String[] getTeachingObjectiveArray() {
		return teachingObjectiveArray;
	}
	public void setTeachingObjectiveArray(String[] teachingObjectiveArray) {
		this.teachingObjectiveArray = teachingObjectiveArray;
	}
	public String[] getTryAtHomeArray() {
		return tryAtHomeArray;
	}
	public void setTryAtHomeArray(String[] tryAtHomeArray) {
		this.tryAtHomeArray = tryAtHomeArray;
	}
	public Integer getIsExists() {
		return isExists;
	}
	public void setIsExists(Integer isExists) {
		this.isExists = isExists;
	}
	public Long getZbBunchId() {
		return zbBunchId;
	}
	public void setZbBunchId(Long zbBunchId) {
		this.zbBunchId = zbBunchId;
	}
	public String getStartDate() {
		return startDate;
	}
	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}
	public String getEndDate() {
		return endDate;
	}
	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}
}

