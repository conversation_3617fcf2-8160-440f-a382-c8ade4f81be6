package org.itboys.mobile.entity.mysql.online;

import org.itboys.mongodb.entity.BaseMySqlEntity;

/**
 * @version V1.0
 * @created with IntelliJ IDEA.
 * @Title: entity
 * @author: shumlinmeng
 * @description:
 */

public class SundayOnlineRewardStudent extends BaseMySqlEntity{

    private Long rank; //排名

    private Long rewardId;//奖励ID

    private Long studentId; //学生ID

    private Integer classHour;

    //关联查询

    private String studentName;

    private String bunchIds;

    private String bunchNames;

    private String guardIds;

    private String guardNames;

    private String image;

    public Long getRewardId() {
        return rewardId;
    }

    public void setRewardId(Long rewardId) {
        this.rewardId = rewardId;
    }

    public Long getStudentId() {
        return studentId;
    }

    public void setStudentId(Long studentId) {
        this.studentId = studentId;
    }

    public Integer getClassHour() {
        return classHour;
    }

    public void setClassHour(Integer classHour) {
        this.classHour = classHour;
    }

    public String getStudentName() {
        return studentName;
    }

    public void setStudentName(String studentName) {
        this.studentName = studentName;
    }

    public String getBunchIds() {
        return bunchIds;
    }

    public void setBunchIds(String bunchIds) {
        this.bunchIds = bunchIds;
    }

    public String getBunchNames() {
        return bunchNames;
    }

    public void setBunchNames(String bunchNames) {
        this.bunchNames = bunchNames;
    }

    public String getGuardIds() {
        return guardIds;
    }

    public void setGuardIds(String guardIds) {
        this.guardIds = guardIds;
    }

    public String getGuardNames() {
        return guardNames;
    }

    public void setGuardNames(String guardNames) {
        this.guardNames = guardNames;
    }

    public String getImage() {
        return image;
    }

    public void setImage(String image) {
        this.image = image;
    }

    public Long getRank() {
        return rank;
    }

    public void setRank(Long rank) {
        this.rank = rank;
    }
}
