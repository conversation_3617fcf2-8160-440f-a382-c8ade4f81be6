package org.itboys.mobile.entity.mysql.course;

import org.itboys.mongodb.entity.BaseMySqlEntity;

/**
 * @version V1.0
 * @created with IntelliJ IDEA.
 * @Title: entity
 * @author: shumlinmeng
 * @date: 2019/5/17 10:56
 * @description: 课程素材
 */
public class SundayCourseMaterial extends BaseMySqlEntity{

    private Long course_id;

    private String material_name;

    private Long material_id;

    private Integer play_number;

    public Long getCourse_id() {
        return course_id;
    }

    public void setCourse_id(Long course_id) {
        this.course_id = course_id;
    }

    public String getMaterial_name() {
        return material_name;
    }

    public void setMaterial_name(String material_name) {
        this.material_name = material_name;
    }

    public Long getMaterial_id() {
        return material_id;
    }

    public void setMaterial_id(Long material_id) {
        this.material_id = material_id;
    }

    public Integer getPlay_number() {
        return play_number;
    }

    public void setPlay_number(Integer play_number) {
        this.play_number = play_number;
    }
}
