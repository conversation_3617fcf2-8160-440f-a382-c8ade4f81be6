package org.itboys.mobile.entity.mysql.body;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wordnik.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @version V1.0
 * @created with IntelliJ IDEA.
 * @Title: entity
 * @author: shumlinmeng
 * @description: 体温情况
 */
public class SchoolStudentBodyTemperature implements Serializable {

    private Long id;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date recordTime;        // 记录时间

    private BigDecimal temperature;

    private Long studentId;         // 学生id

    @ApiModelProperty("检查类型 1：晨检、2：午检 3:晚检")
    private Integer checkType;      // 1：晨检、2：午检

    private Integer handleType;      // 处理方式 1：离园返家 2：入园

    @ApiModelProperty("1：正常 2：需要观察 3：委托吃药 4：传染病预警")
    private Integer state;          // 1：正常 2：需要观察 3：委托吃药 4：传染病预警

    @ApiModelProperty("体温情况 0-正常 1-异常")
    private Integer situation;

    private String symptom;

    private Date createTime;

    private String anaphylactogen;

    private Integer shows;

    public String getAnaphylactogen() {
        return anaphylactogen;
    }

    public void setAnaphylactogen(String anaphylactogen) {
        this.anaphylactogen = anaphylactogen;
    }

    public String getAnaphylactogenRemarks() {
        return anaphylactogenRemarks;
    }

    public void setAnaphylactogenRemarks(String anaphylactogenRemarks) {
        this.anaphylactogenRemarks = anaphylactogenRemarks;
    }

    private String anaphylactogenRemarks;

    public String getImages() {
        return images;
    }

    public void setImages(String images) {
        this.images = images;
    }

    private String images;

    /**
     * 来源 0 app 1 管理后台
     */
    private Integer origin;

    public Integer getOrigin() {
        return origin;
    }

    public void setOrigin(Integer origin) {
        this.origin = origin;
    }

    public String getSymptom() {
        return symptom;
    }

    public void setSymptom(String symptom) {
        this.symptom = symptom;
    }

    private Long creatorId;

    private Integer creatorType;    // 1：家长 2：老师

    private Integer deleted;

    private String remarks;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getRecordTime() {
        return recordTime;
    }

    public void setRecordTime(Date recordTime) {
        this.recordTime = recordTime;
    }

    public BigDecimal getTemperature() {
        return temperature;
    }

    public void setTemperature(BigDecimal temperature) {
        this.temperature = temperature;
    }

    public Long getStudentId() {
        return studentId;
    }

    public void setStudentId(Long studentId) {
        this.studentId = studentId;
    }

    public Integer getCheckType() {
        return checkType;
    }

    public void setCheckType(Integer checkType) {
        this.checkType = checkType;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public Integer getCreatorType() {
        return creatorType;
    }

    public void setCreatorType(Integer creatorType) {
        this.creatorType = creatorType;
    }

    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public Integer getHandleType() {
        return handleType;
    }

    public void setHandleType(Integer handleType) {
        this.handleType = handleType;
    }

    public Integer getSituation() {
        return situation;
    }

    public void setSituation(Integer situation) {
        this.situation = situation;
    }

    public Integer getShows() {
        return shows;
    }

    public void setShows(Integer shows) {
        this.shows = shows;
    }
}
