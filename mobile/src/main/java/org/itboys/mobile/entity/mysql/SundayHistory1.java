package org.itboys.mobile.entity.mysql;

import org.itboys.mongodb.entity.BaseMySqlEntity;

import java.util.Date;

/**
 * <AUTHOR>
 * 日期：2017年4月26日
 * 联系方式 ：<EMAIL>
 * 描述：百睿_用户_实体。
 */


public class SundayHistory1 extends BaseMySqlEntity {


    public String content;
    public String pics;
    public Long student_id;
    private Date record_time;

    public SundayHistory1() {

    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getPics() {
        return pics;
    }

    public void setPics(String pics) {
        this.pics = pics;
    }

    public Long getStudent_id() {
        return student_id;
    }

    public void setStudent_id(Long student_id) {
        this.student_id = student_id;
    }

    public Date getRecord_time() {
        return record_time;
    }

    public void setRecord_time(Date record_time) {
        this.record_time = record_time;
    }
}

