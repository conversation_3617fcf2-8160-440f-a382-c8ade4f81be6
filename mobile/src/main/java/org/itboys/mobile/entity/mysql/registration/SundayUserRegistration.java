package org.itboys.mobile.entity.mysql.registration;

import org.itboys.mongodb.entity.BaseMySqlEntity;

import java.util.Date;

/**
 * @version V1.0
 * @created with IntelliJ IDEA.
 * @Title: entity
 * @author: shumlinmeng
 * @description: 极光推送-设备号
 */
public class SundayUserRegistration extends BaseMySqlEntity{

    private Long id;            //

    private Long memberId;      // 用户ID 1家长  2教师 3游客

    private Integer memberType; // 用户类型

    private String registrationId;  //设备ID

    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    public Long getMemberId() {
        return memberId;
    }

    public void setMemberId(Long memberId) {
        this.memberId = memberId;
    }

    public Integer getMemberType() {
        return memberType;
    }

    public void setMemberType(Integer memberType) {
        this.memberType = memberType;
    }

    public String getRegistrationId() {
        return registrationId;
    }

    public void setRegistrationId(String registrationId) {
        this.registrationId = registrationId;
    }
}
