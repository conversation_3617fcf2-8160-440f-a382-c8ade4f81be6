package org.itboys.mobile.entity.mysql;

import org.itboys.mongodb.entity.BaseMySqlEntity;


/**
 * 作者：jiangxiong
 * 日期：2016年9月22日
 * 联系方式 ：<EMAIL>
 * 描述：百睿_摄像头_实体
 */


public class SundayCamera1 extends BaseMySqlEntity{
    private Long guardId;
    private Long bunchId;
    private Integer status;
    private String number;
    private String name;
    private String guardNumber;

    public Long getGuardId() {
        return guardId;
    }

    public void setGuardId(Long guardId) {
        this.guardId = guardId;
    }

    public Long getBunchId() {
        return bunchId;
    }

    public void setBunchId(Long bunchId) {
        this.bunchId = bunchId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getGuardNumber() {
        return guardNumber;
    }

    public void setGuardNumber(String guardNumber) {
        this.guardNumber = guardNumber;
    }
}
