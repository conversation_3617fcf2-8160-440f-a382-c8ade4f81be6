package org.itboys.mobile.entity.mysql.task;

import org.itboys.mobile.service.mysql.SundayOnlineCourseRecordService;

/**
 * @version V1.0
 * @created with IntelliJ IDEA.
 * @Title: entity
 * @author: shumlinmeng
 * @description:
 */
public class DataSourceFactory {

    public static SundayOnlineCourseRecordService courseRecordService = null;

    static{
        ServiceLocator service = ServiceLocator.getInstance();
        courseRecordService = (SundayOnlineCourseRecordService)service.getService("courseRecordService");
    }
}
