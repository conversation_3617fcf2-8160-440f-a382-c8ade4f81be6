package org.itboys.mobile.entity.mysql.online;

import org.itboys.mongodb.entity.BaseMySqlEntity;

/**
 * @version V1.0
 * @created with IntelliJ IDEA.
 * @Title: entity
 * @author: shumlinmeng
 * @description: 在线课程推送记录表
 */
public class SundayOnlineCourseRecordHistory extends BaseMySqlEntity{

    private Long columnId;  // 栏目ID

    private Long courseId;  // 课程ID

    private Integer courseOrder;// 课程课次(废弃)

    private Long memberId;  // 用户ID

    private Integer memberType; // 用户类型

    public Long getColumnId() {
        return columnId;
    }

    public void setColumnId(Long columnId) {
        this.columnId = columnId;
    }

    public Long getCourseId() {
        return courseId;
    }

    public void setCourseId(Long courseId) {
        this.courseId = courseId;
    }

    public Integer getCourseOrder() {
        return courseOrder;
    }

    public void setCourseOrder(Integer courseOrder) {
        this.courseOrder = courseOrder;
    }

    public Long getMemberId() {
        return memberId;
    }

    public void setMemberId(Long memberId) {
        this.memberId = memberId;
    }

    public Integer getMemberType() {
        return memberType;
    }

    public void setMemberType(Integer memberType) {
        this.memberType = memberType;
    }
}
