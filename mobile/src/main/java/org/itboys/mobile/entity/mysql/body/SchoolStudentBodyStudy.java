package org.itboys.mobile.entity.mysql.body;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wordnik.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 宝宝情绪情况
 */
public class SchoolStudentBodyStudy {
    private Long id;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date recordTime;        // 记录时间

    @ApiModelProperty("蒙氏课程：1=积极；2=被动；3=未参加")
    private Integer msValue;
    @ApiModelProperty("学习力课程：1=积极；2=被动；3=未参加")
    private Integer xxlValue;
    @ApiModelProperty("语言课程：1=积极；2=被动；3=未参加")
    private Integer yyValue;
    @ApiModelProperty("SEL社会情感学习课程：1=积极；2=被动；3=未参加")
    private Integer sparkValue;
    @ApiModelProperty("SPARK拓展情况[暂不启用]：1=积极；2=被动；3=未参加")
    private Integer spark2Value;
    @ApiModelProperty("SEL社会情感学习课程：1=积极；2=被动；3=未参加")
    private Integer selValue;
    /**
     * 学生id
     */
    private Long studentId;         //

    private Date createTime;

    private Long creatorId;

    private Integer creatorType;    // 1：家长 2：老师

    private Integer deleted;

    private String remarks;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getRecordTime() {
        return recordTime;
    }

    public void setRecordTime(Date recordTime) {
        this.recordTime = recordTime;
    }

    public Integer getMsValue() {
		return msValue;
	}

	public void setMsValue(Integer msValue) {
		this.msValue = msValue;
	}

	public Integer getXxlValue() {
		return xxlValue;
	}

	public void setXxlValue(Integer xxlValue) {
		this.xxlValue = xxlValue;
	}

	public Integer getYyValue() {
		return yyValue;
	}

	public void setYyValue(Integer yyValue) {
		this.yyValue = yyValue;
	}

	public Integer getSparkValue() {
		return sparkValue;
	}

	public void setSparkValue(Integer sparkValue) {
		this.sparkValue = sparkValue;
	}

	public Integer getSpark2Value() {
		return spark2Value;
	}

	public void setSpark2Value(Integer spark2Value) {
		this.spark2Value = spark2Value;
	}

	public Integer getSelValue() {
		return selValue;
	}

	public void setSelValue(Integer selValue) {
		this.selValue = selValue;
	}

	public Long getStudentId() {
        return studentId;
    }

    public void setStudentId(Long studentId) {
        this.studentId = studentId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public Integer getCreatorType() {
        return creatorType;
    }

    public void setCreatorType(Integer creatorType) {
        this.creatorType = creatorType;
    }

    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }
}
