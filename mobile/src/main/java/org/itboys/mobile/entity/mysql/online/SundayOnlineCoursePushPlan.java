package org.itboys.mobile.entity.mysql.online;

import org.itboys.mobile.entity.mongo.SundayStudent;
import org.itboys.mongodb.entity.BaseMySqlEntity;

import java.util.Date;

/**
 * @version V1.0
 * @created with IntelliJ IDEA.
 * @Title: entity
 * @author: shumlinmeng
 * @description:
 */
public class SundayOnlineCoursePushPlan extends BaseMySqlEntity{

    private Long memberId;      // 用户ID

    private Integer memberType; // 用户类型

    private Long columnId;      // 栏目ID

    private Long courseId;      // 课程ID

    private Date pushTime;      // 更新时间

    private Integer updateStatus;// 更新状态  1:已更新  0:未更新

    private Integer pushType;   //更新类型  0:自动推送 1:手动推送

    private String courseName;  //课程名称

    private SundayStudent student;

    public Long getMemberId() {
        return memberId;
    }

    public void setMemberId(Long memberId) {
        this.memberId = memberId;
    }

    public Integer getMemberType() {
        return memberType;
    }

    public void setMemberType(Integer memberType) {
        this.memberType = memberType;
    }

    public Long getCourseId() {
        return courseId;
    }

    public void setCourseId(Long courseId) {
        this.courseId = courseId;
    }

    public Date getPushTime() {
        return pushTime;
    }

    public void setPushTime(Date pushTime) {
        this.pushTime = pushTime;
    }

    public Integer getUpdateStatus() {
        return updateStatus;
    }

    public void setUpdateStatus(Integer updateStatus) {
        this.updateStatus = updateStatus;
    }

    public Integer getPushType() {
        return pushType;
    }

    public void setPushType(Integer pushType) {
        this.pushType = pushType;
    }

    public Long getColumnId() {
        return columnId;
    }

    public void setColumnId(Long columnId) {
        this.columnId = columnId;
    }

    public String getCourseName() {
        return courseName;
    }

    public void setCourseName(String courseName) {
        this.courseName = courseName;
    }

    public SundayStudent getStudent() {
        return student;
    }

    public void setStudent(SundayStudent student) {
        this.student = student;
    }
}
