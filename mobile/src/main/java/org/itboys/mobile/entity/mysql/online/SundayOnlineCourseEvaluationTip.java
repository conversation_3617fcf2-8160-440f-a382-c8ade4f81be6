package org.itboys.mobile.entity.mysql.online;

import org.itboys.mongodb.entity.BaseMySqlEntity;

/**
 * @version V1.0
 * @created with IntelliJ IDEA.
 * @Title: entity
 * @author: shumlinmeng
 * @description: 点评点赞表
 */
public class SundayOnlineCourseEvaluationTip extends BaseMySqlEntity {

    private Long evaluationId;  //点评ID

    private Long memberId;      //用户ID

    private Long memberType;    //用户类别

    private Integer status;     //状态(0 取消点赞,未点赞,1 点赞)

    public Long getEvaluationId() {
        return evaluationId;
    }

    public void setEvaluationId(Long evaluationId) {
        this.evaluationId = evaluationId;
    }

    public Long getMemberId() {
        return memberId;
    }

    public void setMemberId(Long memberId) {
        this.memberId = memberId;
    }

    public Long getMemberType() {
        return memberType;
    }

    public void setMemberType(Long memberType) {
        this.memberType = memberType;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}
