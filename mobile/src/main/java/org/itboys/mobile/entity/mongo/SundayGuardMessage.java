package org.itboys.mobile.entity.mongo;

import org.itboys.mongodb.entity.BaseMongoEntity;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Index;
import org.mongodb.morphia.annotations.NotSaved;


/**
 * 作者：jiangxiong
 * 日期：2016年9月22日
 * 联系方式 ：<EMAIL>
 * 描述：百睿_园长信箱_实体
 */

@Entity(value = "SundayGuardMessage", noClassnameStored = true)
public class SundayGuardMessage extends BaseMongoEntity {
    private Long groupId;//组ID
    private Long memberId;//用户id
    private String memberName;//用户昵称
    private String memberImage;//用户头像
    private String content;//留言内容
    private Integer contentType;//1文本留言，2图片

    private Long guardId;//园区ID
    @NotSaved
    private String guardName;//园区名称
    private Integer type;//类型。1家长留言，2园长留言
    private Integer status;//0未回复，1已回复




    public SundayGuardMessage() {
    }

    public Long getGroupId() {
        return groupId;
    }

    public void setGroupId(Long groupId) {
        this.groupId = groupId;
    }

    public Long getMemberId() {
        return memberId;
    }

    public void setMemberId(Long memberId) {
        this.memberId = memberId;
    }

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    public String getMemberImage() {
        return memberImage;
    }

    public void setMemberImage(String memberImage) {
        this.memberImage = memberImage;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Integer getContentType() {
        return contentType;
    }

    public void setContentType(Integer contentType) {
        this.contentType = contentType;
    }

    public Long getGuardId() {
        return guardId;
    }

    public void setGuardId(Long guardId) {
        this.guardId = guardId;
    }

    public String getGuardName() {
        return guardName;
    }

    public void setGuardName(String guardName) {
        this.guardName = guardName;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }


}
