package org.itboys.mobile.entity.mysql;

import org.itboys.mongodb.entity.BaseMySqlEntity;

/**
 * <AUTHOR>
 * 日期：2017年4月26日
 * 联系方式 ：<EMAIL>
 * 描述：百睿_用户_实体。
 */


public class SundayParent1 extends BaseMySqlEntity {

    private String name;
    private String phone;
    private String password;
    private Long student_id;
    private String relation;
    private String address;
    private String birthday;
    private String phone_check;
    private String pic;
    private String company;
    private String create_time;
    private String status;
    private String gender;
    private String is_first_login;

    public SundayParent1() {
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public Long getStudent_id() {
        return student_id;
    }

    public void setStudent_id(Long student_id) {
        this.student_id = student_id;
    }

    public String getRelation() {
        return relation;
    }

    public void setRelation(String relation) {
        this.relation = relation;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public String getPhone_check() {
        return phone_check;
    }

    public void setPhone_check(String phone_check) {
        this.phone_check = phone_check;
    }

    public String getPic() {
        return pic;
    }

    public void setPic(String pic) {
        this.pic = pic;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public String getCreate_time() {
        return create_time;
    }

    public void setCreate_time(String create_time) {
        this.create_time = create_time;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getIs_first_login() {
        return is_first_login;
    }

    public void setIs_first_login(String is_first_login) {
        this.is_first_login = is_first_login;
    }
}

