package org.itboys.mobile.entity.mongo.assessment;

import org.itboys.mongodb.entity.BaseMongoEntity;
import org.mongodb.morphia.annotations.Entity;

/**
 * 作者：江兄
 * 日期：2018年7月31日
 * 描述：百睿_评测_结果_实体
 */
@Entity(value = "SundayAssessmentResult", noClassnameStored = true)
public class SundayAssessmentResult extends BaseMongoEntity {
    private Long memberId;//用户ID
    private String memberName;//用户昵称

    private Long assessmentId;//评测主体ID
    private String assessmentName;//评测主体名称
    private Integer type;//类型，1入园评测（3个维度），2注意力评测(6个维度)
    //入园评测结果
    private Double value1;//分值1自立能力及规则感
    private Double value2;//分值2家庭教育一致性
    private Double value3;//分值3情绪状态及安全感
    //注意力评测结果
    private Double value4;//分值4视觉
    private Double value5;//分值5专注力统合
    private Double value6;//分值6精细动作
    private Double value7;//分值7前庭
    private Double value8;//分值8本体
    private Double value9;//分值9听觉
    //专家信息
    private Long  expertId;//专家ID
    private String expertName;//专家姓名
    private String expertImage;//专家头像
    private String expertDesc;//专家简介
    private Double expertValue;//专家评分
    private String content;//综合点评

    public SundayAssessmentResult() {
    }

    public Long getMemberId() {
        return memberId;
    }

    public void setMemberId(Long memberId) {
        this.memberId = memberId;
    }

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    public Long getAssessmentId() {
        return assessmentId;
    }

    public void setAssessmentId(Long assessmentId) {
        this.assessmentId = assessmentId;
    }

    public String getAssessmentName() {
        return assessmentName;
    }

    public void setAssessmentName(String assessmentName) {
        this.assessmentName = assessmentName;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Double getValue1() {
        return value1;
    }

    public void setValue1(Double value1) {
        this.value1 = value1;
    }

    public Double getValue2() {
        return value2;
    }

    public void setValue2(Double value2) {
        this.value2 = value2;
    }

    public Double getValue3() {
        return value3;
    }

    public void setValue3(Double value3) {
        this.value3 = value3;
    }

    public Double getValue4() {
        return value4;
    }

    public void setValue4(Double value4) {
        this.value4 = value4;
    }

    public Double getValue5() {
        return value5;
    }

    public void setValue5(Double value5) {
        this.value5 = value5;
    }

    public Double getValue6() {
        return value6;
    }

    public void setValue6(Double value6) {
        this.value6 = value6;
    }

    public Double getValue7() {
        return value7;
    }

    public void setValue7(Double value7) {
        this.value7 = value7;
    }

    public Double getValue8() {
        return value8;
    }

    public void setValue8(Double value8) {
        this.value8 = value8;
    }

    public Double getValue9() {
        return value9;
    }

    public void setValue9(Double value9) {
        this.value9 = value9;
    }

    public Long getExpertId() {
        return expertId;
    }

    public void setExpertId(Long expertId) {
        this.expertId = expertId;
    }

    public String getExpertImage() {
        return expertImage;
    }

    public void setExpertImage(String expertImage) {
        this.expertImage = expertImage;
    }

    public String getExpertName() {
        return expertName;
    }

    public void setExpertName(String expertName) {
        this.expertName = expertName;
    }

    public String getExpertDesc() {
        return expertDesc;
    }

    public void setExpertDesc(String expertDesc) {
        this.expertDesc = expertDesc;
    }

    public Double getExpertValue() {
        return expertValue;
    }

    public void setExpertValue(Double expertValue) {
        this.expertValue = expertValue;
    }



    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }


}
