package org.itboys.mobile.entity.mysql.online;

import org.itboys.mongodb.entity.BaseMySqlEntity;

import java.util.Date;
import java.util.List;

/**
 * @version V1.0
 * @created with IntelliJ IDEA.
 * @Title: entity
 * @author: shumlinmeng
 * @description: 奖励设置
 */
public class SundayOnlineReward extends BaseMySqlEntity{

    private Long columnId;  //栏目

    private Integer rewardNumber; //第几期

    private String monthNumber; //更新月份

    private Date startTime;     //更新开始日期

    private Date endTime;       //更新截止日期

    private Integer editStatus;     //0正常 1不能编辑

    private List<SundayOnlineRewardStudent> rewardStudents;

    private Integer rewardStudentNum;       //设置奖励人数
    private Integer rewardStudentTotalNum;  //实际人数
    //数据
    private String rewardStudentData;

    //预计更新课程数量
    private Long classNumber;


    public Long getColumnId() {
        return columnId;
    }

    public void setColumnId(Long columnId) {
        this.columnId = columnId;
    }

    public Integer getRewardNumber() {
        return rewardNumber;
    }

    public void setRewardNumber(Integer rewardNumber) {
        this.rewardNumber = rewardNumber;
    }

    public String getMonthNumber() {
        return monthNumber;
    }

    public void setMonthNumber(String monthNumber) {
        this.monthNumber = monthNumber;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public List<SundayOnlineRewardStudent> getRewardStudents() {
        return rewardStudents;
    }

    public void setRewardStudents(List<SundayOnlineRewardStudent> rewardStudents) {
        this.rewardStudents = rewardStudents;
    }

    public String getRewardStudentData() {
        return rewardStudentData;
    }

    public void setRewardStudentData(String rewardStudentData) {
        this.rewardStudentData = rewardStudentData;
    }

    public Integer getEditStatus() {
        return editStatus;
    }

    public void setEditStatus(Integer editStatus) {
        this.editStatus = editStatus;
    }

    public Integer getRewardStudentNum() {
        return rewardStudentNum;
    }

    public void setRewardStudentNum(Integer rewardStudentNum) {
        this.rewardStudentNum = rewardStudentNum;
    }

    public Long getClassNumber() {
        return classNumber;
    }

    public void setClassNumber(Long classNumber) {
        this.classNumber = classNumber;
    }

    public Integer getRewardStudentTotalNum() {
        return rewardStudentTotalNum;
    }

    public void setRewardStudentTotalNum(Integer rewardStudentTotalNum) {
        this.rewardStudentTotalNum = rewardStudentTotalNum;
    }
}
