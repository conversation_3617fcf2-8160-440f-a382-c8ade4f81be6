package org.itboys.mobile.vo;
import java.io.Serializable;
import java.util.List;

/**
 * 查询家长信息vo
 */

public class QueryParentVo implements Serializable {
    /**
     * 家长id
     */
    private Integer openId;

    /**
     * 家长名字
     */
    private String openName;

    /**
     * 与宝贝关系
     */
    private String relation;
    /**
     * 学生ID
     */
    private Integer studentId;

    /**
     * 学生信息
     */
    private List<QueryStudentVo> child;

    public Integer getOpenId() {
        return openId;
    }

    public void setOpenId(Integer openId) {
        this.openId = openId;
    }

    public String getOpenName() {
        return openName;
    }

    public void setOpenName(String openName) {
        this.openName = openName;
    }

    public String getRelation() {
        return relation;
    }

    public void setRelation(String relation) {
        this.relation = relation;
    }

    public Integer getStudentId() {
        return studentId;
    }

    public void setStudentId(Integer studentId) {
        this.studentId = studentId;
    }

    public List<QueryStudentVo> getChild() {
        return child;
    }

    public void setChild(List<QueryStudentVo> child) {
        this.child = child;
    }
}
