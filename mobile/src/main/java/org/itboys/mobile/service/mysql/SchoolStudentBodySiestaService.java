package org.itboys.mobile.service.mysql;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.itboys.commons.CommonConstants;
import org.itboys.commons.ErrorException;
import org.itboys.commons.utils.tools.ListUtil;
import org.itboys.commons.utils.tools.LongUtil;
import org.itboys.mobile.common.SundayCommonConstants;
import org.itboys.mobile.dao.SchoolStudentBodyLeaveMapper;
import org.itboys.mobile.dao.SchoolStudentBodySiestaMapper;
import org.itboys.mobile.entity.mongo.SundayBunch;
import org.itboys.mobile.entity.mongo.SundayGuard;
import org.itboys.mobile.entity.mongo.SundayStudent;
import org.itboys.mobile.entity.mongo.SundayStudentQinziBunch;
import org.itboys.mobile.entity.mysql.body.SchoolStudentBodyLeave;
import org.itboys.mobile.entity.mysql.body.SchoolStudentBodySiesta;
import org.itboys.mobile.service.mongo.web.SundayBunchService;
import org.itboys.mobile.service.mongo.web.SundayGuardService;
import org.itboys.mobile.service.mongo.web.SundayStudentQinziBunchService;
import org.itboys.mobile.service.mongo.web.SundayStudentService;
import org.itboys.param.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 宝宝午睡情况
 */
@Service
public class SchoolStudentBodySiestaService {

    @Autowired
    private SchoolStudentBodySiestaMapper siestaMapper;

    @Autowired
    private SundayStudentService studentService;

    @Autowired
    private SundayGuardService guardService;

    @Autowired
    private SundayBunchService bunchService;

    @Autowired
    private SundayStudentQinziBunchService studentQinziBunchService;

    @Autowired
    private SchoolStudentBodyLeaveMapper leaveMapper;

    /**
     * @param pageSize
     * @param pageNumber
     * @param studentId
     * @return
     */
    public List<Map<String, Object>> selectSiesta(String pageSize, String pageNumber, Long studentId) {
        Map<String, Object> param = new HashMap<>();

        if (StringUtils.isBlank(pageNumber)) {
            pageNumber = SundayCommonConstants.INIT_PAGE_NUMBER;
        }
        Integer row_start = 0;
        if (StringUtils.isBlank(pageSize)) {
            pageSize = SundayCommonConstants.INIT_PAGE_SIZE;
        }


        row_start = (Integer.parseInt(pageNumber) - 1) * Integer.parseInt(pageSize);
        param.put("row_start", row_start);
        param.put("row_size", Integer.parseInt(pageSize));


        //studentId
        param.put("studentId", studentId);

        List<SchoolStudentBodySiesta> siestaList = siestaMapper.select(param);

        SimpleDateFormat sdf = new SimpleDateFormat(CommonConstants.DATE.FORMAT_YYYY_MM_dd);
        List<Map<String, Object>> result = new ArrayList<>();
        for (SchoolStudentBodySiesta siesta : siestaList) {
            Map<String, Object> item = packageSiesta(siesta, sdf);
            result.add(item);
        }

        return result;
    }


    /**
     * @param pageSize
     * @param pageNumber
     * @return
     */
    public PageResult<SchoolStudentBodySiesta> selectBodyDataFull(String pageSize, String pageNumber, Date startDate,
                                                                 Date endDate, List<Long> studentIds) {

        Map<String, Object> param = new HashMap<>();
        if (StringUtils.isBlank(pageNumber)) {
            pageNumber = SundayCommonConstants.INIT_PAGE_NUMBER;
        }
        Integer row_start = 0;
        if (StringUtils.isBlank(pageSize)) {
            pageSize = SundayCommonConstants.INIT_PAGE_SIZE;
        }
        row_start = (Integer.parseInt(pageNumber) - 1) * Integer.parseInt(pageSize);
        param.put("row_start", row_start);
        param.put("row_size", Integer.parseInt(pageSize));
        param.put("startRecordDate", startDate);
        param.put("endRecordDate", endDate);
        param.put("ids", studentIds);

        List<SchoolStudentBodySiesta> bodyDataList = siestaMapper.select(param);
        Long total = (Long) siestaMapper.count(param);
        PageResult<SchoolStudentBodySiesta> pageResult = new PageResult<>();
        pageResult.setData(bodyDataList);
        pageResult.setTotal(total);
        return pageResult;
    }


    /**
     * 无分页
     *
     * @return
     */
    public List<SchoolStudentBodySiesta> selectBodyDataFullNoPage(Date startDate,
                                                                 Date endDate, List<Long> studentIds) {
        Map<String, Object> param = new HashMap<>();

        param.put("startRecordDate", startDate);
        param.put("endRecordDate", endDate);
        param.put("ids", studentIds);

        List<SchoolStudentBodySiesta> bodyDataList = siestaMapper.select(param);

        return bodyDataList;
    }


    /**
     * @param id
     * @param recordTime
     * @param value
     * @param memberId
     * @param remarks
     */
    public void updateBodyDrinkData(Long id, String recordTime, Integer value, Long memberId, String remarks) {
        SchoolStudentBodySiesta siesta = siestaMapper.getById(id);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        if (siesta != null) {
            try {

                siesta.setRecordTime(sdf.parse(recordTime));
                siesta.setValue(value);
                siesta.setCreatorId(memberId);
                siesta.setRemarks(remarks);

            } catch (ParseException e) {
                e.printStackTrace();
            }
            siestaMapper.update(siesta);
        } else {
            throw new ErrorException("查询ID错误!");
        }

    }
    
    public void updateBodySiestaData(Long studentId, String recordTime, Integer value, Long memberId, String remarks) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Map<String,Object> param = new HashMap<>();
        param.put("studentId", studentId);
        param.put("recordTime", sdf.parse(recordTime));
        SchoolStudentBodySiesta siesta = siestaMapper.getBody(param);
        if (siesta != null) {
            try {
                siesta.setRecordTime(sdf.parse(recordTime));
                siesta.setValue(value);
                siesta.setCreatorId(memberId);
                siesta.setRemarks(remarks);

            } catch (ParseException e) {
                e.printStackTrace();
            }
            siestaMapper.update(siesta);
        } else {
            throw new ErrorException("查询ID错误!");
        }

    }

    /**
     * 创建宝宝午睡记录
     * @param recordTime
     * @param studentId
     * @param value
     * @param creatorType
     * @param memberId
     * @param remarks
     */
    public void createBodySiestaData(String recordTime, Long studentId, Integer value, Integer creatorType, Long memberId, String remarks) {
        if (LongUtil.isNull(studentId) || LongUtil.isZreo(studentId)) {
            throw new ErrorException("studentId 不可为空");
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        try {
        	Map<String,Object> param = new HashMap<>();
        	param.put("studentId", studentId);
        	param.put("recordTime", sdf.parse(recordTime));
            SchoolStudentBodySiesta siesta2 = siestaMapper.getBody(param);
            SchoolStudentBodySiesta siesta = new SchoolStudentBodySiesta();
            siesta.setRecordTime(sdf.parse(recordTime));
            siesta.setCreateTime(new Date());
            siesta.setCreatorId(memberId);
            siesta.setCreatorType(creatorType);
            siesta.setRemarks(remarks);
            siesta.setValue(value);
            siesta.setStudentId(studentId);
            if(siesta2 == null) {
            	siestaMapper.insert(siesta);
            }else {
            	siesta.setId(siesta2.getId());
            	siestaMapper.update(siesta);
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
    }

    /**
     * 批量创建宝宝午睡记录
     * @param recordTime
     * @param bunchId
     * @param value
     * @param creatorType
     * @param memberId
     * @param remarks
     */
    public void batchCreateBodySiestaData(String recordTime, Long bunchId, Integer value, Integer creatorType, Long memberId, String remarks) {
        if (LongUtil.isNull(bunchId) || LongUtil.isZreo(bunchId)) {
            throw new ErrorException("bunchId 不可为空");
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Map<String, Object> param = new HashMap<>();

        SundayBunch bunch = bunchService.getById(bunchId);
        List<Long> studentIds = new ArrayList<>();
        if (!CommonConstants.BUNCHTYPE.QINZIBAN.equals(bunch.getClassCategory())) {
            param.put("bunchId", bunchId);
            param.put("stuType", CommonConstants.YES);
            param.put("status", CommonConstants.YES);
            param.put("isDeleted", 0);//是否删除标识。0.否，1是
            List<SundayStudent> students = studentService.list(param);

            if (ListUtil.isNotNull(students)) {
                for (SundayStudent student : students) {
                    studentIds.add(student.getId());
                }
            }
        } else {
            param.put("bunchId", bunchId);
            param.put("state", CommonConstants.NO);
            param.put("isDeleted", 0);//是否删除标识。0.否，1是
            List<SundayStudentQinziBunch> studentQinziBunches = studentQinziBunchService.list(param);
            if (ListUtil.isNotNull(studentQinziBunches)) {
                for (SundayStudentQinziBunch studentQinziBunch : studentQinziBunches) {
                    if (LongUtil.isNotNull(studentQinziBunch.getStudentId())) {
                        studentIds.add(studentQinziBunch.getStudentId());
                    }
                }
            }
        }

        // 批量增加
        if (ListUtil.isNotNull(studentIds)) {
            for (Long studentId : studentIds) {
            	Map<String, Object> param2 = new HashMap<>();
                param2.put("studentId", studentId);
                param2.put("recordTime", recordTime);
                //过滤请假的学生
            	SchoolStudentBodyLeave leave = leaveMapper.getBody(param2); 
            	if(leave == null || leave.getDeleted().compareTo(1) == 0) {
            		SchoolStudentBodySiesta siesta2 = siestaMapper.getBody(param2);
	                try {
	                	SchoolStudentBodySiesta siesta = new SchoolStudentBodySiesta();
	                    siesta.setRecordTime(sdf.parse(recordTime));
	                    siesta.setCreateTime(new Date());
	                    siesta.setCreatorId(memberId);
	                    siesta.setCreatorType(creatorType);
	                    siesta.setRemarks(remarks);
	                    siesta.setValue(value);
	                    siesta.setStudentId(studentId);
	                    if(siesta2 == null) {
	    	                siestaMapper.insert(siesta);
	                    }else {
	                    	siesta.setId(siesta2.getId());
	                    	siestaMapper.update(siesta);
	                    }
	                } catch (ParseException e) {
	                    e.printStackTrace();
	                }
            	}
            }
        }


    }


    /**
     * @param id
     */
    public void deleteBodySiestaData(Long id) {
        siestaMapper.delete(id);
    }

    /**
     * 获取宝宝午睡记录
     * @param recordTime
     * @param studentId
     * @return
     */
    public Map<String, Object> getBodySiestaData(String recordTime, Long studentId) {

        if (LongUtil.isNull(studentId) || LongUtil.isZreo(studentId)) {
            throw new ErrorException("studentId 不可为空");
        }
        Map<String, Object> param = new HashMap<>();
        param.put("studentId", studentId);
        if (StringUtils.isNotBlank(recordTime)) {
            param.put("recordTime", recordTime);
        }
        SchoolStudentBodySiesta siesta = siestaMapper.getBody(param);
        if(siesta == null) return param;
        SimpleDateFormat sdf = new SimpleDateFormat(CommonConstants.DATE.FORMAT_YYYY_MM_dd);
        Map<String, Object> item = packageSiesta(siesta, sdf);
        return item;
    }

    /**
     * 根据主键id获取午睡记录
     * @param id
     * @return
     */
    public Map<String, Object> getById(Long id) {
        SchoolStudentBodySiesta siesta = siestaMapper.getById(id);
        Map<String, Object> item = new HashMap<>();
        if (siesta != null) {
            SimpleDateFormat sdf = new SimpleDateFormat(CommonConstants.DATE.FORMAT_YYYY_MM_dd);
            item = packageSiesta(siesta, sdf);
        }
        return item;
    }

    /**
     * @param ids
     */
    public void deleteBodySiestaDataByIds(String ids) {
        List<Long> listIds = Arrays.asList(ids.split(","))
                .stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
        for (int i = 0; i < listIds.size(); i++) {
            if (LongUtil.isNotZreo(listIds.get(i))) {
                siestaMapper.delete(listIds.get(i));
            }
        }

    }


    public List<Map<String, Object>> getLatestSiestaList(Long studentId) {
        //
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat sdf2 = new SimpleDateFormat("MM-dd");

        //30天前
        Calendar c = Calendar.getInstance();
        c.add(Calendar.DATE, -30);
        Date start = c.getTime();
        String preDay = sdf.format(start);

        //今天
        String now = sdf.format(new Date());

        Map<String, Object> params = new HashMap<>();

        params.put("startTime", preDay);
        params.put("endTime", now);
        params.put("studentId", studentId);
        //获取近30天前
        List<SchoolStudentBodySiesta> siestaList = siestaMapper.selectChartList(params);
        List<SchoolStudentBodySiesta> siestas = new ArrayList<>();
        if (siestaList != null && siestaList.size() > 0) {
            Map<Date, List<SchoolStudentBodySiesta>> siestaMap = siestaList.stream().collect(Collectors.groupingBy(SchoolStudentBodySiesta::getRecordTime));
            for (Map.Entry<Date, List<SchoolStudentBodySiesta>> entry : siestaMap.entrySet()) {
                List<SchoolStudentBodySiesta> list = entry.getValue();
                list = list.stream().sorted(Comparator.comparing(SchoolStudentBodySiesta::getCreateTime).reversed()).collect(Collectors.toList());
                siestas.add(list.get(0));
            }
            siestas = siestas.stream().sorted(Comparator.comparing(SchoolStudentBodySiesta::getRecordTime).reversed()).limit(7).collect(Collectors.toList());
            siestas = siestas.stream().sorted(Comparator.comparing(SchoolStudentBodySiesta::getRecordTime)).collect(Collectors.toList());
        }
        List<Map<String, Object>> items = new ArrayList<>(7);
        for (SchoolStudentBodySiesta siesta : siestas) {
            Map<String, Object> param = new HashMap<>();

            Date time = siesta.getRecordTime();

            String recordTime = sdf2.format(time);
            param.put("recordTime", recordTime);

            param.put("value", siesta.getValue());

            items.add(param);
        }
        return items;
    }


    public Map<String, Object> packageSiesta(SchoolStudentBodySiesta siesta, SimpleDateFormat sdf) {
        Map<String, Object> item = new HashMap<>();
        item.put("id", siesta.getId());
        item.put("recordTime", sdf.format(siesta.getRecordTime()));
        item.put("value", siesta.getValue());
        item.put("remarks", siesta.getRemarks());
        item.put("creatorId", siesta.getCreatorId());
        item.put("creatorType", siesta.getCreatorType());
        return item;
    }

    public Map<String, Object> packageBodyDataFull(SchoolStudentBodySiesta data, SimpleDateFormat sdf) {

        Map<String, Object> item = new HashMap<>();
        item.put("id", data.getId());
        item.put("recordTime", sdf.format(data.getRecordTime()));
        item.put("value", data.getValue());

        item.put("remarks", data.getRemarks());
        item.put("creatorId", data.getCreatorId());
        item.put("creatorType", data.getCreatorType());

        // 根据学生获取所属班级
        SundayStudent student = studentService.findOne(data.getStudentId());
        SundayGuard sundayGuard = guardService.findGuardInfo(student.getGuardId());
        // 获取所有托班、亲子班 为 bunchesName
        String bunchesName = "";
        if (student.getBunchId() != null) {
            SundayBunch sundayBunch = bunchService.findOne(student.getBunchId());
            item.put("bunchName", sundayBunch.getName());
            bunchesName = bunchesName + sundayBunch.getName();
        }
        if (student.getQinziType()!=null && student.getQinziType() == 1) {
            List<SundayBunch> bunches = bunchService.findByStudentId(student.getId());
            if (bunches != null && bunches.size() > 0) {
                for (int i = 0; i < bunches.size(); i++) {
                    bunchesName = bunchesName + " " + bunches.get(i).getName();
                }
            }
        }

        item.put("bunchesName", bunchesName);
        item.put("student", student);
        item.put("guardId", student.getGuardId());
        item.put("guardName", sundayGuard.getName());
        item.put("bunchId", student.getBunchId());
        item.put("studentId", student.getId());
        item.put("studentName", student.getName());

        // 午睡情况名称映射
        if(data.getValue() == 1){
            item.put("valueName","少于半小时");

        }
        if (data.getValue() == 2){
            item.put("valueName","没有午睡");
        }
        if (data.getValue() == 3){
            item.put("valueName","1-2小时");
        }
        return item;
    }
}
