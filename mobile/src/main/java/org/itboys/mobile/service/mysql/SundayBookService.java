package org.itboys.mobile.service.mysql;


import org.apache.commons.lang3.StringUtils;
import org.itboys.admin.dto.lasted.SundayUploadDto;
import org.itboys.admin.service.lasted.SundayUploadService;
import org.itboys.commons.utils.tools.LongUtil;
import org.itboys.framework.resource.ResourceHolder;
import org.itboys.mobile.entity.mysql.SundayBook;
import org.itboys.mysql.service.BaseMySqlService;
import org.itboys.param.PageResult;
import org.itboys.param.ParamUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * @author：jiangxiong 日期：2017年5月8日
 * 联系方式 ：<EMAIL>
 * 描述：百睿_商品_类目_service_web
 * 当前为单规格。如果需要多规格，请自行扩展
 */

@Service
public class SundayBookService extends BaseMySqlService<SundayBook> {
    @Autowired
    private SundayUploadService uploadService;

    @Autowired
    private ResourceHolder resourceHolder;

    /**
     * 分页获取
     * @param request
     * @return
     */
    public PageResult<SundayBook> selectBook(HttpServletRequest request) {
        Map<String, Object> param = ParamUtil.packageMysqlPageParam(request, "name");
        return new PageResult<SundayBook>(super.select(param), super.count(param));
    }

    /**
     * 新增/修改书籍
     * @param book
     * @param
     * @throws Exception
     */
    public void save(SundayBook book) throws Exception {
        if (LongUtil.isNotZreo(book.getId())) {
            super.update(book);
        } else {
            super.insert(book);
        }
    }

    /**
     * 查询商品详情。后台
     * @param bookId
     * @return
     */
    public SundayBook findOne(Long bookId){
        SundayBook book=null;
        if(LongUtil.isNotZreo(bookId)){
            book = super.find(bookId);
        }else{
            book = new SundayBook();
        }

        return book;
    }

}
