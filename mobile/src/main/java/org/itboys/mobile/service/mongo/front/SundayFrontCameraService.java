package org.itboys.mobile.service.mongo.front;


import org.itboys.commons.CommonConstants;
import org.itboys.commons.ErrorException;
import org.itboys.commons.utils.tools.LongUtil;
import org.itboys.mobile.common.SundayCommonConstants;
import org.itboys.mobile.dto.SundayCameraDto;
import org.itboys.mobile.entity.mongo.SundayCamera;
import org.itboys.mobile.entity.mongo.SundayStudent;
import org.itboys.mobile.service.mongo.web.SundayStudentService;
import org.itboys.mongodb.core.MongoDataSource;
import org.itboys.mongodb.service.BaseMongoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 作者：江兄
 * 日期：2018年7月31日
 * 描述：百睿_摄像头_service
 */
@Service
public class SundayFrontCameraService extends BaseMongoService<SundayCamera> {
    @Autowired
    private SundayStudentService studentService;


    @Resource(name = "mobileDs")
    private MongoDataSource mobileDataSource;

    @Override
    protected MongoDataSource getMongoDataSource() {
        return mobileDataSource;
    }

    @Override
    protected Class<SundayCamera> getEntityClass() {
        return SundayCamera.class;
    }



    /**
     * 获取当前班级的设备
     * @param memberId
     * @return
     * @throws Exception
     */
    public List<SundayCameraDto> getData(Long memberId,Long bunchId,Long guardId) throws Exception{

        Map<String,Object> param = new HashMap<String,Object>();

        //2018年12月18日，判断用户类型
        //教师端
        if(LongUtil.isNotZreo(bunchId)){
            param.put("bunchId",bunchId);
        //学生端
        }else{
            SundayStudent student = studentService.getById(memberId);
            if(!student.getStatus().equals(CommonConstants.YES)){
                throw new ErrorException("该学生已离园,无法查看");
            }
            param.put("bunchId",student.getBunchId());
        }
        param.put("status", SundayCommonConstants.YES);
        List<SundayCamera> cameras = super.list(param);
        List<SundayCameraDto> cameraDtos = new ArrayList<>();
        for(SundayCamera camera:cameras){
            SundayCameraDto cameraDto = new SundayCameraDto();
            cameraDto.setId(camera.getId());
            cameraDto.setCreateTime(camera.getCreateTime());
            cameraDto.setCreateDate(camera.getCreateDate());
            cameraDto.setName(camera.getName());
            cameraDto.setNumber(camera.getNumber());
            cameraDto.setStartTime1(camera.getStartTime1()+":00");
            cameraDto.setEndTime1(camera.getEndTime1()+":00");
            cameraDto.setStartTime2(camera.getStartTime2()+":00");
            cameraDto.setEndTime2(camera.getEndTime2()+":00");
            cameraDto.setStartTime3(camera.getStartTime3()+":00");
            cameraDto.setEndTime3(camera.getEndTime3()+":00");
            cameraDtos.add(cameraDto);
        }

        return cameraDtos;
    }

    /**
     * 确认是否有摄像头数据（按园区）
     * @param guardId
     * @return
     */
    public int checkIsCamera(Long guardId){
        if(!LongUtil.isNotZreo(guardId)){
            return 0;
        }
        Map<String,Object> param = new HashMap<String,Object>();
        param.put("guardId",guardId);
        long count = super.count(param);
        if(LongUtil.isNotZreo(count)){
            return  1;
        }else{
            return 0;
        }
    }


}
