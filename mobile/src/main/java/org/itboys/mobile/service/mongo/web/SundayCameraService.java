package org.itboys.mobile.service.mongo.web;


import org.apache.commons.lang3.StringUtils;
import org.itboys.commons.utils.tools.LongUtil;
import org.itboys.mobile.entity.mongo.SundayCamera;
import org.itboys.mobile.service.BaseYuHuaService;
import org.itboys.mongodb.core.MongoDataSource;
import org.itboys.mongodb.service.BaseMongoService;
import org.itboys.param.PageResult;
import org.itboys.param.ParamUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * 作者：江兄
 * 日期：2018年7月31日
 * 描述：百睿_摄像头_service
 */
@Service
public class SundayCameraService extends BaseMongoService<SundayCamera> {
    @Autowired
    private BaseYuHuaService baseYuHuaService;



    @Resource(name = "mobileDs")
    private MongoDataSource mobileDataSource;

    @Override
    protected MongoDataSource getMongoDataSource() {
        return mobileDataSource;
    }

    @Override
    protected Class<SundayCamera> getEntityClass() {
        return SundayCamera.class;
    }

    /**
     * 分页查询
     * @param request
     * @return
     */
    public PageResult<SundayCamera> selectCamera(HttpServletRequest request,String guardIdsStr,String bunchIdsStr){
        Map<String,Object> param = ParamUtil.packageMongoExactParam(request,"guardId","bunchId");
        param = ParamUtil.yuhuaUtil(param,guardIdsStr,bunchIdsStr,false,false);
        Map<String,String> containsparam = ParamUtil.packageMongoVagueParam(request,"number","name");
        PageResult<SundayCamera> pageResult = super.containpageQuery(request,containsparam,param);
        baseYuHuaService.packageGuardAndBunch(pageResult.getData(),false,false);
        return pageResult;
    }

    /**
     * 新增或修改
     * @param camera
     */
    public void saveCamera(SundayCamera camera){
        if(LongUtil.isNotZreo(camera.getId())){

            super.updateExceptEmpty(camera.getId(),camera);
        }else{
            super.save(camera);
        }
    }
    /**
     * 单个查询
     * @param id
     */
    public SundayCamera findOne(Long id,Long guardId){
        SundayCamera camera  = null;
        if(LongUtil.isNotZreo(id)){
            camera = super.getById(id);

        }else{
            camera = new SundayCamera();
            camera.setId(id);
            camera.setGuardId(guardId);
        }
        return camera;
    }






}
