package org.itboys.mobile.service.mongo.web;


import org.itboys.mobile.entity.mongo.assessment.SundayAssessment;
import org.itboys.mobile.entity.mongo.assessment.SundayAssessmentQuestion;
import org.itboys.mongodb.core.MongoDataSource;
import org.itboys.mongodb.service.BaseMongoService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 作者：江兄
 * 日期：2018年7月31日
 * 描述：百睿_评测_问题_service
 */
@Service
public class SundayAssessmentQuestionService extends BaseMongoService<SundayAssessmentQuestion> {



    @Resource(name = "mobileDs")
    private MongoDataSource mobileDataSource;

    @Override
    protected MongoDataSource getMongoDataSource() {
        return mobileDataSource;
    }

    @Override
    protected Class<SundayAssessmentQuestion> getEntityClass() {
        return SundayAssessmentQuestion.class;
    }



}
