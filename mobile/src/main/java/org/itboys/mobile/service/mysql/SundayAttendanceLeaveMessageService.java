package org.itboys.mobile.service.mysql;


import org.itboys.admin.tools.AdminSessionHolder;
import org.itboys.commons.utils.tools.LongUtil;
import org.itboys.mobile.dao.SundayLeaveMapper;
import org.itboys.mobile.dao.SundayLeaveMessageMapper;
import org.itboys.mobile.entity.mongo.SundayLevelMessage;
import org.itboys.mobile.entity.mongo.SundayParent;
import org.itboys.mobile.entity.mongo.SundayStudent;
import org.itboys.mobile.entity.mongo.SundayTeacher;
import org.itboys.mobile.entity.mysql.attendance.SundayAttendanceLeave;
import org.itboys.mobile.entity.mysql.attendance.SundayAttendanceLeaveMessage;

import org.itboys.mobile.service.mongo.SundayLevelMessageService;
import org.itboys.mobile.service.mongo.web.SundayParentService;
import org.itboys.mobile.service.mongo.web.SundayStudentService;
import org.itboys.mobile.service.mongo.web.SundayTeacherService;
import org.itboys.mysql.service.BaseMySqlService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.method.annotation.SessionAttributesHandler;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * @version V1.0
 * @created with IntelliJ IDEA.
 * @Title: entity
 * @author: shumlinmeng
 * @description:
 */
@Service
public class SundayAttendanceLeaveMessageService extends BaseMySqlService<SundayAttendanceLeaveMessage> {
    @Autowired
    private SundayLeaveMapper leaveMapper;

    @Autowired
    private SundayLeaveMessageMapper messageMapper;

    @Autowired
    private SundayStudentService studentService;

    @Autowired
    private SundayTeacherService teacherService;

    @Autowired
    private SundayParentService parentService;



    public void saveMessage2(Long leaveId,String desc){
        SundayAttendanceLeave leave=leaveMapper.find(leaveId);
        SundayStudent sundayStudent=studentService.findOne(leave.getStudentId());
        SundayAttendanceLeaveMessage leaveMessage=new SundayAttendanceLeaveMessage();
        leaveMessage.setLeaveId(leaveId);
        leaveMessage.setScheduleId(leave.getScheduleId());
        leaveMessage.setBunchId(sundayStudent.getBunchId());
        leaveMessage.setLessonId(leave.getLessonId());
        leaveMessage.setLeaveType(Long.parseLong(leave.getLeaveType()+"") );
        leaveMessage.setMemberId(AdminSessionHolder.getAdminUserId());
        leaveMessage.setContent(desc);
        leaveMessage.setMessageType(3);
        messageMapper.save(leaveMessage);

    }
    public   List<SundayAttendanceLeaveMessage>   selectMessageByLeaveId(Long leaveId){
        Map<String,Object> param=new HashMap<>();
        param.put("leaveId",leaveId);

        List<SundayAttendanceLeaveMessage> leaveMessages= messageMapper.selectMessageByLeaveId(param);
        for(SundayAttendanceLeaveMessage leaveMessage:leaveMessages){
            String memberName="";//人名
            String memberImg="";//头像
            if("1".equals(leaveMessage.getMessageType()+"")){//家长
                SundayParent parent= parentService.findOne(leaveMessage.getMemberId());
                memberName=parent.getName();
            }
            if("2".equals(leaveMessage.getMessageType()+"")){//教师
                SundayTeacher teacher=teacherService.findTeacher(leaveMessage.getMemberId());
                memberName=teacher.getName();
            }
            if("3".equals(leaveMessage.getMessageType()+"")){
                memberName="管理员";
            }
            leaveMessage.setMemberName(memberName);
            leaveMessage.setMemberImage(memberImg);
        }

        return leaveMessages;

    }

}
