package org.itboys.mobile.service.mongo.front;


import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.itboys.commons.CommonConstants;
import org.itboys.commons.ErrorException;
import org.itboys.commons.utils.encryption.Digests;
import org.itboys.commons.utils.tools.ListUtil;
import org.itboys.commons.utils.tools.LongUtil;
import org.itboys.mobile.common.Sender;
import org.itboys.mobile.common.SundayCommonConstants;
import org.itboys.mobile.dao.SchoolStudentBodyLeaveMapper;
import org.itboys.mobile.dto.SundayChildrenDto;
import org.itboys.mobile.dto.SundayMemberDto;
import org.itboys.mobile.dto.SundayParentDto;
import org.itboys.mobile.dto.SundayQinziStudentDto;
import org.itboys.mobile.entity.mongo.SundayBunch;
import org.itboys.mobile.entity.mongo.SundayBunchQinzi;
import org.itboys.mobile.entity.mongo.SundayGuard;
import org.itboys.mobile.entity.mongo.SundayMemberHistory;
import org.itboys.mobile.entity.mongo.SundayParent;
import org.itboys.mobile.entity.mongo.SundayStudent;
import org.itboys.mobile.entity.mongo.SundayStudentQinziBunch;
import org.itboys.mobile.entity.mongo.SundayTeacher;
import org.itboys.mobile.entity.mongo.SundayVisitor;
import org.itboys.mobile.entity.mysql.body.SchoolStudentBodyLeave;
import org.itboys.mobile.service.BaseYuHuaService;
import org.itboys.mobile.service.mongo.SundayParAndStuPermissionService;
import org.itboys.mobile.service.mongo.web.SundayBunchQinziService;
import org.itboys.mobile.service.mongo.web.SundayBunchService;
import org.itboys.mobile.service.mongo.web.SundayGuardService;
import org.itboys.mobile.service.mongo.web.SundayMemberHistoryService;
import org.itboys.mobile.service.mongo.web.SundayParentService;
import org.itboys.mobile.service.mongo.web.SundayStudentQinziBunchService;
import org.itboys.mobile.service.mongo.web.SundayStudentService;
import org.itboys.mobile.service.mongo.web.SundayTeacherService;
import org.itboys.mobile.service.mongo.web.SundayVisitorService;
import org.itboys.mobile.util.AgeUtil;
import org.itboys.mobile.util.NameUtil;
import org.itboys.mobile.util.NumberUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;


/**
 * 作者：江兄
 * 日期：2018年7月31日
 * 描述：百睿_用户信息_service
 */
@Service
public class SundayFrontMemberService {

    @Autowired
    private SundayFrontCodeService codeService;

    @Autowired
    private SundayMemberHistoryService historyService;

    @Autowired
    private SundayGuardService guardService;

    @Autowired
    private SundayBunchService bunchService;


    @Autowired
    private SundayParentService parentService;


    @Autowired
    private SundayStudentService studentService;

    @Autowired
    private SundayTeacherService teacherService;

    @Autowired
    private SundayParAndStuPermissionService parAndStuPermissionService;

    @Autowired
    private BaseYuHuaService baseYuHuaService;

    @Autowired
    private SundayVisitorService visitorService;

    @Autowired
    private SundayStudentQinziBunchService studentQinziBunchService;

    @Autowired
    private SundayBunchQinziService qinziService;

    @Autowired
    private Sender sender;

    @Autowired
    private SchoolStudentBodyLeaveMapper leaveMapper;

    /**
     * 登陆(手机号+密码登录或手机号+验证码)
     *
     * @param mobile
     * @param password
     * @param code
     * @param loginType 1,学生（家长）登陆，2教师登陆，3院长登陆，6,游客登录 99，全部类型登陆
     * @param loginWey  1手机号+密码，2,手机号+验证码 3微信登录
     * @param unionid   微信登录时带unionid
     * @return
     */
    public SundayMemberDto login(String mobile, String password, String code, Integer loginType, Integer loginWey, String unionid, Long addVisitor) {
        Map<String, Object> param = new HashMap<String, Object>();
        //校验验证码
        if (loginWey.equals(SundayCommonConstants.login_way_code)) {
            int result_code = codeService.checkCode(mobile, code, SundayCommonConstants.validate_code_type_login);
            if (result_code != 0) {
                throw new ErrorException("验证码错误");
            }
        }


        String token = Digests.md5(new Date().toString());

        SundayMemberDto memberDto = null;
        // 如果addVisitor为1，则进入创建游客并登陆的流程
        if (addVisitor != null && addVisitor == 1) {
            memberDto = new SundayMemberDto();
            SundayVisitor sundayVisitor = new SundayVisitor();
            sundayVisitor.setMobile(mobile);
            String name = NumberUtil.getCodeNumber("游客");
            sundayVisitor.setName(name);
            sundayVisitor.setImage(NameUtil.getRandomLogo());
            sundayVisitor.setUnionid(unionid);
            sundayVisitor.setStatus(CommonConstants.NO);

            sundayVisitor.setToken(token);


            sundayVisitor.setPassword(Digests.md5(SundayCommonConstants.default_password));
            visitorService.save(sundayVisitor);
            memberDto.setId(sundayVisitor.getId());
            memberDto.setUnionid(unionid);
            memberDto.setMobile(mobile);
            memberDto.setLoginType(6);
            return memberDto;
        }
        //如果是微信登录：根据unionid获取用户，
        if (loginWey.equals(SundayCommonConstants.login_way_wx)) {
            // 如果带mobile和code 说明是绑定
            if (StringUtils.isNotBlank(mobile) && StringUtils.isNotBlank(code)) {
                // 先查是否存在家长，再查是否存在教师
                int result_code = codeService.checkCode(mobile, code, SundayCommonConstants.validate_code_type_login);
                if (result_code != 0) {
                    throw new ErrorException("验证码错误");
                }
                param.put("mobile", mobile);
                List<SundayParent> parents = parentService.list(param);
                if (ListUtil.isNotNull(parents)) {
                    SundayParent parent = parents.get(0);
                    if (!parent.getStatus().equals(SundayCommonConstants.NO)) {
                        throw new ErrorException("账号已封禁");
                    }

                    parent.setToken(token);

                    parentService.update(parent.getId(), "unionid", unionid);
                    parentService.update(parent.getId(), "token", parent.getToken());


                    //获取该家长下所有孩子
                    List<Long> childIds = parAndStuPermissionService.getStudentId(parent.getId());
                    param.clear();
                    param.put("id in", childIds);
                    List<SundayStudent> children = studentService.list(param);
                    SundayStudent student = children.get(0);
                    //2018年11月28日，判断园区是否有效(先判断一个孩子)
                    SundayGuard guard = guardService.getById(student.getGuardId());
                    if (!guard.getStatus().equals(SundayCommonConstants.YES)) {
                        throw new ErrorException("园区未开放");
                    }
                    //2018年9月30日，增加显示老师
                    memberDto = packageMemberDto(student, null, children, parent, 1);

                } else {
                    param.put("mobile", mobile);
                    List<SundayTeacher> teachers = teacherService.list(param);
                    if (ListUtil.isNotNull(teachers)) {
                        SundayTeacher teacher = teachers.get(0);
                        if (!teacher.getStatus().equals(SundayCommonConstants.YES)) {
                            throw new ErrorException("教师已离园");
                        }

                        teacher.setToken(token);
                        teacherService.update(teacher.getId(), "unionid", unionid);
                        teacherService.update(teacher.getId(), "token", teacher.getToken());
                        memberDto = packageMemberDto(null, teacher, null, null, 2);
                    } else {
                        // 找游客进行绑定
                        List<SundayVisitor> visitors = visitorService.list(param);
                        if (ListUtil.isNotNull(visitors)) {
                            SundayVisitor visitor = visitors.get(0);

                            visitorService.update(visitor.getId(), "token", token);

                            if (!visitor.getStatus().equals(SundayCommonConstants.NO)) {
                                throw new ErrorException("账号已封禁");
                            }
                            unionid = visitor.getUnionid();
                            mobile = visitor.getMobile();
                            memberDto = new SundayMemberDto();
                            memberDto.setId(visitor.getId());
                            memberDto.setName(visitor.getName());
                            memberDto.setMobile(mobile);

                            memberDto.setToken(token);

                            memberDto.setUnionid(unionid);
                            memberDto.setLoginType(6);

                            memberDto.setImage(visitor.getImage());

                        } else {
                            throw new ErrorException("该手机号没有关联带角色账号");
                        }

                    }
                }
                return memberDto;
            }
            // 根据unionId 获取家长
            param.put("unionid", unionid);
            List<SundayParent> parents = parentService.list(param);
            if (ListUtil.isNotNull(parents)) {
                SundayParent parent = parents.get(0);

                if (!parent.getStatus().equals(SundayCommonConstants.NO)) {
                    throw new ErrorException("账号已封禁");
                }

                parent.setToken(token);

                parentService.update(parent.getId(), "unionid", unionid);
                parentService.update(parent.getId(), "token", parent.getToken());

                //获取该家长下所有孩子
                List<Long> childIds = parAndStuPermissionService.getStudentId(parent.getId());
                param.clear();
                param.put("id in", childIds);
                List<SundayStudent> children = studentService.list(param);
                SundayStudent student = null;
                if (ListUtil.isNotNull(children)) {
                    student = children.get(0);
                } else {
                    throw new ErrorException("该账号下没有宝贝!");
                }

                //2018年11月28日，判断园区是否有效(先判断一个孩子)
                SundayGuard guard = guardService.getById(student.getGuardId());
                if (!guard.getStatus().equals(SundayCommonConstants.YES)) {
                    throw new ErrorException("园区未开放");
                }
                //2018年9月30日，增加显示老师
                memberDto = packageMemberDto(student, null, children, parent, 1);
            } else {
                // 家长不存在则继续查教师
                List<SundayTeacher> teachers = teacherService.list(param);
                if (ListUtil.isNotNull(teachers)) {
                    SundayTeacher teacher = teachers.get(0);
                    if (!teacher.getStatus().equals(SundayCommonConstants.YES)) {
                        throw new ErrorException("教师已离园");
                    }
                    /*//2018年11月28日，判断园区是否有效(先判断一个孩子)
                    SundayGuard guard = guardService.getById(teacher.getGuardId());
                    if(!guard.getStatus().equals(SundayCommonConstants.YES)){
                        throw new ErrorException("园区未开放");
                    }*/

                    teacher.setToken(token);

                    teacherService.update(teacher.getId(), "unionid", unionid);
                    teacherService.update(teacher.getId(), "token", teacher.getToken());


                    memberDto = packageMemberDto(null, teacher, null, null, 2);
                } else {
                    // 教师不存在则继续查游客
                    List<SundayVisitor> visitors = visitorService.list(param);
                    if (ListUtil.isNotNull(visitors)) {
                        SundayVisitor visitor = visitors.get(0);
                        if (visitor != null && !visitor.getStatus().equals(SundayCommonConstants.NO)) {
                            throw new ErrorException("账号已封禁");
                        }

                        visitorService.update(visitor.getId(), "token", token);

                        unionid = visitor.getUnionid();
                        mobile = visitor.getMobile();
                        memberDto = new SundayMemberDto();


                        memberDto.setId(visitor.getId());
                        memberDto.setName(visitor.getName());
                        memberDto.setImage(visitor.getImage());
                    } else {

                        SundayVisitor sundayVisitor = new SundayVisitor();
                        sundayVisitor.setId(0L);
                        sundayVisitor.setMobile(mobile);
                        sundayVisitor.setName(NumberUtil.getCodeNumber("游客"));
                        sundayVisitor.setImage(NameUtil.getRandomLogo());
                        sundayVisitor.setUnionid(unionid);
                        sundayVisitor.setStatus(CommonConstants.NO);
                        sundayVisitor.setToken(token);
                        sundayVisitor.setPassword(Digests.md5(SundayCommonConstants.default_password));
                        visitorService.save(sundayVisitor);
                        memberDto = new SundayMemberDto();
                        memberDto.setId(sundayVisitor.getId());
                        memberDto.setName(sundayVisitor.getName());
                        memberDto.setImage(sundayVisitor.getImage());

                    }
                    memberDto.setToken(token);
                    memberDto.setUnionid(unionid);
                    memberDto.setMobile(mobile);
                    memberDto.setLoginType(6);
                }

            }
        } else//学生家长登陆
            if (loginType == SundayCommonConstants.login_type_student) {
                param.put("mobile", mobile);
                if (loginWey.equals(SundayCommonConstants.login_way_password)) {
                    // 用账号密码查用户会导致新增游客。
//                param.put("password", Digests.md5(password));
                }
                if (loginWey.equals(SundayCommonConstants.login_way_wx)) {
                    param.clear();
                    param.put("unionid", unionid);
                }
                List<SundayParent> parents = parentService.list(param);
                if (ListUtil.isNotNull(parents)) {
                    SundayParent parent = parents.get(0);
                    if (loginWey.equals(SundayCommonConstants.login_way_password) && !Digests.md5(password).equals(parent.getPassword())) {
                        return null;
                    }
                    if (!parent.getStatus().equals(SundayCommonConstants.NO)) {
                        throw new ErrorException("账号已封禁");
                    }

                    if (loginWey.equals(SundayCommonConstants.login_way_wx)) {
                        parentService.update(parent.getId(), "unionid", unionid);
                    }

                    parent.setToken(token);
                    parentService.update(parent.getId(), "token", parent.getToken());
                    //获取该家长下所有孩子
                    List<Long> childIds = parAndStuPermissionService.getStudentId(parent.getId());
                    param.clear();
                    param.put("id in", childIds);
                    List<SundayStudent> children = studentService.list(param);
                    SundayStudent student = children.get(0);
                    //2018年11月28日，判断园区是否有效(先判断一个孩子)
                    SundayGuard guard = guardService.getById(student.getGuardId());
                    if (!guard.getStatus().equals(SundayCommonConstants.YES)) {
                        throw new ErrorException("园区未开放");
                    }
                    //2018年9月30日，增加显示老师
                    memberDto = packageMemberDto(student, null, children, parent, 1);

                }
                //教师登陆
            } else if (loginType.equals(SundayCommonConstants.login_type_teacher)) {
                param.put("mobile", mobile);
                if (loginWey.equals(SundayCommonConstants.login_way_password)) {
//                param.put("password",Digests.md5(password));
                }
                if (loginWey == SundayCommonConstants.login_way_wx) {
                    param.clear();
                    param.put("unionid", unionid);
                }
                List<SundayTeacher> teachers = teacherService.list(param);
                if (ListUtil.isNotNull(teachers)) {
                    SundayTeacher teacher = teachers.get(0);
                    if (loginWey.equals(SundayCommonConstants.login_way_password) && !Digests.md5(password).equals(teacher.getPassword())) {
                        return null;
                    }
                    if (!teacher.getStatus().equals(SundayCommonConstants.YES)) {
                        throw new ErrorException("教师已离园");
                    }
                /*//2018年11月28日，判断园区是否有效(先判断一个孩子)
                SundayGuard guard = guardService.getById(teacher.getGuardId());
                if(!guard.getStatus().equals(SundayCommonConstants.YES)){
                    throw new ErrorException("园区未开放");
                }*/

                    if (loginWey.equals(SundayCommonConstants.login_way_wx)) {
                        teacherService.update(teacher.getId(), "unionid", unionid);
                    }

                    teacher.setToken(token);

                    teacherService.update(teacher.getId(), "token", teacher.getToken());

                    memberDto = packageMemberDto(null, teacher, null, null, 2);
                }
                //全部类型登陆
            } else if (loginType == SundayCommonConstants.login_type_all) {
                //1，先判断学生家长登陆
                memberDto = login(mobile, password, code, SundayCommonConstants.login_type_student, loginWey, unionid, 0L);
                //2，然后判断教师登陆
                if (memberDto == null) {
                    memberDto = login(mobile, password, code, SundayCommonConstants.login_type_teacher, loginWey, unionid, 0L);
                }
                if (memberDto == null) {
                    memberDto = login(mobile, password, code, SundayCommonConstants.login_type_visitor, loginWey, unionid, 0L);
                }
                //游客登录
            } else if (loginType.equals(SundayCommonConstants.login_type_visitor)) {
                memberDto = new SundayMemberDto();
                String name = "";
                param.put("mobile", mobile);
                if (loginWey.equals(SundayCommonConstants.login_way_wx)) {
                    param.clear();
                    param.put("unionid", unionid);
                }
                List<SundayVisitor> visitors = visitorService.list(param);
                if (ListUtil.isNotNull(visitors)) {
                    SundayVisitor visitor = visitors.get(0);
                    if (loginWey.equals(SundayCommonConstants.login_way_password) && !Digests.md5(password).equals(visitor.getPassword())) {
                        return null;
                    }
                    if (!visitor.getStatus().equals(SundayCommonConstants.NO)) {
                        throw new ErrorException("账号已封禁");
                    }
                    visitorService.update(visitor.getId(), "token", token);
                    unionid = visitor.getUnionid();
                    mobile = visitor.getMobile();
                    name = visitor.getName();
                    memberDto.setId(visitor.getId());
                } else {
                    if (StringUtils.isBlank(mobile)) {
                        SundayVisitor sundayVisitor = new SundayVisitor();
                        sundayVisitor.setMobile(mobile);
                        name = NumberUtil.getCodeNumber("游客");
                        sundayVisitor.setName(name);
                        sundayVisitor.setImage(NameUtil.getRandomLogo());
                        sundayVisitor.setToken(token);
                        sundayVisitor.setUnionid(unionid);
                        sundayVisitor.setStatus(CommonConstants.NO);
                        sundayVisitor.setPassword(Digests.md5(SundayCommonConstants.default_password));
                        visitorService.save(sundayVisitor);
                        memberDto.setId(sundayVisitor.getId());
                        memberDto.setImage(sundayVisitor.getImage());
                    } else {
                        throw new ErrorException("用户名或密码错误!");
                    }
                }
                memberDto.setToken(token);
                memberDto.setName(name);

                memberDto.setUnionid(unionid);
                memberDto.setMobile(mobile);
                memberDto.setLoginType(6);
            }
        return memberDto;
    }


    /**
     * 登陆(手机号+密码登录或手机号+验证码)
     *
     * @param mobile
     * @param password
     * @param code
     * @param loginType 1,学生（家长）登陆，2教师登陆，3院长登陆，6,游客登录 99，全部类型登陆
     * @param loginWey  1手机号+密码，2,手机号+验证码 3微信登录
     * @param unionid   微信登录时带unionid
     * @return
     */
    public SundayMemberDto loginV3(String mobile, String password, String code, Integer loginType, Integer loginWey, String unionid, Long addVisitor) {
        Map<String, Object> param = new HashMap<String, Object>();
        //校验验证码
        if (loginWey.equals(SundayCommonConstants.login_way_code)) {
            int result_code = codeService.checkCode(mobile, code, SundayCommonConstants.validate_code_type_login);
            if (result_code != 0) {
                throw new ErrorException("验证码错误");
            }
        }


        String token = Digests.md5(new Date().toString());

        SundayMemberDto memberDto = null;
        if (addVisitor != null && addVisitor == 1) {
            memberDto = new SundayMemberDto();
            SundayVisitor sundayVisitor = new SundayVisitor();
            sundayVisitor.setMobile(mobile);
            String name = NumberUtil.getCodeNumber("游客");
            sundayVisitor.setName(name);
            sundayVisitor.setImage(NameUtil.getRandomLogo());
            sundayVisitor.setUnionid(unionid);
            sundayVisitor.setStatus(CommonConstants.NO);
            sundayVisitor.setToken(token);

            sundayVisitor.setPassword(SundayCommonConstants.default_password);
            visitorService.save(sundayVisitor);
            memberDto.setId(sundayVisitor.getId());
            memberDto.setUnionid(unionid);
            memberDto.setMobile(mobile);
            memberDto.setLoginType(6);
            return memberDto;
        }
        //如果是微信登录：根据unionid获取用户，
        if (loginWey.equals(SundayCommonConstants.login_way_wx)) {
            // 如果带mobile和code 说明是绑定
            if (StringUtils.isNotBlank(mobile) && StringUtils.isNotBlank(code)) {
                // 先查是否存在家长，再查是否存在教师
                int result_code = codeService.checkCode(mobile, code, SundayCommonConstants.validate_code_type_login);
                if (result_code != 0) {
                    throw new ErrorException("验证码错误");
                }
                param.put("mobile", mobile);
                List<SundayParent> parents = parentService.list(param);
                if (ListUtil.isNotNull(parents)) {
                    SundayParent parent = parents.get(0);
                    if (!parent.getStatus().equals(SundayCommonConstants.NO)) {
                        throw new ErrorException("账号已封禁");
                    }

                    parent.setToken(token);

                    parentService.update(parent.getId(), "unionid", unionid);
                    parentService.update(parent.getId(), "token", parent.getToken());


                    //获取该家长下所有孩子
                    List<Long> childIds = parAndStuPermissionService.getStudentId(parent.getId());
                    param.clear();
                    param.put("id in", childIds);
                    List<SundayStudent> children = studentService.list(param);
                    SundayStudent student = children.get(0);
                    //2018年11月28日，判断园区是否有效(先判断一个孩子)
                    SundayGuard guard = guardService.getById(student.getGuardId());
                    if (!guard.getStatus().equals(SundayCommonConstants.YES)) {
                        throw new ErrorException("园区未开放");
                    }
                    //2018年9月30日，增加显示老师
                    memberDto = packageMemberDto(student, null, children, parent, 1);

                } else {
                    param.put("mobile", mobile);
                    List<SundayTeacher> teachers = teacherService.list(param);
                    if (ListUtil.isNotNull(teachers)) {
                        SundayTeacher teacher = teachers.get(0);
                        if (!teacher.getStatus().equals(SundayCommonConstants.YES)) {
                            throw new ErrorException("教师已离园");
                        }
                        /*//2018年11月28日，判断园区是否有效(先判断一个孩子)
                        SundayGuard guard = guardService.getById(teacher.getGuardId());
                        if(!guard.getStatus().equals(SundayCommonConstants.YES)){
                            throw new ErrorException("园区未开放");
                        }*/

                        teacher.setToken(token);

                        teacherService.update(teacher.getId(), "unionid", unionid);
                        teacherService.update(teacher.getId(), "token", teacher.getToken());


                        memberDto = packageMemberDto(null, teacher, null, null, 2);
                    } else {
                        // 找游客进行绑定
                        List<SundayVisitor> visitors = visitorService.list(param);
                        if (ListUtil.isNotNull(visitors)) {
                            SundayVisitor visitor = visitors.get(0);

                            visitorService.update(visitor.getId(), "token", token);

                            if (!visitor.getStatus().equals(SundayCommonConstants.NO)) {
                                throw new ErrorException("账号已封禁");
                            }
                            unionid = visitor.getUnionid();
                            mobile = visitor.getMobile();
                            memberDto = new SundayMemberDto();
                            memberDto.setId(visitor.getId());
                            memberDto.setName(visitor.getName());
                            memberDto.setMobile(mobile);

                            memberDto.setToken(token);

                            memberDto.setUnionid(unionid);
                            memberDto.setLoginType(6);

                            memberDto.setImage(visitor.getImage());

                        } else {
                            throw new ErrorException("该手机号没有关联带角色账号");
                        }

                    }
                }
                return memberDto;
            }
            // 根据unionId 获取家长
            param.put("unionid", unionid);
            List<SundayParent> parents = parentService.list(param);
            if (ListUtil.isNotNull(parents)) {
                SundayParent parent = parents.get(0);

                if (!parent.getStatus().equals(SundayCommonConstants.NO)) {
                    throw new ErrorException("账号已封禁");
                }

                parent.setToken(token);

                parentService.update(parent.getId(), "unionid", unionid);
                parentService.update(parent.getId(), "token", parent.getToken());

                //获取该家长下所有孩子
                List<Long> childIds = parAndStuPermissionService.getStudentId(parent.getId());
                param.clear();
                param.put("id in", childIds);
                List<SundayStudent> children = studentService.list(param);
                SundayStudent student = null;
                if (ListUtil.isNotNull(children)) {
                    student = children.get(0);
                } else {
                    throw new ErrorException("该账号下没有宝贝!");
                }

                //2018年11月28日，判断园区是否有效(先判断一个孩子)
                SundayGuard guard = guardService.getById(student.getGuardId());
                if (!guard.getStatus().equals(SundayCommonConstants.YES)) {
                    throw new ErrorException("园区未开放");
                }
                //2018年9月30日，增加显示老师
                memberDto = packageMemberDto(student, null, children, parent, 1);
            } else {
                // 家长不存在则继续查教师
                List<SundayTeacher> teachers = teacherService.list(param);
                if (ListUtil.isNotNull(teachers)) {
                    SundayTeacher teacher = teachers.get(0);
                    if (!teacher.getStatus().equals(SundayCommonConstants.YES)) {
                        throw new ErrorException("教师已离园");
                    }
                    /*//2018年11月28日，判断园区是否有效(先判断一个孩子)
                    SundayGuard guard = guardService.getById(teacher.getGuardId());
                    if(!guard.getStatus().equals(SundayCommonConstants.YES)){
                        throw new ErrorException("园区未开放");
                    }*/

                    teacher.setToken(token);

                    teacherService.update(teacher.getId(), "unionid", unionid);
                    teacherService.update(teacher.getId(), "token", teacher.getToken());


                    memberDto = packageMemberDto(null, teacher, null, null, 2);
                } else {
                    // 教师不存在则继续查游客
                    List<SundayVisitor> visitors = visitorService.list(param);
                    if (ListUtil.isNotNull(visitors)) {

                        SundayVisitor visitor = visitors.get(0);

                        if (visitor != null && !visitor.getStatus().equals(SundayCommonConstants.NO)) {
                            throw new ErrorException("账号已封禁");
                        }

                        Long v_id = visitor.getId();
                        visitorService.update(v_id, "token", token);

                        unionid = visitor.getUnionid();
                        mobile = visitor.getMobile();
                        memberDto = new SundayMemberDto();


                        memberDto.setId(visitor.getId());
                        memberDto.setName(visitor.getName());
                        memberDto.setImage(visitor.getImage());
                    } else {

                        SundayVisitor sundayVisitor = new SundayVisitor();
                        sundayVisitor.setId(0L);
                        sundayVisitor.setMobile(mobile);
                        sundayVisitor.setName(NumberUtil.getCodeNumber("游客"));
                        sundayVisitor.setImage(NameUtil.getRandomLogo());
                        sundayVisitor.setUnionid(unionid);
                        sundayVisitor.setStatus(CommonConstants.NO);
                        sundayVisitor.setToken(token);
                        sundayVisitor.setPassword(Digests.md5(SundayCommonConstants.default_password));
                        visitorService.save(sundayVisitor);
                        memberDto = new SundayMemberDto();
                        memberDto.setId(sundayVisitor.getId());
                        memberDto.setName(sundayVisitor.getName());
                        memberDto.setImage(sundayVisitor.getImage());

                    }
                    memberDto.setToken(token);
                    memberDto.setUnionid(unionid);
                    memberDto.setMobile(mobile);
                    memberDto.setLoginType(6);
                }

            }
        } else//学生家长登陆
            if (loginType == SundayCommonConstants.login_type_student) {
                param.put("mobile", mobile);
                if (loginWey.equals(SundayCommonConstants.login_way_wx)) {
                    param.clear();
                    param.put("unionid", unionid);
                }
                List<SundayParent> parents = parentService.list(param);
                if (ListUtil.isNotNull(parents)) {
                    SundayParent parent = parents.get(0);
                    if (loginWey.equals(SundayCommonConstants.login_way_password) && !password.equals(parent.getPassword())) {
                        throw new ErrorException("密码错误");
                    }
                    if (!parent.getStatus().equals(SundayCommonConstants.NO)) {
                        throw new ErrorException("账号已封禁");
                    }

                    if (loginWey.equals(SundayCommonConstants.login_way_wx)) {
                        parentService.update(parent.getId(), "unionid", unionid);
                    }

                    parent.setToken(token);
                    parentService.update(parent.getId(), "token", parent.getToken());
                    List<Long> childIds = parAndStuPermissionService.getStudentId(parent.getId());
                    param.clear();
                    param.put("id in", childIds);
                    List<SundayStudent> children = studentService.list(param);
                    SundayStudent student = children.get(0);
                    SundayGuard guard = guardService.getById(student.getGuardId());
                    if (!guard.getStatus().equals(SundayCommonConstants.YES)) {
                        throw new ErrorException("园区未开放");
                    }
                    memberDto = packageMemberDto(student, null, children, parent, 1);

                }
            } else if (loginType.equals(SundayCommonConstants.login_type_teacher)) {
                param.put("mobile", mobile);
                if (loginWey == SundayCommonConstants.login_way_wx) {
                    param.clear();
                    param.put("unionid", unionid);
                }
                List<SundayTeacher> teachers = teacherService.list(param);
                if (ListUtil.isNotNull(teachers)) {
                    SundayTeacher teacher = teachers.get(0);
                    if (loginWey.equals(SundayCommonConstants.login_way_password) && !password.equals(teacher.getPassword())) {
                        throw new ErrorException("密码错误");
                    }
                    if (!teacher.getStatus().equals(SundayCommonConstants.YES)) {
                        throw new ErrorException("教师已离园");
                    }
                    if (loginWey.equals(SundayCommonConstants.login_way_wx)) {
                        teacherService.update(teacher.getId(), "unionid", unionid);
                    }
                    teacher.setToken(token);
                    teacherService.update(teacher.getId(), "token", teacher.getToken());
                    memberDto = packageMemberDto(null, teacher, null, null, 2);
                }
                //全部类型登陆
            } else if (loginType == SundayCommonConstants.login_type_all) {
                //1，先判断学生家长登陆
                memberDto = loginV3(mobile, password, code, SundayCommonConstants.login_type_student, loginWey, unionid, 0L);
                //2，然后判断教师登陆
                if (memberDto == null) {
                    memberDto = loginV3(mobile, password, code, SundayCommonConstants.login_type_teacher, loginWey, unionid, 0L);
                }
                if (memberDto == null) {
                    memberDto = loginV3(mobile, password, code, SundayCommonConstants.login_type_visitor, loginWey, unionid, 0L);
                }
            } else if (loginType.equals(SundayCommonConstants.login_type_visitor)) {
                memberDto = new SundayMemberDto();
                String name = "";
                param.put("mobile", mobile);
                if (loginWey.equals(SundayCommonConstants.login_way_wx)) {
                    param.clear();
                    param.put("unionid", unionid);
                }
                List<SundayVisitor> visitors = visitorService.list(param);
                if (ListUtil.isNotNull(visitors)) {
                    SundayVisitor visitor = visitors.get(0);
                    if (loginWey.equals(SundayCommonConstants.login_way_password) && !password.equals(visitor.getPassword())) {
                        return null;
                    }
                    if (!visitor.getStatus().equals(SundayCommonConstants.NO)) {
                        throw new ErrorException("账号已封禁");
                    }
                    visitorService.update(visitor.getId(), "token", token);
                    unionid = visitor.getUnionid();
                    mobile = visitor.getMobile();
                    name = visitor.getName();
                    memberDto.setId(visitor.getId());
                } else {
                    if (StringUtils.isBlank(mobile)) {
                        SundayVisitor sundayVisitor = new SundayVisitor();
                        sundayVisitor.setMobile(mobile);
                        name = NumberUtil.getCodeNumber("游客");
                        sundayVisitor.setName(name);
                        sundayVisitor.setImage(NameUtil.getRandomLogo());
                        sundayVisitor.setToken(token);
                        sundayVisitor.setUnionid(unionid);
                        sundayVisitor.setStatus(CommonConstants.NO);
                        sundayVisitor.setPassword(Digests.md5(SundayCommonConstants.default_password));
                        visitorService.save(sundayVisitor);
                        memberDto.setId(sundayVisitor.getId());
                        memberDto.setImage(sundayVisitor.getImage());
                    } else {
                        throw new ErrorException("用户名或密码错误!");
                    }
                }
                memberDto.setToken(token);
                memberDto.setName(name);
                memberDto.setUnionid(unionid);
                memberDto.setMobile(mobile);
                memberDto.setLoginType(6);
            }
        return memberDto;
    }


    /**
     * 切换宝贝
     *
     * @param memberId
     * @param childId
     */
    public SundayMemberDto change(Long memberId, Long childId) {
        SundayStudent student = studentService.getById(childId);
        return packageMemberDto(student, null, null, null, SundayCommonConstants.login_type_student);
    }

    /**
     * 获取用户信息
     *
     * @param memberId
     * @param parentId
     * @return
     */
    public SundayMemberDto getDetail(Long memberId, Long parentId) {
        SundayMemberDto memberDto = null;
        Map<String, Object> param = new HashMap<String, Object>();
        //家长端
        if (LongUtil.isNotZreo(parentId)) {
            SundayParent parent = parentService.getById(parentId);
            //获取该家长下所有孩子
            List<Long> childIds = parAndStuPermissionService.getStudentId(parent.getId());
            param.clear();
            param.put("id in", childIds);
            List<SundayStudent> children = studentService.list(param);
            SundayStudent student = children.get(0);
            //2018年9月30日，增加显示老师
            memberDto = packageMemberDto(student, null, children, parent, 1);
            //教工端
        } else {
            SundayTeacher teacher = teacherService.getById(memberId);
            memberDto = packageMemberDto(null, teacher, null, null, 2);
        }
        return memberDto;
    }

    /**
     * 获取用户信息
     *
     * @param memberId
     * @param parentId
     * @return
     */
    public SundayMemberDto getDetailV3(Long memberId, Long parentId, String token) {
        SundayMemberDto memberDto = null;
        Map<String, Object> param = new HashMap<String, Object>();
        //家长端
        if (LongUtil.isNotZreo(parentId)) {
            SundayParent parent = parentService.getById(parentId);

            if (!parent.getToken().equals(token)) {
                throw new ErrorException(CommonConstants.TOKENMSG);
            }

            //获取该家长下所有孩子
            List<Long> childIds = parAndStuPermissionService.getStudentId(parent.getId());
            param.clear();
            param.put("id in", childIds);
            List<SundayStudent> children = studentService.list(param);
            SundayStudent student = children.get(0);
            //2018年9月30日，增加显示老师
            memberDto = packageMemberDto(student, null, children, parent, 1);
            //教工端
        } else {
            SundayTeacher teacher = teacherService.getById(memberId);
            if (!teacher.getToken().equals(token)) {
                throw new ErrorException(CommonConstants.TOKENMSG);
            }
            memberDto = packageMemberDto(null, teacher, null, null, 2);
        }
        return memberDto;
    }


    /**
     * 忘记密码
     *
     * @param mobile
     * @param password
     * @param code
     * @param loginType
     */
    public void forgetPassword(String mobile, String password, String code, Integer loginType) {
        Map<String, Object> param = new HashMap<String, Object>();
        //校验验证码
        int result_code = codeService.checkCode(mobile, code, SundayCommonConstants.validate_code_type_forget_password);
        if (result_code == -1) {
            throw new ErrorException("系统错误");
        } else if (result_code == -2) {
            throw new ErrorException("请输入正确的手机号");
        } else if (result_code == -3) {
            throw new ErrorException("验证码错误");
        }

        SundayMemberDto memberDto = null;
        //学生家长登陆
        if (loginType == SundayCommonConstants.login_type_student) {
            param.put("mobile", mobile);
            List<SundayParent> parents = parentService.list(param);
            if (ListUtil.isNotNull(parents)) {
                parentService.update(parents.get(0).getId(), "password", Digests.md5(password));
            }

            //教师登陆
        } else if (loginType == SundayCommonConstants.login_type_teacher) {
            param.put("mobile", mobile);
            // param.put("userType",SundayCommonConstants.user_type_teacher);
            List<SundayTeacher> teachers = teacherService.list(param);
            if (ListUtil.isNotNull(teachers)) {
                teacherService.update(teachers.get(0).getId(), "password", Digests.md5(password));
            }
            //全部类型登陆
        } else {

            //1，先修改学生家长 param.put("mobile",mobile);
            param.clear();
            param.put("mobile", mobile);
            List<SundayParent> parents = parentService.list(param);
            if (ListUtil.isNotNull(parents)) {
                parentService.update(parents.get(0).getId(), "password", Digests.md5(password));
            }
            //修改老师的
            param.clear();
            param.put("mobile", mobile);
            //param.put("userType",SundayCommonConstants.user_type_teacher);
            List<SundayTeacher> teachers = teacherService.list(param);
            if (ListUtil.isNotNull(teachers)) {
                teacherService.update(teachers.get(0).getId(), "password", Digests.md5(password));
            }
            //修改游客的
            param.clear();
            param.put("mobile", mobile);
            List<SundayVisitor> visitors = visitorService.list(param);
            if (ListUtil.isNotNull(visitors)) {
                teacherService.update(visitors.get(0).getId(), "password", Digests.md5(password));
            }
        }

    }


    /**
     * 忘记密码
     *
     * @param mobile
     * @param password
     * @param code
     * @param loginType
     */
    public void forgetPasswordV3(String mobile, String password, String code, Integer loginType) {
        Map<String, Object> param = new HashMap<String, Object>();
        //校验验证码
        int result_code = codeService.checkCode(mobile, code, SundayCommonConstants.validate_code_type_forget_password);
        if (result_code == -1) {
            throw new ErrorException("系统错误");
        } else if (result_code == -2) {
            throw new ErrorException("请输入正确的手机号");
        } else if (result_code == -3) {
            throw new ErrorException("验证码错误");
        }

        SundayMemberDto memberDto = null;
        //学生家长登陆
        if (loginType.equals(SundayCommonConstants.login_type_student)) {
            param.put("mobile", mobile);
            List<SundayParent> parents = parentService.list(param);
            if (ListUtil.isNotNull(parents)) {
                parentService.update(parents.get(0).getId(), "password", password);
            }

            //教师登陆
        } else if (loginType.equals(SundayCommonConstants.login_type_teacher)) {
            param.put("mobile", mobile);
            // param.put("userType",SundayCommonConstants.user_type_teacher);
            List<SundayTeacher> teachers = teacherService.list(param);
            if (ListUtil.isNotNull(teachers)) {
                teacherService.update(teachers.get(0).getId(), "password", password);
            }
            //全部类型登陆
        } else {

            //1，先修改学生家长 param.put("mobile",mobile);
            param.clear();
            param.put("mobile", mobile);
            List<SundayParent> parents = parentService.list(param);
            if (ListUtil.isNotNull(parents)) {
                parentService.update(parents.get(0).getId(), "password", password);
            }
            //修改老师的
            param.clear();
            param.put("mobile", mobile);
            //param.put("userType",SundayCommonConstants.user_type_teacher);
            List<SundayTeacher> teachers = teacherService.list(param);
            if (ListUtil.isNotNull(teachers)) {
                teacherService.update(teachers.get(0).getId(), "password", password);
            }
            //修改游客的
            param.clear();
            param.put("mobile", mobile);
            List<SundayVisitor> visitors = visitorService.list(param);
            if (ListUtil.isNotNull(visitors)) {
                teacherService.update(visitors.get(0).getId(), "password", password);
            }
        }

    }


    /**
     * 修改密码
     *
     * @param memberId
     * @param password
     * @param oldPassword
     * @param loginType
     */
    public void updatePassword(Long memberId, Long parentId, String password, String oldPassword, Integer loginType) {


        SundayMemberDto memberDto = null;
        //学生家长
        if (loginType == SundayCommonConstants.login_type_student) {
            //1，盘打算
            SundayParent parent = parentService.getById(parentId);
            if (parent != null) {
                //1,判断旧密码是否一致
                if (!Digests.md5(oldPassword).equalsIgnoreCase(parent.getPassword())) {
                    throw new ErrorException("旧密码错误");
                }
                parentService.update(parent.getId(), "password", Digests.md5(password));
            }
            //教师登陆
        } else if (loginType == SundayCommonConstants.login_type_teacher) {
            SundayTeacher teacher = teacherService.getById(memberId);
            if (teacher != null) {
                //1,判断旧密码是否一致
                if (!Digests.md5(oldPassword).equalsIgnoreCase(teacher.getPassword())) {
                    throw new ErrorException("旧密码错误");
                }
                teacherService.update(teacher.getId(), "password", Digests.md5(password));
            }
        } else if (loginType == SundayCommonConstants.login_type_visitor) {
            SundayVisitor visitor = visitorService.getById(memberId);
            if (visitor != null) {
                //1,判断旧密码是否一致
                if (!Digests.md5(oldPassword).equalsIgnoreCase(visitor.getPassword())) {
                    throw new ErrorException("旧密码错误");
                }
                visitorService.update(visitor.getId(), "password", Digests.md5(password));
            }
        }
    }


    /**
     * 修改密码
     *
     * @param memberId
     * @param password
     * @param oldPassword
     * @param loginType
     */
    public void updatePasswordV3(Long memberId, Long parentId, String password, String oldPassword, Integer loginType) {


        SundayMemberDto memberDto = null;
        //学生家长
        if (loginType == SundayCommonConstants.login_type_student) {
            //1，盘打算
            SundayParent parent = parentService.getById(parentId);
            if (parent != null) {
                //1,判断旧密码是否一致
                if (!oldPassword.equalsIgnoreCase(parent.getPassword())) {
                    throw new ErrorException("旧密码错误");
                }
                parentService.update(parent.getId(), "password", password);
            }
            //教师登陆
        } else if (loginType == SundayCommonConstants.login_type_teacher) {
            SundayTeacher teacher = teacherService.getById(memberId);
            if (teacher != null) {
                //1,判断旧密码是否一致
                if (!oldPassword.equalsIgnoreCase(teacher.getPassword())) {
                    throw new ErrorException("旧密码错误");
                }
                teacherService.update(teacher.getId(), "password", password);
            }
        } else if (loginType == SundayCommonConstants.login_type_visitor) {
            SundayVisitor visitor = visitorService.getById(memberId);
            if (visitor != null) {
                //1,判断旧密码是否一致
                if (!oldPassword.equalsIgnoreCase(visitor.getPassword())) {
                    throw new ErrorException("旧密码错误");
                }
                visitorService.update(visitor.getId(), "password", password);
            }
        }
    }

    /**
     * 查询家长详情
     *
     * @param childId
     * @param parentId
     * @return
     */
    public SundayParentDto getParentDetail(Long childId, Long parentId) {
        return packageParentDto(parentService.getById(parentId));
    }

    /**
     * 修改家长详情
     *
     * @param childId
     * @param parentId
     * @param name
     * @param title
     * @param birthDate
     * @param provinceId
     * @param provinceName
     * @param cityId
     * @param cityName
     * @param districtId
     * @param districtName
     * @param address
     * @param email
     * @param company
     * @return
     */
    public SundayParentDto updateParentDetail(Long childId, Long parentId, String name, String title, String birthDate,
                                              Long provinceId, String provinceName, Long cityId, String cityName, Long districtId, String districtName,
                                              String address, String email, String company,
                                              String provinceCode, String cityCode, String districtCode, String communityCode,
                                              String communityName, String houseName, String roomName,String roomCode) {
        SundayParent parent = new SundayParent();
        parent.setId(parentId);
        parent.setName(name);
        parent.setTitle(title);
        parent.setBirthDate(birthDate);
        parent.setProvinceId(provinceId);
        parent.setProvinceName(provinceName);
        parent.setCityId(cityId);
        parent.setCityName(cityName);
        parent.setDistrictId(districtId);
        parent.setDistrictName(districtName);
        parent.setAddress(address);
        parent.setEmail(email);
        parent.setCompany(company);
        parent.setProvinceCode(provinceCode);
        parent.setCityCode(cityCode);
        parent.setDistrictCode(districtCode);
        parent.setCommunityCode(communityCode);
        parent.setCommunityName(communityName);
        parent.setHouseName(houseName);
        parent.setRoomName(roomName);
        parentService.updateExceptEmpty(parentId, parent);
        parentService.update(parentId, "communityCode", communityCode);
        parentService.update(parentId, "communityName", communityName);
        parentService.update(parentId,"houseName",houseName);
        parentService.update(parentId,"roomName",roomName);
        parentService.update(parentId,"roomCode",roomCode);
        return packageParentDto(parent);
    }


    /**
     * 新增家长
     *
     * @param childId
     * @param mobile
     * @param code
     */
    public void addParent(Long childId, String name, String title, String mobile, String code) {
        int result_code = codeService.checkCode(mobile, code, SundayCommonConstants.validate_code_type_parent_add);
        if (result_code == -1) {
            throw new ErrorException("系统错误");
        } else if (result_code == -2) {
            throw new ErrorException("请输入正确的手机号");
        } else if (result_code == -3) {
            throw new ErrorException("验证码错误");
        }
        Map<String, Object> param = new HashMap<String, Object>();
        param.put("mobile", mobile);
        if (LongUtil.isNotZreo(parentService.count(param))) {
            throw new ErrorException("该手机号已经被注册");
        }

        // 判断是否
        SundayParent parent = new SundayParent();
        parent.setName(name);
        parent.setTitle(title);
        parent.setMobile(mobile);
        List<SundayVisitor> sundayVisitors = visitorService.list(param);
        if (ListUtil.isNotNull(sundayVisitors)) {
            SundayVisitor sundayVisitor = sundayVisitors.get(0);
            parent.setUnionid(sundayVisitor.getUnionid());
        } else {
            parent.setUnionid("");
        }
        parent.setPassword(Digests.md5(SundayCommonConstants.default_password));
        parentService.save(parent);
        //做好和宝贝的权限关联
        parAndStuPermissionService.saveParent(childId, parent.getId(), mobile);


    }

    /**
     * 获取宝贝成长历史
     *
     * @param memberId
     */
    public List<Map<String, Object>> getHistory(Long memberId) {
        Map<String, Object> param = new HashMap<>();
        param.put("memberId", memberId);
        List<SundayMemberHistory> histories = historyService.list(param);
        //组装返回结果
        List<Map<String, Object>> results = new ArrayList<>();
        for (SundayMemberHistory history : histories) {
            Map<String, Object> result = new HashMap<>();
            result.put("id", history.getId());
            result.put("createTime", history.getCreateTime());
            result.put("createDate", history.getCreateDate());
            result.put("date", history.getDate());
            result.put("weight", history.getWeight());
            result.put("height", history.getHeight());
            result.put("age", history.getAge());
            results.add(result);
        }
        return results;
    }

    /**
     * 新增或保存宝贝成长历史
     *
     * @param memberId
     * @param date
     * @param height
     * @param weight
     */
    public void saveHistory(Long id, Long memberId, Long childId, Long bunchId, String date, String height, String weight) {

        SundayStudent student = null;
        //日期，判断用户类型
        //教师端
        if (LongUtil.isNotZreo(bunchId)) {
            student = studentService.getById(childId);
            //学生端
        } else {
            student = studentService.getById(memberId);
        }

        String age = AgeUtil.countAge(date, student.getBirthDate());
        SundayMemberHistory history = new SundayMemberHistory();
        history.setMemberId(student.getId());
        history.setDate(date);
        history.setHeight(height);
        history.setWeight(weight);
        history.setAge(age);


        //保存记录
        if (LongUtil.isNotZreo(id)) {
            historyService.updateExceptEmpty(id, history);
        } else {
            historyService.save(history);
        }
        //更新用户实体中的信息

        student.setHeight(height);
        student.setWeight(weight);
        student.setAge(age);
        studentService.updateExceptEmpty(student.getId(), student);
    }


    /**
     * 组装用户DTO
     *
     * @param student
     * @param teacher
     * @param children
     * @param parent
     * @param loginType
     * @return
     */
    public SundayMemberDto packageMemberDto(SundayStudent student, SundayTeacher teacher, List<SundayStudent> children, SundayParent parent, Integer loginType) {
        //  String imgRoot =resourceHolder.getStringValue("imgRoot");
        SundayMemberDto memberDto = new SundayMemberDto();

        //学生登陆
        if (student != null) {

            memberDto.setId(student.getId());
            memberDto.setCreateTime(student.getCreateTime());
            memberDto.setCreateDate(student.getCreateDate());
            memberDto.setName(student.getName());
            //   memberDto.setImage(imgRoot+student.getImage());
            //2019年4月26日，兼容oss和旧图片
            memberDto.setImage(baseYuHuaService.getImageFullPath(student.getImage()));
            memberDto.setMobile(student.getMobile());
            memberDto.setLoginType(loginType);

            //健康信息
            memberDto.setBirthDate(student.getBirthDate());
            memberDto.setAge(student.getAge());
            memberDto.setHeight(student.getHeight());
            memberDto.setWeight(student.getWeight());
            memberDto.setSex(student.getSex());
            memberDto.setStatus(student.getStatus());

            if (student.getQinziType().equals(CommonConstants.YES)) {
                Map<String, Object> _param = new HashMap<>();
                _param.put("studentId", student.getId());
                _param.put("state", CommonConstants.NO);

                List<Long> _bunchIds = new ArrayList<>();

                List<SundayStudentQinziBunch> studentQinziBunches = studentQinziBunchService.list(_param);
                if (ListUtil.isNotNull(studentQinziBunches)) {

                    SundayBunch bunch = bunchService.getById(studentQinziBunches.get(0).getBunchId());
                    memberDto.setBunchId(bunch.getId());
                    memberDto.setBunchName(bunch.getName());
                    memberDto.setGuardId(bunch.getGuardId());
                }

                // SundayQinziStudentDto dto = studentService.getStudentDtoExtra(memberDto.getId());
                // memberDto.setBunchId(dto.getDetails().get(0).getBunchId());
                // memberDto.setBunchName(dto.getDetails().get(0).getBunchName());
                // memberDto.setGuardId(student.getGuardId());
                // List<SundayQinziStudentDto> det = new ArrayList<>();
                // det.add(dto);
                // memberDto.setQinziStudentDtos(det);
                memberDto.setQinziType(student.getQinziType());
            } else {
                memberDto.setQinziType(student.getQinziType());
            }

            if (student.getStuType().equals(CommonConstants.YES)) {
                //园区信息
                SundayBunch bunch = bunchService.getById(student.getBunchId());
                memberDto.setBunchId(student.getBunchId());
                memberDto.setBunchName(bunch.getName());
                memberDto.setBunchType(bunch.getType());
                memberDto.setStuType(student.getStuType());
                memberDto.setGuardId(student.getGuardId());
            } else {
                memberDto.setStuType(student.getStuType());
            }

            //memberDto.setBunchGrade(member.getBunchGrade());
            if (LongUtil.isNull(memberDto.getGuardId())) {
                memberDto.setGuardId(student.getGuardId());
            }

            SundayGuard guard = guardService.getById(student.getGuardId());
            memberDto.setGuardName(guard.getName());
            //家长信息
            if (parent != null) {
                memberDto.setParentId(parent.getId());
                memberDto.setParentName(parent.getName());
                memberDto.setParentTitle(parent.getTitle());
                memberDto.setMobile(parent.getMobile());
                memberDto.setToken(parent.getToken());
            }

            //所属所有孩子信息
            if (ListUtil.isNotNull(children)) {
                List<SundayChildrenDto> childrenDtos = new ArrayList<>();
                List<SundayQinziStudentDto> old = memberDto.getQinziStudentDtos();
                if (ListUtil.isNull(old)) {
                    old = new ArrayList<>();
                }
                for (SundayStudent child : children) {
                    SundayChildrenDto childrenDto = new SundayChildrenDto();
                    childrenDto.setId(child.getId());
                    childrenDto.setCreateTime(child.getCreateTime());
                    childrenDto.setCreateDate(child.getCreateDate());
                    childrenDto.setName(child.getName());
                    if (child.getStuType().equals(CommonConstants.YES)) {
                        SundayBunch _bunch = bunchService.getById(child.getBunchId());
                        childrenDto.setBunchId(_bunch.getId());
                        childrenDto.setBunchName(_bunch.getName());
                        childrenDto.setBunchType(_bunch.getType());
                    }
                       /*if(child.getQinziType().equals(CommonConstants.YES)){
                           if(student.getId()!=child.getId()){

                               SundayQinziStudentDto d = studentService.getStudentDtoExtra(child.getId());
                               old.add(d);
                           }

                           memberDto.setQinziStudentDtos(old);
                       }*/

                    SundayGuard _guard = guardService.getById(memberDto.getGuardId());

                    memberDto.setGuardId(_guard.getId());
                    memberDto.setGuardName(_guard.getName());
                    childrenDtos.add(childrenDto);
                }
                memberDto.setChildren(childrenDtos);
            }
            //2018年11月7日，更新老师信息
            if (student.getStuType().equals(CommonConstants.YES)) {
                Map<String, Object> param = new HashMap<String, Object>();
                param.clear();
                param.put("guardId", student.getGuardId());
                param.put("contains:bunchIds", "," + student.getBunchId() + ",");
                param.put("status", SundayCommonConstants.YES);
                List<SundayTeacher> teachers = teacherService.list(param);
                String teacherName = "";
                if (ListUtil.isNotNull(teachers)) {
                    for (SundayTeacher t : teachers) {
                        teacherName += t.getName() + ",";
                    }
                    teacherName = teacherName.substring(0, teacherName.length() - 1);
                }
                memberDto.setTeacherName(teacherName);
            }

            //老师登陆
        } else if (teacher != null) {
            memberDto.setId(teacher.getId());
            memberDto.setCreateTime(teacher.getCreateTime());
            memberDto.setCreateDate(teacher.getCreateDate());
            memberDto.setName(teacher.getName());
            // memberDto.setImage(imgRoot+teacher.getImage());
            //2019年4月26日，兼容oss和旧图片
            memberDto.setImage(baseYuHuaService.getImageFullPath(teacher.getImage()));
            memberDto.setMobile(teacher.getMobile());
            memberDto.setLoginType(loginType);
            memberDto.setDegree(teacher.getDegree());
            memberDto.setToken(teacher.getToken());

            Map<String, Object> param = new HashMap<String, Object>();
            List<Map<String, Object>> guardAndBunch = new ArrayList<>();

            JSONArray myJsonArray = JSONArray.fromObject(teacher.getDetailJson());
            List<JSONObject> infos = (List) myJsonArray;

            String bunchIdsStr = "";
            SundayGuard guard = new SundayGuard();
            for (JSONObject obj : infos) {
                Long guardId = obj.getLong("guardId");
                SundayGuard _guard = guardService.getById(guardId);

                //选择有班级的园区
                if (obj.has("bunchIds")) {
                    String _bunchIdsStr = obj.getString("bunchIds");
                    if (StringUtils.isNotBlank(_bunchIdsStr.replaceAll(",", "")) && StringUtils.isBlank(bunchIdsStr)) {
                        bunchIdsStr = _bunchIdsStr;
                        guard = _guard;
                        teacher.setGuardId(guardId);
                        teacher.setBunchIds(bunchIdsStr);
                    }
                }

                param.put("guardId", _guard.getId());
                param.put("guardName", _guard.getName());
                guardAndBunch.add(param);
            }
            memberDto.setGuardAndBunch(guardAndBunch);


            memberDto.setGuardId(teacher.getGuardId());
            memberDto.setGuardName(guard.getName());
            //2018年10月15日，教师增加多个班级
            List<Long> bunchIds = new ArrayList<>();
            //Arrays.asList(member.getBunchIds().split(","));
            for (String bunchId : Arrays.asList(teacher.getBunchIds().split(","))) {
                if (StringUtils.isNotEmpty(bunchId)) {
                    bunchIds.add(Long.valueOf(bunchId));
                }
            }
            param.clear();
            param.put("id in", bunchIds);
            List<SundayBunch> bunchArray = bunchService.list(param);
            List<SundayBunchQinzi> bunchQinzis = qinziService.list(param);

            for (SundayBunch bunch : bunchArray) {
                for (SundayBunchQinzi bunchQinzi : bunchQinzis) {
                    if ((bunchQinzi.getBunchId().equals(bunch.getId()))) {
                        bunch.setLessonId(bunchQinzi.getLessonId());
                        break;
                    }
                }
                if (bunch.getGuardId().equals(guard.getId()) && LongUtil.isNull(memberDto.getBunchId())) {
                    memberDto.setBunchId(bunch.getId());
                    memberDto.setBunchName(bunch.getName());
                    memberDto.setBunchType(bunch.getType());

                    if (bunch.getClassCategory().equals(CommonConstants.BUNCHTYPE.QINZIBAN)) {
                        memberDto.setClassCategory(1);
                    } else {
                        memberDto.setClassCategory(0);
                    }

                }
            }


            List<Map<String, Object>> bunches = new ArrayList<>();
            for (SundayBunch bunchArr : bunchArray) {
                Map<String, Object> bunch = new HashMap<String, Object>();
                bunch.put("id", bunchArr.getId());
                bunch.put("bunchName", bunchArr.getName());
                bunch.put("bunchType", bunchArr.getType());
                bunch.put("lessonId", bunchArr.getLessonId());
                if (bunchArr.getClassCategory().equals(CommonConstants.BUNCHTYPE.QINZIBAN)) {
                    bunch.put("classCategory", 1);
                } else {
                    bunch.put("classCategory", 0);
                }
                bunches.add(bunch);
            }
            memberDto.setBunchs(bunches);

            if (LongUtil.isNull(memberDto.getBunchId()) || LongUtil.isZreo(memberDto.getBunchId())) {
                throw new ErrorException("当前账号没有绑定任何班级,无法登录!");
            }

            //不在默认选择

        }
        return memberDto;
    }

    /**
     * 组装家长DTO
     *
     * @param parent
     * @return
     */
    public SundayParentDto packageParentDto(SundayParent parent) {
        SundayParentDto parentDto = new SundayParentDto();
        parentDto.setId(parent.getId());
        parentDto.setCreateTime(parent.getCreateTime());
        parentDto.setCreateDate(parent.getCreateDate());
        parentDto.setMobile(parent.getMobile());
        parentDto.setName(parent.getName());
        parentDto.setTitle(parent.getTitle());
        parentDto.setBirthDate(parent.getBirthDate());
        parentDto.setProvinceId(parent.getProvinceId());
        parentDto.setProvinceName(parent.getProvinceName());
        parentDto.setCityId(parent.getCityId());
        parentDto.setCityName(parent.getCityName());
        parentDto.setDistrictId(parent.getDistrictId());
        parentDto.setDistrictName(parent.getDistrictName());
        parentDto.setAddress(parent.getAddress());
        parentDto.setEmail(parent.getEmail());
        parentDto.setCompany(parent.getCompany());
        parentDto.setToken(parent.getToken());
        parentDto.setProvinceCode(parent.getProvinceCode());
        parentDto.setCityCode(parent.getCityCode());
        parentDto.setDistrictCode(parent.getDistrictCode());
        parentDto.setCommunityCode(parent.getCommunityCode());
        parentDto.setCommunityName(parent.getCommunityName());
        parentDto.setHouseName(parent.getHouseName());
        parentDto.setRoomName(parent.getRoomName());
        parentDto.setRoomCode(parent.getRoomCode());
        return parentDto;
    }


    /**
     * 获取通讯录或者宝贝花名册
     *
     * @param memberId
     */
    public List<Map<String, Object>> getMembers(Long memberId, Long bunchId) {
        SundayTeacher _teacher = null;
        SundayStudent _student = null;

        if (LongUtil.isNotZreo(bunchId)) {
            _teacher = teacherService.getById(memberId);
        } else {
            _student = studentService.getById(memberId);
        }
        Map<String, Object> param = new HashMap<String, Object>();
        List<Map<String, Object>> results = new ArrayList<>();
        if (_student != null) {
            param.clear();
            param.put("status", SundayCommonConstants.YES);
            param.put("guardId", _student.getGuardId());
            param.put("contains:bunchIds", "," + _student.getBunchId() + ",");

            List<Long> _teacherIds = new ArrayList<>();
            if (_student.getStuType().equals(CommonConstants.YES)) {
                param.put("status", SundayCommonConstants.YES);
                param.put("guardId", _student.getGuardId());
                param.put("contains:bunchIds", "," + _student.getBunchId() + ",");
                List<SundayTeacher> teachers = teacherService.list(param);
                if (ListUtil.isNotNull(teachers)) {
                    for (SundayTeacher teacher : teachers) {
                        _teacherIds.add(teacher.getId());
                    }
                }
            }
            if (_student.getQinziType().equals(CommonConstants.YES)) {
                param.clear();
                param.put("studentId", _student.getId());
                param.put("state", CommonConstants.NO);
                List<SundayStudentQinziBunch> qinziBunches = studentQinziBunchService.list(param);
                if (ListUtil.isNotNull(qinziBunches)) {
                    for (SundayStudentQinziBunch studentQinziBunch : qinziBunches) {
                        if (LongUtil.isNotNull(studentQinziBunch.getBunchId())) {
                            param.clear();
                            param.put("contains:bunchIds", "," + studentQinziBunch.getBunchId() + ",");
                            param.put("status", SundayCommonConstants.YES);
                            List<SundayTeacher> _teachers = teacherService.list(param);
                            if (ListUtil.isNotNull(_teachers)) {
                                for (SundayTeacher teacher : _teachers) {
                                    _teacherIds.add(teacher.getId());
                                }
                            }
                        }
                    }

                }
            }
            param.clear();
            List<SundayTeacher> teachers = new ArrayList<>();
            if (ListUtil.isNotNull(_teacherIds)) {
                param.put("id in", _teacherIds);
                param.put("status", SundayCommonConstants.YES);
                teachers = teacherService.list(param);
            } else {
                teachers = teacherService.list(param);
            }
            for (SundayTeacher t : teachers) {
                Map<String, Object> result = new HashMap<String, Object>();
                result.put("id", t.getId());
                result.put("createTime", t.getCreateTime());
                result.put("createDate", t.getCreateDate());
                result.put("name", t.getName());
                result.put("image", baseYuHuaService.getImageFullPath(t.getImage()));
                result.put("mobile", t.getMobile());
                results.add(result);
            }
        } else if (_teacher != null) {
            param.clear();
            param.put("status", SundayCommonConstants.YES);
            param.put("isDeleted", 0);//是否删除标识。0.否，1是

            SundayBunch bunch = bunchService.getById(bunchId);
            if (bunch.getClassCategory().equals(CommonConstants.BUNCHTYPE.QINZIBAN)) {

                Map<String, Object> map = new HashMap<>();
                map.put("bunchId", bunch.getId());

                List<SundayStudentQinziBunch> qinziBunches = studentQinziBunchService.list(map);

                List<Long> _studentIds = new ArrayList<>();
                if (ListUtil.isNotNull(qinziBunches)) {
                    for (SundayStudentQinziBunch bunch1 : qinziBunches) {
                        _studentIds.add(bunch1.getStudentId());
                    }
                }
                param.put("id in", _studentIds);
            } else {
                param.put("bunchId", bunchId);
                param.put("stuType", 1);
            }

            List<SundayStudent> students = studentService.list(param);
            for (SundayStudent student : students) {
                Map<String, Object> result = new HashMap<String, Object>();
                result.put("id", student.getId());
                result.put("createTime", student.getCreateTime());
                result.put("createDate", student.getCreateDate());
                result.put("name", student.getName());
                result.put("image", baseYuHuaService.getImageFullPath(student.getImage()));
                result.put("mobile", student.getMobile());
                result.put("age", student.getAge());
                result.put("height", student.getHeight());
                result.put("weight", student.getWeight());
                int leaved = 0;//当天是否请假 0=未请假；1=已请假
                Map<String, Object> param2 = new HashMap<String, Object>();
                param2.put("studentId", student.getId());
                param2.put("recordTime", new SimpleDateFormat("yyyy-MM-dd").format(new Date()));//当天数据
                SchoolStudentBodyLeave bodyLeave = leaveMapper.getBody(param2);
                if(bodyLeave != null 
                		&& bodyLeave.getDeleted().compareTo(0) == 0) {//存在数据。并且不是删除状态则说明有请假
                	leaved = 1;
                }
                 result.put("leaved", leaved);
                results.add(result);
            }
        }
        return results;
    }

    public void updateDetail(Long memberId, Long bunchId, String image) {
        //日期，判断用户类型
        //教师端
        if (LongUtil.isNotZreo(bunchId)) {
            teacherService.update(memberId, "image", image);
            //学生端
        } else {
            studentService.update(memberId, "image", image);
        }
    }

    /**
     * 获取下属宝贝/或班级信息
     *
     * @param memberId
     * @param bunchId
     * @param parentId
     * @return
     */
    public List<Map<String, Object>> getChildrenInfo(Long memberId, Long bunchId, Long parentId) {
        List<Map<String, Object>> datas = new ArrayList<>();
        Map<String, Object> param = new HashMap<String, Object>();
        //教师登陆
        if (LongUtil.isNotZreo(bunchId)) {

            SundayBunch _bunch = bunchService.getById(bunchId);

            Long guardId = _bunch.getGuardId();


            SundayTeacher teacher = teacherService.getById(memberId);


            //获取当前园区
            JSONArray myJsonArray = JSONArray.fromObject(teacher.getDetailJson());
            List<JSONObject> infos = (List) myJsonArray;
            for (JSONObject info : infos) {
                Long _guardId = info.getLong("guardId");
                String _bunchIds = info.getString("bunchIds");
                if (_guardId.equals(guardId)) {
                    teacher.setGuardId(_guardId);
                    teacher.setBunchIds(_bunchIds);
                    break;
                }
            }

            if (StringUtils.isNotEmpty(teacher.getBunchIds())) {
                List<Long> bunchIds = new ArrayList<>();
                for (String b : teacher.getBunchIds().split(",")) {
                    if (StringUtils.isNotEmpty(b)) {
                        bunchIds.add(Long.valueOf(b));
                    }
                }
                if (ListUtil.isNotNull(bunchIds)) {
                    param.clear();
                    param.put("id in", bunchIds);
                    List<SundayBunch> bunches = bunchService.list(param);
                    for (SundayBunch bunch : bunches) {
                        Map<String, Object> data = new HashMap<String, Object>();
                        data.put("id", bunch.getId());
                        data.put("bunchName", bunch.getName());
                        data.put("bunchType", bunch.getType());
                        datas.add(data);
                    }
                }

            }

            //学生登陆
        } else {
            List<Long> studentIds = parAndStuPermissionService.getStudentId(parentId);
            if (ListUtil.isNotNull(studentIds)) {
                param.clear();
                param.put("id in", studentIds);
                List<SundayStudent> students = studentService.list(param);
                for (SundayStudent student : students) {
                    Map<String, Object> data = new HashMap<String, Object>();
                    data.put("id", student.getId());
                    data.put("name", student.getName());
                    datas.add(data);
                }
            }
        }
        return datas;
    }


    /**
     * 切换园区
     *
     * @param memberId
     * @return
     */
    public List<Map<String, Object>> changeGuard(Long memberId) {
        SundayTeacher teacher = teacherService.getById(memberId);

        List<Map<String, Object>> guards = new ArrayList<>();
        if (StringUtils.isNotBlank(teacher.getDetailJson())) {
            JSONArray myJsonArray = JSONArray.fromObject(teacher.getDetailJson());
            List<JSONObject> infos = (List) myJsonArray;
            for (JSONObject obj : infos) {
                Map<String, Object> param = new HashMap<>();

                //选择有班级的园区
                if (obj.has("bunchIds")) {
                    String _bunchIdsStr = obj.getString("bunchIds");
                    if (StringUtils.isNotBlank(_bunchIdsStr.replaceAll(",", ""))) {
                        Long guardId = obj.getLong("guardId");
                        SundayGuard guard = guardService.getById(guardId);
                        param.put("guardId", guard.getId());
                        param.put("guardName", guard.getName());
                        guards.add(param);
                    }
                }
            }
        }
        return guards;
    }


    /**
     * 切换班级
     *
     * @param memberId
     * @return
     */
    public List<Map<String, Object>> changeBunch(Long memberId, Long guardId) {
        SundayTeacher teacher = teacherService.getById(memberId);


        Map<String, Object> param = new HashMap<>();
        List<Long> bunchIds = new ArrayList<>();
        //Arrays.asList(member.getBunchIds().split(","));
        for (String bunchId : Arrays.asList(teacher.getBunchIds().split(","))) {
            if (StringUtils.isNotEmpty(bunchId)) {
                bunchIds.add(Long.valueOf(bunchId));
            }
        }
        param.put("id in", bunchIds);
        param.put("guardId", guardId);
        List<Map<String, Object>> bunches = new ArrayList<>();

        List<SundayBunch> bunchArray = bunchService.list(param);
        param.clear();
        param.put("bunchId in", bunchIds);
        List<SundayBunchQinzi> bunchQinzis = qinziService.list(param);

        for (SundayBunch bunch : bunchArray) {
            for (SundayBunchQinzi bunchQinzi : bunchQinzis) {
                if ((bunchQinzi.getBunchId().equals(bunch.getId()))) {
                    bunch.setLessonId(bunchQinzi.getLessonId());
                    break;
                }
            }
        }

        for (SundayBunch bunchArr : bunchArray) {
            Map<String, Object> bunch = new HashMap<String, Object>();
            bunch.put("id", bunchArr.getId());
            bunch.put("bunchName", bunchArr.getName());
            bunch.put("bunchType", bunchArr.getType());
            bunch.put("lessonId", bunchArr.getLessonId());
            if (bunchArr.getClassCategory().equals(CommonConstants.BUNCHTYPE.QINZIBAN)) {
                bunch.put("classCategory", 1);
            } else {
                bunch.put("classCategory", 0);
            }
            bunches.add(bunch);
        }
        return bunches;
    }

    public List<SundayChildrenDto> changeChildren(Long parentId) {
        Map<String, Object> param = new HashMap<>();
        //获取该家长下所有孩子
        List<Long> childIds = parAndStuPermissionService.getStudentId(parentId);
        param.clear();
        param.put("id in", childIds);
        List<SundayStudent> children = studentService.list(param);
        List<SundayChildrenDto> childrenDtos = new ArrayList<>();
        //所属所有孩子信息
        if (ListUtil.isNotNull(children)) {

            for (SundayStudent child : children) {
                SundayChildrenDto childrenDto = new SundayChildrenDto();
                childrenDto.setId(child.getId());
                childrenDto.setCreateTime(child.getCreateTime());
                childrenDto.setCreateDate(child.getCreateDate());
                childrenDto.setName(child.getName());
                childrenDtos.add(childrenDto);
            }
        }
        return childrenDtos;
    }
}
