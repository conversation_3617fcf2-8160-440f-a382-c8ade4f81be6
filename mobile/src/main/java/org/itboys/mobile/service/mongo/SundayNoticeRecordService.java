package org.itboys.mobile.service.mongo;


import org.itboys.mobile.entity.mongo.SundayNoticeRecord;
import org.itboys.mobile.service.BaseYuHuaService;
import org.itboys.mongodb.core.MongoDataSource;
import org.itboys.mongodb.service.BaseMongoService;
import org.itboys.param.PageResult;
import org.itboys.param.ParamUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Map;


/**
 * 作者：江兄
 * 日期：2018年7月31日
 * 描述：百睿_通知_记录_service
 */
@Service
public class SundayNoticeRecordService extends BaseMongoService<SundayNoticeRecord> {

    @Autowired
    private BaseYuHuaService baseYuHuaService;

    @Resource(name = "mobileDs")
    private MongoDataSource mobileDataSource;

    @Override
    protected MongoDataSource getMongoDataSource() {
        return mobileDataSource;
    }

    @Override
    protected Class<SundayNoticeRecord> getEntityClass() {
        return SundayNoticeRecord.class;
    }

    /**
     * 分页查询
     * @param request
     * @return
     */
    public PageResult<SundayNoticeRecord> selectNoticeRecord(HttpServletRequest request,Long noticeId){
        Map<String,Object> param = ParamUtil.packageMongoExactParam(request,"");
        param.put("noticeId",noticeId);
        Map<String,String> containsparam = ParamUtil.packageMongoVagueParam(request,"memberName");
        PageResult<SundayNoticeRecord> pageResult = super.containpageQuery(request,containsparam,param);
        baseYuHuaService.packageGuardAndBunch(pageResult.getData(),false,false);
        return pageResult;
    }

    /**
     * 新增记录
     * @param noticeId
     * @param memberId
     * @param memberName
     */
    public void saveNoticeRecord(Long noticeId,Long memberId,String memberName,Long guardId,Long bunchId)  {
        SundayNoticeRecord record = new SundayNoticeRecord();
        record.setNoticeId(noticeId);
        record.setMemberId(memberId);
        record.setMemberName(memberName);
        record.setGuardId(guardId);
        record.setBunchId(bunchId);
        super.save(record);
    }


    public long countNoBrowseNotice(HttpServletRequest request,Long memberId,Long bunchId){
        long count = 0;

        return count;
    }

}
