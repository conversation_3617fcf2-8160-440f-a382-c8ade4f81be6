package org.itboys.mobile.service.mongo;


import org.itboys.commons.utils.tools.ListUtil;
import org.itboys.commons.utils.tools.LongUtil;
import org.itboys.framework.resource.ResourceHolder;
import org.itboys.mobile.common.SundayCommonConstants;
import org.itboys.mobile.entity.mongo.SundayStudent;
import org.itboys.mobile.entity.mongo.dynamic.SundayDynamicOperate;
import org.itboys.mobile.entity.mongo.dynamic.SundayDynamicTip;
import org.itboys.mobile.service.BaseYuHuaService;
import org.itboys.mobile.service.mongo.web.SundayStudentService;
import org.itboys.mongodb.core.MongoDataSource;
import org.itboys.mongodb.service.BaseMongoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * 作者：江兄
 * 日期：2018年7月31日
 * 描述：百睿_班级圈_消息提示_service
 */
@Service
public class SundayDynamicTipService extends BaseMongoService<SundayDynamicTip> {

    @Autowired
    private SundayDynamicOperateService operateService;
    @Autowired
    private SundayStudentService studentService;

    @Autowired
    private ResourceHolder resourceHolder;

    @Autowired
    private BaseYuHuaService baseYuHuaService;


    @Resource(name = "mobileDs")
    private MongoDataSource mobileDataSource;

    @Override
    protected MongoDataSource getMongoDataSource() {
        return mobileDataSource;
    }

    @Override
    protected Class<SundayDynamicTip> getEntityClass() {
        return SundayDynamicTip.class;
    }



    /**
     * 生成提示记录（一条动态所有点赞、评论、回复的人）,点赞、评论、回复时生成
     * @param dynamicId
     * @param memberId
     */
    public void generateTip(Long dynamicId,Long memberId,Long bunchId){
        Map<String,Object> param = new HashMap<String,Object>();
        param.put("dynamicId",dynamicId);
        List<SundayDynamicOperate> operates = operateService.list(param);
        List<SundayDynamicTip> tips = new ArrayList<>();
        Set<Long> memberIds = new HashSet<>();//用户ID去重复
        for(SundayDynamicOperate operate:operates){
            memberIds.add(operate.getMemberId());
        }
        for(Long _memberId:memberIds){
            for(SundayDynamicOperate operate:operates){
                if(LongUtil.isNotZreo(operate.getMemberId())&& _memberId.equals(operate.getMemberId())&& !_memberId.equals(memberId)){
                    tips.add(new SundayDynamicTip(dynamicId,operate.getMemberId(),bunchId,operate.getMemberImage(), SundayCommonConstants.NO));
                    break;
                }
            }
        }
        if(ListUtil.isNotNull(tips)){
            super.batchSaveWithoutLogin(tips);
        }
    }

    /**
     * 清除提示记录（以提示的动态ID）
     * @param dynamicIds
     * @param memberId
     */
    public void clearTip(List<Long> dynamicIds, Long memberId,Long bunchId){
        if(ListUtil.isNotNull(dynamicIds)){
            Map<String,Object> param = new HashMap<String,Object>();
            param.put("dynamicId in",dynamicIds);
            param.put("memberId",memberId);
            param.put("bunchId",bunchId);
            List<SundayDynamicTip> tips = super.list(param);
            for(SundayDynamicTip tip:tips){
                super.update(tip.getId(),"status",SundayCommonConstants.YES);
            }
        }

    }

    /**
     * 获取最新的消息记录
     * @param memberId
     * @return 数量，头像，动态ID集合
     */
    public Map<String,Object> getTip(Long memberId,Long bunchId){
        Map<String,Object> result = new HashMap<String,Object>();
        int num = 0;
        String image = "";
        String dynamicIds="";
        Map<String,Object> param = new HashMap<String,Object>();
        param.put("memberId",memberId);
        //教师登陆
        if(LongUtil.isNotZreo(bunchId)){
            param.put("bunchId",bunchId);
        }else{
        //学生登陆
            SundayStudent student = studentService.getById(memberId);
            param.put("bunchId",student.getBunchId());
        }

        param.put("status",SundayCommonConstants.NO);
        param.put("sort","ct");
        param.put("order","DESC");
        List<SundayDynamicTip> tips = super.list(param);

        if(ListUtil.isNotNull(tips)){
            num=tips.size();

            //image=resourceHolder.getStringValue("imgRoot")+tips.get(0).getImage();

            for(SundayDynamicTip tip:tips){
                dynamicIds+=tip.getDynamicId()+",";
            }
            dynamicIds=dynamicIds.substring(0,dynamicIds.length()-1);
        }
        result.put("num",num);
        result.put("image",baseYuHuaService.getImageFullPath(image));
        result.put("dynamicIds",dynamicIds);
        result.put("info","字段说明,num:消息数量，image：头像，dynamicIds：关联的动态ID集合。直接使用");
        return result;
    }




}
