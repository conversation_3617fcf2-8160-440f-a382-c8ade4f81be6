package org.itboys.mobile.service.mysql;


import org.itboys.admin.tools.AdminSessionHolder;
import org.itboys.commons.utils.tools.LongUtil;
import org.itboys.mobile.dao.SundayOnlineCourseEvaluationMapper;
import org.itboys.mobile.dao.SundayOnlineCourseEvaluationReplyMapper;
import org.itboys.mobile.entity.mongo.SundayParent;
import org.itboys.mobile.entity.mongo.SundayTeacher;
import org.itboys.mobile.entity.mysql.SundayUser1;
import org.itboys.mobile.entity.mysql.online.SundayOnlineCourseEvaluation;
import org.itboys.mobile.entity.mysql.online.SundayOnlineCourseEvaluationReply;
import org.itboys.mobile.service.mongo.web.SundayParentService;
import org.itboys.mobile.service.mongo.web.SundayTeacherService;
import org.itboys.mysql.service.BaseMySqlService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * @version V1.0
 * @created with IntelliJ IDEA.
 * @Title: entity
 * @author: shumlinmeng
 * @description:
 */
@Service
public class SundayOnlineCourseEvaluationReplyService extends BaseMySqlService<SundayOnlineCourseEvaluationReply>{

    @Autowired
    private SundayOnlineCourseEvaluationReplyMapper replyMapper;

    @Autowired
    private SundayParentService parentService;
    @Autowired
    private SundayTeacherService teacherService;

    @Override
    public void insert(SundayOnlineCourseEvaluationReply reply) {
        reply.setMemberType(3L);//设置成后台
        reply.setMemberName("管理员");
        replyMapper.insert(reply);
    }
    public List<SundayOnlineCourseEvaluationReply> findByEvaluationId(Long id) {
        return replyMapper.findByEvaluationId(id);
    }

    public void saveReply(Long memberId,String desc,Long evaluationId,Long memberType,String memberName){
        SundayOnlineCourseEvaluationReply reply=new SundayOnlineCourseEvaluationReply();
        reply.setCreator(memberId);
        reply.setDesc(desc);
        reply.setEvaluationId(evaluationId);

        if(memberType.equals(1L)){
            reply.setMemberName(memberName+"家长");
        }else if (memberType.equals(2L)){
            reply.setMemberName(memberName);
        }else if(memberType.equals(3L)){
            reply.setMemberName(memberName);
        }else{
            reply.setMemberName(memberName);
        }
        reply.setMemberType(memberType);
        replyMapper.insert(reply);
    }

    public List<SundayOnlineCourseEvaluationReply> addReplyUser(List<SundayOnlineCourseEvaluationReply> list){
//增加memberName字段删除了这一操作
//        for(SundayOnlineCourseEvaluationReply reply:list){
//            SundayUser1 user1= new SundayUser1();
//            user1.setUsername("");
//            if("3".equals(reply.getMemberType()+"")){
//                user1.setUsername("管理员");
//            }
//            if("2".equals(reply.getMemberType()+"")){//2教师
//                SundayTeacher teacher=teacherService.findTeacher(reply.getCreator());
//                if(teacher!=null){
//                    user1.setUsername(teacher.getName());
//                }
//            }
//            if("1".equals(reply.getMemberType()+"")){//1家长
//                SundayParent parent=parentService.findOne(reply.getCreator());
//                if(parent!=null){
//                    user1.setUsername(parent.getName());
//                }
//            }
//            reply.setUser(user1);
//        }

        return  list;
    }

}
