package org.itboys.mobile.service.mysql;


import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;

import org.itboys.commons.utils.tools.LongUtil;
import org.itboys.framework.resource.ResourceHolder;
import org.itboys.mobile.dao.SundayReportJbDetailMapper;
import org.itboys.mobile.entity.mysql.report.SundayReportJbDetail;
import org.itboys.mysql.service.BaseMySqlService;
import org.itboys.param.PageResult;
import org.itboys.param.ParamUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 
 * 周报
 * <AUTHOR>
 *
 */
@Service
public class SundayReportJbDetailService extends BaseMySqlService<SundayReportJbDetail> {
    
    @Autowired
    private ResourceHolder resourceHolder;
    
	@Autowired
    private SundayReportJbDetailMapper sundayReportJbDetailMapper;

    /**
     * 分页获取
     * @param request
     * @return
     */
    public PageResult<SundayReportJbDetail> selectJb(HttpServletRequest request, Integer flag) {
        Map<String, Object> param = ParamUtil.packageMysqlPageParam(request, "code", "status", "bunchId", "studentId", "guardId", "startDate", "endDate");
        if(flag.compareTo(1) == 0 ) { //当flag==1时，为家长端
        	param.put("status", 4);
        }
        if(flag.compareTo(2) == 0 ) { //当flag==2时，审核端
        	param.remove("bunchId");
        	param.remove("studentId");
        	param.remove("guardId");
        	param.put("statusList", Arrays.asList(1,2,3,4));
        }
        return new PageResult<SundayReportJbDetail>(super.select(param), super.count(param));
    }
    
    public PageResult<SundayReportJbDetail> selectJbApprove(HttpServletRequest request, List<Long> bunchIdList) {
        Map<String, Object> param = ParamUtil.packageMysqlPageParam(request, "code", "status");
        param.put("statusList", Arrays.asList(1,2,3,4));
        param.put("bunchIdList", bunchIdList);
    	
        return new PageResult<SundayReportJbDetail>(super.select(param), super.count(param));
    }

    /**
     * 
     * @param sundayReportJbDetail
     * @throws Exception
     */
    public void save(SundayReportJbDetail sundayReportJbDetail) throws Exception {
        if (LongUtil.isNotZreo(sundayReportJbDetail.getId())) {
        	super.update(sundayReportJbDetail);
        } else {
        	sundayReportJbDetail.setId(null);
        	sundayReportJbDetail.setStatus(0);
        	super.insert(sundayReportJbDetail);
        }
    }

    /**
     * 
     * @param id
     * @return
     */
    public SundayReportJbDetail findOne(Long id){
    	SundayReportJbDetail zb=null;
        if(LongUtil.isNotZreo(id)){
        	zb = super.find(id);
        }
        return zb;
    }
    
    /**
     * 
     * @param ids
     */
    public void deleteByIds(String ids){
        List<Long> listIds = Arrays.asList(ids.split(","))
                .stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
        for (int i= 0;i<listIds.size();i++){
            if(LongUtil.isNotZreo(listIds.get(i))){
            	super.delete(listIds.get(i));
            }
        }
    }
    
    /**
     * 通过zbId和bunchId获取信息
     * @param sundayReportJbDetail
     * @return
     */
    public SundayReportJbDetail findOneByJbIdAndBunchId(SundayReportJbDetail sundayReportJbDetail) {
    	
    	return sundayReportJbDetailMapper.findOneByJbIdAndBunchId(sundayReportJbDetail);
    }
    
    public boolean batchUpdateStatus(SundayReportJbDetail sundayReportJbDetail) {
    	
    	return sundayReportJbDetailMapper.batchUpdateStatus(sundayReportJbDetail) > 0 ? true : false;
    }
    
    public List<SundayReportJbDetail> selectList(SundayReportJbDetail sundayReportJbDetail){
    	
    	return sundayReportJbDetailMapper.selectList(sundayReportJbDetail);
    }
    
    public void updateById(SundayReportJbDetail sundayReportJbDetail) {
    	
    	super.update(sundayReportJbDetail);
    }
    
    public List<SundayReportJbDetail> selectNoPage(Map<String,Object> param){
    	
    	return sundayReportJbDetailMapper.selectNoPage(param);
    }
}
