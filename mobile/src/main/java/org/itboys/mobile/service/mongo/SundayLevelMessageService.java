package org.itboys.mobile.service.mongo;


import org.itboys.admin.entity.lasted.SystemUser;
import org.itboys.admin.tools.AdminSessionHolder;
import org.itboys.commons.utils.time.TimeUtils;
import org.itboys.commons.utils.tools.LongUtil;
import org.itboys.framework.resource.ResourceHolder;
import org.itboys.mobile.common.SundayCommonConstants;
import org.itboys.mobile.dto.SundayLevelMessageDto;
import org.itboys.mobile.entity.mongo.*;
import org.itboys.mobile.service.BaseYuHuaService;
import org.itboys.mobile.service.mongo.web.SundayGuardService;
import org.itboys.mobile.service.mongo.web.SundayStudentService;
import org.itboys.mobile.service.mongo.web.SundayTeacherService;
import org.itboys.mobile.service.mongo.web.SundayUserService;
import org.itboys.mongodb.core.MongoDataSource;
import org.itboys.mongodb.service.BaseMongoService;
import org.itboys.param.PageResult;
import org.itboys.param.ParamUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * 作者：江兄
 * 日期：2018年7月31日
 * 描述：百睿_请假留言_service
 */
@Service
public class SundayLevelMessageService extends BaseMongoService<SundayLevelMessage> {

    @Autowired
    private SundayStudentService studentService;
    @Autowired
    private SundayTeacherService teacherService;

    @Autowired
    private SundayGuardService guardService;

    @Autowired
    private SundayGuardMessageGroupService groupService;

    @Autowired
    private ResourceHolder resourceHolder;

    @Autowired
    private BaseYuHuaService baseYuHuaService;

    @Autowired
    private SundayUserService userService;


    @Resource(name = "mobileDs")
    private MongoDataSource mobileDataSource;

    @Override
    protected MongoDataSource getMongoDataSource() {
        return mobileDataSource;
    }

    @Override
    protected Class<SundayLevelMessage> getEntityClass() {
        return SundayLevelMessage.class;
    }

    /**
     * 请假留言
     * @param memberId
     * @param content
     * @param type
     */
    public void saveMessage(Long memberId,Long levelId,String content,Integer type){
        SundayLevelMessage message = new SundayLevelMessage();
        message.setLevelId(levelId);
        message.setType(type);
        message.setContent(content);
        message.setMemberId(memberId);
        if(type == 1){
            SundayStudent student = studentService.getById(memberId);
            message.setMemberName(student.getName());
            message.setMemberImage(student.getImage());
        }else if(type == 2 ){
            SundayTeacher teacher = teacherService.getById(memberId);
            message.setMemberName(teacher.getName());
            message.setMemberImage(teacher.getImage());
        }else if(type == 3){
            SystemUser loginUser = userService.getById(AdminSessionHolder.getAdminUserId());
            message.setMemberName(loginUser.getName());
            message.setMemberImage(resourceHolder.getStringValue("adminRoot")+"/yuhua/admin/img/avatar.png");
        }
        super.save(message);
    }



    /**
     * 获取请假留言
     */
    public List<SundayLevelMessageDto> getMessage(Long levelId){
        Map<String,Object> param = new HashMap<String,Object>();
        param.put("levelId",levelId);
        param.put("sort","ct");
        param.put("order","ASC");
        return  packageBatch(super.list(param));
    }
    /**
     * 组装DTO
     * @param messages
     * @return
     */
    public List<SundayLevelMessageDto> packageBatch(List<SundayLevelMessage> messages){
        List<SundayLevelMessageDto> dtos = new ArrayList<>();
        for(SundayLevelMessage message:messages){
            SundayLevelMessageDto dto = new SundayLevelMessageDto(message.getId(),message.getCreateTime(),message.getCreateDate());
            dto.setMemberId(message.getId());
            dto.setMemberName(message.getMemberName());
            dto.setMemberImage(baseYuHuaService.getImageFullPath(message.getMemberImage()));
            dto.setContent(message.getContent());
            dto.setType(message.getType());
            dtos.add(dto);
        }
        return dtos;
    }

}
