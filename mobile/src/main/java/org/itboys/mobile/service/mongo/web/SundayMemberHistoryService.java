package org.itboys.mobile.service.mongo.web;


import org.itboys.mobile.entity.mongo.SundayMemberHistory;
import org.itboys.mongodb.core.MongoDataSource;
import org.itboys.mongodb.service.BaseMongoService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


/**
 * 作者：江兄
 * 日期：2018年7月31日
 * 描述：百睿_用户_成长记录_service
 */
@Service
public class SundayMemberHistoryService extends BaseMongoService< SundayMemberHistory> {

    @Resource(name = "mobileDs")
    private MongoDataSource mobileDataSource;

    @Override
    protected MongoDataSource getMongoDataSource() {
        return mobileDataSource;
    }

    @Override
    protected Class<SundayMemberHistory> getEntityClass() {
        return   SundayMemberHistory.class;
    }




}
