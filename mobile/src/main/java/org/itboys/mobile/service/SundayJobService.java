package org.itboys.mobile.service;

import org.itboys.commons.CommonConstants;
import org.itboys.commons.utils.tools.ListUtil;
import org.itboys.commons.utils.tools.LongUtil;
import org.itboys.mobile.common.SundayCommonConstants;
import org.itboys.mobile.entity.mongo.*;
import org.itboys.mobile.service.mongo.SundayGuardDataService;
import org.itboys.mobile.service.mongo.SundayParAndStuPermissionService;
import org.itboys.mobile.service.mongo.web.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.regex.Pattern;

/**
 * @author：jiangxiong 日期：2017年5月8日
 * 联系方式 ：<EMAIL>
 * 描述：百睿_项目自动任务_service。
 */

@Service
public class SundayJobService {
    protected Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private SundayGuardDataService guardDataService;
    @Autowired
    private SundayGuardService guardService;
    @Autowired
    private SundayStudentService studentService;
    @Autowired
    private SundayTeacherService teacherService;
    @Autowired
    private SundayParentService parentService;
    @Autowired
    private SundayParAndStuPermissionService parAndStuPermissionService;

    @Autowired
    private SundayBunchService bunchService;

    @Autowired
    private SundayStudentQinziBunchService qinziBunchService;
    /**
     * 记录每日园区概况数据
     */
    public void countGuardData() {

        //   int totalTeacher=0;
        //   int totalStudent = 0;
        Map<String,Object> param  = new HashMap<>();
        //2019年2月25日，增加在园总人数，在园家长人数，在园教工人数
        List<SundayGuard> guards  = guardService.list(param);
        List<SundayGuardData> guardDatas  = new ArrayList<>();
        long teacherTotal = 0;
        long studentTotal = 0;
        long parentTotal =0;
        for(SundayGuard guard:guards){
            long teacherNum = 0 ;
            long studentNum = 0;
            long parentNum = 0;
            param.clear();
            param.put("status", SundayCommonConstants.YES);
            param.put("guardId",guard.getId());
            List<SundayStudent> students = null;

            //托班学生
            param.put("stuType", CommonConstants.YES);

            students =studentService.list(param) ;//在园学生
            Set<Long> _studentIds = new HashSet<>();
            for(SundayStudent student:students){
                _studentIds.add(student.getId());
            }
            param.clear();


            //亲子班学生
            param.put("classCategory", CommonConstants.BUNCHTYPE.QINZIBAN);
            param.put("guardId",guard.getId());
            List<SundayBunch> bunches = bunchService.list(param);
            List<Long> bunchIds = new ArrayList<>();
            for(SundayBunch bunch:bunches){
                bunchIds.add(bunch.getId());
            }
            param.clear();
            param.put("state",CommonConstants.NO);
            param.put("bunchId in",bunchIds);
            List<SundayStudentQinziBunch> qinziBunches = qinziBunchService.list(param);
            if(ListUtil.isNotNull(qinziBunches)){
                for(SundayStudentQinziBunch studentQinziBunch:qinziBunches){
                    if(!_studentIds.contains(studentQinziBunch.getStudentId())&& LongUtil.isNotNull(studentQinziBunch.getStudentId())){
                        _studentIds.add(studentQinziBunch.getStudentId());
                    }
                }
            }
            param.clear();

            param.put("stuType", CommonConstants.YES);
            param.put("id in",_studentIds);
            students =studentService.list(param) ;//在园学生


            studentNum=students.size();
            studentTotal+=studentNum;
            //2019年4月22日，家长人数，按园区去重
            Set<Long> parentIdsSet  = new HashSet<>();
            for(SundayStudent student:students){
                List<Long> parentIds =parAndStuPermissionService.getParentId(student.getId());//在园家长人数
                param.clear();
                param.put("id in",parentIds);
                List<SundayParent> parents =  parentService.list(param);
                for(SundayParent parent:parents){
                    parentIdsSet.add(parent.getId());
                }
                //  //parentIdsSet.addAll(parentIds);
            }
            parentNum=parentIdsSet.size();
            parentTotal+= parentNum;
            param.clear();
            param.put("status", SundayCommonConstants.YES);


            Pattern pattern = Pattern.compile("^.*"+","+guard.getId()+","+".*$", Pattern.CASE_INSENSITIVE);
            param.put("guardIds",pattern);
            //param.put("guardId",guard.getId());
            teacherNum = teacherService.count(param);//在园教工人数
            teacherTotal+=teacherNum;
            guardDatas.add(new SundayGuardData(studentNum,teacherNum,parentNum,guard.getId()));
            teacherTotal+=teacherNum;
        }
        if(ListUtil.isNotNull(guardDatas)){
            guardDataService.batchSaveWithoutLogin(guardDatas);
        }
        logger.info("---学生总人数="+studentTotal+",教工总人数="+teacherTotal+"，家长总人数="+parentTotal);
    }

}