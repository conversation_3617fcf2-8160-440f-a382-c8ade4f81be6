package org.itboys.mobile.service.mysql;


import org.apache.commons.lang3.StringUtils;
import org.itboys.admin.tools.AdminSessionHolder;
import org.itboys.commons.utils.tools.LongUtil;
import org.itboys.mobile.dao.SundayOnlineCourseMapper;
import org.itboys.mobile.dao.SundayOnlineCourseRecommendMapper;
import org.itboys.mobile.dao.SundayOnlineCourseVisitNumberMapper;
import org.itboys.mobile.entity.mysql.online.SundayOnlineCourse;
import org.itboys.mobile.entity.mysql.online.SundayOnlineCourseRecommend;
import org.itboys.mobile.entity.mysql.online.SundayOnlineCourseVisitNumber;
import org.itboys.mysql.service.BaseMySqlService;
import org.itboys.param.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;


/**
 * @version V1.0
 * @created with IntelliJ IDEA.
 * @Title: entity
 * @author: shumlinmeng
 * @description:
 */
@Service
public class SundayOnlineCourseRecommendService extends BaseMySqlService<SundayOnlineCourseRecommend>{


    @Autowired
    private SundayOnlineCourseRecommendMapper recommendMapper;

    public void save(SundayOnlineCourseRecommend courseRecommend){
        courseRecommend.setCt(new Date());
        courseRecommend.setCreator(AdminSessionHolder.getAdminUserId());
        recommendMapper.save(courseRecommend);
    }



    //添加推荐课程
    public void addRecommendCourses(HttpServletRequest request){
        String ids=request.getParameter("ids");
        String[] courses=ids.split(",");
        for(String course:courses){
            if(!StringUtils.isBlank(course)) {
                SundayOnlineCourseRecommend courseRecommend=new SundayOnlineCourseRecommend();
                courseRecommend.setCt(new Date());
                courseRecommend.setCourseId(Long.parseLong(course));
                courseRecommend.setRecommendType(0);
                courseRecommend.setCreator(AdminSessionHolder.getAdminUserId());
                recommendMapper.insert(courseRecommend);
            }

        }
    }

    //添加推荐视频
    public void addRecommendCoursesByVideo(HttpServletRequest request){
        String ids=request.getParameter("ids");
        String[] courses=ids.split(",");
        for(String course:courses){
            if(!StringUtils.isBlank(course)) {
                SundayOnlineCourseRecommend courseRecommend=new SundayOnlineCourseRecommend();
                courseRecommend.setCt(new Date());
                courseRecommend.setCourseId(Long.parseLong(course));
                courseRecommend.setRecommendType(1);
                courseRecommend.setCreator(AdminSessionHolder.getAdminUserId());
                recommendMapper.insert(courseRecommend);
            }

        }
    }
}
