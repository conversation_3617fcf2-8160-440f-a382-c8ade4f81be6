package org.itboys.mobile.service.mysql;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.itboys.commons.CommonConstants;
import org.itboys.commons.ErrorException;
import org.itboys.commons.utils.tools.ListUtil;
import org.itboys.commons.utils.tools.LongUtil;
import org.itboys.mobile.common.SundayCommonConstants;
import org.itboys.mobile.dao.SchoolStudentBodyLeaveMapper;
import org.itboys.mobile.dao.SchoolStudentBodyStudyMapper;
import org.itboys.mobile.entity.mongo.SundayBunch;
import org.itboys.mobile.entity.mongo.SundayGuard;
import org.itboys.mobile.entity.mongo.SundayStudent;
import org.itboys.mobile.entity.mongo.SundayStudentQinziBunch;
import org.itboys.mobile.entity.mysql.body.SchoolStudentBodyEmotion;
import org.itboys.mobile.entity.mysql.body.SchoolStudentBodyLeave;
import org.itboys.mobile.entity.mysql.body.SchoolStudentBodyStudy;
import org.itboys.mobile.service.mongo.web.SundayBunchService;
import org.itboys.mobile.service.mongo.web.SundayGuardService;
import org.itboys.mobile.service.mongo.web.SundayStudentQinziBunchService;
import org.itboys.mobile.service.mongo.web.SundayStudentService;
import org.itboys.param.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 宝宝学习情况
 */
@Service
public class SchoolStudentBodyStudyService {

    @Autowired
    private SchoolStudentBodyStudyMapper studyMapper;

    @Autowired
    private SundayStudentService studentService;

    @Autowired
    private SundayGuardService guardService;

    @Autowired
    private SundayBunchService bunchService;

    @Autowired
    private SundayStudentQinziBunchService studentQinziBunchService;

    @Autowired
    private SchoolStudentBodyLeaveMapper leaveMapper;

    /**
     * @param pageSize
     * @param pageNumber
     * @param studentId
     * @return
     */
    public List<Map<String, Object>> selectStudy(String pageSize, String pageNumber, Long studentId) {
        Map<String, Object> param = new HashMap<>();

        if (StringUtils.isBlank(pageNumber)) {
            pageNumber = SundayCommonConstants.INIT_PAGE_NUMBER;
        }
        Integer row_start = 0;
        if (StringUtils.isBlank(pageSize)) {
            pageSize = SundayCommonConstants.INIT_PAGE_SIZE;
        }


        row_start = (Integer.parseInt(pageNumber) - 1) * Integer.parseInt(pageSize);
        param.put("row_start", row_start);
        param.put("row_size", Integer.parseInt(pageSize));


        //studentId
        param.put("studentId", studentId);
        param.put("deleted", 0);

        List<SchoolStudentBodyStudy> siestaList = studyMapper.select(param);

        SimpleDateFormat sdf = new SimpleDateFormat(CommonConstants.DATE.FORMAT_YYYY_MM_dd);
        List<Map<String, Object>> result = new ArrayList<>();
        for (SchoolStudentBodyStudy siesta : siestaList) {
            Map<String, Object> item = packageStudy(siesta, sdf);
            result.add(item);
        }

        return result;
    }


    /**
     * @param pageSize
     * @param pageNumber
     * @return
     */
    public PageResult<SchoolStudentBodyStudy> selectBodyDataFull(String pageSize, String pageNumber, Date startDate,
                                                                 Date endDate, List<Long> studentIds) {

        Map<String, Object> param = new HashMap<>();
        if (StringUtils.isBlank(pageNumber)) {
            pageNumber = SundayCommonConstants.INIT_PAGE_NUMBER;
        }
        Integer row_start = 0;
        if (StringUtils.isBlank(pageSize)) {
            pageSize = SundayCommonConstants.INIT_PAGE_SIZE;
        }
        row_start = (Integer.parseInt(pageNumber) - 1) * Integer.parseInt(pageSize);
        param.put("row_start", row_start);
        param.put("row_size", Integer.parseInt(pageSize));
        param.put("startRecordDate", startDate);
        param.put("endRecordDate", endDate);
        param.put("ids", studentIds);

        List<SchoolStudentBodyStudy> bodyDataList = studyMapper.select(param);
        Long total = (Long) studyMapper.count(param);
        PageResult<SchoolStudentBodyStudy> pageResult = new PageResult<>();
        pageResult.setData(bodyDataList);
        pageResult.setTotal(total);
        return pageResult;
    }


    /**
     * 无分页
     *
     * @return
     */
    public List<SchoolStudentBodyStudy> selectBodyDataFullNoPage(Date startDate,
                                                                 Date endDate, List<Long> studentIds) {
        Map<String, Object> param = new HashMap<>();

        param.put("startRecordDate", startDate);
        param.put("endRecordDate", endDate);
        param.put("ids", studentIds);

        List<SchoolStudentBodyStudy> bodyDataList = studyMapper.select(param);

        return bodyDataList;
    }


    /**
     * 
     * @param id
     * @param recordTime
     * @param msValue
     * @param xxlValue
     * @param yyValue
     * @param sparkValue
     * @param spark2Value
     * @param selValue
     * @param memberId
     * @param remarks
     */
    public void updateBodyStudyData(Long id, String recordTime, Integer msValue, Integer xxlValue, Integer yyValue, Integer sparkValue, Integer spark2Value, Integer selValue, Long memberId, String remarks) {
        
    	
    	SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
    	SchoolStudentBodyStudy study = null;
    	try {
    		study = new SchoolStudentBodyStudy();
    		study.setId(id);
        	study.setRecordTime(sdf.parse(recordTime));
        	study.setMsValue(msValue);
        	study.setXxlValue(xxlValue);
        	study.setYyValue(yyValue);
        	study.setSparkValue(sparkValue);
        	study.setSpark2Value(spark2Value);
        	study.setSelValue(selValue);
        	study.setCreatorId(memberId);
        	study.setRemarks(remarks);
        } catch (ParseException e) {
            e.printStackTrace();
        }
    	if(id == null) {
    		studyMapper.insert(study);
    	}else {
	    	SchoolStudentBodyStudy schoolStudentBodyStudy = studyMapper.getById(id);
	        if (schoolStudentBodyStudy != null) {
	            studyMapper.update(study);
	        } else {
	            throw new ErrorException("查询ID错误!");
	        }
    	}
    }

    /**
     * 创建记录
     * @param recordTime
     * @param studentId
     * @param msValue
     * @param xxlValue
     * @param yyValue
     * @param sparkValue
     * @param spark2Value
     * @param selValue
     * @param creatorType
     * @param memberId
     * @param remarks
     */
    public void createBodyStudyData(String recordTime, Long studentId, Integer msValue, Integer xxlValue, Integer yyValue, Integer sparkValue, Integer spark2Value, Integer selValue, Integer creatorType, Long memberId, String remarks) {
        if (LongUtil.isNull(studentId) || LongUtil.isZreo(studentId)) {
            throw new ErrorException("studentId 不可为空");
        }
        SchoolStudentBodyStudy study = new SchoolStudentBodyStudy();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        try {
        	Map<String, Object> param2 = new HashMap<>();
            param2.put("studentId", studentId);
            param2.put("recordTime", sdf.parse(recordTime));
            SchoolStudentBodyStudy study1 = studyMapper.getBody(param2);//
            
        	study.setRecordTime(sdf.parse(recordTime));
        	study.setCreateTime(new Date());
        	study.setCreatorId(memberId);
        	study.setCreatorType(creatorType);
        	study.setRemarks(remarks);
        	study.setMsValue(msValue);
        	study.setXxlValue(xxlValue);
        	study.setYyValue(yyValue);
        	study.setSparkValue(sparkValue);
        	study.setSpark2Value(spark2Value);
        	study.setSelValue(selValue);
        	study.setStudentId(studentId);
        	if(study1 == null) {
        		studyMapper.insert(study);
            }else {
            	study.setId(study1.getId());
            	studyMapper.update(study);
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
    }

    /**
     * 批量创建记录
     * @param recordTime
     * @param bunchId
     * @param msValue
     * @param xxlValue
     * @param yyValue
     * @param sparkValue
     * @param spark2Value
     * @param selValue
     * @param creatorType
     * @param memberId
     * @param remarks
     */
    public void batchCreateBodyStudyData(String recordTime, Long bunchId, Integer msValue, Integer xxlValue, Integer yyValue, Integer sparkValue, Integer spark2Value, Integer selValue, Integer creatorType, Long memberId, String remarks) {
        if (LongUtil.isNull(bunchId) || LongUtil.isZreo(bunchId)) {
            throw new ErrorException("bunchId 不可为空");
        }
        
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Map<String, Object> param = new HashMap<>();

        SundayBunch bunch = bunchService.getById(bunchId);
        List<Long> studentIds = new ArrayList<>();
        if (bunch != null && !CommonConstants.BUNCHTYPE.QINZIBAN.equals(bunch.getClassCategory())) {
            param.put("bunchId", bunchId);
            param.put("stuType", CommonConstants.YES);
            param.put("status", CommonConstants.YES);
            param.put("isDeleted", 0);//是否删除标识。0.否，1是
            List<SundayStudent> students = studentService.list(param);

            if (ListUtil.isNotNull(students)) {
                for (SundayStudent student : students) {
                    studentIds.add(student.getId());
                }
            }
        } else {
            param.put("bunchId", bunchId);
            param.put("state", CommonConstants.NO);
            param.put("isDeleted", 0);//是否删除标识。0.否，1是
            List<SundayStudentQinziBunch> studentQinziBunches = studentQinziBunchService.list(param);
            if (ListUtil.isNotNull(studentQinziBunches)) {
                for (SundayStudentQinziBunch studentQinziBunch : studentQinziBunches) {
                    if (LongUtil.isNotNull(studentQinziBunch.getStudentId())) {
                        studentIds.add(studentQinziBunch.getStudentId());
                    }
                }
            }
        }

        // 批量增加
        if (ListUtil.isNotNull(studentIds)) {
            for (Long studentId : studentIds) {
            	Map<String, Object> param2 = new HashMap<>();
                param2.put("studentId", studentId);
                param2.put("recordTime", recordTime);
                studyMapper.deleteBodyStudy(param2);//删除原数据
                
            	SchoolStudentBodyLeave leave = leaveMapper.getBody(param2); 
            	if(leave == null || leave.getDeleted().compareTo(1) == 0) {
	                SchoolStudentBodyStudy study = new SchoolStudentBodyStudy();
	                try {
	                	study.setRecordTime(sdf.parse(recordTime));
	                	study.setCreateTime(new Date());
	                	study.setCreatorId(memberId);
	                	study.setCreatorType(creatorType);
	                	study.setRemarks(remarks);
	                	study.setMsValue(msValue);
	                	study.setXxlValue(xxlValue);
	                	study.setYyValue(yyValue);
	                	study.setSparkValue(sparkValue);
	                	study.setSpark2Value(spark2Value);
	                	study.setSelValue(selValue);
	                	study.setStudentId(studentId);
	                } catch (ParseException e) {
	                    e.printStackTrace();
	                }
	                studyMapper.insert(study);
            	}
            }
        }


    }


    /**
     * @param id
     */
    public void deleteBodyStudyData(Long id) {
    	studyMapper.delete(id);
    }

    /**
     * 获取宝宝午睡记录
     * @param recordTime
     * @param studentId
     * @return
     */
    public Map<String, Object> getBodyStudyData(String recordTime, Long studentId) {

        if (LongUtil.isNull(studentId) || LongUtil.isZreo(studentId)) {
            throw new ErrorException("studentId 不可为空");
        }
        Map<String, Object> param = new HashMap<>();
        param.put("studentId", studentId);
        if (StringUtils.isNotBlank(recordTime)) {
            param.put("recordTime", recordTime);
        }
        SchoolStudentBodyStudy siesta = studyMapper.getBody(param);
        if(siesta == null) return null;
        SimpleDateFormat sdf = new SimpleDateFormat(CommonConstants.DATE.FORMAT_YYYY_MM_dd);
        Map<String, Object> item = packageStudy(siesta, sdf);
        return item;
    }

    /**
     * 根据主键id获取午睡记录
     * @param id
     * @return
     */
    public Map<String, Object> getById(Long id) {
        SchoolStudentBodyStudy siesta = studyMapper.getById(id);
        Map<String, Object> item = new HashMap<>();
        if (siesta != null) {
            SimpleDateFormat sdf = new SimpleDateFormat(CommonConstants.DATE.FORMAT_YYYY_MM_dd);
            item = packageStudy(siesta, sdf);
        }
        return item;
    }

    /**
     * @param ids
     */
    public void deleteBodyStudyDataByIds(String ids) {
        List<Long> listIds = Arrays.asList(ids.split(","))
                .stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
        for (int i = 0; i < listIds.size(); i++) {
            if (LongUtil.isNotZreo(listIds.get(i))) {
            	studyMapper.delete(listIds.get(i));
            }
        }

    }


    public List<Map<String, Object>> getLatestStudyList(Long studentId) {
        //
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat sdf2 = new SimpleDateFormat("MM-dd");

        //30天前
        Calendar c = Calendar.getInstance();
        c.add(Calendar.DATE, -30);
        Date start = c.getTime();
        String preDay = sdf.format(start);

        //今天
        String now = sdf.format(new Date());

        Map<String, Object> params = new HashMap<>();

        params.put("startTime", preDay);
        params.put("endTime", now);
        params.put("studentId", studentId);
        //获取近30天前
        List<SchoolStudentBodyStudy> siestaList = studyMapper.selectChartList(params);
        List<SchoolStudentBodyStudy> siestas = new ArrayList<>();
        if (siestaList != null && siestaList.size() > 0) {
            Map<Date, List<SchoolStudentBodyStudy>> siestaMap = siestaList.stream().collect(Collectors.groupingBy(SchoolStudentBodyStudy::getRecordTime));
            for (Map.Entry<Date, List<SchoolStudentBodyStudy>> entry : siestaMap.entrySet()) {
                List<SchoolStudentBodyStudy> list = entry.getValue();
                list = list.stream().sorted(Comparator.comparing(SchoolStudentBodyStudy::getCreateTime).reversed()).collect(Collectors.toList());
                siestas.add(list.get(0));
            }
            siestas = siestas.stream().sorted(Comparator.comparing(SchoolStudentBodyStudy::getRecordTime).reversed()).limit(7).collect(Collectors.toList());
            siestas = siestas.stream().sorted(Comparator.comparing(SchoolStudentBodyStudy::getRecordTime)).collect(Collectors.toList());
        }
        List<Map<String, Object>> items = new ArrayList<>(7);
        for (SchoolStudentBodyStudy siesta : siestas) {
            Map<String, Object> param = new HashMap<>();

            Date time = siesta.getRecordTime();

            String recordTime = sdf2.format(time);
            param.put("recordTime", recordTime);
            param.put("msValue", siesta.getMsValue());
            param.put("xxlValue", siesta.getXxlValue());
            param.put("yyValue", siesta.getYyValue());
            param.put("sparkValue", siesta.getSparkValue());
            param.put("spark2Value", siesta.getSpark2Value());
            param.put("selValue", siesta.getSelValue());

            items.add(param);
        }
        return items;
    }


    public Map<String, Object> packageStudy(SchoolStudentBodyStudy siesta, SimpleDateFormat sdf) {
        Map<String, Object> item = new HashMap<>();
        item.put("id", siesta.getId());
        item.put("recordTime", sdf.format(siesta.getRecordTime()));
        item.put("msValue", siesta.getMsValue());
        item.put("xxlValue", siesta.getXxlValue());
        item.put("yyValue", siesta.getYyValue());
        item.put("sparkValue", siesta.getSparkValue());
        item.put("spark2Value", siesta.getSpark2Value());
        item.put("selValue", siesta.getSelValue());
        item.put("remarks", siesta.getRemarks());
        item.put("creatorId", siesta.getCreatorId());
        item.put("creatorType", siesta.getCreatorType());
        return item;
    }

    public Map<String, Object> packageBodyDataFull(SchoolStudentBodyStudy data, SimpleDateFormat sdf) {

        Map<String, Object> item = new HashMap<>();
        item.put("id", data.getId());
        item.put("recordTime", sdf.format(data.getRecordTime()));
        item.put("msValue", data.getMsValue());
        item.put("xxlValue", data.getXxlValue());
        item.put("yyValue", data.getYyValue());
        item.put("sparkValue", data.getSparkValue());
        item.put("spark2Value", data.getSpark2Value());
        item.put("selValue", data.getSelValue());

        item.put("remarks", data.getRemarks());
        item.put("creatorId", data.getCreatorId());
        item.put("creatorType", data.getCreatorType());

        // 根据学生获取所属班级
        SundayStudent student = studentService.findOne(data.getStudentId());
        SundayGuard sundayGuard = guardService.findGuardInfo(student.getGuardId());
        // 获取所有托班、亲子班 为 bunchesName
        String bunchesName = "";
        if (student.getBunchId() != null) {
            SundayBunch sundayBunch = bunchService.findOne(student.getBunchId());
            item.put("bunchName", sundayBunch.getName());
            bunchesName = bunchesName + sundayBunch.getName();
        }
        if (student.getQinziType()!=null && student.getQinziType() == 1) {
            List<SundayBunch> bunches = bunchService.findByStudentId(student.getId());
            if (bunches != null && bunches.size() > 0) {
                for (int i = 0; i < bunches.size(); i++) {
                    bunchesName = bunchesName + " " + bunches.get(i).getName();
                }
            }
        }

        item.put("bunchesName", bunchesName);
        item.put("student", student);
        item.put("guardId", student.getGuardId());
        item.put("guardName", sundayGuard.getName());
        item.put("bunchId", student.getBunchId());
        item.put("studentId", student.getId());
        item.put("studentName", student.getName());
        //奇妙园
        if(sundayGuard.getName().contains("奇妙园")) {
	        // 1=积极；2=被动；3=未参加
	        if(data.getMsValue() == 1){
	            item.put("msValueName","积极");
	        }
	        if (data.getMsValue() == 2){
	            item.put("msValueName","被动");
	        }
	        if (data.getMsValue() == 3){
	            item.put("msValueName","未参加");
	        }
	        // 1=积极；2=被动；3=未参加
	        if(data.getXxlValue() == 1){
	            item.put("xxlValueName","积极");
	        }
	        if (data.getXxlValue() == 2){
	            item.put("xxlValueName","被动");
	        }
	        if (data.getXxlValue() == 3){
	            item.put("xxlValueName","未参加");
	        }
	        // 1=积极；2=被动；3=未参加
	        if(data.getYyValue() == 1){
	            item.put("yyValueName","积极");
	        }
	        if (data.getYyValue() == 2){
	            item.put("yyValueName","被动");
	        }
	        if (data.getYyValue() == 3){
	            item.put("yyValueName","未参加");
	        }
	        // 1=积极；2=被动；3=未参加
	        if(data.getSparkValue() == 1){
	            item.put("sparkValueName","积极");
	        }
	        if (data.getSparkValue() == 2){
	            item.put("sparkValueName","被动");
	        }
	        if (data.getSparkValue() == 3){
	            item.put("sparkValueName","未参加");
	        }
	        // 1=积极；2=被动；3=未参加
	        if(data.getSpark2Value() == 1){
	            item.put("spark2ValueName","积极");
	        }
	        if (data.getSpark2Value() == 2){
	            item.put("spark2ValueName","被动");
	        }
	        if (data.getSpark2Value() == 3){
	            item.put("spark2ValueName","未参加");
	        }
	        // 1=积极；2=被动；3=未参加
	        if(data.getSelValue() == 1){
	            item.put("selValueName","积极");
	        }
	        if (data.getSelValue() == 2){
	            item.put("selValueName","被动");
	        }
	        if (data.getSelValue() == 3){
	            item.put("selValueName","未参加");
	        }
        }else { //托育园
	        //【三心课程】 1=积极；2=被动；3=未参加 
	        if(data.getXxlValue() == 1){
	            item.put("xxlValueName","积极");
	        }
	        if (data.getXxlValue() == 2){
	            item.put("xxlValueName","被动");
	        }
	        if (data.getXxlValue() == 3){
	            item.put("xxlValueName","未参加");
	        }
	        //【SPARK】 1=积极；2=被动；3=未参加
	        if(data.getSparkValue() == 1){
	            item.put("sparkValueName","积极");
	        }
	        if (data.getSparkValue() == 2){
	            item.put("sparkValueName","被动");
	        }
	        if (data.getSparkValue() == 3){
	            item.put("sparkValueName","未参加");
	        }
        }
        return item;
    }
}
