package org.itboys.mobile.service.mongo.web;

import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import com.wordnik.swagger.annotations.ApiParam;
import org.itboys.commons.utils.tools.LongUtil;
import org.itboys.mobile.entity.mongo.SundayBunchQinzi;
import org.itboys.mobile.entity.mongo.SundayTeacher;
import org.itboys.mobile.entity.mongo.attendance.SundayLessonMaterial;
import org.itboys.mobile.entity.mongo.attendance.SundayLessonMaterialDto;
import org.itboys.mobile.entity.mongo.material.SundayMaterial;
import org.itboys.mobile.entity.mongo.material.SundayMaterialDto;
import org.itboys.mobile.service.mongo.web.material.SundayMaterialService;
import org.itboys.mobile.util.AccountIdHoldUtils;
import org.itboys.mongodb.core.MongoDataSource;
import org.itboys.mongodb.service.BaseMongoService;
import org.itboys.param.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

@Service
public class SundayLessonMaterialService extends BaseMongoService<SundayLessonMaterial>{
    @Resource(name = "mobileDs")
    private MongoDataSource mobileDataSource;

    @Override
    protected MongoDataSource getMongoDataSource() {
        return mobileDataSource;
    }

    @Override
    protected Class<SundayLessonMaterial> getEntityClass() {
        return SundayLessonMaterial.class;
    }

    @Autowired
    private SundayMaterialService materialService;

    @Autowired
    private SundayBunchQinziService bunchQinziService;

    public SundayLessonMaterial findOne(Long id){
        SundayLessonMaterial lessonMaterial = null;
        if(LongUtil.isNotZreo(id)&& LongUtil.isNotNull(id)){
            lessonMaterial = super.getById(id);
            SundayMaterial material = materialService.getById(lessonMaterial.getMaterialId());
            lessonMaterial.setMaterial(material);
        }else{
            lessonMaterial = new SundayLessonMaterial();
            lessonMaterial.setId(0);
        }
        return lessonMaterial;
    }

    /**
     * 获取亲子素材(废弃)
     * @param request
     * @param materialName
     */
    public List<SundayLessonMaterialDto> getMaterialByList(HttpServletRequest request,String materialName){
        Long memberId = AccountIdHoldUtils.getMemberId();
        Map<String,Object> param = new HashMap();
        HashMap<String,String> paramContain = new HashMap<>();
        paramContain.put("teacherIds",memberId.toString());
        List<SundayBunchQinzi> bunchQinziList = bunchQinziService.containpageQuery(request,paramContain,param).getData();
        List<Long> lessonIds=new ArrayList<>();
        for(SundayBunchQinzi bunchQinzi:bunchQinziList){
            lessonIds.add(bunchQinzi.getLessonId());
        }
        param.put("lessonId in ",lessonIds);
        if (materialName!=null&& materialName.length()>0){
            //param.put("materialName",materialName);
            Pattern pattern = Pattern.compile("^.*"+materialName+".*$", Pattern.CASE_INSENSITIVE);
            DBObject regex = new BasicDBObject();
            regex.put("materialName", pattern);
            param.put("materialName",pattern);
        }
        PageResult<SundayLessonMaterial> pageResult = super.pageQuery(request,param);
        List<SundayLessonMaterial> materialList = pageResult.getData();
        List<SundayLessonMaterialDto> result = new ArrayList<>();
        for(SundayLessonMaterial material:materialList){
            SundayMaterial material1 = materialService.getById(material.getMaterialId());
            SundayMaterialDto dto = new SundayMaterialDto();
            dto.setId(material.getId());
            dto.setType(material1.getType());
            dto.setSourceName(material1.getSourceName());
            dto.setSource(material1.getSource());
            dto.setImage(material1.getImage());
            dto.setSourceSize(material1.getSourceSize());
            dto.setSourceTime(material1.getSourceTime());
            SundayLessonMaterialDto materialDto = new SundayLessonMaterialDto();
            materialDto.setId(material.getId());
            materialDto.setMaterialName(material.getMaterialName());
            materialDto.setMaterial(dto);
            result.add(materialDto);
        }
        return result;
    }
}