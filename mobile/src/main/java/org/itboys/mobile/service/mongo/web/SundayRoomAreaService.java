package org.itboys.mobile.service.mongo.web;

import org.apache.commons.lang.StringUtils;
import org.apache.poi.util.StringUtil;
import org.itboys.commons.CommonConstants;
import org.itboys.commons.ErrorException;
import org.itboys.commons.utils.tools.LongUtil;
import org.itboys.mobile.entity.mongo.SundayBunchQinzi;
import org.itboys.mobile.entity.mongo.SundayPlan;
import org.itboys.mobile.entity.mongo.SundayRoomArea;
import org.itboys.mobile.service.BaseYuHuaService;
import org.itboys.mongodb.core.MongoDataSource;
import org.itboys.mongodb.service.BaseMongoService;
import org.itboys.param.PageResult;
import org.itboys.param.ParamUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @version V1.0
 * @created with IntelliJ IDEA.
 * @Title: entity
 * @author: shumlinmeng
 * @date: 2019/5/21 18:42
 * @description:
 */
@Service
public class SundayRoomAreaService extends BaseMongoService<SundayRoomArea>{


    @Autowired
    private SundayBunchQinziService sundayBunchQinziService;

    @Resource(name = "adminDS")
    private MongoDataSource ds;

    @Override
    protected MongoDataSource getMongoDataSource() {
        return ds;
    }

    @Override
    protected Class<SundayRoomArea> getEntityClass() {
        return SundayRoomArea.class;
    }

    @Autowired
    private BaseYuHuaService baseYuHuaService;


    /**
     * 分页查询
     * @param request
     * @return
     */
    public PageResult<SundayRoomArea> selectRoom(HttpServletRequest request,String guardIdsStr){
        Map<String,Object> param = ParamUtil.packageMongoExactParam(request,"guardId");
        if(StringUtils.isNotEmpty(guardIdsStr)){
            List<Long> guardIds = new ArrayList<>();
            for(String guardId:guardIdsStr.split(",")){
                if(StringUtils.isNotEmpty(guardId)){
                    guardIds.add(Long.valueOf(guardId));
                }
            }
            param.put("guardId in",guardIds);
        }

        PageResult<SundayRoomArea> pageResult = super.pageQuery(request,param);
        //批量查询园区和班级信息
        baseYuHuaService.packageGuard(pageResult.getData(),false);

        return pageResult;
    }




    //删除操作
    public void batchDeleteByIds(String oldRoomIds){
        String[] ids=oldRoomIds.split(",");
        List<Long> longList=new ArrayList<>();
        for(String id:ids){
            if(!StringUtils.isBlank(id)){
                longList.add(Long.parseLong(id));
            }
        }
        batchDeleted(longList);
    }

    //删除操作
    public void batchDeleted(List<Long> oldRoomIds){
        Map<String,Object> params = new HashMap<>();
        params.put("roomId in",oldRoomIds);
        long count = sundayBunchQinziService.count(params);
        if(LongUtil.isZreo(count)){
            params.clear();
            params.put("id in",oldRoomIds);
            List<SundayRoomArea> rooms = super.list(params);
            super.batchDelete(rooms);
        }else{
            throw new ErrorException("已有班级使用该教室,无法删除!");
        }
    }

    //更新操作
    public void saveRoom(SundayRoomArea roomArea){
        if(LongUtil.isNull(roomArea.getGuardId())||LongUtil.isZreo(roomArea.getGuardId())){
            throw new ErrorException("园区为空!");
        }

        Map<String,Object> params = new HashMap<>();
        params.put("name",roomArea.getName());
        params.put("guardId",roomArea.getGuardId());

        if(LongUtil.isNotZreo(roomArea.getId())){
            params.put("id !=",roomArea.getId());
            if(super.count(params)>0){
                throw new ErrorException("教室名称不可重复!");
            }
            params.clear();
            super.update(roomArea.getId(),"name",roomArea.getName());

            params.put("roomId",roomArea.getId());
            params.put("isDeleted",0);
            List<SundayBunchQinzi> qinzis = sundayBunchQinziService.list(params);
            for(SundayBunchQinzi q:qinzis){
                q.setRoomName(roomArea.getName());
            }
            sundayBunchQinziService.batchUpdate(qinzis);
        }else{
            if(super.count(params)>0){
                throw new ErrorException("教室名称不可重复!");
            }
            roomArea.setUsed(CommonConstants.NO);
            super.save(roomArea);
        }

    }
}
