package org.itboys.mobile.service.mongo.web;


import org.itboys.commons.utils.tools.ListUtil;
import org.itboys.commons.utils.tools.LongUtil;
import org.itboys.mobile.entity.mongo.SundayBunch;
import org.itboys.mobile.entity.mongo.assessment.SundayAssessment;
import org.itboys.mobile.entity.mongo.assessment.SundayAssessmentResult;
import org.itboys.mobile.entity.mongo.assessment.SundayAssessmentResultOption;
import org.itboys.mongodb.core.MongoDataSource;
import org.itboys.mongodb.service.BaseMongoService;
import org.itboys.param.PageResult;
import org.itboys.param.ParamUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 作者：江兄
 * 日期：2018年7月31日
 * 描述：百睿_评测_结果_service
 */
@Service
public class SundayAssessmentResultService extends BaseMongoService<SundayAssessmentResult> {

    @Autowired
    private SundayAssessmentResultOptionService resultOptionService;

    @Resource(name = "mobileDs")
    private MongoDataSource mobileDataSource;

    @Override
    protected MongoDataSource getMongoDataSource() {
        return mobileDataSource;
    }

    @Override
    protected Class<SundayAssessmentResult> getEntityClass() {
        return SundayAssessmentResult.class;
    }


    /**
     * 分页查询
     * @param request
     * @return
     */
    public PageResult<SundayAssessmentResult> selectResult(HttpServletRequest request,Long assessmentId){
        Map<String,Object> param = ParamUtil.packageMongoExactParam(request,"");
        param.put("assessmentId",assessmentId);
        Map<String,String> containsparam = ParamUtil.packageMongoVagueParam(request,"assessmentName","memberName");
        PageResult<SundayAssessmentResult> pageResult = super.containpageQuery(request,containsparam,param);
        return pageResult;
    }

    /**
     * 专家点评
     * @param id
     * @param expertId
     * @param expertName
     * @param expertImage
     * @param expertDesc
     * @param expertValue
     * @param content
     */
  /*  public void comment(Long id,
                        Long expertId,String expertName,String expertImage,String expertDesc,
                        Integer  expertValue,String content){
        SundayAssessmentResult result = new SundayAssessmentResult();
        result.setId(id);
        result.setExpertId(expertId);
        result.setExpertName(expertName);
        result.setExpertImage(expertImage);
        result.setExpertDesc(expertDesc);
        result.setExpertValue(expertValue);
       // result.setContent(content);
        super.updateExceptEmpty(id,result);
    }*/

    /**
     * 校验是否参与过评测 0未评测 1已评测，2评测中
     * @param memberId
     * @param assessmentId
     * @return
     */
    public Integer checkIsCheck(Long memberId,Long assessmentId){
        Map<String,Object> param = new HashMap<>();
        param.put("memberId",memberId);
        param.put("assessmentId",assessmentId);
        Integer isCheck = 0;
       if(LongUtil.isNotZreo(super.count(param))){
           isCheck = 1;//已完成评测
       }else{
            //是否在评测中
           if(LongUtil.isNotZreo(resultOptionService.count(param))){
               isCheck = 2;
           }
       }
        return isCheck;
    }

}
