package org.itboys.mobile.service;

import org.itboys.admin.service.lasted.SystemMenuService;
import org.itboys.mobile.dao.*;
import org.itboys.mobile.entity.mongo.SundayBunch;
import org.itboys.mobile.service.mongo.SundayMemberRecordService;
import org.itboys.mobile.service.mongo.web.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author：jiangxiong 日期：2017年5月8日
 * 联系方式 ：<EMAIL>
 * 描述：百睿_脚本_service。
 */

@Service
public class SundayScriptService {
    protected Logger logger = LoggerFactory.getLogger(getClass());
    @Autowired
    private SundayGuard1Mapper guard1Mapper;
    @Autowired
    private SundayGuardService guardService;

    @Autowired
    private SundayBunch1Mapper bunch1Mapper;
    @Autowired
    private SundayBunchService bunchService;


    @Autowired
    private SundayOpus1Mapper opus1Mapper;
    @Autowired
    private SundayOpusService opusService;

    @Autowired
    private SundayNotice1Mapper notice1Mapper;
    @Autowired
    private SundayNoticeService noticeService;


    @Autowired
    private SundayTeacher1Mapper teacher1Mapper;
    @Autowired
    private SundayTeacherService teacherService;
    @Autowired
    private SundayStudent1Mapper student1Mapper;
    @Autowired
    private SundayStudentService studentService;
    @Autowired

    private  SundayDailyDetail1Mapper  dailyDetail1Mapper;
    @Autowired
    private  SundayDailyService  dailyService;
    @Autowired
    private  SundayDailyDetailService  dailyDetailService;
    @Autowired
    private SundayCamera1Mapper camera1Mapper;

    @Autowired
    private SundayCameraService cameraService;

    @Autowired
    private SundayAdvert1Mapper advert1Mapper;
    @Autowired
    private SundayAdvertService advertService;

    @Autowired
    private SundayPlanAndEnglishMapper planAndEnglishMapper;
    @Autowired
    private SundayPlanService planService;

    @Autowired
    private SundayEnglishService englishService;


    @Autowired
    private SundayDynamic1Mapper dynamic1Mapper;
    @Autowired
    private SundayDynamicService dynamicService;




    @Autowired
    private SundayUser1Mapper user1Mapper;
    @Autowired
    private SundayRoleService roleService;


    @Autowired
    private SundayFood1Mapper food1Mapper;
    @Autowired
    private SundayFoodGroupService foodGroupService;
    @Autowired
    private SundayFoodService foodService;

    @Autowired
    private SundayParent1Mapper parent1Mapper;
    @Autowired
    private SundayParentService parentService;

    @Autowired
    private SundayHistory1Mapper history1Mapper;
    @Autowired
    private SundayHistoryService historyService;



    @Autowired
    private SystemMenuService menuService;
    @Autowired
    private SundayMemberRecordService memberRecordService;
    @Autowired
    private SundayUserService userService;



    /********3.0开始******/
    /**
     * 1，转换教师为多园区
     * 2，初始化班级状态
     */
    public void init30(){
        Map<String,Object> param  = new HashMap<>();
        //1，转换教师为多园区，待定
          /*  List<SundayTeacher> teachers = teacherService.list(param);
            for(SundayTeacher teacher:teachers){
                teacherService.update(teacher.getId(),"guardIds",","+teacher.getGuardId()+",");
            }*/
        //2,初始化班级状态
        List<SundayBunch> bunches = bunchService.list(param);
        for(SundayBunch bunch:bunches){
            bunchService.update(bunch.getId(),"status",1);
        }
    }



}