
package org.itboys.mobile.service.mongo.web;

import org.apache.commons.lang.StringUtils;
import org.itboys.commons.CommonConstants;
import org.itboys.mobile.common.SundayCommonConstants;
import org.itboys.mobile.entity.mongo.SundayLevel;
import org.itboys.mobile.service.BaseYuHuaService;
import org.itboys.mongodb.core.MongoDataSource;
import org.itboys.mongodb.service.BaseMongoService;
import org.itboys.param.PageResult;
import org.itboys.param.ParamUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 作者：江兄
 * 日期：2018年7月31日
 * 描述：百睿_请假_service
 */

@Service
public class SundayLevelService extends BaseMongoService<SundayLevel> {

    @Autowired
    private BaseYuHuaService baseYuHuaService;


    @Resource(name = "mobileDs")
    private MongoDataSource mobileDataSource;

    @Override
    protected MongoDataSource getMongoDataSource() {
        return mobileDataSource;
    }

    @Override
    protected Class<SundayLevel> getEntityClass() {
        return SundayLevel.class;
    }


    /**
     *
     * @param request
     * @param guardIdsStr
     * @param bunchIdsStr
     * @return
     */
    public PageResult<SundayLevel> selectLevel(HttpServletRequest request, String guardIdsStr,String bunchIdsStr){
        Map<String,Object> param = ParamUtil.packageMongoExactParam(request,"guardId","bunchId");
        param = ParamUtil.yuhuaUtil(param,guardIdsStr,bunchIdsStr,false,false);
        Map<String,String> containsparam = ParamUtil.packageMongoVagueParam(request,"memberName","bunchName","guarderName");

        PageResult<SundayLevel> pageResult = super.containpageQuery(request,containsparam,param);

        List<SundayLevel> levelList = pageResult.getData().stream().filter(item ->
                CommonConstants.NO.equals(item.getStatus())).collect(Collectors.toList());
        List<SundayLevel> old_levelList = pageResult.getData().stream().filter(item ->
                CommonConstants.YES.equals(item.getStatus())).collect(Collectors.toList());
        Collections.reverse(levelList);
        levelList.addAll(old_levelList);
        pageResult.setData(levelList);
        baseYuHuaService.packageGuardAndBunch(pageResult.getData(),false,false);
        //补充头像
        for(SundayLevel level:pageResult.getData()){
            if(StringUtils.isEmpty(level.getMemberImage())){
                level.setMemberImage(baseYuHuaService.getImageFullPath("/logo/logo26.jpg"));
            }
        }



        return pageResult;

    }
    /**
     * 处理请假通知
     * @param levelId
     * @param memberId
     */
    public void handle(Long levelId,Long memberId){
        super.update(levelId,"status",SundayCommonConstants.YES);
    }





}

