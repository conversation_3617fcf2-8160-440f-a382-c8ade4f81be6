package org.itboys.mobile.service.mongo.web;

import com.alibaba.fastjson.JSON;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.itboys.admin.tools.AdminSessionHolder;
import org.itboys.commons.utils.time.TimeUtils;
import org.itboys.commons.utils.tools.ListUtil;
import org.itboys.commons.utils.tools.LongUtil;
import org.itboys.framework.excel.ExcelUtils;
import org.itboys.framework.resource.ResourceHolder;
import org.itboys.mobile.common.SundayCommonConstants;
import org.itboys.mobile.dao.SundayDataMapper;
import org.itboys.mobile.entity.mongo.SundayGuard;
import org.itboys.mobile.entity.mongo.SundayStudent;
import org.itboys.mobile.entity.mongo.SundayTeacher;
import org.itboys.mobile.entity.mysql.SundayData;
import org.itboys.mobile.service.mongo.SundayGuardDataService;
import org.itboys.mobile.util.DatetUtil;
import org.itboys.mobile.util.WeekUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;

/**
 * @author：jiangxiong 日期：2017年5月8日
 * 联系方式 ：<EMAIL>
 * 描述：百睿_广告信息_service
 */
@Service
public class SundayDataServiceOld {
    protected Logger logger = LoggerFactory.getLogger(getClass());
      @Autowired
    private SundayDataMapper dataMapper;
    @Autowired
    private SundayGuardService guardService;
    @Autowired
    private SundayStudentService studentService;

    @Autowired
    private SundayTeacherService teacherService;

    @Autowired
    private ResourceHolder resourceHolder;

    @Autowired
    private SundayGuardDataService guardDataService;

    private Map<String,Object> packageParam(Long guardId){
        Map<String,Object> param = new HashedMap();
        List<Long> guardIs = getGuardIds();
        if(ListUtil.isNotNull(guardIs)){
            param.put("guardId in",guardIs);
        }
        if(LongUtil.isZreo(guardId)){
            param.put("guardId",guardId);
        }
        return  param;
    }

    /**
     * 数据概况

     * @return 1,累计用户,启动次数,活跃用户,新用户,新用户占活跃比,人均启动次数,
     *          2，启动次数，活跃用户，新用户，新用户占活跃比，次均使用时长，人均使用时长，活跃度
     *          3,在园总人数,在园家长人数,在园教工人数
     */
    public Map<String,Object> count0(){

        //2018年12月5日，增加登陆人员的园区ID
        List<Long> guardIs = getGuardIds();
        Map<String,Object> param = new HashedMap();
        Map<String,Object> result = new HashedMap();
        //昨日开始时间
        Calendar yesterday_start = Calendar.getInstance();
        yesterday_start.add(Calendar.DAY_OF_MONTH,-1);
        yesterday_start.set(Calendar.HOUR_OF_DAY,0);
        yesterday_start.set(Calendar.MINUTE,0);
        yesterday_start.set(Calendar.SECOND,0);
        //昨日结束时间
        Calendar yesterday_end = Calendar.getInstance();
        yesterday_end.add(Calendar.DAY_OF_MONTH,-1);
        yesterday_end.set(Calendar.HOUR_OF_DAY,23);
        yesterday_end.set(Calendar.MINUTE,59);
        yesterday_end.set(Calendar.SECOND,59);

        List<Map<String,Object>> datas = new ArrayList<>();
        long ljyh  = 0;//累计用户
        long zrqdcs  = 0;//昨日启动次数
        int zrhyyh = 0;//昨日活跃用户（点击了两次及以上的）
        long zrxyh = 0;//昨日新用户
        double zrxyhzhydb = 0.00;//昨日新用户占活跃度比
        double zrrjqdcs = 0.00;//昨日人均启动次数
        //总人数（学生+家长）
        param.clear();
        if(ListUtil.isNotNull(guardIs)){
            param.put("guardId in",guardIs);
        }
        long studentCount = studentService.count(param);
        long teacherCount = teacherService.count(param);
        ljyh =studentCount+teacherCount;
        //昨日启动次数(教师和家长)
        param.clear();
        param.put("types",Arrays.asList(new Integer[]{SundayCommonConstants.data_type_index_start_parent,
                SundayCommonConstants.data_type_index_start_teacher}));
        param.put("ct_start", TimeUtils.formatToTime(yesterday_start.getTime()));
        param.put("ct_end", TimeUtils.formatToTime(yesterday_end.getTime()));
        //2018年12月5日，增加登陆人员的园区ID
        if(ListUtil.isNotNull(guardIs)){
            param.put("guardIds",guardIs);
        }
        Serializable zrqdcs_count = dataMapper.count(param);
        if(zrqdcs_count!=null){
            zrqdcs=Long.valueOf(zrqdcs_count.toString());
        }
        //昨日活跃用户（点击了两次及以上的）
        param.clear();
        //2018年12月5日，增加登陆人员的园区ID
        if(ListUtil.isNotNull(guardIs)){
            param.put("guardIds",guardIs);
        }
        param.put("ct_start", TimeUtils.formatToTime(yesterday_start.getTime()));
        param.put("ct_end", TimeUtils.formatToTime(yesterday_end.getTime()));
        param.put("times",2);
        datas.clear();
        datas = dataMapper.countHyMember(param);
        if(ListUtil.isNotNull(datas)){
            zrhyyh=datas.size();
        }

        //昨日新用户
        param.clear();
        param.put("ct >=",yesterday_start.getTimeInMillis());
        param.put("ct <=",yesterday_end.getTimeInMillis());
        //2018年12月5日，增加登陆人员的园区ID
        if(ListUtil.isNotNull(guardIs)){
            param.put("guardId in",guardIs);
        }
        studentCount = studentService.count(param);
        teacherCount = teacherService.count(param);
        zrxyh =studentCount+teacherCount;
        //昨日新用户占活跃度比
        if(zrhyyh>0){
            zrxyhzhydb=Double.valueOf(zrxyh)/zrhyyh;
        }
        //昨日人均启动次数
        param.clear();
        datas.clear();
        //2018年12月5日，增加登陆人员的园区ID
        if(ListUtil.isNotNull(guardIs)){
            param.put("guardIds",guardIs);
        }
        param.put("ct_start", TimeUtils.formatToTime(yesterday_start.getTime()));
        param.put("ct_end", TimeUtils.formatToTime(yesterday_end.getTime()));
        param.put("types",Arrays.asList(new Integer[]{SundayCommonConstants.data_type_index_start_parent,
                SundayCommonConstants.data_type_index_start_teacher}));
        datas = dataMapper.countHyMember(param);
        if(ListUtil.isNotNull(datas)){
            long members = datas.size();
            long times = 0;
            for(Map<String,Object> data:datas){
                times += (Long)data.get("times");
            }
            if(LongUtil.isNotZreo(times)){
                zrrjqdcs =Double.valueOf(members)/Double.valueOf(times);
            }
        }





        result.put("ljyh",ljyh);
        result.put("zrqdcs",zrqdcs);
        result.put("zrhyyh",zrhyyh);
        result.put("zrxyh",zrxyh);
        result.put("zrxyhzhydb",new BigDecimal(zrxyhzhydb*100).setScale(2,BigDecimal.ROUND_HALF_UP)+"%");
        result.put("zrrjqdcs",new BigDecimal(zrrjqdcs).setScale(2,BigDecimal.ROUND_HALF_UP));

        return result;
    }
    /**
     * 应用概况（今日，昨日，30日）
     * @return
     */
    public List<Map<String,Object>> count1(){
        List<Long> guardIs = getGuardIds();
        List<Map<String,Object>> results = new ArrayList<>();
        Map<String,Object> param = new HashedMap();

        for(int j = 0;j<3;j++){
            Calendar date_start = Calendar.getInstance();
            Calendar date_end = Calendar.getInstance();
            String name="";
            //今日（单日）
            if(j==0){
                date_start.set(Calendar.HOUR_OF_DAY,0);
                date_start.set(Calendar.MINUTE,0);
                date_start.set(Calendar.SECOND,0);
                date_end.set(Calendar.HOUR_OF_DAY,23);
                date_end.set(Calendar.MINUTE,59);
                date_end.set(Calendar.SECOND,59);
                name="今日";
           //昨日（单日）
            }else if(j==1){
                date_start.add(Calendar.DAY_OF_MONTH,-1);
                date_start.set(Calendar.HOUR_OF_DAY,0);
                date_start.set(Calendar.MINUTE,0);
                date_start.set(Calendar.SECOND,0);
                date_end.add(Calendar.DAY_OF_MONTH,-1);
                date_end.set(Calendar.HOUR_OF_DAY,23);
                date_end.set(Calendar.MINUTE,59);
                date_end.set(Calendar.SECOND,59);
                name="昨日";
           //30日(累计)
            }else if(j==2){
                date_start.add(Calendar.DAY_OF_MONTH,-30);
                date_start.set(Calendar.HOUR_OF_DAY,0);
                date_start.set(Calendar.MINUTE,0);
                date_start.set(Calendar.SECOND,0);

                date_end.set(Calendar.HOUR_OF_DAY,23);
                date_end.set(Calendar.MINUTE,59);
                date_end.set(Calendar.SECOND,59);
                name="30日";
            }

            //今日、昨日、30日数据
            param.clear();
            //2018年12月5日，增加登陆人员的园区ID
            if(ListUtil.isNotNull(guardIs)){
                param.put("guardId in",guardIs);
            }
            param.put("ct >=",date_start.getTimeInMillis());
            param.put("ct <=",date_end.getTimeInMillis());
            long new_students = studentService.count(param);
            long new_teachers = teacherService.count(param);
            param.clear();
            //2018年12月5日，增加登陆人员的园区ID
            if(ListUtil.isNotNull(guardIs)){
                param.put("guardIds",guardIs);
            }
            param.put("ct_start",TimeUtils.formatToTime(date_start.getTime()));
            param.put("ct_end",TimeUtils.formatToTime(date_end.getTime()));
            List<SundayData> datas = dataMapper.select(param);
            //活跃用户
            param.clear();
            //2018年12月5日，增加登陆人员的园区ID
            if(ListUtil.isNotNull(guardIs)){
                param.put("guardIds",guardIs);
            }
            param.put("ct_start", TimeUtils.formatToTime(date_start.getTime()));
            param.put("ct_end", TimeUtils.formatToTime(date_end.getTime()));
            param.put("times",2);
            //2018年12月5日，增加登陆人员的园区ID
            if(ListUtil.isNotNull(guardIs)){
                param.put("guardIds",guardIs);
            }

            List<Map<String,Object>> hys = dataMapper.countHyMember(param);

            int qdcs  = 0;//启动次数
            int hyyh  = hys.size();//活跃用户
            long xyh =new_students+new_teachers;//新用户 1
            double xyhzhydb = 0.00;//新用户占活跃度比
            double cjsysc = 0.00;//次均使用时长
            double rjsysc = 0.00;//人均使用时长
            double hyd = 0.00;//活跃度
            //计算需要的变量
            double hours=0;//累计时长
            long ljyh = 0;//累计用户
            param.clear();
            //2018年12月5日，增加登陆人员的园区ID
            if(ListUtil.isNotNull(guardIs)){
                param.put("guardIds",guardIs);
            }
            long student_count = studentService.count(param);
            long teacher_count = teacherService.count(param);
            ljyh=student_count+teacher_count;




            if(hyyh > 0){
                xyhzhydb = xyh/hyyh;//新用户占活跃度比
            }
            for(int i=0;i<datas.size();i++){
                SundayData data = datas.get(i);
                if(data.getType().equals(1)||data.getType().equals(2)){
                    qdcs++;//启动次数
                }
            }

            if(ListUtil.isNotNull(datas)){
                hours = (datas.get(datas.size()-1).getCt().getTime()-datas.get(0).getCt().getTime())/1000/60/60;
            }
            if(qdcs >0){
                cjsysc =Double.valueOf(hours)/Double.valueOf(qdcs);//次均使用时长=时长/次数
            }
            if(hyyh >0){
                rjsysc =Double.valueOf(hours)/Double.valueOf(hyyh);//人均使用时长=时长/活跃用户
            }
            if(ljyh>0){
                hyd=Double.valueOf(hyyh)/Double.valueOf(ljyh);//活跃度=活跃人数/累计人数
            }
            Map<String,Object> result  = new HashMap<String,Object>();
            result.put("name",name);
            result.put("qdcs",qdcs);
            result.put("hyyh",hyyh);
            result.put("xyh",xyh);
            result.put("xyhzhydb",new BigDecimal(xyhzhydb*100).setScale(2,BigDecimal.ROUND_HALF_UP)+"%");
            result.put("cjsysc",new BigDecimal(cjsysc).setScale(2,BigDecimal.ROUND_HALF_UP));
            result.put("rjsysc",new BigDecimal(rjsysc).setScale(2,BigDecimal.ROUND_HALF_UP));
            result.put("hyd",new BigDecimal(hyd*100).setScale(2,BigDecimal.ROUND_HALF_UP)+"%");
            results.add(result);
        }



        return results;
    }

    /**
     * 时段分析（24小时）（今日，昨日，上周今日，时段平均）
     * @param queryType 1新用户，2,活跃用户，3，启动次数
     * @return
     */
    public Map<String,Object> count2(Integer queryType){
        //2018年12月5日，增加登陆人员的园区ID
        List<Long> guardIs = getGuardIds();
        Map<String,Object> result =new HashedMap();
        Map<String,Object> param =new HashedMap();
        List<String> categories = new ArrayList<>();//x轴，24小时。
        List<Integer> hours = new ArrayList<>();//24个小时
        List<Calendar> calendars = new ArrayList<>();
        Calendar calendar1 = Calendar.getInstance();
        calendars.add(calendar1);
        Calendar calendar2 = Calendar.getInstance();
        calendar2.add(Calendar.DAY_OF_MONTH,-1);
        calendars.add(calendar2);
        Calendar calendar3 = Calendar.getInstance();
        calendar3.add(Calendar.DAY_OF_MONTH,-7);
        calendars.add(calendar3);

        for(int i=0;i<24;i++){
            String hour ="";
            if(i<10){
                hour="0"+i;
            }else{
                hour=i+"";
            }
            hour+=" 00";
            categories.add(hour);
            hours.add(i);
        }
        List<Long> guardIds = getGuardIds();

        List<Map<String,Object>> series = new ArrayList<>();//Y周数据
        String title="";
        //新用户
        if(queryType==1){
            title="新用户";
            //今日，昨日，上周昨日
            for(int i=0;i<calendars.size();i++){
                Calendar calendar = calendars.get(i);
                if(ListUtil.isNotNull(guardIs)){
                    param.put("guardId in",guardIs);
                }
                param.put("ct >=",DatetUtil.setDateStart(calendar.getTime()).getTime());
                param.put("ct <=",DatetUtil.setDateEnd(calendar.getTime()).getTime());
                List<SundayTeacher> teachers=teacherService.list(param);
                List<SundayStudent> students=studentService.list(param);
                //Y周数据-名称
                Map<String,Object> serie = new HashMap<>();
                if(i==0){
                    serie.put("name","今日");
                }else if(i==1){
                    serie.put("name","昨日");
                }else if(i==2){
                    serie.put("name","上周今日");
                }

                //y周数据-数据
                List<Integer> datas = new ArrayList<>();
                for(Integer hour:hours){
                    int data = 0;
                    for (SundayTeacher teacher:teachers){
                        Calendar cal = Calendar.getInstance();
                        cal.setTimeInMillis(teacher.getCt());
                        if(hour.equals(cal.get(Calendar.HOUR_OF_DAY))){
                            data++;
                        }
                    }
                    for(SundayStudent student:students){
                        Calendar cal = Calendar.getInstance();
                        cal.setTimeInMillis(student.getCt());
                        if(hour.equals(cal.get(Calendar.HOUR_OF_DAY))){
                            data++;
                        }
                    }
                    datas.add(data);
                }
                serie.put("data",datas);
                series.add(serie);
            }
        }
        //活跃用户
        if(queryType==2){
            title="活跃用户";
            //今日，昨日，上周昨日
            for(int i=0;i<calendars.size();i++){
                Calendar calendar = calendars.get(i);
                if(ListUtil.isNotNull(guardIs)){
                    param.put("guardId in",guardIs);
                }
                param.put("ct_start",TimeUtils.formatToTime(DatetUtil.setDateStart(calendar.getTime())));
                param.put("ct_end",TimeUtils.formatToTime(DatetUtil.setDateEnd(calendar.getTime())));
                List<SundayData> sundayDatas=dataMapper.select(param);
                //Y周数据-名称
                Map<String,Object> serie = new HashMap<>();
                if(i==0){
                    serie.put("name","今日");
                }else if(i==1){
                    serie.put("name","昨日");
                }else if(i==2){
                    serie.put("name","上周今日");
                }
                //y周数据-数据
                List<Integer> datas = new ArrayList<>();
                for(Integer hour:hours){
                    int data = 0;
                    for (SundayData sundayData:sundayDatas){
                        Calendar cal = Calendar.getInstance();
                        cal.setTimeInMillis(sundayData.getCt().getTime());
                        if(hour.equals(cal.get(Calendar.HOUR_OF_DAY))){
                            data++;
                        }
                    }
                    datas.add(data);
                }
                serie.put("data",datas);
                series.add(serie);
            }
        }

        if(queryType==3){
            title="启动次数";
            //今日，昨日，上周昨日
            for(int i=0;i<calendars.size();i++){
                Calendar calendar = calendars.get(i);
                if(ListUtil.isNotNull(guardIs)){
                    param.put("guardId in",guardIs);
                }
                param.put("types",Arrays.asList(new Integer[]{1,2}));
                param.put("ct_start",TimeUtils.formatToTime(DatetUtil.setDateStart(calendar.getTime())));
                param.put("ct_end",TimeUtils.formatToTime(DatetUtil.setDateEnd(calendar.getTime())));
                List<SundayData> sundayDatas=dataMapper.select(param);
                //Y周数据-名称
                Map<String,Object> serie = new HashMap<>();
                if(i==0){
                    serie.put("name","今日");
                }else if(i==1){
                    serie.put("name","昨日");
                }else if(i==2){
                    serie.put("name","上周今日");
                }
                //y周数据-数据
                List<Integer> datas = new ArrayList<>();
                for(Integer hour:hours){
                    int data = 0;
                    for (SundayData sundayData:sundayDatas){
                        Calendar cal = Calendar.getInstance();
                        cal.setTimeInMillis(sundayData.getCt().getTime());
                        if(hour.equals(cal.get(Calendar.HOUR_OF_DAY))){
                            data++;
                        }
                    }
                    datas.add(data);
                }
                serie.put("data",datas);
                series.add(serie);
            }
        }
        result.put("categories", JSON.toJSONString(categories));
        result.put("series", JSON.toJSONString(series));
        return  result;
    }
    /**
     * 获取周活跃用户、月活用户、总用户
     * @param year
     * @param month
     * @param guardId
     * @return
     * @throws Exception
     */
    public Map<String,Object> count103(Integer year,Integer month,Long guardId) throws  Exception {
        Map<String, Object> result = new HashedMap();
        Map<String, Object> param = new HashedMap();
        List<String> categories = new ArrayList<>();//x轴，1-5周
        List<Map<String,Object>> series = new ArrayList<>();//Y周数据
        List<Long> guardIds = getGuardIds();


        //获取某一月几周
        List<Map<String, Object>> weeks = WeekUtil.getWeeks(year, month);


        param.clear();
        if(ListUtil.isNotNull(guardIds)){
            param.put("guardId in",guardIds);
        }
        if(LongUtil.isNotZreo(guardId)){
            param.put("guardId",guardId);
        }

        param.put("ct_start",(String)weeks.get(0).get("weekStart"));
        param.put("ct_end",(String)weeks.get(weeks.size()-1).get("weekEnd"));
        param.put("times",8);
       param.put("types",Arrays.asList(new Integer[]{1,2}));
        //查询月活用户
        long monthHy = 0;//月活用户数量
        List<Map<String,Object>> monthDatas = dataMapper.countHyMember(param);
        if(ListUtil.isNotNull(monthDatas)){
            monthHy=monthDatas.size();
        }
        List<Long> data1 = new ArrayList<>();//总用户数
        List<Long> data2 = new ArrayList<>();//本周活跃用户
        List<Long> data3 = new ArrayList<>();//月活跃用户
        for(Map<String,Object> week:weeks){

            long weekHy = 0;//周活人数
            String name = (String)week.get("name");
            categories.add(name);
            param.clear();
            if(ListUtil.isNotNull(guardIds)){
                param.put("guardId in",guardIds);
            }
            if(LongUtil.isNotZreo(guardId)){
                param.put("guardId",guardId);
            }
            param.put("ct_start",(String)week.get("weekStart"));
            param.put("ct_end",(String)week.get("weekEnd"));
            param.put("times",2);
            param.put("types",Arrays.asList(new Integer[]{1,2}));
            List<Map<String,Object>> weekDatas = dataMapper.countHyMember(param);//周活数量
            if(ListUtil.isNotNull(weekDatas)){
                weekHy=weekDatas.size();
            }
            param.clear();
            //本周人数
            long weekNum = 0;//本周用户
            if(ListUtil.isNotNull(guardIds)){
                param.put("guardId in",guardIds);
            }
            if(LongUtil.isNotZreo(guardId)){
                param.put("guardId",guardId);
            }
            param.put("ct >=",(Long)week.get("weekStartLong"));
            param.put("ct <=",(Long)week.get("weekEndLong"));
            weekNum = studentService.count(param)+teacherService.count(param);
            data1.add(weekNum);
            data2.add(weekHy);
            data3.add(monthHy);

        }
        Map<String,Object> series1 = new HashMap<>();
        series1.put("name","总用户数");
        series1.put("data",data1);
        series.add(series1);

        Map<String,Object> series2 = new HashMap<>();
        series2.put("name","周活用户数");
        series2.put("data",data2);
        series.add(series2);

        Map<String,Object> series3 = new HashMap<>();
        series3.put("name","月活用户数");
        series3.put("data",data3);
        series.add(series3);
        result.put("categories", JSON.toJSONString(categories));
        result.put("series",JSON.toJSONString(series));
        return result;
     }
    /**
     * 导出周活跃用户、月活用户、总用户
     * @param year
     * @param month
     * @param guardId
     * @return
     * @throws Exception
     */
    public String daochu103(Integer year,Integer month,Long guardId)throws  Exception{
        List<Map<String,Object>> results = new ArrayList<>();//Y周数据
        Map<String, Object> param = new HashedMap();
        List<Long> guardIds = getGuardIds();//登陆用户园区
        //获取某一月几周
        List<Map<String, Object>> weeks = WeekUtil.getWeeks(year, month);

        param.clear();
        if(ListUtil.isNotNull(guardIds)){
            param.put("guardId in",guardIds);
        }
        if(LongUtil.isNotZreo(guardId)){
            param.put("guardId",guardId);
        }
        param.put("ct_start",(String)weeks.get(0).get("weekStart"));
        param.put("ct_end",(String)weeks.get(weeks.size()-1).get("weekEnd"));
        param.put("times",8);
       param.put("types",Arrays.asList(new Integer[]{1,2}));
        //查询月活用户
        long monthHy = 0;//月活用户数量
        List<Map<String,Object>> monthDatas = dataMapper.countHyMember(param);
        if(ListUtil.isNotNull(monthDatas)){
            monthHy=monthDatas.size();
        }

        for(Map<String,Object> week:weeks){
            long weekHy = 0;//周活人数
            param.clear();
            if(ListUtil.isNotNull(guardIds)){
                param.put("guardId in",guardIds);
            }
            if(LongUtil.isNotZreo(guardId)){
                param.put("guardId",guardId);
            }
            param.put("ct_start",(String)week.get("weekStart"));
            param.put("ct_end",(String)week.get("weekEnd"));
            param.put("times",2);
            param.put("types",Arrays.asList(new Integer[]{1,2}));
            List<Map<String,Object>> weekDatas = dataMapper.countHyMember(param);//周活数量
            if(ListUtil.isNotNull(weekDatas)){
                weekHy=weekDatas.size();
            }
            param.clear();
            //本周人数
            long weekNum = 0;//本周用户
            if(ListUtil.isNotNull(guardIds)){
                param.put("guardId in",guardIds);
            }
            if(LongUtil.isNotZreo(guardId)){
                param.put("guardId",guardId);
            }
            param.put("ct >=",(Long)week.get("weekStartLong"));
            param.put("ct <=",(Long)week.get("weekEndLong"));
            weekNum = studentService.count(param)+teacherService.count(param);
            Map<String,Object> result = new HashMap<>();
            result.put("date", (String)week.get("name"));
            result.put("weekNum", weekNum);
            result.put("weekHy", weekHy);
            result.put("monthHy", monthHy);
            results.add(result);
        }


        String fileName = "周-月活跃数据("+year+"年"+month+"月)"+new Date().getTime();

        String guardName="全部园区";
        if(LongUtil.isNotZreo(guardId)){
            guardName=guardService.getById(guardId).getName();
        }

        String sheetName="sheet1";

        //excel中对应的标题
        List<String> titlesArray = new ArrayList<String>();
        //商品属性中标题对应的字段
        List<String> fieldsArray = new ArrayList<String>();
        fieldsArray.add("date");
        titlesArray.add(guardName);

        fieldsArray.add("weekNum");
        titlesArray.add("总用户数量");

        fieldsArray.add("weekHy");
        titlesArray.add("周活跃人数");

        fieldsArray.add("monthHy");
        titlesArray.add("月活跃用户");



        String [] fields = (String [])fieldsArray.toArray((new String[fieldsArray.size()]));
        String [] titles = (String [])titlesArray.toArray((new String[titlesArray.size()]));
        //配置文件输出流
        //System.out.println("文件位置"+outFile.;);
        //设置文件位置
        fileName=fileName+".xls";
        String downloadPath=resourceHolder.getStringValue("fileUploadPath");
        String savePath="/excel/"+fileName;
        String filePath=downloadPath+savePath;

        //String resultPath=File.separator+"excel"+File.separator+fileName;
        try {
            OutputStream out = new FileOutputStream(new File(filePath));
            ExcelUtils.exportExcelFile(sheetName, null, titles, fields, results, out);
        }catch (Exception e){
            e.printStackTrace();
        }finally {
            logger.info("忽略-----FileNotFoundException");
        }
        String resultPath =resourceHolder.getStringValue("webRoot")+"/upload/"+savePath;
        logger.info("---------resultPath--"+resultPath);
        return resultPath;
    }
    /**
     * 总体数据(所有的)
     * @param ct_start
     * @param ct_end
     * @return
     */
    public Map<String,Object> count100(Date ct_start, Date ct_end){
        //2018年12月5日，增加登陆人员的园区ID
        List<Long> guardIs = getGuardIds();
        Map<String,Object> param = new HashedMap();
        Map<String,Object> result = new HashedMap();

        //2018年12月5日，增加登陆人员的园区ID
        if(ListUtil.isNotNull(guardIs)){
            param.put("guardIds",guardIs);
        }
        param.put("types",Arrays.asList(new Integer[]{1,2}));
        param.put("ct_start", TimeUtils.formatToTime(DatetUtil.setDateStart(ct_start)));
        param.put("ct_end", TimeUtils.formatToTime(DatetUtil.setDateEnd(ct_end)));

        List<SundayData> datas = dataMapper.select(param);

        int qdzrs  = 0;//启动总人数
        int qdzcs  = 0;//启动总次数
        double rjqdcs = 0.00;//人均启动次数
        double cjsysc = 0.00;//次均使用时长
        int jzqdrs = 0;//家长启动人数
        int jzqdcs = 0;//家长启动次数
        int jgqdrs = 0;//教工启动人数
        int jgqdcs = 0;//教工启动次数
        double hours = 0;//数据总共有几个小时。
        //2019年2月25日，增加在园总人数，在园家长人数，在园教工人数
        long zyzrs = 0;//增加在园总人数
        long zyjzrs = 0;//在园家长人数
        long zyjgrs = 0;//在园教工人数
        Set<Long> memberIds = new HashSet<>();
        for(int i = 0;i<datas.size();i++ ){
            SundayData data = datas.get(i);
            memberIds.add(data.getMemberId());

        }
        hours = (datas.get(datas.size()-1).getCt().getTime()-datas.get(0).getCt().getTime())/1000/60/60;
        //0，登陆页，1，首页、启动（家长端），2首页、启动（教师端），3教学计划，4一周英语，5一周食谱，6一日作息，7园区简介，8宝贝画面，9园区通知，10班级圈，11我的）

        //1,统计次数
        for(SundayData data:datas){
                qdzcs++;//启动总次数
                if (data.getType() == 1) {
                    jzqdcs++;//家长启动次数
                }
                if (data.getType() == 2) {
                    jgqdcs++;//教工启动次数
                }
        }
        //2,统计人数
        for (Long memberId : memberIds) {
            for (SundayData data : datas) {
                if (LongUtil.isNotZreo(data.getMemberId()) &&
                        data.getMemberId().equals(memberId)) {
                    qdzrs++;//启动总人数
                    if (data.getType() == 1) {
                        jzqdrs++;//家长启动人数
                    }
                    if (data.getType() == 2) {
                        jgqdrs++;//教工启动人数
                    }
                    break;
                }
            }
        }
        if(qdzrs > 0){
            rjqdcs = qdzcs/qdzrs;//人均启动次数=总启动人数/启动总次数
        }
        if(hours > 0.00){
            cjsysc = qdzcs/hours;//次均使用时长=启动总次数/总时长
        }
        //2019年2月25日，增加在园总人数，在园家长人数，在园教工人数
        Map<String,Long> guardData = guardDataService.countGuardData(ct_start,ct_end,null);

        zyjzrs=guardData.get("parents");//在园家长人数
        zyjgrs = guardData.get("teachers");//在园教工人数
        zyzrs = zyjzrs+zyjgrs;//在园总人数





        result.put("qdzrs",qdzrs);
        result.put("qdzcs",qdzcs);
        result.put("rjqdcs",new BigDecimal(rjqdcs).setScale(2,BigDecimal.ROUND_HALF_UP));
        result.put("cjsysc",new BigDecimal(cjsysc).setScale(2,BigDecimal.ROUND_HALF_UP));
        result.put("jzqdrs",jzqdrs);
        result.put("jzqdcs",jzqdcs);
        result.put("jgqdrs",jgqdrs);
        result.put("jgqdcs",jgqdcs);

        result.put("zyzrs",zyzrs);
        result.put("zyjzrs",zyjzrs);
        result.put("zyjgrs",zyjgrs);
        return result;
    }

    /**
     *  总体数据(所有的) 导出
     * @param ct_start
     * @param ct_end
     */
    public String daochu100(Date ct_start, Date ct_end){
        Map<String,Object> result = count100(ct_start,ct_end);
        String date = TimeUtils.formatToDateCn(ct_start)+"-"+TimeUtils.formatToDateCn(ct_end);
        result.put("date",date);
        List< Map<String,Object>> results = new ArrayList<>();
        results.add(result);
        String fileName = "总体数据"+date;

        String sheetName="sheet1";

        //excel中对应的标题
        List<String> titlesArray = new ArrayList<String>();
        //商品属性中标题对应的字段
        List<String> fieldsArray = new ArrayList<String>();
        fieldsArray.add("date");
        titlesArray.add("日期");

        fieldsArray.add("zyzrs");
        titlesArray.add("在园总人数");

        fieldsArray.add("zyjzrs");
        titlesArray.add("在园家长人数");

        fieldsArray.add("zyjgrs");
        titlesArray.add("在园教工人数");



        fieldsArray.add("qdzrs");
        titlesArray.add("启动总人数");

        fieldsArray.add("qdzcs");
        titlesArray.add("启动总次数");

        fieldsArray.add("cjsysc");
        titlesArray.add("次均使用时长");

        fieldsArray.add("jzqdrs");
        titlesArray.add("家长启动人数");

        fieldsArray.add("jzqdcs");
        titlesArray.add("家长启动人数");

        fieldsArray.add("jzqdcs");
        titlesArray.add("家长启动次数");

        fieldsArray.add("jgqdrs");
        titlesArray.add("教工启动人数");

        fieldsArray.add("jgqdcs");
        titlesArray.add("教工启动次数");

        String [] fields = (String [])fieldsArray.toArray((new String[fieldsArray.size()]));
        String [] titles = (String [])titlesArray.toArray((new String[titlesArray.size()]));
        //配置文件输出流
        //System.out.println("文件位置"+outFile.;);
        //设置文件位置
        fileName=fileName+".xls";
        String downloadPath=resourceHolder.getStringValue("fileUploadPath");
        String savePath="/excel/"+fileName;
        String filePath=downloadPath+savePath;

        //String resultPath=File.separator+"excel"+File.separator+fileName;
        try {
            OutputStream out = new FileOutputStream(new File(filePath));
            ExcelUtils.exportExcelFile(sheetName, null, titles, fields, results, out);
        }catch (Exception e){
            e.printStackTrace();
        }finally {
            logger.info("忽略-----FileNotFoundException");
        }
        String resultPath =resourceHolder.getStringValue("webRoot")+"/upload/"+savePath;
        logger.info("---------resultPath--"+resultPath);

        return resultPath;

    }

    /**
     * 总体数据(按园区分组的)
     * @param ct_start
     * @param ct_end
     * @return
     */
    public List<Map<String,Object>> count101(Date ct_start, Date ct_end) throws  Exception{
        //2018年12月5日，增加登陆人员的园区ID
        List<Long> guardIs = getGuardIds();
        List<Map<String,Object>> results = new ArrayList<>();
        Map<String,Object> param = new HashedMap();
        param.put("types",Arrays.asList(new Integer[]{1,2}));
        param.put("ct_start", TimeUtils.formatToTime(DatetUtil.setDateStart(ct_start)));
        param.put("ct_end", TimeUtils.formatToTime(DatetUtil.setDateEnd(ct_end)));

        //2018年12月5日，增加登陆人员的园区ID
        if(ListUtil.isNotNull(guardIs)){
            param.put("guardIds",guardIs);
        }
        List<SundayData> datas = dataMapper.select(param);
       // Set<Long> guardIds = new HashSet<>();
        Set<Long> memberIds = new HashSet<>();
        for(int i = 0;i<datas.size();i++ ){
            SundayData data = datas.get(i);
           // guardIds.add(data.getGuardId());
            memberIds.add(data.getMemberId());
        }
        param.clear();
        //param.put("id in",guardIds);
    //   List<SundayGuard> guards =  new ArrayList<>();
        List<SundayGuard> guards =  guardService.list(param);
        /*if(guardIds.size()>0){
            param.clear();
            param.put("id in",guardIds);
            guards = guardService.list(param);
        }*/
        //按园区分组
       // for(Long guardId:guardIds){
        for(SundayGuard guard:guards){
            Long guardId = guard.getId();
            String guardName = guard.getName();
            Map<String,Object> result = new HashedMap();
            int qdzrs  = 0;//启动总人数
            int qdzcs  = 0;//启动总次数
            double rjqdcs = 0.00;//人均启动次数
            double cjsysc = 0.00;//次均使用时长
            int jzqdrs = 0;//家长启动人数
            int jzqdcs = 0;//家长启动次数
            int jgqdrs = 0;//教工启动人数
            int jgqdcs = 0;//教工启动次数
            double hours = 0;//数据总共有几个小时。
            //2019年2月25日，增加在园总人数，在园家长人数，在园教工人数
            long zyzrs = 0;//增加在园总人数
            long zyjzrs = 0;//在园家长人数
            long zyjgrs = 0;//在园教工人数



            //统计次数
            for(int i = 0;i<datas.size();i++ ){
                SundayData data =datas.get(i);
                if (LongUtil.isNotZreo(data.getGuardId()) &&
                        data.getGuardId().equals(guardId)) {
                    qdzcs++;//启动总次数
                    if (data.getType() == 1) {
                        jzqdcs++;//家长启动次数
                    }
                    if (data.getType() == 2) {
                        jgqdcs++;//教工启动次数
                    }
                    /*if(i<datas.size()-1){
                        SundayData data1 = datas.get(i+1);
                        hours+=(data1.getCt().getTime()-data.getCt().getTime())/1000/60/60;
                    }*/
                }
            }
            hours = (datas.get(datas.size()-1).getCt().getTime()-datas.get(0).getCt().getTime())/1000/60/60;
            //2,统计人数
            for (Long memberId : memberIds) {
                for (SundayData data : datas) {
                    if(LongUtil.isNotZreo(data.getGuardId()) &&
                            data.getGuardId().equals(guardId)&&
                            data.getMemberId().equals(memberId)) {
                        qdzrs++;//启动总人数
                        if (data.getType() == 1) {
                            jzqdrs++;//家长启动人数
                        }
                        if (data.getType() == 2) {
                            jgqdrs++;//教工启动人数
                        }
                        break;
                    }
                }
            }

            if(qdzrs > 0){
                rjqdcs = qdzcs/qdzrs;//人均启动次数
            }
            if(hours > 0.00){
                cjsysc = qdzcs/hours;//次均使用时长
            }
           /* String guardName = "";
            for(SundayGuard guard:guards){
                if(guardId.equals(guard.getId())){
                    guardName=guard.getName();
                    break;
                }
            }*/
            //2019年2月25日，增加在园总人数，在园家长人数，在园教工人数
            //2019年2月25日，增加在园总人数，在园家长人数，在园教工人数
            Map<String,Long> guardData = guardDataService.countGuardData(ct_start,ct_end,guardId);
            zyjzrs=guardData.get("parents");//在园家长人数
            zyjgrs = guardData.get("teachers");//在园教工人数
            zyzrs = zyjzrs+zyjgrs;//在园总人数

            result.put("guardName",guardName);
            result.put("qdzrs",qdzrs);
            result.put("qdzrs",qdzrs);
            result.put("qdzcs",qdzcs);
            result.put("rjqdcs",new BigDecimal(rjqdcs).setScale(2,BigDecimal.ROUND_HALF_UP));
            result.put("cjsysc",new BigDecimal(cjsysc).setScale(2,BigDecimal.ROUND_HALF_UP));
            result.put("jzqdrs",jzqdrs);
            result.put("jzqdcs",jzqdcs);
            result.put("jgqdrs",jgqdrs);
            result.put("jgqdcs",jgqdcs);

            result.put("zyzrs",zyzrs);
            result.put("zyjzrs",zyjzrs);
            result.put("zyjgrs",zyjgrs);
            results.add(result);
        }


        return results;
    }
    public String daochu101(Date ct_start, Date ct_end)throws  Exception{
        List<Map<String,Object>> results = count101(ct_start,ct_end);
        String date = TimeUtils.formatToDateCn(ct_start)+"-"+TimeUtils.formatToDateCn(ct_end);

        String fileName = "园区总体数据"+date+new Date().getTime();

        String sheetName="sheet1";

        //excel中对应的标题
        List<String> titlesArray = new ArrayList<String>();
        //商品属性中标题对应的字段
        List<String> fieldsArray = new ArrayList<String>();
        fieldsArray.add("guardName");
        titlesArray.add(date);
        
        fieldsArray.add("zyzrs");
        titlesArray.add("在园总人数");

        fieldsArray.add("zyjzrs");
        titlesArray.add("在园家长人数");

        fieldsArray.add("zyjgrs");
        titlesArray.add("在园教工人数");

        fieldsArray.add("qdzrs");
        titlesArray.add("启动总人数");

        fieldsArray.add("qdzcs");
        titlesArray.add("启动总次数");

        fieldsArray.add("rjqdcs");
        titlesArray.add("人均启动次数");

        fieldsArray.add("cjsysc");
        titlesArray.add("次均使用时长");

        fieldsArray.add("jzqdrs");
        titlesArray.add("家长启动人数");

        fieldsArray.add("jzqdcs");
        titlesArray.add("家长启动次数");

        fieldsArray.add("jgqdrs");
        titlesArray.add("教工启动人数");

        fieldsArray.add("jgqdcs");
        titlesArray.add("教工启动次数");

        String [] fields = (String [])fieldsArray.toArray((new String[fieldsArray.size()]));
        String [] titles = (String [])titlesArray.toArray((new String[titlesArray.size()]));
        //配置文件输出流
        //System.out.println("文件位置"+outFile.;);
        //设置文件位置
        fileName=fileName+".xls";
        String downloadPath=resourceHolder.getStringValue("fileUploadPath");
        String savePath="/excel/"+fileName;
        String filePath=downloadPath+savePath;

        //String resultPath=File.separator+"excel"+File.separator+fileName;
        try {
            OutputStream out = new FileOutputStream(new File(filePath));
            ExcelUtils.exportExcelFile(sheetName, null, titles, fields, results, out);
        }catch (Exception e){
            e.printStackTrace();
        }finally {
            logger.info("忽略-----FileNotFoundException");
        }
        String resultPath =resourceHolder.getStringValue("webRoot")+"/upload/"+savePath;
        logger.info("---------resultPath--"+resultPath);

        return resultPath;

    }


    /**
     * 1日均访问数据（访问人数UV  访问次数PV），2，园区日均访问数据（访问人数UV  访问次数PV）
     * @param ct_start
     * @param ct_end
     * @return
     */
    public Map<String,Object> count102(Date ct_start, Date ct_end,Long guardId,Integer queryType)throws  Exception{
        //2018年12月5日，增加登陆人员的园区ID
        List<Long> guardIs = getGuardIds();
        Map<String,Object> result =new HashedMap();
        Map<String,Object> param = new HashedMap();
        //2018年12月5日，增加登陆人员的园区ID
        if(ListUtil.isNotNull(guardIs)){
            param.put("guardIds",guardIs);
        }

        if(LongUtil.isNotZreo(guardId)){
            param.put("guardId",guardId);
        }
        param.put("type",queryType);
        param.put("ct_start", TimeUtils.formatToTime(DatetUtil.setDateStart(ct_start)));
        param.put("ct_end", TimeUtils.formatToTime(DatetUtil.setDateEnd(ct_end)));

        List<SundayData> sundayDatas = dataMapper.select(param);
        List<String> dates = WeekUtil.getDates(ct_start,ct_end);
        Set<Long> memberIds = new HashSet<>();
        for(SundayData data:sundayDatas){
            memberIds.add(data.getMemberId());
        }
        List<Integer> data1s = new ArrayList<>();
        List<Integer> data2s = new ArrayList<>();
        String name1 = "";
        String name2 = "";
        String title = "";
        //标题
        if(queryType  == 0){
            title="登陆页日均访问数据";
            name1="登陆页日均访问次数";
            name2="登陆页日均访问人数";
        }else if(queryType == 1 || queryType == 2){
            title="首页日均访问数据";
            name1="首页日均访问次数";
            name2="首页日均访问人数";
        }else if(queryType == 3){
            title="教学计划日均访问数据";
            name1="教学计划日均访问次数";
            name2="教学计划日均访问人数";
        }else if(queryType == 4){
            title="一周英语日均访问数据";
            name1="一周英语日均访问次数";
            name2="一周英语日均访问人数";
        }else if(queryType == 5){
            title="一周食谱日均访问数据";
            name1="一周食谱日均访问次数";
            name2="一周食谱日均访问人数";
        }else if(queryType == 6){
            title="宝贝作品集日均访问数据";
            name1="宝贝作品集日均访问次数";
            name2="宝贝作品集日均访问人数";
        }else if(queryType == 7){
            title="一日作息日均访问数据";
            name1="一日作息日均访问次数";
            name2="一日作息日均访问人数";
        }else if(queryType == 8){
            title="园区简介日均访问数据";
            name1="园区简介日均访问次数";
            name2="园区简介日均访问人数";
        }else if(queryType == 9){
            title="宝贝画面日均访问数据";
            name1="宝贝画面日均访问次数";
            name2="宝贝画面日均访问人数";
        }else if(queryType == 10){
            title="通知日均访问数据";
            name1="通知日均访问次数";
            name2="通知日均访问人数";
        }else if(queryType == 11){
            title="班级圈日均访问数据";
            name1="班级圈日均访问次数";
            name2="班级圈日均访问人数";
        }else if(queryType == 12){
            title="我的均日访问数据";
            name1="我的日均访问次数";
            name2="我的日均访问人数";
        }

        for(String date:dates){

           // days.add(date);
            int loginUV  = 0;//登陆页UV
            int loginPV  = 0;//登陆页PV

            int indexUV  = 0;//首页UV
            int indexPV  = 0;//首页PV


            int planUV  = 0;//教学计划UV
            int planPV  = 0;//教学计划PV

            int englishUV  = 0;//教学计划UV
            int englishPV  = 0;//教学计划PV

            int foodUV  = 0;//一周食谱UV
            int foodPV  = 0;//一周食谱PV

            int opusUV  = 0;//宝贝作品集UV
            int opusPV  = 0;//宝贝作品集PV

            int dailyUV  = 0;//一日作息UV
            int dailyPV  = 0;//一日作息PV

            int guardUV  = 0;//园区简介UV
            int guardPV  = 0;//园区简介PV

            int cameraUV  = 0;//宝贝画面UV
            int cameraPV  = 0;//园区简介PV

            int noticeUV  = 0;//通知UV
            int noticePV  = 0;//通知PV

            int dynamicUV  = 0;//班级圈UV
            int dynamicPV  = 0;//班级圈PV

            int myUV  = 0;//我UV
            int myPV  = 0;//我VV
            //1,统计次数
            Set<Long> memberIds0 = new HashSet<>();
            Set<Long> memberIds1 = new HashSet<>();
            Set<Long> memberIds3 = new HashSet<>();
            Set<Long> memberIds4 = new HashSet<>();
            Set<Long> memberIds5 = new HashSet<>();
            Set<Long> memberIds6 = new HashSet<>();
            Set<Long> memberIds7 = new HashSet<>();
            Set<Long> memberIds8 = new HashSet<>();
            Set<Long> memberIds9 = new HashSet<>();
            Set<Long> memberIds10 = new HashSet<>();
            Set<Long> memberIds11 = new HashSet<>();
            Set<Long> memberIds12 = new HashSet<>();

            for(SundayData data:sundayDatas){
                int  type = data.getType();
                if(date.equalsIgnoreCase(data.getCreateDate())){
                    if(type  == 0){
                        loginPV++;
                        memberIds0.add(data.getMemberId());
                    }else if(type == 1 || type == 2){
                        indexPV++;
                        memberIds1.add(data.getMemberId());
                    }else if(type == 3){
                        planPV++;
                        memberIds3.add(data.getMemberId());
                    }else if(type == 4){
                        englishPV++;
                        memberIds4.add(data.getMemberId());
                    }else if(type == 5){
                        foodPV++;
                        memberIds5.add(data.getMemberId());
                    }else if(type == 6){
                        opusPV++;
                        memberIds6.add(data.getMemberId());
                    }else if(type == 7){
                        dailyPV++;
                        memberIds7.add(data.getMemberId());
                    }else if(type == 8){
                        guardPV++;
                        memberIds8.add(data.getMemberId());
                    }else if(type == 9){
                        cameraPV++;
                        memberIds9.add(data.getMemberId());
                    }else if(type == 10){
                        noticePV++;
                        memberIds10.add(data.getMemberId());
                    }else if(type == 11){
                        dynamicPV++;
                        memberIds11.add(data.getMemberId());
                    }else if(type == 12){
                        myPV++;
                        memberIds12.add(data.getMemberId());
                    }
                }
            }

            loginUV=memberIds0.size();
            indexUV=memberIds1.size();
            planUV=memberIds3.size();
            englishUV=memberIds4.size();
            foodUV=memberIds5.size();
            opusUV=memberIds6.size();
            dailyUV=memberIds7.size();
            guardUV=memberIds8.size();
            cameraUV=memberIds9.size();
            noticeUV=memberIds10.size();
            dynamicUV=memberIds11.size();
            myUV=memberIds12.size();


            if(queryType  == 0){
               data1s.add(loginPV);
               data2s.add(loginUV);
            }else if(queryType == 1 || queryType == 2){
                data1s.add(indexPV);
                data2s.add(indexUV);
            }else if(queryType == 3){
                data1s.add(planPV);
                data2s.add(planUV);
            }else if(queryType == 4){
                data1s.add(englishPV);
                data2s.add(englishUV);
            }else if(queryType == 5){
                data1s.add(foodPV);
                data2s.add(foodUV);
            }else if(queryType == 6){
                data1s.add(opusPV);
                data2s.add(opusUV);
            }else if(queryType == 7){
                data1s.add(dailyPV);
                data2s.add(dailyUV);
            }else if(queryType == 8){
                data1s.add(guardPV);
                data2s.add(guardUV);
            }else if(queryType == 9){
                data1s.add(cameraPV);
                data2s.add(cameraUV);
            }else if(queryType == 10){
                data1s.add(noticePV);
                data2s.add(noticeUV);
            }else if(queryType == 11){
                data1s.add(dynamicPV);
                data2s.add(dynamicUV);
            }else if(queryType == 12){
                data1s.add(myPV);
                data2s.add(myUV);
            }
        }
        List<Map<String,Object>> series = new ArrayList<>();
        Map<String,Object> serie1 =new HashMap<>();
        Map<String,Object> serie2 =new HashMap<>();
        serie1.put("name",name1);
        serie1.put("data",data1s);
        series.add(serie1);
        serie2.put("name",name2);
        serie2.put("data",data2s);
        series.add(serie2);



        result.put("dates",JSON.toJSONString(dates));
        result.put("title",title);
        result.put("series", JSON.toJSONString(series));
        return result;
    }

    /**
     * 导出
     * @param ct_start
     * @param ct_end
     * @param guardId
     * @return
     * @throws Exception
     */
    public String daochu102(Date ct_start, Date ct_end,Long guardId)throws  Exception{
        //2018年12月5日，增加登陆人员的园区ID
        List<Long> guardIs = getGuardIds();
        List<Map<String,Object>> results = new ArrayList<>();
        Map<String,Object> param = new HashedMap();

        //2018年12月5日，增加登陆人员的园区ID
        if(ListUtil.isNotNull(guardIs)){
            param.put("guardIds",guardIs);
        }
        if(LongUtil.isNotZreo(guardId)){
            param.put("guardId",guardId);
        }
        param.put("ct_start", TimeUtils.formatToTime(DatetUtil.setDateStart(ct_start)));
        param.put("ct_end", TimeUtils.formatToTime(DatetUtil.setDateEnd(ct_end)));
        List<SundayData> sundayDatas = dataMapper.select(param);
        List<String> dates = WeekUtil.getDates(ct_start,ct_end);
        Set<Long> memberIds = new HashSet<>();
        for(SundayData data:sundayDatas){
            memberIds.add(data.getMemberId());
        }
        String guardName="全部园区";
        if(LongUtil.isNotZreo(guardId)){
            guardName=guardService.getById(guardId).getName();
        }

        for(String date:dates){
            Map<String,Object> result = new HashMap<>();
            // days.add(date);
            int loginUV  = 0;//登陆页UV
            int loginPV  = 0;//登陆页PV

            int indexUV  = 0;//首页UV
            int indexPV  = 0;//首页PV


            int planUV  = 0;//教学计划UV
            int planPV  = 0;//教学计划PV

            int englishUV  = 0;//教学计划UV
            int englishPV  = 0;//教学计划PV

            int foodUV  = 0;//一周食谱UV
            int foodPV  = 0;//一周食谱PV

            int opusUV  = 0;//宝贝作品集UV
            int opusPV  = 0;//宝贝作品集PV

            int dailyUV  = 0;//一日作息UV
            int dailyPV  = 0;//一日作息PV

            int guardUV  = 0;//园区简介UV
            int guardPV  = 0;//园区简介PV

            int cameraUV  = 0;//宝贝画面UV
            int cameraPV  = 0;//园区简介PV

            int noticeUV  = 0;//通知UV
            int noticePV  = 0;//通知PV

            int dynamicUV  = 0;//班级圈UV
            int dynamicPV  = 0;//班级圈PV

            int myUV  = 0;//我UV
            int myPV  = 0;//我VV
            //1,统计次数
            Set<Long> memberIds0 = new HashSet<>();
            Set<Long> memberIds1 = new HashSet<>();
            Set<Long> memberIds3 = new HashSet<>();
            Set<Long> memberIds4 = new HashSet<>();
            Set<Long> memberIds5 = new HashSet<>();
            Set<Long> memberIds6 = new HashSet<>();
            Set<Long> memberIds7 = new HashSet<>();
            Set<Long> memberIds8 = new HashSet<>();
            Set<Long> memberIds9 = new HashSet<>();
            Set<Long> memberIds10 = new HashSet<>();
            Set<Long> memberIds11 = new HashSet<>();
            Set<Long> memberIds12 = new HashSet<>();

            for(SundayData data:sundayDatas){
                int  type = data.getType();
                if(date.equalsIgnoreCase(data.getCreateDate())){
                    if(type  == 0){
                        loginPV++;
                        memberIds0.add(data.getMemberId());
                    }else if(type == 1 || type == 2){
                        indexPV++;
                        memberIds1.add(data.getMemberId());
                    }else if(type == 3){
                        planPV++;
                        memberIds3.add(data.getMemberId());
                    }else if(type == 4){
                        englishPV++;
                        memberIds4.add(data.getMemberId());
                    }else if(type == 5){
                        foodPV++;
                        memberIds5.add(data.getMemberId());
                    }else if(type == 6){
                        opusPV++;
                        memberIds6.add(data.getMemberId());
                    }else if(type == 7){
                        dailyPV++;
                        memberIds7.add(data.getMemberId());
                    }else if(type == 8){
                        guardPV++;
                        memberIds8.add(data.getMemberId());
                    }else if(type == 9){
                        cameraPV++;
                        memberIds9.add(data.getMemberId());
                    }else if(type == 10){
                        noticePV++;
                        memberIds10.add(data.getMemberId());
                    }else if(type == 11){
                        dynamicPV++;
                        memberIds11.add(data.getMemberId());
                    }else if(type == 12){
                        myPV++;
                        memberIds12.add(data.getMemberId());
                    }
                }
            }

            loginUV=memberIds0.size();
            indexUV=memberIds1.size();
            planUV=memberIds3.size();
            englishUV=memberIds4.size();
            foodUV=memberIds5.size();
            opusUV=memberIds6.size();
            dailyUV=memberIds7.size();
            guardUV=memberIds8.size();
            cameraUV=memberIds9.size();
            noticeUV=memberIds10.size();
            dynamicUV=memberIds11.size();
            myUV=memberIds12.size();


            result.put("loginUV",loginUV);
            result.put("loginPV",loginPV);

            result.put("indexUV",indexUV);
            result.put("indexPV",indexPV);

            result.put("planUV",planUV);
            result.put("planPV",planPV);

            result.put("englishUV",englishUV);
            result.put("englishPV",englishPV);

            result.put("foodUV",foodUV);
            result.put("foodPV",foodPV);

            result.put("opusUV",opusUV);
            result.put("opusPV",opusPV);

            result.put("dailyUV",dailyUV);
            result.put("dailyPV",dailyPV);

            result.put("guardUV",guardUV);
            result.put("guardPV",guardPV);

            result.put("cameraUV",cameraUV);
            result.put("cameraPV",cameraPV);

            result.put("noticeUV",noticeUV);
            result.put("noticePV",noticePV);

            result.put("dynamicUV",dynamicUV);
            result.put("dynamicPV",dynamicPV);
            result.put("myUV",myUV);
            result.put("myPV",myPV);
            result.put("date",date);
            results.add(result);
        }
        //计算最后一列合计
        int loginUV  = 0;//登陆页UV
        int loginPV  = 0;//登陆页PV

        int indexUV  = 0;//首页UV
        int indexPV  = 0;//首页PV


        int planUV  = 0;//教学计划UV
        int planPV  = 0;//教学计划PV

        int englishUV  = 0;//教学计划UV
        int englishPV  = 0;//教学计划PV

        int foodUV  = 0;//一周食谱UV
        int foodPV  = 0;//一周食谱PV

        int opusUV  = 0;//宝贝作品集UV
        int opusPV  = 0;//宝贝作品集PV

        int dailyUV  = 0;//一日作息UV
        int dailyPV  = 0;//一日作息PV

        int guardUV  = 0;//园区简介UV
        int guardPV  = 0;//园区简介PV

        int cameraUV  = 0;//宝贝画面UV
        int cameraPV  = 0;//园区简介PV

        int noticeUV  = 0;//通知UV
        int noticePV  = 0;//通知PV

        int dynamicUV  = 0;//班级圈UV
        int dynamicPV  = 0;//班级圈PV

        int myUV  = 0;//我UV
        int myPV  = 0;//我VV
        for(Map<String,Object> result:results){
            loginUV+=(Integer) result.get("loginUV");
            loginPV+=(Integer) result.get("loginPV");

            indexUV+=(Integer) result.get("indexUV");
            indexPV+=(Integer) result.get("indexPV");

            planUV+=(Integer) result.get("planUV");
            planPV+=(Integer) result.get("planPV");

            englishUV+=(Integer) result.get("englishUV");
            englishPV+=(Integer) result.get("englishPV");

            foodUV+=(Integer) result.get("foodUV");
            foodPV+=(Integer) result.get("foodPV");

            opusUV+=(Integer) result.get("opusUV");
            opusPV+=(Integer) result.get("opusPV");

            dailyUV+=(Integer) result.get("dailyUV");
            dailyPV+=(Integer) result.get("dailyPV");

            guardUV+=(Integer) result.get("guardUV");
            guardPV+=(Integer) result.get("guardPV");

            cameraUV+=(Integer) result.get("cameraUV");
            cameraPV+=(Integer) result.get("cameraPV");

            noticeUV+=(Integer) result.get("noticeUV");
            noticePV+=(Integer) result.get("noticePV");

            dynamicUV+=(Integer) result.get("dynamicUV");
            dynamicPV+=(Integer) result.get("dynamicPV");

            myUV+=(Integer) result.get("myUV");
            myPV+=(Integer) result.get("myPV");
        }
        Map<String,Object> result = new HashMap<>();
        result.put("loginUV",loginUV);
        result.put("loginPV",loginPV);

        result.put("indexUV",indexUV);
        result.put("indexPV",indexPV);

        result.put("planUV",planUV);
        result.put("planPV",planPV);

        result.put("englishUV",englishUV);
        result.put("englishPV",englishPV);

        result.put("foodUV",foodUV);
        result.put("foodPV",foodPV);

        result.put("opusUV",opusUV);
        result.put("opusPV",opusPV);

        result.put("dailyUV",dailyUV);
        result.put("dailyPV",dailyPV);

        result.put("guardUV",guardUV);
        result.put("guardPV",guardPV);

        result.put("cameraUV",cameraUV);
        result.put("cameraPV",cameraPV);

        result.put("noticeUV",noticeUV);
        result.put("noticePV",noticePV);

        result.put("dynamicUV",dynamicUV);
        result.put("dynamicPV",dynamicPV);
        result.put("myUV",myUV);
        result.put("myPV",myPV);
        result.put("date","合计");
        results.add(result);
        //导出

        String date = TimeUtils.formatToDateCn(ct_start)+"-"+TimeUtils.formatToDateCn(ct_end);
        String fileName = guardName+"日均访问数据"+date+new Date().getTime();

        String sheetName="sheet1";

        //excel中对应的标题
        List<String> titlesArray = new ArrayList<String>();
        //商品属性中标题对应的字段
        List<String> fieldsArray = new ArrayList<String>();
        fieldsArray.add("date");
        titlesArray.add("日期");

        fieldsArray.add("loginUV");
        titlesArray.add("登陆页UV");
        fieldsArray.add("loginPV");
        titlesArray.add("登陆页PV");

        fieldsArray.add("indexUV");
        titlesArray.add("首页UV");
        fieldsArray.add("indexPV");
        titlesArray.add("首页PV");

        fieldsArray.add("planUV");
        titlesArray.add("教学计划UV");
        fieldsArray.add("planPV");
        titlesArray.add("教学计划PV");

        fieldsArray.add("englishUV");
        titlesArray.add("一周英语UV");
        fieldsArray.add("englishPV");
        titlesArray.add("一周英语PV");

        fieldsArray.add("foodUV");
        titlesArray.add("一周食谱UV");
        fieldsArray.add("foodPV");
        titlesArray.add("一周食谱PV");

        fieldsArray.add("opusUV");
        titlesArray.add("宝贝作品集UV");
        fieldsArray.add("opusPV");
        titlesArray.add("宝贝作品集PV");

        fieldsArray.add("dailyUV");
        titlesArray.add("一日作息UV");
        fieldsArray.add("dailyPV");
        titlesArray.add("一日作息PV");

        fieldsArray.add("guardUV");
        titlesArray.add("园区简介UV");
        fieldsArray.add("guardPV");
        titlesArray.add("园区简介PV");

        fieldsArray.add("cameraUV");
        titlesArray.add("宝贝画面UV");
        fieldsArray.add("cameraPV");
        titlesArray.add("宝贝画面PV");

        fieldsArray.add("noticeUV");
        titlesArray.add("通知UV");
        fieldsArray.add("noticePV");
        titlesArray.add("通知PV");

        fieldsArray.add("dynamicUV");
        titlesArray.add("班级圈UV");
        fieldsArray.add("dynamicPV");
        titlesArray.add("班级圈PV");

        fieldsArray.add("myUV");
        titlesArray.add("我UV");
        fieldsArray.add("myPV");
        titlesArray.add("我PV");

        String [] fields = (String [])fieldsArray.toArray((new String[fieldsArray.size()]));
        String [] titles = (String [])titlesArray.toArray((new String[titlesArray.size()]));
        //配置文件输出流
        //System.out.println("文件位置"+outFile.;);
        //设置文件位置
        fileName=fileName+".xls";
        String downloadPath=resourceHolder.getStringValue("fileUploadPath");
        String savePath="/excel/"+fileName;
        String filePath=downloadPath+savePath;

        //String resultPath=File.separator+"excel"+File.separator+fileName;
        try {
            OutputStream out = new FileOutputStream(new File(filePath));
            ExcelUtils.exportExcelFile(sheetName, null, titles, fields, results, out);
        }catch (Exception e){
            e.printStackTrace();
        }finally {
            logger.info("忽略-----FileNotFoundException");
        }
        String resultPath =resourceHolder.getStringValue("webRoot")+"/upload/"+savePath;
        logger.info("---------resultPath--"+resultPath);


        return resultPath;
    }







    /**
     * 获取登录人员关联的园区ID
     * @return
     */
    public List<Long> getGuardIds(){
        List<Long> guardIds = new ArrayList<>();

        if(StringUtils.isNotEmpty(AdminSessionHolder.getGuardIds())){
            String[] guardIdStrs =  AdminSessionHolder.getGuardIds().split(",");
            for(String guardIdStr:guardIdStrs){
                if(StringUtils.isNotEmpty(guardIdStr)){
                    guardIds.add(Long.valueOf(guardIdStr));
                }
            }
        }

        return  guardIds;
    }
}
