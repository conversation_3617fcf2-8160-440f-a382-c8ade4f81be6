package org.itboys.mobile.service.mysql;


import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;

import org.itboys.commons.utils.tools.LongUtil;
import org.itboys.framework.resource.ResourceHolder;
import org.itboys.mobile.dao.SundayReportYbDetailMapper;
import org.itboys.mobile.entity.mysql.report.SundayReportYbDetail;
import org.itboys.mysql.service.BaseMySqlService;
import org.itboys.param.PageResult;
import org.itboys.param.ParamUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 
 * 周报
 * <AUTHOR>
 *
 */
@Service
public class SundayReportYbDetailService extends BaseMySqlService<SundayReportYbDetail> {
    
    @Autowired
    private ResourceHolder resourceHolder;
    
	@Autowired
    private SundayReportYbDetailMapper sundayReportYbDetailMapper;

    /**
     * 分页获取
     * @param request
     * @return
     */
    public PageResult<SundayReportYbDetail> selectYb(HttpServletRequest request, Integer flag) {
        Map<String, Object> param = ParamUtil.packageMysqlPageParam(request, "code", "status", "bunchId", "studentId", "guardId", "startDate", "endDate");
        if(flag.compareTo(1) == 0 ) { //当flag==1时，为家长端
        	param.put("status", 4);
        }
        if(flag.compareTo(2) == 0 ) { //当flag==2时，审核端
        	param.remove("bunchId");
        	param.remove("studentId");
        	param.remove("guardId");
        	param.put("statusList", Arrays.asList(1,2,3,4));
        }
        return new PageResult<SundayReportYbDetail>(super.select(param), super.count(param));
    }
    

    public PageResult<SundayReportYbDetail> selectYbApprove(HttpServletRequest request, List<Long> bunchIdList) {
        Map<String, Object> param = ParamUtil.packageMysqlPageParam(request, "code", "status");
        param.put("statusList", Arrays.asList(1,2,3,4));
        param.put("bunchIdList", bunchIdList);
    	
        return new PageResult<SundayReportYbDetail>(super.select(param), super.count(param));
    }

    /**
     * 
     * @param sundayReportYbDetail
     * @throws Exception
     */
    public void save(SundayReportYbDetail sundayReportYbDetail) throws Exception {
        if (LongUtil.isNotZreo(sundayReportYbDetail.getId())) {
        	super.update(sundayReportYbDetail);
        } else {
        	sundayReportYbDetail.setId(null);
        	sundayReportYbDetail.setStatus(0);
        	super.insert(sundayReportYbDetail);
        }
    }

    /**
     * 
     * @param id
     * @return
     */
    public SundayReportYbDetail findOne(Long id){
    	SundayReportYbDetail zb=null;
        if(LongUtil.isNotZreo(id)){
        	zb = super.find(id);
        }
        return zb;
    }
    
    /**
     * 
     * @param ids
     */
    public void deleteByIds(String ids){
        List<Long> listIds = Arrays.asList(ids.split(","))
                .stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
        for (int i= 0;i<listIds.size();i++){
            if(LongUtil.isNotZreo(listIds.get(i))){
            	super.delete(listIds.get(i));
            }
        }
    }
    
    /**
     * 通过zbId和bunchId获取信息
     * @param sundayReportYbDetail
     * @return
     */
    public SundayReportYbDetail findOneByYbIdAndBunchId(SundayReportYbDetail sundayReportYbDetail) {
    	
    	return sundayReportYbDetailMapper.findOneByYbIdAndBunchId(sundayReportYbDetail);
    }
    
    public boolean batchUpdateStatus(SundayReportYbDetail sundayReportYbDetail) {
    	
    	return sundayReportYbDetailMapper.batchUpdateStatus(sundayReportYbDetail) > 0 ? true : false;
    }
    
    public List<SundayReportYbDetail> selectList(SundayReportYbDetail sundayReportYbDetail){
    	
    	return sundayReportYbDetailMapper.selectList(sundayReportYbDetail);
    }
    
    public void updateById(SundayReportYbDetail sundayReportYbDetail) {
    	
    	super.update(sundayReportYbDetail);
    }
    
    public List<SundayReportYbDetail> selectNoPage(Map<String,Object> param){
    	
    	return sundayReportYbDetailMapper.selectNoPage(param);
    }
}
