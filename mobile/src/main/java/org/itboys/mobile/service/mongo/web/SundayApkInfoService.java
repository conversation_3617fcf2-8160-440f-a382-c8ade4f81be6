package org.itboys.mobile.service.mongo.web;

import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.itboys.admin.service.lasted.SundayUploadService;
import org.itboys.commons.utils.tools.ListUtil;
import org.itboys.framework.resource.ResourceHolder;
import org.itboys.mobile.entity.mongo.SundayApkInfo;
import org.itboys.mobile.service.BaseYuHuaService;
import org.itboys.mongodb.core.MongoDataSource;
import org.itboys.mongodb.service.BaseMongoService;
import org.itboys.param.PageResult;
import org.itboys.param.ParamUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class SundayApkInfoService extends BaseMongoService<SundayApkInfo> {



    @Autowired
    private SundayUploadService uploadService;
    @Autowired
    private BaseYuHuaService baseYuHuaService;


    @Resource(name = "mobileDs")
    private MongoDataSource sundayDataSource;

    @Override
    protected MongoDataSource getMongoDataSource() {
        return sundayDataSource;
    }

    @Override
    protected Class<SundayApkInfo> getEntityClass() {
        return SundayApkInfo.class;
    }

    /*
     *
     * @param logs。更新日志
     * @param type。类型：1，安卓用户版本，3，安卓管理版，3，苹果用户版，4，苹果管理版
     * @param file。安卓APK文件
     * @param versionCode。苹果版本号
     * @throws IOException
     * @throws FileNotFoundException
     */

    public void uploadApk2(String logs, Integer type, MultipartFile file, Integer versionCode, String downloadUrl,Integer forceUpdate) throws Exception {

        //上传安卓
       if (type == 1 || type == 3) {
            SundayApkInfo apkInfo = new SundayApkInfo();
            if(type == 1){
                Map<String, String> map = uploadService.upload_oss_single_v2(file);
                if(StringUtils.isNotEmpty(map.get(SundayUploadService.UPLOAD_HALF_PATH))){
                    apkInfo.setDownLoadUrl(map.get(SundayUploadService.UPLOAD_HALF_PATH));
                    apkInfo.setType(type);
                    apkInfo.setFileLogs(logs);
                    apkInfo.setVersionCode(versionCode);
                    apkInfo.setForceUpdate(forceUpdate);
                    apkInfo.setFileMd5(map.get(SundayUploadService.UPLOAD_FILE_MD5));
                }
            }else if(type == 3){
                apkInfo.setType(type);
                apkInfo.setFileLogs(logs);
                apkInfo.setVersionCode(versionCode);
                apkInfo.setDownLoadUrl(downloadUrl);
                apkInfo.setForceUpdate(forceUpdate);
            }

            super.save(apkInfo);
        }
    }
    /**
     * 分页查询Apk信息
     *
     * @param request
     * @return
     */
    public PageResult<SundayApkInfo> select(HttpServletRequest request) {
        Map<String, Object> param = ParamUtil.packageMongoExactParam(request, "type", "versionCode");
        Map<String, String> containsparam = new HashMap<String, String>();
        PageResult<SundayApkInfo> pageResult = super.containpageQuery(request, containsparam, param);
        pageResult.getData().forEach(apkInfo -> {
            apkInfo.setDownLoadUrl(baseYuHuaService.getImageFullPath(apkInfo.getDownLoadUrl()));
        });
        return super.containpageQuery(request, containsparam, param);
    }

    //获取新版本APP
    public SundayApkInfo getApkInfo(Integer versionCode, Integer type) throws Exception {
        Map<String, Object> param = Maps.newHashMapWithExpectedSize(2);
        param.put("versionCode >", versionCode);
        param.put("type", type);
        param.put("sort", "-versionCode");
        param.put("isDeleted", 0);
        List<SundayApkInfo> infoes = super.list(param);
        if (ListUtil.isNotNull(infoes)) {
            return infoes.get(0);
        }
        return null;
    }

}

