package org.itboys.mobile.service.mongo;


import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.itboys.admin.constant.AdminSessionConstant;
import org.itboys.admin.entity.lasted.SystemUser;
import org.itboys.admin.service.lasted.SystemUserService;
import org.itboys.admin.tools.AdminSessionHolder;
import org.itboys.commons.CommonConstants;
import org.itboys.commons.ErrorException;
import org.itboys.commons.utils.time.TimeUtils;
import org.itboys.commons.utils.tools.ListUtil;
import org.itboys.commons.utils.tools.LongUtil;
import org.itboys.framework.resource.ResourceHolder;
import org.itboys.mobile.common.Sender;
import org.itboys.mobile.common.SundayCommonConstants;
import org.itboys.mobile.dto.SundayGuardMessageDto;
import org.itboys.mobile.entity.mongo.*;
import org.itboys.mobile.service.BaseYuHuaService;
import org.itboys.mobile.service.mongo.web.*;
import org.itboys.mongodb.core.MongoDataSource;
import org.itboys.mongodb.service.BaseMongoService;
import org.itboys.param.PageResult;
import org.itboys.param.ParamUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import sun.security.krb5.internal.PAData;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * 作者：江兄
 * 日期：2018年7月31日
 * 描述：百睿_学生和家长_中间表_service
 */
@Service
public class SundayParAndStuPermissionService extends BaseMongoService<SundayParAndStuPermission> {
    @Autowired
    private SundayStudentService studentService;
    @Autowired
    private SundayParentService parentService;
    @Autowired
    private SystemUserService userService;
    @Autowired
    private BaseYuHuaService baseYuHuaService;


    @Resource(name = "mobileDs")
    private MongoDataSource mobileDataSource;


    @Autowired
    private SundayTeacherService teacherService;

    @Autowired
    private SundayVisitorService visitorService;

    @Autowired
    private SundayStudentQinziBunchService qinziBunchService;

    @Autowired
    private SundayBunchService bunchService;

    @Autowired
    private Sender sender;


    @Override
    protected MongoDataSource getMongoDataSource() {
        return mobileDataSource;
    }

    @Override
    protected Class<SundayParAndStuPermission> getEntityClass() {
        return SundayParAndStuPermission.class;
    }

    /**
     * 新增家长
     *
     * @param studentId
     * @param parentId
     */
    public void saveParent(Long studentId, Long parentId) {
        Map<String, Object> param = new HashMap<String, Object>();
        param.put("studentId", parentId);
        param.put("parentId", parentId);
        List<SundayParAndStuPermission> olds = super.list(param);
        if (ListUtil.isNotNull(olds)) {
            SundayParAndStuPermission old = olds.get(0);
            old.setStudentId(studentId);
            old.setParentId(parentId);
            super.updateExceptEmpty(old.getId(), old);
        } else {
            super.save(new SundayParAndStuPermission(studentId, parentId));
        }

    }

    public void saveParent(Long studentId, Long parentId, String mobile) {
        Map<String, Object> param = new HashMap<String, Object>();
        param.put("studentId", parentId);
        param.put("parentId", parentId);
        List<SundayParAndStuPermission> olds = super.list(param);
        if (ListUtil.isNotNull(olds)) {
            SundayParAndStuPermission old = olds.get(0);
            old.setStudentId(studentId);
            old.setParentId(parentId);
            super.updateExceptEmpty(old.getId(), old);
        } else {
            super.save(new SundayParAndStuPermission(studentId, parentId));
        }
        sender.clearToken(mobile);
        sender.updateUserPhone(Math.toIntExact(parentId), mobile);
    }

    /**
     * 获取孩子ID
     *
     * @param parentId
     * @return
     */
    public List<Long> getStudentId(Long parentId) {
        List<Long> studentIds = new ArrayList<>();
        Map<String, Object> param = new HashMap<String, Object>();
        param.put("parentId", parentId);
        List<SundayParAndStuPermission> parAndStuPermissions = super.list(param);
        for (SundayParAndStuPermission parAndStuPermission : parAndStuPermissions) {
            studentIds.add(parAndStuPermission.getStudentId());
        }
        if (ListUtil.isNull(studentIds)) {
            studentIds.add(0l);
        }
        return studentIds;
    }

    /**
     * 获取孩子ID
     *
     * @param parentIds
     * @return
     */
    public List<Long> getStudentId(List<Long> parentIds) {
        List<Long> studentIds = new ArrayList<>();
        if (ListUtil.isNull(parentIds)) {
            return studentIds;
        }
        Map<String, Object> param = new HashMap<String, Object>();
        param.put("parentId in", parentIds);
        List<SundayParAndStuPermission> parAndStuPermissions = super.list(param);
        for (SundayParAndStuPermission parAndStuPermission : parAndStuPermissions) {
            studentIds.add(parAndStuPermission.getStudentId());
        }
        return studentIds;
    }

    /**
     * 获取家长ID
     *
     * @param studentId
     * @return
     */
    public List<Long> getParentId(Long studentId) {
        List<Long> parentIds = new ArrayList<>();
        Map<String, Object> param = new HashMap<String, Object>();
        param.put("studentId", studentId);
        List<SundayParAndStuPermission> parAndStuPermissions = super.list(param);
        for (SundayParAndStuPermission parAndStuPermission : parAndStuPermissions) {
            parentIds.add(parAndStuPermission.getParentId());
        }
        if (ListUtil.isNull(parentIds)) {
            parentIds.add(0l);
        }
        return parentIds;
    }

    public List<Long> getParentId(List<Long> studentIds) {
        List<Long> parentIds = new ArrayList<>();
        if (ListUtil.isNull(studentIds)) {
            return parentIds;
        }
        Map<String, Object> param = new HashMap<String, Object>();
        param.put("studentId in", studentIds);
        List<SundayParAndStuPermission> parAndStuPermissions = super.list(param);
        parAndStuPermissions.forEach(p -> {
            parentIds.add(p.getParentId());
        });
        return parentIds;
    }

    public List<Long> getParentIds() {
        List<Long> parentIds = new ArrayList<>();
        Map<String, Object> param = new HashMap<String, Object>();
        List<SundayParAndStuPermission> parAndStuPermissions = super.list(param);
        for (SundayParAndStuPermission parAndStuPermission : parAndStuPermissions) {
            parentIds.add(parAndStuPermission.getParentId());
        }
        if (ListUtil.isNull(parentIds)) {
            parentIds.add(0l);
        }
        return parentIds;
    }

    /**
     * 新增，编辑学生时，校验学生-家长信息是否为唯一
     *
     * @param id
     * @param name
     */
    public void checkStudentIsExist(long id, String name, String mobile) {
        Map<String, Object> param = new HashMap<String, Object>();
        param.clear();
        param.put("name", name);
        if (LongUtil.isNotZreo(id)) {
            param.put("id !=", id);
        }
        List<SundayStudent> oldStudents = studentService.list(param);
        if (ListUtil.isNotNull(oldStudents)) {
            //2，如果姓名重复，校验新的姓名对应的家长和重新学生家长信息
            //2.1，找到重复学生家长
            List<Long> oldStudentIds = new ArrayList<>();
            oldStudents.forEach(oldStu -> {
                oldStudentIds.add(oldStu.getId());
            });
            List<Long> oldParentIds = getParentId(oldStudentIds);
            List<SundayParent> oldParents = new ArrayList<>();
            if (ListUtil.isNotNull(oldParentIds)) {
                param.clear();
                param.put("id in", oldParentIds);
                oldParents = parentService.list(param);
            }
            //2.2,找到本人的家长
            List<Long> parentIds = getParentId(id);
            List<SundayParent> parents = new ArrayList<>();
            if (ListUtil.isNotNull(parentIds)) {
                param.clear();
                param.put("id in", parentIds);
                parents = parentService.list(param);

            }
            for (SundayParent oldParent : oldParents) {
                logger.info("已经存在的家长姓名=" + oldParent.getName() + ",家长手机号=" + oldParent.getMobile());
            }
            //2.3，如果是新增学生，家长信息是一起过来的
            if (StringUtils.isNotEmpty(mobile)) {
                parents.add(new SundayParent(mobile));
            }
            for (SundayParent parent : parents) {
                logger.info("本人家长姓名=" + parent.getName() + ",家长手机号=" + parent.getMobile());
            }
            //3,比对
            String msg = "";
            for (SundayParent parent : parents) {
                for (SundayParent oldParent : oldParents) {
                    if (StringUtils.isNotEmpty(parent.getMobile()) &&
                            StringUtils.isNotEmpty(oldParent.getMobile()) &&
                            parent.getMobile().equalsIgnoreCase(oldParent.getMobile())) {
                        msg = "学生姓名-家长信息-已经存在,请修改后重试！";
                        break;
                    }
                }
            }
            if (StringUtils.isNotEmpty(msg)) {
                throw new ErrorException(msg);
            }
        }
    }

    /**
     * 新增，编辑学生时，获取重名信息
     *
     * @param id
     * @param name
     * @return
     */
    public void checkStudentExistMsg(long id, String name) {
        String msg = "";
        Map<String, Object> param = new HashMap<String, Object>();
        param.clear();
        param.put("name", name);
        if (LongUtil.isNotZreo(id)) {
            param.put("id !=", id);
        }
        List<SundayStudent> students = studentService.list(param);
        baseYuHuaService.packageGuardAndBunch(students, false, false);
        if (ListUtil.isNotNull(students)) {
            long loginUserId = AdminSessionHolder.getAdminUserId();
            if (LongUtil.isNotZreo(loginUserId)) {
                SystemUser user = userService.getById(loginUserId);
                msg += user.getName() + "你好，";
            }
            for (SundayStudent student : students) {
                if (student.getStuType().equals(CommonConstants.YES)) {
                    msg += student.getBunchName() + "已经有叫" + student.getName() + "的小朋友了,";
                } else {
                    //TODO:LXK
                    param.clear();
                    param.put("studentId", student.getId());
                    param.put("state", CommonConstants.NO);
                    List<SundayStudentQinziBunch> qinziBunches = qinziBunchService.list(param);
                    if (ListUtil.isNotNull(qinziBunches)) {
                        SundayStudentQinziBunch studentQinziBunch = qinziBunches.get(0);
                        SundayBunch bunch = bunchService.getById(studentQinziBunch.getBunchId());
                        msg += "亲子班" + bunch.getName() + "中已经有叫" + student.getName() + "的小朋友了,";
                    }


                }

            }
            msg += "确认是否重复录入？";
            //李四老师你好，太阳班已经有叫王五的小朋友，确认是否重复录入？
        }
        if (StringUtils.isNotEmpty(msg)) {
            throw new ErrorException(msg);
        }


    }


    /**
     * 新增，编辑学生时，获取重名信息
     *
     * @param id
     * @param name
     * @return
     */
    public void checkStudentExistMsgV2(long id, String name, Long guardId) {
        String msg = "";
        Map<String, Object> param = new HashMap<String, Object>();
        param.clear();
        param.put("name", name);
        if (LongUtil.isNotZreo(id)) {
            param.put("id !=", id);
        }
        List<SundayStudent> students = studentService.list(param);
        baseYuHuaService.packageGuardAndBunch(students, false, false);


        if (ListUtil.isNotNull(students)) {
            for (SundayStudent student : students) {
                if (LongUtil.isNotNull(guardId)) {

                    if (student.getStuType().equals(CommonConstants.YES) && student.getGuardId().equals(guardId)) {
                        msg += student.getBunchName() + "已经有叫" + student.getName() + "的小朋友了,";
                    } else {
                        //TODO:LXK
                        param.clear();
                        param.put("studentId", student.getId());
                        param.put("state", CommonConstants.NO);
                        List<SundayStudentQinziBunch> qinziBunches = qinziBunchService.list(param);
                        List<Long> bunchIds = new ArrayList<>();
                        if (ListUtil.isNotNull(qinziBunches)) {
                            for (SundayStudentQinziBunch studentQinziBunch : qinziBunches) {
                                bunchIds.add(studentQinziBunch.getBunchId());
                            }
                            param.clear();
                            param.put("guardId", guardId);
                            param.put("id in", bunchIds);
                            List<SundayBunch> bunches = bunchService.list(param);
                            if (ListUtil.isNotNull(bunches)) {
                                SundayBunch bunch = bunches.get(0);
                                msg += "亲子班" + bunch.getName() + "中已经有叫" + student.getName() + "的小朋友了,";
                            }

                        }
                    }

                } else {
                    if (student.getStuType().equals(CommonConstants.YES)) {
                        msg += student.getBunchName() + "已经有叫" + student.getName() + "的小朋友了,";
                    } else {
                        //TODO:LXK
                        param.clear();
                        param.put("studentId", student.getId());
                        param.put("state", CommonConstants.NO);
                        List<SundayStudentQinziBunch> qinziBunches = qinziBunchService.list(param);
                        if (ListUtil.isNotNull(qinziBunches)) {
                            SundayStudentQinziBunch studentQinziBunch = qinziBunches.get(0);
                            SundayBunch bunch = bunchService.getById(studentQinziBunch.getBunchId());
                            msg += "亲子班" + bunch.getName() + "中已经有叫" + student.getName() + "的小朋友了,";
                        }

                    }
                }

            }

        }
        if (StringUtils.isNotBlank(msg)) {
            long loginUserId = AdminSessionHolder.getAdminUserId();
            if (LongUtil.isNotZreo(loginUserId)) {
                SystemUser user = userService.getById(loginUserId);
                msg = user.getName() + "你好，" + msg + "确认是否重复录入？";
            }
        }
        //李四老师你好，太阳班已经有叫王五的小朋友，确认是否重复录入？
        if (StringUtils.isNotEmpty(msg)) {
            throw new ErrorException(msg);
        }


    }


    /**
     * 新增，编辑家长时，校验学生-家长信息是否为唯一
     *
     * @param id
     * @param mobile
     * @param studentId
     */
    public void checkParentIsExist(long id, String mobile, long studentId) {

        Map<String, Object> param = new HashMap<String, Object>();
        SundayStudent student = studentService.getById(studentId);
        param.put("name", student.getName());
        param.put("id !=", studentId);
        List<SundayStudent> oldStudents = studentService.list(param);
        if (ListUtil.isNotNull(oldStudents)) {
            //2，如果姓名重复，校验新的姓名对应的家长和重新学生家长信息
            //2.1，找到重复学生家长
            List<Long> oldStudentIds = new ArrayList<>();
            oldStudents.forEach(oldStu -> {
                oldStudentIds.add(oldStu.getId());
            });
            List<Long> oldParentIds = getParentId(oldStudentIds);
            List<SundayParent> oldParents = new ArrayList<>();
            if (ListUtil.isNotNull(oldParentIds)) {
                param.clear();
                param.put("id in", oldParentIds);
                oldParents = parentService.list(param);
            }
            for (SundayParent oldParent : oldParents) {
                logger.info("已经存在的家长姓名=" + oldParent.getName() + ",家长手机号=" + oldParent.getMobile());
            }
            //3,比对
            String msg = "";
            for (SundayParent oldParent : oldParents) {
                if (StringUtils.isNotEmpty(oldParent.getMobile()) &&
                        mobile.equalsIgnoreCase(oldParent.getMobile())) {
                    msg = "学生姓名-家长信息-已经存在,请修改后重试！";
                    break;
                }
            }
            if (StringUtils.isNotEmpty(msg)) {
                throw new ErrorException(msg);
            }
        }
    }


    /**
     * 新增，编辑家长时，获取重名信息
     *
     * @param id
     * @param mobile
     * @param studentId 李四老师你好，手机号：18989898989已绑定太阳班张三小朋友，是否同时绑定2位小朋友呢？
     *                  有多个情况下：
     *                  多个情况下：李四老师你好，手机号：18989898989已绑定太阳班张三、月亮班王五小朋友，是否同时绑定3位小朋友呢？
     */
    public void checkParentExistMsg(long id, String mobile, long studentId) {
        String msg = "";
        Map<String, Object> param = new HashMap<String, Object>();
        param.put("mobile", mobile);
        List<SundayParent> parents = parentService.list(param);
        if (ListUtil.isNotNull(parents)) {
            List<Long> parentIds = new ArrayList<>();
            parents.forEach(p -> {
                parentIds.add(p.getId());
            });
            List<Long> studentIds = getStudentId(parentIds);

            if (!studentIds.contains(studentId)) {
                param.clear();
                param.put("id in", studentIds);
                param.put("id !=", studentId);
                List<SundayStudent> students = studentService.list(param);
                baseYuHuaService.packageGuardAndBunch(students, false, false);
                if (ListUtil.isNotNull(students)) {
                    long loginUserId = AdminSessionHolder.getAdminUserId();
                    if (LongUtil.isNotZreo(loginUserId)) {
                        SystemUser user = userService.getById(loginUserId);
                        msg += user.getName() + "你好，";
                    }
                    msg += "手机号：" + mobile;
                    for (SundayStudent student : students) {
                        if (student.getQinziType().equals(CommonConstants.YES)) {
                            msg += "已绑定 亲子班的" + student.getName() + "小朋友,";
                        } else {
                            msg += "已绑定" + student.getBunchName() + student.getName() + "小朋友,";
                        }

                    }
                    msg += "是否同时绑定" + (students.size() + 1) + "位小朋友呢";
                }
            }
        }
        if (StringUtils.isNotEmpty(msg)) {
            throw new ErrorException(msg);
        }
    }


    /**
     * 编辑亲子家长时，获取重名信息
     *
     * @param parentData
     * @param studentId  李四老师你好，手机号：18989898989已绑定太阳班张三小朋友，是否同时绑定2位小朋友呢？
     *                   有多个情况下：
     *                   多个情况下：李四老师你好，手机号：18989898989已绑定太阳班张三、月亮班王五小朋友，是否同时绑定3位小朋友呢？
     */
    public Map<String, Object> checkParentsExistMsg(String parentData, long studentId) {
        Map<String, Object> result = new HashMap<>();
        List<SundayParent> old_parents = new ArrayList<>();
        List<SundayParent> now_parents = new ArrayList<>();
        String msg = "";

        boolean flag = true;
        Map<String, Object> param = new HashMap<String, Object>();
        if (StringUtils.isNotBlank(parentData)) {
            JSONArray myJsonArray = JSONArray.fromObject(parentData);
            List<JSONObject> parents = (List) myJsonArray;
            if (ListUtil.isNotNull(parents)) {

                //result_list
                List<Map<String, Object>> data = new ArrayList<>();
                for (JSONObject parent : parents) {
                    Map<String, Object> result_parents = new HashMap<>();


                    Long parentId = parent.getLong("parentId");
                    String mobile = parent.getString("parentMobile");
                    String parentTitle = parent.getString("parentTitle");
                    String parentName = parent.getString("parentName");

                    Map<String, Object> map = new HashMap<>();
                    map.put("mobile", mobile);

                    //判断教师
                    long count = teacherService.count(map);
                    if (count > 0) {
                        throw new ErrorException("手机号  " + mobile + "  已绑定一名教师,无法新增!");
                    }

                    if (LongUtil.isNotZreo(parentId)) {
                        //判断游客
                        count = visitorService.count(map);
                        if (count > 0) {
                            throw new ErrorException("手机号  " + mobile + "  已绑定一名游客,无法新增!");
                        }
                        //判断家长
                        map.put("id !=", parentId);
                        count = parentService.count(map);
                        if (count > 0) {
                            throw new ErrorException("手机号  " + mobile + "  已绑定一名家长,无法修改!");
                        } else {
                            /*SundayParent old_parent = parentService.getById(parentId);

                            if(!old_parent.getMobile().equals(mobile)){
                                result_parents.put("mobile",mobile);
                                List<Map<String,Object>> children = new ArrayList<>();
                                List<Long> parentIds = new ArrayList<>();
                                parentIds.add(parentId);
                                List<Long> studentIds = getStudentId(parentIds);
                                param.clear();
                                param.put("id in",studentIds);
                                param.put("id !=",studentId);
                                List<SundayStudent> students = studentService.list(param);
                                baseYuHuaService.packageGuardAndBunch(students,false,false);
                                if(ListUtil.isNotNull(students)){
                                    flag =false;
                                    //msg+="手机号："+mobile;
                                    for(SundayStudent student:students){
                                        String bunchName = "";
                                        if(student.getStuType().equals(CommonConstants.YES)){
                                            bunchName+=","+student.getBunchName();
                                        }

                                        if(student.getQinziType().equals(CommonConstants.YES)){
                                            map.clear();
                                            map.put("studentId",student.getId());
                                            map.put("state",CommonConstants.NO);
                                            List<SundayStudentQinziBunch> qinziBunches = qinziBunchService.list(map);
                                            if(ListUtil.isNotNull(qinziBunches)){
                                                for(SundayStudentQinziBunch qinziBunch:qinziBunches){
                                                    SundayBunch bunch = bunchService.getById(qinziBunch.getBunchId());
                                                    bunchName += ","+bunch.getName();
                                                }
                                            }

                                        }
                                        Map<String,Object> child = new HashMap<>();
                                        child.put("bunchName",bunchName);
                                        child.put("childName",student.getName());
                                        children.add(child);
                                        //msg+="已绑定"+bunchName+"  "+student.getName()+"小朋友,</br>";
                                    }
                                    //msg+="</br>";
                                    result_parents.put("children",children);
                                }
                            }*/
                        }

                        SundayParent _p = new SundayParent();
                        _p.setId(parentId);
                        _p.setMobile(mobile);
                        _p.setName(parentName);
                        _p.setTitle(parentTitle);
                        old_parents.add(_p);
                    } else {
                        count = parentService.count(map);
                        if (count > 0) {
                            param.clear();
                            param.put("mobile", mobile);
                            List<SundayParent> _parents = parentService.list(param);
                            List<Long> parentIds = new ArrayList<>();
                            _parents.forEach(p -> {
                                parentIds.add(p.getId());
                            });
                            List<Long> studentIds = getStudentId(parentIds);
                            param.clear();
                            param.put("id in", studentIds);
                            param.put("id !=", studentId);
                            List<SundayStudent> students = studentService.list(param);
                            baseYuHuaService.packageGuardAndBunch(students, false, false);
                            if (ListUtil.isNotNull(students)) {
                                flag = false;
                                //msg+="手机号："+mobile;
                                result_parents.put("mobile", mobile);

                                List<Map<String, Object>> children = new ArrayList<>();

                                for (SundayStudent student : students) {
                                    String bunchName = "";
                                    if (student.getStuType().equals(CommonConstants.YES)) {
                                        bunchName += "," + student.getBunchName();
                                    }

                                    if (student.getQinziType().equals(CommonConstants.YES)) {
                                        map.clear();
                                        map.put("studentId", student.getId());
                                        map.put("state", CommonConstants.NO);
                                        List<SundayStudentQinziBunch> qinziBunches = qinziBunchService.list(map);
                                        if (ListUtil.isNotNull(qinziBunches)) {
                                            for (SundayStudentQinziBunch qinziBunch : qinziBunches) {
                                                SundayBunch bunch = bunchService.getById(qinziBunch.getBunchId());
                                                bunchName += "," + bunch.getName();
                                            }
                                        }

                                    }
                                    bunchName = bunchName.substring(1, bunchName.length());
                                    //msg+="已绑定"+bunchName+"  "+student.getName()+"小朋友,";
                                    Map<String, Object> child = new HashMap<>();
                                    child.put("bunchName", bunchName);
                                    child.put("childName", student.getName());
                                    children.add(child);
                                }
                                //msg+="</br>";
                                result_parents.put("children", children);
                            }
                        }
                        SundayParent _p = new SundayParent();
                        _p.setId(0);
                        _p.setMobile(mobile);
                        _p.setName(parentName);
                        _p.setTitle(parentTitle);
                        now_parents.add(_p);
                    }
                    if (result_parents.get("children") != null) {
                        data.add(result_parents);
                    }
                }
                if (data.size() > 0) {
                    result.put("parent", data);
                }
            }
        } else {
            throw new ErrorException("参数错误!");
        }

        if (!flag) {
            long loginUserId = AdminSessionHolder.getAdminUserId();
            if (LongUtil.isNotZreo(loginUserId)) {
                SystemUser user = userService.getById(loginUserId);
                //title
                result.put("title", user.getName() + "你好");
            }
            result.put("content", "是否绑定?");
            return result;
        } else {
            return result;
        }
    }
}
