package org.itboys.mobile.service.mongo.front;


import org.apache.commons.lang3.StringUtils;
import org.itboys.commons.ErrorException;
import org.itboys.commons.utils.tools.ListUtil;
import org.itboys.commons.utils.tools.LongUtil;
import org.itboys.framework.resource.ResourceHolder;
import org.itboys.mobile.common.SundayCommonConstants;
import org.itboys.mobile.dto.SundayAssessmentDto;
import org.itboys.mobile.dto.SundayAssessmentOptionDto;
import org.itboys.mobile.dto.SundayAssessmentQuestionDto;
import org.itboys.mobile.dto.SundayCourseDto;
import org.itboys.mobile.entity.mongo.SundayExpert;
import org.itboys.mobile.entity.mongo.SundayStudent;
import org.itboys.mobile.entity.mongo.assessment.*;
import org.itboys.mobile.service.mongo.web.*;
import org.itboys.mongodb.core.MongoDataSource;
import org.itboys.mongodb.service.BaseMongoService;
import org.itboys.param.PageResult;
import org.itboys.param.ParamUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;

/**
 * 作者：江兄
 * 日期：2018年7月31日
 * 描述：百睿_评测_service
 */
@Service
public class SundayFrontAssessmentService extends BaseMongoService<SundayAssessment> {

    @Autowired
    private SundayAssessmentQuestionService questionService;
    @Autowired
    private SundayAssessmentOptionService optionService;
    @Autowired
    private SundayAssessmentResultService resultService;
    @Autowired
    private SundayStudentService studentService;
    @Autowired
    private SundayFrontCourseService courseService;
    @Autowired
    private ResourceHolder resourceHolder;
    @Autowired
    private SundayAssessmentDocumentService documentService;

    @Autowired
    private SundayAssessmentResultOptionService resultOptionService;

    @Autowired
    private SundayExpertService expertService;

    @Resource(name = "mobileDs")
    private MongoDataSource mobileDataSource;

    @Override
    protected MongoDataSource getMongoDataSource() {
        return mobileDataSource;
    }

    @Override
    protected Class<SundayAssessment> getEntityClass() {
        return SundayAssessment.class;
    }


    /**
     * 获取首页的所有结果
     * @param memberId
     */
    public Map<String,Object> getIndexData(HttpServletRequest request,Long memberId){
        Map<String,Object> result = new HashMap<String,Object>();
        Map<String,Object> param = new HashMap<String,Object>();
        //1,儿童信息
        SundayStudent child = studentService.getById(memberId);
        result.put("id",child.getId());
        result.put("name",child.getName());
        result.put("age",child.getAge());
        result.put("height",child.getHeight());
        result.put("weight",child.getWeight());
        result.put("sex",child.getSex());
        //2，评测报告地址
        String result_url=resourceHolder.getStringValue("mobileRoot")+"/sunday/mobile/assessment/h5/getResult?type=2&isIndex=1";
        result.put("resultUrl",result_url);
        //2.1,查询是否有评测报告
        Integer isHasResult = 0;
        param.put("type",2);
        param.put("memberId",memberId);
        //param.put("status",SundayCommonConstants.YES);
        if(LongUtil.isNotZreo(resultService.count(param))){
            isHasResult=1;
        }
        result.put("isHasResult",isHasResult);


        //课程需要按照园区过滤数据(待定)
        //3，3节家长课程
        List<SundayCourseDto> course1 = courseService.getCourse(memberId,2,SundayCommonConstants.course_type_1);
        result.put("course1",course1);
        //4，3节亲子互动课程
        List<SundayCourseDto> course2 = courseService.getCourse(memberId,2,SundayCommonConstants.course_type_2);
        result.put("course2",course2);
        result.put("info","特殊字段。isHasResult(是否有评测结果)1有0无 course1(家长课程，最多三节)，course2(亲子教育课程，最多三节)resultUrl(评测结果h5网页URL)");
        return result;

    }






    /**
     * 分页获取评测列表
     * @param request
     * @param memberId
     */
    public List<SundayAssessmentDto> getData(HttpServletRequest request,Long memberId,Integer type){
        Map<String,Object> param = ParamUtil.packageMongoExactParam(request,"");
        if(type == 999){
            param.put("type in", Arrays.asList(new Integer[]{1,2}));
        }else{
            param.put("type",type);
        }
        //1,获取所有评测主体
        PageResult<SundayAssessment> pageResult = super.pageQuery(request,param);
        List<Long> assessmentIds = new ArrayList<>();
        for(SundayAssessment assessment:pageResult.getData()){
            assessmentIds.add(assessment.getId());
        }
        List<SundayAssessmentDto> assessmentDtos = packageBath(pageResult.getData());
        //2,校验是否评测过
        if(ListUtil.isNotNull(assessmentIds)){
           /* param.clear();
            param.put("assessmentId in",assessmentIds);
            param.put("memberId",memberId);

            List<SundayAssessmentResult> results = resultService.list(param);
            for(SundayAssessmentDto assessmentDto:assessmentDtos){
              assessmentDto.setIsCheck(0);
              for(SundayAssessmentResult result:results){
                  if(LongUtil.isNotZreo(result.getAssessmentId())&&
                          result.getAssessmentId().equals(assessmentDto.getId())){

                    //  assessmentDto.setIsCheck(1);
                      //2018年10月16日，状态
                      if(result.getStatus()==1){
                          assessmentDto.setIsCheck(1);
                      }else{
                          assessmentDto.setIsCheck(2);
                      }
                      break;
                  }
              }
          }*/
           //2018年10月16日，直接循环中多次查询。总共也没几个评测
            for(SundayAssessmentDto assessmentDto:assessmentDtos){
                assessmentDto.setIsCheck(resultService.checkIsCheck(memberId,assessmentDto.getId()));
            }
        }
        return assessmentDtos;
    }

    /**
     *获取评测详情(办好所有评测的尸体)
     * @param assessmentId
     * @param memberId
     * @return
     */
    public SundayAssessmentDto getDetail(Long assessmentId,Long memberId){
        SundayAssessment assessment = super.getById(assessmentId);
        Map<String,Object> param = new HashMap();
        param.put("assessmentId",assessmentId);
        //2，获取所有评测题目
        List<SundayAssessmentQuestion> questions= questionService.list(param);
        //3，获取所有评测选项
        List<SundayAssessmentOption>  options= optionService.list(param);
        //4，已经做过的选题
        param.clear();
        param.put("assessmentId",assessmentId);
        param.put("memberId",memberId);
        List<SundayAssessmentResultOption> oldOptions =resultOptionService.list(param);
        SundayAssessmentDto dto= packageSingle(assessment,questions,options,oldOptions);





        dto.setIsCheck(resultService.checkIsCheck(memberId,assessmentId));
        return dto;

    }

    /**
     * 提交评测（允许做到一半提交）。做了多少题，就提交多少题
     * @param memberId
     * @param assessmentId
     * @param questionIds
     * @param optionIds
     */
    public void submit(Long memberId,Long assessmentId,List<Long> questionIds,List <Long> optionIds) {
        //1.0，判断参数
        if (ListUtil.isNull(questionIds) ||
                ListUtil.isNull(optionIds) ||
                questionIds.size() != optionIds.size()) {
            throw new ErrorException("参数错误，请重试");
        }
        //1.1,批评是否测试过
        Integer isCheck  = resultService.checkIsCheck(memberId,assessmentId);
        if(isCheck == 1){
            throw new ErrorException("您已完成此评测");
        }
        //2，覆盖本次新的评测答题记录
        //2.1,删除原来的
        Map<String, Object> param = new HashMap<String, Object>();
        param.put("memberId",memberId);
        param.put("assessmentId",assessmentId);
        resultOptionService.delete(param);

        param.clear();
        param.put("assessmentId", assessmentId);
       // List<SundayAssessmentQuestion> questions = questionService.list(param);
        List<SundayAssessmentOption> options = optionService.list(param);
        //2,本次提交的
        List<SundayAssessmentResultOption> resultOptions = new ArrayList<>();
        for(int i=0 ;i<questionIds.size();i++){
            Long questionId = questionIds.get(i);
            Long optionId = optionIds.get(i);
            SundayAssessmentResultOption resultOption = new SundayAssessmentResultOption();
            resultOption.setAssessmentId(assessmentId);
            resultOption.setMemberId(memberId);
            resultOption.setQuestionId(questionId);
            resultOption.setOptionId(optionId);
            Integer value = 0;//分值
            Integer type = 0;//类型，1入园评测（3个维度），2注意力评测(6个维度)
            Integer dimension  =  0;//维度
            for(SundayAssessmentOption option:options){
                if(optionId.equals(option.getId())){
                    value = option.getValue();
                    type= option.getType();
                    dimension=option.getDimension();
                    break;
                }

            }

            resultOption.setValue(value);
            resultOption.setType(type);
            resultOption.setDimension(dimension);
            resultOptions.add(resultOption);
        }
        //3，保存本次提交的结果
        if(ListUtil.isNotNull(resultOptions)){
            resultOptionService.batchSaveWithoutLogin(resultOptions);
        }
        //4，判断是否完成评测，是否需要生成评测报告

        param.clear();
        param.put("assessmentId", assessmentId);
        List<SundayAssessmentQuestion>  questions =questionService.list(param);
        logger.info("总题数="+questions.size()+",本次答题数量="+resultOptions.size());
        if(questions.size()==resultOptions.size()){
            SundayAssessment  assessment = super.getById(assessmentId);
            //3，计算分值（平均分）
            //3.1，入园测试
            Double value1 = 0.00;//分值1自立能力及规则感
            int count1 = 0;
            Double value2 = 0.00;//分值2家庭教育一致性
            int count2 = 0;
            Double value3 = 0.00;//分值3情绪状态及安全感
            int count3 = 0;
            //能力测试
            Double value4 = 0.00;//分值4视觉
            int count4 = 0;
            Double value5 = 0.00;//分值5专注力统合
            int count5 = 0;
            Double value6 = 0.00;//分值6精细动作
            int count6 = 0;
            Double value7 = 0.00;//分值7前庭
            int count7 = 0;
            Double value8 = 0.00;//分值8本体
            int count8 = 0;
            Double value9 = 0.00;//分值9听觉
            int count9 = 0;

            //入院评测
            if (assessment.getType() == SundayCommonConstants.assessment_type_begin) {
                for (SundayAssessmentResultOption resultOption : resultOptions) {
                    if (resultOption.getDimension() == 1) {
                        count1++;
                        value1 += resultOption.getValue();
                    } else if (resultOption.getDimension() == 2) {
                        count2++;
                        value2 += resultOption.getValue();
                    } else if (resultOption.getDimension() == 3) {
                        count3++;
                        value3 += resultOption.getValue();
                    }
                }

                //避免选项中没有对应的维度（被除数不能为0）
                if (count1 > 0) {
                    value1 = value1 / count1;
                }
                if (count2 > 0) {
                    value2 = value2 / count2;
                }
                if (count3 > 0) {
                    value3 = value3 / count3;
                }
                //能力测试
            } else if (assessment.getType() == SundayCommonConstants.assessment_type_ability) {
                for (SundayAssessmentResultOption resultOption : resultOptions) {
                        if (resultOption.getDimension() == 4) {
                            count4++;
                            value4 += resultOption.getValue();
                        } else if (resultOption.getDimension() == 5) {
                            count5++;
                            value5 += resultOption.getValue();
                        } else if (resultOption.getDimension() == 6) {
                            count6++;
                            value6 += resultOption.getValue();
                        } else if (resultOption.getDimension() == 7) {
                            count7++;
                            value7 += resultOption.getValue();
                        } else if (resultOption.getDimension() == 8) {
                            count8++;
                            value8 += resultOption.getValue();
                        } else if (resultOption.getDimension() == 9) {
                            count9++;
                            value9 += resultOption.getValue();
                        }
                    }
                //避免选项中没有对应的维度（被除数不能为0）
                if (count4 > 0) {
                    value4 = value4 / count4;
                }
                if (count5 > 0) {
                    value5 = value5 / count5;
                }
                if (count6 > 0) {
                    value6 = value6 / count6;
                }
                if (count7 > 0) {
                    value7 = value7 / count7;
                }
                if (count8 > 0) {
                    value8 = value8 / count8;
                }
                if (count9 > 0) {
                    value9 = value9 / count9;
                }
            }
            SundayAssessmentResult result = new SundayAssessmentResult();
            result.setMemberId(memberId);
            SundayStudent member = studentService.getById(memberId);
            result.setMemberName(member.getName());  //宝贝名称
            result.setAssessmentId(assessmentId);
            result.setAssessmentName(assessment.getName());//评测名称
            result.setType(assessment.getType());
            result.setValue1(new BigDecimal(value1).setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue());
            result.setValue2(new BigDecimal(value2).setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue());
            result.setValue3(new BigDecimal(value3).setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue());
            result.setValue4(new BigDecimal(value4).setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue());
            result.setValue5(new BigDecimal(value5).setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue());
            result.setValue6(new BigDecimal(value6).setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue());
            result.setValue7(new BigDecimal(value7).setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue());
            result.setValue8(new BigDecimal(value8).setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue());
            result.setValue9(new BigDecimal(value9).setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue());
            //2018年10月16日，增加专家信息
            if(LongUtil.isNotZreo(assessment.getExpertId())){
                SundayExpert expert = expertService.getById(assessment.getExpertId());
                result.setExpertId(expert.getId());
                result.setExpertName(expert.getName());
                result.setExpertImage(expert.getImage());
                result.setExpertDesc(expert.getDesc());
                result.setExpertValue(7.00);
            }

            resultService.save(result);
        }

    }

    /**
     * 获取评测结果（h5）
     * @param assessmentId
     * @param memberId
     */
    public Map<String,Object> getResult(Long assessmentId,Integer type,Long memberId,boolean isIndex){
        String imgRoot = resourceHolder.getStringValue("imgRoot");
        Map<String,Object> result = new HashMap<>();
        result.put("id",null);
        Map<String,Object> param = new HashMap<>();
        //如果没有传入评测ID。则默认获取入院评测结果
        if(!LongUtil.isNotZreo(assessmentId)){
            param.put("memberId",memberId);
            param.put("type",type);
            param.put("sort","ct");
            param.put("order","DESC");
        }else{
            param.put("assessmentId",assessmentId);
            param.put("memberId",memberId);
        }

        List<SundayAssessmentResult> assessmentResults = resultService.list(param);
        if(ListUtil.isNotNull(assessmentResults)){
            SundayAssessmentResult assessmentResult = assessmentResults.get(0);
            result.put("id",assessmentResult.getId());
            //入院评测
            if(assessmentResult.getType() == 1){
                Double value1 = assessmentResult.getValue1();//分值1自立能力及规则感
                Double value2 = assessmentResult.getValue2();//分值2家庭教育一致性
                Double value3 = assessmentResult.getValue3();//分值3情绪状态及安全感
                //百分比（偏移量）
                Double valueRate1 = (value1/7)*100;//7分制
                Double valueRate2 = (value2/5)*100;//7分制
                Double valueRate3 = (value3/5)*100;//7分制

                //获取所有评测结果标准文本
                param.clear();
                //1自立能力及规则感
                SundayAssessmentDocument document1 = new SundayAssessmentDocument();
                //2家庭教育一致性
                SundayAssessmentDocument document2 = new SundayAssessmentDocument();
                //3,情绪状态及安全感
                SundayAssessmentDocument document3 = new SundayAssessmentDocument();
                //5,入院评测综合点评
                SundayAssessmentDocument document5 = new SundayAssessmentDocument();

                List<SundayAssessmentDocument> documents = documentService.list(param);
                for(SundayAssessmentDocument document:documents){
                    if(document.getType()==1){
                        document1=document;
                    }else if(document.getType()==2){
                        document2=document;
                    }else if(document.getType()==3){
                        document3=document;
                    }else if(document.getType()==5) {
                        document5 = document;
                    }
                }

                String valueMsg1 = "";
                String valueMsg2 = "";
                String valueMsg3 = "";
                String content= "";
                boolean boolean1 = false;
                boolean boolean2 = false;
                boolean boolean3 = false;
                //匹配文本。大于等于开始，小于结束
                if(value1>document1.getEnd()){
                    valueMsg1=document1.getGood();
                     boolean1 = true;
                }
                if(value1<document1.getStart()){
                    valueMsg1=document1.getBad();
                }
                if(value1>=document1.getStart()&value1<=document1.getEnd()){
                    valueMsg1=document1.getNormal();
                }

                if(value2>document2.getEnd()){
                    valueMsg2=document2.getGood();
                    boolean2 = true;
                }
                if(value2<document2.getStart()){
                    valueMsg2=document2.getBad();
                }
                if(value2>=document2.getStart()&value2<=document2.getEnd()){
                    valueMsg2=document2.getNormal();
                }

                if(value3>document3.getEnd()){
                    valueMsg3=document3.getGood();
                    boolean3 = true;
                }
                if(value3<document3.getStart()){
                    valueMsg3=document3.getBad();
                }
                if(value3>=document3.getStart()&value3<=document3.getEnd()){
                    valueMsg3=document3.getNormal();
                }



                //综合评价
                if(boolean1&&boolean2&&boolean3){
                    content=document5.getValue123();
                }else if(boolean1&&boolean2){
                    content=document5.getValue12();
                }else if(boolean1&&boolean3){
                    content=document5.getValue13();
                }else if(boolean2&&boolean3){
                    content=document5.getValue23();
                }else if(boolean1){
                    content=document5.getValue23();
                }else if(boolean2){
                    content=document5.getValue23();
                }else {
                    content=document5.getValue();
                }


                result.put("value1",new BigDecimal(value1).setScale(2,BigDecimal.ROUND_HALF_UP));
                result.put("value2",new BigDecimal(value2).setScale(2,BigDecimal.ROUND_HALF_UP));
                result.put("value3",new BigDecimal(value3).setScale(2,BigDecimal.ROUND_HALF_UP));
                result.put("valueRate1",new BigDecimal(valueRate1).setScale(2,BigDecimal.ROUND_HALF_UP));
                result.put("valueRate2",new BigDecimal(valueRate2).setScale(2,BigDecimal.ROUND_HALF_UP));
                result.put("valueRate3",new BigDecimal(valueRate3).setScale(2,BigDecimal.ROUND_HALF_UP));
                result.put("valueMsg1",valueMsg1);
                result.put("valueMsg2",valueMsg2);
                result.put("valueMsg3",valueMsg3);

                result.put("content",content);
                result.put("type", assessmentResult.getType());
            }
            //注意力评测
            if(assessmentResult.getType() == 2){

                result.put("value4",new BigDecimal(assessmentResult.getValue4()).setScale(2,BigDecimal.ROUND_HALF_UP));
                result.put("value5",new BigDecimal(assessmentResult.getValue5()).setScale(2,BigDecimal.ROUND_HALF_UP));
                result.put("value6",new BigDecimal(assessmentResult.getValue6()).setScale(2,BigDecimal.ROUND_HALF_UP));
                result.put("value7",new BigDecimal(assessmentResult.getValue7()).setScale(2,BigDecimal.ROUND_HALF_UP));
                result.put("value8",new BigDecimal(assessmentResult.getValue8()).setScale(2,BigDecimal.ROUND_HALF_UP));
                result.put("value9",new BigDecimal(assessmentResult.getValue9()).setScale(2,BigDecimal.ROUND_HALF_UP));
                result.put("expertId",assessmentResult.getExpertId());
                result.put("expertName",assessmentResult.getExpertName());
                result.put("expertImage",imgRoot+assessmentResult.getExpertImage());
                result.put("type", assessmentResult.getType());
                //2018年8月20日，增加综合评分。匹配对应评语
                Double value_avg =(assessmentResult.getValue4().doubleValue()+
                        assessmentResult.getValue5().doubleValue()+
                        assessmentResult.getValue6().doubleValue()+
                        assessmentResult.getValue7().doubleValue()+
                        assessmentResult.getValue8().doubleValue()+
                        assessmentResult.getValue9().doubleValue())/6;
                result.put("value_avg", value_avg);

                //匹配综合点评
                //获取所有评测结果标准文本
                param.clear();
                param.put("type",4);
                SundayAssessmentDocument document4 = documentService.list(param).get(0);
                //匹配评语图片 好：大于3.93；中：2.68-3.93；差：小于2.68
                String value_image =resourceHolder.getStringValue("weixinRoot")+"/yuhua/mobile/img";
                if(value_avg.doubleValue()>document4.getEnd()){
                    value_image+="/3.png";
                }else if(value_avg.doubleValue()>=document4.getStart()&&value_avg.doubleValue()<=document4.getEnd()){
                    value_image+="/2.png";
                }else if(value_avg.doubleValue()<document4.getStart()){
                    value_image+="/1.png";;
                }
                result.put("value_image",value_image );
                //5,注意力中综合点评
                String content = "";
                if(value_avg.doubleValue()>document4.getEnd()){
                    content=document4.getGood();
                }else if(value_avg.doubleValue()>=document4.getStart()&&value_avg.doubleValue()<=document4.getEnd()){
                    content=document4.getNormal();
                }else if(value_avg.doubleValue()<document4.getStart()){
                    content=document4.getBad();
                }
                    //首页的时候，只显示部分信息
                if(isIndex
                        &&StringUtils.isNotEmpty(content)
                        &&content.length()>60){
                    content= content.substring(0,60)+"......";
                }

                result.put("content",content);
            }
        }
        return result;

    }

    /**
     *
     * @param assessments
     * @return
     */
    public List<SundayAssessmentDto> packageBath(List<SundayAssessment> assessments){
        List<SundayAssessmentDto> dtos = new ArrayList<>();
        for(SundayAssessment assessment:assessments){
            dtos.add(packageSingle(assessment,null,null,null));
        }
        return dtos;
    }

    /**
     *
     * @param assessment
     * @param questions
     * @param options
     * @return
     */
    public SundayAssessmentDto packageSingle(SundayAssessment assessment,List<SundayAssessmentQuestion> questions,List<SundayAssessmentOption> options,List<SundayAssessmentResultOption> resultOptions){
        List<SundayAssessmentQuestionDto> questionDtos = new ArrayList<>();
        //组装问题
        if(ListUtil.isNotNull(questions)){

            for(SundayAssessmentQuestion question:questions){
                if(LongUtil.isNotZreo(question.getAssessmentId())&&
                        question.getAssessmentId().equals(assessment.getId())){
                    List<SundayAssessmentOptionDto> optionDtos = new ArrayList<>();
                    //2018年10月16日，已经做过的题

                    //组装答案
                    for(SundayAssessmentOption option:options){
                        if(LongUtil.isNotZreo(option.getQuestionId())&&
                                question.getId() == option.getQuestionId()){
                            optionDtos.add(new SundayAssessmentOptionDto(
                                    option.getId(), option.getCreateTime(),option.getCreateDate(),
                                    option.getName(), option.getType(),option.getValue(),option.getTypeStr(), option.getDimension(), option.getDimensionStr()
                            ));
                        }
                    }
                    SundayAssessmentQuestionDto questionDto =   new SundayAssessmentQuestionDto(
                            question.getId(), question.getCreateTime(),question.getCreateDate(),
                            question.getName(), question.getType(),question.getTypeStr(), question.getDimension(), question.getDimensionStr());

                    long oldOptionId = 0;
                    for(SundayAssessmentResultOption resultOption:resultOptions){
                        if(LongUtil.isNotZreo(resultOption.getQuestionId())&&
                                resultOption.getQuestionId().equals(question.getId())){
                            oldOptionId=resultOption.getOptionId();
                            break;
                        }
                    }
                    questionDto.setOldOptionId(oldOptionId);
                    questionDto.setOptions(optionDtos);
                    questionDtos.add(questionDto);
                }
            }
        }
        SundayAssessmentDto assessmentDto = new SundayAssessmentDto(assessment.getId(), assessment.getCreateTime(),assessment.getCreateDate(),
                assessment.getName(),assessment.getDesc(),assessment.getType(),assessment.getTypeStr());
        assessmentDto.setQuestions(questionDtos);
        return assessmentDto;
    }





    /**
     * 提交评测结果
     * @param memberId
     * @param questionIds
     * @param optionIds
     */
    public void submitOld(Long memberId,Long assessmentId,List<Long> questionIds,List <Long> optionIds) {
        //1,判断是否完全答题
        if (ListUtil.isNull(questionIds) ||
                ListUtil.isNull(optionIds) ||
                questionIds.size() != optionIds.size()) {
            throw new ErrorException("参数错误，请重试");
        }
        SundayAssessment assessment = super.getById(assessmentId);

        Map<String, Object> param = new HashMap<String, Object>();
        param.put("assessmentId", assessmentId);
        List<SundayAssessmentQuestion> questions = questionService.list(param);
        if (questionIds.size() != questions.size() ||
                optionIds.size() != questions.size()) {
            throw new ErrorException("请完成所有题目后再提交");
        }
        //1.1,批评是否测试过
        Integer isCheck  = resultService.checkIsCheck(memberId,assessmentId);
        if(isCheck == 1){
            throw new ErrorException("您已完成此评测");
        }
        //2,查询选项，匹配分值
        param.clear();
        param.put("assessmentId", assessmentId);
        List<SundayAssessmentOption> options = optionService.list(param);
        //3，计算分值（平均分）
        //3.1，入园测试
        Double value1 = 0.00;//分值1自立能力及规则感
        int count1 = 0;
        Double value2 = 0.00;//分值2家庭教育一致性
        int count2 = 0;
        Double value3 = 0.00;//分值3情绪状态及安全感
        int count3 = 0;
        //能力测试
        Double value4 = 0.00;//分值4视觉
        int count4 = 0;
        Double value5 = 0.00;//分值5专注力统合
        int count5 = 0;
        Double value6 = 0.00;//分值6精细动作
        int count6 = 0;
        Double value7 = 0.00;//分值7前庭
        int count7 = 0;
        Double value8 = 0.00;//分值8本体
        int count8 = 0;
        Double value9 = 0.00;//分值9听觉
        int count9 = 0;

        //入院评测
        if (assessment.getType() == SundayCommonConstants.assessment_type_begin) {
            for (SundayAssessmentOption option : options) {
                //匹配选项
                boolean isMatch = false;
                for(Long optionId:optionIds){
                    if(optionId.equals(option.getId())){
                        isMatch=true;
                        break;
                    }
                }
                if(isMatch){
                    if (option.getDimension() == 1) {
                        count1++;
                        value1 += option.getValue();
                    } else if (option.getDimension() == 2) {
                        count2++;
                        value2 += option.getValue();
                    } else if (option.getDimension() == 3) {
                        count3++;
                        value3 += option.getValue();
                    }
                }
            }
            //避免选项中没有对应的维度（被除数不能为0）
            if (count1 > 0) {
                value1 = value1 / count1;
            }
            if (count2 > 0) {
                value2 = value2 / count2;
            }
            if (count3 > 0) {
                value3 = value3 / count3;
            }
            //能力测试
        } else if (assessment.getType() == SundayCommonConstants.assessment_type_ability) {

            for (SundayAssessmentOption option : options) {
                //匹配选项
                boolean isMatch = false;
                for(Long optionId:optionIds){
                    if(optionId.equals(option.getId())){
                        isMatch=true;
                        break;
                    }
                }
                if(isMatch){
                    if (option.getDimension() == 4) {
                        count4++;
                        value4 += option.getValue();
                    } else if (option.getDimension() == 5) {
                        count5++;
                        value5 += option.getValue();
                    } else if (option.getDimension() == 6) {
                        count6++;
                        value6 += option.getValue();
                    } else if (option.getDimension() == 7) {
                        count7++;
                        value7 += option.getValue();
                    } else if (option.getDimension() == 8) {
                        count8++;
                        value8 += option.getValue();
                    } else if (option.getDimension() == 9) {
                        count9++;
                        value9 += option.getValue();
                    }
                }

            }
            //避免选项中没有对应的维度（被除数不能为0）
            if (count4 > 0) {
                value4 = value4 / count4;
            }
            if (count5 > 0) {
                value5 = value5 / count5;
            }
            if (count6 > 0) {
                value6 = value6 / count6;
            }
            if (count7 > 0) {
                value7 = value7 / count7;
            }
            if (count8 > 0) {
                value8 = value8 / count8;
            }
            if (count9 > 0) {
                value9 = value9 / count9;
            }
        }
        SundayAssessmentResult result = new SundayAssessmentResult();
        result.setMemberId(memberId);
        SundayStudent member = studentService.getById(memberId);
        result.setMemberName(member.getName());  //宝贝名称
        result.setAssessmentId(assessmentId);
        result.setAssessmentName(assessment.getName());//评测名称
        result.setType(assessment.getType());
        result.setValue1(new BigDecimal(value1).setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue());
        result.setValue2(new BigDecimal(value2).setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue());
        result.setValue3(new BigDecimal(value3).setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue());
        result.setValue4(new BigDecimal(value4).setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue());
        result.setValue5(new BigDecimal(value5).setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue());
        result.setValue6(new BigDecimal(value6).setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue());
        result.setValue7(new BigDecimal(value7).setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue());
        result.setValue8(new BigDecimal(value8).setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue());
        result.setValue9(new BigDecimal(value9).setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue());
        resultService.save(result);

    }
}
