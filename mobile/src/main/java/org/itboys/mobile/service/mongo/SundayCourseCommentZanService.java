package org.itboys.mobile.service.mongo;


import org.itboys.commons.utils.tools.LongUtil;
import org.itboys.mobile.entity.mongo.SundayBunch;
import org.itboys.mobile.entity.mongo.SundayGuard;
import org.itboys.mobile.entity.mongo.course.SundayCourseComment;
import org.itboys.mobile.entity.mongo.course.SundayCourseCommentZan;
import org.itboys.mobile.entity.mongo.course.SundayCourseHistory;
import org.itboys.mongodb.core.MongoDataSource;
import org.itboys.mongodb.service.BaseMongoService;
import org.itboys.param.PageResult;
import org.itboys.param.ParamUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 作者：江兄
 * 日期：2018年7月31日
 * 描述：百睿_课程_评论_点赞_service
 */
@Service
public class SundayCourseCommentZanService extends BaseMongoService<SundayCourseCommentZan> {


    @Resource(name = "mobileDs")
    private MongoDataSource mobileDataSource;

    @Override
    protected MongoDataSource getMongoDataSource() {
        return mobileDataSource;
    }

    @Override
    protected Class<SundayCourseCommentZan> getEntityClass() {
        return SundayCourseCommentZan.class;
    }


}
