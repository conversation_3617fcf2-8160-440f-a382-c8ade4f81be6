package org.itboys.mobile.service.mongo.web;

import org.itboys.commons.CommonConstants;
import org.itboys.mobile.entity.mongo.schedule.SundayScheduleCancelConfig;
import org.itboys.mobile.entity.mongo.schedule.SundayScheduleEntity;
import org.itboys.mongodb.core.MongoDataSource;
import org.itboys.mongodb.service.BaseMongoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @version V1.0
 * @created with IntelliJ IDEA.
 * @Title: entity
 * @author: shumlinmeng
 * @description:
 */

@Service
public class SundayScheduleCancelConfigService extends BaseMongoService<SundayScheduleCancelConfig>{

    @Resource(name = "mobileDs")
    private MongoDataSource mobileDataSource;

    @Override
    protected MongoDataSource getMongoDataSource() {
        return mobileDataSource;
    }


    @Override
    protected Class<SundayScheduleCancelConfig> getEntityClass() {
        return SundayScheduleCancelConfig.class;
    }


    public void editConfig(Long configId){
        Map<String,Object> param = new HashMap<>();
        List<SundayScheduleCancelConfig> configs = super.list(param);
        for(SundayScheduleCancelConfig c:configs){
            if(configId.equals(c.getId())){
                c.setUsed(CommonConstants.YES);
            }else {
                c.setUsed(CommonConstants.NO);
            }
        }
        super.batchUpdate(configs);
    }
}
