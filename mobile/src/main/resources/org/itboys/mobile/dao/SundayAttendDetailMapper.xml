<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.itboys.mobile.dao.SundayAttendDetailMapper">


    <resultMap id="attendDetailMap" type="org.itboys.mobile.entity.mysql.attendance.SundayAttendDetail">
        <result column="id" property="id"/>
        <result column="studentId" property="studentId"/>
        <result column="bunchId" property="bunchId"/>
        <result column="scheduleId" property="scheduleId"/>
        <result column="lessonId" property="lessonId"/>
        <result column="lessonName" property="lessonName"/>
        <result column="description" property="description"/>
        <result column="buildTime" property="buildTime"/>
        <result column="consumeHour" property="consumeHour"/>
        <result column="consumeType" property="consumeType"/>
        <result column="classHour" property="classHour"/>
        <result column="surplusHour" property="surplusHour"/>

        <association property="surplusHourV2" column="{buildTime=buildTime,studentId=studentId}" select="org.itboys.mobile.dao.SundayAttendDetailMapper.getSurplusHourV2"/>
    </resultMap>

    <sql id="table_page">
        <if test="row_start != null  and  row_size != null">
            <![CDATA[limit #{row_start},#{row_size}]]>
        </if>
    </sql>


    <insert id="save" parameterType="org.itboys.mobile.entity.mysql.attendance.SundayAttendDetail" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO
        school_attend_detail
        (studentId,bunchId,scheduleId,lessonId,lessonName,description,buildTime,consumeHour,classHour,surplusHour,consumeType,creator,ct,ut,updater,isDeleted)
        VALUES (#{studentId},#{bunchId},#{scheduleId},#{lessonId},#{lessonName},#{description},#{buildTime},#{consumeHour},#{classHour},#{surplusHour},
        #{consumeType},#{creator},now(),now(),#{updater},0)
    </insert>

    <update id="update">
        UPDATE
        school_attend_detail
        SET ut=now()
        <if test="description != null">
            <![CDATA[, description =#{description}]]>
        </if>
        <if test="consumeHour != null">
            <![CDATA[, consumeHour =#{consumeHour}]]>
        </if>
        <if test="consumeType != null">
            <![CDATA[, consumeType =#{consumeType}]]>
        </if>
        <if test="updater != null">
            <![CDATA[, updater =#{updater}]]>
        </if>
        WHERE  id =#{id}
    </update>


    <delete id="delete">
        UPDATE school_attend_detail
        SET isDeleted = 1
        WHERE id = #{id}
    </delete>

    <delete id="deleteByStudentIdAndScheduleId">
        UPDATE school_attend_detail
        SET isDeleted = 1
        WHERE 1 = 1
        <if test="studentId != null">
            <![CDATA[AND studentId = #{studentId}]]>
        </if>
        AND scheduleId = #{scheduleId}
    </delete>

    <delete id="deleteByScheduleId">
        UPDATE school_attend_detail
        SET isDeleted = 1
        WHERE scheduleId = #{scheduleId}
    </delete>

    <select id="selectAttendDetailByStudentId" resultMap="attendDetailMap">
        SELECT *
        FROM school_attend_detail
        WHERE 1 = 1
        AND isDeleted = 0
        AND studentId = #{studentId}
        <if test="lessonId != null">
            <![CDATA[AND lessonId =#{lessonId}]]>
        </if>
        <if test="consumeType != null">
            <![CDATA[AND consumeType =#{consumeType}]]>
        </if>
        <if test="buildTime != null">
            <![CDATA[AND buildTime < #{buildTime}]]>
        </if>
        ORDER BY buildTime DESC
        <include refid="table_page"/>
    </select>

    <select id="countByStudentId" resultType="int">
        SELECT COUNT(*)
        FROM school_attend_detail
        WHERE 1 = 1
        AND isDeleted = 0
        AND studentId = #{studentId}
    </select>

    <select id="findByStudentIdAndScheduleId" resultMap="attendDetailMap">
        SELECT *
        FROM school_attend_detail
        WHERE 1 = 1
        AND isDeleted = 0
        AND studentId = #{studentId}
        AND scheduleId = #{scheduleId}
        LIMIT 0,1
    </select>

    <update id="batchUpdateAttendByScheduleId">
        UPDATE school_attend_detail
        SET ut=now(),
        consumeType = 0,
        consumeHour=#{consumeHour}
        WHERE scheduleId = #{scheduleId}
    </update>

    <select id="getSurplusHourV2" resultType="String">

        SELECT COALESCE(SUM(consumeHour),0) AS surplusHourV2
        FROM school_attend_detail
        WHERE 1=1
        AND studentId = #{studentId}
        <![CDATA[AND buildTime < #{buildTime} ]]>
        AND scheduleId IS NOT NULL
    </select>

</mapper>