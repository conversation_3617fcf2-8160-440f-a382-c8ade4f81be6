<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.itboys.mobile.dao.SundayPlanAndEnglishMapper">


	<select id="select" resultType="org.itboys.mobile.entity.mysql.SundayPlanAndEnglish">
		SELECT
                school_plan.id,
                school_plan.park_name,
                school_plan.park_id,
                school_plan.class_name,
                school_plan.class_id,
                school_plan.flag,
                school_plan.content,
                school_plan.`year`,
                school_plan.`month`,
                school_plan.`week`,
                school_plan.theme,
                school_plan.target,
                school_plan.src,
                school_plan.publish_time,
                school_plan.create_time,
                school_plan.type,
                school_plan.pics
        FROM
          school_plan
        where 1=1
        <if test="flag !=null">
            <![CDATA[ and flag = #{flag}]]>
        </if>
        <if test="type !=null">
            <![CDATA[ and `type` = #{type}]]>
        </if>


	</select>



</mapper>