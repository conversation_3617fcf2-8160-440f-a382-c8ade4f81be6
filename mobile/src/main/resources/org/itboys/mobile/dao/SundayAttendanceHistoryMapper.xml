<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.itboys.mobile.dao.SundayAttendanceHistoryMapper">


    <resultMap id="historyMap" type="org.itboys.mobile.entity.mysql.attendance.SundayAttendanceHistory">
        <result column="id" property="id"/>
        <result column="guardId" property="guardId"/>
        <result column="scheduleId" property="scheduleId"/>
        <result column="bunchId" property="bunchId"/>
        <result column="lessonId" property="lessonId"/>
        <result column="beginTime" property="beginTime"/>
        <result column="endTime" property="endTime"/>
        <result column="startInterval" property="startInterval"/>
        <result column="endInterval" property="endInterval"/>
        <result column="presentNumber" property="presentNumber"/>
        <result column="totalNumber" property="totalNumber"/>
        <result column="lessonNumber" property="lessonNumber"/>
        <result column="leaveNumber" property="leaveNumber"/>
        <result column="absentNumber" property="absentNumber"/>
        <result column="repairNumber" property="repairNumber"/>
        <result column="editNum" property="editNum"/>
    </resultMap>

    <sql id="table_page">
        <if test="row_start != null  and  row_size != null">
            <![CDATA[limit #{row_start},#{row_size}]]>
        </if>
    </sql>


    <insert id="save" parameterType="org.itboys.mobile.entity.mysql.attendance.SundayAttendanceHistory" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO
        school_attendance_history
        (guardId,scheduleId,bunchId,lessonId,beginTime,endTime,startInterval,endInterval
        ,presentNumber,totalNumber,lessonNumber,leaveNumber,absentNumber,repairNumber,editNum,creator,ct,ut,updater,isDeleted)
        VALUES (#{guardId},#{scheduleId},#{bunchId},#{lessonId},#{beginTime},#{endTime},#{startInterval},#{endInterval}
        ,#{presentNumber},#{totalNumber},#{lessonNumber},#{leaveNumber},#{absentNumber},#{repairNumber},0,#{creator},now(),now(),#{updater},0)
    </insert>

    <select id="findById" resultMap="historyMap">
        SELECT *
        FROM school_attendance_history
        WHERE id = #{id}
    </select>

    <delete id="deleteByScheduleId">
        UPDATE school_attendance_history
        SET isDeleted = 1
        WHERE 1 = 1
        AND scheduleId = #{scheduleId}
    </delete>

    <update id="updateAttendHistory">
        UPDATE
        school_attendance_history
        SET ut=now()
        <if test="presentNumber != null">
            <![CDATA[, presentNumber =#{presentNumber}]]>
        </if>
        <if test="totalNumber != null">
            <![CDATA[, totalNumber =#{totalNumber}]]>
        </if>
        <if test="lessonNumber != null">
            <![CDATA[, lessonNumber =#{lessonNumber}]]>
        </if>
        <if test="absentNumber != null">
            <![CDATA[, absentNumber =#{absentNumber}]]>
        </if>
        <if test="leaveNumber != null">
            <![CDATA[, leaveNumber =#{leaveNumber}]]>
        </if>
        <if test="repairNumber != null">
            <![CDATA[, repairNumber =#{repairNumber}]]>
        </if>
        <if test="updater != null">
            <![CDATA[, updater =#{updater}]]>
        </if>
        WHERE  id =#{id}
    </update>

    <select id="selectAttendHistoryByPage" resultMap="historyMap">
        SELECT *
        FROM school_attendance_history
        WHERE 1 = 1
        AND isDeleted = 0
        <if test="guardId != null">
            <![CDATA[ and guardId =#{guardId}]]>
        </if>
        <if test="lessonId != null">
            <![CDATA[ and lessonId =#{lessonId}]]>
        </if>
        <if test="bunchId != null">
            <![CDATA[ and bunchId =#{bunchId}]]>
        </if>
        <if test="beginTime != null  and  beginTime != ''">
            <![CDATA[ and beginTime >= #{beginTime}]]>
        </if>
        <if test="endTime != null  and  endTime != ''">
            <![CDATA[ and endTime <= #{endTime}]]>
        </if>
        <if test="now != null  and  now != ''">
            <![CDATA[ and endTime < #{now}]]>
        </if>
        <if test="bunchIds != null  and bunchIds.size()>0">
            AND bunchId IN
            <foreach item="item" index="index" collection="bunchIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="guardIds != null and guardIds.size()>0">
            AND guardId IN
            <foreach item="item" index="index" collection="guardIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY beginTime DESC,startInterval ASC ,endInterval ASC
        <include refid="table_page"/>
    </select>


    <select id="countHistory" resultType="long">
        SELECT COUNT(*)
        FROM school_attendance_history
        WHERE 1 = 1
        AND isDeleted = 0
        <if test="guardId != null">
            <![CDATA[ and guardId =#{guardId}]]>
        </if>
        <if test="lessonId != null">
            <![CDATA[ and lessonId =#{lessonId}]]>
        </if>
        <if test="bunchId != null">
            <![CDATA[ and bunchId =#{bunchId}]]>
        </if>
        <if test="beginTime != null  and  beginTime != ''">
            <![CDATA[ and beginTime >= #{beginTime}]]>
        </if>
        <if test="endTime != null  and  endTime != ''">
            <![CDATA[ and endTime <= #{endTime}]]>
        </if>
        <if test="bunchIds != null  and bunchIds.size()>0">
            AND bunchId IN
            <foreach item="item" index="index" collection="bunchIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="guardIds != null and guardIds.size()>0">
            AND guardId IN
            <foreach item="item" index="index" collection="guardIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="findByScheduleId" resultMap="historyMap">
        SELECT *
        FROM school_attendance_history
        WHERE 1 = 1
        AND isDeleted = 0
        AND scheduleId = #{scheduleId}
    </select>

    <select id="findPresentNumberByScheduleId" resultType="String">
        SELECT presentNumber
        FROM school_attendance_history
        WHERE 1 = 1
        AND isDeleted = 0
        AND scheduleId = #{scheduleId}
        LIMIT 0,1
    </select>
</mapper>