<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.itboys.mobile.dao.SundayParent1Mapper">


	<select id="select" resultType="org.itboys.mobile.entity.mysql.SundayParent1">
		SELECT
    school_parent.id,
    school_parent.`name`,
    school_parent.phone,
    school_parent.`password`,
    school_parent.student_id,
    school_parent.relation,
    school_parent.address,
    school_parent.birthday,
    school_parent.phone_check,
    school_parent.pic,
    school_parent.email,
    school_parent.company,
    school_parent.create_time,
    school_parent.`status`,
    school_parent.gender,
    school_parent.is_first_login
            FROM
    school_parent
	</select>



</mapper>