<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.itboys.mobile.dao.SchoolStudentAlbumMapper">

    <resultMap id="AlbumMap" type="org.itboys.mobile.entity.mysql.album.SchoolStudentAlbum">
        <result property="id" column="id"/>
        <result property="studentId" column="student_id"/>
        <result property="year" column="year"/>
        <result property="month" column="month"/>
        <result property="staticUrl" column="static_url"/>
        <result property="pdfUrl" column="pdf_url"/>
        <result property="coverIndex" column="cover_image_offset"/>
        <result property="pageSize" column="page_size"/>
        <result property="recorders" column="recorders"/>
        <result property="flagPdfRefresh" column="flag_pdf_refresh"/>
        <result property="flagHtmlRefresh" column="flag_refresh"/>

    </resultMap>

    <!--基础表-->
    <sql id="table_name">
        album
    </sql>

    <!--分页-->
    <sql id="table_page">
        <if test="row_start != null  and  row_size != null">
            <![CDATA[limit #{row_start},#{row_size}]]>
        </if>
    </sql>

    <!--新增-->
    <insert id="insert" parameterType="org.itboys.mobile.entity.mysql.album.SchoolStudentAlbum" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT  INTO
        <include refid="table_name"/>
        (record_time,value,student_id,height,weight,headSize,toothAmount,remarks,create_time,creator_id,creator_type,deleted)
        VALUES (#{recordTime},#{value},#{studentId},#{height},#{weight},#{headSize},#{toothAmount},#{remarks},now(),#{creatorId},#{creatorType},0)
    </insert>

    <!--删除-->
    <update id="delete" parameterType="long">
        UPDATE
        <include refid="table_name"/>
        SET
        deleted = 1
        where  id =#{id}
    </update>

    <!--查询-->
    <select id="getById" resultMap="AlbumMap">
        SELECT *
        FROM <include refid="table_name"/>
        WHERE 1 = 1  AND id = #{id}
    </select>

    <select id="getAlbum" resultMap="AlbumMap">
        SELECT *
        FROM <include refid="table_name"/>
        WHERE 1 = 1
        AND student_id = #{studentId}
        <if test="year != null and year != ''">
            <![CDATA[ and year = #{year}]]>
        </if>
        <if test="month != null and month != ''">
            <![CDATA[ and month = #{month}]]>
        </if>

        LIMIT 0,1
    </select>

    <select id="select" resultMap="AlbumMap">
        SELECT *
        FROM <include refid="table_name"/>
        WHERE 1 = 1
        <if test="studentId != null">
            <![CDATA[ and student_id = #{studentId}]]>
        </if>
    </select>


    <select id="count" resultType="long">
        SELECT COUNT(*)
        FROM <include refid="table_name"/>
        WHERE 1 = 1
        <if test="studentId != null">
            <![CDATA[ and student_id = #{studentId}]]>
        </if>
    </select>

    <!--更新-->
    <update id="update" parameterType="org.itboys.mobile.entity.mysql.album.SchoolStudentAlbum">
        UPDATE
        <include refid="table_name"/>
        <trim prefix="set" suffixOverrides=",">
            <if test="recordTime != null and recordTime != ''">
                <![CDATA[ record_time = #{recordTime} ,]]>
            </if>
            <if test="value != null">
                <![CDATA[`value` =#{value} ,]]>
            </if>
            <if test="studentId != null">
                <![CDATA[student_id =#{studentId} ,]]>
            </if>
            <if test="remarks != null">
                <![CDATA[remarks =#{remarks} ,]]>
            </if>
            <if test="height != null">
                <![CDATA[ height = #{height} ,]]>
            </if>
            <if test="weight != null">
                <![CDATA[ weight = #{weight} ,]]>
            </if>
            <if test="headSize != null">
                <![CDATA[ headSize = #{headSize} ,]]>
            </if>
            <if test="toothAmount != null">
                <![CDATA[ toothAmount = #{toothAmount} ,]]>
            </if>
            <if test="creatorType != null">
                <![CDATA[creator_type =#{creatorType},]]>
            </if>
            <if test="flagPdfRefresh != null">
                <![CDATA[flag_pdf_refresh =#{flagPdfRefresh},]]>
            </if>
            <if test="flagHtmlRefresh != null">
                <![CDATA[flag_refresh =#{flagHtmlRefresh},]]>
            </if>
        </trim>
        WHERE  id =#{id}
    </update>

    <!--更新-->
    <update id="updateByUserId" parameterType="org.itboys.mobile.entity.mysql.album.SchoolStudentAlbum">
        UPDATE
        <include refid="table_name"/>
        <trim prefix="set" suffixOverrides=",">
            <if test="flagPdfRefresh != null">
                <![CDATA[flag_pdf_refresh =#{flagPdfRefresh},]]>
            </if>
            <if test="flagHtmlRefresh != null">
                <![CDATA[flag_refresh =#{flagHtmlRefresh},]]>
            </if>
        </trim>
        WHERE  student_id =#{studentId}
    </update>

</mapper>