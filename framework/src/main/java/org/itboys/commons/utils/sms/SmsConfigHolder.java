package org.itboys.commons.utils.sms;

import com.google.common.collect.Maps;
import org.itboys.commons.utils.xml.XmlUtils;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import java.util.Map;

/**
 * 短信相关的一些配置信息
 * <AUTHOR>
 */
public class SmsConfigHolder {

	private static final String SMS_URL = "smsurl";
	private static final String SMS_ACTION = "action";
	private static final String SMS_USER_ID = "userid";
	private static final String SMS_ACCOUNT = "account";
	private static final String SMS_PASSWORD = "password";
	private static final String SMS_SEND_TIME = "sendTime";
	private static final String SMS_EXTNO = "extno";
	
	private static Map<String,String> configMap;
	
	static{
		initConfigMap();
	}

	public static String getSmsUrl() {
		return configMap.get(SMS_URL);
	}

	public static String getSmsAction() {
		return configMap.get(SMS_ACTION);
	}

	public static String getSmsUserId() {
		return configMap.get(SMS_USER_ID);
	}

	public static String getSmsAccount() {
		return configMap.get(SMS_ACCOUNT);
	}

	public static String getSmsPassword() {
		return configMap.get(SMS_PASSWORD);
	}

	public static String getSmsSendTime() {
		return configMap.get(SMS_SEND_TIME);
	}

	public static String getSmsExtno() {
		return configMap.get(SMS_EXTNO);
	}

	private static void initConfigMap(){
		Document doc = XmlUtils.getDocumentFromClassPath("sms-config.xml");
		NodeList configNodeList=doc.getElementsByTagName("config");
		configMap = Maps.newHashMapWithExpectedSize(configNodeList.getLength());
		for(int i=0,size=configNodeList.getLength();i<size;i++){
			Node node = configNodeList.item(i);
			String key = node.getAttributes().getNamedItem("key").getNodeValue();
			String value = node.getTextContent();
			configMap.put(key, value);
		}
	}
}
