package org.itboys.commons.utils.reflection;

import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * Collections工具集.
 * 在JDK的Collections和Guava的Collections2后, 命名为Collections3.
 * 
 * <AUTHOR>
 */
public class Collections3 {

	/**
	 * 返回a-b的集合.
	 */
	public static <T> List<T> subtract(final Collection<T> a, final Collection<T> b) {
		ArrayList<T> list = new ArrayList<T>(a);
		for (Object element : b) {
			list.remove(element);
		}
		return list;
	}

	/**
	 * 提取集合中的对象的属性(通过Getter函数), 组合成Map.
	 * 
	 * @param collection 来源集合.
	 * @param keyPropertyName 要提取为Map中的Key值的属性名.
	 * @param valuePropertyName 要提取为Map中的Value值的属性名.
	 */
	public static Map<Object,Object> extractToMap(final Collection<?> collection, final String keyPropertyName,
			final String valuePropertyName) {
		Map<Object,Object> map = new HashMap<Object,Object>(collection.size());

		try {
			for (Object obj : collection) {
				map.put(PropertyUtils.getProperty(obj, keyPropertyName),
						PropertyUtils.getProperty(obj, valuePropertyName));
			}
		} catch (Exception e) {
			throw Reflections.convertReflectionExceptionToUnchecked(e);
		}

		return map;
	}

	/**
	 * 提取集合中的对象的属性(通过Getter函数), 组合成List.
	 * 
	 * @param collection 来源集合.
	 * @param propertyName 要提取的属性名.
	 */
	@SuppressWarnings("unchecked")
	public static <T> List<T> extractToList(final Collection<?> collection, final String propertyName) {
		List<T> list = new ArrayList<T>(collection.size());

		try {
			for (Object obj : collection) {
				list.add((T)PropertyUtils.getProperty(obj, propertyName));
			}
		} catch (Exception e) {
			throw Reflections.convertReflectionExceptionToUnchecked(e);
		}

		return list;
	}

	/**
	 * 提取集合中的对象的属性(通过Getter函数), 组合成由分割符分隔的字符串.
	 * 
	 * @param collection 来源集合.
	 * @param propertyName 要提取的属性名.
	 * @param separator 分隔符.
	 */
	public static String extractToString(final Collection<?> collection, final String propertyName, final String separator) {
		List<?> list = extractToList(collection, propertyName);
		return StringUtils.join(list, separator);
	}

	/**
	 * 转换Collection为String, 中间以 separator分隔。
	 */
	public static String convertToString(final Collection<?> collection, final String separator) {
		return StringUtils.join(collection, separator);
	}

	/**
	 * 转换Collection为String, 每个元素的前面加入prefix，后面加入postfix，如<div>mymessage</div>。
	 */
	public static String convertToString(final Collection<?> collection, final String prefix, final String postfix) {
		StringBuilder builder = new StringBuilder();
		for (Object o : collection) {
			builder.append(prefix).append(o).append(postfix);
		}
		return builder.toString();
	}
}
