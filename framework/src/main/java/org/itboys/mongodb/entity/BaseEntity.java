package org.itboys.mongodb.entity;

import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;


/**
 * mongodb实体相关的父类
 * <AUTHOR>
 *
 */
public class BaseEntity implements Serializable{

	private static final long serialVersionUID = -7856313888252598212L;

	/**
	 * 创建时间的毫秒long类型
	 */
	private long ct;
	
	/**
	 * 修改时间的毫秒类型
	 */
	private long ut;

	/**
	 * 是否删除
	 */
//	private boolean deleted = false;

	public long getCt() {
		return ct;
	}

	public void setCt(long ct) {
		this.ct = ct;
	}

	public long getUt() {
		return ut;
	}

/*	public boolean isDeleted() {
		return deleted;
	}

	public void setDeleted(boolean deleted) {
		this.deleted = deleted;
	}*/

	public void setUt(long ut) {
		this.ut = ut;
	}
	
	@Override
    public String toString(){
		return ToStringBuilder.reflectionToString(this);
	}
	
	@Override
    public int hashCode(){
		return HashCodeBuilder.reflectionHashCode(this);
	}
}
