package org.itboys.mongodb.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.bson.types.ObjectId;
import org.itboys.mongodb.JsonObjectIdSerializer;
import org.mongodb.morphia.annotations.Id;

/**
 * ID为mongodb ObjectId类型的父类
 * <AUTHOR>
 *
 */
public class BaseObjectIdEntity extends BaseEntity {

	private static final long serialVersionUID = -6571236456844690257L;
	
	@Id
	@JsonSerialize(using=JsonObjectIdSerializer.class)
	private ObjectId id;

	public ObjectId getId() {
		return id;
	}

	public void setId(ObjectId id) {
		this.id = id;
	}

	@Override
	public boolean equals(Object obj){
		if(obj==null){
			return false;
		}
		if(this.getId()==null){
			return false;
		}
		if (obj.getClass().equals(this.getClass())) {
			BaseObjectIdEntity o = (BaseObjectIdEntity)obj;
			if(o.getId()==null){
				return false;
			}
			return o.getId().equals(this.id);
		}
		return false;
	}
}
