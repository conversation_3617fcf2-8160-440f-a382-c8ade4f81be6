package org.itboys.mongodb;

import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.List;

public class MongoUtils {
	/**
	 * mongo数据库update时   字段为空的处理
	 * @param newObject 新的对象 即页面提交上来的
	 * @param oldObject 旧的对象 即通过id从数据库中查出来的
	 */
	public static void fill(Object newObject,Object oldObject){
		List<String> names = getFiledName(newObject , new String[0]);
		for(String name : names){
			try {
				Object value = PropertyUtils.getProperty(newObject, name);
				if( value == null){
					PropertyUtils.setProperty(newObject, name, PropertyUtils.getProperty(oldObject, name));
				}else if (value instanceof String
						&& StringUtils.isEmpty((String)value)) {
					PropertyUtils.setProperty(newObject, name, PropertyUtils.getProperty(oldObject, name));
				}
			} catch (IllegalAccessException e) {
				e.printStackTrace();
			} catch (InvocationTargetException e) {
				e.printStackTrace();
			} catch (NoSuchMethodException e) {
				e.printStackTrace();
			}
		}
	}
	
	/**
	 * mongo数据库update时   字段为空的处理
	 * @param newObject 新的对象 即页面提交上来的
	 * @param oldObject 旧的对象 即通过id从数据库中查出来的
	 */
	public static void fill(Object newObject,Object oldObject , String[] ignoreNames){
		List<String> names = getFiledName(newObject , ignoreNames);
		for(String name : names){
			try {
				Object value = PropertyUtils.getProperty(newObject, name);
				if( value == null){
					PropertyUtils.setProperty(newObject, name, PropertyUtils.getProperty(oldObject, name));
				}else if (value instanceof String
						&& StringUtils.isEmpty((String)value)) {
					PropertyUtils.setProperty(newObject, name, PropertyUtils.getProperty(oldObject, name));
				}
			} catch (IllegalAccessException e) {
				e.printStackTrace();
			} catch (InvocationTargetException e) {
				e.printStackTrace();
			} catch (NoSuchMethodException e) {
				e.printStackTrace();
			}
		}
	}
	
	/**
	 * 获取属性名数组
	 * */
	public static List<String> getFiledName(Object o , String[] ignoreNames) {
		Field[] fields = o.getClass().getDeclaredFields();
		Field[] fields2 = o.getClass().getSuperclass().getDeclaredFields();
		List<String> list = new ArrayList<String>();
		for (int i = 0; i < fields.length; i++) {
//			System.out.println(fields[i].getType());
			String name = fields[i].getName();
			boolean pass = true;
			for(String ignoreName : ignoreNames){
				if(ignoreName.equals(name)){
					pass = false;
					break;
				}
			}
			//静态变量的名称都是大写,据此过滤静态变量
			if(Character.isLowerCase(name.charAt(0)) && !"serialVersionUID".equals(name) && pass){
				list.add(name);
			}
		}
		for (int i = 0; i < fields2.length; i++) {
//			System.out.println(fields[i].getType());
			String name = fields2[i].getName();
			boolean pass = true;
			for(String ignoreName : ignoreNames){
				if(ignoreName.equals(name)){
					pass = false;
					break;
				}
			}
			//静态变量的名称都是大写,据此过滤静态变量
			if(Character.isLowerCase(name.charAt(0)) && !"serialVersionUID".equals(name) && pass){
				list.add(name);
			}
		}
		return list;
	}
	
}
