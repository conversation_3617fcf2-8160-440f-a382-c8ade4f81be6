/* 查询全年启动次数大于等于4次的家长人数（旧版数据 2020年1月开始用新数据） */
select count(*) from (
 select memberId,count(*) as count from(
 select DATE_FORMAT(ct,'%Y-%m-%d') as day,memberId,type from sunday_data where type=1 and ct>'2019-01-01 00:00:0000' group by day,memberId) a
 group by memberId) as b
 where count >=4;


 /* 查询全年托育家长累计启动应用次数（旧版数据 2020年1月开始用新数据）*/


  /* 查询全年亲子家长累计启动应用次数（旧版数据 2020年1月开始用新数据）*/


/* 10月托班家长人数 */

/* 10月托班家长启动人数*/

/* 10月托班家长启动次数*/

/* 10月教工人数*/

/* 10月教工启动人数*/

/* 10月教工启动次数*/
